<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html>
<html>
<head>
    <title>Force Cleanup Invoice Types</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Force Cleanup Invoice Types</h1>";
    
    if (!isset($_GET['confirm'])) {
        echo "<h2>⚠️ WARNING: This will forcefully clean up invoice types</h2>";
        echo "<p>This script will:</p>";
        echo "<ol>";
        echo "<li>Temporarily disable the unique index on 'code'</li>";
        echo "<li>Consolidate all invoice types</li>";
        echo "<li>Re-enable the unique index</li>";
        echo "</ol>";
        echo "<a href='?confirm=1' style='background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Execute Force Cleanup</a>";
    } else {
        echo "<h2>Executing Force Cleanup...</h2>";
        
        try {
            // Step 1: Drop the problematic index
            echo "<h3>Step 1: Dropping unique index on code...</h3>";
            try {
                $pdo->exec("ALTER TABLE config_invoice_types DROP INDEX idx_code");
                echo "<p class='success'>✅ Dropped idx_code index</p>";
            } catch (Exception $e) {
                echo "<p class='warning'>⚠️ Index might not exist or already dropped</p>";
            }
            
            // Step 2: Update all invoices to use the correct types
            echo "<h3>Step 2: Consolidating invoice types...</h3>";
            
            // Map all invoices to the correct types
            $mappings = [
                // Location: Use ID 12
                "UPDATE invoices SET type_id = 12 WHERE type_id IN (42)",
                // Loyer: Use ID 1  
                "UPDATE invoices SET type_id = 1 WHERE type_id IN (39)",
                // Retrocession 25%: Use ID 37
                "UPDATE invoices SET type_id = 37 WHERE type_id IN (40)",
                // Retrocession 30%: Use ID 38
                "UPDATE invoices SET type_id = 38 WHERE type_id IN (28)"
            ];
            
            foreach ($mappings as $sql) {
                $stmt = $pdo->prepare($sql);
                $stmt->execute();
                $count = $stmt->rowCount();
                echo "<p>✅ Updated {$count} invoices</p>";
            }
            
            // Step 3: Delete duplicate types
            echo "<h3>Step 3: Deleting duplicate types...</h3>";
            $stmt = $pdo->prepare("DELETE FROM config_invoice_types WHERE id IN (39, 40, 42, 28)");
            $stmt->execute();
            echo "<p class='success'>✅ Deleted duplicate invoice types</p>";
            
            // Step 4: Update codes to standard values
            echo "<h3>Step 4: Standardizing codes...</h3>";
            $updates = [
                "UPDATE config_invoice_types SET code = 'loyer', numbering_pattern = 'FAC-LOY-{YEAR}-{NUMBER:4}' WHERE id = 1",
                "UPDATE config_invoice_types SET code = 'location', numbering_pattern = 'FAC-LOC-{YEAR}-{NUMBER:4}' WHERE id = 12",
                "UPDATE config_invoice_types SET code = 'retrocession_25', numbering_pattern = 'FAC-RET25-{YEAR}-{NUMBER:4}' WHERE id = 37",
                "UPDATE config_invoice_types SET code = 'retrocession_30', numbering_pattern = 'FAC-RET30-{YEAR}-{NUMBER:4}' WHERE id = 38"
            ];
            
            foreach ($updates as $sql) {
                $pdo->exec($sql);
                echo "<p>✅ " . substr($sql, 42, 40) . "...</p>";
            }
            
            // Step 5: Update config mappings
            echo "<h3>Step 5: Updating config mappings...</h3>";
            $configs = [
                "DELETE FROM config WHERE `key` IN ('loc_invoice_type', 'location_invoice_type', 'loy_invoice_type', 'loyer_invoice_type', 'ret_invoice_type', 'retrocession_invoice_type')",
                "INSERT INTO config (`key`, `value`, created_at, updated_at) VALUES 
                 ('ret_invoice_type', 'retrocession_25', NOW(), NOW()),
                 ('loy_invoice_type', 'loyer', NOW(), NOW()),
                 ('loc_invoice_type', 'location', NOW(), NOW())"
            ];
            
            foreach ($configs as $sql) {
                $pdo->exec($sql);
            }
            echo "<p class='success'>✅ Updated config mappings</p>";
            
            // Step 6: Recreate index
            echo "<h3>Step 6: Recreating unique index...</h3>";
            $pdo->exec("CREATE UNIQUE INDEX idx_code ON config_invoice_types(code)");
            echo "<p class='success'>✅ Recreated unique index on code</p>";
            
            // Show final state
            echo "<h2 class='success'>✅ Cleanup Complete!</h2>";
            echo "<h3>Final Invoice Types:</h3>";
            
            $stmt = $pdo->query("
                SELECT id, code, name, prefix, color, numbering_pattern
                FROM config_invoice_types 
                WHERE is_active = 1
                ORDER BY id
            ");
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th><th>Pattern</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $name = json_decode($row['name'], true);
                $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td><strong>{$row['code']}</strong></td>";
                echo "<td>{$displayName}</td>";
                echo "<td>{$row['prefix']}</td>";
                echo "<td><small>{$row['numbering_pattern']}</small></td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show config mappings
            echo "<h3>Config Mappings:</h3>";
            $stmt = $pdo->query("SELECT `key`, `value` FROM config WHERE `key` LIKE '%_invoice_type' ORDER BY `key`");
            echo "<table>";
            echo "<tr><th>Key</th><th>Value</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr><td>{$row['key']}</td><td>{$row['value']}</td></tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<br><a href='/fit/public/test-user-invoice-generation.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Invoice Generation</a>";
    }
    
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "<p class='error'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}