{% extends "base-modern.twig" %}

{% block title %}{{ __('config.color_schemes') }}{% endblock %}

{% block styles %}
<style>
    .color-scheme-card {
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .color-scheme-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .color-scheme-card.active {
        border: 2px solid var(--bs-primary);
    }
    
    .color-scheme-card.active::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 0 40px 40px 0;
        border-color: transparent var(--bs-primary) transparent transparent;
    }
    
    .color-scheme-card.active::before {
        content: '\F26E';
        font-family: 'bootstrap-icons';
        position: absolute;
        top: 2px;
        right: 2px;
        color: white;
        font-size: 14px;
        z-index: 1;
    }
    
    .color-preview {
        display: flex;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        margin-bottom: 1rem;
    }
    
    .color-swatch {
        flex: 1;
        position: relative;
        transition: flex 0.3s ease;
    }
    
    .color-swatch:hover {
        flex: 2;
    }
    
    .color-swatch .color-name {
        position: absolute;
        bottom: 4px;
        left: 4px;
        right: 4px;
        font-size: 10px;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        opacity: 0;
        transition: opacity 0.3s ease;
        text-align: center;
    }
    
    .color-swatch:hover .color-name {
        opacity: 1;
    }
    
    .color-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }
    
    .color-item {
        text-align: center;
    }
    
    .color-box {
        width: 100%;
        height: 40px;
        border-radius: 4px;
        border: 1px solid rgba(0,0,0,0.1);
        margin-bottom: 4px;
        cursor: pointer;
        transition: transform 0.2s ease;
    }
    
    .color-box:hover {
        transform: scale(1.1);
    }
    
    .color-label {
        font-size: 11px;
        color: var(--bs-secondary);
    }
    
    .scheme-actions {
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .color-scheme-card:hover .scheme-actions {
        opacity: 1;
    }
    
    #colorSchemeModal .color-input-group {
        margin-bottom: 15px;
    }
    
    #colorSchemeModal .color-preview-box {
        width: 40px;
        height: 40px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        cursor: pointer;
    }
    
    #previewModal .preview-container {
        background: var(--preview-body-bg, #f8fafc);
        padding: 20px;
        border-radius: 8px;
        min-height: 400px;
    }
    
    .preview-sidebar {
        background: var(--preview-sidebar-bg, #1e293b);
        color: var(--preview-sidebar-text, #cbd5e1);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .preview-card {
        background: var(--preview-card-bg, #ffffff);
        border: 1px solid var(--preview-border-color, #e2e8f0);
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .preview-button {
        margin: 5px;
        border: none;
    }
</style>
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('common.home') }}</a></li>
<li class="breadcrumb-item"><a href="{{ base_url }}/config">{{ __('config.configuration') }}</a></li>
<li class="breadcrumb-item active">{{ __('config.color_schemes') }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.color_schemes') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createSchemeModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.create_color_scheme') }}
            </button>
        </div>
    </div>

    <!-- Info Alert -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.color_schemes_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Color Schemes Grid -->
    <!-- Debug: colorSchemes count = {{ colorSchemes|length }} -->
    <div class="row" id="colorSchemesGrid">
        {% for scheme in colorSchemes %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card color-scheme-card {{ scheme.is_current ? 'active' : '' }}" data-scheme-id="{{ scheme.id }}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="card-title mb-1">
                                {{ scheme.display_name }}
                                {% if scheme.is_system %}
                                    <span class="badge bg-secondary ms-2">{{ __('config.system') }}</span>
                                {% endif %}
                                {% if scheme.is_default %}
                                    <span class="badge bg-info ms-2">{{ __('config.default') }}</span>
                                {% endif %}
                            </h5>
                            <p class="text-muted small mb-0">{{ scheme.display_description }}</p>
                        </div>
                        <div class="scheme-actions">
                            <div class="btn-group btn-group-sm" role="group">
                                {% if not scheme.is_current %}
                                <button type="button" 
                                        class="btn btn-outline-success" 
                                        onclick="activateScheme({{ scheme.id }})"
                                        data-bs-toggle="tooltip" 
                                        title="{{ __('config.activate') }}">
                                    <i class="bi bi-check-circle"></i>
                                </button>
                                {% endif %}
                                <button type="button" 
                                        class="btn btn-outline-primary" 
                                        onclick="previewScheme({{ scheme.id }})"
                                        data-bs-toggle="tooltip" 
                                        title="{{ __('common.preview') }}">
                                    <i class="bi bi-eye"></i>
                                </button>
                                {% if not scheme.is_system %}
                                <button type="button" 
                                        class="btn btn-outline-secondary" 
                                        onclick="editScheme({{ scheme.id }})"
                                        data-bs-toggle="tooltip" 
                                        title="{{ __('common.edit') }}">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                {% endif %}
                                <button type="button" 
                                        class="btn btn-outline-info" 
                                        onclick="duplicateScheme({{ scheme.id }})"
                                        data-bs-toggle="tooltip" 
                                        title="{{ __('common.duplicate') }}">
                                    <i class="bi bi-files"></i>
                                </button>
                                {% if not scheme.is_system and not scheme.is_default and not scheme.is_current %}
                                <button type="button" 
                                        class="btn btn-outline-danger" 
                                        onclick="deleteScheme({{ scheme.id }})"
                                        data-bs-toggle="tooltip" 
                                        title="{{ __('common.delete') }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Color Preview -->
                    <div class="color-preview">
                        <div class="color-swatch" style="background-color: {{ scheme.colors.primary }}">
                            <span class="color-name">Primary</span>
                        </div>
                        <div class="color-swatch" style="background-color: {{ scheme.colors.secondary }}">
                            <span class="color-name">Secondary</span>
                        </div>
                        <div class="color-swatch" style="background-color: {{ scheme.colors.success }}">
                            <span class="color-name">Success</span>
                        </div>
                        <div class="color-swatch" style="background-color: {{ scheme.colors.info }}">
                            <span class="color-name">Info</span>
                        </div>
                        <div class="color-swatch" style="background-color: {{ scheme.colors.warning }}">
                            <span class="color-name">Warning</span>
                        </div>
                        <div class="color-swatch" style="background-color: {{ scheme.colors.danger }}">
                            <span class="color-name">Danger</span>
                        </div>
                    </div>

                    <!-- Additional Colors -->
                    <div class="color-grid">
                        <div class="color-item">
                            <div class="color-box" style="background-color: {{ scheme.colors.sidebar_bg }}" 
                                 title="Sidebar Background"></div>
                            <div class="color-label">Sidebar</div>
                        </div>
                        <div class="color-item">
                            <div class="color-box" style="background-color: {{ scheme.colors.navbar_bg }}" 
                                 title="Navbar Background"></div>
                            <div class="color-label">Navbar</div>
                        </div>
                        <div class="color-item">
                            <div class="color-box" style="background-color: {{ scheme.colors.body_bg }}" 
                                 title="Body Background"></div>
                            <div class="color-label">Body</div>
                        </div>
                        <div class="color-item">
                            <div class="color-box" style="background-color: {{ scheme.colors.card_bg }}" 
                                 title="Card Background"></div>
                            <div class="color-label">Card</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Create Scheme Modal -->
<div class="modal fade" id="createSchemeModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.create_color_scheme') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="createSchemeForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ __('common.name') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('common.code') }} <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="code" required pattern="[a-z0-9_]+" 
                               placeholder="e.g., my_custom_theme">
                        <small class="text-muted">{{ __('config.code_format_hint') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('common.description') }}</label>
                        <textarea class="form-control" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('config.base_scheme') }}</label>
                        <select class="form-select" name="base_scheme">
                            {% for scheme in colorSchemes %}
                                <option value="{{ scheme.code }}" {{ scheme.is_default ? 'selected' : '' }}>
                                    {{ scheme.display_name }}
                                </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">{{ __('config.base_scheme_hint') }}</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>{{ __('common.create') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Scheme Modal -->
<div class="modal fade" id="editSchemeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.edit_color_scheme') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editSchemeForm">
                <input type="hidden" name="scheme_id">
                <div class="modal-body">
                    <ul class="nav nav-tabs mb-3" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" data-bs-toggle="tab" data-bs-target="#basicTab" type="button">
                                {{ __('config.basic_info') }}
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" data-bs-toggle="tab" data-bs-target="#colorsTab" type="button">
                                {{ __('config.colors') }}
                            </button>
                        </li>
                    </ul>
                    
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="basicTab">
                            <div class="mb-3">
                                <label class="form-label">{{ __('common.name') }} <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">{{ __('common.description') }}</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="colorsTab">
                            <div class="row">
                                <!-- Main Colors -->
                                <div class="col-md-6">
                                    <h6 class="mb-3">{{ __('config.theme_colors') }}</h6>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.primary_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[primary]">
                                            <input type="text" class="form-control" name="colors_text[primary]" placeholder="#6366f1">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.secondary_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[secondary]">
                                            <input type="text" class="form-control" name="colors_text[secondary]" placeholder="#64748b">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.success_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[success]">
                                            <input type="text" class="form-control" name="colors_text[success]" placeholder="#10b981">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.info_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[info]">
                                            <input type="text" class="form-control" name="colors_text[info]" placeholder="#06b6d4">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.warning_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[warning]">
                                            <input type="text" class="form-control" name="colors_text[warning]" placeholder="#f59e0b">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.danger_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[danger]">
                                            <input type="text" class="form-control" name="colors_text[danger]" placeholder="#ef4444">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.light_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[light]">
                                            <input type="text" class="form-control" name="colors_text[light]" placeholder="#f8fafc">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.dark_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[dark]">
                                            <input type="text" class="form-control" name="colors_text[dark]" placeholder="#1e293b">
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- UI Colors -->
                                <div class="col-md-6">
                                    <h6 class="mb-3">{{ __('config.interface_colors') }}</h6>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.sidebar_bg') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[sidebar_bg]">
                                            <input type="text" class="form-control" name="colors_text[sidebar_bg]" placeholder="#1e293b">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.sidebar_text') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[sidebar_text]">
                                            <input type="text" class="form-control" name="colors_text[sidebar_text]" placeholder="#cbd5e1">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.navbar_bg') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[navbar_bg]">
                                            <input type="text" class="form-control" name="colors_text[navbar_bg]" placeholder="#ffffff">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.navbar_text') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[navbar_text]">
                                            <input type="text" class="form-control" name="colors_text[navbar_text]" placeholder="#1e293b">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.body_bg') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[body_bg]">
                                            <input type="text" class="form-control" name="colors_text[body_bg]" placeholder="#f8fafc">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.card_bg') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[card_bg]">
                                            <input type="text" class="form-control" name="colors_text[card_bg]" placeholder="#ffffff">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.text_primary') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[text_primary]">
                                            <input type="text" class="form-control" name="colors_text[text_primary]" placeholder="#1e293b">
                                        </div>
                                    </div>
                                    
                                    <div class="color-input-group">
                                        <label class="form-label">{{ __('config.border_color') }}</label>
                                        <div class="input-group">
                                            <input type="color" class="form-control form-control-color" name="colors[border_color]">
                                            <input type="text" class="form-control" name="colors_text[border_color]" placeholder="#e2e8f0">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') }}
                    </button>
                    <button type="button" class="btn btn-info" onclick="previewChanges()">
                        <i class="bi bi-eye me-2"></i>{{ __('common.preview') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.color_scheme_preview') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="previewContainer" class="preview-container">
                    <!-- Preview content will be injected here -->
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// Color scheme data
let colorSchemes = {{ colorSchemes|json_encode|raw }};
let currentSchemeId = null;

// Initialize
$(document).ready(function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Bind color input changes
    $('#editSchemeModal').on('input', 'input[type="color"]', function() {
        const textInput = $(this).siblings('input[type="text"]');
        textInput.val($(this).val());
    });
    
    $('#editSchemeModal').on('input', 'input[type="text"][name^="colors_text"]', function() {
        const colorInput = $(this).siblings('input[type="color"]');
        const value = $(this).val();
        if (value.match(/^#[0-9A-F]{6}$/i)) {
            colorInput.val(value);
        }
    });
    
    // Form submissions
    $('#createSchemeForm').on('submit', function(e) {
        e.preventDefault();
        createScheme();
    });
    
    $('#editSchemeForm').on('submit', function(e) {
        e.preventDefault();
        updateScheme();
    });
});

// Activate scheme
function activateScheme(id) {
    if (!confirm('{{ __("config.confirm_activate_scheme") }}')) {
        return;
    }
    
    $.ajax({
        url: '{{ base_url }}/config/color-schemes/' + id + '/activate',
        type: 'POST',
        data: {
            csrf_token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            toastr.success(response.message);
            setTimeout(() => location.reload(), 1000);
        },
        error: function(xhr) {
            const response = xhr.responseJSON || {};
            toastr.error(response.message || '{{ __("common.error_occurred") }}');
        }
    });
}

// Create scheme
function createScheme() {
    const formData = $('#createSchemeForm').serialize() + '&csrf_token=' + $('meta[name="csrf-token"]').attr('content');
    
    $.ajax({
        url: '{{ base_url }}/config/color-schemes',
        type: 'POST',
        data: formData,
        success: function(response) {
            toastr.success(response.message);
            $('#createSchemeModal').modal('hide');
            setTimeout(() => location.reload(), 1000);
        },
        error: function(xhr) {
            const response = xhr.responseJSON || {};
            toastr.error(response.message || '{{ __("common.error_occurred") }}');
        }
    });
}

// Edit scheme
function editScheme(id) {
    const scheme = colorSchemes.find(s => s.id == id);
    if (!scheme) return;
    
    currentSchemeId = id;
    
    // Set basic info
    $('#editSchemeForm input[name="scheme_id"]').val(id);
    $('#editSchemeForm input[name="name"]').val(scheme.display_name);
    $('#editSchemeForm textarea[name="description"]').val(scheme.display_description);
    
    // Set colors
    const colors = scheme.colors;
    for (const [key, value] of Object.entries(colors)) {
        if (!key.endsWith('_rgb') && !key.includes('shadow')) {
            $(`#editSchemeForm input[name="colors[${key}]"]`).val(value);
            $(`#editSchemeForm input[name="colors_text[${key}]"]`).val(value);
        }
    }
    
    $('#editSchemeModal').modal('show');
}

// Update scheme
function updateScheme() {
    const formData = new FormData($('#editSchemeForm')[0]);
    const schemeId = formData.get('scheme_id');
    
    // Collect colors
    const colors = {};
    $('#editSchemeForm input[name^="colors["]').each(function() {
        const key = $(this).attr('name').match(/colors\[([^\]]+)\]/)[1];
        colors[key] = $(this).val();
    });
    
    const data = {
        name: formData.get('name'),
        description: formData.get('description'),
        colors: colors,
        csrf_token: $('meta[name="csrf-token"]').attr('content')
    };
    
    $.ajax({
        url: '{{ base_url }}/config/color-schemes/' + schemeId,
        type: 'PUT',
        data: JSON.stringify(data),
        contentType: 'application/json',
        success: function(response) {
            toastr.success(response.message);
            $('#editSchemeModal').modal('hide');
            setTimeout(() => location.reload(), 1000);
        },
        error: function(xhr) {
            const response = xhr.responseJSON || {};
            toastr.error(response.message || '{{ __("common.error_occurred") }}');
        }
    });
}

// Preview scheme
function previewScheme(id) {
    $.ajax({
        url: '{{ base_url }}/config/color-schemes/' + id + '/preview',
        type: 'GET',
        success: function(response) {
            showPreview(response.css, response.colors);
        },
        error: function(xhr) {
            const response = xhr.responseJSON || {};
            toastr.error(response.message || '{{ __("common.error_occurred") }}');
        }
    });
}

// Preview changes
function previewChanges() {
    // Collect current colors from form
    const colors = {};
    $('#editSchemeForm input[name^="colors["]').each(function() {
        const key = $(this).attr('name').match(/colors\[([^\]]+)\]/)[1];
        colors[key] = $(this).val();
    });
    
    // Generate CSS
    let css = ":root {\n";
    const colorMap = {
        'primary': '--bs-primary',
        'secondary': '--bs-secondary',
        'success': '--bs-success',
        'info': '--bs-info',
        'warning': '--bs-warning',
        'danger': '--bs-danger',
        'light': '--bs-light',
        'dark': '--bs-dark',
        'sidebar_bg': '--sidebar-bg',
        'sidebar_text': '--sidebar-text',
        'navbar_bg': '--navbar-bg',
        'navbar_text': '--navbar-text',
        'card_bg': '--bs-card-bg',
        'body_bg': '--bs-body-bg',
        'text_primary': '--bs-body-color',
        'border_color': '--bs-border-color'
    };
    
    for (const [key, cssVar] of Object.entries(colorMap)) {
        if (colors[key]) {
            css += `    ${cssVar}: ${colors[key]};\n`;
        }
    }
    css += "}";
    
    showPreview(css, colors);
}

// Show preview
function showPreview(css, colors) {
    // Create preview HTML
    const previewHtml = `
        <style id="previewStyles">
            #previewContainer ${css.replace(':root', '')}
            #previewContainer {
                --preview-primary: ${colors.primary};
                --preview-secondary: ${colors.secondary};
                --preview-success: ${colors.success};
                --preview-info: ${colors.info};
                --preview-warning: ${colors.warning};
                --preview-danger: ${colors.danger};
                --preview-sidebar-bg: ${colors.sidebar_bg};
                --preview-sidebar-text: ${colors.sidebar_text};
                --preview-navbar-bg: ${colors.navbar_bg};
                --preview-navbar-text: ${colors.navbar_text};
                --preview-card-bg: ${colors.card_bg};
                --preview-body-bg: ${colors.body_bg};
                --preview-text-primary: ${colors.text_primary};
                --preview-border-color: ${colors.border_color};
            }
        </style>
        
        <div class="preview-sidebar">
            <h5 style="color: var(--preview-sidebar-text);">{{ __('common.navigation') }}</h5>
            <ul class="list-unstyled">
                <li class="mb-2"><a href="#" style="color: var(--preview-sidebar-text); text-decoration: none;">{{ __('common.dashboard') }}</a></li>
                <li class="mb-2"><a href="#" style="color: var(--preview-sidebar-text); text-decoration: none;">{{ __('invoices.invoices') }}</a></li>
                <li class="mb-2"><a href="#" style="color: var(--preview-sidebar-text); text-decoration: none;">{{ __('clients.title') }}</a></li>
            </ul>
        </div>
        
        <div class="preview-card" style="color: var(--preview-text-primary);">
            <h4>{{ __('config.sample_content') }}</h4>
            <p>{{ __('config.preview_description') }}</p>
            
            <div class="mt-4">
                <button class="preview-button btn text-white" style="background-color: var(--preview-primary);">Primary</button>
                <button class="preview-button btn text-white" style="background-color: var(--preview-secondary);">Secondary</button>
                <button class="preview-button btn text-white" style="background-color: var(--preview-success);">Success</button>
                <button class="preview-button btn text-white" style="background-color: var(--preview-info);">Info</button>
                <button class="preview-button btn text-dark" style="background-color: var(--preview-warning);">Warning</button>
                <button class="preview-button btn text-white" style="background-color: var(--preview-danger);">Danger</button>
            </div>
        </div>
    `;
    
    $('#previewContainer').html(previewHtml);
    $('#previewModal').modal('show');
}

// Duplicate scheme
function duplicateScheme(id) {
    if (!confirm('{{ __("config.confirm_duplicate_scheme") }}')) {
        return;
    }
    
    $.ajax({
        url: '{{ base_url }}/config/color-schemes/' + id + '/duplicate',
        type: 'POST',
        data: {
            csrf_token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            toastr.success(response.message);
            setTimeout(() => location.reload(), 1000);
        },
        error: function(xhr) {
            const response = xhr.responseJSON || {};
            toastr.error(response.message || '{{ __("common.error_occurred") }}');
        }
    });
}

// Delete scheme
function deleteScheme(id) {
    if (!confirm('{{ __("config.confirm_delete_scheme") }}')) {
        return;
    }
    
    $.ajax({
        url: '{{ base_url }}/config/color-schemes/' + id,
        type: 'DELETE',
        data: {
            csrf_token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            toastr.success(response.message);
            setTimeout(() => location.reload(), 1000);
        },
        error: function(xhr) {
            const response = xhr.responseJSON || {};
            toastr.error(response.message || '{{ __("common.error_occurred") }}');
        }
    });
}
</script>
{% endblock %}