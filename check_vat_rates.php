<?php
/**
 * Check VAT rates configuration
 */

require_once __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS'] ?? '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== VAT RATES CONFIGURATION ===\n\n";
    
    // Get all VAT rates
    $stmt = $pdo->prepare("SELECT * FROM config_vat_rates ORDER BY rate ASC");
    $stmt->execute();
    $rates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($rates) > 0) {
        echo "Found " . count($rates) . " VAT rates:\n\n";
        
        foreach ($rates as $rate) {
            echo "ID: {$rate['id']}\n";
            echo "Name: {$rate['name']}\n";
            echo "Rate: {$rate['rate']}%\n";
            echo "Code: {$rate['code']}\n";
            echo "Is Default: " . ($rate['is_default'] ? 'YES' : 'NO') . "\n";
            echo "Active: " . ($rate['is_active'] ? 'YES' : 'NO') . "\n";
            echo "---\n";
        }
        
        // Check for 17% rate
        $has17 = false;
        $default17 = false;
        foreach ($rates as $rate) {
            if ($rate['rate'] == 17) {
                $has17 = true;
                if ($rate['is_default']) {
                    $default17 = true;
                }
            }
        }
        
        echo "\nAnalysis:\n";
        echo "- Has 17% rate: " . ($has17 ? 'YES' : 'NO') . "\n";
        echo "- 17% is default: " . ($default17 ? 'YES' : 'NO') . "\n";
        
        // Check for multiple defaults
        $defaultCount = 0;
        foreach ($rates as $rate) {
            if ($rate['is_default']) {
                $defaultCount++;
            }
        }
        
        if ($defaultCount > 1) {
            echo "⚠️  WARNING: Multiple default rates found ($defaultCount)\n";
        } elseif ($defaultCount == 0) {
            echo "⚠️  WARNING: No default rate found\n";
        }
        
    } else {
        echo "No VAT rates found in database!\n";
    }
    
    // Check what the API returns
    echo "\n\n=== API ENDPOINT CHECK ===\n";
    echo "The invoice form loads VAT rates from: /api/vat-rates\n";
    echo "Please check if this endpoint returns the correct data.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}