<?php
// Show current catalog structure
require_once dirname(__DIR__) . '/vendor/autoload.php';
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

// Admin check
if (!isset($_SESSION['user'])) {
    die("Access denied. Please log in.");
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Current Catalog Structure</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; font-weight: bold; }
        .success { color: green; }
        .missing { color: red; }
        .exists { color: blue; }
    </style>
</head>
<body>
    <h1>Current Catalog Structure</h1>
    
    <?php
    try {
        $db = Flight::db();
        
        // catalog_items structure
        echo "<h2>catalog_items Table</h2>";
        $stmt = $db->query("SHOW COLUMNS FROM catalog_items");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        $itemColumns = [];
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
            $itemColumns[] = $column['Field'];
        }
        echo "</table>";
        
        // Check missing columns
        $requiredColumns = ['min_stock', 'max_stock', 'location', 'supplier_id'];
        $missingColumns = array_diff($requiredColumns, $itemColumns);
        
        if (!empty($missingColumns)) {
            echo "<h3>Missing Columns in catalog_items:</h3>";
            echo "<ul>";
            foreach ($missingColumns as $col) {
                echo "<li class='missing'>$col</li>";
            }
            echo "</ul>";
        }
        
        // catalog_categories structure
        echo "<h2>catalog_categories Table</h2>";
        $stmt = $db->query("SHOW COLUMNS FROM catalog_categories");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
        
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "<td>{$column['Extra']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // catalog_stock_movements structure  
        echo "<h2>catalog_stock_movements Table</h2>";
        $tables = $db->query("SHOW TABLES LIKE 'catalog_stock_movements'")->fetchAll();
        if (count($tables) > 0) {
            echo "<p class='success'>✅ Table exists</p>";
            
            $stmt = $db->query("SHOW COLUMNS FROM catalog_stock_movements");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<table>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
            
            foreach ($columns as $column) {
                echo "<tr>";
                echo "<td>{$column['Field']}</td>";
                echo "<td>{$column['Type']}</td>";
                echo "<td>{$column['Null']}</td>";
                echo "<td>{$column['Key']}</td>";
                echo "<td>{$column['Default']}</td>";
                echo "<td>{$column['Extra']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p class='missing'>❌ Table does not exist</p>";
        }
        
    } catch (Exception $e) {
        echo "<p class='missing'>Error: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <h2>Actions</h2>
    <ul>
        <li><a href="/fit/public/fix-catalog-columns.php">Fix Missing Columns</a></li>
        <li><a href="/fit/public/products">Go to Products</a></li>
    </ul>
</body>
</html>