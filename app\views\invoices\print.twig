<!DOCTYPE html>
<html lang="{{ app.request.lang ?? 'fr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ __('invoices.invoice') }} {{ invoice.invoice_number }}</title>
    <style>
        /* A4 Page Setup */
        @page {
            size: A4 portrait;
            margin: 15mm;
        }
        
        @media print {
            body { 
                margin: 0;
                background: white;
            }
            .no-print { display: none !important; }
            .watermark { opacity: 0.1 !important; }
            .invoice-container {
                width: 100%;
                max-width: none;
                padding: 0;
                margin: 0;
                box-shadow: none;
            }
        }
        
        @media screen {
            body {
                margin: 0;
                padding: 20px;
                background: #f5f5f5;
            }
        }
        
        body {
            font-family: Arial, Helvetica, sans-serif;
            font-size: 10pt;
            line-height: 1.3;
            color: #000;
        }
        
        .invoice-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            padding: 15mm;
            box-sizing: border-box;
        }
        
        /* Header */
        .header {
            margin-bottom: 30px;
        }
        
        .company-info {
            font-size: 9pt;
            line-height: 1.4;
        }
        
        .company-name {
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .client-box {
            border: 1px solid #000;
            padding: 10px;
            margin-bottom: 20px;
            min-height: 80px;
            font-size: 10pt;
        }
        
        .invoice-number-box {
            text-align: right;
            margin-bottom: 20px;
        }
        
        .invoice-number-box h2 {
            font-size: 11pt;
            margin: 0 0 5px 0;
            font-weight: normal;
        }
        
        /* Client info */
        .addresses {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        
        .address-box {
            width: 45%;
        }
        
        .address-box h3 {
            font-size: 11pt;
            margin: 0 0 10px 0;
            border-bottom: 1px solid #ddd;
            padding-bottom: 5px;
        }
        
        .address-content {
            font-size: 10pt;
            line-height: 1.5;
        }
        
        /* Object section */
        .object-section {
            margin-bottom: 20px;
        }
        
        .object-section td {
            padding: 3px 0;
            font-size: 10pt;
        }
        
        /* Table */
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .items-table {
            border: 1px solid #000;
            margin-bottom: 20px;
        }
        
        .items-table th {
            background: #e0e0e0;
            padding: 8px;
            text-align: center;
            font-size: 9pt;
            font-weight: normal;
            border: 1px solid #000;
        }
        
        .items-table td {
            padding: 8px;
            border: 1px solid #000;
            font-size: 9pt;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        /* Totals box */
        .totals-box {
            border: 1px solid #000;
            width: 60mm;
            margin-left: auto;
            margin-bottom: 30px;
        }
        
        .totals-box td {
            padding: 5px 8px;
            font-size: 9pt;
            border-bottom: 1px solid #000;
        }
        
        .totals-box tr:last-child td {
            border-bottom: none;
            font-weight: bold;
        }
        
        /* Bank details */
        .bank-details {
            margin-top: 30px;
            font-size: 9pt;
            line-height: 1.4;
        }
        
        .payment-conditions {
            margin-bottom: 20px;
            font-size: 9pt;
        }
        
        /* Status */
        .status {
            display: inline-block;
            padding: 5px 15px;
            font-size: 10pt;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-paid {
            color: green;
            border: 2px solid green;
        }
        
        .status-draft {
            color: gray;
            border: 2px solid gray;
        }
        
        .status-cancelled {
            color: red;
            border: 2px solid red;
        }
        
        /* Watermark */
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 120px;
            font-weight: bold;
            color: #f0f0f0;
            opacity: 0.1;
            z-index: -1;
            text-transform: uppercase;
        }
        
        /* Payment info */
        .payment-info {
            background: #f9f9f9;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 10pt;
        }
        
        .payment-info h4 {
            margin: 0 0 5px 0;
            font-size: 11pt;
        }
        
        /* Print button */
        .print-button {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 20px;
            background: #333;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 10pt;
        }
        
        @media screen {
            .invoice-container {
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                margin-top: 20px;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    {% if invoice.status == 'draft' %}
        <div class="watermark">BROUILLON</div>
    {% elseif invoice.status == 'cancelled' %}
        <div class="watermark">ANNULÉ</div>
    {% endif %}
    
    <button class="print-button no-print" onclick="window.print()">Imprimer</button>
    
    <div class="invoice-container">
        <!-- Header FIT 360 style -->
        <div class="header">
            <table width="100%">
                <tr>
                    <td width="50%">
                        <div class="company-info">
                            <div class="company-name">{{ company_name }}</div>
                            {{ company_address }}<br>
                            {{ company_postal_code }} {{ company_city }} - {{ company_country }}<br>
                            {{ company_phone }}<br>
                            TVA: {{ company_vat_number }}
                        </div>
                    </td>
                    <td width="50%">
                        <div class="client-box">
                            <strong>
                                {% if invoice.client.company_name %}
                                    {{ invoice.client.company_name }}<br>
                                {% endif %}
                                {{ invoice.client.first_name }} {{ invoice.client.last_name }}
                            </strong><br>
                            {% if invoice.client.address or invoice.client.address_line1 %}
                                {{ invoice.client.address|default(invoice.client.address_line1) }}<br>
                                {% if invoice.client.address_line2 %}
                                    {{ invoice.client.address_line2 }}<br>
                                {% endif %}
                                {{ invoice.client.postal_code }} {{ invoice.client.city }}<br>
                                {% if invoice.client.country %}{{ invoice.client.country }}{% endif %}
                            {% endif %}
                        </div>
                    </td>
                </tr>
            </table>
            
            <div class="invoice-number-box">
                {% set documentTypeName = 'FACTURE' %}
                {% if invoice.document_type.name[app.request.lang|default('fr')] is defined %}
                    {% set documentTypeName = invoice.document_type.name[app.request.lang|default('fr')]|upper %}
                {% elseif invoice.invoice_type == 'credit_note' %}
                    {% set documentTypeName = 'AVOIR' %}
                {% elseif invoice.invoice_type == 'quote' %}
                    {% set documentTypeName = 'DEVIS' %}
                {% elseif invoice.invoice_type == 'proforma' %}
                    {% set documentTypeName = 'PROFORMA' %}
                {% endif %}
                <h2>{{ documentTypeName }} N° {{ invoice.invoice_number }}</h2>
                <div>{{ invoice.issue_date|date('d/m/Y') }}</div>
            </div>
        </div>
        
        <!-- Object and Period section -->
        <table class="object-section">
            <tr>
                <td width="80"><strong>Objet:</strong></td>
                <td>
                    {% if invoice.description %}
                        {{ invoice.description }}
                    {% elseif invoiceTypeDetails and invoiceTypeDetails.display_name %}
                        {{ invoiceTypeDetails.display_name|upper }}
                    {% elseif invoice.invoice_type == 'rental' %}
                        LOYER + CHARGES
                    {% elseif invoice.invoice_type in ['retrocession', 'retrocession_30', 'retrocession_25'] %}
                        RETROCESSION CNS/PATIENTS
                    {% elseif invoice.invoice_type == 'service' %}
                        PRESTATIONS DE SERVICES
                    {% else %}
                        FACTURE
                    {% endif %}
                </td>
            </tr>
            {% if invoice.period %}
            <tr>
                <td><strong>Période:</strong></td>
                <td>{{ invoice.period }}</td>
            </tr>
            {% endif %}
        </table>
        
        <!-- Items table FIT 360 style -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 10%">Qté</th>
                    <th style="width: 50%; text-align: left;">OBJET</th>
                    <th style="width: 15%">TOTAL</th>
                    <th style="width: 15%">HTVA</th>
                    <th style="width: 10%">% TVA</th>
                </tr>
            </thead>
            <tbody>
                {% for item in invoice.lines %}
                {% set lineTotal = item.quantity * item.unit_price %}
                {% set vatAmount = lineTotal * (item.vat_rate / 100) %}
                {% set totalWithVat = lineTotal + vatAmount %}
                <tr>
                    <td class="text-center">{{ item.quantity|number_format(0) }}</td>
                    <td>{{ item.description }}</td>
                    <td class="text-right">{{ totalWithVat|number_format(2, ',', ' ') }}</td>
                    <td class="text-right">{{ vatAmount|number_format(2, ',', ' ') }}</td>
                    <td class="text-center">{{ item.vat_rate|number_format(0) }} %</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- Totals FIT 360 style -->
        <table class="totals-box">
            <tr>
                <td><strong>TOTAL hors TVA</strong></td>
                <td class="text-right">{{ invoice.subtotal|number_format(2, ',', ' ') }}</td>
            </tr>
            <tr>
                <td><strong>TVA</strong></td>
                <td class="text-right">{{ (invoice.total_vat ?? invoice.vat_amount)|number_format(2, ',', ' ') }}</td>
            </tr>
            {% if invoice.cns_base_amount > 0 %}
            <tr>
                <td><strong>Montant CNS/Patient</strong></td>
                <td class="text-right">{{ invoice.cns_base_amount|number_format(2, ',', ' ') }}</td>
            </tr>
            {% endif %}
            {% if invoice.secretariat_vat_amount > 0 %}
            <tr>
                <td><strong>Frais secrétariat</strong></td>
                <td class="text-right">{{ invoice.secretariat_vat_amount|number_format(2, ',', ' ') }}</td>
            </tr>
            {% endif %}
            <tr>
                <td><strong>TOTAL TVA incl.</strong></td>
                <td class="text-right"><strong>{{ invoice.total|number_format(2, ',', ' ') }}</strong></td>
            </tr>
        </table>
        
        <!-- Payment conditions -->
        <div class="payment-conditions">
            {% set paymentTermsText = 'Dès réception' %}
            {% if invoice.payment_terms %}
                {% set paymentTermsText = invoice.payment_terms %}
            {% elseif invoice.payment_terms_days is defined %}
                {% if invoice.payment_terms_days == 0 %}
                    {% set paymentTermsText = 'Dès réception' %}
                {% elseif invoice.payment_terms_days == 15 %}
                    {% set paymentTermsText = '15 jours' %}
                {% elseif invoice.payment_terms_days == 30 %}
                    {% set paymentTermsText = '30 jours' %}
                {% elseif invoice.payment_terms_days == 45 %}
                    {% set paymentTermsText = '45 jours' %}
                {% elseif invoice.payment_terms_days == 60 %}
                    {% set paymentTermsText = '60 jours' %}
                {% else %}
                    {% set paymentTermsText = invoice.payment_terms_days ~ ' jours' %}
                {% endif %}
            {% endif %}
            <strong>Paiement : {{ paymentTermsText }}</strong>
        </div>
        
        <!-- Bank details FIT 360 style -->
        <div class="bank-details">
            <strong>FIT360</strong><br>
            {{ bank_name }}<br>
            IBAN - {{ iban }}<br>
            SWIFT CODE - {{ swift_code }}
        </div>
        
        {% if invoice.notes %}
        <div style="margin-top: 20px; font-size: 9pt;">
            <strong>Notes:</strong><br>
            {{ invoice.notes|nl2br }}
        </div>
        {% endif %}
    </div>
</body>
</html>