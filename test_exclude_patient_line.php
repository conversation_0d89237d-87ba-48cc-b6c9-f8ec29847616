<?php
/**
 * Test script for the exclude patient line feature
 * This script simulates creating a retrocession invoice with and without the patient line
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Services\RetrocessionCalculator;
use App\Helpers\MoneyHelper;

// Test data
$testDataEntry = [
    'cns_amount' => 1000.00,
    'patient_amount' => 500.00,
    'exclude_patient_line' => false
];

$calculator = new RetrocessionCalculator();

// Test 1: Normal calculation with patient line
echo "=== Test 1: Normal retrocession (with patient line) ===\n";
$result1 = $calculator->calculate($testDataEntry);
echo "CNS Amount: €" . number_format($testDataEntry['cns_amount'], 2) . "\n";
echo "Patient Amount: €" . number_format($testDataEntry['patient_amount'], 2) . "\n";
echo "CNS Part (20%): €" . number_format($result1['cns_part'], 2) . "\n";
echo "Patient Part (20%): €" . number_format($result1['patient_part'], 2) . "\n";
echo "Practitioner Total: €" . number_format($result1['practitioner_total'], 2) . "\n";
echo "Secretary Fee (10% TVAC): €" . number_format($result1['secretariat_tvac'], 2) . "\n";
echo "Invoice Total: €" . number_format($result1['invoice_total'], 2) . "\n\n";

// Test 2: Calculation without patient line
echo "=== Test 2: Retrocession without patient line ===\n";
$testDataEntry['exclude_patient_line'] = true;
$result2 = $calculator->calculate($testDataEntry);

// Simulate what would happen in invoice generation
$practitionerTotal = $result2['cns_part']; // Only CNS part
$invoiceTotal = MoneyHelper::sum([$practitionerTotal, $result2['secretariat_tvac']]);

echo "CNS Amount: €" . number_format($testDataEntry['cns_amount'], 2) . "\n";
echo "Patient Amount: €" . number_format($testDataEntry['patient_amount'], 2) . " (excluded from invoice)\n";
echo "CNS Part (20%): €" . number_format($result2['cns_part'], 2) . "\n";
echo "Patient Part (20%): €" . number_format($result2['patient_part'], 2) . " (not included in invoice)\n";
echo "Practitioner Total: €" . number_format($practitionerTotal, 2) . " (CNS part only)\n";
echo "Secretary Fee (10% TVAC): €" . number_format($result2['secretariat_tvac'], 2) . "\n";
echo "Invoice Total: €" . number_format($invoiceTotal, 2) . "\n\n";

// Test 3: Show invoice lines that would be created
echo "=== Test 3: Invoice lines comparison ===\n";
echo "\nWith patient line:\n";
echo "1. Part CNS: €" . number_format($result1['cns_part'], 2) . "\n";
echo "2. Part Patient: €" . number_format($result1['patient_part'], 2) . "\n";
echo "3. Frais de secrétariat (TVAC): €" . number_format($result1['secretariat_tvac'], 2) . "\n";
echo "Total: €" . number_format($result1['invoice_total'], 2) . "\n";

echo "\nWithout patient line:\n";
echo "1. Part CNS: €" . number_format($result2['cns_part'], 2) . "\n";
echo "2. Frais de secrétariat (TVAC): €" . number_format($result2['secretariat_tvac'], 2) . "\n";
echo "Total: €" . number_format($invoiceTotal, 2) . "\n";

echo "\n=== Migration Status ===\n";
try {
    $db = Flight::db();
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $hasColumn = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'exclude_patient_line') {
            $hasColumn = true;
            break;
        }
    }
    
    if ($hasColumn) {
        echo "✓ Column 'exclude_patient_line' exists in retrocession_data_entry table\n";
    } else {
        echo "✗ Column 'exclude_patient_line' NOT FOUND - Run migration 039_add_exclude_patient_line_to_retrocession.php\n";
    }
} catch (Exception $e) {
    echo "Error checking database: " . $e->getMessage() . "\n";
}

echo "\n=== Test completed ===\n";