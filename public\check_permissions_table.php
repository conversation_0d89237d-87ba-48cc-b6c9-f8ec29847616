<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Check if permissions table exists
$stmt = $db->query("SHOW TABLES LIKE 'permissions'");
if ($stmt->rowCount() > 0) {
    echo "<h3>permissions table structure:</h3><pre>";
    $stmt = $db->query("SHOW COLUMNS FROM permissions");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
    echo "</pre>";
    
    echo "<h3>Sample permissions data:</h3><pre>";
    $stmt = $db->query("SELECT * FROM permissions LIMIT 5");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        print_r($row);
    }
    echo "</pre>";
} else {
    echo "<h3>No 'permissions' table found</h3>";
}

// List all tables with 'permission' in the name
echo "<h3>All permission-related tables:</h3><pre>";
$stmt = $db->query("SHOW TABLES LIKE '%permission%'");
while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
    echo $row[0] . "\n";
}
echo "</pre>";
?>