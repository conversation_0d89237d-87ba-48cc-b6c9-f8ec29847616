# Email Template UI Analysis Report

## Overview
This report analyzes the mismatch between the current email template UI and the enhanced backend capabilities, documenting what needs to be updated to support conditional editing, priority selection, and testing features.

## Current State Analysis

### 1. UI Implementation (email-templates-modern.twig)

#### Current Features:
- Basic template listing with active/inactive status
- Simple editor with subject, body, and footer fields
- Preview tab for visual rendering
- Settings tab with:
  - Language selection (fr, en, de)
  - Template type (read-only field)
  - Active status toggle
  - Include logo option
  - Attach invoice option
- Variables modal showing available placeholders
- Test email functionality (basic)
- Restore default option

#### Missing UI Elements:
1. **No conditional template editing**
2. **No priority configuration**
3. **No template selection rules interface**
4. **No variant management**
5. **No inheritance configuration**
6. **No advanced testing options**
7. **No template category management**
8. **No performance metrics display**

### 2. Backend Capabilities (Enhanced Services)

#### EmailService Features:
- Conditional template selection based on context
- Template variants and inheritance
- Advanced variable parsing
- Performance logging
- Email tracking and history

#### TemplateSelector Features:
- Priority-based template selection (CRITICAL, HIGH, MEDIUM, LOW, DEFAULT)
- Conditional rules evaluation
- Template inheritance chains
- Context-aware selection (invoice type, client type, payment status, etc.)
- Selection metadata tracking
- Multi-language support with fallbacks
- Template scoring and ranking

### 3. Database Structure

#### Current Schema (config_email_templates):
```sql
- id
- code (unique identifier)
- name
- category
- subject (JSON for multi-language)
- body (JSON for multi-language)
- variables (JSON)
- is_active
- timestamps
```

#### Missing Database Fields:
- priority
- parent_template_id (for inheritance)
- conditions (JSON for selection rules)
- metadata (JSON for additional config)
- performance_metrics
- last_used_at
- usage_count

### 4. Route Analysis

#### Current Routes:
- GET /config/email-templates (list view)
- Missing routes for:
  - Create/Update/Delete templates
  - Test email with context
  - Manage selection rules
  - View usage statistics
  - Template preview with variables

## Required UI Updates

### 1. Template List Enhancement
```html
<!-- Add to template list item -->
<div class="template-meta">
    <span class="badge bg-danger">Priority: HIGH</span>
    <span class="badge bg-info">Invoice Reminder</span>
    <span class="text-muted">Used: 45 times</span>
</div>
```

### 2. New Editor Tabs

#### Conditions Tab:
- Rule builder interface
- Condition types:
  - Invoice type selector
  - Client type selector
  - Payment status options
  - Days overdue range
  - Language preferences
  - Custom conditions editor

#### Priority Tab:
- Priority level selector (1-5)
- Inheritance configuration
- Parent template selector
- Override options

#### Testing Tab:
- Context builder for testing
- Sample data presets
- Test with specific invoice
- Test with client profile
- Bulk testing options

### 3. Advanced Features Panel

#### Template Variants:
- Create variant button
- Variant conditions
- A/B testing configuration
- Performance comparison

#### Usage Analytics:
- Times sent
- Open rates (if tracked)
- Click rates (if tracked)
- Last used date
- Performance score

### 4. Form Updates

#### Subject Field Enhancement:
```html
<div class="subject-editor">
    <input type="text" name="subject[fr]" />
    <button class="btn-add-condition">Add Conditional Subject</button>
</div>
```

#### Body Editor Enhancement:
- Rich text editor with:
  - Conditional blocks support
  - Variable autocomplete
  - Template sections
  - Preview with sample data

## Implementation Plan

### Phase 1: Database Migration
1. Add missing columns to config_email_templates
2. Create template_selection_rules table
3. Create template_usage_stats table

### Phase 2: Backend Routes
1. Implement CRUD operations for templates
2. Add selection rules management endpoints
3. Create advanced testing endpoints
4. Add analytics endpoints

### Phase 3: UI Components
1. Update template listing with metadata
2. Add new editor tabs
3. Implement condition builder
4. Create testing interface

### Phase 4: Integration
1. Connect UI to new endpoints
2. Implement real-time preview
3. Add performance monitoring
4. Create help documentation

## Priority Actions

1. **Immediate**: Add missing CRUD routes for email templates
2. **High**: Implement priority field in UI and database
3. **High**: Create conditions editor interface
4. **Medium**: Add template testing with context
5. **Medium**: Implement usage analytics
6. **Low**: Add A/B testing features

## Technical Recommendations

1. Use Vue.js or Alpine.js for dynamic condition builder
2. Implement JSON schema validation for conditions
3. Add real-time preview with debouncing
4. Cache compiled templates for performance
5. Use WebSockets for live testing feedback
6. Implement undo/redo for template editing

## Conclusion

The current UI is significantly behind the backend capabilities. The TemplateSelector service has advanced features for conditional selection, priority-based routing, and inheritance that are not exposed in the UI. Implementing the recommended updates will unlock these powerful features and provide users with a comprehensive email template management system.