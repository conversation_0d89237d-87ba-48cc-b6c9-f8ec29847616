<?php
/**
 * Sales Invoice Routes
 */

use App\Controllers\SalesInvoiceController;

// Sales routes
Flight::group('/sales', function() {
    
    // Sales invoices
    Flight::group('/invoices', function() {
        
        // List invoices
        Flight::route('GET /', [new SalesInvoiceController(), 'index']);
        
        // Create invoice
        Flight::route('GET /create', [new SalesInvoiceController(), 'create']);
        Flight::route('POST /create', [new SalesInvoiceController(), 'store']);
        
        // Quick sale mode
        Flight::route('GET /quick-sale', [new SalesInvoiceController(), 'quickSale']);
        
        // Invoice details
        Flight::route('GET /@id:[0-9]+', [new SalesInvoiceController(), 'show']);
        
        // Edit invoice
        Flight::route('GET /@id:[0-9]+/edit', [new SalesInvoiceController(), 'edit']);
        Flight::route('POST /@id:[0-9]+/edit', [new SalesInvoiceController(), 'update']);
        
        // Delete invoice
        Flight::route('DELETE /@id:[0-9]+', [new SalesInvoiceController(), 'destroy']);
        Flight::route('POST /@id:[0-9]+/delete', [new SalesInvoiceController(), 'destroy']);
        
        // PDF
        Flight::route('GET /@id:[0-9]+/pdf', [new SalesInvoiceController(), 'pdf']);
        Flight::route('GET /@id:[0-9]+/download', [new SalesInvoiceController(), 'download']);
        
        // Payment
        Flight::route('POST /@id:[0-9]+/payment', [new SalesInvoiceController(), 'recordPayment']);
        
        // Check voucher
        Flight::route('GET /check-voucher', [new SalesInvoiceController(), 'checkVoucher']);
    });
    
});