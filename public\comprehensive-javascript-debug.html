<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive JavaScript Debug - Coach Dropdown</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #28a745, #007bff); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { padding: 12px 24px; margin: 8px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; text-decoration: none; display: inline-block; text-align: center; }
        .btn-success { background: #28a745; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .success { border-left: 5px solid #28a745; background: #f8fff9; }
        .info { border-left: 5px solid #17a2b8; background: #f0f8ff; }
        .warning { border-left: 5px solid #ffc107; background: #fffdf5; }
        .error { border-left: 5px solid #dc3545; background: #fff5f5; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; font-size: 14px; border: 1px solid #e9ecef; }
        .highlight { background: #fff3cd; padding: 2px 4px; border-radius: 3px; font-weight: bold; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .step { margin: 15px 0; padding: 15px; background: #e9ecef; border-radius: 5px; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
        .tab { display: inline-block; padding: 10px 20px; margin: 5px; border: none; border-radius: 5px 5px 0 0; cursor: pointer; background: #e9ecef; }
        .tab.active { background: #007bff; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        .status-unknown { background: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Comprehensive JavaScript Debug</h1>
            <p>Complete diagnostic tool for coach dropdown JavaScript issues</p>
            <p><strong>✅ Syntax Fixes Applied + Firefox Add-on Detection</strong></p>
        </div>

        <div class="card">
            <h2>🎯 Quick Actions</h2>
            <div class="grid">
                <div>
                    <h3>Test Invoice Creation</h3>
                    <a href="/fit/public/invoices/create?type=location&_debug=true" class="btn btn-success" target="_blank" id="invoiceTestLink">
                        🎯 Test Location Invoice
                    </a>
                </div>
                <div>
                    <h3>Detect Browser Issues</h3>
                    <a href="/fit/public/firefox-addon-detector.html" class="btn btn-warning" target="_blank">
                        🔍 Detect Firefox Add-ons
                    </a>
                </div>
                <div>
                    <h3>Run Diagnostics</h3>
                    <button class="btn btn-primary" onclick="runComprehensiveDiagnostics()">
                        📊 Run Full Diagnostics
                    </button>
                </div>
                <div>
                    <h3>Emergency Reset</h3>
                    <button class="btn btn-danger" onclick="emergencyReset()">
                        🚨 Emergency Reset
                    </button>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📋 Tab Navigation</h2>
            <div>
                <button class="tab active" onclick="showTab('symptoms')">🩺 Symptoms</button>
                <button class="tab" onclick="showTab('diagnostics')">📊 Diagnostics</button>
                <button class="tab" onclick="showTab('solutions')">🛠️ Solutions</button>
                <button class="tab" onclick="showTab('testing')">🧪 Testing</button>
            </div>
        </div>

        <div id="symptoms" class="tab-content active">
            <div class="card error">
                <h2>🩺 Current Symptoms</h2>
                <div class="grid">
                    <div>
                        <h3>JavaScript Errors</h3>
                        <ul>
                            <li><span class="status-indicator status-error"></span>Syntax Error: missing ) after argument list (line 4477)</li>
                            <li><span class="status-indicator status-error"></span>window.showDebugConsole is not a function</li>
                            <li><span class="status-indicator status-error"></span>window.autoFixCoachDropdown is not a function</li>
                            <li><span class="status-indicator status-error"></span>window.manuallyPopulateCoaches is not a function</li>
                        </ul>
                    </div>
                    <div>
                        <h3>Visual Issues</h3>
                        <ul>
                            <li><span class="status-indicator status-error"></span>Coach dropdown is empty</li>
                            <li><span class="status-indicator status-warning"></span>Debug buttons don't work</li>
                            <li><span class="status-indicator status-success"></span>Page loads normally otherwise</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div id="diagnostics" class="tab-content">
            <div class="card info">
                <h2>📊 Diagnostic Results</h2>
                <div id="diagnosticResults">
                    <p>Click "Run Full Diagnostics" to see detailed analysis.</p>
                </div>
                <button class="btn btn-primary" onclick="runComprehensiveDiagnostics()">🔍 Run Diagnostics</button>
            </div>
        </div>

        <div id="solutions" class="tab-content">
            <div class="card">
                <h2>🛠️ Progressive Solutions</h2>
                
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>Browser Cache (Most Common)</strong>
                    <p>Clear browser cache and hard refresh:</p>
                    <div class="code">
                        1. Press Ctrl+Shift+Delete<br>
                        2. Select "All time" and check all boxes<br>
                        3. Click "Clear data"<br>
                        4. Press Ctrl+Shift+R (hard refresh)
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">2</span>
                    <strong>Firefox Add-on Interference</strong>
                    <p>Test without extensions:</p>
                    <div class="code">
                        1. Open Private Window (Ctrl+Shift+P)<br>
                        2. Test the invoice page in private mode<br>
                        3. Or disable extensions temporarily
                    </div>
                    <a href="/fit/public/firefox-addon-detector.html" class="btn btn-warning" target="_blank">🔍 Detect Add-ons</a>
                </div>

                <div class="step">
                    <span class="step-number">3</span>
                    <strong>Syntax Error Resolution</strong>
                    <p>The multi-line console.log statement was fixed:</p>
                    <div class="code">
                        Fixed: Line 4488 console.log statement consolidated to single line
                    </div>
                </div>

                <div class="step">
                    <span class="step-number">4</span>
                    <strong>Alternative Browsers</strong>
                    <p>Test in different browsers:</p>
                    <div class="code">
                        • Chrome: Clean environment<br>
                        • Edge: Windows default<br>
                        • Safari: Mac alternative
                    </div>
                </div>
            </div>
        </div>

        <div id="testing" class="tab-content">
            <div class="card">
                <h2>🧪 Testing Protocol</h2>
                
                <div class="grid">
                    <div class="info">
                        <h3>Quick Tests</h3>
                        <button class="btn btn-primary" onclick="testBasicJS()">Test Basic JS</button>
                        <button class="btn btn-primary" onclick="testDOMAccess()">Test DOM Access</button>
                        <button class="btn btn-primary" onclick="testConsoleAPI()">Test Console API</button>
                        <div id="quickTestResults"></div>
                    </div>
                    <div class="warning">
                        <h3>Expected Console Output</h3>
                        <div class="code">
                            ✅ Modern template loading...<br>
                            ✅ Base URL: /fit/public<br>
                            ✅ Template: modern<br>
                            ✅ Complete invoice fix loaded<br>
                            ✅ 🔍 === INVOICE CREATION DEBUG SESSION START ===<br>
                            ✅ 🔍 === ENSURING DEBUG FUNCTIONS ARE AVAILABLE ===<br>
                            ✅ 🔍 window.showDebugConsole is available<br>
                            ❌ NO SYNTAX ERRORS
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card success">
            <h2>✅ Latest Fixes Applied</h2>
            <div class="grid">
                <div>
                    <h3>JavaScript Syntax</h3>
                    <ul>
                        <li>✅ All template literals converted to string concatenation</li>
                        <li>✅ Multi-line console.log statements fixed</li>
                        <li>✅ Function definition scope issues resolved</li>
                        <li>✅ Safety wrapper functions added</li>
                    </ul>
                </div>
                <div>
                    <h3>Debug System</h3>
                    <ul>
                        <li>✅ Function availability checks added</li>
                        <li>✅ Error handling implemented</li>
                        <li>✅ Fallback mechanisms created</li>
                        <li>✅ Comprehensive logging system</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card info">
            <h2>📞 Support Information</h2>
            <p>If issues persist after trying all solutions:</p>
            <ol>
                <li>Run the Firefox add-on detector</li>
                <li>Test in private/incognito mode</li>
                <li>Try a different browser</li>
                <li>Check browser console for remaining errors</li>
                <li>Provide console output and browser information</li>
            </ol>
        </div>
    </div>

    <script>
        // Update test link with timestamp
        document.addEventListener('DOMContentLoaded', function() {
            const testLink = document.getElementById('invoiceTestLink');
            const url = new URL(testLink.href);
            url.searchParams.set('_timestamp', Date.now());
            testLink.href = url.toString();
        });

        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        function runComprehensiveDiagnostics() {
            const results = document.getElementById('diagnosticResults');
            results.innerHTML = '<p>🔄 Running comprehensive diagnostics...</p>';
            
            const diagnostics = {
                browser: {
                    name: navigator.userAgent,
                    language: navigator.language,
                    online: navigator.onLine,
                    cookieEnabled: navigator.cookieEnabled
                },
                javascript: {
                    basicJS: testBasicJS(),
                    domAccess: testDOMAccess(),
                    consoleAPI: testConsoleAPI(),
                    fetch: typeof fetch !== 'undefined',
                    promises: typeof Promise !== 'undefined'
                },
                page: {
                    url: window.location.href,
                    protocol: window.location.protocol,
                    host: window.location.host,
                    loaded: document.readyState
                }
            };
            
            results.innerHTML = `
                <h3>🔍 Diagnostic Results</h3>
                <div class="code">${JSON.stringify(diagnostics, null, 2)}</div>
                <div class="warning">
                    <h4>Next Steps:</h4>
                    <p>If JavaScript tests pass but coach dropdown still fails, the issue is likely:</p>
                    <ol>
                        <li>Browser cache (clear cache and hard refresh)</li>
                        <li>Firefox add-on interference (test in private mode)</li>
                        <li>Syntax error preventing functions from being defined</li>
                    </ol>
                </div>
            `;
        }

        function testBasicJS() {
            try {
                const result = 2 + 2;
                return result === 4;
            } catch (error) {
                return false;
            }
        }

        function testDOMAccess() {
            try {
                const testDiv = document.createElement('div');
                return testDiv instanceof HTMLElement;
            } catch (error) {
                return false;
            }
        }

        function testConsoleAPI() {
            try {
                console.log('Test message');
                return true;
            } catch (error) {
                return false;
            }
        }

        function emergencyReset() {
            if (confirm('This will reload the page with cache-busting parameters. Continue?')) {
                const url = new URL(window.location.href);
                url.searchParams.set('_emergency', Date.now());
                url.searchParams.set('_nocache', 'true');
                window.location.href = url.toString();
            }
        }
    </script>
</body>
</html>