{% extends "base-modern.twig" %}

{% block title %}{{ __('common.dashboard') }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ base_url }}/css/mobile-responsive.css">
<link rel="stylesheet" href="{{ base_url }}/css/mobile-dashboard.css">
{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-gradient-primary text-white">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">{{ __('dashboard.welcome_back') }}, {{ session.user_name|default(user.name|default('Guest')) }}!</h2>
                        <p class="mb-0 opacity-75">{{ __('dashboard.today_summary') }} - {{ 'now'|date('l, F j, Y') }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="{{ base_url }}/invoices/create" class="btn btn-light btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Dashboard Controls -->
<div class="row mb-3">
    <div class="col-12 d-flex justify-content-end align-items-center gap-3">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="auto-refresh-toggle">
            <label class="form-check-label" for="auto-refresh-toggle">
                {{ __('dashboard.auto_refresh') }}
            </label>
        </div>
        <button class="btn btn-sm btn-outline-primary" id="refresh-dashboard">
            <i class="bi bi-arrow-clockwise"></i> {{ __('common.refresh') }}
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-4 mb-4 dashboard-stats">
    <!-- Total Patients -->
    <div class="col-xl-3 col-md-6">
        <div class="small-box bg-primary stat-patients">
            <div class="inner">
                <h3>{{ stats.total_patients|default(0)|number_format(0, '.', ',') }}</h3>
                <p>{{ __('dashboard.total_patients') }}</p>
            </div>
            <div class="icon">
                <i class="fas fa-user-injured"></i>
            </div>
            <a href="{{ base_url }}/patients" class="small-box-footer">
                {{ __('common.more_info') }} <i class="bi bi-arrow-right-circle"></i>
            </a>
        </div>
    </div>
    
    <!-- Total Clients -->
    <div class="col-xl-3 col-md-6">
        <div class="small-box bg-success stat-clients">
            <div class="inner">
                <h3>{{ stats.total_clients|default(0)|number_format(0, '.', ',') }}</h3>
                <p>{{ __('dashboard.total_clients') }}</p>
            </div>
            <div class="icon">
                <i class="bi bi-building"></i>
            </div>
            <a href="{{ base_url }}/clients" class="small-box-footer">
                {{ __('common.more_info') }} <i class="bi bi-arrow-right-circle"></i>
            </a>
        </div>
    </div>
    
    <!-- Pending Invoices -->
    <div class="col-xl-3 col-md-6">
        <div class="small-box bg-warning stat-pending">
            <div class="inner">
                <h3>{{ stats.pending_invoices|default(0)|number_format(0, '.', ',') }}</h3>
                <p>{{ __('dashboard.pending_invoices') }}</p>
            </div>
            <div class="icon">
                <i class="bi bi-clock-history"></i>
            </div>
            <a href="{{ base_url }}/invoices?status=sent" class="small-box-footer">
                {{ __('common.more_info') }} <i class="bi bi-arrow-right-circle"></i>
            </a>
        </div>
    </div>
    
    <!-- Monthly Revenue -->
    <div class="col-xl-3 col-md-6">
        <div class="small-box bg-info stat-revenue">
            <div class="inner">
                <h3>{{ stats.monthly_revenue|default(0)|number_format(2, ',', '.') }} €</h3>
                <p>{{ __('dashboard.monthly_revenue') }}</p>
            </div>
            <div class="icon">
                <i class="bi bi-currency-dollar"></i>
            </div>
            <a href="{{ base_url }}/reports/revenue" class="small-box-footer">
                {{ __('common.more_info') }} <i class="bi bi-arrow-right-circle"></i>
            </a>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row g-4 mb-4">
    <!-- Revenue Chart -->
    <div class="col-xl-8">
        <div class="card h-100 revenue-chart-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('dashboard.revenue_overview') }}</h5>
                <div class="card-tools">
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary active" data-period="week">{{ __('common.week') }}</button>
                        <button type="button" class="btn btn-outline-secondary" data-period="month">{{ __('common.month') }}</button>
                        <button type="button" class="btn btn-outline-secondary" data-period="year">{{ __('common.year') }}</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Invoice Status Chart -->
    <div class="col-xl-4">
        <div class="card h-100 invoice-status-container">
            <div class="card-header">
                <h5 class="mb-0">{{ __('dashboard.invoice_status') }}</h5>
            </div>
            <div class="card-body">
                <canvas id="invoiceStatusChart" height="300"></canvas>
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-2">
                        <span class="badge badge-status-paid">{{ __('invoices.status_paid') }}</span>
                        <strong class="status-paid count">{{ stats.paid_invoices|default(0) }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="badge badge-status-sent">{{ __('invoices.status_sent') }}</span>
                        <strong class="status-sent count">{{ stats.sent_invoices|default(0) }}</strong>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span class="badge badge-status-overdue">{{ __('invoices.status_overdue') }}</span>
                        <strong class="status-overdue count">{{ stats.overdue_invoices|default(0) }}</strong>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Tables Row -->
<div class="row g-4">
    <!-- Recent Invoices -->
    <div class="col-xl-6">
        <div class="card invoices-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('dashboard.recent_invoices') }}</h5>
                <a href="{{ base_url }}/invoices" class="btn btn-sm btn-primary">
                    {{ __('common.view_all') }}
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0 recent-invoices-table">
                        <thead>
                            <tr>
                                <th>{{ __('invoices.invoice_number') }}</th>
                                <th>{{ __('invoices.client') }}</th>
                                <th>{{ __('invoices.amount') }}</th>
                                <th>{{ __('invoices.status') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in recent_invoices %}
                            <tr>
                                <td>
                                    <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="text-primary text-decoration-none">
                                        #{{ invoice.invoice_number }}
                                    </a>
                                </td>
                                <td>{{ invoice.client_name }}</td>
                                <td>{{ invoice.total_amount|number_format(2, ',', '.') }} €</td>
                                <td>
                                    <span class="badge badge-status-{{ invoice.status }}">
                                        {{ __('invoices.status_' ~ invoice.status) }}
                                    </span>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <div class="empty-state">
                                        <div class="empty-state-icon">
                                            <i class="bi bi-file-earmark-text"></i>
                                        </div>
                                        <p class="empty-state-description">{{ __('dashboard.no_recent_invoices') }}</p>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="card-footer invoice-pagination"></div>
            </div>
        </div>
    </div>
    
    <!-- Recent Activities -->
    <div class="col-xl-6">
        <div class="card activities-container">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('dashboard.recent_activities') }}</h5>
                <a href="{{ base_url }}/activities" class="btn btn-sm btn-primary">
                    {{ __('common.view_all') }}
                </a>
            </div>
            <div class="card-body">
                <div class="timeline">
                    {% for activity in recent_activities %}
                    <div class="timeline-item">
                        <div class="timeline-badge bg-{{ activity.type_color|default('primary') }}">
                            <i class="bi bi-{{ activity.icon|default('circle') }}"></i>
                        </div>
                        <div class="timeline-content">
                            <h6 class="mb-1">{{ activity.title }}</h6>
                            <p class="mb-1 text-muted">{{ activity.description }}</p>
                            <small class="text-muted">
                                <i class="bi bi-clock me-1"></i>{{ activity.created_at|date('M j, g:i A') }}
                            </small>
                        </div>
                    </div>
                    {% else %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="bi bi-activity"></i>
                        </div>
                        <p class="empty-state-description">{{ __('dashboard.no_recent_activities') }}</p>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card bg-light border-0">
            <div class="card-body">
                <h5 class="card-title mb-3">{{ __('dashboard.quick_actions') }}</h5>
                <div class="row g-3">
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/patients/create" class="btn btn-outline-primary w-100 py-3">
                            <i class="fas fa-user-plus mb-2 d-block fs-4"></i>
                            {{ __('patients.add_patient') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/clients/create" class="btn btn-outline-success w-100 py-3">
                            <i class="bi bi-building-add mb-2 d-block fs-4"></i>
                            {{ __('clients.add_client') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/invoices/create" class="btn btn-outline-info w-100 py-3">
                            <i class="bi bi-file-earmark-plus mb-2 d-block fs-4"></i>
                            {{ __('invoices.create_invoice') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/reports" class="btn btn-outline-warning w-100 py-3">
                            <i class="bi bi-graph-up mb-2 d-block fs-4"></i>
                            {{ __('common.view_reports') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* Timeline Styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--bs-gray-300);
}

.timeline-item {
    position: relative;
    padding-bottom: 1.5rem;
}

.timeline-item:last-child {
    padding-bottom: 0;
}

.timeline-badge {
    position: absolute;
    left: -22px;
    top: 0;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 10px;
}

.timeline-content {
    margin-left: 10px;
}

/* Gradient Background for Welcome Card */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #7c3aed 100%);
}
</style>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
<script>
// Set base URL for dashboard manager
window.baseUrl = '{{ base_url }}';

// Translation function bridge
window.__ = function(key) {
    // This would normally use the Twig translation system
    // For now, return the key as fallback
    return key;
};
</script>
<script src="{{ base_url }}/js/dashboard.js"></script>
{% endblock %}