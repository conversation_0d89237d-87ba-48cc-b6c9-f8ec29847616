<?php
/**
 * Final Dashboard Test - After Fixes
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Dashboard After Fixes</h1>";
echo "<p>We've fixed:</p>";
echo "<ul>";
echo "<li>✅ Database query that was looking for patient_id column</li>";
echo "<li>✅ Twig functions (shouldShowMenu and getMenuClass)</li>";
echo "</ul>";

echo "<h2>Actions:</h2>";
echo "<p><a href='/fit/public/' class='btn btn-primary' style='padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px;'>Try Main Dashboard Now</a></p>";

echo "<p>If it still shows an error:</p>";
echo "<ol>";
echo "<li><a href='/fit/public/catch_dashboard_error.php'>Run Error Catcher Again</a> - to see if there's a new error</li>";
echo "<li><a href='/fit/public/check_invoices_table.php'>Check Invoices Table Structure</a></li>";
echo "<li><a href='/fit/public/clear_twig_cache.php'>Clear Twig Cache</a> (see below)</li>";
echo "</ol>";

// Add a Twig cache clearer
if (isset($_GET['clear_cache'])) {
    $cache_dir = dirname(__DIR__) . '/storage/cache/twig';
    if (is_dir($cache_dir)) {
        $files = glob($cache_dir . '/*');
        $count = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $count++;
            }
        }
        echo "<p style='color: green;'>✅ Cleared $count Twig cache files!</p>";
        echo "<p><a href='/fit/public/'>Try Dashboard Now</a></p>";
    } else {
        echo "<p>No Twig cache directory found.</p>";
    }
}
?>

<hr>
<h3>Quick Cache Clear:</h3>
<p><a href="?clear_cache=1" class="btn" style="padding: 5px 15px; background: #ffc107; color: black; text-decoration: none; border-radius: 3px;">Clear Twig Cache</a></p>