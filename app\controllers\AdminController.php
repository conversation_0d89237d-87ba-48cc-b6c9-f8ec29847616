<?php

namespace App\Controllers;

use Flight;

class AdminController extends BaseController
{
    public function __construct()
    {
        parent::__construct();
        
        // Check admin access
        if (!isset($_SESSION['user']['is_admin']) || !$_SESSION['user']['is_admin']) {
            Flight::redirect('/');
            exit;
        }
    }
    
    /**
     * Admin dashboard
     */
    public function index()
    {
        $this->render('admin/dashboard', [
            'title' => __('admin.admin_dashboard')
        ]);
    }
    
    /**
     * Menu configuration page
     */
    public function menuConfig()
    {
        // Define all available menus
        $menuItems = [
            ['id' => 'patients', 'title' => 'patients.title', 'icon' => 'fas fa-user-injured'],
            ['id' => 'clients', 'title' => 'clients.title', 'icon' => 'bi bi-building'],
            ['id' => 'invoices', 'title' => 'invoices.invoices', 'icon' => 'bi bi-file-earmark-text'],
            ['id' => 'users', 'title' => 'users.users', 'icon' => 'bi bi-people'],
            ['id' => 'config', 'title' => 'config.configuration', 'icon' => 'bi bi-gear'],
            ['id' => 'translations', 'title' => 'translations.translations', 'icon' => 'bi bi-translate'],
            ['id' => 'billing', 'title' => 'common.billing', 'icon' => 'bi bi-cash-coin'],
            ['id' => 'reports', 'title' => 'reports.reports', 'icon' => 'bi bi-graph-up'],
        ];
        
        // Get current settings
        $db = Flight::db();
        $companyId = $_SESSION['company_id'] ?? 1;
        
        try {
            $stmt = $db->prepare("SELECT menu_id, enabled FROM menu_settings WHERE company_id = ?");
            $stmt->execute([$companyId]);
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (Exception $e) {
            $settings = [];
        }
        
        // Apply settings to menu items
        foreach ($menuItems as &$item) {
            if (isset($settings[$item['id']])) {
                $item['enabled'] = (bool)$settings[$item['id']];
            } else {
                // Use default visibility
                $defaultVisibility = [
                    'patients' => true,
                    'clients' => true,
                    'invoices' => true,
                    'users' => true,
                    'config' => true,
                    'translations' => false,
                    'billing' => false,
                    'reports' => true
                ];
                $item['enabled'] = $defaultVisibility[$item['id']] ?? true;
            }
        }
        
        $this->render('admin/menu-config', [
            'title' => __('admin.menu_configuration'),
            'menuItems' => $menuItems
        ]);
    }
    
    /**
     * Save menu configuration
     */
    public function saveMenuConfig()
    {
        $menuItems = Flight::request()->data->menu ?? [];
        $db = Flight::db();
        $companyId = $_SESSION['company_id'] ?? 1;
        
        try {
            // Create table if not exists
            $db->exec("CREATE TABLE IF NOT EXISTS menu_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_id INT NOT NULL,
                menu_id VARCHAR(50) NOT NULL,
                enabled TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_company_menu (company_id, menu_id)
            )");
            
            // Start transaction
            $db->beginTransaction();
            
            // Delete existing settings for this company
            $stmt = $db->prepare("DELETE FROM menu_settings WHERE company_id = ?");
            $stmt->execute([$companyId]);
            
            // Insert new settings
            $stmt = $db->prepare("INSERT INTO menu_settings (company_id, menu_id, enabled) VALUES (?, ?, ?)");
            
            // Define all menu IDs
            $allMenuIds = ['patients', 'clients', 'invoices', 'users', 'config', 'translations', 'billing', 'reports'];
            
            foreach ($allMenuIds as $menuId) {
                $enabled = isset($menuItems[$menuId]) ? 1 : 0;
                $stmt->execute([$companyId, $menuId, $enabled]);
            }
            
            $db->commit();
            
            Flight::json([
                'success' => true,
                'message' => __('admin.menu_config_saved')
            ]);
        } catch (Exception $e) {
            $db->rollBack();
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred')
            ]);
        }
    }
    
    /**
     * Permissions management
     */
    public function permissions()
    {
        $this->render('admin/permissions', [
            'title' => __('admin.permissions')
        ]);
    }
}