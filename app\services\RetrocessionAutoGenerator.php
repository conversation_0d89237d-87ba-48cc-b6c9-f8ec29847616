<?php

namespace App\Services;

use Flight;
use PDO;
use Exception;

class RetrocessionAutoGenerator
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Generate monthly retrocession entries for all practitioners
     * @param int $month Month (1-12)
     * @param int $year Year
     * @return array Results summary
     */
    public function generateMonthlyEntries($month, $year)
    {
        $results = [
            'success' => 0,
            'skipped' => 0,
            'errors' => []
        ];
        
        try {
            // Get all practitioners with monthly amounts configured
            $stmt = $this->db->prepare("
                SELECT DISTINCT u.id, u.first_name, u.last_name, u.email,
                       uma.cns_amount, uma.patient_amount, uma.is_active
                FROM users u
                INNER JOIN user_group_members ugm ON u.id = ugm.user_id
                INNER JOIN user_groups ug ON ugm.group_id = ug.id
                INNER JOIN user_monthly_retrocession_amounts uma ON u.id = uma.user_id
                WHERE ug.slug IN ('practitioners', 'coaches')
                AND u.is_active = 1
                AND uma.month = :month
                AND uma.is_active = 1
            ");
            
            $stmt->execute(['month' => $month]);
            $practitioners = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($practitioners as $practitioner) {
                try {
                    // Get the client ID for this user
                    $clientStmt = $this->db->prepare("
                        SELECT id FROM clients 
                        WHERE user_id = :user_id 
                        AND is_practitioner = 1
                        LIMIT 1
                    ");
                    $clientStmt->execute(['user_id' => $practitioner['id']]);
                    $clientId = $clientStmt->fetchColumn();
                    
                    if (!$clientId) {
                        $results['errors'][] = sprintf(
                            "No practitioner client found for %s %s",
                            $practitioner['first_name'],
                            $practitioner['last_name']
                        );
                        continue;
                    }
                    
                    // Check if entry already exists
                    $existingStmt = $this->db->prepare("
                        SELECT id FROM retrocession_data_entry 
                        WHERE practitioner_id = :practitioner_id 
                        AND period_month = :month 
                        AND period_year = :year
                    ");
                    
                    $existingStmt->execute([
                        'practitioner_id' => $clientId,
                        'month' => $month,
                        'year' => $year
                    ]);
                    
                    if ($existingStmt->fetch()) {
                        $results['skipped']++;
                        continue;
                    }
                    
                    // Create auto-generated entry
                    $this->createAutoEntry($practitioner['id'], $month, $year, 
                                         $practitioner['cns_amount'], 
                                         $practitioner['patient_amount']);
                    
                    $results['success']++;
                    
                } catch (Exception $e) {
                    $results['errors'][] = sprintf(
                        "Failed for %s %s: %s",
                        $practitioner['first_name'],
                        $practitioner['last_name'],
                        $e->getMessage()
                    );
                }
            }
            
        } catch (Exception $e) {
            $results['errors'][] = "General error: " . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Create auto-generated entry
     */
    private function createAutoEntry($userId, $month, $year, $cnsAmount, $patientAmount)
    {
        // Get the client ID for this user
        $stmt = $this->db->prepare("
            SELECT id FROM clients 
            WHERE user_id = :user_id 
            AND is_practitioner = 1
            LIMIT 1
        ");
        $stmt->execute(['user_id' => $userId]);
        $clientId = $stmt->fetchColumn();
        
        if (!$clientId) {
            throw new Exception("No practitioner client found for user ID: $userId");
        }
        
        // Get the monthly amount record ID for reference
        $stmt = $this->db->prepare("
            SELECT id FROM user_monthly_retrocession_amounts 
            WHERE user_id = :user_id AND month = :month
        ");
        $stmt->execute(['user_id' => $userId, 'month' => $month]);
        $monthlyAmountId = $stmt->fetchColumn();
        
        // Create the entry
        $stmt = $this->db->prepare("
            INSERT INTO retrocession_data_entry (
                practitioner_id, period_month, period_year,
                cns_amount, patient_amount,
                data_source, auto_generated_from,
                status, entered_by, entered_at
            ) VALUES (
                :practitioner_id, :period_month, :period_year,
                :cns_amount, :patient_amount,
                'auto_generated', :auto_generated_from,
                'draft', :entered_by, NOW()
            )
        ");
        
        $stmt->execute([
            'practitioner_id' => $clientId,
            'period_month' => $month,
            'period_year' => $year,
            'cns_amount' => $cnsAmount,
            'patient_amount' => $patientAmount,
            'auto_generated_from' => $monthlyAmountId,
            'entered_by' => 1 // System user
        ]);
    }
    
    /**
     * Generate entries for current month
     */
    public function generateCurrentMonth()
    {
        return $this->generateMonthlyEntries(date('n'), date('Y'));
    }
    
    /**
     * Generate entries for next month
     */
    public function generateNextMonth()
    {
        $nextMonth = date('n') + 1;
        $year = date('Y');
        
        if ($nextMonth > 12) {
            $nextMonth = 1;
            $year++;
        }
        
        return $this->generateMonthlyEntries($nextMonth, $year);
    }
}