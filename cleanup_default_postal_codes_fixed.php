<?php
/**
 * Clean up default postal codes for users without actual addresses
 */

// Load .env file directly
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found!");
}

$envContent = file_get_contents($envFile);
$lines = explode("\n", $envContent);
$env = [];

foreach ($lines as $line) {
    $line = trim($line);
    if (empty($line) || strpos($line, '#') === 0) continue;
    
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $env[$key] = $value;
    }
}

// Database configuration from .env
$dbHost = $env['DB_HOST'] ?? '127.0.0.1';
$dbPort = $env['DB_PORT'] ?? '3306';
$dbName = $env['DB_DATABASE'] ?? 'fitapp';
$dbUser = $env['DB_USERNAME'] ?? 'root';
$dbPass = $env['DB_PASSWORD'] ?? '';

echo "<pre>";
echo "=== Cleaning Up Default Postal Codes ===\n";
echo "Database: $dbName\n";
echo "User: $dbUser\n\n";

try {
    // Connect to database using .env credentials
    $dsn = "mysql:host=$dbHost;port=$dbPort;dbname=$dbName;charset=utf8mb4";
    $db = new PDO($dsn, $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "✓ Connected to database successfully\n\n";
    
    // Find users with default postal code but no VAT number
    $stmt = $db->query("
        SELECT id, username, first_name, last_name, 
               postal_code, city, address,
               vat_number, vat_intercommunautaire,
               billing_postal_code, billing_city, billing_address
        FROM users
        WHERE postal_code = 'L-9579'
        AND (vat_number IS NULL OR vat_number = '')
        AND (vat_intercommunautaire IS NULL OR vat_intercommunautaire = '')
    ");
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($users) . " users with default postal code but no VAT number\n\n";
    
    foreach ($users as $user) {
        echo "User: " . $user['first_name'] . " " . $user['last_name'] . " (" . $user['username'] . ")\n";
        echo "  Current postal code: " . $user['postal_code'] . "\n";
        echo "  Current city: " . $user['city'] . "\n";
        echo "  Current address: " . $user['address'] . "\n";
        
        // Check if they have billing address info
        if (!empty($user['billing_postal_code']) && $user['billing_postal_code'] != 'L-9579') {
            echo "  Has different billing postal code: " . $user['billing_postal_code'] . "\n";
            echo "  → Keeping address info as it may be intentional\n";
        } else {
            echo "  → Clearing default address values\n";
            
            // Clear default values only if they match the migration defaults
            $updateStmt = $db->prepare("
                UPDATE users 
                SET postal_code = NULL,
                    city = NULL,
                    address = NULL
                WHERE id = :id
                AND postal_code = 'L-9579'
                AND city = 'Weidingen'
                AND address = '15, am Pëtz'
            ");
            $result = $updateStmt->execute(['id' => $user['id']]);
            
            if ($result && $updateStmt->rowCount() > 0) {
                echo "  ✓ Default address cleared\n";
            } else {
                echo "  ⚠ Address not updated (may have been modified)\n";
            }
        }
        echo "\n";
    }
    
    // Also check for any practitioners specifically
    echo "\n=== Checking Practitioners Specifically ===\n";
    $stmt = $db->query("
        SELECT u.id, u.username, u.first_name, u.last_name, 
               u.postal_code, u.city, u.address,
               u.vat_number, u.vat_intercommunautaire,
               ugm.group_id, ug.name as group_name
        FROM users u
        LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
        LEFT JOIN user_groups ug ON ugm.group_id = ug.id
        WHERE u.postal_code = 'L-9579'
        AND (u.vat_number IS NULL OR u.vat_number = '')
        AND (u.vat_intercommunautaire IS NULL OR u.vat_intercommunautaire = '')
        AND ug.name IN ('Practitioner', 'Praticien', 'Kiné')
    ");
    
    $practitioners = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($practitioners) > 0) {
        echo "Found " . count($practitioners) . " practitioners with default postal code\n\n";
        foreach ($practitioners as $prac) {
            echo "- " . $prac['first_name'] . " " . $prac['last_name'] . " (Group: " . $prac['group_name'] . ")\n";
        }
    } else {
        echo "No additional practitioners found with default postal codes.\n";
    }
    
    echo "\n✅ Cleanup complete!\n";
    echo "\nNote: Only cleared addresses that exactly matched the migration defaults:\n";
    echo "- Address: '15, am Pëtz'\n";
    echo "- Postal Code: 'L-9579'\n";
    echo "- City: 'Weidingen'\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "\nDatabase connection details:\n";
    echo "Host: $dbHost\n";
    echo "Port: $dbPort\n";
    echo "Database: $dbName\n";
    echo "User: $dbUser\n";
    echo "Password: " . (empty($dbPass) ? '(empty)' : '***hidden***') . "\n";
}
echo "</pre>";