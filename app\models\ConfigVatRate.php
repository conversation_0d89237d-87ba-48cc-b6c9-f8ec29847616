<?php
namespace App\Models;

use App\Core\Model;

class ConfigVatRate extends Model
{
    protected $table = 'config_vat_rates';
    
    protected $fillable = [
        'name',
        'rate',
        'code',
        'description',
        'is_active',
        'is_default',
        'sort_order',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'rate' => 'decimal:2',
        'is_active' => 'boolean',
        'is_default' => 'boolean',
        'sort_order' => 'integer'
    ];
    
    /**
     * Scope for active VAT rates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    /**
     * Get catalog items using this VAT rate
     */
    public function catalogItems()
    {
        return $this->hasMany(CatalogItem::class, 'vat_rate_id');
    }
    
    /**
     * Get invoices using this VAT rate
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'vat_rate_id');
    }
    
    /**
     * Format rate with percentage
     */
    public function formatRate()
    {
        return number_format($this->rate, 2, ',', ' ') . '%';
    }
    
    /**
     * Get VAT rates for dropdown
     */
    public static function getForDropdown($includeInactive = false)
    {
        $query = static::orderBy('sort_order')->orderBy('rate');
        
        if (!$includeInactive) {
            $query->active();
        }
        
        return $query->pluck('name', 'id')->toArray();
    }
    
    /**
     * Calculate VAT amount from gross amount
     */
    public function calculateVatFromGross($grossAmount)
    {
        return $grossAmount - ($grossAmount / (1 + $this->rate / 100));
    }
    
    /**
     * Calculate VAT amount from net amount
     */
    public function calculateVatFromNet($netAmount)
    {
        return $netAmount * ($this->rate / 100);
    }
    
    /**
     * Get standard VAT rate
     */
    public static function getStandardRate()
    {
        return static::active()
                    ->where('code', 'STANDARD')
                    ->orWhere('rate', 17)
                    ->first();
    }
    
    /**
     * Get localized name
     */
    public function getLocalizedName($locale = null)
    {
        if (!$locale) {
            $locale = $_SESSION['language'] ?? 'en';
        }
        
        // Check if name is JSON
        $names = json_decode($this->name, true);
        
        if (is_array($names)) {
            // Return localized version or fallback to English or first available
            return $names[$locale] ?? $names['en'] ?? reset($names);
        }
        
        // If not JSON, return as is
        return $this->name;
    }
    
    /**
     * Get display name (alias for getLocalizedName)
     */
    public function getDisplayName($locale = null)
    {
        return $this->getLocalizedName($locale);
    }
}