<?php

namespace App\Core;

use Flight;

class Response
{
    /**
     * Send JSON response
     */
    public function json($data, $code = 200)
    {
        Flight::json($data, $code);
        return $this;
    }
    
    /**
     * Send success response
     */
    public function success($message = 'Success', $data = null, $code = 200)
    {
        $response = [
            'success' => true,
            'message' => $message
        ];
        
        if ($data !== null) {
            $response['data'] = $data;
        }
        
        return $this->json($response, $code);
    }
    
    /**
     * Send error response
     */
    public function error($message = 'Error', $errors = null, $code = 400)
    {
        $response = [
            'success' => false,
            'message' => $message
        ];
        
        if ($errors !== null) {
            $response['errors'] = $errors;
        }
        
        return $this->json($response, $code);
    }
    
    /**
     * Send redirect response
     */
    public function redirect($url, $code = 302)
    {
        Flight::redirect($url, $code);
        return $this;
    }
    
    /**
     * Send view response
     */
    public function view($template, $data = [])
    {
        $view = Flight::get('view');
        
        // Add common data
        $data['app_name'] = Flight::get('config')['app_name'] ?? 'Fit360 AdminDesk';
        $data['session'] = $_SESSION ?? [];
        
        echo $view->render($template . '.twig', $data);
        return $this;
    }
    
    /**
     * Set response header
     */
    public function header($name, $value)
    {
        header("$name: $value");
        return $this;
    }
    
    /**
     * Set response status code
     */
    public function status($code)
    {
        http_response_code($code);
        return $this;
    }
}