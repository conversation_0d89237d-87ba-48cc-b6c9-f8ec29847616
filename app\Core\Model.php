<?php

namespace App\Core;

use Flight;
use PDO;
use App\Helpers\MoneyHelper;

abstract class Model
{
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $casts = [];
    protected $attributes = [];
    
    /**
     * Fields that contain monetary values and should be rounded
     */
    protected $monetary = [];
    
    /**
     * Get database connection
     */
    public static function db()
    {
        return Flight::db();
    }
    
    /**
     * Get table name
     */
    public function getTable()
    {
        return $this->table;
    }
    
    /**
     * Find a record by ID
     */
    public static function find($id)
    {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table} WHERE {$instance->primaryKey} = :id LIMIT 1";
        $stmt = self::db()->prepare($sql);
        $stmt->execute(['id' => $id]);
        
        $data = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($data) {
            $instance->fill($data);
            return $instance;
        }
        
        return null;
    }
    
    /**
     * Find or fail
     */
    public static function findOrFail($id)
    {
        $result = self::find($id);
        if (!$result) {
            throw new \Exception("Record not found");
        }
        return $result;
    }
    
    /**
     * Get all records
     */
    public static function all()
    {
        $instance = new static();
        $sql = "SELECT * FROM {$instance->table}";
        $stmt = self::db()->query($sql);
        
        $results = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $model = new static();
            $model->fill($row);
            $results[] = $model;
        }
        
        return new Collection($results);
    }
    
    /**
     * Create a new record
     */
    public static function create($data)
    {
        $instance = new static();
        $instance->fill($data);
        $instance->save();
        return $instance;
    }
    
    /**
     * Update or create
     */
    public static function updateOrCreate($attributes, $values)
    {
        $instance = self::where($attributes)->first();
        
        if ($instance) {
            $instance->fill($values);
            $instance->save();
        } else {
            $instance = self::create(array_merge($attributes, $values));
        }
        
        return $instance;
    }
    
    /**
     * Fill model with data
     */
    public function fill($data)
    {
        foreach ($data as $key => $value) {
            if (in_array($key, $this->fillable) || $key === $this->primaryKey) {
                // Apply monetary rounding if field is defined as monetary
                if (in_array($key, $this->monetary) && is_numeric($value)) {
                    $value = MoneyHelper::round($value);
                }
                $this->attributes[$key] = $this->castAttribute($key, $value);
            }
        }
        return $this;
    }
    
    /**
     * Save the model
     */
    public function save()
    {
        if (isset($this->attributes[$this->primaryKey])) {
            return $this->performUpdate();
        } else {
            return $this->performInsert();
        }
    }
    
    /**
     * Delete the model
     */
    public function delete()
    {
        if (!isset($this->attributes[$this->primaryKey])) {
            return false;
        }
        
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = self::db()->prepare($sql);
        return $stmt->execute(['id' => $this->attributes[$this->primaryKey]]);
    }
    
    /**
     * Perform insert
     */
    protected function performInsert()
    {
        $fields = array_keys($this->attributes);
        $placeholders = array_map(function($field) { return ':' . $field; }, $fields);
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = self::db()->prepare($sql);
        
        // Prepare values for binding, converting arrays to JSON
        $params = [];
        foreach ($this->attributes as $key => $value) {
            if (is_array($value) && isset($this->casts[$key]) && in_array($this->casts[$key], ['json', 'array'])) {
                $params[$key] = json_encode($value);
            } else {
                $params[$key] = $value;
            }
        }
        
        if ($stmt->execute($params)) {
            $this->attributes[$this->primaryKey] = self::db()->lastInsertId();
            return true;
        }
        
        return false;
    }
    
    /**
     * Perform update
     */
    protected function performUpdate()
    {
        $fields = [];
        $params = [];
        
        foreach ($this->attributes as $key => $value) {
            if ($key !== $this->primaryKey) {
                $fields[] = "$key = :$key";
                // Convert arrays to JSON for json/array casted fields
                if (is_array($value) && isset($this->casts[$key]) && in_array($this->casts[$key], ['json', 'array'])) {
                    $params[$key] = json_encode($value);
                } else {
                    $params[$key] = $value;
                }
            }
        }
        
        $params['id'] = $this->attributes[$this->primaryKey];
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE {$this->primaryKey} = :id";
        $stmt = self::db()->prepare($sql);
        
        return $stmt->execute($params);
    }
    
    /**
     * Cast attribute
     */
    protected function castAttribute($key, $value)
    {
        if (!isset($this->casts[$key])) {
            return $value;
        }
        
        switch ($this->casts[$key]) {
            case 'int':
            case 'integer':
                return $value === null ? null : (int) $value;
            case 'float':
            case 'double':
                return $value === null ? null : (float) $value;
            case 'bool':
            case 'boolean':
                return (bool) $value;
            case 'array':
            case 'json':
                return is_string($value) ? json_decode($value, true) : $value;
            default:
                return $value;
        }
    }
    
    /**
     * Get an attribute
     */
    public function __get($key)
    {
        return $this->attributes[$key] ?? null;
    }
    
    /**
     * Set an attribute
     */
    public function __set($key, $value)
    {
        $this->attributes[$key] = $this->castAttribute($key, $value);
    }
    
    /**
     * Check if attribute exists
     */
    public function __isset($key)
    {
        return isset($this->attributes[$key]);
    }
    
    /**
     * Convert to array
     */
    public function toArray()
    {
        return $this->attributes;
    }
    
    /**
     * Count records
     */
    public static function count()
    {
        $instance = new static();
        $sql = "SELECT COUNT(*) as count FROM {$instance->table}";
        $stmt = self::db()->query($sql);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int) $result['count'];
    }
    
    /**
     * Simple where clause
     */
    public static function where($field, $operator = null, $value = null)
    {
        if (is_array($field)) {
            return new QueryBuilder(new static(), $field);
        }

        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        return new QueryBuilder(new static(), [[$field, $operator, $value]]);
    }

    /**
     * Where null clause
     */
    public static function whereNull($field)
    {
        return new QueryBuilder(new static(), [[$field, 'IS', null]]);
    }

    /**
     * Where not null clause
     */
    public static function whereNotNull($field)
    {
        return new QueryBuilder(new static(), [[$field, 'IS NOT', null]]);
    }

    /**
     * Where in clause
     */
    public static function whereIn($field, $values)
    {
        return new QueryBuilder(new static(), [[$field, 'IN', $values]]);
    }

    /**
     * Where not in clause
     */
    public static function whereNotIn($field, $values)
    {
        return new QueryBuilder(new static(), [[$field, 'NOT IN', $values]]);
    }
    
    /**
     * Order by
     */
    public static function orderBy($field, $direction = 'ASC')
    {
        return new QueryBuilder(new static(), [], [[$field, $direction]]);
    }
    
    /**
     * Update multiple records
     */
    public function update($data)
    {
        $this->fill($data);
        return $this->save();
    }
    
    /**
     * Define a one-to-many relationship
     */
    protected function hasMany($relatedModel, $foreignKey = null, $localKey = null)
    {
        if (!$foreignKey) {
            // Default foreign key is table_name_id (e.g., category_id)
            $foreignKey = rtrim($this->table, 's') . '_id';
        }
        
        if (!$localKey) {
            $localKey = $this->primaryKey;
        }
        
        $relatedInstance = new $relatedModel();
        return $relatedInstance::where($foreignKey, '=', $this->attributes[$localKey]);
    }
    
    /**
     * Define a belongs-to relationship
     */
    protected function belongsTo($relatedModel, $foreignKey = null, $ownerKey = null)
    {
        if (!$foreignKey) {
            // Default foreign key is related_table_id (e.g., parent_id)
            $relatedInstance = new $relatedModel();
            $foreignKey = rtrim($relatedInstance->getTable(), 's') . '_id';
        }
        
        if (!$ownerKey) {
            $ownerKey = 'id';
        }
        
        $relatedInstance = new $relatedModel();
        if (isset($this->attributes[$foreignKey])) {
            return $relatedInstance::where($ownerKey, '=', $this->attributes[$foreignKey]);
        }
        
        return new QueryBuilder($relatedInstance, []);
    }
    
    /**
     * Define a has-one relationship
     */
    protected function hasOne($relatedModel, $foreignKey = null, $localKey = null)
    {
        return $this->hasMany($relatedModel, $foreignKey, $localKey);
    }
}