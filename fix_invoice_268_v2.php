<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Helpers\MoneyHelper;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Fixing Invoice 268 (v2) ===\n\n";
    
    // Get invoice 268
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 268");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "Invoice 268 not found.\n";
        exit;
    }
    
    echo "Current invoice details:\n";
    echo "Number: " . $invoice['invoice_number'] . "\n";
    echo "Total: €" . number_format($invoice['total'], 2) . "\n";
    echo "Subtotal: €" . number_format($invoice['subtotal'], 2) . "\n";
    echo "VAT: €" . number_format($invoice['vat_amount'], 2) . "\n\n";
    
    // First check invoice_items table
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM invoice_items WHERE invoice_id = 268");
    $stmt->execute();
    $itemCount = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($itemCount == 0) {
        echo "No items in invoice_items table, checking invoice_lines table...\n";
        
        // Check invoice_lines table
        $stmt = $pdo->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 268 ORDER BY id");
        $stmt->execute();
        $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($lines)) {
            echo "\nFound " . count($lines) . " lines in invoice_lines table:\n";
            
            $totalQuantity = 0;
            $totalAmount = 0;
            
            foreach ($lines as $line) {
                echo sprintf("- %s: Qty %s, Amount: €%.2f\n", 
                    $line['description'],
                    $line['quantity'] ?? '1',
                    $line['line_total']
                );
                
                // Try to extract quantity from description or use 1
                $qty = floatval($line['quantity'] ?? 1);
                if ($qty == 0 && preg_match('/(\d+)\s*(séance|session|cours)/i', $line['description'], $matches)) {
                    $qty = intval($matches[1]);
                }
                if ($qty == 0) $qty = 1;
                
                $totalQuantity += $qty;
                $totalAmount += floatval($line['line_total']);
            }
            
            echo "\nTotal from lines: €" . number_format($totalAmount, 2) . "\n";
            
            // For LOC invoices, we expect €30 TTC per session
            // If current total is 179.48 + 30.51 = 209.99, this suggests 7 sessions
            // 7 x 30 = 210 TTC
            
            // Let's calculate based on current total
            $currentTotal = floatval($invoice['total']);
            $sessionsEstimate = round($currentTotal / 30);
            
            echo "\nEstimated sessions based on total: $sessionsEstimate\n";
            
            if ($sessionsEstimate == 7) {
                $expectedTTC = 210.00; // 7 x €30
            } else {
                $expectedTTC = $sessionsEstimate * 30.00;
            }
        } else {
            echo "No lines found in invoice_lines table either.\n";
            exit;
        }
    } else {
        // Use invoice_items table
        $stmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = 268 ORDER BY id");
        $stmt->execute();
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $totalQuantity = 0;
        foreach ($items as $item) {
            $totalQuantity += $item['quantity'];
        }
        
        $expectedTTC = $totalQuantity * 30.00; // €30 per session TTC
    }
    
    echo "\nExpected TTC total: €" . number_format($expectedTTC, 2) . "\n";
    
    // Recalculate using TTC method
    $vatRate = 17.00; // Luxembourg VAT rate
    $ttcCalc = MoneyHelper::calculateFromTTC($expectedTTC, $vatRate);
    
    echo "\nCorrect calculation:\n";
    echo "TTC Total: €" . number_format($ttcCalc['total'], 2) . "\n";
    echo "NET (Subtotal): €" . number_format($ttcCalc['net'], 2) . "\n";
    echo "VAT: €" . number_format($ttcCalc['vat'], 2) . "\n";
    
    // Update the invoice
    if (abs($invoice['total'] - $expectedTTC) > 0.01) {
        echo "\nUpdating invoice to correct total...\n";
        
        $stmt = $pdo->prepare("
            UPDATE invoices 
            SET subtotal = ?,
                vat_amount = ?,
                total = ?,
                updated_at = NOW()
            WHERE id = 268
        ");
        $stmt->execute([$ttcCalc['net'], $ttcCalc['vat'], $ttcCalc['total']]);
        
        echo "✓ Invoice 268 has been fixed!\n";
        echo "New total: €" . number_format($ttcCalc['total'], 2) . "\n";
    } else {
        echo "\n✓ Invoice 268 already has the correct total of €" . number_format($expectedTTC, 2) . "\n";
    }
    
    // Verify the fix
    $stmt = $pdo->prepare("SELECT total FROM invoices WHERE id = 268");
    $stmt->execute();
    $newTotal = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "\nFinal verification: Invoice 268 total is now €" . number_format($newTotal, 2) . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}