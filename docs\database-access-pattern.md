# Database Access Pattern for Controllers

## Correct Pattern

In all controllers that extend from `App\Core\Controller`, you must use `Flight::db()` to access the database connection, not `$this->db`.

### ✅ Correct Usage

```php
// Simple query
$stmt = Flight::db()->query("SELECT * FROM users");
$users = $stmt->fetchAll();

// Prepared statement
$stmt = Flight::db()->prepare("SELECT * FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch();

// Insert with last insert ID
$stmt = Flight::db()->prepare("INSERT INTO users (name, email) VALUES (?, ?)");
$stmt->execute([$name, $email]);
$userId = Flight::db()->lastInsertId();
```

### ❌ Incorrect Usage

```php
// DO NOT USE:
$stmt = $this->db->query("SELECT * FROM users");
$stmt = $this->db->prepare("SELECT * FROM users WHERE id = ?");
$id = $this->db->lastInsertId();
```

## Why This Pattern?

1. **Consistency**: The base Controller class uses `Flight::db()` for database access
2. **Framework Integration**: Flight provides the database connection through its service container
3. **No Property Definition**: Controllers don't have a `$this->db` property defined
4. **Global Access**: `Flight::db()` can be accessed from anywhere in the application

## Fixed Controllers

The following controllers have been updated to use the correct pattern:
- `ConfigController.php` - Fixed 7 occurrences
- `ErrorLogController.php` - Fixed 7 occurrences  
- `EmailTemplateController.php` - Fixed 7 occurrences

## Prevention

To prevent this issue in the future:

1. **Code Reviews**: Check for `$this->db` usage in controller files
2. **IDE Configuration**: Set up inspections to flag `$this->db` usage in controllers
3. **Documentation**: Ensure all developers are aware of the correct pattern
4. **Base Controller**: The base Controller class correctly demonstrates the pattern

## Quick Check Command

To verify no controllers are using the incorrect pattern:

```bash
grep -r '\$this->db' app/controllers/
```

This should return no results if all controllers are using the correct pattern.