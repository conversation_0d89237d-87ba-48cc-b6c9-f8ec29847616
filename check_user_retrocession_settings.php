<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\User;
use App\Models\UserRetrocessionSetting;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_NAME'] ?? 'healthcenter_billing';
$username = $_ENV['DB_USER'] ?? 'root';
$password = $_ENV['DB_PASS'] ?? '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n\n";
    
    // Get user ID from command line or prompt
    $userId = $argv[1] ?? null;
    
    if (!$userId) {
        echo "Usage: php check_user_retrocession_settings.php [user_id]\n";
        echo "\nListing all users with retrocession settings:\n";
        
        $stmt = $pdo->query("
            SELECT DISTINCT u.id, u.name, u.email, 
                   GROUP_CONCAT(DISTINCT g.name) as groups
            FROM users u
            INNER JOIN user_retrocession_settings urs ON u.id = urs.user_id
            LEFT JOIN user_group ug ON u.id = ug.user_id
            LEFT JOIN groups g ON ug.group_id = g.id
            WHERE urs.is_active = 1
            GROUP BY u.id
            ORDER BY u.name
        ");
        
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($users as $user) {
            echo sprintf("ID: %d | Name: %s | Email: %s | Groups: %s\n", 
                $user['id'], 
                $user['name'], 
                $user['email'],
                $user['groups'] ?? 'None'
            );
        }
        exit;
    }
    
    // Get user info
    $stmt = $pdo->prepare("
        SELECT u.*, GROUP_CONCAT(g.name) as groups
        FROM users u
        LEFT JOIN user_group ug ON u.id = ug.user_id
        LEFT JOIN groups g ON ug.group_id = g.id
        WHERE u.id = ?
        GROUP BY u.id
    ");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "User with ID $userId not found.\n";
        exit;
    }
    
    echo "User Information:\n";
    echo "ID: " . $user['id'] . "\n";
    echo "Name: " . $user['name'] . "\n";
    echo "Email: " . $user['email'] . "\n";
    echo "Groups: " . ($user['groups'] ?? 'None') . "\n";
    echo "\n";
    
    // Get all retrocession settings for this user
    $stmt = $pdo->prepare("
        SELECT urs.*, rp.name as profile_name
        FROM user_retrocession_settings urs
        LEFT JOIN rate_profiles rp ON urs.rate_profile_id = rp.id
        WHERE urs.user_id = ?
        ORDER BY urs.valid_from DESC, urs.is_active DESC
    ");
    $stmt->execute([$userId]);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($settings)) {
        echo "No retrocession settings found for this user.\n";
        exit;
    }
    
    echo "Retrocession Settings:\n";
    echo str_repeat("-", 100) . "\n";
    
    foreach ($settings as $setting) {
        $status = $setting['is_active'] ? 'ACTIVE' : 'INACTIVE';
        $validFrom = date('d/m/Y', strtotime($setting['valid_from']));
        $validTo = $setting['valid_to'] ? date('d/m/Y', strtotime($setting['valid_to'])) : 'No end date';
        
        echo sprintf(
            "ID: %d | Status: %s | Profile: %s | Valid: %s - %s\n",
            $setting['id'],
            $status,
            $setting['profile_name'] ?? 'None',
            $validFrom,
            $validTo
        );
        
        // Check for overlaps with other active settings
        if ($setting['is_active']) {
            $overlapStmt = $pdo->prepare("
                SELECT COUNT(*) as overlap_count
                FROM user_retrocession_settings
                WHERE user_id = ?
                AND id != ?
                AND is_active = 1
                AND (
                    (valid_from <= ? AND (valid_to IS NULL OR valid_to >= ?))
                    OR (valid_from <= ? AND (valid_to IS NULL OR valid_to >= ?))
                    OR (? <= valid_from AND (? IS NULL OR ? >= valid_to))
                )
            ");
            
            $overlapStmt->execute([
                $userId,
                $setting['id'],
                $setting['valid_from'], $setting['valid_from'],
                $setting['valid_to'], $setting['valid_to'],
                $setting['valid_from'], $setting['valid_to'], $setting['valid_to']
            ]);
            
            $overlaps = $overlapStmt->fetch(PDO::FETCH_ASSOC);
            if ($overlaps['overlap_count'] > 0) {
                echo "  ⚠️  WARNING: This setting has date overlaps with " . $overlaps['overlap_count'] . " other active setting(s)\n";
            }
        }
    }
    
    echo str_repeat("-", 100) . "\n";
    
    // Show active date ranges
    echo "\nActive Date Coverage:\n";
    $activeSettings = array_filter($settings, function($s) { return $s['is_active']; });
    
    if (empty($activeSettings)) {
        echo "No active retrocession settings.\n";
    } else {
        foreach ($activeSettings as $setting) {
            $validFrom = date('d/m/Y', strtotime($setting['valid_from']));
            $validTo = $setting['valid_to'] ? date('d/m/Y', strtotime($setting['valid_to'])) : 'Ongoing';
            echo "  • " . $validFrom . " to " . $validTo . " (Profile: " . ($setting['profile_name'] ?? 'None') . ")\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}