{% extends "base-modern.twig" %}

{% block title %}{{ __('config.email_templates') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-envelope-open-text me-2"></i>{{ __('config.email_templates') }}
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#variablesModal">
                <i class="bi bi-code-slash me-2"></i>{{ __('config.template_variables') }}
            </button>
            <button type="button" class="btn btn-secondary" onclick="showTemplateStats()">
                <i class="bi bi-graph-up me-2"></i>{{ __('common.statistics') }}
            </button>
            <a href="{{ base_url }}/email-templates/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('common.create_new') }}
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-3">
                    <label class="form-label small">{{ __('config.invoice_type') }}</label>
                    <select class="form-select" id="filterInvoiceType" onchange="filterTemplates()">
                        <option value="">{{ __('common.all') }}</option>
                        {% for type in invoice_types %}
                        <option value="{{ type.code }}">{{ type.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label small">{{ __('config.email_type') }}</label>
                    <select class="form-select" id="filterEmailType" onchange="filterTemplates()">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="invoice_new">{{ __('email.type_invoice_new') }}</option>
                        <option value="invoice_reminder">{{ __('email.type_invoice_reminder') }}</option>
                        <option value="invoice_overdue">{{ __('email.type_invoice_overdue') }}</option>
                        <option value="invoice_partial">{{ __('email.type_invoice_partial') }}</option>
                        <option value="invoice_paid">{{ __('email.type_invoice_paid') }}</option>
                        <option value="invoice_cancelled">{{ __('email.type_invoice_cancelled') }}</option>
                        <option value="quote_new">{{ __('email.type_quote_new') }}</option>
                        <option value="appointment_reminder">{{ __('email.type_appointment_reminder') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">{{ __('config.priority') }}</label>
                    <select class="form-select" id="filterPriority" onchange="filterTemplates()">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="1">⚠️ {{ __('priority.critical') }}</option>
                        <option value="2">🔴 {{ __('priority.high') }}</option>
                        <option value="3">🟡 {{ __('priority.medium') }}</option>
                        <option value="4">🟢 {{ __('priority.low') }}</option>
                        <option value="5">⚪ {{ __('priority.default') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">{{ __('common.status') }}</label>
                    <select class="form-select" id="filterStatus" onchange="filterTemplates()">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="1">{{ __('common.active') }}</option>
                        <option value="0">{{ __('common.inactive') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label small">{{ __('common.search') }}</label>
                    <input type="text" class="form-control" id="searchTemplates" placeholder="{{ __('common.search') }}..." onkeyup="filterTemplates()">
                </div>
            </div>
        </div>
    </div>

    <!-- Templates Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('config.email_templates_list') }}</h5>
                <span class="badge bg-primary" id="templateCount">{{ templates|length }} {{ __('common.templates') }}</span>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="templatesTable">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 60px;">{{ __('common.priority') }}</th>
                            <th>{{ __('common.name') }}</th>
                            <th>{{ __('config.email_type') }}</th>
                            <th>{{ __('config.invoice_type') }}</th>
                            <th class="text-center">{{ __('config.usage_count') }}</th>
                            <th class="text-center">{{ __('common.status') }}</th>
                            <th class="text-center" style="width: 120px;">{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for template in templates %}
                        <tr data-template-id="{{ template.id }}" 
                            data-invoice-type="{{ template.invoice_type }}"
                            data-email-type="{{ template.email_type }}"
                            data-priority="{{ template.priority }}"
                            data-status="{{ template.is_active ? '1' : '0' }}"
                            data-name="{{ template.name|lower }}">
                            <td class="text-center">
                                {% if template.priority == 1 %}
                                    <span class="badge bg-danger" data-bs-toggle="tooltip" title="{{ __('priority.critical') }}">⚠️ P1</span>
                                {% elseif template.priority == 2 %}
                                    <span class="badge bg-warning" data-bs-toggle="tooltip" title="{{ __('priority.high') }}">🔴 P2</span>
                                {% elseif template.priority == 3 %}
                                    <span class="badge bg-info" data-bs-toggle="tooltip" title="{{ __('priority.medium') }}">🟡 P3</span>
                                {% elseif template.priority == 4 %}
                                    <span class="badge bg-success" data-bs-toggle="tooltip" title="{{ __('priority.low') }}">🟢 P4</span>
                                {% else %}
                                    <span class="badge bg-secondary" data-bs-toggle="tooltip" title="{{ __('priority.default') }}">⚪ P5</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <h6 class="mb-0">{{ template.name }}</h6>
                                        <small class="text-muted">{{ template.code }}</small>
                                        {% if template.parent_template_id %}
                                        <small class="text-info ms-2" data-bs-toggle="tooltip" title="{{ __('config.inherits_from') }}">
                                            <i class="bi bi-diagram-2"></i> {{ __('config.inherited') }}
                                        </small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">
                                    {{ template.email_type ? __('email.type_' ~ template.email_type) : '-' }}
                                </span>
                            </td>
                            <td>
                                {% if template.invoice_type %}
                                    <span class="badge bg-primary">{{ invoice_types[template.invoice_type].name ?? template.invoice_type }}</span>
                                {% else %}
                                    <span class="text-muted">{{ __('common.all') }}</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <span class="badge bg-light text-dark">{{ template.usage_count|default(0) }}</span>
                            </td>
                            <td class="text-center">
                                {% if template.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ base_url }}/email-templates/{{ template.id }}/test" 
                                       class="btn btn-outline-info" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('config.test_template') }}">
                                        <i class="bi bi-send"></i>
                                    </a>
                                    <a href="{{ base_url }}/email-templates/{{ template.id }}/edit" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-warning" 
                                            onclick="duplicateTemplate({{ template.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.duplicate') }}">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    {% if not template.is_system %}
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="deleteTemplate({{ template.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                <div id="noResultsMessage" class="text-center py-5" style="display: none;">
                    <i class="bi bi-search fs-1 text-muted"></i>
                    <p class="text-muted mt-2">{{ __('common.no_results_found') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Variables Modal -->
<div class="modal fade" id="variablesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.available_variables') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">{{ __('config.invoice_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{invoice_number}</code>
                                <small class="d-block text-muted">{{ __('config.var_invoice_number') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{invoice_date}</code>
                                <small class="d-block text-muted">{{ __('config.var_invoice_date') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{due_date}</code>
                                <small class="d-block text-muted">{{ __('config.var_due_date') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{total_amount}</code>
                                <small class="d-block text-muted">{{ __('config.var_total_amount') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{subtotal}</code>
                                <small class="d-block text-muted">{{ __('config.var_subtotal') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{vat_amount}</code>
                                <small class="d-block text-muted">{{ __('config.var_vat_amount') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{balance_due}</code>
                                <small class="d-block text-muted">{{ __('config.var_balance_due') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{payment_link}</code>
                                <small class="d-block text-muted">{{ __('config.var_payment_link') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{invoice_link}</code>
                                <small class="d-block text-muted">{{ __('config.var_invoice_link') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">{{ __('config.client_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{client_name}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_name') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_email}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_email') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_phone}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_phone') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_address}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_address') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_city}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_city') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_postal_code}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_postal_code') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_country}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_country') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_vat}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_vat') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">{{ __('config.company_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{company_name}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_name') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_email}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_email') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_phone}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_phone') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_address}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_address') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_website}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_website') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_vat}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_vat') }}</small>
                            </div>
                        </div>
                        
                        <h6 class="text-primary mb-3 mt-4">{{ __('config.special_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{items_table}</code>
                                <small class="d-block text-muted">{{ __('config.var_items_table') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{payment_history}</code>
                                <small class="d-block text-muted">{{ __('config.var_payment_history') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{current_year}</code>
                                <small class="d-block text-muted">{{ __('config.var_current_year') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{unsubscribe_link}</code>
                                <small class="d-block text-muted">{{ __('config.var_unsubscribe_link') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-4">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>{{ __('config.tip') }}:</strong> {{ __('config.variables_usage_tip') }}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Stats Modal -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.template_statistics') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="statsContent">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{{ __('common.loading') }}...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Filter templates
function filterTemplates() {
    const invoiceType = document.getElementById('filterInvoiceType').value.toLowerCase();
    const emailType = document.getElementById('filterEmailType').value.toLowerCase();
    const priority = document.getElementById('filterPriority').value;
    const status = document.getElementById('filterStatus').value;
    const search = document.getElementById('searchTemplates').value.toLowerCase();
    
    const rows = document.querySelectorAll('#templatesTable tbody tr');
    let visibleCount = 0;
    
    rows.forEach(row => {
        const rowInvoiceType = row.dataset.invoiceType?.toLowerCase() || '';
        const rowEmailType = row.dataset.emailType?.toLowerCase() || '';
        const rowPriority = row.dataset.priority;
        const rowStatus = row.dataset.status;
        const rowName = row.dataset.name;
        
        let show = true;
        
        if (invoiceType && rowInvoiceType !== invoiceType) show = false;
        if (emailType && rowEmailType !== emailType) show = false;
        if (priority && rowPriority !== priority) show = false;
        if (status && rowStatus !== status) show = false;
        if (search && !rowName.includes(search)) show = false;
        
        row.style.display = show ? '' : 'none';
        if (show) visibleCount++;
    });
    
    // Update count
    document.getElementById('templateCount').textContent = `${visibleCount} {{ __('common.templates') }}`;
    
    // Show/hide no results message
    document.getElementById('noResultsMessage').style.display = visibleCount === 0 ? 'block' : 'none';
}

// Duplicate template
function duplicateTemplate(id) {
    if (!confirm('{{ __("config.duplicate_template_confirm") }}')) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `{{ base_url }}/email-templates/${id}/duplicate`;
    
    const csrf = document.createElement('input');
    csrf.type = 'hidden';
    csrf.name = 'csrf_token';
    csrf.value = '{{ csrf_token }}';
    form.appendChild(csrf);
    
    document.body.appendChild(form);
    form.submit();
}

// Delete template
function deleteTemplate(id) {
    if (!confirm('{{ __("config.delete_template_confirm") }}')) return;
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `{{ base_url }}/email-templates/${id}`;
    
    const method = document.createElement('input');
    method.type = 'hidden';
    method.name = '_method';
    method.value = 'DELETE';
    form.appendChild(method);
    
    const csrf = document.createElement('input');
    csrf.type = 'hidden';
    csrf.name = 'csrf_token';
    csrf.value = '{{ csrf_token }}';
    form.appendChild(csrf);
    
    document.body.appendChild(form);
    form.submit();
}

// Show template statistics
function showTemplateStats() {
    const modal = new bootstrap.Modal(document.getElementById('statsModal'));
    modal.show();
    
    // Load stats via AJAX
    fetch('{{ base_url }}/api/email-templates/stats')
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                document.getElementById('statsContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>${data.error}
                    </div>
                `;
                return;
            }
            
            // Build stats HTML
            let html = '<div class="row">';
            
            // Summary stats
            html += `
                <div class="col-md-12 mb-4">
                    <h6 class="text-muted mb-3">{{ __('config.summary') }}</h6>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h5 class="card-title">${data.summary.total_templates}</h5>
                                    <p class="card-text mb-0">{{ __('config.total_templates') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h5 class="card-title">${data.summary.total_emails_30d}</h5>
                                    <p class="card-text mb-0">{{ __('config.emails_sent_30d') }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h5 class="card-title">${data.summary.avg_emails_per_day}</h5>
                                    <p class="card-text mb-0">{{ __('config.avg_emails_per_day') }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Template usage
            html += `
                <div class="col-md-12">
                    <h6 class="text-muted mb-3">{{ __('config.template_usage') }}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ __('common.template') }}</th>
                                    <th class="text-center">{{ __('config.emails_sent') }}</th>
                                    <th class="text-center">{{ __('config.last_used') }}</th>
                                </tr>
                            </thead>
                            <tbody>
            `;
            
            data.templates.forEach(template => {
                html += `
                    <tr>
                        <td>${template.name}</td>
                        <td class="text-center">${template.email_count || 0}</td>
                        <td class="text-center">${template.last_used ? new Date(template.last_used).toLocaleDateString() : '-'}</td>
                    </tr>
                `;
            });
            
            html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            `;
            
            document.getElementById('statsContent').innerHTML = html;
        })
        .catch(error => {
            document.getElementById('statsContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>{{ __('common.error_loading_data') }}
                </div>
            `;
        });
}

// Initialize tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}