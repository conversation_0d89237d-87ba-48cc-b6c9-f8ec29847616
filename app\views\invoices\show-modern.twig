{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.invoice') }} {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.invoice') }} {{ invoice.invoice_number }}</h1>
            <div class="mt-2">
                {% if invoice.status == 'draft' %}
                    <span class="badge bg-secondary">{{ __('invoices.status.draft') }}</span>
                {% elseif invoice.status == 'sent' %}
                    <span class="badge bg-info">{{ __('invoices.status.sent') }}</span>
                {% elseif invoice.status == 'paid' %}
                    <span class="badge bg-success">{{ __('invoices.status.paid') }}</span>
                {% elseif invoice.status == 'partial' %}
                    <span class="badge bg-warning">{{ __('invoices.status.partial') }}</span>
                {% elseif invoice.status == 'overdue' %}
                    <span class="badge bg-danger">{{ __('invoices.status.overdue') }}</span>
                {% elseif invoice.status == 'cancelled' %}
                    <span class="badge bg-dark">{{ __('invoices.status.cancelled') }}</span>
                {% endif %}
            </div>
        </div>
        <div class="d-flex gap-2">
            {% if invoice.status == 'draft' %}
                <a href="{{ base_url }}/invoices/{{ invoice.id }}/edit" class="btn btn-primary">
                    <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                </a>
                <button type="button" class="btn btn-success" onclick="markAsSent()">
                    <i class="bi bi-send me-2"></i>{{ __('invoices.mark_as_sent')|default('Marquer comme envoyée') }}
                </button>
            {% endif %}
            <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" 
                        data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical me-2"></i>{{ __('common.actions') }}
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank">
                            <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/download">
                            <i class="bi bi-download me-2"></i>{{ __('invoices.download_pdf') }}
                        </a>
                    </li>
                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="sendInvoice()">
                            <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_invoice') }}
                        </a>
                    </li>
                    {% endif %}
                    {% if invoice.status == 'sent' or invoice.status == 'partial' or invoice.status == 'overdue' %}
                    <li>
                        <a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#paymentModal">
                            <i class="bi bi-cash me-2"></i>{{ __('invoices.record_payment') }}
                        </a>
                    </li>
                    {% endif %}
                    {% if invoice.status in ['sent', 'paid', 'partial'] %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/credit-note">
                            <i class="bi bi-file-earmark-minus me-2"></i>{{ __('invoices.create_credit_note') }}
                        </a>
                    </li>
                    {% endif %}
                    {% if invoice.status == 'draft' %}
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="confirmCancel()">
                            <i class="bi bi-x-circle me-2"></i>{{ __('invoices.cancel_invoice') }}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
            <a href="{{ base_url }}/invoices" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Invoice Preview -->
            <div class="card shadow-sm mb-4">
                <div class="card-body p-5">
                    <!-- Invoice Header -->
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            {% if company_logo %}
                                <img src="{{ base_url }}/{{ company_logo }}" alt="{{ company_name }}" style="max-height: 80px;">
                            {% else %}
                                <h2 class="text-primary mb-0">{{ company_name }}</h2>
                            {% endif %}
                            <div class="mt-3">
                                <p class="mb-1">{{ company_address }}</p>
                                <p class="mb-1">{{ company_city }}, {{ company_postal_code }}</p>
                                <p class="mb-1">{{ company_country }}</p>
                            </div>
                        </div>
                        <div class="col-sm-6 text-sm-end">
                            <h3 class="text-uppercase text-muted">{{ __('invoices.invoice') }}</h3>
                            <h4 class="mb-3">{{ invoice.invoice_number }}</h4>
                            
                            <dl class="row mb-0">
                                <dt class="col-6 text-muted">{{ __('invoices.issue_date') }}:</dt>
                                <dd class="col-6">{{ invoice.issue_date|date('d/m/Y') }}</dd>
                                
                                {% if invoice.due_date %}
                                <dt class="col-6 text-muted">{{ __('invoices.due_date') }}:</dt>
                                <dd class="col-6">
                                    {{ invoice.due_date|date('d/m/Y') }}
                                    {% if invoice.is_overdue %}
                                        <i class="bi bi-exclamation-circle text-danger ms-1"></i>
                                    {% endif %}
                                </dd>
                                {% endif %}
                            </dl>
                        </div>
                    </div>

                    <!-- Bill To -->
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <h6 class="text-muted mb-2">{{ __('invoices.from') }}:</h6>
                            <p class="mb-0">
                                <strong>{{ company_name }}</strong><br>
                                {% if company_vat_number %}
                                    {{ __('config.vat_number') }}: {{ company_vat_number }}<br>
                                {% endif %}
                                {% if company_phone %}
                                    {{ __('common.phone') }}: {{ company_phone }}<br>
                                {% endif %}
                                {% if company_email %}
                                    {{ __('common.email') }}: {{ company_email }}
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-sm-6">
                            <h6 class="text-muted mb-2">{{ __('invoices.bill_to') }}:</h6>
                            <div class="mb-0">
                                {% if invoice.patient_id %}
                                    <strong>{{ invoice.patient.first_name }} {{ invoice.patient.last_name }}</strong><br>
                                    {% if invoice.patient.address or invoice.patient.address_line1 %}
                                        {{ invoice.patient.address|default(invoice.patient.address_line1) }}<br>
                                    {% endif %}
                                    {% if invoice.patient.address_line2 %}
                                        {{ invoice.patient.address_line2 }}<br>
                                    {% endif %}
                                    {% if invoice.patient.postal_code or invoice.patient.city %}
                                        {{ invoice.patient.postal_code }} {{ invoice.patient.city }}<br>
                                    {% endif %}
                                    {% if invoice.patient.country %}
                                        {{ invoice.patient.country }}<br>
                                    {% endif %}
                                    {% if invoice.patient.phone %}
                                        {{ __('common.phone') }}: {{ invoice.patient.phone }}<br>
                                    {% endif %}
                                    {% if invoice.patient.mobile %}
                                        {{ __('common.mobile') }}: {{ invoice.patient.mobile }}<br>
                                    {% endif %}
                                    {% if invoice.patient.email %}
                                        {{ __('common.email') }}: {{ invoice.patient.email }}
                                    {% endif %}
                                {% else %}
                                    <strong>
                                        {% if invoice.client.company_name %}
                                            {{ invoice.client.company_name }}
                                        {% else %}
                                            {{ invoice.client.first_name }} {{ invoice.client.last_name }}
                                        {% endif %}
                                    </strong><br>
                                    {% if invoice.client.vat_number %}
                                        {{ __('config.vat_number') }}: {{ invoice.client.vat_number }}<br>
                                    {% endif %}
                                    {% if invoice.client.address or invoice.client.address_line1 %}
                                        {{ invoice.client.address|default(invoice.client.address_line1) }}<br>
                                    {% endif %}
                                    {% if invoice.client.address_line2 %}
                                        {{ invoice.client.address_line2 }}<br>
                                    {% endif %}
                                    {% if invoice.client.postal_code or invoice.client.city %}
                                        {{ invoice.client.postal_code }} {{ invoice.client.city }}<br>
                                    {% endif %}
                                    {% if invoice.client.country %}
                                        {{ invoice.client.country }}<br>
                                    {% endif %}
                                    {% if invoice.client.phone %}
                                        {{ __('common.phone') }}: {{ invoice.client.phone }}<br>
                                    {% endif %}
                                    {% if invoice.client.mobile %}
                                        {{ __('common.mobile') }}: {{ invoice.client.mobile }}<br>
                                    {% endif %}
                                    {% if invoice.client.email %}
                                        {{ __('common.email') }}: {{ invoice.client.email }}
                                    {% endif %}
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive mb-4">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>{{ __('invoices.description') }}</th>
                                    <th class="text-center">{{ __('invoices.quantity') }}</th>
                                    <th class="text-end">{{ __('invoices.unit_price') }}</th>
                                    <th class="text-center">{{ __('invoices.vat_rate') }}</th>
                                    <th class="text-end">{{ __('invoices.total') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.lines %}
                                <tr>
                                    <td>{{ item.description }}</td>
                                    <td class="text-center">{{ item.quantity }}</td>
                                    <td class="text-end">{{ currency }}{{ item.unit_price|number_format(2, ',', ' ') }}</td>
                                    <td class="text-center">{{ item.vat_rate }}%</td>
                                    <td class="text-end">{{ currency }}{{ (item.quantity * item.unit_price * (1 + item.vat_rate / 100))|number_format(2, ',', ' ') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                    <td class="text-end">{{ currency }}{{ invoice.subtotal|number_format(2, ',', ' ') }}</td>
                                </tr>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                    <td class="text-end">{{ currency }}{{ invoice.vat_amount|number_format(2, ',', ' ') }}</td>
                                </tr>
                                {% if invoice.cns_base_amount > 0 %}
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.cns_base_amount') | default('CNS/Patient Amount') }}:</strong></td>
                                    <td class="text-end">{{ currency }}{{ invoice.cns_base_amount|number_format(2, ',', ' ') }}</td>
                                </tr>
                                {% endif %}
                                {% if invoice.secretariat_vat_amount > 0 %}
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.secretary_fee') | default('Secretary Fee') }}:</strong></td>
                                    <td class="text-end">{{ currency }}{{ invoice.secretariat_vat_amount|number_format(2, ',', ' ') }}</td>
                                </tr>
                                {% endif %}
                                {% if invoice.discount_amount > 0 %}
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.discount') }}:</strong></td>
                                    <td class="text-end text-success">-{{ currency }}{{ invoice.discount_amount|number_format(2, ',', ' ') }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <td colspan="4" class="text-end"><h5 class="mb-0">{{ __('invoices.total') }}:</h5></td>
                                    <td class="text-end"><h5 class="mb-0 text-primary">{{ currency }}{{ invoice.total|number_format(2, ',', ' ') }}</h5></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Notes -->
                    {% if invoice.notes %}
                    <div class="mb-4">
                        <h6 class="text-muted">{{ __('invoices.notes') }}:</h6>
                        <p>{{ invoice.notes|nl2br }}</p>
                    </div>
                    {% endif %}

                    <!-- Payment Terms -->
                    {% if invoice.payment_terms %}
                    <div class="mb-4">
                        <h6 class="text-muted">{{ __('invoices.payment_terms') }}:</h6>
                        <p>{{ invoice.payment_terms }}</p>
                    </div>
                    {% endif %}

                    <!-- Footer -->
                    <div class="text-center text-muted mt-5">
                        <p class="mb-0">{{ __('invoices.thank_you') }}</p>
                    </div>
                </div>
            </div>

            <!-- Internal Notes (if any) -->
            {% if invoice.internal_notes %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="bi bi-sticky-fill me-2"></i>{{ __('invoices.internal_notes') }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ invoice.internal_notes|nl2br }}</p>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Payment Status -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-cash-stack me-2"></i>{{ __('invoices.payment_status') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-6">{{ __('invoices.total') }}:</dt>
                        <dd class="col-6 text-end">{{ currency }}{{ invoice.total|number_format(2, ',', ' ') }}</dd>
                        
                        <dt class="col-6">{{ __('invoices.paid_amount') }}:</dt>
                        <dd class="col-6 text-end">{{ currency }}{{ invoice.paid_amount|default(0)|number_format(2, ',', ' ') }}</dd>
                        
                        <dt class="col-6">{{ __('invoices.balance_due') }}:</dt>
                        <dd class="col-6 text-end">
                            {% set balance = invoice.total - invoice.paid_amount|default(0) %}
                            <strong class="{{ balance > 0 ? 'text-danger' : 'text-success' }}">
                                {{ currency }}{{ balance|number_format(2, ',', ' ') }}
                            </strong>
                        </dd>
                    </dl>
                    
                    {% if payments|length > 0 %}
                    <hr>
                    <h6 class="mb-3">{{ __('invoices.payment_history') }}</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ __('common.date') }}</th>
                                    <th>{{ __('invoices.amount') }}</th>
                                    <th>{{ __('invoices.payment_method') }}</th>
                                    <th>{{ __('invoices.reference') }}</th>
                                    <th>{{ __('common.status') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ payment.payment_date|date('d/m/Y') }}</td>
                                    <td class="text-nowrap">
                                        <strong class="text-success">{{ currency }}{{ payment.amount|number_format(2, ',', ' ') }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            {{ payment.payment_method_name|default(payment.payment_method_code|default('N/A')) }}
                                        </span>
                                    </td>
                                    <td>{{ payment.reference|default('-') }}</td>
                                    <td>
                                        {% if loop.last and invoice.total > invoice.paid_amount %}
                                            <span class="badge bg-warning">{{ __('invoices.status.partial') }}</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ __('invoices.payment_complete') }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% if payment.notes %}
                                <tr>
                                    <td colspan="5" class="text-muted small ps-4">
                                        <i class="bi bi-chat-text me-1"></i> {{ payment.notes }}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr class="table-secondary">
                                    <td><strong>{{ __('common.total') }}</strong></td>
                                    <td colspan="4">
                                        <strong>{{ currency }}{{ invoice.paid_amount|default(0)|number_format(2, ',', ' ') }}</strong>
                                        {% if invoice.paid_amount < invoice.total %}
                                            <span class="text-muted">/ {{ currency }}{{ invoice.total|number_format(2, ',', ' ') }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>{{ __('common.quick_actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank" class="btn btn-outline-primary">
                            <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                        </a>
                        <a href="{{ base_url }}/invoices/{{ invoice.id }}/download" class="btn btn-outline-primary">
                            <i class="bi bi-download me-2"></i>{{ __('invoices.download_pdf') }}
                        </a>
                        {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                        <button type="button" class="btn btn-info" onclick="sendInvoice()">
                            <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_invoice') }}
                        </button>
                        {% endif %}
                        <button type="button" class="btn btn-outline-secondary" onclick="duplicateInvoice()">
                            <i class="bi bi-files me-2"></i>{{ __('invoices.duplicate_invoice') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.system_info') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0 small">
                        <dt class="col-6">{{ __('common.created_at') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.created_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-6">{{ __('common.updated_at') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.updated_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-6">{{ __('common.created_by') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.created_by_name|default('System') }}</dd>
                        
                        {% if invoice.sent_at %}
                        <dt class="col-6">{{ __('invoices.sent_at') }}:</dt>
                        <dd class="col-6 text-end">{{ invoice.sent_at|date('d/m/Y H:i') }}</dd>
                        {% endif %}
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payment Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{{ base_url }}/invoices/{{ invoice.id }}/payment" id="paymentForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('invoices.record_payment') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="payment_amount" class="form-label">{{ __('invoices.amount') }} *</label>
                        <div class="input-group">
                            <span class="input-group-text">{{ currency }}</span>
                            {% set balance = invoice.total - invoice.paid_amount|default(0) %}
                            <input type="number" class="form-control" id="payment_amount" name="amount" 
                                   value="{{ balance }}" min="0.01" max="{{ balance }}" 
                                   step="0.01" required>
                        </div>
                        <small class="text-muted">{{ __('invoices.balance_due') }}: {{ currency }}{{ balance|number_format(2, ',', ' ') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_date" class="form-label">{{ __('invoices.payment_date') }} *</label>
                        <input type="date" class="form-control" id="payment_date" name="payment_date" 
                               value="{{ 'now'|date('Y-m-d') }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_method" class="form-label">{{ __('invoices.payment_method') }} *</label>
                        <select class="form-select" id="payment_method" name="payment_method" required>
                            <option value="">{{ __('common.select') }}</option>
                            {% for method in paymentMethods %}
                                <option value="{{ method.code }}">{{ method.display_name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_reference" class="form-label">{{ __('invoices.payment_reference') }}</label>
                        <input type="text" class="form-control" id="payment_reference" name="reference">
                    </div>
                    
                    <div class="mb-3">
                        <label for="payment_notes" class="form-label">{{ __('common.notes') }}</label>
                        <textarea class="form-control" id="payment_notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('invoices.record_payment') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Payment form with debug AJAX submission
document.addEventListener('DOMContentLoaded', function() {
    // Check if we should open the payment modal
    if (window.location.hash === '#payment') {
        const paymentModal = document.getElementById('paymentModal');
        if (paymentModal) {
            const modal = new bootstrap.Modal(paymentModal);
            modal.show();
            // Remove the hash to prevent reopening on refresh
            history.replaceState(null, null, window.location.pathname);
        }
    }
    
    const paymentForm = document.getElementById('paymentForm');
    if (paymentForm) {
        console.log('Payment form initialized');
        
        paymentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('Form submission intercepted for debugging');
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Show form data in console
            console.log('=== FORM DATA ===');
            for (let [key, value] of formData.entries()) {
                console.log(key + ':', value);
            }
            console.log('=================');
            
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
            
            // Submit with AJAX to get debug info
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                
                // First get the text to see what's actually returned
                return response.text().then(text => {
                    console.log('Raw response:', text);
                    
                    // Try to parse as JSON
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Failed to parse JSON:', e);
                        console.error('Response was:', text);
                        throw new Error('Invalid JSON response: ' + text.substring(0, 200));
                    }
                });
            })
            .then(data => {
                console.log('=== SERVER RESPONSE ===');
                console.log(data);
                console.log('====================');
                
                if (data.debug) {
                    console.log('=== DEBUG INFO ===');
                    console.log('Invoice ID:', data.debug.invoice_id);
                    console.log('Current Total:', data.debug.invoice_current_total);
                    console.log('Current Paid:', data.debug.invoice_current_paid);
                    console.log('Payment Amount:', data.debug.payment_data?.amount);
                    console.log('Payment ID:', data.debug.payment_id);
                    console.log('Updated Totals:', data.debug.updated_totals);
                    console.log('Status Change:', data.debug.old_status, '->', data.debug.new_status);
                    console.log('Full Debug:', data.debug);
                    console.log('==================');
                }
                
                if (data.success) {
                    console.log('Payment recorded successfully, redirecting...');
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        window.location.reload();
                    }
                } else {
                    console.error('Payment failed:', data.error);
                    alert('Error: ' + data.error);
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('=== FETCH ERROR ===');
                console.error(error);
                console.error('===================');
                alert('Error submitting payment: ' + error.message);
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            });
        });
    }
});

function sendInvoice() {
    if (confirm('{{ __("invoices.send_confirm") }}')) {
        // Create a form and submit it as POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/invoices/{{ invoice.id }}/send';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '{{ csrf_token }}';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function duplicateInvoice() {
    window.location.href = '{{ base_url }}/invoices/create?duplicate={{ invoice.id }}';
}

function confirmCancel() {
    if (confirm('{{ __("invoices.cancel_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/invoices/{{ invoice.id }}/cancel';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function markAsSent() {
    Swal.fire({
        title: '{{ __("invoices.mark_as_sent_confirm_title")|default("Marquer comme envoyée ?") }}',
        text: '{{ __("invoices.mark_as_sent_confirm_text")|default("Cette facture sera marquée comme envoyée et ne pourra plus être modifiée.") }}',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("invoices.yes_mark_sent")|default("Oui, marquer comme envoyée") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/{{ invoice.id }}/send';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
{% endblock %}