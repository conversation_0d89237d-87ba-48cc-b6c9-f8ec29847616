{% extends 'base-modern.twig' %}

{% block title %}{{ __('permissions.management') }} - {{ parent() }}{% endblock %}

{% block content %}
<div class="container-fluid permissions-container">
    <!-- Mobile Header -->
    <div class="d-lg-none mobile-header mb-3">
        <div class="d-flex justify-content-between align-items-center">
            <h4 class="mb-0">
                <i class="fas fa-shield-alt"></i>
                {{ __('permissions.management') }}
            </h4>
            <button class="btn btn-sm btn-primary" data-bs-toggle="offcanvas" data-bs-target="#groupsOffcanvas">
                <i class="fas fa-users"></i>
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Desktop Groups Panel -->
        <div class="col-lg-3 d-none d-lg-block">
            <div class="card card-outline card-primary">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('permissions.user_groups') }}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush group-list-container" id="groupList">
                        {% for group in groups %}
                        <a href="#" class="list-group-item list-group-item-action {% if loop.first %}active{% endif %}" 
                           data-group-id="{{ group.id }}" 
                           onclick="window.loadGroupPermissions({{ group.id }}); return false;">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>
                                    <i class="fas fa-users me-2"></i>
                                    {{ group.name }}
                                </span>
                                <span class="badge bg-primary">{{ group.user_count|default(0) }}</span>
                            </div>
                            {% if group.description %}
                            <small class="text-muted">{{ group.description }}</small>
                            {% endif %}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                <div class="card-footer">
                    <button class="btn btn-sm btn-primary w-100" onclick="window.createNewGroup()">
                        <i class="fas fa-plus"></i> {{ __('permissions.add_group') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Permissions Matrix -->
        <div class="col-lg-9">
            <form id="permissionsForm" method="POST" action="{{ url('/permissions/update') }}">
                {{ csrf_field() }}
                <input type="hidden" name="group_id" id="currentGroupId" value="{{ groups[0].id ?? '' }}">
                
                <div class="card card-outline card-success">
                    <div class="card-header">
                        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                            <h5 class="mb-2 mb-md-0">
                                {{ __('permissions.permissions_for') }} 
                                <span id="currentGroupName" class="fw-bold">{{ groups[0].name ?? '' }}</span>
                            </h5>
                            <div class="input-group input-group-sm" style="max-width: 250px;">
                                <input type="text" class="form-control" placeholder="{{ __('permissions.search_modules') }}" 
                                       id="moduleSearch" onkeyup="window.filterModules()">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0 p-md-3">
                        <!-- Mobile View -->
                        <div class="d-lg-none permissions-mobile-mode">
                            <div class="accordion" id="permissionsAccordion">
                                <!-- Permissions modules will be rendered here dynamically -->
                            </div>
                        </div>

                        <!-- Desktop View -->
                        <div class="d-none d-lg-block">
                            <div class="table-responsive permissions-table-container">
                                <table class="table table-hover table-striped" id="permissionsTable">
                                    <thead class="sticky-top bg-white">
                                        <tr>
                                            <th style="width: 30%">{{ __('permissions.module') }}</th>
                                            <th class="text-center" style="width: 14%">
                                                <div class="group-switch">
                                                    <label for="toggle-all-view">
                                                        <span class="permission-switch switch-sm">
                                                            <input type="checkbox" id="toggle-all-view" class="permission-toggle" data-permission="view">
                                                            <span class="slider"></span>
                                                        </span>
                                                        <i class="fas fa-eye d-lg-none" title="{{ __('permissions.view') }}"></i>
                                                        <span class="d-none d-lg-inline">{{ __('permissions.view') }}</span>
                                                    </label>
                                                </div>
                                            </th>
                                            <th class="text-center" style="width: 14%">
                                                <div class="group-switch">
                                                    <label for="toggle-all-create">
                                                        <span class="permission-switch switch-sm">
                                                            <input type="checkbox" id="toggle-all-create" class="permission-toggle" data-permission="create">
                                                            <span class="slider"></span>
                                                        </span>
                                                        <i class="fas fa-plus d-lg-none" title="{{ __('permissions.create') }}"></i>
                                                        <span class="d-none d-lg-inline">{{ __('permissions.create') }}</span>
                                                    </label>
                                                </div>
                                            </th>
                                            <th class="text-center" style="width: 14%">
                                                <div class="group-switch">
                                                    <label for="toggle-all-edit">
                                                        <span class="permission-switch switch-sm">
                                                            <input type="checkbox" id="toggle-all-edit" class="permission-toggle" data-permission="edit">
                                                            <span class="slider"></span>
                                                        </span>
                                                        <i class="fas fa-edit d-lg-none" title="{{ __('permissions.edit') }}"></i>
                                                        <span class="d-none d-lg-inline">{{ __('permissions.edit') }}</span>
                                                    </label>
                                                </div>
                                            </th>
                                            <th class="text-center" style="width: 14%">
                                                <div class="group-switch">
                                                    <label for="toggle-all-delete">
                                                        <span class="permission-switch switch-sm">
                                                            <input type="checkbox" id="toggle-all-delete" class="permission-toggle" data-permission="delete">
                                                            <span class="slider"></span>
                                                        </span>
                                                        <i class="fas fa-trash d-lg-none" title="{{ __('permissions.delete') }}"></i>
                                                        <span class="d-none d-lg-inline">{{ __('permissions.delete') }}</span>
                                                    </label>
                                                </div>
                                            </th>
                                            <th class="text-center" style="width: 14%">{{ __('permissions.special') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody id="permissionsBody">
                                        <!-- Permissions will be loaded dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row g-2">
                            <div class="col-12 col-md-6">
                                <div class="btn-group w-100" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="window.permissionsManager && window.permissionsManager.copyFromGroup()">
                                        <i class="fas fa-copy"></i>
                                        <span class="d-none d-sm-inline">{{ __('permissions.copy_from') }}</span>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="window.resetToDefault()">
                                        <i class="fas fa-undo"></i>
                                        <span class="d-none d-sm-inline">{{ __('permissions.reset_default') }}</span>
                                    </button>
                                </div>
                            </div>
                            <div class="col-12 col-md-6 text-end">
                                <button type="submit" class="btn btn-success w-100 w-md-auto">
                                    <i class="fas fa-save"></i> {{ __('permissions.save_changes') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Mobile Groups Offcanvas -->
<div class="offcanvas offcanvas-start" tabindex="-1" id="groupsOffcanvas">
    <div class="offcanvas-header">
        <h5 class="offcanvas-title">{{ __('permissions.user_groups') }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
    </div>
    <div class="offcanvas-body p-0">
        <div class="list-group list-group-flush" id="mobileGroupList">
            {% for group in groups %}
            <a href="#" class="list-group-item list-group-item-action {% if loop.first %}active{% endif %}" 
               data-group-id="{{ group.id }}" 
               onclick="window.loadGroupPermissions({{ group.id }}); bootstrap.Offcanvas.getInstance(document.getElementById('groupsOffcanvas')).hide(); return false;">
                <div class="d-flex justify-content-between align-items-center">
                    <span>
                        <i class="fas fa-users me-2"></i>
                        {{ group.name }}
                    </span>
                    <span class="badge bg-primary">{{ group.user_count|default(0) }}</span>
                </div>
                {% if group.description %}
                <small class="text-muted">{{ group.description }}</small>
                {% endif %}
            </a>
            {% endfor %}
        </div>
        <div class="p-3">
            <button class="btn btn-primary w-100" onclick="window.createNewGroup()">
                <i class="fas fa-plus"></i> {{ __('permissions.add_group') }}
            </button>
        </div>
    </div>
</div>

<!-- Floating Action Button (Mobile) -->
<div class="fab-container d-lg-none">
    <button type="submit" form="permissionsForm" class="btn btn-success fab-button">
        <i class="fas fa-save"></i>
    </button>
</div>

<!-- Template Modal -->
<div class="modal fade" id="templateModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('permissions.load_template') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('admin')">
                        <h6 class="mb-1">{{ __('permissions.template_admin') }}</h6>
                        <p class="mb-1 small">{{ __('permissions.template_admin_desc') }}</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('manager')">
                        <h6 class="mb-1">{{ __('permissions.template_manager') }}</h6>
                        <p class="mb-1 small">{{ __('permissions.template_manager_desc') }}</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('staff')">
                        <h6 class="mb-1">{{ __('permissions.template_staff') }}</h6>
                        <p class="mb-1 small">{{ __('permissions.template_staff_desc') }}</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('readonly')">
                        <h6 class="mb-1">{{ __('permissions.template_readonly') }}</h6>
                        <p class="mb-1 small">{{ __('permissions.template_readonly_desc') }}</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<!-- Include Permissions Manager Script -->
<script src="{{ base_url }}/js/permissions-manager.js?v={{ 'now'|date('YmdHis') }}&fix=6"></script>
<!-- Include Permission Switches Script -->
<script src="{{ base_url }}/js/permission-switches.js?v={{ 'now'|date('YmdHis') }}"></script>

<script>
// Mobile-optimized permission modules configuration
const permissionModules = [
    {
        code: 'invoices',
        name: '{{ __("modules.invoices") }}',
        icon: 'fa-file-invoice',
        description: '{{ __("modules.invoices_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['bulk_generate', 'send_email', 'export', 'void'],
        submodules: {
            'invoices.retrocession': {
                code: 'invoices.retrocession',
                name: '{{ __("modules.retrocession") }}',
                icon: 'fa-hand-holding-usd',
                permissions: ['view', 'create', 'edit', 'delete']
            },
            'invoices.recurring': {
                code: 'invoices.recurring',
                name: '{{ __("modules.recurring_invoices") }}',
                icon: 'fa-sync',
                permissions: ['view', 'create', 'edit', 'delete'],
                special: ['manage_schedules']
            }
        }
    },
    {
        code: 'clients',
        name: '{{ __("modules.clients") }}',
        icon: 'fa-users',
        description: '{{ __("modules.clients_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['export', 'import', 'merge']
    },
    {
        code: 'products',
        name: '{{ __("modules.products") }}',
        icon: 'fa-box',
        description: '{{ __("modules.products_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['manage_stock', 'import', 'bulk_update']
    },
    {
        code: 'reports',
        name: '{{ __("modules.reports") }}',
        icon: 'fa-chart-bar',
        description: '{{ __("modules.reports_desc") }}',
        permissions: ['view'],
        special: ['view_financial', 'view_inventory', 'view_analytics', 'export', 'schedule']
    },
    {
        code: 'config',
        name: '{{ __("modules.configuration") }}',
        icon: 'fa-cog',
        description: '{{ __("modules.config_desc") }}',
        permissions: ['view', 'edit'],
        special: ['manage_company', 'manage_system', 'manage_templates', 'manage_integrations']
    },
    {
        code: 'users',
        name: '{{ __("modules.users") }}',
        icon: 'fa-user-cog',
        description: '{{ __("modules.users_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['manage_groups', 'manage_permissions', 'reset_passwords', 'view_activity']
    }
];

// Initialize permissions manager with mobile optimizations
const permissionsManagerOptions = {
    modules: permissionModules,
    apiBaseUrl: '{{ url("/admin/permissions") }}',
    csrfToken: '{{ csrf_token }}',
    autoSave: false,
    mobileOptimized: true,
    translations: {
        saving: '{{ __("permissions.saving") }}',
        saved: '{{ __("permissions.saved_successfully") }}',
        error: '{{ __("permissions.save_failed") }}',
        confirmReset: '{{ __("permissions.confirm_reset") }}',
        confirmCopy: '{{ __("permissions.confirm_copy") }}',
        loading: '{{ __("common.loading") }}',
        noResults: '{{ __("common.no_results") }}',
        searchPlaceholder: '{{ __("permissions.search_modules") }}',
        selectAll: '{{ __("permissions.select_all") }}',
        deselectAll: '{{ __("permissions.deselect_all") }}'
    }
};

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap 5 components
    const offcanvasElements = document.querySelectorAll('.offcanvas');
    offcanvasElements.forEach(el => new bootstrap.Offcanvas(el));
    
    // Initialize permissions manager
    if (typeof PermissionsManager !== 'undefined' && typeof permissionsManagerOptions !== 'undefined') {
        window.permissionsManager = new PermissionsManager(permissionsManagerOptions);
        
        // Load initial group
        const firstGroup = document.querySelector('#groupList .list-group-item');
        if (firstGroup) {
            const groupId = firstGroup.dataset.groupId;
            window.loadGroupPermissions(groupId);
        }
    }
    
    // Mobile-specific enhancements
    handleMobileOptimizations();
});

// Enhanced mobile view handling
function handleMobileOptimizations() {
    const isMobile = window.innerWidth < 992;
    
    if (isMobile) {
        // Add mobile class to body
        document.body.classList.add('mobile-view');
        
        // Enhance touch targets
        document.querySelectorAll('.form-check-input').forEach(input => {
            input.classList.add('permission-checkbox');
        });
        
        // Add swipe support for offcanvas
        enableSwipeGestures();
    } else {
        document.body.classList.remove('mobile-view');
    }
}

// Enable swipe gestures for mobile
function enableSwipeGestures() {
    let touchStartX = 0;
    let touchEndX = 0;
    
    document.addEventListener('touchstart', e => {
        touchStartX = e.changedTouches[0].screenX;
    }, { passive: true });
    
    document.addEventListener('touchend', e => {
        touchEndX = e.changedTouches[0].screenX;
        handleSwipeGesture();
    }, { passive: true });
    
    function handleSwipeGesture() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;
        
        // Swipe left to open groups
        if (Math.abs(diff) > swipeThreshold && diff < 0 && touchStartX < 50) {
            const offcanvas = bootstrap.Offcanvas.getInstance(document.getElementById('groupsOffcanvas'));
            if (offcanvas) offcanvas.show();
        }
    }
}

// Global functions for permissions management
window.loadGroupPermissions = function(groupId) {
    if (!window.permissionsManager) return false;
    
    // Update active group in all lists
    document.querySelectorAll('.list-group-item[data-group-id]').forEach(item => {
        item.classList.toggle('active', item.dataset.groupId === groupId.toString());
    });
    
    // Update current group display
    const currentItem = document.querySelector(`[data-group-id="${groupId}"]`);
    if (currentItem) {
        const groupName = currentItem.querySelector('span').textContent.trim();
        document.getElementById('currentGroupName').textContent = groupName;
        document.getElementById('currentGroupId').value = groupId;
    }
    
    // Load permissions
    window.permissionsManager.loadGroupPermissions(groupId);
    
    return false;
};

window.resetToDefault = function() {
    if (!window.permissionsManager) return;
    
    if (confirm('{{ __("permissions.confirm_reset") }}')) {
        window.permissionsManager.resetToDefault();
    }
};

window.createNewGroup = function() {
    if (window.permissionsManager) {
        window.permissionsManager.createNewGroup();
    }
};

window.filterModules = function() {
    if (window.permissionsManager) {
        window.permissionsManager.filterModules();
    }
};

// Responsive handling
window.addEventListener('resize', handleMobileOptimizations);

// Add visual feedback for touch interactions
if ('ontouchstart' in window) {
    document.body.classList.add('touch-device');
}

// Form dirty state indicator
document.getElementById('permissionsForm')?.addEventListener('change', () => {
    document.querySelector('.fab-button')?.classList.add('pulse');
});

// Remove pulse after save
document.getElementById('permissionsForm')?.addEventListener('submit', () => {
    setTimeout(() => {
        document.querySelector('.fab-button')?.classList.remove('pulse');
    }, 2000);
});
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<link rel="stylesheet" href="{{ base_url }}/css/permission-switches.css?v={{ 'now'|date('YmdHis') }}">
<style>
/* Mobile-specific permission styles */
.permissions-container {
    min-height: calc(100vh - 200px);
}

/* Mobile header */
.mobile-header {
    padding: 1rem;
    background: var(--bs-gray-100);
    border-radius: 0.5rem;
}

/* Touch-friendly checkboxes */
.touch-device .permission-checkbox {
    width: 1.5rem;
    height: 1.5rem;
    cursor: pointer;
}

/* Mobile accordion styles */
.permissions-mobile-mode .accordion-item {
    margin-bottom: 0.5rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.5rem !important;
}

.permissions-mobile-mode .accordion-button {
    padding: 1rem;
    font-weight: 500;
}

.permissions-mobile-mode .accordion-body {
    padding: 1rem;
}

/* Permission grid for mobile */
.permission-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.permission-item {
    padding: 0.75rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.375rem;
    text-align: center;
}

.permission-item label {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    margin: 0;
}

.permission-item i {
    font-size: 1.25rem;
    color: var(--bs-primary);
}

/* Special permissions */
.special-permissions {
    background: var(--bs-gray-100);
    padding: 1rem;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

/* Floating action button */
.fab-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1030;
}

.fab-button {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.fab-button.pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(25, 135, 84, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(25, 135, 84, 0);
    }
}

/* Responsive tables */
@media (max-width: 991px) {
    .permissions-table-container {
        max-height: calc(100vh - 300px);
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .permissions-table-container table {
        font-size: 0.875rem;
    }
    
    .permissions-table-container th,
    .permissions-table-container td {
        padding: 0.5rem;
    }
}

/* Offcanvas enhancements */
.offcanvas {
    width: 300px !important;
}

.offcanvas-backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Loading states */
.permissions-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    color: var(--bs-gray-600);
}

.permissions-loading i {
    font-size: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Improve form elements on mobile */
@media (max-width: 767px) {
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.25rem;
    }
    
    .card-footer .row {
        gap: 0.5rem;
    }
    
    .input-group-sm {
        width: 100% !important;
        max-width: none !important;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .permissions-container {
        color-scheme: dark;
    }
    
    .mobile-header {
        background: var(--bs-dark);
        color: var(--bs-light);
    }
}
</style>
{% endblock %}