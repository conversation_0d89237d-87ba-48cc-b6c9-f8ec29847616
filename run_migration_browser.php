<?php
/**
 * Browser-based migration runner for document_type_column_configs table
 * Access this file via: http://localhost/fit/run_migration_browser.php
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Migration Runner</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .info {
            color: #004085;
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .warning {
            color: #856404;
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Database Migration Runner</h1>
        
<?php

try {
    // Load composer autoloader
    require_once __DIR__ . '/vendor/autoload.php';
    
    echo '<div class="info">Loading environment configuration...</div>';
    
    // Load .env file
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    echo '<div class="info">Connecting to database...</div>';
    
    // Get database credentials from .env
    $dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbPort = $_ENV['DB_PORT'] ?? '3306';
    $dbName = $_ENV['DB_DATABASE'] ?? 'healthcenter_billing';
    $dbUser = $_ENV['DB_USERNAME'] ?? 'root';
    $dbPass = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $dsn = "mysql:host={$dbHost};port={$dbPort};dbname={$dbName};charset=utf8mb4";
    $db = new PDO($dsn, $dbUser, $dbPass, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);
    
    // Check if table exists
    echo '<div class="info">Checking if document_type_column_configs table exists...</div>';
    
    $stmt = $db->query("SHOW TABLES LIKE 'document_type_column_configs'");
    $tableExists = $stmt->rowCount() > 0;
    
    if ($tableExists) {
        echo '<div class="warning">Table \'document_type_column_configs\' already exists.</div>';
        
        // Check if table_name column exists
        $stmt = $db->query("SHOW COLUMNS FROM document_type_column_configs LIKE 'table_name'");
        $columnExists = $stmt->rowCount() > 0;
        
        if (!$columnExists) {
            echo '<div class="info">Adding \'table_name\' column to existing table...</div>';
            
            try {
                $db->exec("ALTER TABLE document_type_column_configs ADD COLUMN `table_name` varchar(100) NOT NULL AFTER `invoice_type_id`");
                echo '<div class="success">✅ Column added successfully!</div>';
                
                // Add index on table_name
                $db->exec("ALTER TABLE document_type_column_configs ADD INDEX idx_table_name (`table_name`)");
                echo '<div class="success">✅ Index added on table_name column!</div>';
                
            } catch (Exception $e) {
                echo '<div class="error">❌ Error adding column: ' . htmlspecialchars($e->getMessage()) . '</div>';
            }
        } else {
            echo '<div class="success">✅ Column \'table_name\' already exists.</div>';
        }
        
        // Show table structure
        echo '<div class="info">Current table structure:</div>';
        echo '<pre>';
        $stmt = $db->query("DESCRIBE document_type_column_configs");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Field | Type | Null | Key | Default | Extra\n";
        echo str_repeat("-", 80) . "\n";
        foreach ($columns as $col) {
            echo sprintf("%-20s | %-20s | %-5s | %-5s | %-10s | %s\n", 
                $col['Field'], 
                $col['Type'], 
                $col['Null'], 
                $col['Key'], 
                $col['Default'] ?? 'NULL', 
                $col['Extra']
            );
        }
        echo '</pre>';
        
    } else {
        echo '<div class="info">Creating table \'document_type_column_configs\'...</div>';
        
        $sql = "CREATE TABLE IF NOT EXISTS `document_type_column_configs` (
          `id` int NOT NULL AUTO_INCREMENT,
          `document_type_id` int NOT NULL,
          `invoice_type_id` int DEFAULT NULL,
          `table_name` varchar(100) NOT NULL,
          `column_configs` json NOT NULL,
          `created_by` int DEFAULT NULL,
          `updated_by` int DEFAULT NULL,
          `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          UNIQUE KEY `unique_doc_type_table_invoice` (`document_type_id`,`table_name`,`invoice_type_id`),
          KEY `idx_document_type_id` (`document_type_id`),
          KEY `idx_invoice_type_id` (`invoice_type_id`),
          KEY `idx_table_name` (`table_name`),
          KEY `fk_dtcc_created_by` (`created_by`),
          KEY `fk_dtcc_updated_by` (`updated_by`),
          CONSTRAINT `fk_dtcc_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
          CONSTRAINT `fk_dtcc_document_type` FOREIGN KEY (`document_type_id`) REFERENCES `document_types` (`id`) ON DELETE CASCADE,
          CONSTRAINT `fk_dtcc_invoice_type` FOREIGN KEY (`invoice_type_id`) REFERENCES `config_invoice_types` (`id`) ON DELETE CASCADE,
          CONSTRAINT `fk_dtcc_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        try {
            $db->exec($sql);
            echo '<div class="success">✅ Table created successfully!</div>';
            
            // Show the created table structure
            echo '<div class="info">Created table structure:</div>';
            echo '<pre>';
            $stmt = $db->query("DESCRIBE document_type_column_configs");
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo "Field | Type | Null | Key | Default | Extra\n";
            echo str_repeat("-", 80) . "\n";
            foreach ($columns as $col) {
                echo sprintf("%-20s | %-20s | %-5s | %-5s | %-10s | %s\n", 
                    $col['Field'], 
                    $col['Type'], 
                    $col['Null'], 
                    $col['Key'], 
                    $col['Default'] ?? 'NULL', 
                    $col['Extra']
                );
            }
            echo '</pre>';
            
        } catch (Exception $e) {
            echo '<div class="error">❌ Error creating table: ' . htmlspecialchars($e->getMessage()) . '</div>';
            echo '<pre>' . htmlspecialchars($sql) . '</pre>';
        }
    }
    
    echo '<div class="success">🎉 Migration completed!</div>';
    echo '<p>You can now try creating the invoice for Isabelle Lamy again.</p>';
    echo '<a href="/fit/public/invoices/create?type=location" class="button">Go to Invoice Creation</a>';
    
} catch (Exception $e) {
    echo '<div class="error">';
    echo '<strong>❌ Error:</strong> ' . htmlspecialchars($e->getMessage());
    echo '</div>';
    echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
}

// Files already cleaned up

?>
    </div>
</body>
</html>