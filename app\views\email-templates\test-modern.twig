{% extends "base-modern.twig" %}

{% block title %}{{ __('config.test_email_template') }}{% endblock %}

{% block head %}
<style>
    .preview-container {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        background: white;
        min-height: 500px;
    }
    .email-header {
        background: #f8f9fa;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    .email-body {
        padding: 1.5rem;
    }
    .variable-display {
        background: #e9ecef;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-family: monospace;
        font-size: 0.875rem;
        margin: 0.125rem;
        display: inline-block;
    }
    .variable-value {
        color: #0d6efd;
        font-weight: 500;
    }
    .device-preview {
        margin: 0 auto;
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        border-radius: 0.5rem;
        overflow: hidden;
    }
    .device-preview.desktop {
        max-width: 1200px;
    }
    .device-preview.tablet {
        max-width: 768px;
    }
    .device-preview.mobile {
        max-width: 375px;
    }
    .preview-toolbar {
        background: #f8f9fa;
        padding: 0.5rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        justify-content: center;
        gap: 0.5rem;
    }
    .test-result {
        padding: 1rem;
        border-radius: 0.375rem;
        margin-top: 1rem;
    }
    .test-result.success {
        background: #d1e7dd;
        color: #0f5132;
        border: 1px solid #badbcc;
    }
    .test-result.error {
        background: #f8d7da;
        color: #842029;
        border: 1px solid #f5c2c7;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-send me-2"></i>{{ __('config.test_email_template') }}
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <a href="{{ base_url }}/email-templates" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <a href="{{ base_url }}/email-templates/{{ template.id }}/edit" class="btn btn-outline-primary">
                <i class="bi bi-pencil me-2"></i>{{ __('common.edit_template') }}
            </a>
        </div>
    </div>

    <!-- Template Info -->
    <div class="alert alert-info">
        <div class="d-flex align-items-center">
            <i class="bi bi-info-circle fs-4 me-3"></i>
            <div>
                <h6 class="mb-1">{{ __('config.testing_template') }}: <strong>{{ template.name }}</strong></h6>
                <small>{{ __('config.template_type') }}: {{ __('email.type_' ~ template.email_type) }} | 
                       {{ __('config.priority') }}: 
                       {% if template.priority == 1 %}⚠️ {{ __('priority.critical') }}
                       {% elseif template.priority == 2 %}🔴 {{ __('priority.high') }}
                       {% elseif template.priority == 3 %}🟡 {{ __('priority.medium') }}
                       {% elseif template.priority == 4 %}🟢 {{ __('priority.low') }}
                       {% else %}⚪ {{ __('priority.default') }}{% endif %}
                </small>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Test Configuration -->
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-gear me-2"></i>{{ __('config.test_configuration') }}</h5>
                </div>
                <div class="card-body">
                    <form id="testForm" onsubmit="sendTestEmail(event)">
                        <!-- Invoice Selection -->
                        <div class="mb-3">
                            <label for="invoice_id" class="form-label">
                                {{ __('config.select_invoice') }} *
                                <i class="bi bi-question-circle text-muted" data-bs-toggle="tooltip" 
                                   title="{{ __('config.invoice_test_data_hint') }}"></i>
                            </label>
                            <select class="form-select" id="invoice_id" name="invoice_id" required onchange="loadInvoiceData()">
                                <option value="">{{ __('common.select') }}...</option>
                                {% for invoice in recent_invoices %}
                                <option value="{{ invoice.id }}" data-client="{{ invoice.client_name }}" 
                                        data-amount="{{ invoice.total_amount }}" data-type="{{ invoice.type }}">
                                    #{{ invoice.invoice_number }} - {{ invoice.client_name }} ({{ invoice.total_amount|number_format(2) }} €)
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Test Email Address -->
                        <div class="mb-3">
                            <label for="test_email" class="form-label">
                                {{ __('config.test_email_address') }} *
                            </label>
                            <input type="email" class="form-control" id="test_email" name="test_email" 
                                   value="{{ current_user.email }}" required
                                   placeholder="<EMAIL>">
                            <small class="text-muted">{{ __('config.test_email_hint') }}</small>
                        </div>

                        <!-- Additional Options -->
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="include_attachments" name="include_attachments" checked>
                                <label class="form-check-label" for="include_attachments">
                                    {{ __('config.include_invoice_pdf') }}
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="use_actual_data" name="use_actual_data" checked>
                                <label class="form-check-label" for="use_actual_data">
                                    {{ __('config.use_real_invoice_data') }}
                                </label>
                            </div>
                        </div>

                        <!-- Language Override -->
                        <div class="mb-3">
                            <label for="language" class="form-label">{{ __('config.language_override') }}</label>
                            <select class="form-select" id="language" name="language">
                                <option value="">{{ __('config.use_client_language') }}</option>
                                <option value="fr">Français</option>
                                <option value="en">English</option>
                                <option value="de">Deutsch</option>
                            </select>
                        </div>

                        <!-- Send Test Button -->
                        <button type="submit" class="btn btn-primary w-100" id="sendButton">
                            <i class="bi bi-send me-2"></i>{{ __('config.send_test_email') }}
                        </button>
                    </form>

                    <!-- Test Results -->
                    <div id="testResults" style="display: none;">
                        <!-- Results will be inserted here -->
                    </div>
                </div>
            </div>

            <!-- Variable Substitution Preview -->
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-code me-2"></i>{{ __('config.variable_substitution') }}</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted small mb-3">{{ __('config.variables_resolved_hint') }}</p>
                    <div id="variablesList">
                        <div class="text-center py-3 text-muted">
                            <i class="bi bi-arrow-up"></i> {{ __('config.select_invoice_first') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Preview -->
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-eye me-2"></i>{{ __('config.email_preview') }}</h5>
                        <div class="btn-group btn-group-sm" role="group">
                            <button type="button" class="btn btn-outline-secondary active" onclick="setPreviewDevice('desktop')">
                                <i class="bi bi-laptop"></i> Desktop
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPreviewDevice('tablet')">
                                <i class="bi bi-tablet"></i> Tablet
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="setPreviewDevice('mobile')">
                                <i class="bi bi-phone"></i> Mobile
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body bg-light">
                    <div class="device-preview desktop" id="devicePreview">
                        <div class="preview-toolbar">
                            <span class="badge bg-secondary">{{ __('config.preview_mode') }}</span>
                        </div>
                        <div class="preview-container">
                            <div class="email-header">
                                <div class="row">
                                    <div class="col">
                                        <strong>{{ __('config.from') }}:</strong> {{ company.email|default('<EMAIL>') }}<br>
                                        <strong>{{ __('config.to') }}:</strong> <span id="previewTo">{{ __('config.recipient_email') }}</span><br>
                                        <strong>{{ __('config.subject') }}:</strong> <span id="previewSubject">{{ template.subject }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="email-body" id="previewBody">
                                {{ template.body_html|default(template.body)|raw }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer bg-white">
                    <div class="row g-2">
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="viewHtmlSource()">
                                <i class="bi bi-code-slash me-2"></i>{{ __('config.view_html_source') }}
                            </button>
                        </div>
                        <div class="col-md-6">
                            <button type="button" class="btn btn-outline-secondary w-100" onclick="viewTextVersion()">
                                <i class="bi bi-file-text me-2"></i>{{ __('config.view_text_version') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email History -->
            <div class="card shadow-sm mt-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>{{ __('config.recent_test_emails') }}</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-sm mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>{{ __('common.date') }}</th>
                                    <th>{{ __('config.recipient') }}</th>
                                    <th>{{ __('config.invoice') }}</th>
                                    <th>{{ __('common.status') }}</th>
                                    <th>{{ __('common.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody id="emailHistory">
                                {% for log in test_history %}
                                <tr>
                                    <td>{{ log.created_at|date('d/m/Y H:i') }}</td>
                                    <td>{{ log.recipient_email }}</td>
                                    <td>#{{ log.invoice_number }}</td>
                                    <td>
                                        {% if log.status == 'sent' %}
                                            <span class="badge bg-success">{{ __('config.sent') }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ __('config.failed') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-info" 
                                                onclick="viewEmailLog({{ log.id }})">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-3">
                                        {{ __('config.no_test_emails_sent') }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Source View Modal -->
<div class="modal fade" id="sourceModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sourceModalTitle">{{ __('config.email_source') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <pre id="sourceCode" class="bg-dark text-white p-3 rounded" style="max-height: 500px; overflow-y: auto;"></pre>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.close') }}</button>
                <button type="button" class="btn btn-primary" onclick="copySource()">
                    <i class="bi bi-clipboard me-2"></i>{{ __('common.copy') }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let currentVariables = {};
let currentPreviewHtml = '';
let currentPreviewText = '';

// Load invoice data and update preview
function loadInvoiceData() {
    const select = document.getElementById('invoice_id');
    const option = select.options[select.selectedIndex];
    
    if (!option.value) {
        document.getElementById('variablesList').innerHTML = `
            <div class="text-center py-3 text-muted">
                <i class="bi bi-arrow-up"></i> {{ __('config.select_invoice_first') }}
            </div>
        `;
        return;
    }
    
    // Show loading
    document.getElementById('variablesList').innerHTML = `
        <div class="text-center py-3">
            <div class="spinner-border spinner-border-sm text-primary" role="status">
                <span class="visually-hidden">{{ __('common.loading') }}...</span>
            </div>
        </div>
    `;
    
    // Fetch invoice data and preview
    fetch(`{{ base_url }}/api/email-templates/preview`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            template_id: {{ template.id }},
            invoice_id: option.value
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            throw new Error(data.error);
        }
        
        // Update variables display
        currentVariables = data.variables;
        updateVariablesDisplay(data.variables);
        
        // Update preview
        document.getElementById('previewTo').textContent = data.recipient_email;
        document.getElementById('previewSubject').textContent = data.parsed_subject;
        document.getElementById('previewBody').innerHTML = data.parsed_html;
        
        // Store for source view
        currentPreviewHtml = data.parsed_html;
        currentPreviewText = data.parsed_text;
    })
    .catch(error => {
        console.error('Error loading preview:', error);
        document.getElementById('variablesList').innerHTML = `
            <div class="alert alert-danger mb-0">
                <i class="bi bi-exclamation-triangle me-2"></i>{{ __('common.error_loading_data') }}
            </div>
        `;
    });
}

// Update variables display
function updateVariablesDisplay(variables) {
    let html = '<div class="small">';
    
    for (const [key, value] of Object.entries(variables)) {
        html += `
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="variable-display">{${key}}</span>
                <span class="variable-value text-truncate ms-2" style="max-width: 200px;" 
                      data-bs-toggle="tooltip" title="${value}">
                    ${value}
                </span>
            </div>
        `;
    }
    
    html += '</div>';
    document.getElementById('variablesList').innerHTML = html;
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Send test email
function sendTestEmail(event) {
    event.preventDefault();
    
    const form = event.target;
    const button = document.getElementById('sendButton');
    const resultsDiv = document.getElementById('testResults');
    
    // Disable button and show loading
    button.disabled = true;
    button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.sending") }}...';
    
    // Clear previous results
    resultsDiv.style.display = 'none';
    resultsDiv.innerHTML = '';
    
    // Prepare data
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    data.template_id = {{ template.id }};
    
    // Send request
    fetch(`{{ base_url }}/api/email-templates/test`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '{{ csrf_token }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            resultsDiv.innerHTML = `
                <div class="test-result success">
                    <i class="bi bi-check-circle me-2"></i>
                    <strong>{{ __('common.success') }}!</strong><br>
                    {{ __('config.test_email_sent_to') }} ${data.test_email}
                </div>
            `;
            
            // Refresh email history
            refreshEmailHistory();
        } else {
            throw new Error(result.error || '{{ __("common.unknown_error") }}');
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = `
            <div class="test-result error">
                <i class="bi bi-exclamation-circle me-2"></i>
                <strong>{{ __('common.error') }}:</strong><br>
                ${error.message}
            </div>
        `;
    })
    .finally(() => {
        // Re-enable button
        button.disabled = false;
        button.innerHTML = '<i class="bi bi-send me-2"></i>{{ __("config.send_test_email") }}';
        resultsDiv.style.display = 'block';
    });
}

// Set preview device
function setPreviewDevice(device) {
    const preview = document.getElementById('devicePreview');
    preview.className = `device-preview ${device}`;
    
    // Update button states
    document.querySelectorAll('.btn-group button').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

// View HTML source
function viewHtmlSource() {
    document.getElementById('sourceModalTitle').textContent = '{{ __("config.html_source") }}';
    document.getElementById('sourceCode').textContent = currentPreviewHtml || document.getElementById('previewBody').innerHTML;
    
    const modal = new bootstrap.Modal(document.getElementById('sourceModal'));
    modal.show();
}

// View text version
function viewTextVersion() {
    document.getElementById('sourceModalTitle').textContent = '{{ __("config.text_version") }}';
    document.getElementById('sourceCode').textContent = currentPreviewText || 'No text version available';
    
    const modal = new bootstrap.Modal(document.getElementById('sourceModal'));
    modal.show();
}

// Copy source to clipboard
function copySource() {
    const source = document.getElementById('sourceCode').textContent;
    navigator.clipboard.writeText(source).then(() => {
        // Show success message
        const button = event.target;
        const originalHtml = button.innerHTML;
        button.innerHTML = '<i class="bi bi-check me-2"></i>{{ __("common.copied") }}!';
        
        setTimeout(() => {
            button.innerHTML = originalHtml;
        }, 2000);
    });
}

// View email log
function viewEmailLog(logId) {
    // Implementation for viewing email log details
    console.log('View email log:', logId);
}

// Refresh email history
function refreshEmailHistory() {
    fetch(`{{ base_url }}/api/email-templates/{{ template.id }}/test-history`)
        .then(response => response.json())
        .then(data => {
            if (data.history) {
                const tbody = document.getElementById('emailHistory');
                tbody.innerHTML = data.history.map(log => `
                    <tr>
                        <td>${new Date(log.created_at).toLocaleString()}</td>
                        <td>${log.recipient_email}</td>
                        <td>#${log.invoice_number}</td>
                        <td>
                            ${log.status === 'sent' 
                                ? '<span class="badge bg-success">{{ __("config.sent") }}</span>'
                                : '<span class="badge bg-danger">{{ __("config.failed") }}</span>'
                            }
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-info" 
                                    onclick="viewEmailLog(${log.id})">
                                <i class="bi bi-eye"></i>
                            </button>
                        </td>
                    </tr>
                `).join('') || `
                    <tr>
                        <td colspan="5" class="text-center text-muted py-3">
                            {{ __('config.no_test_emails_sent') }}
                        </td>
                    </tr>
                `;
            }
        })
        .catch(error => {
            console.error('Error refreshing history:', error);
        });
}

// Initialize on load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Load first invoice if available
    const invoiceSelect = document.getElementById('invoice_id');
    if (invoiceSelect.options.length > 1) {
        invoiceSelect.selectedIndex = 1;
        loadInvoiceData();
    }
});
</script>
{% endblock %}