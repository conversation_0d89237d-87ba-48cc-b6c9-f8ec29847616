{% extends "base-modern.twig" %}

{% block title %}{{ client.name }} - {{ __('common.client_details') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ client.name }}</h1>
            <small class="text-muted">{{ __('common.client_number') }}: {{ client.client_number }}</small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/clients/{{ client.id }}/edit" class="btn btn-primary">
                <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
            </a>
            <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" 
                        data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                    <i class="bi bi-three-dots-vertical"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/invoices/create?client_id={{ client.id }}">
                            <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="{{ base_url }}/quotes/create?client_id={{ client.id }}">
                            <i class="bi bi-file-earmark-text me-2"></i>{{ __('quotes.create_quote') }}
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="printClient({{ client.id }})">
                            <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                        </a>
                    </li>
                    <li>
                        <a class="dropdown-item" href="#" onclick="exportClient({{ client.id }})">
                            <i class="bi bi-download me-2"></i>{{ __('common.export') }}
                        </a>
                    </li>
                    <li><hr class="dropdown-divider"></li>
                    <li>
                        <a class="dropdown-item text-danger" href="#" onclick="deleteClient({{ client.id }})">
                            <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                        </a>
                    </li>
                </ul>
            </div>
            <a href="{{ base_url }}/clients" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Client Information -->
        <div class="col-lg-4">
            <!-- Basic Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-building me-2"></i>{{ __('clients.company_information') }}</h6>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-5">{{ __('common.name') }}:</dt>
                        <dd class="col-7">{{ client.name }}</dd>
                        
                        <dt class="col-5">{{ __('clients.company_type') }}:</dt>
                        <dd class="col-7">
                            {% if client.company_type %}
                                <span class="badge bg-info">{{ client.company_type }}</span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-5">{{ __('clients.registration_number') }}:</dt>
                        <dd class="col-7">{{ client.registration_number|default('-') }}</dd>
                        
                        <dt class="col-5">{{ __('clients.vat_number') }}:</dt>
                        <dd class="col-7">{{ client.vat_number|default('-') }}</dd>
                        
                        <dt class="col-5">{{ __('common.status') }}:</dt>
                        <dd class="col-7">
                            {% if client.is_active %}
                                <span class="badge bg-success">{{ __('common.active') }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ __('common.inactive') }}</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-5">{{ __('common.created_at') }}:</dt>
                        <dd class="col-7">{{ client.created_at|date('d/m/Y') }}</dd>
                    </dl>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-telephone me-2"></i>{{ __('clients.contact_information') }}</h6>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-5">{{ __('common.email') }}:</dt>
                        <dd class="col-7">
                            {% if client.email %}
                                <a href="mailto:{{ client.email }}">{{ client.email }}</a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-5">{{ __('common.phone') }}:</dt>
                        <dd class="col-7">
                            {% if client.phone %}
                                <a href="tel:{{ client.phone }}">{{ client.phone }}</a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-5">{{ __('clients.fax') }}:</dt>
                        <dd class="col-7">{{ client.fax|default('-') }}</dd>
                        
                        <dt class="col-5">{{ __('clients.website') }}:</dt>
                        <dd class="col-7">
                            {% if client.website %}
                                <a href="{{ client.website }}" target="_blank">{{ client.website }}</a>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>

            <!-- Address -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-geo-alt me-2"></i>{{ __('common.address') }}</h6>
                </div>
                <div class="card-body">
                    <address class="mb-0">
                        {{ client.address }}<br>
                        {{ client.postal_code }} {{ client.city }}<br>
                        {{ client.country }}
                    </address>
                </div>
            </div>
        </div>

        <!-- Related Information -->
        <div class="col-lg-8">
            <!-- Statistics -->
            <div class="row g-3 mb-4">
                <div class="col-md-3">
                    <div class="card border-start border-4 border-primary h-100">
                        <div class="card-body">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('clients.total_invoices') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_invoices|default(0) }}</div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-start border-4 border-success h-100">
                        <div class="card-body">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('clients.paid_amount') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.paid_amount|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-start border-4 border-warning h-100">
                        <div class="card-body">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('clients.pending_amount') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.pending_amount|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-3">
                    <div class="card border-start border-4 border-info h-100">
                        <div class="card-body">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('clients.total_quotes') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_quotes|default(0) }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Invoices -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h6 class="mb-0 fw-bold text-primary">{{ __('clients.recent_invoices') }}</h6>
                    <a href="{{ base_url }}/invoices?client_id={{ client.id }}" class="btn btn-sm btn-outline-primary">
                        {{ __('common.view_all') }} <i class="bi bi-arrow-right ms-1"></i>
                    </a>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>{{ __('invoices.invoice_number') }}</th>
                                    <th>{{ __('common.date') }}</th>
                                    <th>{{ __('common.amount') }}</th>
                                    <th>{{ __('common.status') }}</th>
                                    <th>{{ __('common.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in recent_invoices %}
                                <tr>
                                    <td>
                                        <a href="{{ base_url }}/invoices/{{ invoice.id }}">
                                            {{ invoice.invoice_number }}
                                        </a>
                                    </td>
                                    <td>{{ invoice.issue_date|date('d/m/Y') }}</td>
                                    <td>{{ currency }}{{ invoice.total_amount|number_format(2, ',', ' ') }}</td>
                                    <td>
                                        {% if invoice.status == 'paid' %}
                                            <span class="badge bg-success">{{ __('invoices.paid') }}</span>
                                        {% elseif invoice.status == 'pending' %}
                                            <span class="badge bg-warning">{{ __('invoices.pending') }}</span>
                                        {% elseif invoice.status == 'overdue' %}
                                            <span class="badge bg-danger">{{ __('invoices.overdue') }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ invoice.status }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-3 text-muted">
                                        {{ __('clients.no_invoices_found') }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="mb-0 fw-bold text-primary">{{ __('common.notes') }}</h6>
                </div>
                <div class="card-body">
                    {% if client.notes %}
                        <p class="mb-0">{{ client.notes|nl2br }}</p>
                    {% else %}
                        <p class="text-muted mb-0">{{ __('common.no_notes') }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deleteClient(id) {
    if (confirm('{{ __("clients.delete_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/clients/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function printClient(id) {
    window.open('{{ base_url }}/clients/' + id + '/print', '_blank');
}

function exportClient(id) {
    window.location.href = '{{ base_url }}/clients/' + id + '/export';
}
</script>
{% endblock %}