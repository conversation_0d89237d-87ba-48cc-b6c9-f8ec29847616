<?php

// Create export logs table for tracking downloads

$db_config = [
    'host' => 'localhost',
    'dbname' => 'admindesk',
    'username' => 'root',
    'password' => '',
    'charset' => 'utf8mb4'
];

try {
    $pdo = new PDO(
        "mysql:host={$db_config['host']};dbname={$db_config['dbname']};charset={$db_config['charset']}",
        $db_config['username'],
        $db_config['password'],
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "Connected to database successfully.\n";
    
    // Create export_logs table
    $sql = "CREATE TABLE IF NOT EXISTS export_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        export_type VARCHAR(20) NOT NULL,
        module VARCHAR(50) NOT NULL,
        record_count INT NOT NULL DEFAULT 0,
        filters JSON,
        exported_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45),
        user_agent VARCHAR(255),
        INDEX idx_user_id (user_id),
        INDEX idx_module (module),
        INDEX idx_exported_at (exported_at),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created export_logs table successfully.\n";
    
    // Add some sample data for testing
    $testData = [
        [
            'user_id' => 1,
            'export_type' => 'excel',
            'module' => 'invoices',
            'record_count' => 150,
            'filters' => json_encode(['status' => 'paid', 'date_from' => '2024-01-01']),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ],
        [
            'user_id' => 1,
            'export_type' => 'csv',
            'module' => 'users',
            'record_count' => 50,
            'filters' => json_encode(['group_id' => 2]),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ],
        [
            'user_id' => 1,
            'export_type' => 'pdf',
            'module' => 'products',
            'record_count' => 75,
            'filters' => json_encode(['category' => 'services']),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        ]
    ];
    
    $stmt = $pdo->prepare("
        INSERT INTO export_logs (user_id, export_type, module, record_count, filters, ip_address, user_agent)
        VALUES (:user_id, :export_type, :module, :record_count, :filters, :ip_address, :user_agent)
    ");
    
    foreach ($testData as $data) {
        $stmt->execute($data);
    }
    
    echo "✓ Added sample export log data.\n";
    
    // Display table structure
    $stmt = $pdo->query("DESCRIBE export_logs");
    $columns = $stmt->fetchAll();
    
    echo "\nTable structure:\n";
    echo str_repeat("-", 80) . "\n";
    printf("%-20s %-30s %-10s %-10s\n", "Field", "Type", "Null", "Key");
    echo str_repeat("-", 80) . "\n";
    
    foreach ($columns as $column) {
        printf("%-20s %-30s %-10s %-10s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'],
            $column['Key']
        );
    }
    
    // Display sample data
    $stmt = $pdo->query("SELECT * FROM export_logs ORDER BY exported_at DESC LIMIT 5");
    $logs = $stmt->fetchAll();
    
    echo "\n\nRecent export logs:\n";
    echo str_repeat("-", 120) . "\n";
    printf("%-5s %-10s %-15s %-15s %-10s %-20s %-15s\n", 
        "ID", "User ID", "Type", "Module", "Records", "Exported At", "IP");
    echo str_repeat("-", 120) . "\n";
    
    foreach ($logs as $log) {
        printf("%-5s %-10s %-15s %-15s %-10s %-20s %-15s\n",
            $log['id'],
            $log['user_id'] ?? 'N/A',
            $log['export_type'],
            $log['module'],
            $log['record_count'],
            $log['exported_at'],
            $log['ip_address']
        );
    }
    
    echo "\n✅ Export logs table created and configured successfully!\n";
    
} catch (PDOException $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}