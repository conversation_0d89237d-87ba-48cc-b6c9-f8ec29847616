<?php

namespace App\Models;

use App\Core\Model;
use PDO;

class ActivityLog extends Model
{
    protected $table = 'activity_logs';
    protected $fillable = [
        'user_id', 'entity_type', 'entity_id', 'action', 
        'description', 'changes', 'ip_address', 'user_agent'
    ];
    
    protected $casts = [
        'changes' => 'json',
        'created_at' => 'datetime'
    ];
    
    /**
     * Log an activity
     * 
     * @param string $entityType Type of entity (invoice, client, patient, etc.)
     * @param int|null $entityId ID of the related entity
     * @param string $action Action performed (created, updated, deleted, sent, paid, etc.)
     * @param string $description Human-readable description
     * @param array|null $changes Changed fields and values
     * @param int|null $userId User ID (defaults to current user)
     * @return bool
     */
    public static function log($entityType, $entityId, $action, $description, $changes = null, $userId = null)
    {
        try {
            // Get current user if not provided
            if ($userId === null && isset($_SESSION['user_id'])) {
                $userId = $_SESSION['user_id'];
            }
            
            // Get IP address
            $ipAddress = self::getIpAddress();
            
            // Get user agent
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            // Prepare data
            $data = [
                'user_id' => $userId,
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'action' => $action,
                'description' => $description,
                'changes' => $changes ? json_encode($changes) : null,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent
            ];
            
            // Create log entry
            $log = new static();
            return $log->create($data);
            
        } catch (\Exception $e) {
            // Log error but don't throw exception to avoid breaking the main operation
            error_log("ActivityLog::log error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get the user that performed the activity
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
    
    /**
     * Get formatted activity for display
     * 
     * @return array
     */
    public function getFormatted()
    {
        $data = $this->toArray();
        
        // Add user name if available
        if ($this->user_id) {
            $user = User::find($this->user_id);
            if ($user) {
                $data['user_name'] = $user->getFullName();
                $data['user_email'] = $user->email;
            }
        }
        
        // Format created_at
        if (isset($data['created_at'])) {
            $data['created_at_formatted'] = date('d/m/Y H:i:s', strtotime($data['created_at']));
            $data['created_at_relative'] = $this->getRelativeTime($data['created_at']);
        }
        
        // Decode changes if JSON
        if (isset($data['changes']) && is_string($data['changes'])) {
            $data['changes'] = json_decode($data['changes'], true);
        }
        
        // Add action icon and color
        $data['action_icon'] = $this->getActionIcon($data['action']);
        $data['action_color'] = $this->getActionColor($data['action']);
        
        return $data;
    }
    
    /**
     * Get activities for an entity
     * 
     * @param string $entityType
     * @param int $entityId
     * @param int $limit
     * @return array
     */
    public static function getForEntity($entityType, $entityId, $limit = 50)
    {
        $sql = "SELECT * FROM activity_logs 
                WHERE entity_type = :entity_type 
                AND entity_id = :entity_id 
                ORDER BY created_at DESC 
                LIMIT :limit";
        
        $stmt = self::db()->prepare($sql);
        $stmt->bindParam(':entity_type', $entityType, PDO::PARAM_STR);
        $stmt->bindParam(':entity_id', $entityId, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $activities = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $activity = new static();
            $activity->fill($row);
            $activities[] = $activity->getFormatted();
        }
        
        return $activities;
    }
    
    /**
     * Get activities by user
     * 
     * @param int $userId
     * @param int $limit
     * @return array
     */
    public static function getByUser($userId, $limit = 100)
    {
        $sql = "SELECT * FROM activity_logs 
                WHERE user_id = :user_id 
                ORDER BY created_at DESC 
                LIMIT :limit";
        
        $stmt = self::db()->prepare($sql);
        $stmt->bindParam(':user_id', $userId, PDO::PARAM_INT);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $activities = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $activity = new static();
            $activity->fill($row);
            $activities[] = $activity->getFormatted();
        }
        
        return $activities;
    }
    
    /**
     * Get recent activities
     * 
     * @param int $limit
     * @return array
     */
    public static function getRecent($limit = 50)
    {
        $sql = "SELECT * FROM activity_logs 
                ORDER BY created_at DESC 
                LIMIT :limit";
        
        $stmt = self::db()->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $activities = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $activity = new static();
            $activity->fill($row);
            $activities[] = $activity->getFormatted();
        }
        
        return $activities;
    }
    
    /**
     * Search activities
     * 
     * @param array $criteria
     * @param int $limit
     * @return array
     */
    public static function search($criteria, $limit = 100)
    {
        $where = [];
        $params = [];
        
        if (!empty($criteria['entity_type'])) {
            $where[] = 'entity_type = :entity_type';
            $params['entity_type'] = $criteria['entity_type'];
        }
        
        if (!empty($criteria['action'])) {
            $where[] = 'action = :action';
            $params['action'] = $criteria['action'];
        }
        
        if (!empty($criteria['user_id'])) {
            $where[] = 'user_id = :user_id';
            $params['user_id'] = $criteria['user_id'];
        }
        
        if (!empty($criteria['date_from'])) {
            $where[] = 'created_at >= :date_from';
            $params['date_from'] = $criteria['date_from'];
        }
        
        if (!empty($criteria['date_to'])) {
            $where[] = 'created_at <= :date_to';
            $params['date_to'] = $criteria['date_to'];
        }
        
        if (!empty($criteria['search'])) {
            $where[] = '(description LIKE :search OR changes LIKE :search2)';
            $params['search'] = '%' . $criteria['search'] . '%';
            $params['search2'] = '%' . $criteria['search'] . '%';
        }
        
        $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        
        $sql = "SELECT * FROM activity_logs 
                {$whereClause}
                ORDER BY created_at DESC 
                LIMIT :limit";
        
        $stmt = self::db()->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        $activities = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $activity = new static();
            $activity->fill($row);
            $activities[] = $activity->getFormatted();
        }
        
        return $activities;
    }
    
    /**
     * Get IP address
     * 
     * @return string|null
     */
    private static function getIpAddress()
    {
        // Check for IP behind proxy
        if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
            return $_SERVER['HTTP_CLIENT_IP'];
        } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            // Can contain multiple IPs, get the first one
            $ips = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
            return trim($ips[0]);
        } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            return $_SERVER['REMOTE_ADDR'];
        }
        
        return null;
    }
    
    /**
     * Get relative time string
     * 
     * @param string $datetime
     * @return string
     */
    private function getRelativeTime($datetime)
    {
        $timestamp = strtotime($datetime);
        $diff = time() - $timestamp;
        
        if ($diff < 60) {
            return 'il y a ' . $diff . ' secondes';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return 'il y a ' . $minutes . ' minute' . ($minutes > 1 ? 's' : '');
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return 'il y a ' . $hours . ' heure' . ($hours > 1 ? 's' : '');
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return 'il y a ' . $days . ' jour' . ($days > 1 ? 's' : '');
        } else {
            return date('d/m/Y H:i', $timestamp);
        }
    }
    
    /**
     * Get action icon
     * 
     * @param string $action
     * @return string
     */
    private function getActionIcon($action)
    {
        $icons = [
            'created' => 'fa-plus-circle',
            'updated' => 'fa-edit',
            'deleted' => 'fa-trash',
            'sent' => 'fa-paper-plane',
            'paid' => 'fa-check-circle',
            'viewed' => 'fa-eye',
            'downloaded' => 'fa-download',
            'cancelled' => 'fa-times-circle',
            'restored' => 'fa-undo',
            'logged_in' => 'fa-sign-in-alt',
            'logged_out' => 'fa-sign-out-alt',
            'imported' => 'fa-file-import',
            'exported' => 'fa-file-export'
        ];
        
        return $icons[$action] ?? 'fa-circle';
    }
    
    /**
     * Get action color
     * 
     * @param string $action
     * @return string
     */
    private function getActionColor($action)
    {
        $colors = [
            'created' => 'success',
            'updated' => 'info',
            'deleted' => 'danger',
            'sent' => 'primary',
            'paid' => 'success',
            'viewed' => 'secondary',
            'downloaded' => 'info',
            'cancelled' => 'warning',
            'restored' => 'info',
            'logged_in' => 'success',
            'logged_out' => 'secondary',
            'imported' => 'primary',
            'exported' => 'primary'
        ];
        
        return $colors[$action] ?? 'secondary';
    }
}