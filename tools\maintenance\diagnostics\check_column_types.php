<?php
// Check column types for foreign key compatibility

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Column Types</h2>";
    
    // Check document_types.id
    $stmt = $db->query("SHOW COLUMNS FROM document_types WHERE Field = 'id'");
    $col = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<strong>document_types.id:</strong> " . $col['Type'] . "<br>";
    
    // Check config_invoice_types.id
    $stmt = $db->query("SHOW COLUMNS FROM config_invoice_types WHERE Field = 'id'");
    $col = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<strong>config_invoice_types.id:</strong> " . $col['Type'] . "<br>";
    
    // Check users.id
    $stmt = $db->query("SHOW COLUMNS FROM users WHERE Field = 'id'");
    $col = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<strong>users.id:</strong> " . $col['Type'] . "<br>";
    
    // Check invoices.type_id
    $stmt = $db->query("SHOW COLUMNS FROM invoices WHERE Field = 'type_id'");
    if ($col = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<strong>invoices.type_id:</strong> " . $col['Type'] . "<br>";
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>