<?php

namespace App\Core;

class ValidationException extends \Exception
{
    protected $errors;
    
    public function __construct($errors, $message = 'Validation failed', $code = 422)
    {
        $this->errors = $errors;
        parent::__construct($message, $code);
    }
    
    /**
     * Get validation errors
     */
    public function getErrors()
    {
        return $this->errors;
    }
    
    /**
     * Get first error message
     */
    public function getFirstError()
    {
        foreach ($this->errors as $field => $messages) {
            return $messages[0];
        }
        return 'Validation failed';
    }
}