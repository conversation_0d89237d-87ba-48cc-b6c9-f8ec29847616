<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

// Force French language
$_SESSION['user_language'] = 'fr';
\App\Helpers\Language::setLanguage('fr');

// Test if we can access the template engine
$twig = Flight::view();

// Render a test template with translation
$testTemplate = "{{ __('config.code') }}|{{ __('config.payment_term_name_hint') }}|{{ __('common.name') }}";

$loader = new \Twig\Loader\ArrayLoader([
    'test' => $testTemplate
]);

$testTwig = new \Twig\Environment($loader);

// Add the translation function
$testTwig->addFunction(new \Twig\TwigFunction('__', function($key, $params = []) {
    return __($key, $params);
}));

header('Content-Type: text/plain');
echo "=== PAYMENT TERMS TRANSLATION DEBUG ===\n\n";

echo "1. Direct PHP translation test:\n";
echo "   config.code = " . __('config.code') . "\n";
echo "   config.payment_term_name_hint = " . __('config.payment_term_name_hint') . "\n";
echo "   common.name = " . __('common.name') . "\n\n";

echo "2. Twig template test:\n";
try {
    $result = $testTwig->render('test');
    echo "   Result: " . $result . "\n\n";
} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n\n";
}

echo "3. Language settings:\n";
echo "   Current language: " . \App\Helpers\Language::getCurrentLanguage() . "\n";
echo "   Session language: " . ($_SESSION['user_language'] ?? 'not set') . "\n\n";

echo "4. Check if translations are loaded:\n";
$configTranslations = \App\Helpers\Language::getGroup('config', 'fr');
echo "   Total config translations: " . count($configTranslations) . "\n";
echo "   Sample keys: " . implode(', ', array_slice(array_keys($configTranslations), 0, 5)) . "\n\n";

echo "5. Check specific keys:\n";
$checkKeys = ['code', 'payment_term_name_hint', 'payment_term_code_hint', 'payment_days', 'payment_days_hint'];
foreach ($checkKeys as $key) {
    $value = $configTranslations[$key] ?? 'NOT FOUND';
    echo "   config.$key = $value\n";
}

echo "\n6. Test the actual payment terms page translations:\n";
// Get payment terms to see if they have data
try {
    $db = Flight::db();
    $stmt = $db->query("SELECT * FROM config_payment_terms LIMIT 1");
    $term = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($term) {
        echo "   Sample payment term:\n";
        echo "   - ID: " . $term['id'] . "\n";
        echo "   - Name: " . $term['name'] . "\n";
        echo "   - Code: " . $term['code'] . "\n";
        echo "   - Days: " . $term['days'] . "\n";
    } else {
        echo "   No payment terms found in database\n";
    }
} catch (Exception $e) {
    echo "   Database error: " . $e->getMessage() . "\n";
}