<?php

namespace App\Core;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class TwigExtensions extends AbstractExtension
{
    public function getFilters()
    {
        return [
            new TwigFilter('age', [$this, 'calculateAge']),
            new TwigFilter('number_format', [$this, 'numberFormat']),
            new TwigFilter('humanize', [$this, 'humanize']),
            new TwigFilter('safe_date', [$this, 'safeDate']),
            new TwigFilter('format_date', [$this, 'formatDate']),
            new TwigFilter('format_date_short', [$this, 'formatDateShort']),
            new TwigFilter('format_datetime', [$this, 'formatDateTime']),
        ];
    }

    public function getFunctions()
    {
        return [
            new TwigFunction('csrf_field', [$this, 'csrfField'], ['is_safe' => ['html']]),
            new TwigFunction('__', [$this, 'translate']),
            new TwigFunction('asset', [$this, 'asset']),
            new TwigFunction('cached_asset', [$this, 'cachedAsset']),
            new TwigFunction('shouldShowMenu', [$this, 'shouldShowMenu']),
            new TwigFunction('getMenuClass', [$this, 'getMenuClass']),
        ];
    }

    public function calculateAge($birthDate)
    {
        if (empty($birthDate)) {
            return 0;
        }

        $birthDate = new \DateTime($birthDate);
        $today = new \DateTime();
        $age = $today->diff($birthDate);
        
        return $age->y;
    }

    public function numberFormat($number, $decimals = 0, $decimalSeparator = ',', $thousandsSeparator = '.')
    {
        return number_format((float)$number, $decimals, $decimalSeparator, $thousandsSeparator);
    }

    public function csrfField()
    {
        $token = $_SESSION['csrf_token'] ?? '';
        return '<input type="hidden" name="csrf_token" value="' . htmlspecialchars($token) . '">';
    }

    public function translate($key, $params = [])
    {
        return \__($key, $params);
    }
    
    /**
     * Convert snake_case or camelCase to human readable format
     */
    public function humanize($text)
    {
        // Convert camelCase to snake_case
        $text = preg_replace('/(?<!^)[A-Z]/', '_$0', $text);
        
        // Replace underscores and hyphens with spaces
        $text = str_replace(['_', '-'], ' ', $text);
        
        // Capitalize first letter of each word
        return ucwords(strtolower($text));
    }
    
    /**
     * Safely format dates, handling invalid or empty dates
     */
    public function safeDate($date, $format = 'd/m/Y')
    {
        if (empty($date) || $date === '0000-00-00' || $date === '-0001-11-30') {
            return '';
        }
        
        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format($format);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    /**
     * Format date using system date format
     */
    public function formatDate($date, $format = null)
    {
        if (empty($date) || $date === '0000-00-00' || $date === '-0001-11-30') {
            return '';
        }
        
        // Get system date format if not specified
        if ($format === null) {
            $format = \get_date_format();
        }
        
        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format($format);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    /**
     * Format date using system short date format
     */
    public function formatDateShort($date, $format = null)
    {
        if (empty($date) || $date === '0000-00-00' || $date === '-0001-11-30') {
            return '';
        }
        
        // Get system short date format if not specified
        if ($format === null) {
            $format = $this->getShortDateFormat();
        }
        
        try {
            $dateObj = new \DateTime($date);
            return $dateObj->format($format);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    /**
     * Format datetime using system date and time format
     */
    public function formatDateTime($datetime, $format = null)
    {
        if (empty($datetime)) {
            return '';
        }
        
        // Get system formats if not specified
        if ($format === null) {
            $dateFormat = \get_date_format();
            $timeFormat = $this->getTimeFormat();
            $format = $dateFormat . ' ' . $timeFormat;
        }
        
        try {
            $dateObj = new \DateTime($datetime);
            return $dateObj->format($format);
        } catch (\Exception $e) {
            return '';
        }
    }
    
    /**
     * Get system time format
     */
    private function getTimeFormat()
    {
        static $format = null;
        
        if ($format === null) {
            try {
                $db = \Flight::db();
                $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'time_format' AND category = 'system'");
                $stmt->execute();
                $result = $stmt->fetch(\PDO::FETCH_ASSOC);
                $format = $result['value'] ?? 'H:i';
            } catch (\Exception $e) {
                $format = 'H:i'; // Default format
            }
        }
        
        return $format;
    }
    
    /**
     * Get system short date format
     */
    private function getShortDateFormat()
    {
        static $format = null;
        
        if ($format === null) {
            try {
                $db = \Flight::db();
                $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'date_format_short' AND category = 'system'");
                $stmt->execute();
                $result = $stmt->fetch(\PDO::FETCH_ASSOC);
                $format = $result['value'] ?? 'd/m';
            } catch (\Exception $e) {
                $format = 'd/m'; // Default format
            }
        }
        
        return $format;
    }
    
    /**
     * Asset URL with versioning
     */
    public function asset($path)
    {
        $baseUrl = \Flight::get('flight.base_url');
        $version = $_ENV['APP_VERSION'] ?? '1.0.0';
        $debug = $_ENV['APP_DEBUG'] ?? false;
        
        // In production, use minified versions if available
        if (!$debug && (strpos($path, '.min.') === false)) {
            $minPath = str_replace('.css', '.min.css', $path);
            $minPath = str_replace('.js', '.min.js', $minPath);
            
            // Check if minified version exists
            $fullPath = __DIR__ . '/../../public/' . ltrim($minPath, '/');
            if (file_exists($fullPath)) {
                $path = $minPath;
            }
        }
        
        // Remove leading slash for consistency
        $path = ltrim($path, '/');
        
        // Add version parameter
        $separator = strpos($path, '?') !== false ? '&' : '?';
        
        return rtrim($baseUrl, '/') . '/' . $path . $separator . 'v=' . $version;
    }
    
    /**
     * Cached asset with modification time
     */
    public function cachedAsset($path)
    {
        $baseUrl = \Flight::get('flight.base_url');
        $fullPath = __DIR__ . '/../../public/' . ltrim($path, '/');
        
        // Get file modification time for cache busting
        $version = file_exists($fullPath) ? filemtime($fullPath) : time();
        
        // Remove leading slash for consistency
        $path = ltrim($path, '/');
        
        // Add version parameter
        $separator = strpos($path, '?') !== false ? '&' : '?';
        
        return rtrim($baseUrl, '/') . '/' . $path . $separator . 't=' . $version;
    }
    
    /**
     * Check if menu should be shown
     */
    public function shouldShowMenu($menuId)
    {
        // Include the menu helper if not already included
        require_once __DIR__ . '/../helpers/menu-config.php';
        
        // Call the global function
        return \shouldShowMenu($menuId);
    }
    
    /**
     * Get menu CSS class
     */
    public function getMenuClass($menuId)
    {
        // Include the menu helper if not already included
        require_once __DIR__ . '/../helpers/menu-config.php';
        
        // Call the global function
        return \getMenuClass($menuId);
    }
}