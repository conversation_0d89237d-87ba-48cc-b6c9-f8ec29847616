<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\CustomField;
use Flight;

class FieldManagerController extends Controller
{
    /**
     * Display field management interface
     */
    public function index($module = null)
    {
        // Default to patients if no module specified
        if (!$module) {
            $module = 'patients';
        }
        // Get all fields for the module
        $allFields = CustomField::getAllFieldsForModule($module);
        
        // Get current visibility settings
        $listFields = CustomField::getVisibleFields($module, 'list');
        $formFields = CustomField::getVisibleFields($module, 'form');
        
        // Create visibility map
        $visibilityMap = [];
        foreach ($listFields as $field) {
            $visibilityMap['list'][$field['field_name']] = true;
        }
        foreach ($formFields as $field) {
            $visibilityMap['form'][$field['field_name']] = true;
        }
        
        // Get available modules
        $modules = [
            'patients' => __('patients.title'),
            'clients' => __('clients.title'),
            // Add more modules as they are created
        ];
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'config/field-manager-modern';
        
        $this->render($viewFile, [
            'title' => __('config.field_management'),
            'current_module' => $module,
            'modules' => $modules,
            'all_fields' => $allFields,
            'visibility_map' => $visibilityMap,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }
    
    /**
     * Save field visibility settings
     */
    public function saveVisibility()
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        $module = $this->input('module');
        $viewType = $this->input('view_type');
        $fields = $this->input('fields', []);
        
        if (empty($module) || empty($viewType)) {
            Flight::json(['error' => __('validation.missing_required_fields')], 400);
            return;
        }
        
        // Prepare fields data
        $fieldData = [];
        foreach ($fields as $field) {
            // Handle both simple array and object array formats
            if (is_array($field)) {
                $fieldData[] = [
                    'field_name' => $field['field_name'],
                    'is_visible' => 1,
                    'display_order' => $field['display_order'] ?? count($fieldData) + 1
                ];
            } else {
                $fieldData[] = [
                    'field_name' => $field,
                    'is_visible' => 1,
                    'display_order' => count($fieldData) + 1
                ];
            }
        }
        
        if (CustomField::saveFieldVisibility($module, $viewType, $fieldData)) {
            Flight::json(['success' => true, 'message' => __('config.visibility_saved')]);
        } else {
            Flight::json(['error' => __('common.error_occurred')], 500);
        }
    }
    
    /**
     * Create new custom field
     */
    public function createField()
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        $module = $this->input('module');
        
        // Get the French label as the primary label
        $fieldLabelFr = $this->input('field_label_fr') ?: $this->input('field_label');
        
        $fieldData = [
            'module' => $module,
            'field_name' => $this->input('field_name'),
            'field_label' => $fieldLabelFr,
            'field_type' => $this->input('field_type', 'text'),
            'field_options' => $this->input('field_options'),
            'placeholder' => $this->input('placeholder_fr') ?: $this->input('placeholder'),
            'default_value' => $this->input('default_value'),
            'help_text' => $this->input('help_text_fr') ?: $this->input('help_text'),
            'template_variable' => $this->input('template_variable'),
            'is_required' => $this->input('is_required') ? 1 : 0,
            'is_searchable' => $this->input('is_searchable') ? 1 : 0,
            'is_sortable' => $this->input('is_sortable') ? 1 : 0,
            'created_by' => $_SESSION['user_id']
        ];
        
        // Generate template variable if not provided
        if (empty($fieldData['template_variable'])) {
            $fieldData['template_variable'] = '{{' . $module . '.' . $fieldData['field_name'] . '}}';
        }
        
        // Validate field name
        if (!preg_match('/^[a-z][a-z0-9_]*$/', $fieldData['field_name'])) {
            Flight::json(['error' => __('config.invalid_field_name')], 400);
            return;
        }
        
        try {
            $customField = new CustomField();
            $fieldId = $customField->create($fieldData);
            
            if ($fieldId) {
                // Save translations for other languages
                $this->saveFieldTranslations($fieldId, [
                    'fr' => [
                        'label' => $fieldLabelFr,
                        'placeholder' => $this->input('placeholder_fr'),
                        'help_text' => $this->input('help_text_fr')
                    ],
                    'en' => [
                        'label' => $this->input('field_label_en'),
                        'placeholder' => $this->input('placeholder_en'),
                        'help_text' => $this->input('help_text_en')
                    ]
                ]);
                
                // Add column to the actual table (for better performance)
                $this->addColumnToTable($module, $fieldData);
                
                Flight::json([
                    'success' => true, 
                    'message' => __('config.field_created'),
                    'field_id' => $fieldId
                ]);
            } else {
                Flight::json(['error' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            $error = strpos($e->getMessage(), 'Duplicate') !== false 
                ? __('config.field_already_exists') 
                : __('common.error_occurred');
            Flight::json(['error' => $error], 500);
        }
    }
    
    /**
     * Update custom field
     */
    public function updateField($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        $fieldData = [
            'field_label' => $this->input('field_label'),
            'placeholder' => $this->input('placeholder'),
            'default_value' => $this->input('default_value'),
            'help_text' => $this->input('help_text'),
            'is_required' => $this->input('is_required') ? 1 : 0,
            'is_searchable' => $this->input('is_searchable') ? 1 : 0,
            'is_sortable' => $this->input('is_sortable') ? 1 : 0,
            'updated_by' => $_SESSION['user_id']
        ];
        
        // Field options can be updated for select/multiselect
        if (in_array($this->input('field_type'), ['select', 'multiselect'])) {
            $fieldData['field_options'] = $this->input('field_options');
        }
        
        try {
            $customField = new CustomField();
            if ($customField->update($id, $fieldData)) {
                Flight::json(['success' => true, 'message' => __('config.field_updated')]);
            } else {
                Flight::json(['error' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['error' => __('common.error_occurred')], 500);
        }
    }
    
    /**
     * Delete custom field
     */
    public function deleteField($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        try {
            // Get field info before deletion
            $field = CustomField::findById($id);
            if (!$field) {
                Flight::json(['error' => __('common.not_found')], 404);
                return;
            }
            
            // Only allow deletion of custom fields
            if ($field['field_source'] === 'default') {
                Flight::json(['error' => __('config.cannot_delete_default_field')], 400);
                return;
            }
            
            $customField = new CustomField();
            if ($customField->update($id, ['is_active' => 0, 'updated_by' => $_SESSION['user_id']])) {
                Flight::json(['success' => true, 'message' => __('config.field_deleted')]);
            } else {
                Flight::json(['error' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['error' => __('common.error_occurred')], 500);
        }
    }
    
    /**
     * Save field translations
     */
    private function saveFieldTranslations($fieldId, $translations)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            INSERT INTO custom_field_translations 
            (field_id, language, label, placeholder, help_text) 
            VALUES (:field_id, :language, :label, :placeholder, :help_text)
            ON DUPLICATE KEY UPDATE
            label = VALUES(label),
            placeholder = VALUES(placeholder),
            help_text = VALUES(help_text)
        ");
        
        foreach ($translations as $lang => $data) {
            if (!empty($data['label'])) {
                $stmt->execute([
                    'field_id' => $fieldId,
                    'language' => $lang,
                    'label' => $data['label'],
                    'placeholder' => $data['placeholder'] ?? null,
                    'help_text' => $data['help_text'] ?? null
                ]);
            }
        }
    }
    
    /**
     * Add column to actual database table for better performance
     */
    private function addColumnToTable($module, $fieldData)
    {
        // Map module to table name
        $tableMap = [
            'patients' => 'patients',
            'clients' => 'clients',
            // Add more mappings as needed
        ];
        
        if (!isset($tableMap[$module])) {
            return;
        }
        
        $table = $tableMap[$module];
        $column = $fieldData['field_name'];
        
        // Map field types to SQL types
        $typeMap = [
            'text' => 'VARCHAR(255)',
            'textarea' => 'TEXT',
            'number' => 'DECIMAL(10,2)',
            'date' => 'DATE',
            'datetime' => 'DATETIME',
            'checkbox' => 'TINYINT(1)',
            'email' => 'VARCHAR(255)',
            'phone' => 'VARCHAR(50)',
            'url' => 'VARCHAR(500)',
            'select' => 'VARCHAR(100)',
            'multiselect' => 'TEXT'
        ];
        
        $sqlType = $typeMap[$fieldData['field_type']] ?? 'VARCHAR(255)';
        $default = $fieldData['default_value'] ? "DEFAULT '{$fieldData['default_value']}'" : 'DEFAULT NULL';
        
        try {
            $db = Flight::db();
            $sql = "ALTER TABLE `{$table}` ADD COLUMN `custom_{$column}` {$sqlType} {$default}";
            $db->exec($sql);
        } catch (\Exception $e) {
            // Column might already exist or table alteration failed
            // We'll use the EAV pattern as fallback
        }
    }
}