<?php
return [
    // Page titles
    'title' => 'Product Categories',
    'create' => 'Create Category',
    'edit' => 'Edit Category',
    'list' => 'Category List',
    
    // Buttons
    'create_button' => 'New Category',
    'back_to_list' => 'Back to Categories',
    
    // Table headers
    'name' => 'Name',
    'parent' => 'Parent Category',
    'products_count' => 'Products',
    'sort_order' => 'Sort Order',
    'icon' => 'Icon',
    'is_active' => 'Active',
    'actions' => 'Actions',
    
    // Form fields
    'no_parent' => 'No Parent (Root Category)',
    'icon_help' => 'Font Awesome icon class (e.g., fa-box)',
    'sort_order_help' => 'Lower numbers appear first',
    
    // Messages
    'no_categories' => 'No categories found',
    'created_success' => 'Category created successfully',
    'updated_success' => 'Category updated successfully',
    'deleted_success' => 'Category deleted successfully',
    'has_products' => 'Cannot delete category with products',
    'has_children' => 'Cannot delete category with subcategories',
    
    // Validation
    'name_required' => 'Category name is required',
];