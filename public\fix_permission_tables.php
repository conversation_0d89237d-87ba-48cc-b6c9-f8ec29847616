<?php
// Start session
session_start();

// Check authentication
if (!isset($_SESSION['user_id'])) {
    die("Unauthorized access. Please login first.");
}

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin']) {
    $isAdmin = true;
} elseif (isset($_SESSION['user_groups'])) {
    if (is_array($_SESSION['user_groups'])) {
        foreach ($_SESSION['user_groups'] as $group) {
            if (strtolower($group['name']) === 'administrators') {
                $isAdmin = true;
                break;
            }
        }
    } elseif ($_SESSION['user_groups'] === 'Administrators') {
        $isAdmin = true;
    }
}

if (!$isAdmin) {
    die("Only administrators can run this script.");
}

// Load bootstrap
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Permission Tables - Fit360 AdminDesk</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 800px; margin-top: 50px; }
        .card { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .log-output { background-color: #1e1e1e; color: #d4d4d4; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; max-height: 400px; overflow-y: auto; }
        .success { color: #4ec9b0; }
        .error { color: #f48771; }
        .info { color: #569cd6; }
        .warning { color: #dcdcaa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h4 class="mb-0">Fix Permission Tables</h4>
            </div>
            <div class="card-body">
                <p>This script will fix the permission tables to be compatible with your database structure.</p>
                
                <div class="log-output">
                    <?php
                    try {
                        echo '<span class="info">[INFO] Starting table fixes...</span><br>';
                        
                        // Get database connection
                        $db = Flight::db();
                        echo '<span class="success">[✓] Database connection established</span><br><br>';
                        
                        // First, check the users table structure
                        echo '<span class="info">[INFO] Checking users table structure...</span><br>';
                        $stmt = $db->query("SHOW COLUMNS FROM users WHERE Field = 'id'");
                        $userIdColumn = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        $userIdType = strtoupper($userIdColumn['Type']);
                        echo '<span class="info">Users table ID type: ' . $userIdType . '</span><br>';
                        
                        // Extract the integer type (INT or INT(11) or BIGINT, etc.)
                        $intType = 'INT';
                        if (preg_match('/^(TINYINT|SMALLINT|MEDIUMINT|INT|BIGINT)/i', $userIdType, $matches)) {
                            $intType = strtoupper($matches[1]);
                        }
                        
                        // Check user_groups table structure
                        echo '<span class="info">[INFO] Checking user_groups table structure...</span><br>';
                        $stmt = $db->query("SHOW COLUMNS FROM user_groups WHERE Field = 'id'");
                        $groupIdColumn = $stmt->fetch(PDO::FETCH_ASSOC);
                        $groupIdType = strtoupper($groupIdColumn['Type']);
                        echo '<span class="info">User groups table ID type: ' . $groupIdType . '</span><br><br>';
                        
                        // Create permission_templates table with correct foreign key type
                        echo '<span class="info">[INFO] Creating permission_templates table...</span><br>';
                        try {
                            // First try to drop if exists (in case of partial creation)
                            $db->exec("DROP TABLE IF EXISTS permission_templates");
                            
                            $sql = "CREATE TABLE permission_templates (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                name VARCHAR(100) NOT NULL,
                                description TEXT,
                                code VARCHAR(50) NOT NULL UNIQUE,
                                permissions_json JSON NOT NULL COMMENT 'JSON structure: {module_code: [permission_codes]}',
                                is_system BOOLEAN DEFAULT FALSE COMMENT 'System templates cannot be deleted',
                                created_by $intType,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                
                                INDEX idx_code (code),
                                INDEX idx_system (is_system)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                            
                            $db->exec($sql);
                            echo '<span class="success">[✓] Created permission_templates table</span><br>';
                            
                            // Add foreign key separately to handle potential issues
                            try {
                                $db->exec("ALTER TABLE permission_templates ADD CONSTRAINT fk_templates_created_by 
                                          FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL");
                                echo '<span class="success">[✓] Added foreign key for created_by</span><br>';
                            } catch (PDOException $e) {
                                echo '<span class="warning">[!] Could not add foreign key: ' . $e->getMessage() . '</span><br>';
                            }
                            
                        } catch (PDOException $e) {
                            echo '<span class="error">[✗] Error creating permission_templates: ' . $e->getMessage() . '</span><br>';
                        }
                        
                        // Create permission_audit_log table
                        echo '<br><span class="info">[INFO] Creating permission_audit_log table...</span><br>';
                        try {
                            // First try to drop if exists
                            $db->exec("DROP TABLE IF EXISTS permission_audit_log");
                            
                            // Extract group ID type
                            $groupIntType = 'INT';
                            if (preg_match('/^(TINYINT|SMALLINT|MEDIUMINT|INT|BIGINT)/i', $groupIdType, $matches)) {
                                $groupIntType = strtoupper($matches[1]);
                            }
                            
                            $sql = "CREATE TABLE permission_audit_log (
                                id INT AUTO_INCREMENT PRIMARY KEY,
                                group_id $groupIntType NOT NULL,
                                module_id INT NOT NULL,
                                permission_code VARCHAR(50) NOT NULL,
                                action ENUM('granted', 'revoked') NOT NULL,
                                previous_value BOOLEAN,
                                new_value BOOLEAN,
                                changed_by $intType NOT NULL,
                                changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                ip_address VARCHAR(45),
                                user_agent VARCHAR(255),
                                notes TEXT,
                                
                                INDEX idx_group_module (group_id, module_id),
                                INDEX idx_changed_at (changed_at),
                                INDEX idx_changed_by (changed_by)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                            
                            $db->exec($sql);
                            echo '<span class="success">[✓] Created permission_audit_log table</span><br>';
                            
                            // Add foreign keys separately
                            $foreignKeys = [
                                "ALTER TABLE permission_audit_log ADD CONSTRAINT fk_audit_group 
                                 FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE",
                                "ALTER TABLE permission_audit_log ADD CONSTRAINT fk_audit_module 
                                 FOREIGN KEY (module_id) REFERENCES system_modules(id) ON DELETE CASCADE",
                                "ALTER TABLE permission_audit_log ADD CONSTRAINT fk_audit_changed_by 
                                 FOREIGN KEY (changed_by) REFERENCES users(id)"
                            ];
                            
                            foreach ($foreignKeys as $fkSql) {
                                try {
                                    $db->exec($fkSql);
                                    echo '<span class="success">[✓] Added foreign key constraint</span><br>';
                                } catch (PDOException $e) {
                                    echo '<span class="warning">[!] Could not add foreign key: ' . $e->getMessage() . '</span><br>';
                                }
                            }
                            
                        } catch (PDOException $e) {
                            echo '<span class="error">[✗] Error creating permission_audit_log: ' . $e->getMessage() . '</span><br>';
                        }
                        
                        // Verify all tables exist
                        echo '<br><span class="info">[INFO] Verifying all tables...</span><br>';
                        $requiredTables = [
                            'system_modules',
                            'module_permissions', 
                            'group_permissions',
                            'permission_templates',
                            'permission_audit_log'
                        ];
                        
                        $allExist = true;
                        foreach ($requiredTables as $table) {
                            $stmt = $db->query("SHOW TABLES LIKE '$table'");
                            if ($stmt->rowCount() > 0) {
                                echo '<span class="success">[✓] Table exists: ' . $table . '</span><br>';
                            } else {
                                echo '<span class="error">[✗] Table missing: ' . $table . '</span><br>';
                                $allExist = false;
                            }
                        }
                        
                        if ($allExist) {
                            echo '<br><span class="success"><strong>✅ All permission tables are ready!</strong></span><br>';
                            echo '<span class="info">You can now run the permission seeder.</span><br>';
                        } else {
                            echo '<br><span class="error"><strong>❌ Some tables are still missing.</strong></span><br>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<span class="error">[ERROR] ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                        echo '<pre class="error">' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
                    }
                    ?>
                </div>
                
                <div class="mt-4">
                    <a href="/fit/public/run-permission-seeder.php?key=fit360-permission-setup-2024" class="btn btn-success">
                        <i class="fas fa-seedling"></i> Run Permission Seeder
                    </a>
                    <a href="/fit/public/config" class="btn btn-primary">Back to Configuration</a>
                    <a href="/fit/public" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>