<?php

namespace Tests\Phase3;

use PHPUnit\Framework\TestCase;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\InvoicePayment;
use App\Services\RetrocessionCalculator;
use App\Helpers\MoneyHelper;
use Flight;

class MoneyHelperCompatibilityTest extends TestCase
{
    protected static $db;
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
    }
    
    /**
     * Test that invoice calculations use MoneyHelper correctly
     */
    public function testInvoiceCalculationsWithRounding()
    {
        // Test case 1: Numbers that round differently with PHP_ROUND_HALF_UP
        $testCases = [
            // [quantity, unit_price, vat_rate, expected_subtotal, expected_vat, expected_total]
            [2.5, 45.99, 17, 114.98, 19.55, 134.53],  // 2.5 * 45.99 = 114.975 → 114.98
            [3, 33.333, 17, 100.00, 17.00, 117.00],   // 3 * 33.333 = 99.999 → 100.00
            [1, 99.995, 17, 100.00, 17.00, 117.00],   // 99.995 → 100.00
            [4, 12.345, 17, 49.38, 8.39, 57.77],      // 4 * 12.345 = 49.38
        ];
        
        foreach ($testCases as $index => $testCase) {
            list($quantity, $unitPrice, $vatRate, $expectedSubtotal, $expectedVat, $expectedTotal) = $testCase;
            
            // Create item and calculate
            $item = new InvoiceItem();
            $item->quantity = $quantity;
            $item->unit_price = $unitPrice;
            $item->vat_rate = $vatRate;
            $item->calculateTotals();
            
            $subtotal = MoneyHelper::round($quantity * $unitPrice);
            $vat = MoneyHelper::round($subtotal * ($vatRate / 100));
            $total = MoneyHelper::round($subtotal + $vat);
            
            $this->assertEquals($expectedSubtotal, $subtotal, 
                "Test case $index: Subtotal calculation failed for $quantity * $unitPrice");
            $this->assertEquals($expectedVat, $vat, 
                "Test case $index: VAT calculation failed for $expectedSubtotal * $vatRate%");
            $this->assertEquals($expectedTotal, $total, 
                "Test case $index: Total calculation failed");
            
            // Verify item calculations match
            $this->assertEquals($expectedVat, $item->vat_amount, 
                "Test case $index: Item VAT amount mismatch");
            $this->assertEquals($expectedTotal, $item->total_amount, 
                "Test case $index: Item total amount mismatch");
        }
    }
    
    /**
     * Test retrocession calculations with MoneyHelper
     */
    public function testRetrocessionCalculationsWithRounding()
    {
        $calculator = new RetrocessionCalculator();
        
        // Test with amounts that have rounding edge cases
        $testData = [
            'cns_amount' => 99.995,      // Should round to 100.00
            'patient_amount' => 49.995,   // Should round to 50.00
            'cns_percent' => 33.33,       // Non-standard percentage
            'patient_percent' => 33.33,
            'secretariat_percent' => 33.34,
            'vat_rate' => 17
        ];
        
        $result = $calculator->calculate($testData);
        
        // Verify amounts are properly rounded
        $this->assertEquals(100.00, $result['cns_amount']);
        $this->assertEquals(50.00, $result['patient_amount']);
        $this->assertEquals(150.00, $result['total_amount']);
        
        // Verify percentage calculations
        $expectedCnsPart = MoneyHelper::calculatePercentage(100.00, 33.33); // 33.33
        $expectedPatientPart = MoneyHelper::calculatePercentage(50.00, 33.33); // 16.67
        $expectedSecretariatTvac = MoneyHelper::calculatePercentage(150.00, 33.34); // 50.01
        
        $this->assertEquals($expectedCnsPart, $result['cns_part']);
        $this->assertEquals($expectedPatientPart, $result['patient_part']);
        $this->assertEquals($expectedSecretariatTvac, $result['secretariat_tvac']);
        
        // Verify VAT calculation
        $expectedVat = MoneyHelper::calculateTax($expectedSecretariatTvac, 17, true);
        $this->assertEquals($expectedVat, $result['vat_amount']);
    }
    
    /**
     * Test payment allocation with rounding
     */
    public function testPaymentAllocationWithRounding()
    {
        // Create invoice with specific amount
        $invoice = new Invoice();
        $invoice->invoice_number = 'TEST-PAY-ROUND-001';
        $invoice->document_type_id = 1;
        $invoice->billable_type = 'client';
        $invoice->billable_id = 1;
        $invoice->issue_date = date('Y-m-d');
        $invoice->status = Invoice::STATUS_SENT;
        $invoice->subtotal = 85.47;  // Amount that leads to complex VAT
        $invoice->vat_amount = 14.53; // 85.47 * 0.17 = 14.5299 → 14.53
        $invoice->total = 100.00;
        
        // Verify total calculation
        $calculatedTotal = MoneyHelper::round($invoice->subtotal + $invoice->vat_amount);
        $this->assertEquals($invoice->total, $calculatedTotal);
        
        // Test partial payments
        $payments = [33.333, 33.334, 33.333]; // Total: 100.00
        $totalPaid = 0;
        
        foreach ($payments as $payment) {
            $roundedPayment = MoneyHelper::round($payment);
            $totalPaid = MoneyHelper::round($totalPaid + $roundedPayment);
        }
        
        $this->assertEquals(100.00, $totalPaid);
    }
    
    /**
     * Test credit note calculations
     */
    public function testCreditNoteCalculations()
    {
        // Original invoice amount
        $originalAmount = 117.00;
        $originalVat = 17.00;
        $originalSubtotal = 100.00;
        
        // Partial credit (e.g., 40%)
        $creditPercentage = 40;
        
        $creditSubtotal = MoneyHelper::calculatePercentage($originalSubtotal, $creditPercentage);
        $creditVat = MoneyHelper::calculatePercentage($originalVat, $creditPercentage);
        $creditTotal = MoneyHelper::round($creditSubtotal + $creditVat);
        
        $this->assertEquals(40.00, $creditSubtotal);
        $this->assertEquals(6.80, $creditVat);
        $this->assertEquals(46.80, $creditTotal);
        
        // Verify negative amounts for credit note
        $this->assertEquals(-40.00, -$creditSubtotal);
        $this->assertEquals(-6.80, -$creditVat);
        $this->assertEquals(-46.80, -$creditTotal);
    }
    
    /**
     * Test edge cases in monetary calculations
     */
    public function testMonetaryEdgeCases()
    {
        // Test floating point precision issues
        $amount1 = 0.1;
        $amount2 = 0.2;
        $sum = MoneyHelper::round($amount1 + $amount2);
        $this->assertEquals(0.30, $sum);
        
        // Test division and multiplication chains
        $base = 100;
        $divided = MoneyHelper::round($base / 3); // 33.33
        $multiplied = MoneyHelper::round($divided * 3); // 99.99
        $this->assertEquals(33.33, $divided);
        $this->assertEquals(99.99, $multiplied);
        
        // Test percentage calculation edge cases
        $this->assertEquals(0.01, MoneyHelper::calculatePercentage(0.03, 33.33));
        $this->assertEquals(0.01, MoneyHelper::calculatePercentage(0.02, 50));
        $this->assertEquals(100.00, MoneyHelper::calculatePercentage(99.995, 100));
    }
    
    /**
     * Test invoice line sorting and order preservation
     */
    public function testInvoiceLineOrder()
    {
        $lines = [
            ['quantity' => 1.5, 'unit_price' => 33.33, 'order' => 1],
            ['quantity' => 2.5, 'unit_price' => 22.22, 'order' => 2],
            ['quantity' => 3.5, 'unit_price' => 11.11, 'order' => 3],
        ];
        
        $totalSubtotal = 0;
        $totalVat = 0;
        
        foreach ($lines as $line) {
            $subtotal = MoneyHelper::round($line['quantity'] * $line['unit_price']);
            $vat = MoneyHelper::round($subtotal * 0.17);
            
            $totalSubtotal = MoneyHelper::round($totalSubtotal + $subtotal);
            $totalVat = MoneyHelper::round($totalVat + $vat);
        }
        
        $total = MoneyHelper::round($totalSubtotal + $totalVat);
        
        // Verify accumulation doesn't introduce errors
        $this->assertGreaterThan(0, $total);
        $this->assertEquals($total, MoneyHelper::round($totalSubtotal + $totalVat));
    }
}