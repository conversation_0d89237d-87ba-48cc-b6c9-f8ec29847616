<?php

namespace App\Database\Seeds;

use Flight;

class PaymentMethodsSeeder
{
    public function run()
    {
        $db = Flight::db();
        
        // Check if payment methods already exist
        $stmt = $db->query("SELECT COUNT(*) as count FROM payment_methods");
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "Payment methods already exist, skipping...\n";
            return;
        }
        
        $paymentMethods = [
            [
                'name' => json_encode([
                    'fr' => 'Espèces',
                    'en' => 'Cash'
                ]),
                'code' => 'CASH',
                'is_active' => 1
            ],
            [
                'name' => json_encode([
                    'fr' => 'Carte de crédit',
                    'en' => 'Credit Card'
                ]),
                'code' => 'CARD',
                'is_active' => 1
            ],
            [
                'name' => json_encode([
                    'fr' => 'Virement bancaire',
                    'en' => 'Bank Transfer'
                ]),
                'code' => 'BANK',
                'is_active' => 1
            ],
            [
                'name' => json_encode([
                    'fr' => 'Chèque',
                    'en' => 'Check'
                ]),
                'code' => 'CHECK',
                'is_active' => 1
            ],
            [
                'name' => json_encode([
                    'fr' => 'PayPal',
                    'en' => 'PayPal'
                ]),
                'code' => 'PAYPAL',
                'is_active' => 0
            ]
        ];
        
        // Insert payment methods
        $stmt = $db->prepare("
            INSERT INTO payment_methods (name, code, is_active, created_at)
            VALUES (?, ?, ?, NOW())
        ");
        
        foreach ($paymentMethods as $method) {
            $stmt->execute([
                $method['name'],
                $method['code'],
                $method['is_active']
            ]);
            echo "Created payment method: " . $method['code'] . "\n";
        }
        
        echo "Payment methods seeded successfully!\n";
    }
}