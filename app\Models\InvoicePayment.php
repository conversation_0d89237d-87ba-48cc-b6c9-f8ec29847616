<?php

namespace App\Models;

use App\Core\Model;
use App\Helpers\MoneyHelper;

class InvoicePayment extends Model
{
    protected $table = 'invoice_payments';
    
    /**
     * Fields that contain monetary values
     */
    protected $monetary = [
        'amount'
    ];
    
    protected $fillable = [
        'invoice_id',
        'payment_date',
        'amount',
        'payment_method',
        'reference',
        'notes',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'amount' => 'decimal:2',
        'payment_date' => 'date'
    ];
    
    /**
     * Get the invoice this payment belongs to
     */
    public function getInvoice()
    {
        return Invoice::find($this->invoice_id);
    }
    
    /**
     * Get payment method label
     */
    public function getPaymentMethodLabel()
    {
        $methods = [
            'cash' => 'Cash',
            'bank_transfer' => 'Bank Transfer',
            'credit_card' => 'Credit Card',
            'debit_card' => 'Debit Card',
            'check' => 'Check',
            'paypal' => 'PayPal',
            'other' => 'Other'
        ];
        
        return $methods[$this->payment_method] ?? $this->payment_method;
    }
}