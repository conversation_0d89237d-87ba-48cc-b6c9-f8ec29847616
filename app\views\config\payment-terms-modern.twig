{% extends "base-modern.twig" %}

{% block title %}{{ __('config.payment_terms') }}{% endblock %}

{% block head_extra %}
<style>
.status-toggle {
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}
.status-toggle:hover {
    opacity: 0.8;
}
.status-toggle i {
    transition: transform 0.2s ease;
}
.status-toggle:hover i {
    transform: scale(1.2);
}
.payment-term-card {
    transition: all 0.3s ease;
}
.payment-term-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
.default-badge {
    position: absolute;
    top: 10px;
    right: 10px;
}
.card-header .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    vertical-align: middle;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.payment_terms') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentTermModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_payment_term') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.payment_terms_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Current Active Payment Terms -->
    <div class="mb-5">
        <h4 class="mb-3">{{ __('config.current_active_payment_terms') }}</h4>
        <div class="row g-4">
            {% set hasActive = false %}
            {% for term in paymentTerms|filter(term => term.is_active) %}
                {% set hasActive = true %}
                <div class="col-lg-4 col-md-6" data-id="{{ term.id }}" 
                     data-name="{{ term.display_name|e('html_attr') }}"
                     data-code="{{ term.code }}"
                     data-days="{{ term.days }}"
                     data-description="{{ term.display_description|default('')|e('html_attr') }}"
                     data-is-default="{{ term.is_default ? '1' : '0' }}"
                     data-raw-name="{{ term.name }}"
                     data-raw-description="{{ term.description|default('') }}">
                    <div class="card h-100 payment-term-card">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ term.display_name }}</h5>
                                <div class="d-flex gap-2 align-items-center">
                                    {% if term.is_default %}
                                        <span class="badge bg-light text-primary">
                                            <i class="bi bi-star-fill me-1"></i>{{ __('common.default') }}
                                        </span>
                                    {% endif %}
                                    <span class="status-toggle" 
                                          onclick="toggleStatus({{ term.id }}, false)"
                                          title="{{ __('config.click_to_deactivate') }}"
                                          style="cursor: pointer;">
                                        <i class="bi bi-eye-fill text-success fs-5"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="text-muted small">{{ __('config.code') }}</div>
                                <div><code>{{ term.code }}</code></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="text-muted small">{{ __('config.payment_days') }}</div>
                                <div class="fw-bold">
                                    {% if term.days == 0 %}
                                        {{ __('config.immediate_payment') }}
                                    {% else %}
                                        {{ term.days }} {{ __('common.days') }}
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if term.display_description %}
                            <div class="mb-3">
                                <div class="text-muted small">{{ __('common.description') }}</div>
                                <div>{{ term.display_description }}</div>
                            </div>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editPaymentTerm({{ term.id }}, '{{ term.display_name|e('js') }}', '{{ term.code }}', {{ term.days }}, '{{ term.display_description|default('')|e('js') }}', {{ term.is_default ? 'true' : 'false' }})">
                                    <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deletePaymentTerm({{ term.id }}, '{{ term.display_name|e('js') }}')">
                                    <i class="bi bi-trash me-1"></i>{{ __('common.delete') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
            
            {% if not hasActive %}
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('config.no_active_payment_terms') }}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Inactive Payment Terms -->
    {% set hasInactive = false %}
    {% for term in paymentTerms %}
        {% if not term.is_active %}
            {% set hasInactive = true %}
        {% endif %}
    {% endfor %}
    
    {% if hasInactive %}
    <div class="mb-5">
        <h4 class="mb-3">{{ __('config.inactive_payment_terms') }}</h4>
        <div class="row g-4">
            {% for term in paymentTerms %}
                {% if not term.is_active %}
                <div class="col-lg-4 col-md-6" data-id="{{ term.id }}"
                     data-name="{{ term.display_name|e('html_attr') }}"
                     data-code="{{ term.code }}"
                     data-days="{{ term.days }}"
                     data-description="{{ term.display_description|default('')|e('html_attr') }}"
                     data-is-default="{{ term.is_default ? '1' : '0' }}"
                     data-raw-name="{{ term.name }}"
                     data-raw-description="{{ term.description|default('') }}">
                    <div class="card h-100 opacity-75">
                        <div class="card-header bg-secondary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">{{ term.display_name }}</h5>
                                <span class="status-toggle" 
                                      onclick="toggleStatus({{ term.id }}, true)"
                                      title="{{ __('config.click_to_activate') }}"
                                      style="cursor: pointer;">
                                    <i class="bi bi-eye-slash-fill text-danger fs-5"></i>
                                </span>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="text-muted small">{{ __('config.code') }}</div>
                                <div><code>{{ term.code }}</code></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="text-muted small">{{ __('config.payment_days') }}</div>
                                <div class="fw-bold">
                                    {% if term.days == 0 %}
                                        {{ __('config.immediate_payment') }}
                                    {% else %}
                                        {{ term.days }} {{ __('common.days') }}
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if term.display_description %}
                            <div class="mb-3">
                                <div class="text-muted small">{{ __('common.description') }}</div>
                                <div>{{ term.display_description }}</div>
                            </div>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="editPaymentTerm({{ term.id }}, '{{ term.display_name|e('js') }}', '{{ term.code }}', {{ term.days }}, '{{ term.display_description|default('')|e('js') }}', {{ term.is_default ? 'true' : 'false' }})">
                                    <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                        onclick="deletePaymentTerm({{ term.id }}, '{{ term.display_name|e('js') }}')">
                                    <i class="bi bi-trash me-1"></i>{{ __('common.delete') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Add/Edit Payment Term Modal -->
<div class="modal fade" id="paymentTermModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="paymentTermForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" id="paymentTermId" name="id" value="">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentTermModalTitle">{{ __('config.add_payment_term') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">{{ __('common.name') }} *</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <small class="text-muted">{{ __('config.payment_term_name_hint') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="code" class="form-label">{{ __('config.code') }} *</label>
                        <input type="text" class="form-control" id="code" name="code" required pattern="[a-zA-Z0-9_\-]+" maxlength="50">
                        <small class="text-muted">{{ __('config.payment_term_code_hint') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="days" class="form-label">{{ __('config.payment_days') }} *</label>
                        <input type="number" class="form-control" id="days" name="days" min="0" max="365" value="0" required>
                        <small class="text-muted">{{ __('config.payment_days_hint') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">{{ __('common.description') }}</label>
                        <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                        <small class="text-muted">{{ __('config.payment_term_description_hint') }}</small>
                    </div>
                    
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="is_default" name="is_default" value="1">
                        <label class="form-check-label" for="is_default">
                            {{ __('config.set_as_default') }}
                        </label>
                        <small class="text-muted d-block">{{ __('config.default_payment_term_hint') }}</small>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" checked>
                        <label class="form-check-label" for="is_active">
                            {{ __('common.active') }}
                        </label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
let paymentTermModal;

// Wait for DOM and Bootstrap to be ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap modal
    paymentTermModal = new bootstrap.Modal(document.getElementById('paymentTermModal'));

    // Form submission
    document.getElementById('paymentTermForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const id = formData.get('id');
        
        // Ensure checkbox values are included
        if (!formData.has('is_active')) {
            formData.append('is_active', '0');
        }
        if (!formData.has('is_default')) {
            formData.append('is_default', '0');
        }
        
        // Add method override for PUT requests
        if (id) {
            formData.append('_method', 'PUT');
        }
        
        // Debug: Log form data
        console.log('Form submission - ID:', id);
        for (let [key, value] of formData.entries()) {
            console.log(key + ':', value);
        }
        
        fetch('{{ base_url }}/config/payment-terms' + (id ? '/' + id : ''), {
            method: 'POST', // Always use POST, let _method handle the actual method
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                paymentTermModal.hide();
                window.location.reload();
            } else {
                alert(data.message || '{{ __("common.error") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("common.error_occurred") }}');
        });
    });

    // Reset form when modal is closed
    document.getElementById('paymentTermModal').addEventListener('hidden.bs.modal', function() {
        document.getElementById('paymentTermForm').reset();
        document.getElementById('paymentTermId').value = '';
        document.getElementById('paymentTermModalTitle').textContent = '{{ __("config.add_payment_term") }}';
        document.getElementById('code').readOnly = false;
    });

    // Auto-generate code from name
    document.getElementById('name').addEventListener('input', function() {
        if (!document.getElementById('paymentTermId').value) { // Only for new terms
            const code = this.value
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '_')
                .replace(/^_+|_+$/g, '');
            document.getElementById('code').value = code;
        }
    });

}); // End of DOMContentLoaded

// Global functions that need to be accessible from onclick handlers

// Edit payment term
function editPaymentTerm(id, name, code, days, description, isDefault) {
    console.log('Edit payment term called with:', {id, name, code, days, description, isDefault});
    
    document.getElementById('paymentTermId').value = id;
    document.getElementById('paymentTermModalTitle').textContent = '{{ __("config.edit_payment_term") }}';
    document.getElementById('name').value = name;
    document.getElementById('code').value = code;
    document.getElementById('code').readOnly = true; // Don't allow changing code
    document.getElementById('days').value = days;
    document.getElementById('description').value = description || '';
    document.getElementById('is_default').checked = isDefault;
    document.getElementById('is_active').checked = true;
    
    if (paymentTermModal) {
        paymentTermModal.show();
    } else {
        console.error('Payment term modal not initialized');
    }
}

// Toggle status
function toggleStatus(id, activate) {
    if (!confirm('{{ __("config.confirm_status_change") }}')) return;
    
    // Get data from element attributes
    const element = document.querySelector(`[data-id="${id}"]`);
    
    if (!element) {
        alert('Payment term element not found');
        return;
    }
    
    // Use the display name from the h5 element (not encoded)
    const displayName = element.querySelector('h5').textContent.trim();
    
    // Try to get description from the card body
    let description = '';
    const descElements = element.querySelectorAll('.mb-3');
    descElements.forEach(el => {
        const label = el.querySelector('.text-muted.small');
        if (label && label.textContent === '{{ __("common.description") }}') {
            const descDiv = el.querySelector('div:not(.text-muted)');
            if (descDiv) description = descDiv.textContent.trim();
        }
    });
    
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token }}');
    formData.append('_method', 'PUT');
    formData.append('name', displayName);
    formData.append('days', element.dataset.days || '0');
    formData.append('description', description);
    formData.append('is_active', activate ? '1' : '0');
    formData.append('is_default', element.dataset.isDefault || '0');
    
    fetch('{{ base_url }}/config/payment-terms/' + id, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
}

// Delete payment term
function deletePaymentTerm(id, name) {
    if (!confirm('{{ __("config.confirm_delete_payment_term") }}: ' + name + '?')) return;
    
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token }}');
    formData.append('_method', 'DELETE');
    
    fetch('{{ base_url }}/config/payment-terms/' + id, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
}

</script>
{% endblock %}