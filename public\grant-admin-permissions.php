<?php
/**
 * Grant all permissions to Administrators group
 * Run this script once to set up admin permissions
 */

// Load application bootstrap
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

try {
    $db = Flight::db();
    
    echo "<h3>Granting All Permissions to Administrators Group</h3>";
    
    // Start transaction
    $db->beginTransaction();
    
    // Get the Administrators group ID (usually 1)
    $groupStmt = $db->prepare("SELECT id, name FROM user_groups WHERE id = 1 OR LOWER(name) LIKE '%admin%' LIMIT 1");
    $groupStmt->execute();
    $adminGroup = $groupStmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$adminGroup) {
        throw new Exception("Administrators group not found!");
    }
    
    echo "Found group: " . $adminGroup['name'] . " (ID: " . $adminGroup['id'] . ")<br><br>";
    
    // First, clear existing permissions for admin group
    $deleteStmt = $db->prepare("DELETE FROM group_permissions WHERE group_id = ?");
    $deleteStmt->execute([$adminGroup['id']]);
    echo "Cleared existing permissions<br>";
    
    // Get all available permissions
    $permStmt = $db->prepare("SELECT id, code, name, category FROM permissions ORDER BY category, code");
    $permStmt->execute();
    $permissions = $permStmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($permissions) . " permissions to grant<br><br>";
    
    // Insert permissions for admin group
    $insertStmt = $db->prepare("
        INSERT INTO group_permissions (group_id, permission_id, granted_at, granted_by) 
        VALUES (?, ?, NOW(), 1)
    ");
    
    $grantedCount = 0;
    $categories = [];
    
    foreach ($permissions as $permission) {
        try {
            $insertStmt->execute([$adminGroup['id'], $permission['id']]);
            $grantedCount++;
            
            if (!isset($categories[$permission['category']])) {
                $categories[$permission['category']] = [];
            }
            $categories[$permission['category']][] = $permission['code'];
            
        } catch (Exception $e) {
            echo "Warning: Could not grant permission " . $permission['code'] . ": " . $e->getMessage() . "<br>";
        }
    }
    
    // Commit transaction
    $db->commit();
    
    echo "<h4>Successfully granted $grantedCount permissions!</h4>";
    
    // Display summary by category
    echo "<h4>Permissions Granted by Category:</h4>";
    echo "<ul>";
    foreach ($categories as $category => $perms) {
        echo "<li><strong>" . ucfirst($category) . ":</strong> " . count($perms) . " permissions";
        echo "<ul>";
        foreach ($perms as $perm) {
            echo "<li>" . $perm . "</li>";
        }
        echo "</ul></li>";
    }
    echo "</ul>";
    
    // Verify permissions were granted
    $verifyStmt = $db->prepare("
        SELECT COUNT(*) as count 
        FROM group_permissions 
        WHERE group_id = ?
    ");
    $verifyStmt->execute([$adminGroup['id']]);
    $result = $verifyStmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h4>Verification:</h4>";
    echo "Administrator group now has " . $result['count'] . " permissions<br>";
    
    echo "<br><div style='background: #d4edda; color: #155724; padding: 10px; border: 1px solid #c3e6cb; border-radius: 4px;'>";
    echo "<strong>✓ Success!</strong> Administrators now have full system access.<br>";
    echo "You can now log in as an administrator and manage permissions for other groups.";
    echo "</div>";
    
    // Security warning
    echo "<br><div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
    echo "<strong>⚠ Security Warning:</strong> Delete this file after running in production!";
    echo "</div>";
    
} catch (Exception $e) {
    if (isset($db) && $db->inTransaction()) {
        $db->rollBack();
    }
    
    echo "<div style='background: #f8d7da; color: #721c24; padding: 10px; border: 1px solid #f5c6cb; border-radius: 4px;'>";
    echo "<strong>Error:</strong> " . $e->getMessage();
    echo "</div>";
}
?>

<br>
<a href="/fit/public/admin/permissions" style="display: inline-block; margin-top: 20px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;">Go to Permission Management</a>