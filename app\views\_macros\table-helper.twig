{# Table Helper Macros #}

{# Main table wrapper with filters #}
{% macro tableWithFilters(config) %}
<div class="table-wrapper" data-table-id="{{ config.tableId|default('data-table') }}">
    {# Filters Card #}
    {% if config.filters is defined or config.showSearch|default(true) %}
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ config.formAction|default('') }}" class="row g-3">
                {# Custom filters #}
                {% if config.filters is defined %}
                    {% for filter in config.filters %}
                        <div class="col-md-{{ filter.width|default(3) }}">
                            <label for="{{ filter.id }}" class="form-label">{{ filter.label }}</label>
                            {% if filter.type == 'select' %}
                                <select class="form-select" id="{{ filter.id }}" name="{{ filter.name|default(filter.id) }}" 
                                        {% if filter.autoSubmit|default(true) %}onchange="this.form.submit()"{% endif %}>
                                    <option value="">{{ filter.placeholder|default('All') }}</option>
                                    {% for value, label in filter.options %}
                                        <option value="{{ value }}" {{ filter.value == value ? 'selected' : '' }}>{{ label }}</option>
                                    {% endfor %}
                                </select>
                            {% elseif filter.type == 'date' %}
                                <input type="date" class="form-control" id="{{ filter.id }}" 
                                       name="{{ filter.name|default(filter.id) }}" value="{{ filter.value }}"
                                       {% if filter.autoSubmit|default(true) %}onchange="this.form.submit()"{% endif %}>
                            {% else %}
                                <input type="text" class="form-control" id="{{ filter.id }}" 
                                       name="{{ filter.name|default(filter.id) }}" value="{{ filter.value }}"
                                       placeholder="{{ filter.placeholder|default('') }}">
                            {% endif %}
                        </div>
                    {% endfor %}
                {% endif %}
                
                {# Search field #}
                {% if config.showSearch|default(true) %}
                <div class="col-md-{{ config.searchWidth|default(3) }}">
                    <label for="search" class="form-label">{{ __('common.search') }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ config.searchValue|default('') }}" 
                               placeholder="{{ config.searchPlaceholder|default(__('common.search_placeholder')) }}"
                               data-dynamic-search="true">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                    </div>
                </div>
                {% endif %}
                
                {# Action buttons #}
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <button type="button" class="btn btn-secondary" onclick="clearTableFilters()">
                                <i class="bi bi-x-circle me-2"></i>{{ __('common.reset_filters') }}
                            </button>
                        </div>
                        
                        <div class="d-flex gap-2">
                            {# Import button #}
                            {% if config.showImport|default(true) and config.importUrl %}
                            <a href="{{ config.importUrl }}" class="btn btn-outline-primary">
                                <i class="bi bi-upload me-2"></i>{{ __('common.import') }}
                            </a>
                            {% endif %}
                            
                            {# Export dropdown #}
                            {% if config.showExport|default(true) %}
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-download me-2"></i>{{ __('common.export') }}
                                </button>
                                <ul class="dropdown-menu">
                                    {% for format in config.exportFormats|default(['csv', 'excel', 'pdf']) %}
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="tableHelper.exportData('{{ format }}')">
                                            {{ format|upper }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    {% endif %}
    
    {# Table Card #}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="{{ config.tableId|default('data-table') }}">
                    {{ config.tableContent|raw }}
                </table>
            </div>
            
            {# Bulk Actions and Pagination #}
            {% if config.showBulkActions|default(true) or config.pagination is defined %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                {# Bulk Actions #}
                {% if config.showBulkActions|default(true) and config.bulkActions is defined %}
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" 
                            disabled id="bulkActionsBtn">
                        {{ __('common.bulk_actions') }}
                    </button>
                    <ul class="dropdown-menu">
                        {% for action in config.bulkActions %}
                        <li>
                            <a class="dropdown-item {{ action.class|default('') }}" href="#" 
                               onclick="tableHelper.executeBulkAction('{{ action.action }}')">
                                <i class="{{ action.icon }} me-2"></i>{{ action.label }}
                            </a>
                        </li>
                        {% if action.divider|default(false) %}
                        <li><hr class="dropdown-divider"></li>
                        {% endif %}
                        {% endfor %}
                    </ul>
                </div>
                {% endif %}
                
                {# Pagination #}
                {% if config.pagination is defined %}
                    {{ include('_partials/pagination.twig', config.pagination) }}
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

{# Initialize table helper #}
<script>
let tableHelper;
document.addEventListener('DOMContentLoaded', function() {
    tableHelper = initTableHelper({
        tableId: '{{ config.tableId|default('data-table') }}',
        searchInputId: 'search',
        searchColumns: {{ config.searchColumns|default([])|json_encode }},
        filters: {{ config.filterConfigs|default([])|json_encode }},
        storageKey: '{{ config.storageKey|default('table_filters') }}',
        exportFormats: {{ config.exportFormats|default(['csv', 'excel', 'pdf'])|json_encode }},
        exportUrl: '{{ config.exportUrl|default('') }}',
        bulkActions: {{ config.bulkActionConfigs|default([])|json_encode }},
        bulkActionUrl: '{{ config.bulkActionUrl|default('') }}',
        translations: {
            search: '{{ __('common.search') }}',
            noResults: '{{ __('common.no_results') }}',
            bulkActions: '{{ __('common.bulk_actions') }}',
            export: '{{ __('common.export') }}',
            import: '{{ __('common.import') }}',
            reset: '{{ __('common.reset_filters') }}',
            results: '{{ __('common.results') }}',
            selected: '{{ __('common.selected') }}'
        }
    });
});

function clearTableFilters() {
    tableHelper.clearFilters();
    window.location.href = '{{ config.resetUrl|default('?reset_filters=1') }}';
}
</script>
{% endmacro %}

{# Table header with checkbox #}
{% macro tableHeader(columns, showCheckbox = true) %}
<thead>
    <tr>
        {% if showCheckbox %}
        <th width="30">
            <input type="checkbox" class="form-check-input" id="selectAll">
        </th>
        {% endif %}
        {% for column in columns %}
        <th class="{{ column.class|default('') }}" {% if column.width %}width="{{ column.width }}"{% endif %}>
            {{ column.label }}
        </th>
        {% endfor %}
    </tr>
</thead>
{% endmacro %}

{# Table row with checkbox #}
{% macro tableRow(data, columns, showCheckbox = true, rowId = null) %}
<tr data-id="{{ rowId }}">
    {% if showCheckbox %}
    <td>
        <input type="checkbox" class="form-check-input row-checkbox" value="{{ rowId }}">
    </td>
    {% endif %}
    {% for column in columns %}
    <td class="{{ column.class|default('') }}">
        {% if column.template is defined %}
            {{ include(column.template, { 'value': data[column.key], 'row': data }) }}
        {% else %}
            {{ data[column.key]|default('') }}
        {% endif %}
    </td>
    {% endfor %}
</tr>
{% endmacro %}

{# Empty state #}
{% macro emptyState(message, icon = 'bi-inbox', colspan = 10) %}
<tr>
    <td colspan="{{ colspan }}" class="text-center py-5">
        <div class="text-muted">
            <i class="{{ icon }} fs-1 d-block mb-3"></i>
            <p class="mb-0">{{ message }}</p>
        </div>
    </td>
</tr>
{% endmacro %}