<?php
/**
 * Translation Monitor - Utility to check and export missing translations
 * 
 * Usage: php translation_monitor.php [action]
 * Actions:
 *   - check: Display current missing translations
 *   - export: Export missing translations to language files
 *   - clear: Clear the missing translations log
 */

require_once __DIR__ . '/../../vendor/autoload.php';
require_once __DIR__ . '/../config/bootstrap.php';

use App\Helpers\Language;

// Get command line action
$action = $argv[1] ?? 'check';

switch ($action) {
    case 'check':
        checkMissingTranslations();
        break;
        
    case 'export':
        exportMissingTranslations();
        break;
        
    case 'clear':
        clearMissingTranslationsLog();
        break;
        
    default:
        echo "Unknown action: $action\n";
        echo "Available actions: check, export, clear\n";
        exit(1);
}

function checkMissingTranslations()
{
    echo "=== Missing Translations Report ===\n\n";
    
    // Read the log file
    $logFile = __DIR__ . '/../../storage/logs/missing_translations.log';
    
    if (!file_exists($logFile)) {
        echo "No missing translations log found.\n";
        return;
    }
    
    $lines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $stats = [];
    $byLanguage = [];
    
    foreach ($lines as $line) {
        // Parse log entry
        if (preg_match('/\[(.*?)\].*Language: (\w+), Key: ([\w\.]+), URL: (.*)/', $line, $matches)) {
            $timestamp = $matches[1];
            $language = $matches[2];
            $key = $matches[3];
            $url = $matches[4];
            
            // Group by language
            if (!isset($byLanguage[$language])) {
                $byLanguage[$language] = [];
            }
            
            if (!isset($byLanguage[$language][$key])) {
                $byLanguage[$language][$key] = [
                    'count' => 0,
                    'urls' => [],
                    'first_seen' => $timestamp,
                    'last_seen' => $timestamp
                ];
            }
            
            $byLanguage[$language][$key]['count']++;
            $byLanguage[$language][$key]['last_seen'] = $timestamp;
            
            if (!in_array($url, $byLanguage[$language][$key]['urls'])) {
                $byLanguage[$language][$key]['urls'][] = $url;
            }
        }
    }
    
    // Display statistics
    foreach ($byLanguage as $language => $keys) {
        echo "Language: $language\n";
        echo str_repeat('-', 50) . "\n";
        
        // Sort by frequency
        uasort($keys, function($a, $b) {
            return $b['count'] - $a['count'];
        });
        
        foreach ($keys as $key => $info) {
            echo sprintf(
                "  %s (used %d times)\n    First seen: %s\n    Last seen: %s\n    URLs: %s\n\n",
                $key,
                $info['count'],
                $info['first_seen'],
                $info['last_seen'],
                implode(', ', array_slice($info['urls'], 0, 3)) . (count($info['urls']) > 3 ? '...' : '')
            );
        }
        
        echo "\nTotal missing keys for $language: " . count($keys) . "\n\n";
    }
}

function exportMissingTranslations()
{
    echo "=== Exporting Missing Translations ===\n\n";
    
    $missingTranslations = Language::exportMissingTranslations();
    
    if (empty($missingTranslations)) {
        echo "No missing translations to export.\n";
        return;
    }
    
    $exportDir = __DIR__ . '/../../storage/missing_translations/';
    if (!is_dir($exportDir)) {
        mkdir($exportDir, 0777, true);
    }
    
    foreach ($missingTranslations as $language => $groups) {
        $langDir = $exportDir . $language;
        if (!is_dir($langDir)) {
            mkdir($langDir, 0777, true);
        }
        
        foreach ($groups as $group => $translations) {
            $file = $langDir . '/' . $group . '_missing.php';
            
            $content = "<?php\n\n";
            $content .= "/**\n";
            $content .= " * Missing translations for $group group\n";
            $content .= " * Generated on " . date('Y-m-d H:i:s') . "\n";
            $content .= " * \n";
            $content .= " * Copy the translations you want to add to the main language file\n";
            $content .= " */\n\n";
            $content .= "return [\n";
            
            foreach ($translations as $key => $value) {
                $content .= "    '" . $key . "' => '" . addslashes($value) . "',\n";
            }
            
            $content .= "];\n";
            
            file_put_contents($file, $content);
            echo "Exported $language/$group to $file\n";
        }
    }
    
    echo "\nExport complete! Check the storage/missing_translations directory.\n";
}

function clearMissingTranslationsLog()
{
    $logFile = __DIR__ . '/../../storage/logs/missing_translations.log';
    
    if (file_exists($logFile)) {
        unlink($logFile);
        echo "Missing translations log cleared.\n";
    } else {
        echo "No log file to clear.\n";
    }
}