<!DOCTYPE html>
<html lang="{{ app.language|default('fr') }}" data-bs-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{{ title|default('Dashboard') }}{% endblock %} - {{ app_name }}</title>
    <meta name="author" content="{{ app_name }}">
    <meta name="description" content="{{ app_name }} - Modern Health Center Billing System">
    <link rel="icon" type="image/x-icon" href="{{ base_url }}/favicon.ico">
    <meta name="csrf-token" content="{{ csrf_token|default('') }}">
    
    <!-- Google Fonts - Inter -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- Font Awesome 6 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    
    <!-- SweetAlert2 & Toastr -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- Custom Modern Theme CSS -->
    <style>
        :root {
            --sidebar-width: 280px;
            --navbar-height: 60px;
            --sidebar-bg: #1e293b;
            --sidebar-text: #cbd5e1;
            --sidebar-hover: #334155;
            --sidebar-active: #6366f1;
        }
        
        /* Full height layout */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            font-size: 0.875rem;
        }
        
        /* App wrapper for full height */
        .app-wrapper {
            display: flex;
            height: 100vh;
            overflow: hidden;
        }
        
        /* Sidebar - Full height */
        .app-sidebar {
            width: var(--sidebar-width);
            background: var(--sidebar-bg);
            display: flex;
            flex-direction: column;
            flex-shrink: 0;
            transition: width 0.3s ease;
            overflow: hidden;
        }
        
        .app-sidebar.collapsed {
            width: 60px;
        }
        
        /* Sidebar Brand */
        .sidebar-brand {
            padding: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(0, 0, 0, 0.1);
        }
        
        .brand-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: var(--sidebar-text);
            font-weight: 600;
            font-size: 1.125rem;
        }
        
        /* Sidebar Navigation - Scrollable */
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 0.5rem 0;
        }
        
        /* Custom scrollbar for sidebar */
        .sidebar-nav::-webkit-scrollbar {
            width: 6px;
        }
        
        .sidebar-nav::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }
        
        .sidebar-nav::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }
        
        /* Menu items */
        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .nav-item {
            margin-bottom: 0.125rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: var(--sidebar-text);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            position: relative;
        }
        
        .nav-link:hover {
            background: var(--sidebar-hover);
            color: white;
        }
        
        .nav-link.active {
            background: var(--sidebar-hover);
            color: white;
            border-left-color: var(--sidebar-active);
        }
        
        .nav-icon {
            width: 20px;
            font-size: 1.125rem;
            text-align: center;
            flex-shrink: 0;
        }
        
        /* Submenu */
        .nav-treeview {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .nav-treeview.show {
            max-height: 500px;
        }
        
        .nav-treeview .nav-link {
            padding-left: 3rem;
            font-size: 0.8125rem;
        }
        
        /* Main content area */
        .app-main {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        /* Navbar */
        .app-header {
            height: var(--navbar-height);
            background: white;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            padding: 0 1.5rem;
            flex-shrink: 0;
        }
        
        /* Content area - scrollable */
        .app-content {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            background: #f8fafc;
        }
        
        /* Collapsed sidebar adjustments */
        .app-sidebar.collapsed .brand-text,
        .app-sidebar.collapsed .nav-link p,
        .app-sidebar.collapsed .nav-arrow {
            display: none;
        }
        
        .app-sidebar.collapsed .nav-link {
            justify-content: center;
        }
        
        /* Badge for menu items */
        .nav-link .badge {
            margin-left: auto;
        }
        
        /* Disabled menu items */
        .nav-link.disabled {
            opacity: 0.5;
            pointer-events: none;
        }
    </style>
    
    {% block styles %}{% endblock %}
</head>
<body>
    <div class="app-wrapper">
        <!-- Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Brand -->
            <div class="sidebar-brand">
                <a href="{{ base_url }}/" class="brand-link">
                    <i class="bi bi-heart-pulse-fill text-primary fs-4"></i>
                    <span class="brand-text">{{ app_name }}</span>
                </a>
            </div>
            
            <!-- Sidebar Navigation -->
            <nav class="sidebar-nav">
                <ul class="sidebar-menu">
                    {% set menuConfig = getMenuConfig() %}
                    
                    <!-- Dashboard - Always visible -->
                    <li class="nav-item">
                        <a href="{{ base_url }}/" class="nav-link {{ current_route == 'home' ? 'active' : '' }}">
                            <i class="nav-icon bi bi-speedometer2"></i>
                            <p>{{ __('common.dashboard') }}</p>
                        </a>
                    </li>
                    
                    <!-- Dynamic Menu Items -->
                    {% for item in menuConfig %}
                        {% if item.enabled or (session.user and session.user.is_admin) %}
                            <li class="nav-item">
                                {% if item.submenu %}
                                    <a href="#" class="nav-link" onclick="toggleSubmenu('{{ item.id }}'); return false;">
                                        <i class="nav-icon {{ item.icon }}"></i>
                                        <p>{{ __(item.title) }}</p>
                                        <i class="nav-arrow bi bi-chevron-right ms-auto"></i>
                                    </a>
                                    <ul class="nav nav-treeview" id="{{ item.id }}-menu">
                                        {% for subitem in item.submenu %}
                                            {% if subitem.enabled or (session.user and session.user.is_admin) %}
                                                <li class="nav-item">
                                                    <a href="{{ base_url }}{{ subitem.url }}" class="nav-link {{ not subitem.enabled and not session.user.is_admin ? 'disabled' : '' }}">
                                                        <i class="nav-icon bi bi-circle"></i>
                                                        <p>{{ __(subitem.title) }}</p>
                                                    </a>
                                                </li>
                                            {% endif %}
                                        {% endfor %}
                                    </ul>
                                {% else %}
                                    <a href="{{ base_url }}{{ item.url }}" class="nav-link {{ current_route starts with item.route ? 'active' : '' }} {{ not item.enabled and not session.user.is_admin ? 'disabled' : '' }}">
                                        <i class="nav-icon {{ item.icon }}"></i>
                                        <p>{{ __(item.title) }}</p>
                                        {% if item.badge %}
                                            <span class="badge bg-{{ item.badge.color }} ms-auto">{{ item.badge.count }}</span>
                                        {% endif %}
                                    </a>
                                {% endif %}
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    <!-- Admin Menu - Only for admins -->
                    {% if session.user and session.user.is_admin %}
                        <li class="nav-item mt-3">
                            <a href="#" class="nav-link" onclick="toggleSubmenu('admin'); return false;">
                                <i class="nav-icon bi bi-shield-lock"></i>
                                <p>{{ __('admin.admin_tools') }}</p>
                                <i class="nav-arrow bi bi-chevron-right ms-auto"></i>
                            </a>
                            <ul class="nav nav-treeview" id="admin-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/admin/menu-config" class="nav-link">
                                        <i class="nav-icon bi bi-menu-button-wide"></i>
                                        <p>{{ __('admin.menu_configuration') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/admin/permissions" class="nav-link">
                                        <i class="nav-icon bi bi-key"></i>
                                        <p>{{ __('admin.permissions') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content Area -->
        <div class="app-main">
            <!-- Header/Navbar -->
            <header class="app-header">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <!-- Left side -->
                    <div class="d-flex align-items-center">
                        <!-- Sidebar Toggle -->
                        <button class="btn btn-link text-dark p-2" onclick="toggleSidebar()">
                            <i class="bi bi-list fs-4"></i>
                        </button>
                        
                        <!-- Page Title (Mobile) -->
                        <h5 class="mb-0 d-md-none">{{ title|default('Dashboard') }}</h5>
                    </div>
                    
                    <!-- Right side -->
                    <div class="d-flex align-items-center gap-3">
                        <!-- Theme Toggle -->
                        <button class="btn btn-link text-dark p-2" onclick="toggleTheme()">
                            <i class="bi bi-sun-fill" id="theme-icon"></i>
                        </button>
                        
                        <!-- User Menu -->
                        <div class="dropdown">
                            <button class="btn btn-link text-dark dropdown-toggle d-flex align-items-center gap-2" 
                                    data-bs-toggle="dropdown">
                                <span class="d-none d-md-inline">{{ session.user_name|default('User') }}</span>
                                <i class="bi bi-person-circle fs-4"></i>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="{{ base_url }}/profile">
                                    <i class="bi bi-person me-2"></i>{{ __('users.my_profile') }}
                                </a></li>
                                <li><a class="dropdown-item" href="{{ base_url }}/settings">
                                    <i class="bi bi-gear me-2"></i>{{ __('common.settings') }}
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="{{ base_url }}/logout">
                                    <i class="bi bi-box-arrow-right me-2"></i>{{ __('auth.logout') }}
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Content Area -->
            <main class="app-content">
                <div class="container-fluid">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    
    <script>
        // Configure toastr
        toastr.options = {
            closeButton: true,
            progressBar: true,
            positionClass: "toast-top-right",
            timeOut: "3000"
        };
        
        // Toggle Sidebar
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('collapsed');
            localStorage.setItem('sidebarCollapsed', document.getElementById('sidebar').classList.contains('collapsed'));
        }
        
        // Toggle Submenu
        function toggleSubmenu(menuId) {
            const submenu = document.getElementById(menuId + '-menu');
            submenu.classList.toggle('show');
            
            // Rotate arrow
            const arrow = event.currentTarget.querySelector('.nav-arrow');
            if (arrow) {
                arrow.style.transform = submenu.classList.contains('show') ? 'rotate(90deg)' : '';
            }
        }
        
        // Toggle Theme
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Update icon
            const icon = document.getElementById('theme-icon');
            icon.className = newTheme === 'dark' ? 'bi bi-moon-fill' : 'bi bi-sun-fill';
        }
        
        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            // Restore sidebar state
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                document.getElementById('sidebar').classList.add('collapsed');
            }
            
            // Restore theme
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
            document.getElementById('theme-icon').className = savedTheme === 'dark' ? 'bi bi-moon-fill' : 'bi bi-sun-fill';
        });
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>