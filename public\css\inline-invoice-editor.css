/**
 * Inline Invoice Editor Styles
 * Provides visual feedback for editable invoice line fields
 */

/* Base editable field styling */
.editable-field {
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
    padding: 4px 6px;
    border-radius: 4px;
    min-height: 32px;
}

/* Hover state - show field is editable */
.editable-field:hover {
    background-color: #e3f2fd;
    box-shadow: inset 0 0 0 1px #90caf9;
}

/* Edit icon on hover */
.editable-field:hover::after {
    content: "✏️";
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    opacity: 0.6;
}

/* Active editing state */
.editable-field.editing {
    background-color: #fff3cd;
    box-shadow: inset 0 0 0 2px #ffc107;
    padding: 2px 4px;
}

.editable-field.editing::after {
    display: none;
}

/* Saving state */
.editable-field.saving {
    background-color: #e8f5e9;
    position: relative;
    overflow: hidden;
}

.editable-field.saving::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(76, 175, 80, 0.3), transparent);
    animation: saving-pulse 1s infinite;
}

@keyframes saving-pulse {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Success state */
.editable-field.saved {
    background-color: #c8e6c9;
    animation: save-success 0.5s ease;
}

@keyframes save-success {
    0% { background-color: #4caf50; }
    100% { background-color: #c8e6c9; }
}

/* Error state */
.editable-field.error {
    background-color: #ffcdd2;
    box-shadow: inset 0 0 0 2px #f44336;
}

/* Modified indicator */
.editable-field.modified {
    position: relative;
}

.editable-field.modified::before {
    content: "•";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff9800;
    font-size: 16px;
}

/* Inline editing input */
.inline-edit-input {
    width: 100%;
    border: none;
    background: transparent;
    font-family: inherit;
    font-size: inherit;
    padding: 2px 4px;
    outline: none;
}

/* Error message */
.inline-edit-error {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 12px;
    color: #f44336;
    white-space: nowrap;
    z-index: 10;
    background: white;
    padding: 2px 6px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .editable-field {
        min-height: 44px; /* Touch-friendly size */
        padding: 8px 10px;
    }
    
    .editable-field:hover::after {
        font-size: 16px;
        right: 8px;
    }
    
    .inline-edit-error {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        max-width: 90%;
        white-space: normal;
    }
}

/* Disable editing on sent/paid invoices */
.invoice-sent .editable-field,
.invoice-paid .editable-field {
    cursor: not-allowed;
    opacity: 0.7;
}

.invoice-sent .editable-field:hover,
.invoice-paid .editable-field:hover {
    background-color: transparent;
    box-shadow: none;
}

.invoice-sent .editable-field:hover::after,
.invoice-paid .editable-field:hover::after {
    display: none;
}

/* Loading spinner for save operations */
.save-spinner {
    display: inline-block;
    width: 14px;
    height: 14px;
    border: 2px solid rgba(0,0,0,0.1);
    border-top-color: #2196f3;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
    margin-left: 8px;
    vertical-align: middle;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success checkmark */
.save-success-icon {
    color: #4caf50;
    margin-left: 8px;
    animation: check-appear 0.3s ease;
}

@keyframes check-appear {
    0% { 
        transform: scale(0);
        opacity: 0;
    }
    50% { transform: scale(1.2); }
    100% { 
        transform: scale(1);
        opacity: 1;
    }
}