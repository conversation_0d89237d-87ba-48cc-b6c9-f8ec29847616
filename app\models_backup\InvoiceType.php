<?php

namespace App\Models;

use App\Core\Model;

class InvoiceType extends Model
{
    protected $table = 'invoice_types';
    
    protected $fillable = [
        'name',
        'code',
        'prefix',
        'description',
        'color',
        'order',
        'is_active'
    ];
    
    protected $casts = [
        'order' => 'integer',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get active invoice types
     */
    public static function getActive()
    {
        $db = \Flight::db();
        $stmt = $db->prepare("
            SELECT * FROM config_invoice_types 
            WHERE is_active = 1 
            ORDER BY id ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get invoice type by code
     */
    public static function getByCode($code)
    {
        return self::where('code', $code)->first();
    }
    
    /**
     * Reorder invoice types
     */
    public static function reorder($ids)
    {
        foreach ($ids as $order => $id) {
            self::where('id', $id)->update(['order' => $order]);
        }
    }
}