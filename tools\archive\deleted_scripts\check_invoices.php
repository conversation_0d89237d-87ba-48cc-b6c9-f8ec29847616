<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CHECKING ALL INVOICES ===\n\n";
    
    // Count total invoices
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM invoices");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total invoices in database: {$result['total']}\n\n";
    
    // Get latest invoices
    echo "Latest 10 invoices:\n";
    echo str_repeat("-", 120) . "\n";
    echo str_pad("ID", 5) . str_pad("Invoice Number", 20) . str_pad("Status", 15) . str_pad("Date", 12) . str_pad("Client", 30) . str_pad("Total", 10) . "Archived\n";
    echo str_repeat("-", 120) . "\n";
    
    $stmt = $pdo->query("
        SELECT 
            i.id,
            i.invoice_number,
            i.status,
            i.issue_date,
            i.total,
            i.is_archived,
            i.client_id,
            i.user_id,
            i.billable_type,
            i.billable_id,
            c.company_name as client_name,
            CONCAT(u.first_name, ' ', u.last_name) as user_name
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id AND i.billable_type = 'client'
        LEFT JOIN users u ON i.user_id = u.id AND i.billable_type = 'user'
        ORDER BY i.id DESC
        LIMIT 10
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        // Determine the billable name
        $billableName = '-';
        if ($row['billable_type'] === 'client' && $row['client_name']) {
            $billableName = $row['client_name'];
        } elseif ($row['billable_type'] === 'user' && $row['user_name']) {
            $billableName = $row['user_name'];
        } elseif ($row['client_name']) {
            $billableName = $row['client_name'];
        } elseif ($row['user_name']) {
            $billableName = $row['user_name'];
        }
        
        echo str_pad($row['id'], 5) . 
             str_pad($row['invoice_number'] ?: '-', 20) . 
             str_pad($row['status'] ?: 'draft', 15) . 
             str_pad($row['issue_date'], 12) . 
             str_pad(substr($billableName, 0, 30), 30) . 
             str_pad(number_format($row['total'], 2), 10) . 
             ($row['is_archived'] ? 'Yes' : 'No') . "\n";
    }
    
    // Check for archived invoices
    echo "\n\nArchived invoices:\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM invoices WHERE is_archived = 1");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total archived: {$result['count']}\n";
    
    // Check invoice status distribution
    echo "\nInvoice status distribution:\n";
    $stmt = $pdo->query("
        SELECT status, COUNT(*) as count 
        FROM invoices 
        GROUP BY status 
        ORDER BY count DESC
    ");
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $status = $row['status'] ?: '(empty/draft)';
        echo "- {$status}: {$row['count']}\n";
    }
    
    // Check the latest invoice details
    echo "\n\nLatest invoice details:\n";
    $stmt = $pdo->query("SELECT * FROM invoices ORDER BY id DESC LIMIT 1");
    $latest = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($latest) {
        foreach ($latest as $key => $value) {
            if ($value !== null && $value !== '') {
                echo "- {$key}: {$value}\n";
            }
        }
    } else {
        echo "No invoices found!\n";
    }
    
    // Check if there's a filter in the session
    echo "\n\nChecking for potential issues:\n";
    
    // Check document types
    $stmt = $pdo->query("SELECT id, code, name FROM document_types WHERE is_active = 1");
    echo "\nActive document types:\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "- ID: {$row['id']}, Code: {$row['code']}\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}