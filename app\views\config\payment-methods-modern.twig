{% extends "base-modern.twig" %}

{% block title %}{{ __('config.payment_methods') }}{% endblock %}

{% block head_extra %}
<style>
.status-toggle {
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
}
.status-toggle:hover {
    opacity: 0.8;
}
.status-toggle i {
    transition: transform 0.2s ease;
}
.status-toggle:hover i {
    transform: scale(1.2);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.payment_methods') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentMethodModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_payment_method') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.payment_methods_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Current Active Payment Methods -->
    <div class="mb-5">
        <h4 class="mb-3">{{ __('config.current_active_payment_methods') }}</h4>
        <div class="row g-4">
            {% set hasActive = false %}
            {% for method in paymentMethods|filter(method => method.is_active) %}
                {% set hasActive = true %}
                <div class="col-lg-4 col-md-6" data-id="{{ method.id }}"
                     data-name="{{ method.display_name|e('html_attr') }}"
                     data-code="{{ method.code }}">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-credit-card me-2"></i>{{ method.display_name }}
                                </h5>
                                <div class="d-flex gap-2 align-items-center">
                                    <span class="badge bg-white text-dark">{{ method.code }}</span>
                                    <span class="status-toggle" 
                                          onclick="toggleStatus({{ method.id }}, false)"
                                          title="{{ __('config.click_to_deactivate') }}"
                                          style="cursor: pointer;">
                                        <i class="bi bi-eye-fill text-white fs-5"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <dl class="row mb-0">
                                <dt class="col-6">{{ __('common.code') }}:</dt>
                                <dd class="col-6"><code>{{ method.code }}</code></dd>
                                
                                <dt class="col-6">{{ __('common.created_at') }}:</dt>
                                <dd class="col-6">{{ method.created_at|date('d/m/Y') }}</dd>
                            </dl>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="editPaymentMethod({{ method.id }})">
                                    <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deletePaymentMethod({{ method.id }})">
                                    <i class="bi bi-trash me-1"></i>{{ __('common.delete') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
            {% if not hasActive %}
                <div class="col-12">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('config.no_active_payment_methods') }}
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Inactive Payment Methods -->
    {% set hasInactive = false %}
    {% for method in paymentMethods %}
        {% if not method.is_active %}
            {% set hasInactive = true %}
        {% endif %}
    {% endfor %}
    
    {% if hasInactive %}
    <div class="mb-5">
        <h4 class="mb-3">{{ __('config.inactive_payment_methods') }}</h4>
        <div class="row g-4">
            {% for method in paymentMethods %}
                {% if not method.is_active %}
                <div class="col-lg-4 col-md-6" data-id="{{ method.id }}"
                     data-name="{{ method.display_name|e('html_attr') }}"
                     data-code="{{ method.code }}">
                    <div class="card h-100 border-secondary opacity-75">
                        <div class="card-header bg-secondary text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="bi bi-credit-card me-2"></i>{{ method.display_name }}
                                </h5>
                                <div class="d-flex gap-2 align-items-center">
                                    <span class="badge bg-white text-dark">{{ method.code }}</span>
                                    <span class="status-toggle" 
                                          onclick="toggleStatus({{ method.id }}, true)"
                                          title="{{ __('config.click_to_activate') }}"
                                          style="cursor: pointer;">
                                        <i class="bi bi-eye-slash-fill text-white fs-5"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <dl class="row mb-0">
                                <dt class="col-6">{{ __('common.code') }}:</dt>
                                <dd class="col-6"><code>{{ method.code }}</code></dd>
                                
                                <dt class="col-6">{{ __('common.created_at') }}:</dt>
                                <dd class="col-6">{{ method.created_at|date('d/m/Y') }}</dd>
                            </dl>
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="editPaymentMethod({{ method.id }})">
                                    <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deletePaymentMethod({{ method.id }})">
                                    <i class="bi bi-trash me-1"></i>{{ __('common.delete') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Add/Edit Payment Method Modal -->
<div class="modal fade" id="paymentMethodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="paymentMethodForm" method="POST" action="{{ base_url }}/config/payment-methods">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" id="method_id" name="id" value="">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">{{ __('config.add_payment_method') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="method_name" class="form-label">{{ __('common.name') }} *</label>
                        <input type="text" class="form-control" id="method_name" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="method_code" class="form-label">{{ __('common.code') }} *</label>
                        <input type="text" class="form-control" id="method_code" name="code" required pattern="[A-Z0-9_]+" maxlength="20">
                        <small class="text-muted">{{ __('config.code_format_uppercase') }}</small>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="method_active" name="is_active" value="1" checked>
                        <label class="form-check-label" for="method_active">
                            {{ __('common.active') }}
                        </label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Store payment methods data for client-side use
const paymentMethods = {{ paymentMethods|json_encode|raw }};

// Edit payment method
function editPaymentMethod(id) {
    const method = paymentMethods.find(m => m.id === id);
    if (!method) return;
    
    document.getElementById('method_id').value = method.id;
    document.getElementById('method_name').value = method.display_name;
    document.getElementById('method_code').value = method.code;
    document.getElementById('method_code').readOnly = true; // Don't allow changing code
    document.getElementById('method_active').checked = method.is_active === 1;
    
    document.getElementById('modalTitle').textContent = '{{ __("config.edit_payment_method") }}';
    document.getElementById('paymentMethodForm').action = '{{ base_url }}/config/payment-methods/' + id;
    
    // Add method override for PUT
    if (!document.querySelector('input[name="_method"]')) {
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'PUT';
        document.getElementById('paymentMethodForm').appendChild(methodInput);
    }
    
    const modal = new bootstrap.Modal(document.getElementById('paymentMethodModal'));
    modal.show();
}

// Delete payment method
function deletePaymentMethod(id) {
    const method = paymentMethods.find(m => m.id === id);
    if (!method) return;
    
    if (!confirm('{{ __("config.confirm_delete") }}: ' + method.display_name + '?')) {
        return;
    }
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ base_url }}/config/payment-methods/' + id;
    
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token }}';
    form.appendChild(csrfInput);
    
    const methodInput = document.createElement('input');
    methodInput.type = 'hidden';
    methodInput.name = '_method';
    methodInput.value = 'DELETE';
    form.appendChild(methodInput);
    
    document.body.appendChild(form);
    form.submit();
}

// Reset form when modal is closed
document.getElementById('paymentMethodModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('paymentMethodForm').reset();
    document.getElementById('method_id').value = '';
    document.getElementById('method_code').readOnly = false;
    document.getElementById('modalTitle').textContent = '{{ __("config.add_payment_method") }}';
    
    // Remove method override input
    const methodInput = document.querySelector('input[name="_method"]');
    if (methodInput) methodInput.remove();
    
    document.getElementById('paymentMethodForm').action = '{{ base_url }}/config/payment-methods';
});

// Auto-uppercase code field
document.getElementById('method_code').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9_]/g, '');
});

// Toggle status
function toggleStatus(id, activate) {
    if (!confirm('{{ __("config.confirm_status_change") }}')) return;
    
    // Get the payment method from our data
    const method = paymentMethods.find(m => m.id === id);
    if (!method) {
        alert('Payment method not found in data');
        return;
    }
    
    console.log('Toggle status for method:', method);
    console.log('Using display_name:', method.display_name);
    
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token }}');
    formData.append('_method', 'PUT');
    formData.append('name', method.display_name);
    formData.append('code', method.code);
    formData.append('is_active', activate ? '1' : '0');
    
    // Debug: Log all form data
    console.log('Sending form data:');
    for (let [key, value] of formData.entries()) {
        console.log(key + ':', value);
    }
    
    fetch('{{ base_url }}/config/payment-methods/' + id, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
}
</script>
{% endblock %}

{% block scripts %}
<script>
// Payment method form handler using our working pattern
document.getElementById('paymentMethodForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const methodId = formData.get('id');
    const isEdit = methodId && methodId !== '';
    
    // Ensure checkbox values
    if (!formData.has('is_active')) {
        formData.append('is_active', '0');
    }
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message || (isEdit ? '{{ __("config.payment_method_updated") }}' : '{{ __("config.payment_method_created") }}'));
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            toastr.error(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('{{ __("common.error_occurred") }}');
    });
});
</script>
{% endblock %}