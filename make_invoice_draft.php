<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\Invoice;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceNumber = $_GET['invoice'] ?? $argv[1] ?? null;
    
    if (!$invoiceNumber) {
        echo "Usage: php make_invoice_draft.php [invoice_number]\n";
        echo "Or access via browser: make_invoice_draft.php?invoice=FAC-RET25-2025-0198\n";
        exit;
    }
    
    echo "=== Converting Invoice to Draft ===\n\n";
    
    // Get the invoice
    $stmt = $pdo->prepare("
        SELECT i.*, it.name as invoice_type_name 
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
        WHERE i.invoice_number = ?
    ");
    $stmt->execute([$invoiceNumber]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "Invoice '$invoiceNumber' not found.\n";
        exit;
    }
    
    echo "Found invoice:\n";
    echo "- Number: " . $invoice['invoice_number'] . "\n";
    echo "- Type: " . ($invoice['invoice_type_name'] ?? 'Standard') . "\n";
    echo "- Current Status: " . $invoice['status'] . "\n";
    echo "- Total: €" . number_format($invoice['total'], 2) . "\n";
    echo "- Client ID: " . $invoice['client_id'] . "\n";
    
    // Check if already draft
    if ($invoice['status'] === Invoice::STATUS_DRAFT) {
        echo "\n✓ Invoice is already a draft.\n";
        exit;
    }
    
    // Check for payments
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as payment_count, COALESCE(SUM(pa.amount), 0) as total_paid
        FROM payment_allocations pa
        JOIN payments p ON pa.payment_id = p.id
        WHERE pa.invoice_id = ?
    ");
    $stmt->execute([$invoice['id']]);
    $paymentInfo = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($paymentInfo['payment_count'] > 0) {
        echo "\n⚠️  WARNING: This invoice has " . $paymentInfo['payment_count'] . " payment(s) totaling €" . 
             number_format($paymentInfo['total_paid'], 2) . "\n";
        echo "Converting to draft will remove the payment associations.\n";
        
        if (php_sapi_name() === 'cli') {
            echo "Continue? (y/n): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            if (trim($line) != 'y') {
                echo "Cancelled.\n";
                exit;
            }
            fclose($handle);
        }
    }
    
    echo "\nConverting to draft...\n";
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Remove payment allocations if any
        if ($paymentInfo['payment_count'] > 0) {
            $stmt = $pdo->prepare("DELETE FROM payment_allocations WHERE invoice_id = ?");
            $stmt->execute([$invoice['id']]);
            echo "- Removed payment allocations\n";
        }
        
        // Update invoice status to draft
        $stmt = $pdo->prepare("
            UPDATE invoices 
            SET status = ?,
                paid_amount = 0,
                payment_status = NULL,
                sent_at = NULL,
                updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([Invoice::STATUS_DRAFT, $invoice['id']]);
        echo "- Updated invoice status to draft\n";
        
        // Log the action
        $stmt = $pdo->prepare("
            INSERT INTO activity_log (user_id, action, model_type, model_id, description, ip_address, created_at)
            VALUES (?, 'convert_to_draft', 'invoice', ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $_SESSION['user_id'] ?? 1,
            $invoice['id'],
            "Converted invoice {$invoice['invoice_number']} from {$invoice['status']} to draft",
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
        ]);
        echo "- Logged activity\n";
        
        $pdo->commit();
        
        echo "\n✓ Successfully converted invoice '{$invoiceNumber}' to draft status!\n";
        echo "\nYou can now:\n";
        echo "- Edit the invoice: http://localhost/fit/public/invoices/" . $invoice['id'] . "/edit\n";
        echo "- View the invoice: http://localhost/fit/public/invoices/" . $invoice['id'] . "\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "\n❌ Error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}