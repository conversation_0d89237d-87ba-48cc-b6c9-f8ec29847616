/**
 * Enhanced Dashboard with Loading States and Error Handling
 * Fit360 AdminDesk
 */

(function() {
    'use strict';

    const DashboardEnhanced = {
        // Configuration
        config: {
            refreshInterval: 5 * 60 * 1000, // 5 minutes
            apiTimeout: 30000, // 30 seconds
            retryAttempts: 3,
            retryDelay: 1000
        },

        // State management
        state: {
            isLoading: false,
            autoRefresh: false,
            refreshTimer: null,
            loadingComponents: new Set()
        },

        // Initialize dashboard
        init: function() {
            this.bindEvents();
            this.initializeComponents();
            this.setupAutoRefresh();
            this.checkInitialState();
        },

        // Bind event handlers
        bindEvents: function() {
            // Refresh button
            const refreshBtn = document.getElementById('refresh-dashboard');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => this.refreshDashboard());
            }

            // Auto-refresh toggle
            const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
            if (autoRefreshToggle) {
                autoRefreshToggle.addEventListener('change', (e) => {
                    this.state.autoRefresh = e.target.checked;
                    if (this.state.autoRefresh) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                });
            }

            // Pull to refresh (mobile)
            if ('ontouchstart' in window) {
                this.setupPullToRefresh();
            }

            // Network status
            window.addEventListener('online', () => this.handleOnline());
            window.addEventListener('offline', () => this.handleOffline());
        },

        // Initialize dashboard components
        initializeComponents: function() {
            // Show initial loading state
            this.showSkeletonLoaders();

            // Load each component
            Promise.all([
                this.loadStats(),
                this.loadRecentInvoices(),
                this.loadCharts()
            ]).then(() => {
                this.hideSkeletonLoaders();
                this.showSuccessToast('Dashboard loaded successfully');
            }).catch(error => {
                this.hideSkeletonLoaders();
                this.showError('Failed to load dashboard', error);
            });
        },

        // Load statistics
        loadStats: async function() {
            const statsContainer = document.querySelector('.dashboard-stats');
            if (!statsContainer) return;

            this.state.loadingComponents.add('stats');
            
            try {
                const stats = await this.fetchWithRetry('/fit/public/api/dashboard/stats');
                this.renderStats(stats);
            } catch (error) {
                this.showComponentError(statsContainer, 'Failed to load statistics');
                throw error;
            } finally {
                this.state.loadingComponents.delete('stats');
            }
        },

        // Load recent invoices
        loadRecentInvoices: async function() {
            const invoicesContainer = document.querySelector('.recent-invoices-container');
            if (!invoicesContainer) return;

            this.state.loadingComponents.add('invoices');

            try {
                const invoices = await this.fetchWithRetry('/fit/public/api/dashboard/recent-invoices');
                this.renderRecentInvoices(invoices);
            } catch (error) {
                this.showComponentError(invoicesContainer, 'Failed to load recent invoices');
                throw error;
            } finally {
                this.state.loadingComponents.delete('invoices');
            }
        },

        // Load charts
        loadCharts: async function() {
            const chartsContainer = document.querySelector('.dashboard-charts');
            if (!chartsContainer) return;

            this.state.loadingComponents.add('charts');

            try {
                const chartData = await this.fetchWithRetry('/fit/public/api/dashboard/revenue-chart');
                this.renderCharts(chartData);
            } catch (error) {
                this.showComponentError(chartsContainer, 'Failed to load charts');
                throw error;
            } finally {
                this.state.loadingComponents.delete('charts');
            }
        },

        // Fetch with retry logic
        fetchWithRetry: async function(url, options = {}, attempts = this.config.retryAttempts) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.apiTimeout);

            try {
                const response = await fetch(url, {
                    ...options,
                    signal: controller.signal,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        ...options.headers
                    }
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                clearTimeout(timeoutId);

                if (attempts > 1 && !error.name.includes('Abort')) {
                    // Wait before retrying
                    await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
                    return this.fetchWithRetry(url, options, attempts - 1);
                }

                throw error;
            }
        },

        // Show skeleton loaders
        showSkeletonLoaders: function() {
            // Stats skeleton
            const statsContainer = document.querySelector('.dashboard-stats');
            if (statsContainer) {
                statsContainer.innerHTML = this.generateStatsSkeleton();
            }

            // Table skeleton
            const tableContainer = document.querySelector('.recent-invoices-table');
            if (tableContainer) {
                tableContainer.innerHTML = this.generateTableSkeleton();
            }

            // Chart skeleton
            const chartsContainer = document.querySelector('.dashboard-charts');
            if (chartsContainer) {
                chartsContainer.innerHTML = this.generateChartSkeleton();
            }
        },

        // Hide skeleton loaders
        hideSkeletonLoaders: function() {
            document.querySelectorAll('.skeleton-loader').forEach(el => {
                el.classList.add('fade-in');
            });
        },

        // Generate stats skeleton HTML
        generateStatsSkeleton: function() {
            let html = '';
            for (let i = 0; i < 4; i++) {
                html += `
                    <div class="col-xl-3 col-md-6">
                        <div class="stat-card-skeleton skeleton-loader">
                            <div class="skeleton-title"></div>
                            <div class="skeleton-value"></div>
                        </div>
                    </div>
                `;
            }
            return html;
        },

        // Generate table skeleton HTML
        generateTableSkeleton: function() {
            let html = '<div class="table-skeleton">';
            for (let i = 0; i < 5; i++) {
                html += `
                    <div class="skeleton-row skeleton-loader">
                        <div class="skeleton-cell" style="width: 25%"></div>
                        <div class="skeleton-cell" style="width: 30%"></div>
                        <div class="skeleton-cell" style="width: 20%"></div>
                        <div class="skeleton-cell" style="width: 15%"></div>
                        <div class="skeleton-cell" style="width: 10%"></div>
                    </div>
                `;
            }
            html += '</div>';
            return html;
        },

        // Generate chart skeleton HTML
        generateChartSkeleton: function() {
            return `
                <div class="chart-skeleton skeleton-loader">
                    <div class="skeleton-bars">
                        <div class="skeleton-bar"></div>
                        <div class="skeleton-bar"></div>
                        <div class="skeleton-bar"></div>
                        <div class="skeleton-bar"></div>
                        <div class="skeleton-bar"></div>
                    </div>
                </div>
            `;
        },

        // Render stats
        renderStats: function(stats) {
            // Implementation depends on your stats structure
            console.log('Rendering stats:', stats);
        },

        // Render recent invoices
        renderRecentInvoices: function(invoices) {
            // Implementation depends on your invoice structure
            console.log('Rendering invoices:', invoices);
        },

        // Render charts
        renderCharts: function(chartData) {
            // Implementation depends on your chart library
            console.log('Rendering charts:', chartData);
        },

        // Refresh dashboard
        refreshDashboard: function() {
            if (this.state.isLoading) return;

            this.state.isLoading = true;
            this.showProgressBar();
            
            const refreshBtn = document.getElementById('refresh-dashboard');
            if (refreshBtn) {
                refreshBtn.classList.add('btn-loading');
                const icon = refreshBtn.querySelector('i');
                if (icon) icon.classList.add('spinning');
            }

            this.initializeComponents().finally(() => {
                this.state.isLoading = false;
                this.hideProgressBar();
                
                if (refreshBtn) {
                    refreshBtn.classList.remove('btn-loading');
                    const icon = refreshBtn.querySelector('i');
                    if (icon) icon.classList.remove('spinning');
                }
            });
        },

        // Setup auto refresh
        setupAutoRefresh: function() {
            const savedState = localStorage.getItem('dashboard_auto_refresh');
            if (savedState === 'true') {
                const toggle = document.getElementById('auto-refresh-toggle');
                if (toggle) {
                    toggle.checked = true;
                    this.state.autoRefresh = true;
                    this.startAutoRefresh();
                }
            }
        },

        // Start auto refresh
        startAutoRefresh: function() {
            this.stopAutoRefresh(); // Clear any existing timer
            
            this.state.refreshTimer = setInterval(() => {
                this.refreshDashboard();
            }, this.config.refreshInterval);

            localStorage.setItem('dashboard_auto_refresh', 'true');
        },

        // Stop auto refresh
        stopAutoRefresh: function() {
            if (this.state.refreshTimer) {
                clearInterval(this.state.refreshTimer);
                this.state.refreshTimer = null;
            }
            localStorage.setItem('dashboard_auto_refresh', 'false');
        },

        // Setup pull to refresh
        setupPullToRefresh: function() {
            let startY = 0;
            let currentY = 0;
            let pulling = false;
            const threshold = 100;

            const pullBox = document.createElement('div');
            pullBox.className = 'pull-to-refresh-box';
            pullBox.innerHTML = '<i class="bi bi-arrow-clockwise fs-4"></i>';
            document.body.appendChild(pullBox);

            document.addEventListener('touchstart', (e) => {
                if (window.scrollY === 0) {
                    startY = e.touches[0].pageY;
                    pulling = true;
                }
            }, { passive: true });

            document.addEventListener('touchmove', (e) => {
                if (!pulling) return;

                currentY = e.touches[0].pageY;
                const diff = currentY - startY;

                if (diff > 0 && diff < threshold * 2) {
                    pullBox.style.top = Math.min(diff - 80, 20) + 'px';
                    pullBox.classList.add('show');
                    
                    if (diff > threshold) {
                        pullBox.classList.add('ready');
                    } else {
                        pullBox.classList.remove('ready');
                    }
                }
            }, { passive: true });

            document.addEventListener('touchend', () => {
                if (!pulling) return;

                const diff = currentY - startY;
                if (diff > threshold) {
                    pullBox.classList.add('refreshing');
                    this.refreshDashboard().finally(() => {
                        pullBox.classList.remove('show', 'ready', 'refreshing');
                        pullBox.style.top = '-80px';
                    });
                } else {
                    pullBox.classList.remove('show', 'ready');
                    pullBox.style.top = '-80px';
                }

                pulling = false;
                startY = 0;
                currentY = 0;
            });
        },

        // Show progress bar
        showProgressBar: function() {
            let progressBar = document.querySelector('.progress-indicator');
            if (!progressBar) {
                progressBar = document.createElement('div');
                progressBar.className = 'progress-indicator';
                progressBar.innerHTML = '<div class="progress-bar"></div>';
                document.body.appendChild(progressBar);
            }
            
            progressBar.classList.add('show', 'indeterminate');
        },

        // Hide progress bar
        hideProgressBar: function() {
            const progressBar = document.querySelector('.progress-indicator');
            if (progressBar) {
                progressBar.classList.remove('show', 'indeterminate');
            }
        },

        // Show error
        showError: function(title, error) {
            console.error(title, error);
            
            const errorHtml = `
                <div class="error-state">
                    <div class="error-icon">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="error-title">${title}</div>
                    <div class="error-message">${error.message || 'An unexpected error occurred'}</div>
                    <div class="error-actions">
                        <button class="btn btn-primary" onclick="location.reload()">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reload Page
                        </button>
                    </div>
                </div>
            `;

            // Show in main content area
            const mainContent = document.querySelector('.content-wrapper') || document.querySelector('main');
            if (mainContent) {
                mainContent.innerHTML = errorHtml;
            }

            // Also show toast
            this.showErrorToast(title);
        },

        // Show component error
        showComponentError: function(container, message) {
            if (container) {
                container.innerHTML = `
                    <div class="card-error">
                        <i class="bi bi-exclamation-circle error-icon"></i>
                        ${message}
                        <button class="btn btn-sm btn-link" onclick="location.reload()">Retry</button>
                    </div>
                `;
            }
        },

        // Toast notifications
        showToast: function(message, type = 'info') {
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container';
                document.body.appendChild(toastContainer);
            }

            const toastId = 'toast-' + Date.now();
            const toast = document.createElement('div');
            toast.className = `toast toast-${type} fade-in`;
            toast.id = toastId;
            toast.innerHTML = `
                <div class="toast-header">
                    <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">${message}</div>
            `;

            toastContainer.appendChild(toast);

            // Auto remove after 5 seconds
            setTimeout(() => {
                toast.remove();
            }, 5000);
        },

        showSuccessToast: function(message) {
            this.showToast(message, 'success');
        },

        showErrorToast: function(message) {
            this.showToast(message, 'error');
        },

        showWarningToast: function(message) {
            this.showToast(message, 'warning');
        },

        showInfoToast: function(message) {
            this.showToast(message, 'info');
        },

        // Handle online status
        handleOnline: function() {
            this.showSuccessToast('Connection restored');
            if (this.state.autoRefresh) {
                this.refreshDashboard();
            }
        },

        // Handle offline status
        handleOffline: function() {
            this.showWarningToast('No internet connection');
            this.stopAutoRefresh();
        },

        // Check initial state
        checkInitialState: function() {
            if (!navigator.onLine) {
                this.showWarningToast('You are currently offline');
            }
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => DashboardEnhanced.init());
    } else {
        DashboardEnhanced.init();
    }

    // Export for external use
    window.DashboardEnhanced = DashboardEnhanced;

})();