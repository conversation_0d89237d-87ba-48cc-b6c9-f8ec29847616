<?php

namespace App\Models;

use App\Core\Model;
use Exception;

class ServicePackage extends Model
{
    protected $table = 'service_packages';
    
    protected $fillable = [
        'name',
        'description',
        'total_sessions',
        'valid_days',
        'price',
        'services_included',
        'is_active'
    ];
    
    protected $casts = [
        'services_included' => 'json',
        'price' => 'decimal',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get active packages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    /**
     * Get included services
     */
    public function getIncludedServices()
    {
        if (empty($this->services_included)) {
            return collect([]);
        }
        
        $serviceIds = is_string($this->services_included) 
            ? json_decode($this->services_included, true) 
            : $this->services_included;
            
        if (empty($serviceIds)) {
            return collect([]);
        }
        
        return CatalogItem::whereIn('id', $serviceIds)->get();
    }
    
    /**
     * Check if a service is included in this package
     */
    public function includesService($serviceId)
    {
        $services = is_string($this->services_included) 
            ? json_decode($this->services_included, true) 
            : $this->services_included;
            
        return in_array($serviceId, $services ?? []);
    }
    
    /**
     * Create a purchase for this package
     */
    public function createPurchase($clientId, $invoiceId, $purchaseDate = null)
    {
        if (!$purchaseDate) {
            $purchaseDate = date('Y-m-d');
        }
        
        $expiryDate = date('Y-m-d', strtotime($purchaseDate . ' + ' . $this->valid_days . ' days'));
        
        return PackagePurchase::create([
            'package_id' => $this->id,
            'client_id' => $clientId,
            'invoice_id' => $invoiceId,
            'purchase_date' => $purchaseDate,
            'expiry_date' => $expiryDate,
            'sessions_total' => $this->total_sessions,
            'sessions_used' => 0,
            'status' => 'active'
        ]);
    }
    
    /**
     * Get purchases relationship
     */
    public function purchases()
    {
        return $this->hasMany(PackagePurchase::class, 'package_id');
    }
    
    /**
     * Get active purchases count
     */
    public function getActivePurchasesCount()
    {
        return $this->purchases()
            ->where('status', 'active')
            ->count();
    }
    
    /**
     * Get total revenue from this package
     */
    public function getTotalRevenue()
    {
        return $this->purchases()
            ->join('sales_invoices', 'package_purchases.invoice_id', '=', 'sales_invoices.id')
            ->where('sales_invoices.payment_status', 'paid')
            ->sum('service_packages.price');
    }
    
    /**
     * Get average usage rate
     */
    public function getAverageUsageRate()
    {
        $purchases = $this->purchases()
            ->whereIn('status', ['completed', 'expired'])
            ->get();
            
        if ($purchases->isEmpty()) {
            return 0;
        }
        
        $totalUsageRate = 0;
        foreach ($purchases as $purchase) {
            $usageRate = ($purchase->sessions_used / $purchase->sessions_total) * 100;
            $totalUsageRate += $usageRate;
        }
        
        return round($totalUsageRate / $purchases->count(), 2);
    }
    
    /**
     * Duplicate this package
     */
    public function duplicate($newName = null)
    {
        $data = $this->toArray();
        unset($data['id'], $data['created_at'], $data['updated_at']);
        
        if ($newName) {
            $data['name'] = $newName;
        } else {
            $data['name'] = $data['name'] . ' (Copy)';
        }
        
        return static::create($data);
    }
    
    /**
     * Format price for display
     */
    public function getFormattedPriceAttribute()
    {
        return number_format($this->price, 2) . ' €';
    }
    
    /**
     * Get validity period text
     */
    public function getValidityPeriodAttribute()
    {
        if ($this->valid_days == 365) {
            return '1 year';
        } elseif ($this->valid_days == 180) {
            return '6 months';
        } elseif ($this->valid_days == 90) {
            return '3 months';
        } elseif ($this->valid_days == 30) {
            return '1 month';
        } else {
            return $this->valid_days . ' days';
        }
    }
    
    /**
     * Get price per session
     */
    public function getPricePerSessionAttribute()
    {
        if ($this->total_sessions == 0) {
            return 0;
        }
        
        return round($this->price / $this->total_sessions, 2);
    }
}