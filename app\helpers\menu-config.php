<?php

/**
 * Check if a menu item should be displayed
 * @param string $menuId The menu item ID
 * @return bool Whether the menu should be shown
 */
function shouldShowMenu($menuId) {
    // Admin users see everything
    if (isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin']) {
        return true;
    }
    
    // Check database for menu visibility settings
    static $menuSettings = null;
    
    if ($menuSettings === null) {
        $menuSettings = [];
        
        try {
            $db = Flight::db();
            $companyId = $_SESSION['company_id'] ?? 1;
            
            // Create table if not exists
            $db->exec("CREATE TABLE IF NOT EXISTS menu_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                company_id INT NOT NULL,
                menu_id VARCHAR(50) NOT NULL,
                enabled TINYINT(1) DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                UNIQUE KEY unique_company_menu (company_id, menu_id)
            )");
            
            $stmt = $db->prepare("SELECT menu_id, enabled FROM menu_settings WHERE company_id = ?");
            $stmt->execute([$companyId]);
            $menuSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (Exception $e) {
            // If error, show all menus by default
            error_log("Menu settings error: " . $e->getMessage());
        }
    }
    
    // If we have a setting for this menu, use it
    if (isset($menuSettings[$menuId])) {
        return (bool)$menuSettings[$menuId];
    }
    
    // Default visibility for each menu
    $defaultVisibility = [
        'patients' => true,
        'clients' => true,
        'invoices' => true,
        'users' => true,
        'config' => true,
        'translations' => false, // Hidden by default
        'billing' => false, // Hidden by default
        'reports' => true
    ];
    
    return $defaultVisibility[$menuId] ?? true;
}

/**
 * Get CSS class for disabled menu items (admin only)
 * @param string $menuId The menu item ID
 * @return string CSS classes
 */
function getMenuClass($menuId) {
    if (isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin']) {
        if (!shouldShowMenu($menuId)) {
            return 'opacity-50';
        }
    }
    return '';
}