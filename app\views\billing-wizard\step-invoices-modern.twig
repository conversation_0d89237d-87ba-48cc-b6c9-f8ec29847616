{% extends "base-modern.twig" %}

{% block title %}{{ __('billing.wizard_select_invoices') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Wizard Progress -->
    <div class="mb-4">
        <div class="d-flex justify-content-center">
            <div class="wizard-progress">
                <div class="wizard-step active">
                    <div class="step-number">1</div>
                    <div class="step-label">{{ __('billing.select_invoices') }}</div>
                </div>
                <div class="wizard-line"></div>
                <div class="wizard-step">
                    <div class="step-number">2</div>
                    <div class="step-label">{{ __('billing.configure_options') }}</div>
                </div>
                <div class="wizard-line"></div>
                <div class="wizard-step">
                    <div class="step-number">3</div>
                    <div class="step-label">{{ __('billing.review_generate') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('billing.select_invoices_to_generate') }}</h1>
        <a href="{{ base_url }}/billing-wizard" class="btn btn-secondary">
            <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
        </a>
    </div>

    <form method="POST" action="{{ base_url }}/billing-wizard/step-2" id="invoiceSelectionForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" name="session_id" value="{{ session_id }}">
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Invoice Type Selection -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-receipt me-2"></i>{{ __('billing.invoice_type') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="invoice_type" id="type_recurring" 
                                           value="recurring" checked onchange="updateInvoiceList()">
                                    <label class="form-check-label" for="type_recurring">
                                        <i class="bi bi-calendar-check text-primary"></i> {{ __('billing.recurring_invoices') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="invoice_type" id="type_retrocession" 
                                           value="retrocession" onchange="updateInvoiceList()">
                                    <label class="form-check-label" for="type_retrocession">
                                        <i class="bi bi-percent text-success"></i> {{ __('billing.retrocession_invoices') }}
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="invoice_type" id="type_pending" 
                                           value="pending" onchange="updateInvoiceList()">
                                    <label class="form-check-label" for="type_pending">
                                        <i class="bi bi-clock-history text-warning"></i> {{ __('billing.pending_invoices') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Period Selection -->
                <div class="card shadow-sm mb-4" id="periodSelection">
                    <div class="card-header bg-white">
                        <h6 class="mb-0"><i class="bi bi-calendar3 me-2"></i>{{ __('billing.billing_period') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="period_month" class="form-label">{{ __('common.month') }}</label>
                                <select class="form-select" id="period_month" name="period_month">
                                    {% for i in 1..12 %}
                                        <option value="{{ i }}" {{ current_month == i ? 'selected' : '' }}>
                                            {{ ('2024-' ~ i ~ '-01')|date('F') }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="period_year" class="form-label">{{ __('common.year') }}</label>
                                <select class="form-select" id="period_year" name="period_year">
                                    {% for year in (current_year - 2)..(current_year + 1) %}
                                        <option value="{{ year }}" {{ current_year == year ? 'selected' : '' }}>{{ year }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice List -->
                <div class="card shadow-sm">
                    <div class="card-header bg-white d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-list-check me-2"></i>{{ __('billing.available_invoices') }}</h6>
                        <div>
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                <i class="bi bi-check2-all"></i> {{ __('common.select_all') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                                <i class="bi bi-x-circle"></i> {{ __('common.select_none') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th width="40">
                                            <input type="checkbox" class="form-check-input" id="selectAllCheck" checked>
                                        </th>
                                        <th>{{ __('common.patient') }}</th>
                                        <th>{{ __('common.services') }}</th>
                                        <th>{{ __('common.amount') }}</th>
                                        <th>{{ __('invoices.last_invoice') }}</th>
                                        <th>{{ __('common.status') }}</th>
                                    </tr>
                                </thead>
                                <tbody id="invoiceList">
                                    {% for item in available_invoices %}
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input invoice-check" 
                                                   name="selected_invoices[]" value="{{ item.id }}" checked>
                                        </td>
                                        <td>
                                            <strong>{{ item.patient_name }}</strong>
                                            <br><small class="text-muted">{{ item.patient_number }}</small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ item.service_count }} services</span>
                                        </td>
                                        <td>
                                            <strong>{{ currency }}{{ item.total_amount|number_format(2, ',', ' ') }}</strong>
                                        </td>
                                        <td>
                                            {% if item.last_invoice_date %}
                                                {{ item.last_invoice_date|date('d/m/Y') }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if item.type == 'recurring' %}
                                                <span class="badge bg-primary">{{ __('billing.recurring') }}</span>
                                            {% elseif item.type == 'retrocession' %}
                                                <span class="badge bg-success">{{ __('billing.retrocession') }}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{{ __('billing.pending') }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% else %}
                                    <tr>
                                        <td colspan="6" class="text-center py-4 text-muted">
                                            {{ __('billing.no_invoices_available') }}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Summary -->
                <div class="card shadow-sm sticky-top" style="top: 20px;">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-calculator me-2"></i>{{ __('billing.summary') }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-7">{{ __('billing.selected_invoices') }}:</dt>
                            <dd class="col-5 text-end" id="selectedCount">0</dd>
                            
                            <dt class="col-7">{{ __('billing.total_amount') }}:</dt>
                            <dd class="col-5 text-end" id="totalAmount">{{ currency }}0,00</dd>
                            
                            <dt class="col-7">{{ __('billing.average_amount') }}:</dt>
                            <dd class="col-5 text-end" id="averageAmount">{{ currency }}0,00</dd>
                        </dl>
                        
                        <hr>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="continueBtn" disabled>
                                {{ __('common.continue') }} <i class="bi bi-arrow-right ms-2"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
.wizard-progress {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.wizard-step {
    text-align: center;
    position: relative;
}

.wizard-step .step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 0.5rem;
}

.wizard-step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.wizard-step .step-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.wizard-step.active .step-label {
    color: #0d6efd;
    font-weight: 500;
}

.wizard-line {
    flex: 1;
    height: 2px;
    background-color: #e9ecef;
    margin: 0 1rem;
    margin-bottom: 2rem;
}
</style>

<script>
// Update invoice list based on type
function updateInvoiceList() {
    const type = document.querySelector('input[name="invoice_type"]:checked').value;
    const periodDiv = document.getElementById('periodSelection');
    
    // Show/hide period selection based on type
    if (type === 'pending') {
        periodDiv.style.display = 'none';
    } else {
        periodDiv.style.display = 'block';
    }
    
    // TODO: Load invoices via AJAX based on type and period
    updateSummary();
}

// Select all invoices
function selectAll() {
    document.querySelectorAll('.invoice-check').forEach(cb => {
        cb.checked = true;
    });
    document.getElementById('selectAllCheck').checked = true;
    updateSummary();
}

// Deselect all invoices
function selectNone() {
    document.querySelectorAll('.invoice-check').forEach(cb => {
        cb.checked = false;
    });
    document.getElementById('selectAllCheck').checked = false;
    updateSummary();
}

// Select all checkbox handler
document.getElementById('selectAllCheck').addEventListener('change', function() {
    if (this.checked) {
        selectAll();
    } else {
        selectNone();
    }
});

// Update summary
function updateSummary() {
    const checkboxes = document.querySelectorAll('.invoice-check:checked');
    const count = checkboxes.length;
    let total = 0;
    
    // Calculate total (would need actual amounts from data attributes)
    checkboxes.forEach(cb => {
        // total += parseFloat(cb.dataset.amount || 0);
    });
    
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('totalAmount').textContent = '{{ currency }}' + total.toFixed(2).replace('.', ',');
    document.getElementById('averageAmount').textContent = '{{ currency }}' + (count > 0 ? (total / count).toFixed(2).replace('.', ',') : '0,00');
    
    // Enable/disable continue button
    document.getElementById('continueBtn').disabled = count === 0;
}

// Individual checkbox change handler
document.querySelectorAll('.invoice-check').forEach(cb => {
    cb.addEventListener('change', updateSummary);
});

// Initial summary update
updateSummary();
</script>
{% endblock %}