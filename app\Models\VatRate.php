<?php

namespace App\Models;

use App\Core\Model;

class VatRate extends Model
{
    protected $table = 'config_vat_rates';
    
    protected $fillable = [
        'code',
        'rate',
        'description',
        'is_default',
        'is_active'
    ];
    
    protected $casts = [
        'rate' => 'float',
        'is_default' => 'boolean'
    ];
    
    /**
     * Get active VAT rates
     */
    public static function getActive()
    {
        $db = \Flight::db();
        $stmt = $db->prepare("
            SELECT * FROM config_vat_rates 
            WHERE is_active = 1 
            ORDER BY rate ASC
        ");
        $stmt->execute();
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get the default VAT rate
     */
    public static function getDefault()
    {
        return self::where('is_default', true)->first();
    }
    
    /**
     * Set a VAT rate as default
     */
    public function setAsDefault()
    {
        // Unset all other defaults
        self::where('is_default', true)->update(['is_default' => false]);
        
        // Set this as default
        $this->is_default = true;
        $this->save();
        
        return $this;
    }
}