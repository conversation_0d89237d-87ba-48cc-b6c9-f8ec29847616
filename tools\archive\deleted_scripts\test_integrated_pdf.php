<?php
/**
 * Test Integrated PDF System
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\PdfService;

$invoiceNumber = $_GET['number'] ?? 'FAC-2025-00064';

try {
    // Get invoice using the model
    $db = Flight::db();
    $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ?");
    $stmt->execute([$invoiceNumber]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$result) {
        throw new Exception("Invoice not found");
    }
    
    $invoiceModel = new Invoice();
    $invoice = $invoiceModel->getInvoiceWithDetails($result['id']);
    
    // Debug: Show what we're sending to PdfService
    if (isset($_GET['debug'])) {
        echo "<h2>Invoice Data being sent to PdfService:</h2>";
        echo "<pre>";
        print_r($invoice);
        echo "</pre>";
        
        // Check what might be missing
        echo "<h3>Checks:</h3>";
        echo "<ul>";
        echo "<li>Has invoice_type: " . (!empty($invoice['invoice_type']) ? 'YES (' . $invoice['invoice_type'] . ')' : 'NO') . "</li>";
        echo "<li>Has lines: " . (isset($invoice['lines']) && count($invoice['lines']) > 0 ? 'YES' : 'NO') . "</li>";
        echo "<li>Has client: " . (isset($invoice['client']) ? 'YES' : 'NO') . "</li>";
        echo "</ul>";
        exit;
    }
    
    // Create a new PdfService instance
    $pdfService = new PdfService();
    
    // Try to generate PDF
    $pdf = $pdfService->generateInvoicePdf($invoice);
    
    // Output PDF
    header('Content-Type: application/pdf');
    header('Content-Disposition: inline; filename="invoice-' . $invoice['invoice_number'] . '.pdf"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    echo $pdf;
    exit;
    
} catch (Exception $e) {
    echo "<h1>Error in integrated PDF system:</h1>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<h2>Stack trace:</h2>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    
    // Show the line where error occurred
    $trace = $e->getTrace();
    if (!empty($trace[0]['file']) && !empty($trace[0]['line'])) {
        echo "<h3>Error location:</h3>";
        echo "<p>File: " . $trace[0]['file'] . "</p>";
        echo "<p>Line: " . $trace[0]['line'] . "</p>";
    }
}