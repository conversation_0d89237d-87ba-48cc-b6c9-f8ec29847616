<?php
/**
 * Check Invoice FAC-RET30-2025-0189 Details and Calculations
 */

// Load environment and bootstrap
require_once __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

require_once __DIR__ . '/app/config/bootstrap.php';

try {
    $db = Flight::db();
    
    echo "=== CHECKING INVOICE FAC-RET30-2025-0189 ===\n\n";
    
    // Get invoice details
    $stmt = $db->prepare("
        SELECT 
            i.*,
            CONCAT(u.first_name, ' ', u.last_name) as user_name,
            u.vat_intercommunautaire,
            u.email as user_email,
            it.name as invoice_type_name,
            it.prefix as invoice_type_prefix,
            dt.name as document_type_name
        FROM invoices i
        LEFT JOIN users u ON i.user_id = u.id
        LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
        LEFT JOIN config_document_types dt ON i.document_type_id = dt.id
        WHERE i.invoice_number = 'FAC-RET30-2025-0189'
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        die("Invoice FAC-RET30-2025-0189 not found!\n");
    }
    
    // Display invoice header
    echo "INVOICE DETAILS:\n";
    echo "----------------\n";
    echo "Invoice Number: " . $invoice['invoice_number'] . "\n";
    echo "Status: " . $invoice['status'] . "\n";
    echo "Type: " . $invoice['invoice_type_name'] . " (" . $invoice['invoice_type_prefix'] . ")\n";
    echo "Document Type: " . $invoice['document_type_name'] . "\n";
    echo "User: " . $invoice['user_name'] . " (ID: " . $invoice['user_id'] . ")\n";
    echo "User Email: " . $invoice['user_email'] . "\n";
    echo "VAT Intracommunautaire: " . ($invoice['vat_intercommunautaire'] ?: 'NONE') . "\n";
    echo "Issue Date: " . $invoice['issue_date'] . "\n";
    echo "Due Date: " . $invoice['due_date'] . "\n";
    echo "Period: " . $invoice['period'] . "\n";
    echo "\n";
    
    // Get invoice lines
    $stmt = $db->prepare("
        SELECT 
            il.*,
            vr.rate as vat_rate_value,
            vr.name as vat_rate_name
        FROM invoice_lines il
        LEFT JOIN config_vat_rates vr ON il.vat_rate_id = vr.id
        WHERE il.invoice_id = ?
        ORDER BY il.id
    ");
    $stmt->execute([$invoice['id']]);
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "INVOICE LINES:\n";
    echo "--------------\n";
    
    $calculatedSubtotal = 0;
    $calculatedVatAmount = 0;
    
    foreach ($lines as $index => $line) {
        echo "\nLine " . ($index + 1) . ":\n";
        echo "  Description: " . $line['description'] . "\n";
        echo "  Quantity: " . $line['quantity'] . "\n";
        echo "  Unit Price: €" . number_format($line['unit_price'], 2) . "\n";
        echo "  Line Subtotal: €" . number_format($line['quantity'] * $line['unit_price'], 2) . "\n";
        echo "  VAT Rate: " . $line['vat_rate_name'] . " (" . $line['vat_rate_value'] . "%)\n";
        
        $lineSubtotal = $line['quantity'] * $line['unit_price'];
        $lineVat = $lineSubtotal * ($line['vat_rate_value'] / 100);
        $lineTotal = $lineSubtotal + $lineVat;
        
        echo "  VAT Amount: €" . number_format($lineVat, 2) . "\n";
        echo "  Line Total (with VAT): €" . number_format($lineTotal, 2) . "\n";
        
        $calculatedSubtotal += $lineSubtotal;
        $calculatedVatAmount += $lineVat;
    }
    
    $calculatedTotal = $calculatedSubtotal + $calculatedVatAmount;
    
    echo "\n";
    echo "TOTALS COMPARISON:\n";
    echo "------------------\n";
    echo "                    Stored    |  Calculated\n";
    echo "Subtotal:        €" . str_pad(number_format($invoice['subtotal'], 2), 9) . " | €" . number_format($calculatedSubtotal, 2) . "\n";
    echo "VAT Amount:      €" . str_pad(number_format($invoice['vat_amount'], 2), 9) . " | €" . number_format($calculatedVatAmount, 2) . "\n";
    echo "Total:           €" . str_pad(number_format($invoice['total'], 2), 9) . " | €" . number_format($calculatedTotal, 2) . "\n";
    
    // Check for discrepancies
    $subtotalDiff = abs($invoice['subtotal'] - $calculatedSubtotal);
    $vatDiff = abs($invoice['vat_amount'] - $calculatedVatAmount);
    $totalDiff = abs($invoice['total'] - $calculatedTotal);
    
    if ($subtotalDiff > 0.01 || $vatDiff > 0.01 || $totalDiff > 0.01) {
        echo "\n⚠️  DISCREPANCIES FOUND:\n";
        if ($subtotalDiff > 0.01) {
            echo "   Subtotal difference: €" . number_format($subtotalDiff, 2) . "\n";
        }
        if ($vatDiff > 0.01) {
            echo "   VAT difference: €" . number_format($vatDiff, 2) . "\n";
        }
        if ($totalDiff > 0.01) {
            echo "   Total difference: €" . number_format($totalDiff, 2) . "\n";
        }
    } else {
        echo "\n✅ All calculations match!\n";
    }
    
    // Check retrocession details if this is a retrocession invoice
    if (strpos($invoice['invoice_number'], 'RET') !== false) {
        echo "\nRETROCESSION DETAILS:\n";
        echo "--------------------\n";
        
        $stmt = $db->prepare("
            SELECT 
                ir.*,
                rr.name as rate_name,
                rr.calculation_type,
                rr.percentage,
                rr.fixed_amount
            FROM invoice_retrocessions ir
            LEFT JOIN retrocession_rates rr ON ir.retrocession_rate_id = rr.id
            WHERE ir.invoice_id = ?
        ");
        $stmt->execute([$invoice['id']]);
        $retrocession = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($retrocession) {
            echo "Month: " . $retrocession['month'] . "/" . $retrocession['year'] . "\n";
            echo "Rate Profile: " . $retrocession['rate_name'] . "\n";
            echo "Calculation Type: " . $retrocession['calculation_type'] . "\n";
            echo "Base CNS: €" . number_format($retrocession['base_cns'], 2) . "\n";
            echo "Base Patients: €" . number_format($retrocession['base_patients'], 2) . "\n";
            echo "Total Base: €" . number_format($retrocession['base_cns'] + $retrocession['base_patients'], 2) . "\n";
            
            if ($retrocession['calculation_type'] == 'percentage') {
                echo "Percentage: " . $retrocession['percentage'] . "%\n";
                
                // Check if the retrocession calculation is correct
                $expectedRetrocessionCNS = $retrocession['base_cns'] * ($retrocession['percentage'] / 100);
                $expectedRetrocessionPatients = $retrocession['base_patients'] * ($retrocession['percentage'] / 100);
                
                echo "\nExpected Retrocession CNS: €" . number_format($expectedRetrocessionCNS, 2) . "\n";
                echo "Expected Retrocession Patients: €" . number_format($expectedRetrocessionPatients, 2) . "\n";
                
                // Find the corresponding lines
                foreach ($lines as $line) {
                    if (strpos($line['description'], 'RÉTROCESSION CNS') !== false) {
                        echo "\nCNS Line: €" . number_format($line['unit_price'], 2);
                        if (abs($line['unit_price'] - $expectedRetrocessionCNS) > 0.01) {
                            echo " ⚠️  Expected: €" . number_format($expectedRetrocessionCNS, 2);
                        } else {
                            echo " ✅";
                        }
                    }
                    if (strpos($line['description'], 'RÉTROCESSION PATIENTS') !== false) {
                        echo "\nPatients Line: €" . number_format($line['unit_price'], 2);
                        if (abs($line['unit_price'] - $expectedRetrocessionPatients) > 0.01) {
                            echo " ⚠️  Expected: €" . number_format($expectedRetrocessionPatients, 2);
                        } else {
                            echo " ✅";
                        }
                    }
                }
            }
        } else {
            echo "No retrocession details found for this invoice.\n";
        }
    }
    
    echo "\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}