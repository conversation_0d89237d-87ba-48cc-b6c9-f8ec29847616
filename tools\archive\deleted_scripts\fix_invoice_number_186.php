<?php
// Fix invoice number to use LOY prefix and correct sequence

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

// Load .env file
loadEnv(__DIR__ . '/../.env');

// Get database config from env
$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? '';

// Create PDO connection
$db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

try {
    echo "<h2>Fixing Invoice Number</h2>";
    
    // Get the invoice with wrong number
    $stmt = $db->prepare("
        SELECT i.*, dt.id as document_type_id, it.prefix as type_prefix
        FROM invoices i
        JOIN document_types dt ON i.document_type = dt.code
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.invoice_number = 'FAC-2025-0190'
        LIMIT 1
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "Invoice FAC-2025-0190 not found!<br>";
        exit;
    }
    
    echo "Found invoice ID: " . $invoice['id'] . "<br>";
    echo "Current number: " . $invoice['invoice_number'] . "<br>";
    echo "Invoice type prefix: " . ($invoice['type_prefix'] ?? 'None') . "<br>";
    
    // Check if it's a rental (LOY) invoice
    if ($invoice['type_prefix'] === 'LOY') {
        $newNumber = 'FAC-LOY-2025-0186';
        
        // Check if new number already exists
        $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ?");
        $stmt->execute([$newNumber]);
        if ($stmt->fetch()) {
            echo "<strong>Error:</strong> Invoice number $newNumber already exists!<br>";
            exit;
        }
        
        // Update the invoice number
        $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
        $stmt->execute([$newNumber, $invoice['id']]);
        
        echo "✓ Updated invoice number to: $newNumber<br>";
        
        // Also need to adjust the sequence counter
        // First check current sequence
        $stmt = $db->prepare("
            SELECT * FROM document_sequences 
            WHERE document_type_id = ? AND year = 2025
        ");
        $stmt->execute([$invoice['document_type_id']]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($sequence) {
            echo "Current sequence number: " . $sequence['last_number'] . "<br>";
            
            // Update sequence to 185 so next invoice will be 186
            $stmt = $db->prepare("
                UPDATE document_sequences 
                SET last_number = 185 
                WHERE document_type_id = ? AND year = 2025
            ");
            $stmt->execute([$invoice['document_type_id']]);
            echo "✓ Reset sequence to 185 (next will be 186)<br>";
        }
        
        // Add numbers 187-189 to deleted pool so they won't be used
        for ($num = 187; $num <= 189; $num++) {
            $deletedNumber = 'FAC-2025-' . str_pad($num, 4, '0', STR_PAD_LEFT);
            
            $stmt = $db->prepare("
                INSERT IGNORE INTO deleted_invoice_numbers (
                    document_type_id, 
                    invoice_number, 
                    year, 
                    sequence_number, 
                    deleted_by
                ) VALUES (?, ?, 2025, ?, 1)
            ");
            $stmt->execute([$invoice['document_type_id'], $deletedNumber, $num]);
            echo "✓ Added $deletedNumber to deleted numbers pool<br>";
        }
        
        echo "<br><strong>Success!</strong> Invoice number has been corrected.";
        
    } else {
        echo "This invoice doesn't have LOY prefix. Type prefix: " . ($invoice['type_prefix'] ?? 'None') . "<br>";
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>