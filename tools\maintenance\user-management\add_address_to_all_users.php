<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== ADDING ADDRESS TO ALL USERS ===\n\n";
    
    // First, check if columns exist
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = 'fitapp' 
        AND TABLE_NAME = 'users' 
        AND COLUMN_NAME = 'address'
    ");
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] == 0) {
        echo "❌ Address columns don't exist! Running migration first...\n\n";
        
        // Run the migration
        $migrationFile = dirname(__DIR__) . '/database/migrations/2025_01_07_add_address_vat_to_users.sql';
        if (file_exists($migrationFile)) {
            $sql = file_get_contents($migrationFile);
            $statements = array_filter(array_map('trim', explode(';', $sql)));
            
            foreach ($statements as $statement) {
                if (!empty($statement)) {
                    try {
                        $pdo->exec($statement);
                        echo "✓ Executed: " . substr($statement, 0, 50) . "...\n";
                    } catch (PDOException $e) {
                        echo "⚠️  Warning: " . $e->getMessage() . "\n";
                    }
                }
            }
            echo "\n✓ Migration completed!\n\n";
        } else {
            echo "ERROR: Migration file not found!\n";
            exit(1);
        }
    }
    
    // Now update all users with empty addresses
    echo "Updating users with default address...\n\n";
    
    // First, show current status
    $stmt = $pdo->query("
        SELECT COUNT(*) as total,
               SUM(CASE WHEN address IS NULL OR address = '' THEN 1 ELSE 0 END) as empty_address,
               SUM(CASE WHEN address IS NOT NULL AND address != '' THEN 1 ELSE 0 END) as has_address
        FROM users
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current status:\n";
    echo "- Total users: {$stats['total']}\n";
    echo "- Users with address: {$stats['has_address']}\n";
    echo "- Users without address: {$stats['empty_address']}\n\n";
    
    // Update users without address
    $stmt = $pdo->prepare("
        UPDATE users 
        SET 
            address = :address,
            postal_code = :postal_code,
            city = :city,
            country = :country
        WHERE address IS NULL OR address = ''
    ");
    
    $result = $stmt->execute([
        ':address' => '15, am Pëtz',
        ':postal_code' => 'L-9579',
        ':city' => 'Weidingen',
        ':country' => 'LU'
    ]);
    
    $updatedCount = $stmt->rowCount();
    echo "✓ Updated {$updatedCount} users with default address\n\n";
    
    // Show some examples
    echo "=== SAMPLE USERS AFTER UPDATE ===\n\n";
    $stmt = $pdo->query("
        SELECT id, username, first_name, last_name, 
               address, postal_code, city, country, vat_intercommunautaire 
        FROM users 
        ORDER BY id
        LIMIT 10
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($users as $user) {
        echo "User #{$user['id']} ({$user['username']} - {$user['first_name']} {$user['last_name']}):\n";
        echo "  📍 {$user['address']}, {$user['postal_code']} {$user['city']}, {$user['country']}\n";
        if ($user['vat_intercommunautaire']) {
            echo "  💼 VAT: {$user['vat_intercommunautaire']}\n";
        }
        echo "\n";
    }
    
    echo "✅ All done! All users now have address information.\n";
    echo "\nYou can now edit any user and customize their address as needed.\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}