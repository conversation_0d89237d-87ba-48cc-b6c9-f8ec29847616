<?php

namespace Tests\Phase4;

use PHPUnit\Framework\TestCase;
use App\Models\CatalogItem;
use App\Models\CatalogCategory;
use App\Models\StockMovement;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use Flight;

class StockManagementTest extends TestCase
{
    protected static $db;
    protected $testItem;
    protected $testCategory;
    protected $testMovements = [];
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        self::$db->exec("DELETE FROM stock_movements WHERE item_id IN (SELECT id FROM catalog_items WHERE code LIKE 'TEST-STOCK-%')");
        self::$db->exec("DELETE FROM catalog_items WHERE code LIKE 'TEST-STOCK-%'");
        self::$db->exec("DELETE FROM catalog_categories WHERE code LIKE 'TEST-STOCK-%'");
    }
    
    public function setUp(): void
    {
        // Create unique test category for each test
        $testId = uniqid();
        $this->testCategory = CatalogCategory::create([
            'code' => 'TEST-STOCK-CAT-' . $testId,
            'name' => json_encode(['en' => 'Stock Test Category ' . $testId]),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);

        // Create test item with stock
        $this->testItem = CatalogItem::create([
            'code' => 'TEST-STOCK-ITEM-' . $testId,
            'name' => 'Stock Test Item ' . $testId,
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'unit_price' => 50.00,
            'is_stockable' => 1,
            'current_stock' => 100,
            'low_stock_alert' => 20,
            'is_active' => 1,
            'created_by' => 1
        ]);
    }

    public function testCreateStockMovement()
    {
        $movement = StockMovement::create([
            'item_id' => $this->testItem->id,
            'movement_type' => 'adjustment',
            'quantity' => 50,
            'reference_type' => null,
            'reference_id' => null,
            'notes' => 'Initial stock adjustment',
            'created_by' => 1
        ]);
        
        $this->assertNotNull($movement);
        $this->assertNotNull($movement->id);
        $this->assertEquals($this->testItem->id, $movement->item_id);
        $this->assertEquals('adjustment', $movement->movement_type);
        $this->assertEquals(50, $movement->quantity);
        
        $this->testMovements[] = $movement->id;
        
        return $movement;
    }
    
    public function testStockMovementTypes()
    {
        $movementTypes = [
            ['type' => 'purchase', 'quantity' => 100, 'notes' => 'Purchase order #123'],
            ['type' => 'sale', 'quantity' => -25, 'notes' => 'Invoice #456'],
            ['type' => 'return', 'quantity' => 5, 'notes' => 'Customer return'],
            ['type' => 'adjustment', 'quantity' => -10, 'notes' => 'Inventory count adjustment'],
            ['type' => 'transfer', 'quantity' => -15, 'notes' => 'Transfer to other location'],
            ['type' => 'damage', 'quantity' => -3, 'notes' => 'Damaged goods']
        ];
        
        foreach ($movementTypes as $movData) {
            $movement = StockMovement::create([
                'item_id' => $this->testItem->id,
                'movement_type' => $movData['type'],
                'quantity' => $movData['quantity'],
                'notes' => $movData['notes'],
                'created_by' => 1
            ]);
            
            $this->assertEquals($movData['type'], $movement->movement_type);
            $this->assertEquals($movData['quantity'], $movement->quantity);
            
            $this->testMovements[] = $movement->id;
        }
    }
    
    public function testStockAdjustmentViaItem()
    {
        $initialStock = $this->testItem->current_stock;
        
        // Test positive adjustment
        $movement1 = $this->testItem->adjustStock(50, 'purchase');
        $this->assertNotNull($movement1);
        $this->assertEquals(50, $movement1->quantity);
        $this->assertEquals($initialStock + 50, $this->testItem->current_stock);
        
        // Test negative adjustment
        $movement2 = $this->testItem->adjustStock(-30, 'sale');
        $this->assertNotNull($movement2);
        $this->assertEquals(-30, $movement2->quantity);
        $this->assertEquals($initialStock + 50 - 30, $this->testItem->current_stock);
        
        $this->testMovements[] = $movement1->id;
        $this->testMovements[] = $movement2->id;
    }
    
    public function testStockMovementWithReference()
    {
        // Create a test invoice
        $invoice = Invoice::create([
            'invoice_number' => 'TEST-STOCK-INV-001',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => 1,
            'issue_date' => date('Y-m-d'),
            'status' => 'sent',
            'subtotal_amount' => 100.00,
            'vat_amount' => 17.00,
            'total_amount' => 117.00
        ]);
        
        // Create stock movement referencing the invoice
        $movement = $this->testItem->adjustStock(-5, 'sale', $invoice, 1);
        
        $this->assertNotNull($movement);
        $this->assertEquals('App\\Models\\Invoice', $movement->reference_type);
        $this->assertEquals($invoice->id, $movement->reference_id);
        
        $this->testMovements[] = $movement->id;
        
        // Clean up invoice
        self::$db->exec("DELETE FROM invoices WHERE id = " . $invoice->id);
    }
    
    public function testGetStockMovements()
    {
        // Create several movements
        for ($i = 1; $i <= 5; $i++) {
            $movement = StockMovement::create([
                'item_id' => $this->testItem->id,
                'movement_type' => 'adjustment',
                'quantity' => $i * 10,
                'created_by' => 1
            ]);
            $this->testMovements[] = $movement->id;
        }
        
        // Get movements for the item
        $movements = StockMovement::where('item_id', '=', $this->testItem->id)
                                 ->orderBy('created_at', 'DESC')
                                 ->get();
        
        $this->assertGreaterThanOrEqual(5, count($movements));
        
        // Verify they are ordered by date (newest first)
        $previousDate = null;
        foreach ($movements as $movement) {
            if ($previousDate !== null) {
                $this->assertLessThanOrEqual($previousDate, $movement->created_at);
            }
            $previousDate = $movement->created_at;
        }
    }
    
    public function testCalculateStockBalance()
    {
        // Reset stock to known value
        $this->testItem->current_stock = 0;
        $this->testItem->save();
        
        // Create movements
        $movements = [
            ['quantity' => 100, 'type' => 'purchase'],  // +100 = 100
            ['quantity' => -30, 'type' => 'sale'],      // -30 = 70
            ['quantity' => 50, 'type' => 'purchase'],   // +50 = 120
            ['quantity' => -40, 'type' => 'sale'],      // -40 = 80
            ['quantity' => 10, 'type' => 'return'],     // +10 = 90
        ];
        
        $expectedStock = 0;
        foreach ($movements as $movData) {
            $this->testItem->adjustStock($movData['quantity'], $movData['type']);
            $expectedStock += $movData['quantity'];
        }
        
        // Verify final stock
        $this->assertEquals($expectedStock, $this->testItem->current_stock);
        
        // Calculate balance from movements
        $totalMovements = self::$db->prepare("
            SELECT SUM(quantity) as total 
            FROM stock_movements 
            WHERE item_id = ?
        ");
        $totalMovements->execute([$this->testItem->id]);
        $result = $totalMovements->fetch(\PDO::FETCH_ASSOC);
        
        $this->assertEquals($expectedStock, $result['total']);
    }
    
    public function testStockAlerts()
    {
        $testId = uniqid();

        // Create items with different stock levels
        $items = [
            [
                'code' => 'TEST-STOCK-CRITICAL-' . $testId,
                'name' => 'Critical Stock Item',
                'current_stock' => 5,
                'low_stock_alert' => 20
            ],
            [
                'code' => 'TEST-STOCK-LOW-' . $testId,
                'name' => 'Low Stock Item',
                'current_stock' => 25,
                'low_stock_alert' => 30
            ],
            [
                'code' => 'TEST-STOCK-GOOD-' . $testId,
                'name' => 'Good Stock Item',
                'current_stock' => 100,
                'low_stock_alert' => 20
            ],
            [
                'code' => 'TEST-STOCK-OUT-' . $testId,
                'name' => 'Out of Stock Item',
                'current_stock' => 0,
                'low_stock_alert' => 10
            ]
        ];
        
        $createdItems = [];
        foreach ($items as $itemData) {
            $item = CatalogItem::create(array_merge($itemData, [
                'category_id' => $this->testCategory->id,
                'item_type' => 'product',
                'unit_price' => 25.00,
                'is_stockable' => 1,
                'is_active' => 1,
                'created_by' => 1
            ]));
            $createdItems[] = $item;
        }
        
        // Get low stock items
        $lowStockItems = CatalogItem::getLowStockItems();
        
        // Filter to our test items
        $testLowStock = [];
        foreach ($lowStockItems as $item) {
            if (strpos($item->code, 'TEST-STOCK-') === 0) {
                $testLowStock[] = $item;
            }
        }
        
        // Should find 3 items (CRITICAL, LOW, and OUT)
        $this->assertCount(3, $testLowStock);
        
        // Verify stock statuses
        foreach ($createdItems as $item) {
            if (strpos($item->code, 'TEST-STOCK-CRITICAL-') === 0 || strpos($item->code, 'TEST-STOCK-LOW-') === 0) {
                $this->assertTrue($item->hasLowStock());
                $this->assertEquals('low_stock', $item->getStockStatus());
            } elseif (strpos($item->code, 'TEST-STOCK-OUT-') === 0) {
                $this->assertFalse($item->isInStock());
                $this->assertEquals('out_of_stock', $item->getStockStatus());
            } elseif (strpos($item->code, 'TEST-STOCK-GOOD-') === 0) {
                $this->assertFalse($item->hasLowStock());
                $this->assertEquals('in_stock', $item->getStockStatus());
            }
        }
        
        // Clean up
        foreach ($createdItems as $item) {
            self::$db->exec("DELETE FROM stock_movements WHERE item_id = " . $item->id);
            self::$db->exec("DELETE FROM catalog_items WHERE id = " . $item->id);
        }
    }
    
    public function testStockMovementHistory()
    {
        // Create a series of movements to simulate real usage
        $history = [
            ['date' => '-30 days', 'type' => 'purchase', 'quantity' => 200, 'notes' => 'Initial purchase'],
            ['date' => '-25 days', 'type' => 'sale', 'quantity' => -50, 'notes' => 'Sale to customer A'],
            ['date' => '-20 days', 'type' => 'sale', 'quantity' => -30, 'notes' => 'Sale to customer B'],
            ['date' => '-15 days', 'type' => 'return', 'quantity' => 10, 'notes' => 'Return from customer A'],
            ['date' => '-10 days', 'type' => 'damage', 'quantity' => -5, 'notes' => 'Damaged in storage'],
            ['date' => '-5 days', 'type' => 'adjustment', 'quantity' => -3, 'notes' => 'Inventory count correction'],
            ['date' => '-1 day', 'type' => 'sale', 'quantity' => -20, 'notes' => 'Sale to customer C']
        ];
        
        foreach ($history as $entry) {
            $movement = StockMovement::create([
                'item_id' => $this->testItem->id,
                'movement_type' => $entry['type'],
                'quantity' => $entry['quantity'],
                'notes' => $entry['notes'],
                'created_at' => date('Y-m-d H:i:s', strtotime($entry['date'])),
                'created_by' => 1
            ]);
            $this->testMovements[] = $movement->id;
        }
        
        // Get movement history
        $movements = StockMovement::where('item_id', '=', $this->testItem->id)
                                 ->orderBy('created_at', 'DESC')
                                 ->get();
        
        // Verify we have all movements
        $this->assertGreaterThanOrEqual(7, count($movements));
        
        // Calculate running balance
        $runningBalance = 0;
        foreach (array_reverse($movements->all()) as $movement) {
            $runningBalance += $movement->quantity;
        }
        
        // The running balance should match the sum of all quantities
        $expectedBalance = array_sum(array_column($history, 'quantity'));
        $this->assertEquals($expectedBalance, $runningBalance);
    }
    
    public function tearDown(): void
    {
        // Clean up ALL stock movements for this test item first (foreign key constraint)
        if ($this->testItem) {
            self::$db->exec("DELETE FROM stock_movements WHERE item_id = " . $this->testItem->id);
        }

        // Clean up specific test movements (if any remain)
        if (!empty($this->testMovements)) {
            self::$db->exec("DELETE FROM stock_movements WHERE id IN (" . implode(',', $this->testMovements) . ")");
        }

        // Clean up test item (now safe to delete)
        if ($this->testItem) {
            self::$db->exec("DELETE FROM catalog_items WHERE id = " . $this->testItem->id);
        }

        // Clean up test category
        if ($this->testCategory) {
            self::$db->exec("DELETE FROM catalog_categories WHERE id = " . $this->testCategory->id);
        }

        // Reset arrays
        $this->testMovements = [];
    }
    
    public static function tearDownAfterClass(): void
    {
        // Final cleanup
        self::$db->exec("DELETE FROM stock_movements WHERE item_id IN (SELECT id FROM catalog_items WHERE code LIKE 'TEST-STOCK-%')");
        self::$db->exec("DELETE FROM catalog_items WHERE code LIKE 'TEST-STOCK-%'");
        self::$db->exec("DELETE FROM catalog_categories WHERE code LIKE 'TEST-STOCK-%'");
    }
}