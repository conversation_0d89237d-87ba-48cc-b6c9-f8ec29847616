<?php
/**
 * Check Invoices Table Structure
 */

// Load environment
define('APP_PATH', dirname(__DIR__));
require APP_PATH . '/vendor/autoload.php';

if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Connect to database
try {
    $db = new PDO(
        "mysql:host=" . ($_ENV['DB_HOST'] ?? '127.0.0.1') . ";dbname=" . ($_ENV['DB_DATABASE'] ?? 'fitapp'),
        $_ENV['DB_USERNAME'] ?? 'root',
        $_ENV['DB_PASSWORD'] ?? ''
    );
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Invoices Table Structure</h1>";
    echo "<pre>";
    
    // Get table structure
    $stmt = $db->query("DESCRIBE invoices");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Columns in invoices table:\n";
    echo str_pad("Field", 25) . str_pad("Type", 30) . str_pad("Null", 10) . "\n";
    echo str_repeat("-", 65) . "\n";
    
    $hasPatientId = false;
    $hasClientId = false;
    
    foreach ($columns as $col) {
        echo str_pad($col['Field'], 25);
        echo str_pad($col['Type'], 30);
        echo str_pad($col['Null'], 10);
        echo "\n";
        
        if ($col['Field'] === 'patient_id') $hasPatientId = true;
        if ($col['Field'] === 'client_id') $hasClientId = true;
    }
    
    echo "\n\nAnalysis:\n";
    echo "Has patient_id column: " . ($hasPatientId ? "YES" : "NO") . "\n";
    echo "Has client_id column: " . ($hasClientId ? "YES" : "NO") . "\n";
    
    if (!$hasPatientId) {
        echo "\n⚠ The invoices table doesn't have a patient_id column.\n";
        echo "This is causing the dashboard error.\n";
    }
    
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h1>Database Error</h1>";
    echo "<pre style='color: red;'>" . $e->getMessage() . "</pre>";
}