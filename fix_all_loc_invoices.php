<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Helpers\MoneyHelper;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Fixing ALL LOC Invoices (including drafts) ===\n\n";
    
    // Get all LOC invoices including drafts
    $stmt = $pdo->prepare("
        SELECT i.*, it.code as invoice_type_code, it.prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
        WHERE (i.invoice_number LIKE '%LOC%'
           OR it.code = 'location'
           OR it.prefix LIKE '%LOC%')
        ORDER BY i.id DESC
    ");
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($invoices) . " LOC invoices (including drafts)\n\n";
    
    $fixedCount = 0;
    $byStatus = ['draft' => 0, 'sent' => 0, 'paid' => 0, 'other' => 0];
    
    foreach ($invoices as $invoice) {
        $invoiceId = $invoice['id'];
        $currentTotal = floatval($invoice['total']);
        $status = $invoice['status'];
        
        // First check invoice_items table
        $stmt = $pdo->prepare("
            SELECT * FROM invoice_items 
            WHERE invoice_id = ? 
            ORDER BY id
        ");
        $stmt->execute([$invoiceId]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // If no items, check invoice_lines table
        if (empty($items)) {
            $stmt = $pdo->prepare("
                SELECT *, 
                       quantity as quantity,
                       line_total as unit_price,
                       vat_rate,
                       'Line item' as description
                FROM invoice_lines 
                WHERE invoice_id = ? 
                ORDER BY id
            ");
            $stmt->execute([$invoiceId]);
            $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($lines)) {
                // Convert lines to items format for processing
                $items = [];
                foreach ($lines as $line) {
                    // Try to extract quantity from description
                    $qty = 1;
                    if (preg_match('/(\d+)\s*(séance|session|cours)/i', $line['description'], $matches)) {
                        $qty = intval($matches[1]);
                    }
                    
                    // Assume line_total is the NET total for all units
                    $unitPrice = $qty > 0 ? $line['line_total'] / $qty : $line['line_total'];
                    
                    $items[] = [
                        'quantity' => $qty,
                        'unit_price' => $unitPrice,
                        'vat_rate' => $line['vat_rate'] ?? 17,
                        'description' => $line['description']
                    ];
                }
            }
        }
        
        if (empty($items)) {
            continue; // Skip if no items
        }
        
        // Recalculate using TTC method
        $newSubtotal = 0;
        $newVat = 0;
        $newTotal = 0;
        
        foreach ($items as $item) {
            $quantity = floatval($item['quantity']);
            $unitPrice = floatval($item['unit_price']);
            $vatRate = floatval($item['vat_rate'] ?? 17);
            
            // Calculate TTC per unit
            $ttcPerUnit = $unitPrice * (1 + $vatRate / 100);
            $ttcPerUnit = MoneyHelper::round($ttcPerUnit);
            
            // Check if this should be a round number (€15, €30, €45, etc.)
            $roundedTTC = round($ttcPerUnit);
            if (abs($roundedTTC - $ttcPerUnit) < 0.10 && ($roundedTTC % 15 == 0 || $roundedTTC % 10 == 0)) {
                $ttcPerUnit = $roundedTTC;
            }
            
            // Calculate line total
            $lineTTC = $quantity * $ttcPerUnit;
            $ttcCalc = MoneyHelper::calculateFromTTC($lineTTC, $vatRate);
            
            $newSubtotal += $ttcCalc['net'];
            $newVat += $ttcCalc['vat'];
            $newTotal += $ttcCalc['total'];
        }
        
        // Round final totals
        $newSubtotal = MoneyHelper::round($newSubtotal);
        $newVat = MoneyHelper::round($newVat);
        $newTotal = MoneyHelper::round($newTotal);
        
        // Check if invoice totals need updating
        $difference = abs($currentTotal - $newTotal);
        
        if ($difference > 0.01) {
            echo "Invoice #{$invoice['invoice_number']} (ID: $invoiceId) - Status: $status\n";
            echo "  Current total: €" . number_format($currentTotal, 2) . "\n";
            echo "  New total: €" . number_format($newTotal, 2) . "\n";
            echo "  Difference: €" . number_format($newTotal - $currentTotal, 2) . "\n";
            
            // Update invoice totals
            $stmt = $pdo->prepare("
                UPDATE invoices 
                SET subtotal = ?,
                    vat_amount = ?,
                    total = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$newSubtotal, $newVat, $newTotal, $invoiceId]);
            
            echo "  ✓ Fixed!\n\n";
            $fixedCount++;
            
            // Track by status
            if (isset($byStatus[$status])) {
                $byStatus[$status]++;
            } else {
                $byStatus['other']++;
            }
        }
    }
    
    echo "\n=== Summary ===\n";
    echo "Total invoices fixed: $fixedCount\n";
    echo "By status:\n";
    foreach ($byStatus as $status => $count) {
        if ($count > 0) {
            echo "  - " . ucfirst($status) . ": $count\n";
        }
    }
    
    // Special check for invoice 268
    echo "\n=== Checking Invoice 268 ===\n";
    
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 268");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "Invoice #" . $invoice['invoice_number'] . "\n";
        echo "Status: " . $invoice['status'] . "\n";
        echo "Total: €" . number_format($invoice['total'], 2) . "\n";
        
        if (abs($invoice['total'] - 210.00) < 0.01) {
            echo "✓ Invoice 268 is correctly showing €210.00\n";
        } else {
            echo "⚠ Invoice 268 total is €" . number_format($invoice['total'], 2) . " - may need manual adjustment\n";
            
            // Try to fix it specifically
            echo "\nAttempting to fix invoice 268 to €210.00...\n";
            $ttcCalc = MoneyHelper::calculateFromTTC(210.00, 17.00);
            
            $stmt = $pdo->prepare("
                UPDATE invoices 
                SET subtotal = ?,
                    vat_amount = ?,
                    total = ?,
                    updated_at = NOW()
                WHERE id = 268
            ");
            $stmt->execute([$ttcCalc['net'], $ttcCalc['vat'], $ttcCalc['total']]);
            
            echo "✓ Invoice 268 has been set to €210.00\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}