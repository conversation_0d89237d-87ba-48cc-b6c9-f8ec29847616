<?php

namespace Tests\Phase4;

use PHPUnit\Framework\TestCase;
use App\Models\CatalogCategory;
use App\Models\CatalogItem;
use Flight;

class CatalogCategoryTest extends TestCase
{
    protected static $db;
    protected $testCategories = [];
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        self::$db->exec("DELETE FROM catalog_items WHERE category_id IN (SELECT id FROM catalog_categories WHERE code LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM catalog_categories WHERE code LIKE 'TEST-%'");
    }
    
    public function testCreateCategory()
    {
        $category = CatalogCategory::create([
            'code' => 'TEST-CAT-ROOT',
            'name' => json_encode(['en' => 'Test Root Category', 'fr' => 'Catégorie Racine Test']),
            'description' => json_encode(['en' => 'Test root category description']),
            'parent_id' => null,
            'icon' => 'bi-folder',
            'color' => '#007bff',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->assertNotNull($category);
        $this->assertNotNull($category->id);
        $this->assertEquals('TEST-CAT-ROOT', $category->code);
        $this->assertNull($category->parent_id);
        
        $this->testCategories[] = $category->id;
        
        return $category;
    }
    
    public function testGetLocalizedName()
    {
        // Create test category
        $category = CatalogCategory::create([
            'code' => 'TEST-CAT-LOCALIZED',
            'name' => json_encode(['en' => 'Test Root Category', 'fr' => 'Catégorie Racine Test']),
            'description' => json_encode(['en' => 'Test root category description']),
            'parent_id' => null,
            'icon' => 'bi-folder',
            'color' => '#007bff',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $category->id;
        
        // Test English name
        $englishName = $category->getLocalizedName('en');
        $this->assertEquals('Test Root Category', $englishName);
        
        // Test French name
        $frenchName = $category->getLocalizedName('fr');
        $this->assertEquals('Catégorie Racine Test', $frenchName);
        
        // Test fallback
        $germanName = $category->getLocalizedName('de');
        $this->assertEquals('Test Root Category', $germanName); // Should fallback to English
    }
    
    public function testCreateSubcategory()
    {
        // First create a parent category
        $parentCategory = CatalogCategory::create([
            'code' => 'TEST-CAT-PARENT',
            'name' => json_encode(['en' => 'Parent Category', 'fr' => 'Catégorie Parent']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $parentCategory->id;
        
        $subcategory = CatalogCategory::create([
            'code' => 'TEST-CAT-SUB1',
            'name' => json_encode(['en' => 'Test Subcategory', 'fr' => 'Sous-catégorie Test']),
            'parent_id' => $parentCategory->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->assertNotNull($subcategory);
        $this->assertEquals($parentCategory->id, $subcategory->parent_id);
        
        $this->testCategories[] = $subcategory->id;
        
        // Test parent relationship
        $parent = $subcategory->getParent();
        $this->assertNotNull($parent);
        $this->assertEquals($parentCategory->id, $parent->id);
        
        return $subcategory;
    }
    
    public function testGetChildren()
    {
        // Create parent category
        $parentCategory = CatalogCategory::create([
            'code' => 'TEST-CAT-GETCHILD',
            'name' => json_encode(['en' => 'Parent for Children Test']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $parentCategory->id;
        
        // Create subcategories
        $subcategory1 = CatalogCategory::create([
            'code' => 'TEST-CAT-CHILD1',
            'name' => json_encode(['en' => 'Test Child 1']),
            'parent_id' => $parentCategory->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $subcategory2 = CatalogCategory::create([
            'code' => 'TEST-CAT-CHILD2',
            'name' => json_encode(['en' => 'Test Child 2']),
            'parent_id' => $parentCategory->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $subcategory1->id;
        $this->testCategories[] = $subcategory2->id;
        
        // Get children
        $children = $parentCategory->getChildren();
        
        $this->assertGreaterThanOrEqual(2, count($children));
        
        // Verify both subcategories are in the children
        $childCodes = [];
        foreach ($children as $child) {
            $childCodes[] = $child->code;
        }
        
        $this->assertContains('TEST-CAT-CHILD1', $childCodes);
        $this->assertContains('TEST-CAT-CHILD2', $childCodes);
    }
    
    public function testGetPath()
    {
        // Create a hierarchy
        $root = CatalogCategory::create([
            'code' => 'TEST-CAT-PATH-ROOT',
            'name' => json_encode(['en' => 'Path Root']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $middle = CatalogCategory::create([
            'code' => 'TEST-CAT-PATH-MID',
            'name' => json_encode(['en' => 'Path Middle']),
            'parent_id' => $root->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $leaf = CatalogCategory::create([
            'code' => 'TEST-CAT-PATH-LEAF',
            'name' => json_encode(['en' => 'Path Leaf']),
            'parent_id' => $middle->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $root->id;
        $this->testCategories[] = $middle->id;
        $this->testCategories[] = $leaf->id;
        
        $path = $leaf->getPath();
        
        $this->assertIsArray($path);
        $this->assertCount(3, $path); // Root, middle and leaf
        
        // Path should be ordered from root to current
        $this->assertEquals('TEST-CAT-PATH-ROOT', $path[0]->code);
        $this->assertEquals('TEST-CAT-PATH-MID', $path[1]->code);
        $this->assertEquals('TEST-CAT-PATH-LEAF', $path[2]->code);
    }
    
    public function testGetBreadcrumb()
    {
        // Create a hierarchy
        $parent = CatalogCategory::create([
            'code' => 'TEST-CAT-BC-PARENT',
            'name' => json_encode(['en' => 'Breadcrumb Parent', 'fr' => 'Parent Ariane']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $child = CatalogCategory::create([
            'code' => 'TEST-CAT-BC-CHILD',
            'name' => json_encode(['en' => 'Breadcrumb Child', 'fr' => 'Enfant Ariane']),
            'parent_id' => $parent->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $parent->id;
        $this->testCategories[] = $child->id;
        
        $breadcrumb = $child->getBreadcrumb();
        
        $this->assertIsString($breadcrumb);
        $this->assertStringContainsString('Breadcrumb Parent', $breadcrumb);
        $this->assertStringContainsString('Breadcrumb Child', $breadcrumb);
        $this->assertStringContainsString(' > ', $breadcrumb);
    }
    
    public function testMultiLevelHierarchy()
    {
        // Create a 3-level hierarchy
        $level1 = CatalogCategory::create([
            'code' => 'TEST-L1',
            'name' => json_encode(['en' => 'Level 1']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $level1->id;
        
        $level2 = CatalogCategory::create([
            'code' => 'TEST-L2',
            'name' => json_encode(['en' => 'Level 2']),
            'parent_id' => $level1->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $level2->id;
        
        $level3 = CatalogCategory::create([
            'code' => 'TEST-L3',
            'name' => json_encode(['en' => 'Level 3']),
            'parent_id' => $level2->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $level3->id;
        
        // Test path from level 3
        $path = $level3->getPath();
        $this->assertCount(3, $path);
        $this->assertEquals('TEST-L1', $path[0]->code);
        $this->assertEquals('TEST-L2', $path[1]->code);
        $this->assertEquals('TEST-L3', $path[2]->code);
        
        // Test breadcrumb
        $breadcrumb = $level3->getBreadcrumb();
        $this->assertEquals('Level 1 > Level 2 > Level 3', $breadcrumb);
    }
    
    public function testGetItems()
    {
        // Create a test category
        $category = CatalogCategory::create([
            'code' => 'TEST-CAT-ITEMS',
            'name' => json_encode(['en' => 'Items Test Category']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $category->id;
        
        // Create test items in the category
        $items = [];
        for ($i = 1; $i <= 3; $i++) {
            $item = CatalogItem::create([
                'code' => "TEST-ITEM-CAT-{$i}",
                'name' => "Category Item {$i}",
                'category_id' => $category->id,
                'item_type' => 'product',
                'unit_price' => 10.00 * $i,
                'is_active' => 1,
                'created_by' => 1
            ]);
            $items[] = $item;
        }
        
        // Get items from category
        $categoryItems = $category->getItems();
        
        // Filter to only our test items
        $testItems = [];
        foreach ($categoryItems as $item) {
            if (strpos($item->code, 'TEST-ITEM-CAT-') === 0) {
                $testItems[] = $item;
            }
        }
        
        $this->assertCount(3, $testItems);
        
        // Clean up items
        foreach ($items as $item) {
            self::$db->exec("DELETE FROM catalog_items WHERE id = " . $item->id);
        }
    }
    
    public function testGetRootCategories()
    {
        // Create multiple root categories
        $roots = [];
        for ($i = 1; $i <= 3; $i++) {
            $root = CatalogCategory::create([
                'code' => "TEST-ROOT-{$i}",
                'name' => json_encode(['en' => "Root Category {$i}"]),
                'parent_id' => null,
                'is_active' => 1,
                'created_by' => 1
            ]);
            $roots[] = $root;
            $this->testCategories[] = $root->id;
        }
        
        // Get all root categories
        $allRoots = CatalogCategory::getRootCategories();
        
        // Filter to only our test roots
        $testRoots = [];
        foreach ($allRoots as $cat) {
            if (strpos($cat->code, 'TEST-ROOT-') === 0) {
                $testRoots[] = $cat;
            }
        }
        
        $this->assertCount(3, $testRoots);
    }
    
    public function testCategoryTree()
    {
        // Create a tree structure
        $electronics = CatalogCategory::create([
            'code' => 'TEST-TREE-ELEC',
            'name' => json_encode(['en' => 'Electronics']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $electronics->id;
        
        $computers = CatalogCategory::create([
            'code' => 'TEST-TREE-COMP',
            'name' => json_encode(['en' => 'Computers']),
            'parent_id' => $electronics->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $computers->id;
        
        $laptops = CatalogCategory::create([
            'code' => 'TEST-TREE-LAPT',
            'name' => json_encode(['en' => 'Laptops']),
            'parent_id' => $computers->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $laptops->id;
        
        $phones = CatalogCategory::create([
            'code' => 'TEST-TREE-PHON',
            'name' => json_encode(['en' => 'Phones']),
            'parent_id' => $electronics->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        $this->testCategories[] = $phones->id;
        
        // Test tree structure
        $elecChildren = $electronics->getChildren();
        $testElecChildren = [];
        foreach ($elecChildren as $cat) {
            if (strpos($cat->code, 'TEST-TREE-') === 0) {
                $testElecChildren[] = $cat;
            }
        }
        $this->assertCount(2, $testElecChildren);
        
        $compChildren = $computers->getChildren();
        $testCompChildren = [];
        foreach ($compChildren as $cat) {
            if (strpos($cat->code, 'TEST-TREE-') === 0) {
                $testCompChildren[] = $cat;
            }
        }
        $this->assertCount(1, $testCompChildren);
    }
    
    public function testCategoryStatus()
    {
        $category = CatalogCategory::create([
            'code' => 'TEST-STATUS-CAT',
            'name' => json_encode(['en' => 'Status Test Category']),
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $category->id;
        
        // Test active status
        $this->assertTrue($category->is_active);
        
        // Deactivate
        $category->is_active = 0;
        $category->save();
        
        // Verify deactivated
        $deactivated = CatalogCategory::find($category->id);
        $this->assertFalse($deactivated->is_active);
    }
    
    public function testGenerateCode()
    {
        // Test default prefix
        $code1 = CatalogCategory::generateCode();
        $this->assertStringStartsWith('CAT', $code1);
        
        // Test custom prefix
        $code2 = CatalogCategory::generateCode('PROD');
        $this->assertStringStartsWith('PROD', $code2);
        
        // Create a category with the generated code
        $category = CatalogCategory::create([
            'code' => $code2,
            'name' => json_encode(['en' => 'Generated Code Category']),
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testCategories[] = $category->id;
        
        // Generate next code - should increment
        $code3 = CatalogCategory::generateCode('PROD');
        $this->assertNotEquals($code2, $code3);
        $this->assertGreaterThan($code2, $code3);
    }
    
    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        self::$db->exec("DELETE FROM catalog_items WHERE category_id IN (SELECT id FROM catalog_categories WHERE code LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM catalog_categories WHERE code LIKE 'TEST-%'");
    }
}