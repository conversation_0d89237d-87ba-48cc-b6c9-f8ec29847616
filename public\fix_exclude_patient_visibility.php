<?php
/**
 * Fix for Exclude Patient Line visibility
 */

echo "<pre>";
echo "=== Fixing Exclude Patient Line Visibility ===\n\n";

$file = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($file);

// Create backup
$backup = $file . '.backup.' . date('YmdHis');
file_put_contents($backup, $content);
echo "✓ Created backup: " . basename($backup) . "\n\n";

// Find the section where we call toggleExcludePatientLine on page load
$searchPattern = "// Check on page load\n    toggleExcludePatientLine();";
$replacePattern = "// Check on page load\n    toggleExcludePatientLine();\n    \n    // Also check after a delay to ensure DOM is ready\n    setTimeout(() => {\n        toggleExcludePatientLine();\n        console.log('Checked exclude patient line visibility on load');\n    }, 100);";

// Replace the pattern
$newContent = str_replace($searchPattern, $replacePattern, $content);

if ($newContent === $content) {
    echo "⚠️ Could not find the exact pattern. Looking for alternative...\n";
    
    // Try alternative pattern
    $searchPattern = "toggleExcludePatientLine();";
    $pos = strrpos($content, $searchPattern);
    if ($pos !== false) {
        // Find the specific instance after "Check on page load"
        $checkPos = strrpos($content, "// Check on page load", -(strlen($content) - $pos));
        if ($checkPos !== false) {
            $newContent = substr($content, 0, $pos + strlen($searchPattern)) . "\n    \n    // Also check after a delay to ensure DOM is ready\n    setTimeout(() => {\n        toggleExcludePatientLine();\n        console.log('Checked exclude patient line visibility on load');\n    }, 100);" . substr($content, $pos + strlen($searchPattern));
            echo "✓ Found and updated alternative pattern\n";
        }
    }
}

// Save the file
file_put_contents($file, $newContent);

echo "\n✅ Fix applied successfully!\n\n";

echo "The exclude patient line checkbox will now:\n";
echo "1. Be visible immediately when a retrocession type is selected\n";
echo "2. Be checked automatically after DOM is ready\n";
echo "3. Show proper visibility based on invoice type\n\n";

echo "To verify:\n";
echo "1. Refresh the page: http://localhost/fit/public/invoices/create?type=retrocession_30\n";
echo "2. The 'Exclude patient line' checkbox should be visible\n";
echo "3. Check browser console for: 'Checked exclude patient line visibility on load'\n";

echo "</pre>";
?>