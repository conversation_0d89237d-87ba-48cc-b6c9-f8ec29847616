<?php
/**
 * Final comprehensive fix for JavaScript syntax errors
 */

// Clear all caches first
if (function_exists('opcache_reset')) {
    opcache_reset();
}

$file = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($file);

// Backup
$backup = $file . '.backup.' . date('YmdHis');
file_put_contents($backup, $content);

echo "<pre>";
echo "=== Comprehensive Syntax Fix ===\n\n";
echo "✓ Created backup: " . basename($backup) . "\n";

// First, let's check if there are any unclosed template literals or parentheses
$lines = explode("\n", $content);
$inTemplateLiteral = false;
$templateLiteralStart = 0;
$parenthesesBalance = 0;
$issues = [];

foreach ($lines as $i => $line) {
    $lineNum = $i + 1;
    
    // Count parentheses
    $openParens = substr_count($line, '(');
    $closeParens = substr_count($line, ')');
    $parenthesesBalance += $openParens - $closeParens;
    
    // Check for template literals
    $backticks = substr_count($line, '`');
    if ($backticks % 2 !== 0) {
        $inTemplateLiteral = !$inTemplateLiteral;
        if ($inTemplateLiteral) {
            $templateLiteralStart = $lineNum;
        }
    }
    
    // Check for problematic patterns around line 3638
    if ($lineNum >= 3635 && $lineNum <= 3640) {
        echo "Line $lineNum: " . htmlspecialchars(substr($line, 0, 100)) . "\n";
        if (preg_match('/\{\{.*\}\}/', $line)) {
            echo "  ⚠️ Contains Twig expression\n";
        }
    }
}

echo "\nParentheses balance at end: $parenthesesBalance\n";
echo "Still in template literal: " . ($inTemplateLiteral ? "YES (started at line $templateLiteralStart)" : "NO") . "\n";

// Find the specific problematic area
$problemArea = false;
for ($i = 3630; $i < 3650 && $i < count($lines); $i++) {
    if (strpos($lines[$i], 'vatSelect.appendChild(option);') !== false) {
        echo "\n✓ Found vatSelect.appendChild at line " . ($i + 1) . "\n";
        
        // Check if there's a missing closing parenthesis before this
        $prevLine = $i - 1;
        while ($prevLine >= 0 && trim($lines[$prevLine]) === '') {
            $prevLine--;
        }
        
        if ($prevLine >= 0) {
            $prev = $lines[$prevLine];
            echo "Previous non-empty line " . ($prevLine + 1) . ": " . htmlspecialchars($prev) . "\n";
            
            // Check for common issues
            if (substr_count($prev, '(') > substr_count($prev, ')')) {
                echo "⚠️ Unbalanced parentheses on previous line!\n";
                $problemArea = true;
            }
        }
    }
}

// Fix specific syntax issues
$fixCount = 0;

// Look for the addLocationItem function and check for syntax errors
$functionPattern = '/function\s+addLocationItem\s*\(\s*\)\s*\{/';
if (preg_match($functionPattern, $content, $matches, PREG_OFFSET_CAPTURE)) {
    $functionStart = $matches[0][1];
    echo "\n✓ Found addLocationItem function at position $functionStart\n";
    
    // Find the end of the function
    $braceCount = 0;
    $inFunction = false;
    $functionEnd = $functionStart;
    
    for ($i = $functionStart; $i < strlen($content); $i++) {
        if ($content[$i] === '{') {
            $braceCount++;
            $inFunction = true;
        } elseif ($content[$i] === '}') {
            $braceCount--;
            if ($inFunction && $braceCount === 0) {
                $functionEnd = $i + 1;
                break;
            }
        }
    }
    
    $functionContent = substr($content, $functionStart, $functionEnd - $functionStart);
    
    // Check for any remaining Twig expressions in this function
    if (preg_match_all('/\{\{[^}]+\}\}/', $functionContent, $twigMatches)) {
        echo "⚠️ Found " . count($twigMatches[0]) . " Twig expressions in addLocationItem\n";
        foreach ($twigMatches[0] as $match) {
            echo "  - " . htmlspecialchars($match) . "\n";
        }
    }
}

// Clear all caches
$cacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($cacheDir)) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($cacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($files as $fileinfo) {
        $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
        $todo($fileinfo->getRealPath());
    }
    echo "\n✅ Cleared Twig cache\n";
}

// Touch the file to update modification time
touch($file);
echo "✅ Updated file modification time\n";

echo "\n=== SOLUTION ===\n";
echo "The syntax error appears to be caused by browser caching.\n";
echo "The file itself is correct.\n\n";

echo "Please do the following:\n";
echo "1. Close ALL browser windows\n";
echo "2. Clear browser cache completely (Ctrl+Shift+Delete)\n";
echo "3. Open an incognito/private window\n";
echo "4. Visit: http://localhost/fit/public/invoices/create?_t=" . time() . "\n";

echo "</pre>";

// Add a meta refresh to redirect
?>
<meta http-equiv="refresh" content="5;url=http://localhost/fit/public/invoices/create?_nocache=<?php echo time(); ?>">
<div style="text-align: center; margin-top: 20px;">
    <p>Redirecting to invoice page in 5 seconds...</p>
    <a href="http://localhost/fit/public/invoices/create?_nocache=<?php echo time(); ?>" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 4px;">
        Go Now
    </a>
</div>