<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.prefix { font-weight: bold; color: #007bff; }
button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
</style>';

echo "<h1>Update Invoice Type Prefixes</h1>";

// Default prefixes mapping
$defaultPrefixes = [
    'rental' => 'LOY',      // Loyer
    'hourly' => 'HOR',      // Horaire
    'service' => 'SER',     // Service
    'retrocession_30' => 'RET30', // Rétrocession 30%
    'retrocession_25' => 'RET25', // Rétrocession 25%
    'medical' => 'MED',     // Medical
    'credit_note' => 'CN',  // Credit Note
    'quote' => 'DEV',       // Devis
    'receipt' => 'REC',     // Receipt
    'other' => 'DIV'        // Divers
];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'update_prefixes') {
        try {
            $db->beginTransaction();
            
            foreach ($_POST['prefixes'] as $id => $prefix) {
                if (!empty($prefix)) {
                    $stmt = $db->prepare("UPDATE config_invoice_types SET prefix = ? WHERE id = ?");
                    $stmt->execute([strtoupper($prefix), $id]);
                }
            }
            
            $db->commit();
            echo '<div class="success">✓ Prefixes updated successfully!</div>';
        } catch (Exception $e) {
            $db->rollBack();
            echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
        }
    }
}

// Get current invoice types
echo '<div class="section">';
echo '<h2>Current Invoice Types and Prefixes</h2>';
$stmt = $db->query("
    SELECT id, name, code, prefix, description, is_active 
    FROM config_invoice_types 
    ORDER BY id
");
$types = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<form method="post">';
echo '<input type="hidden" name="action" value="update_prefixes">';
echo '<table>';
echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Current Prefix</th><th>New Prefix</th><th>Example</th></tr>';

foreach ($types as $type) {
    // Decode JSON fields
    $name = json_decode($type['name'], true);
    $displayName = is_array($name) ? ($name['fr'] ?? $name['en'] ?? $type['name']) : $type['name'];
    
    $currentPrefix = $type['prefix'] ?? '';
    $suggestedPrefix = $defaultPrefixes[$type['code']] ?? strtoupper(substr($type['code'], 0, 3));
    $prefixToUse = $currentPrefix ?: $suggestedPrefix;
    
    echo '<tr>';
    echo '<td>' . $type['id'] . '</td>';
    echo '<td>' . htmlspecialchars($displayName) . '</td>';
    echo '<td>' . $type['code'] . '</td>';
    echo '<td class="prefix">' . ($currentPrefix ?: 'NULL') . '</td>';
    echo '<td><input type="text" name="prefixes[' . $type['id'] . ']" value="' . $prefixToUse . '" maxlength="10" style="width: 100px; text-transform: uppercase;"></td>';
    echo '<td><strong>FAC-' . $prefixToUse . '-2025-0186</strong></td>';
    echo '</tr>';
}

echo '</table>';
echo '<button type="submit" class="btn-success">Update All Prefixes</button>';
echo '</form>';
echo '</div>';

// Check if prefix column exists
echo '<div class="section">';
echo '<h2>Database Structure Check</h2>';
$stmt = $db->query("
    SELECT COLUMN_NAME, DATA_TYPE, CHARACTER_MAXIMUM_LENGTH, IS_NULLABLE
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'config_invoice_types'
    AND COLUMN_NAME = 'prefix'
");
$column = $stmt->fetch(\PDO::FETCH_ASSOC);

if ($column) {
    echo '<div class="success">✓ Prefix column exists in config_invoice_types table</div>';
    echo '<p>Column details: ' . $column['DATA_TYPE'] . '(' . $column['CHARACTER_MAXIMUM_LENGTH'] . '), Nullable: ' . $column['IS_NULLABLE'] . '</p>';
} else {
    echo '<div class="error">✗ Prefix column does not exist in config_invoice_types table</div>';
    echo '<p>You need to add the prefix column first.</p>';
}
echo '</div>';

// Show how invoice generation will work
echo '<div class="section">';
echo '<h2>How Invoice Numbers Will Be Generated</h2>';
echo '<ol>';
echo '<li>User creates invoice and selects type (e.g., "Loyer/Rental")</li>';
echo '<li>System gets the prefix for that type (e.g., "LOY")</li>';
echo '<li>Invoice number becomes: <strong>FAC-LOY-2025-0186</strong></li>';
echo '</ol>';
echo '<p>Format: <code>FAC-{TYPE_PREFIX}-{YYYY}-{NNNN}</code></p>';
echo '</div>';

// Add column if missing
if (!$column) {
    echo '<div class="section">';
    echo '<h2>Add Missing Prefix Column</h2>';
    echo '<form method="post">';
    echo '<input type="hidden" name="action" value="add_column">';
    echo '<button type="submit" class="btn-primary">Add Prefix Column to Database</button>';
    echo '</form>';
    echo '</div>';
    
    if ($_POST['action'] === 'add_column') {
        try {
            $db->exec("ALTER TABLE config_invoice_types ADD COLUMN prefix VARCHAR(10) NULL AFTER code");
            echo '<div class="success">✓ Prefix column added successfully! Please refresh the page.</div>';
        } catch (Exception $e) {
            echo '<div class="error">Error adding column: ' . $e->getMessage() . '</div>';
        }
    }
}