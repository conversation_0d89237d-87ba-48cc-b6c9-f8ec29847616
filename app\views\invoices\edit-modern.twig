{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.edit_invoice') }} - {{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.edit_invoice') }}</h1>
            <p class="text-muted mb-0">{{ invoice.invoice_number }}</p>
        </div>
        <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    {% if invoice.status != 'draft' %}
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        {{ __('invoices.cannot_edit_sent_invoice') }}
    </div>
    {% else %}

    <form method="POST" action="{{ base_url }}/invoices/{{ invoice.id }}" id="invoiceForm" class="needs-validation" novalidate>
        <input type="hidden" name="_method" value="PUT">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <!-- Invoice Details -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>{{ __('invoices.invoice_details') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <label for="type_id" class="form-label">{{ __('invoices.invoice_type') }} *</label>
                                <select class="form-select" id="type_id" name="type_id" required>
                                    {% for type in invoice_types %}
                                        <option value="{{ type.id }}" {{ invoice.type_id == type.id ? 'selected' : '' }}>
                                            {{ type.display_name|default(type.name) }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="invoice_number" class="form-label">{{ __('invoices.invoice_number') }} *</label>
                                <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                       value="{{ invoice.invoice_number }}" required readonly>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="issue_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                                <input type="date" class="form-control" id="issue_date" name="issue_date" 
                                       value="{{ invoice.issue_date|date('Y-m-d') }}" required>
                            </div>
                            
                            <div class="col-md-12">
                                <label class="form-label">{{ __('invoices.bill_to') }}</label>
                                <div class="p-3 bg-light rounded">
                                    {% if invoice.user_id %}
                                        <i class="bi bi-person text-primary me-2"></i>
                                        <strong>{{ invoice.user.full_name|default(invoice.user.username) }}</strong>
                                        <span class="text-muted ms-2">({{ __('users.user') }})</span>
                                    {% elseif invoice.client_id %}
                                        <i class="bi bi-building text-info me-2"></i>
                                        <strong>
                                            {% if invoice.client.client_type == 'individual' %}
                                                {{ invoice.client.first_name }} {{ invoice.client.last_name }}
                                            {% else %}
                                                {{ invoice.client.company_name }}
                                            {% endif %}
                                        </strong>
                                        <span class="text-muted ms-2">({{ __('clients.client') }})</span>
                                    {% else %}
                                        <span class="text-muted">{{ __('invoices.no_recipient') }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Items -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>{{ __('invoices.invoice_items') }}</h5>
                        <div>
                            <button type="button" class="btn btn-sm btn-light me-2" id="searchProductBtn">
                                <i class="bi bi-search me-1"></i>{{ __('invoices.search_product') | default('Search Product') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-light" id="addItemBtn">
                                <i class="bi bi-plus-circle me-1"></i>{{ __('invoices.add_item') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="itemsTable">
                                <thead>
                                    <tr>
                                        <th width="40%">{{ __('invoices.description') }}</th>
                                        <th width="15%">{{ __('invoices.quantity') }}</th>
                                        <th width="15%">{{ __('invoices.unit_price') }}</th>
                                        <th width="15%">{{ __('invoices.vat_rate') }}</th>
                                        <th width="15%">{{ __('invoices.total') }}</th>
                                        <th width="50"></th>
                                    </tr>
                                </thead>
                                <tbody id="itemsBody">
                                    {% for item in invoice.items %}
                                    <tr class="invoice-item">
                                        <td>
                                            <input type="hidden" name="items[{{ loop.index0 }}][id]" value="{{ item.id }}">
                                            <input type="text" class="form-control form-control-sm item-description" 
                                                   name="items[{{ loop.index0 }}][description]" value="{{ item.description }}" required>
                                        </td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm item-quantity" 
                                                   name="items[{{ loop.index0 }}][quantity]" value="{{ item.quantity }}" 
                                                   min="1" step="0.01" required>
                                        </td>
                                        <td>
                                            <input type="number" class="form-control form-control-sm item-price" 
                                                   name="items[{{ loop.index0 }}][unit_price]" value="{{ item.unit_price }}" 
                                                   min="0" step="0.01" required>
                                        </td>
                                        <td>
                                            <select class="form-select form-select-sm item-vat" name="items[{{ loop.index0 }}][vat_rate]" required>
                                                {% for vat in vat_rates %}
                                                    <option value="{{ vat.rate }}" data-rate="{{ vat.rate }}" 
                                                            {{ item.vat_rate == vat.rate ? 'selected' : '' }}>
                                                        {{ vat.name }} ({{ vat.rate }}%)
                                                    </option>
                                                {% endfor %}
                                            </select>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control form-control-sm item-total" readonly 
                                                   value="{{ currency }}{{ item.line_total|number_format(2, '.', '') }}">
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-danger remove-item">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                        <td colspan="2">
                                            <strong id="subtotal">{{ currency }}{{ invoice.subtotal|number_format(2, '.', '') }}</strong>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                        <td colspan="2">
                                            <strong id="vatAmount">{{ currency }}{{ invoice.vat_amount|number_format(2, '.', '') }}</strong>
                                        </td>
                                    </tr>
                                    <tr id="cnsAmountRow" style="display: none;">
                                        <td colspan="4" class="text-end">
                                            <strong>{{ __('invoices.cns_base_amount') | default('CNS/Patient Amount') }}:</strong>
                                        </td>
                                        <td colspan="2">
                                            <div class="input-group input-group-sm">
                                                <span class="input-group-text">{{ currency }}</span>
                                                <input type="number" class="form-control form-control-sm" id="cns_base_amount" 
                                                       name="cns_base_amount" min="0" step="0.01" value="{{ invoice.cns_base_amount|default(0)|number_format(2, '.', '') }}">
                                            </div>
                                        </td>
                                    </tr>
                                    <tr id="secretaryFeeRow" style="display: none;">
                                        <td colspan="4" class="text-end">
                                            <strong>{{ __('invoices.secretary_fee') | default('Secretary Fee') }} (<span id="secretaryPercent">{{ secretary_percent|default(10) }}</span>%):</strong>
                                        </td>
                                        <td colspan="2">
                                            <strong id="secretaryFeeAmount">{{ currency }}{{ invoice.secretariat_vat_amount|default(0)|number_format(2, '.', '') }}</strong>
                                            <input type="hidden" id="secretary_fee_amount" name="secretary_fee_amount" value="{{ invoice.secretariat_vat_amount|default(0)|number_format(2, '.', '') }}">
                                        </td>
                                    </tr>
                                    {% if invoice.discount_amount > 0 %}
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.discount') }}:</strong></td>
                                        <td colspan="2">
                                            <strong class="text-success">-{{ currency }}{{ invoice.discount_amount|number_format(2, '.', '') }}</strong>
                                        </td>
                                    </tr>
                                    {% endif %}
                                    <tr>
                                        <td colspan="4" class="text-end"><strong>{{ __('invoices.total') }}:</strong></td>
                                        <td colspan="2">
                                            <strong id="total" class="text-primary">{{ currency }}{{ invoice.total|number_format(2, '.', '') }}</strong>
                                        </td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('invoices.additional_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="notes" class="form-label">{{ __('invoices.notes') }}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ invoice.notes }}</textarea>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="internal_notes" class="form-label">{{ __('invoices.internal_notes') }}</label>
                                <textarea class="form-control" id="internal_notes" name="internal_notes" rows="2">{{ invoice.internal_notes }}</textarea>
                                <small class="text-muted">{{ __('invoices.internal_notes_hint') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Status -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.status') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-3">
                            <span class="badge bg-secondary fs-6">{{ __('invoices.status.draft') }}</span>
                        </div>
                        <dl class="row mb-0 small">
                            <dt class="col-6">{{ __('common.created_at') }}:</dt>
                            <dd class="col-6 text-end">{{ invoice.created_at|date('d/m/Y H:i') }}</dd>
                            
                            <dt class="col-6">{{ __('common.updated_at') }}:</dt>
                            <dd class="col-6 text-end">{{ invoice.updated_at|date('d/m/Y H:i') }}</dd>
                        </dl>
                    </div>
                </div>

                <!-- Payment Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>{{ __('invoices.payment_terms') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="payment_term_id" class="form-label">{{ __('invoices.payment_terms') }}</label>
                            <select class="form-select" id="payment_term_id" name="payment_term_id">
                                <option value="">{{ __('common.select') }}</option>
                                {% for term in paymentTerms %}
                                    <option value="{{ term.id }}" data-days="{{ term.days }}" 
                                            {% if term.id == invoice.payment_term_id %}selected{% endif %}>
                                        {{ term.display_name }}
                                        {% if term.display_description %}
                                            ({{ term.display_description }})
                                        {% endif %}
                                    </option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">{{ __('config.payment_term_description_hint') }}</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="due_date" class="form-label">{{ __('invoices.due_date') }}</label>
                            <input type="date" class="form-control" id="due_date" name="due_date" 
                                   value="{{ invoice.due_date ? invoice.due_date|date('Y-m-d') : '' }}">
                            <small class="text-muted">{{ __('invoices.leave_empty_immediate') }}</small>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" name="action" value="save" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>{{ __('common.save_changes') }}
                            </button>
                            <button type="submit" name="action" value="save_and_send" class="btn btn-success">
                                <i class="bi bi-send me-2"></i>{{ __('invoices.save_and_send') }}
                            </button>
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                            </button>
                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!-- Delete Form (hidden) -->
    <form id="deleteForm" method="POST" action="{{ base_url }}/invoices/{{ invoice.id }}/delete" style="display: none;">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
    </form>

    {% endif %}
</div>

<!-- Product Search Modal -->
<div class="modal fade" id="productSearchModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.search_product') | default('Search Product') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="productSearchInput" 
                           placeholder="{{ __('invoices.search_by_name_or_code') | default('Search by name or code...') }}">
                </div>
                <div id="productSearchResults" style="max-height: 400px; overflow-y: auto;">
                    <!-- Search results will appear here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Item Template -->
<template id="itemTemplate">
    <tr class="invoice-item">
        <td>
            <input type="text" class="form-control form-control-sm item-description" name="items[INDEX][description]" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-quantity" name="items[INDEX][quantity]" value="1" min="1" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-price" name="items[INDEX][unit_price]" min="0" step="0.01" required>
        </td>
        <td>
            <select class="form-select form-select-sm item-vat" name="items[INDEX][vat_rate]" required>
                {% for vat in vat_rates %}
                    <option value="{{ vat.rate }}" data-rate="{{ vat.rate }}" {{ vat.is_default ? 'selected' : '' }}>
                        {{ vat.name }} ({{ vat.rate }}%)
                    </option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm item-total" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger remove-item">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    </tr>
</template>

<script>
let itemIndex = {{ invoice.items|length }};
const currency = '{{ currency }}';
const secretaryPercent = {{ secretary_percent|default(10) }};

document.addEventListener('DOMContentLoaded', function() {
    // Add item button
    document.getElementById('addItemBtn').addEventListener('click', addItem);
    
    // Search product button
    document.getElementById('searchProductBtn').addEventListener('click', function() {
        const modal = new bootstrap.Modal(document.getElementById('productSearchModal'));
        modal.show();
        document.getElementById('productSearchInput').focus();
    });
    
    // Product search input
    let searchTimeout;
    document.getElementById('productSearchInput').addEventListener('input', function(e) {
        clearTimeout(searchTimeout);
        const term = e.target.value.trim();
        
        if (term.length < 2) {
            document.getElementById('productSearchResults').innerHTML = '';
            return;
        }
        
        searchTimeout = setTimeout(() => searchProducts(term), 300);
    });
    
    // Initialize existing items
    document.querySelectorAll('.invoice-item').forEach(row => {
        row.querySelector('.item-quantity').addEventListener('input', () => calculateItemTotal(row));
        row.querySelector('.item-price').addEventListener('input', () => calculateItemTotal(row));
        row.querySelector('.item-vat').addEventListener('change', () => calculateItemTotal(row));
        row.querySelector('.remove-item').addEventListener('click', () => removeItem(row));
    });
    
    // Handle payment term change
    document.getElementById('payment_term_id').addEventListener('change', updateDueDate);
    
    // Handle issue date change
    document.getElementById('issue_date').addEventListener('change', updateDueDate);
    
    // Handle invoice type change to show/hide CNS fields
    document.getElementById('type_id').addEventListener('change', handleInvoiceTypeChange);
    
    // Handle CNS amount input
    document.getElementById('cns_base_amount').addEventListener('input', calculateSecretaryFee);
    
    // Initial calculation
    calculateTotals();
    
    // Check if we should show CNS fields on load
    handleInvoiceTypeChange();
});

function addItem() {
    const template = document.getElementById('itemTemplate');
    const clone = template.content.cloneNode(true);
    
    // Replace INDEX with actual index
    clone.querySelectorAll('[name*="INDEX"]').forEach(element => {
        element.name = element.name.replace('INDEX', itemIndex);
    });
    
    // Add event listeners
    const row = clone.querySelector('tr');
    row.querySelector('.item-quantity').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-price').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-vat').addEventListener('change', () => calculateItemTotal(row));
    row.querySelector('.remove-item').addEventListener('click', () => removeItem(row));
    
    document.getElementById('itemsBody').appendChild(clone);
    itemIndex++;
}

function removeItem(row) {
    if (document.querySelectorAll('.invoice-item').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('{{ __("invoices.at_least_one_item") }}');
    }
}

function calculateItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
    const price = parseFloat(row.querySelector('.item-price').value) || 0;
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex].dataset.rate) || 0;
    
    const subtotal = quantity * price;
    const vat = subtotal * (vatRate / 100);
    const total = subtotal + vat;
    
    row.querySelector('.item-total').value = currency + total.toFixed(2);
    calculateTotals();
}

function calculateTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    document.querySelectorAll('.invoice-item').forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const vatSelect = row.querySelector('.item-vat');
        const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex].dataset.rate) || 0;
        
        const itemSubtotal = quantity * price;
        const itemVat = itemSubtotal * (vatRate / 100);
        
        subtotal += itemSubtotal;
        vatAmount += itemVat;
    });
    
    const discount = {{ invoice.discount_amount|default(0) }};
    const secretaryFee = parseFloat(document.getElementById('secretary_fee_amount').value) || 0;
    const total = subtotal + vatAmount - discount + secretaryFee;
    
    document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
    document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
    document.getElementById('total').textContent = currency + total.toFixed(2);
}

function handleInvoiceTypeChange() {
    const invoiceTypeSelect = document.getElementById('type_id');
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    
    // Show CNS fields for retrocession invoices
    const showCnsFields = typeName.includes('rétrocession') || typeName.includes('retrocession');
    
    document.getElementById('cnsAmountRow').style.display = showCnsFields ? '' : 'none';
    document.getElementById('secretaryFeeRow').style.display = showCnsFields ? '' : 'none';
    
    if (!showCnsFields) {
        // Reset CNS values if not a retrocession invoice
        document.getElementById('cns_base_amount').value = '0.00';
        document.getElementById('secretary_fee_amount').value = '0.00';
        document.getElementById('secretaryFeeAmount').textContent = currency + '0.00';
        calculateTotals();
    }
}

function calculateSecretaryFee() {
    const cnsAmount = parseFloat(document.getElementById('cns_base_amount').value) || 0;
    const secretaryFee = (cnsAmount * secretaryPercent) / 100;
    
    document.getElementById('secretary_fee_amount').value = secretaryFee.toFixed(2);
    document.getElementById('secretaryFeeAmount').textContent = currency + secretaryFee.toFixed(2);
    
    calculateTotals();
}

function confirmDelete() {
    if (confirm('{{ __("invoices.delete_confirm") }}')) {
        document.getElementById('deleteForm').submit();
    }
}

function updateDueDate() {
    const paymentTermSelect = document.getElementById('payment_term_id');
    const issueDate = document.getElementById('issue_date').value;
    const dueDateInput = document.getElementById('due_date');
    
    if (!paymentTermSelect.value || !issueDate) {
        return;
    }
    
    const selectedOption = paymentTermSelect.options[paymentTermSelect.selectedIndex];
    const days = parseInt(selectedOption.getAttribute('data-days')) || 0;
    
    // Calculate due date
    const date = new Date(issueDate);
    date.setDate(date.getDate() + days);
    
    // Format as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}`;
}

// Search products
function searchProducts(term) {
    fetch(`{{ base_url }}/api/products/search?term=${encodeURIComponent(term)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProductSearchResults(data.items);
            }
        })
        .catch(error => {
            console.error('Error searching products:', error);
        });
}

// Display product search results
function displayProductSearchResults(products) {
    const container = document.getElementById('productSearchResults');
    
    if (products.length === 0) {
        container.innerHTML = '<div class="p-3 text-muted text-center">{{ __("products.no_products_found") | default("No products found") }}</div>';
        return;
    }
    
    container.innerHTML = products.map(product => `
        <div class="list-group-item list-group-item-action" style="cursor: pointer;" onclick="addProductToInvoice(${JSON.stringify(product).replace(/"/g, '&quot;')})">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${product.name}</strong>
                    <span class="text-muted">(${product.code})</span>
                    <br>
                    <small class="text-muted">${product.category || ''}</small>
                </div>
                <div class="text-end">
                    <div class="fw-bold">${currency}${product.price_formatted}</div>
                    <small class="text-muted">{{ __("common.vat") }}: ${product.vat_rate}%</small>
                </div>
            </div>
        </div>
    `).join('');
}

// Add product to invoice
function addProductToInvoice(product) {
    // Add a new row with product data
    const template = document.getElementById('itemTemplate');
    const clone = template.content.cloneNode(true);
    
    // Replace INDEX with actual index
    clone.querySelectorAll('[name*="INDEX"]').forEach(element => {
        element.name = element.name.replace('INDEX', itemIndex);
    });
    
    // Set product data
    const row = clone.querySelector('tr');
    row.querySelector('.item-description').value = product.name;
    row.querySelector('.item-quantity').value = 1;
    row.querySelector('.item-price').value = product.price;
    
    // Set VAT rate
    const vatSelect = row.querySelector('.item-vat');
    const vatOption = Array.from(vatSelect.options).find(opt => 
        parseFloat(opt.dataset.rate) === parseFloat(product.vat_rate)
    );
    if (vatOption) {
        vatSelect.value = vatOption.value;
    }
    
    // Add event listeners
    row.querySelector('.item-quantity').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-price').addEventListener('input', () => calculateItemTotal(row));
    row.querySelector('.item-vat').addEventListener('change', () => calculateItemTotal(row));
    row.querySelector('.remove-item').addEventListener('click', () => removeItem(row));
    
    document.getElementById('itemsBody').appendChild(clone);
    itemIndex++;
    
    // Calculate totals
    calculateItemTotal(document.getElementById('itemsBody').lastElementChild);
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('productSearchModal')).hide();
    document.getElementById('productSearchInput').value = '';
    document.getElementById('productSearchResults').innerHTML = '';
}

// Delete confirmation
function confirmDelete() {
    Swal.fire({
        title: '{{ __("common.are_you_sure") }}',
        text: '{{ __("invoices.delete_warning") }}',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.delete") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            // Submit the hidden delete form
            document.getElementById('deleteForm').submit();
        }
    });
}
</script>
{% endblock %}