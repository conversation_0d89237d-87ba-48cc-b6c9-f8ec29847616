<?php
/**
 * Email Management Routes
 */

use App\Services\EmailService;
use App\Services\EmailQueueService;
use App\Models\EmailTemplate;

// Email dashboard
Flight::route('GET /email/dashboard', function() {
    $user = Flight::auth()->user;
    
    // Check permissions
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $queueService = new EmailQueueService();
    $stats = $queueService->getQueueStats();
    
    Flight::render('email/dashboard', [
        'stats' => $stats,
        'user' => $user
    ]);
});

// Queue management
Flight::route('GET /api/email/queue', function() {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $page = Flight::request()->query->page ?? 1;
    $status = Flight::request()->query->status ?? null;
    
    $db = Flight::db();
    $query = "SELECT * FROM email_queue";
    $params = [];
    
    if ($status) {
        $query .= " WHERE status = :status";
        $params[':status'] = $status;
    }
    
    $query .= " ORDER BY created_at DESC LIMIT 50 OFFSET :offset";
    $params[':offset'] = ($page - 1) * 50;
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    
    Flight::json([
        'success' => true,
        'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
    ]);
});

// Process queue manually
Flight::route('POST /api/email/queue/process', function() {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $queueService = new EmailQueueService();
    $limit = Flight::request()->data->limit ?? 10;
    
    try {
        $results = $queueService->processQueue($limit);
        
        Flight::json([
            'success' => true,
            'results' => $results
        ]);
    } catch (Exception $e) {
        Flight::json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Resend failed email
Flight::route('POST /api/email/queue/:id/resend', function($id) {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $db = Flight::db();
    
    // Reset email status to pending
    $stmt = $db->prepare("
        UPDATE email_queue 
        SET status = 'pending', 
            retry_count = 0,
            scheduled_at = NOW(),
            error_message = NULL
        WHERE id = :id
    ");
    
    $stmt->execute([':id' => $id]);
    
    Flight::json([
        'success' => true,
        'message' => 'Email scheduled for resend'
    ]);
});

// Cancel queued email
Flight::route('POST /api/email/queue/:id/cancel', function($id) {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $db = Flight::db();
    
    $stmt = $db->prepare("
        UPDATE email_queue 
        SET status = 'cancelled'
        WHERE id = :id AND status = 'pending'
    ");
    
    $stmt->execute([':id' => $id]);
    
    Flight::json([
        'success' => true,
        'message' => 'Email cancelled'
    ]);
});

// Blacklist management
Flight::route('GET /api/email/blacklist', function() {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $db = Flight::db();
    $stmt = $db->query("
        SELECT b.*, u.name as added_by_name
        FROM email_blacklist b
        LEFT JOIN users u ON b.added_by = u.id
        ORDER BY b.created_at DESC
    ");
    
    Flight::json([
        'success' => true,
        'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
    ]);
});

// Add to blacklist
Flight::route('POST /api/email/blacklist', function() {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $data = Flight::request()->data;
    $queueService = new EmailQueueService();
    
    try {
        $queueService->blacklistEmail(
            $data->email,
            $data->reason ?? 'manual',
            $user['id'],
            $data->notes ?? null
        );
        
        Flight::json([
            'success' => true,
            'message' => 'Email added to blacklist'
        ]);
    } catch (Exception $e) {
        Flight::json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Remove from blacklist
Flight::route('DELETE /api/email/blacklist/:email', function($email) {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $queueService = new EmailQueueService();
    $queueService->unblacklistEmail(urldecode($email));
    
    Flight::json([
        'success' => true,
        'message' => 'Email removed from blacklist'
    ]);
});

// Test email configuration
Flight::route('POST /api/email/test', function() {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $data = Flight::request()->data;
    $emailService = new EmailService();
    
    try {
        // Send test email immediately (bypass queue)
        $result = $emailService->send([
            'to' => $data->to ?? $user['email'],
            'subject' => 'Test Email from Fit360 AdminDesk',
            'body_html' => '
                <h2>Test Email</h2>
                <p>This is a test email from your Fit360 AdminDesk email configuration.</p>
                <p>If you receive this email, your SMTP settings are correctly configured.</p>
                <hr>
                <p><small>Sent at: ' . date('Y-m-d H:i:s') . '</small></p>
            ',
            'body_text' => 'This is a test email from Fit360 AdminDesk. Your email configuration is working correctly.',
            'immediate' => true // Bypass queue for test
        ]);
        
        if ($result['success']) {
            Flight::json([
                'success' => true,
                'message' => 'Test email sent successfully'
            ]);
        } else {
            Flight::json([
                'success' => false,
                'error' => $result['message']
            ], 500);
        }
    } catch (Exception $e) {
        Flight::json([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
});

// Email logs
Flight::route('GET /api/email/logs', function() {
    $user = Flight::auth()->user;
    
    if (!in_array($user['role'], ['admin', 'super_admin'])) {
        Flight::forbidden();
    }
    
    $page = Flight::request()->query->page ?? 1;
    $search = Flight::request()->query->search ?? null;
    
    $db = Flight::db();
    $query = "SELECT * FROM email_logs";
    $params = [];
    
    if ($search) {
        $query .= " WHERE recipient_email LIKE :search OR subject LIKE :search";
        $params[':search'] = "%$search%";
    }
    
    $query .= " ORDER BY created_at DESC LIMIT 50 OFFSET :offset";
    $params[':offset'] = ($page - 1) * 50;
    
    $stmt = $db->prepare($query);
    $stmt->execute($params);
    
    Flight::json([
        'success' => true,
        'data' => $stmt->fetchAll(PDO::FETCH_ASSOC)
    ]);
});