{% extends 'base-modern.twig' %}

{% block title %}{{ __('admin.menu_configuration') }}{% endblock %}

{% block styles %}
<style>
    .menu-item {
        border-left: 3px solid transparent;
        transition: all 0.3s ease;
    }
    
    .menu-item:hover {
        background-color: var(--bs-gray-100);
        border-left-color: var(--bs-primary);
    }
    
    .submenu-item {
        padding-left: 3rem;
        font-size: 0.875rem;
    }
    
    .menu-icon {
        width: 40px;
        text-align: center;
        font-size: 1.125rem;
    }
    
    .toggle-switch {
        position: relative;
        display: inline-block;
        width: 50px;
        height: 24px;
    }
    
    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }
    
    .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    
    input:checked + .slider {
        background-color: var(--bs-success);
    }
    
    input:checked + .slider:before {
        transform: translateX(26px);
    }
    
    input:disabled + .slider {
        opacity: 0.5;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('common.home') }}</a></li>
<li class="breadcrumb-item"><a href="{{ base_url }}/admin">{{ __('admin.admin_tools') }}</a></li>
<li class="breadcrumb-item active">{{ __('admin.menu_configuration') }}</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ __('admin.menu_configuration') }}</h3>
                <div class="card-tools">
                    <button type="button" class="btn btn-success btn-sm" id="save-menu-config">
                        <i class="bi bi-save me-1"></i> {{ __('common.save_changes') }}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <i class="bi bi-info-circle me-2"></i>
                    {{ __('admin.menu_config_info') }}
                </div>
                
                <form id="menu-config-form">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th width="50">{{ __('common.icon') }}</th>
                                    <th>{{ __('admin.menu_item') }}</th>
                                    <th width="200">{{ __('admin.visibility') }}</th>
                                    <th width="100" class="text-center">{{ __('common.enabled') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in menuItems %}
                                    <tr class="menu-item">
                                        <td class="menu-icon">
                                            <i class="{{ item.icon }}"></i>
                                        </td>
                                        <td>
                                            <strong>{{ __(item.title) }}</strong>
                                            {% if item.admin_only %}
                                                <span class="badge bg-danger ms-2">{{ __('admin.admin_only') }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="text-muted">{{ __('admin.visible_to_all') }}</span>
                                        </td>
                                        <td class="text-center">
                                            <label class="toggle-switch">
                                                <input type="checkbox" 
                                                       name="menu[{{ item.id }}]" 
                                                       value="1" 
                                                       {% if item.enabled %}checked{% endif %}
                                                       {% if item.id == 'dashboard' %}disabled{% endif %}>
                                                <span class="slider"></span>
                                            </label>
                                        </td>
                                    </tr>
                                    
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Preview Card -->
        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">{{ __('admin.preview') }}</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h5>{{ __('admin.enabled_items') }}</h5>
                        <ul class="list-unstyled" id="enabled-items">
                            <!-- Will be populated by JavaScript -->
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>{{ __('admin.disabled_items') }}</h5>
                        <ul class="list-unstyled text-muted" id="disabled-items">
                            <!-- Will be populated by JavaScript -->
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h5>{{ __('admin.admin_only_items') }}</h5>
                        <ul class="list-unstyled text-warning" id="admin-items">
                            <!-- Will be populated by JavaScript -->
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Update preview on change
    function updatePreview() {
        var enabledItems = [];
        var disabledItems = [];
        var adminItems = [];
        
        $('#menu-config-form input[type="checkbox"]').each(function() {
            var $row = $(this).closest('tr');
            var itemName = $row.find('td:eq(1)').text().trim();
            var isAdmin = $row.find('.badge-danger').length > 0;
            var isEnabled = $(this).is(':checked');
            
            if (isAdmin) {
                adminItems.push('<li><i class="bi bi-shield-lock me-2"></i>' + itemName + '</li>');
            } else if (isEnabled) {
                enabledItems.push('<li><i class="bi bi-check-circle me-2 text-success"></i>' + itemName + '</li>');
            } else {
                disabledItems.push('<li><i class="bi bi-x-circle me-2 text-danger"></i>' + itemName + '</li>');
            }
        });
        
        $('#enabled-items').html(enabledItems.join(''));
        $('#disabled-items').html(disabledItems.join(''));
        $('#admin-items').html(adminItems.join(''));
    }
    
    // Initial preview
    updatePreview();
    
    // Update preview on change
    $('#menu-config-form input[type="checkbox"]').on('change', updatePreview);
    
    // Save configuration
    $('#save-menu-config').on('click', function() {
        var $btn = $(this);
        $btn.prop('disabled', true).html('<i class="bi bi-hourglass-split me-1"></i> {{ __("common.saving") }}...');
        
        var formData = $('#menu-config-form').serialize();
        
        $.ajax({
            url: '{{ base_url }}/admin/menu-config/save',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.success) {
                    toastr.success(response.message || '{{ __("admin.menu_config_saved") }}');
                    // Reload page to reflect changes
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message || '{{ __("common.error_occurred") }}');
                }
            },
            error: function() {
                toastr.error('{{ __("common.error_occurred") }}');
            },
            complete: function() {
                $btn.prop('disabled', false).html('<i class="bi bi-save me-1"></i> {{ __("common.save_changes") }}');
            }
        });
    });
    
    // Handle parent-child relationships
    $('input[type="checkbox"]').on('change', function() {
        var $row = $(this).closest('tr');
        if ($row.hasClass('menu-item') && $row.next().hasClass('submenu-item')) {
            // This is a parent item
            var isChecked = $(this).is(':checked');
            var $nextRow = $row.next();
            
            // Disable/enable all child items based on parent state
            while ($nextRow.length && $nextRow.hasClass('submenu-item')) {
                $nextRow.find('input[type="checkbox"]').prop('disabled', !isChecked);
                if (!isChecked) {
                    $nextRow.find('input[type="checkbox"]').prop('checked', false);
                }
                $nextRow = $nextRow.next();
            }
        }
    });
    
    // Initialize parent-child relationships
    $('tr.menu-item').each(function() {
        var $checkbox = $(this).find('input[type="checkbox"]');
        if (!$checkbox.is(':checked') && $(this).next().hasClass('submenu-item')) {
            var $nextRow = $(this).next();
            while ($nextRow.length && $nextRow.hasClass('submenu-item')) {
                $nextRow.find('input[type="checkbox"]').prop('disabled', true);
                $nextRow = $nextRow.next();
            }
        }
    });
});
</script>
{% endblock %}