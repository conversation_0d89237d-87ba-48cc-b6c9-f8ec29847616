<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html>
<html>
<head>
    <title>Cleanup Duplicate Invoice Types</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .action-buttons { margin: 20px 0; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }
        .btn-danger { background-color: #dc3545; }
        .btn-success { background-color: #28a745; }
        .btn-info { background-color: #17a2b8; }
    </style>
</head>
<body>
    <h1>Cleanup Duplicate Invoice Types</h1>";
    
    if (!isset($_GET['confirm'])) {
        // Show current state and what will be done
        echo "<h2>Current State Analysis</h2>";
        
        // Show duplicates
        echo "<h3>Duplicate Location Types:</h3>";
        $stmt = $pdo->query("
            SELECT id, code, name, prefix, 
                   (SELECT COUNT(*) FROM invoices WHERE type_id = config_invoice_types.id) as invoice_count
            FROM config_invoice_types 
            WHERE code IN ('LOC', 'loca', 'location')
            ORDER BY id
        ");
        $locTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th><th>Used by Invoices</th><th>Action</th></tr>";
        foreach ($locTypes as $type) {
            $name = json_decode($type['name'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $type['name']) : $type['name'];
            $action = ($type['id'] == 12) ? '<span class="success">KEEP</span>' : '<span class="error">DELETE</span>';
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>{$displayName}</td>";
            echo "<td>{$type['prefix']}</td>";
            echo "<td>{$type['invoice_count']}</td>";
            echo "<td>{$action}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Duplicate Loyer Types:</h3>";
        $stmt = $pdo->query("
            SELECT id, code, name, prefix, 
                   (SELECT COUNT(*) FROM invoices WHERE type_id = config_invoice_types.id) as invoice_count
            FROM config_invoice_types 
            WHERE code IN ('rent', 'rental', 'loyer', 'loye', 'loy')
            ORDER BY id
        ");
        $loyTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th><th>Used by Invoices</th><th>Action</th></tr>";
        foreach ($loyTypes as $type) {
            $name = json_decode($type['name'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $type['name']) : $type['name'];
            if ($type['id'] == 1) {
                $action = '<span class="success">KEEP (update code to "loyer")</span>';
            } else {
                $action = '<span class="error">DELETE</span>';
            }
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>{$displayName}</td>";
            echo "<td>{$type['prefix']}</td>";
            echo "<td>{$type['invoice_count']}</td>";
            echo "<td>{$action}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>Duplicate Retrocession Types:</h3>";
        $stmt = $pdo->query("
            SELECT id, code, name, prefix, 
                   (SELECT COUNT(*) FROM invoices WHERE type_id = config_invoice_types.id) as invoice_count
            FROM config_invoice_types 
            WHERE code LIKE 'ret%' OR prefix LIKE 'RET%'
            ORDER BY id
        ");
        $retTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th><th>Used by Invoices</th><th>Action</th></tr>";
        foreach ($retTypes as $type) {
            $name = json_decode($type['name'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $type['name']) : $type['name'];
            
            if ($type['id'] == 37) {
                $action = '<span class="success">KEEP (update code to "retrocession_25")</span>';
            } elseif ($type['id'] == 38) {
                $action = '<span class="success">KEEP (update code to "retrocession_30")</span>';
            } else {
                $action = '<span class="error">DELETE</span>';
            }
            
            echo "<tr>";
            echo "<td>{$type['id']}</td>";
            echo "<td>{$type['code']}</td>";
            echo "<td>{$displayName}</td>";
            echo "<td>{$type['prefix']}</td>";
            echo "<td>{$type['invoice_count']}</td>";
            echo "<td>{$action}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h2>What This Script Will Do:</h2>";
        echo "<ol>";
        echo "<li><strong>Location Types:</strong> Keep ID 12 (LOC), delete ID 42 (loca)</li>";
        echo "<li><strong>Loyer Types:</strong> Keep ID 1 and update code to 'loyer', delete ID 39</li>";
        echo "<li><strong>Retrocession Types:</strong> Keep IDs 37 & 38 with updated codes, delete IDs 28 & 40</li>";
        echo "<li>Update all invoices to use the kept types</li>";
        echo "<li>Update config mappings</li>";
        echo "</ol>";
        
        echo "<div class='action-buttons'>";
        echo "<a href='?confirm=1' class='btn btn-danger'>Execute Cleanup</a>";
        echo "<a href='/fit/public/test-user-invoice-generation.php' class='btn btn-info'>Cancel</a>";
        echo "</div>";
        
    } else {
        // Execute cleanup
        echo "<h2>Executing Cleanup...</h2>";
        
        $pdo->beginTransaction();
        
        try {
            // 1. Clean up Location types
            echo "<h3>Cleaning Location Types:</h3>";
            
            // Update invoices using ID 42 to use ID 12
            $stmt = $pdo->prepare("UPDATE invoices SET type_id = 12 WHERE type_id = 42");
            $stmt->execute();
            $count = $stmt->rowCount();
            echo "<p>✅ Updated {$count} invoices from type 42 to type 12</p>";
            
            // Delete duplicate location type
            $stmt = $pdo->prepare("DELETE FROM config_invoice_types WHERE id = 42");
            $stmt->execute();
            echo "<p>✅ Deleted duplicate location type (ID 42)</p>";
            
            // 2. Clean up Loyer types
            echo "<h3>Cleaning Loyer Types:</h3>";
            
            // First, update invoices using ID 39 to use ID 1
            $stmt = $pdo->prepare("UPDATE invoices SET type_id = 1 WHERE type_id = 39");
            $stmt->execute();
            $count = $stmt->rowCount();
            echo "<p>✅ Updated {$count} invoices from type 39 to type 1</p>";
            
            // Delete duplicate loyer type FIRST
            $stmt = $pdo->prepare("DELETE FROM config_invoice_types WHERE id = 39");
            $stmt->execute();
            echo "<p>✅ Deleted duplicate loyer type (ID 39)</p>";
            
            // NOW update code for ID 1 (after duplicate is gone)
            $stmt = $pdo->prepare("UPDATE config_invoice_types SET code = 'loyer' WHERE id = 1");
            $stmt->execute();
            echo "<p>✅ Updated type ID 1 code from 'rent' to 'loyer'</p>";
            
            // 3. Clean up Retrocession types
            echo "<h3>Cleaning Retrocession Types:</h3>";
            
            // Update codes for kept types
            $stmt = $pdo->prepare("UPDATE config_invoice_types SET code = 'retrocession_25' WHERE id = 37");
            $stmt->execute();
            echo "<p>✅ Updated type ID 37 code to 'retrocession_25'</p>";
            
            $stmt = $pdo->prepare("UPDATE config_invoice_types SET code = 'retrocession_30' WHERE id = 38");
            $stmt->execute();
            echo "<p>✅ Updated type ID 38 code to 'retrocession_30'</p>";
            
            // Update invoices using deleted types
            $stmt = $pdo->prepare("UPDATE invoices SET type_id = 37 WHERE type_id IN (28, 40)");
            $stmt->execute();
            $count = $stmt->rowCount();
            echo "<p>✅ Updated {$count} invoices from types 28/40 to type 37</p>";
            
            // Delete duplicate retrocession types
            $stmt = $pdo->prepare("DELETE FROM config_invoice_types WHERE id IN (28, 40)");
            $stmt->execute();
            echo "<p>✅ Deleted duplicate retrocession types (IDs 28, 40)</p>";
            
            // 4. Update config mappings
            echo "<h3>Updating Config Mappings:</h3>";
            
            $mappings = [
                ['key' => 'ret_invoice_type', 'value' => 'retrocession_25'],
                ['key' => 'loy_invoice_type', 'value' => 'loyer'],
                ['key' => 'loc_invoice_type', 'value' => 'LOC']
            ];
            
            foreach ($mappings as $config) {
                $stmt = $pdo->prepare("
                    INSERT INTO config (`key`, `value`, created_at, updated_at)
                    VALUES (:key, :value, NOW(), NOW())
                    ON DUPLICATE KEY UPDATE `value` = :value, updated_at = NOW()
                ");
                $stmt->execute($config);
                echo "<p>✅ Updated config: {$config['key']} = {$config['value']}</p>";
            }
            
            $pdo->commit();
            
            echo "<h2 class='success'>✅ Cleanup Complete!</h2>";
            
            // Show final state
            echo "<h3>Final Invoice Types:</h3>";
            $stmt = $pdo->query("
                SELECT id, code, name, prefix, color
                FROM config_invoice_types 
                WHERE is_active = 1
                ORDER BY code
            ");
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $name = json_decode($row['name'], true);
                $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td><strong>{$row['code']}</strong></td>";
                echo "<td>{$displayName}</td>";
                echo "<td>{$row['prefix']}</td>";
                echo "</tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "<p class='error'>❌ Error during cleanup: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "<p>Transaction rolled back - no changes were made.</p>";
        }
        
        echo "<div class='action-buttons'>";
        echo "<a href='/fit/public/test-user-invoice-generation.php' class='btn btn-success'>Test Invoice Generation</a>";
        echo "<a href='/fit/public/check-invoice-types-config.php' class='btn btn-info'>Check Invoice Types</a>";
        echo "</div>";
    }
    
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "<p class='error'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}