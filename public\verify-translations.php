<?php
/**
 * Translation Verification Script
 * This script checks all translations files and verifies the translation system is working correctly
 */

// Initialize the application
require_once dirname(__DIR__) . '/vendor/autoload.php';
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

use App\Helpers\Language;

// Security check - only allow admins
session_start();
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    header('HTTP/1.0 403 Forbidden');
    die('Access denied. Admin privileges required.');
}

// Get all available languages
$languages = Language::getAvailableLanguages();
$langDir = dirname(__DIR__) . '/app/lang';

// Function to scan language files
function scanLanguageFiles($language) {
    $langDir = dirname(__DIR__) . '/app/lang/' . $language;
    $files = [];
    
    if (is_dir($langDir)) {
        $phpFiles = glob($langDir . '/*.php');
        foreach ($phpFiles as $file) {
            $fileName = basename($file, '.php');
            $translations = include $file;
            
            if (is_array($translations)) {
                $files[$fileName] = [
                    'path' => $file,
                    'count' => count($translations, COUNT_RECURSIVE) - count($translations),
                    'keys' => array_keys($translations)
                ];
            }
        }
    }
    
    return $files;
}

// Function to count translations recursively
function countTranslations($array) {
    $count = 0;
    foreach ($array as $value) {
        if (is_array($value)) {
            $count += countTranslations($value);
        } else {
            $count++;
        }
    }
    return $count;
}

// Scan all languages
$languageData = [];
$allFiles = [];

foreach ($languages as $lang) {
    $files = scanLanguageFiles($lang);
    $languageData[$lang] = $files;
    
    // Collect all unique file names
    foreach ($files as $fileName => $data) {
        if (!in_array($fileName, $allFiles)) {
            $allFiles[] = $fileName;
        }
    }
}

// Sort file names
sort($allFiles);

// Test translation functions
$testResults = [];

// Test 1: Basic translation
$testResults['basic'] = [
    'test' => '__("common.save")',
    'result' => __('common.save'),
    'expected' => 'Should return the translation for "Save" button'
];

// Test 2: Translation with parameters
$testResults['params'] = [
    'test' => '__("common.welcome_user", ["name" => "John"])',
    'result' => __('common.welcome_user', ['name' => 'John']),
    'expected' => 'Should replace :name with John'
];

// Test 3: Missing translation
$testResults['missing'] = [
    'test' => '__("nonexistent.key")',
    'result' => __('nonexistent.key'),
    'expected' => 'Should return formatted missing key'
];

// Test 4: Fallback language
$originalLang = Language::getCurrentLanguage();
Language::setLanguage('de'); // German might have missing translations
$testResults['fallback'] = [
    'test' => 'German: __("common.save")',
    'result' => __('common.save'),
    'expected' => 'Should fallback to English if German translation missing'
];
Language::setLanguage($originalLang);

// Test 5: Language switching
Language::setLanguage('fr');
$frenchSave = __('common.save');
Language::setLanguage('en');
$englishSave = __('common.save');
Language::setLanguage($originalLang);

$testResults['switching'] = [
    'test' => 'Language switching',
    'result' => "FR: $frenchSave, EN: $englishSave",
    'expected' => 'Different results for different languages'
];

// Check for missing files
$missingFiles = [];
foreach ($allFiles as $file) {
    foreach ($languages as $lang) {
        if (!isset($languageData[$lang][$file])) {
            if (!isset($missingFiles[$lang])) {
                $missingFiles[$lang] = [];
            }
            $missingFiles[$lang][] = $file;
        }
    }
}

// Get missing translations from the log
$missingTranslations = Language::getMissingTranslations();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Translation System Verification</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .translation-grid {
            overflow-x: auto;
        }
        .translation-table {
            font-size: 0.9em;
        }
        .translation-table th {
            position: sticky;
            top: 0;
            background: #fff;
            z-index: 10;
        }
        .missing-file {
            background-color: #ffebee;
        }
        .complete-file {
            background-color: #e8f5e9;
        }
        .test-pass {
            color: #2e7d32;
            font-weight: bold;
        }
        .test-fail {
            color: #c62828;
            font-weight: bold;
        }
        .lang-header {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .file-count {
            font-size: 0.85em;
            color: #666;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .summary-card {
            border-left: 4px solid #2196F3;
        }
        .coverage-bar {
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 5px 0;
        }
        .coverage-fill {
            height: 100%;
            background-color: #4caf50;
            text-align: center;
            line-height: 20px;
            color: white;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="mb-4">Translation System Verification</h1>
        
        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card summary-card">
                    <div class="card-body">
                        <h5 class="card-title">Languages</h5>
                        <h2><?php echo count($languages); ?></h2>
                        <p class="text-muted mb-0"><?php echo implode(', ', array_map('strtoupper', $languages)); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card summary-card">
                    <div class="card-body">
                        <h5 class="card-title">Translation Files</h5>
                        <h2><?php echo count($allFiles); ?></h2>
                        <p class="text-muted mb-0">Unique files across all languages</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card summary-card">
                    <div class="card-body">
                        <h5 class="card-title">Current Language</h5>
                        <h2><?php echo strtoupper(Language::getCurrentLanguage()); ?></h2>
                        <p class="text-muted mb-0">Fallback: <?php echo strtoupper(Language::$fallbackLanguage ?? 'en'); ?></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card summary-card">
                    <div class="card-body">
                        <h5 class="card-title">Missing Keys Logged</h5>
                        <h2><?php 
                            $totalMissing = 0;
                            foreach ($missingTranslations as $keys) {
                                $totalMissing += count($keys);
                            }
                            echo $totalMissing;
                        ?></h2>
                        <p class="text-muted mb-0">Across all languages</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Function Tests -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">Translation Function Tests</h3>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Test</th>
                            <th>Result</th>
                            <th>Expected</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($testResults as $testName => $test): ?>
                        <tr>
                            <td><code><?php echo htmlspecialchars($test['test']); ?></code></td>
                            <td><strong><?php echo htmlspecialchars($test['result']); ?></strong></td>
                            <td><small><?php echo htmlspecialchars($test['expected']); ?></small></td>
                            <td>
                                <?php 
                                $pass = true;
                                if ($testName === 'missing' && strpos($test['result'], '[') !== 0) {
                                    $pass = false;
                                } elseif ($testName === 'basic' && empty($test['result'])) {
                                    $pass = false;
                                }
                                ?>
                                <span class="<?php echo $pass ? 'test-pass' : 'test-fail'; ?>">
                                    <?php echo $pass ? '✓ PASS' : '✗ FAIL'; ?>
                                </span>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Translation Coverage -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">Translation File Coverage</h3>
            </div>
            <div class="card-body">
                <div class="translation-grid">
                    <table class="table table-bordered translation-table">
                        <thead>
                            <tr>
                                <th>File</th>
                                <?php foreach ($languages as $lang): ?>
                                <th class="text-center lang-header">
                                    <?php echo strtoupper($lang); ?>
                                    <div class="file-count">
                                        <?php echo count($languageData[$lang]); ?> files
                                    </div>
                                </th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($allFiles as $file): ?>
                            <tr>
                                <td><strong><?php echo $file; ?>.php</strong></td>
                                <?php foreach ($languages as $lang): ?>
                                <td class="text-center <?php echo isset($languageData[$lang][$file]) ? 'complete-file' : 'missing-file'; ?>">
                                    <?php if (isset($languageData[$lang][$file])): ?>
                                        <span class="badge bg-success">
                                            <?php echo $languageData[$lang][$file]['count']; ?> keys
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Missing</span>
                                    <?php endif; ?>
                                </td>
                                <?php endforeach; ?>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Missing Files Report -->
        <?php if (!empty($missingFiles)): ?>
        <div class="card mb-4">
            <div class="card-header bg-warning">
                <h3 class="mb-0">Missing Translation Files</h3>
            </div>
            <div class="card-body">
                <?php foreach ($missingFiles as $lang => $files): ?>
                <div class="mb-3">
                    <h5><?php echo strtoupper($lang); ?> is missing:</h5>
                    <ul>
                        <?php foreach ($files as $file): ?>
                        <li><code><?php echo $file; ?>.php</code></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Language Coverage Statistics -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">Language Coverage Statistics</h3>
            </div>
            <div class="card-body">
                <?php foreach ($languages as $lang): ?>
                <div class="mb-4">
                    <h5><?php echo strtoupper($lang); ?> Language</h5>
                    <?php 
                    $totalFiles = count($allFiles);
                    $langFiles = count($languageData[$lang]);
                    $coverage = ($langFiles / $totalFiles) * 100;
                    ?>
                    <div class="coverage-bar">
                        <div class="coverage-fill" style="width: <?php echo $coverage; ?>%">
                            <?php echo round($coverage, 1); ?>% (<?php echo $langFiles; ?>/<?php echo $totalFiles; ?> files)
                        </div>
                    </div>
                    
                    <?php if ($langFiles > 0): ?>
                    <small class="text-muted">
                        Total translations: <?php 
                        $total = 0;
                        foreach ($languageData[$lang] as $file) {
                            $total += $file['count'];
                        }
                        echo number_format($total);
                        ?> keys
                    </small>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Recently Logged Missing Translations -->
        <?php if (!empty($missingTranslations)): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">Recently Logged Missing Translations</h3>
            </div>
            <div class="card-body">
                <?php foreach ($missingTranslations as $lang => $keys): ?>
                <div class="mb-3">
                    <h5><?php echo strtoupper($lang); ?></h5>
                    <pre><?php echo htmlspecialchars(implode("\n", array_slice($keys, 0, 10))); ?><?php 
                    if (count($keys) > 10) echo "\n... and " . (count($keys) - 10) . " more";
                    ?></pre>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Sample Translations -->
        <div class="card mb-4">
            <div class="card-header">
                <h3 class="mb-0">Sample Translations</h3>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Key</th>
                            <?php foreach ($languages as $lang): ?>
                            <th><?php echo strtoupper($lang); ?></th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php 
                        // Sample keys to test
                        $sampleKeys = [
                            'common.save',
                            'common.cancel',
                            'common.edit',
                            'common.delete',
                            'users.username',
                            'auth.login',
                            'dashboard.title',
                            'products.name',
                            'invoices.number',
                            'pos.title'
                        ];
                        
                        foreach ($sampleKeys as $key):
                        ?>
                        <tr>
                            <td><code><?php echo $key; ?></code></td>
                            <?php foreach ($languages as $lang): ?>
                            <td>
                                <?php 
                                Language::setLanguage($lang);
                                $translation = __($key);
                                echo htmlspecialchars($translation);
                                ?>
                            </td>
                            <?php endforeach; ?>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
                <?php 
                // Restore original language
                Language::setLanguage($originalLang);
                ?>
            </div>
        </div>

        <!-- Debug Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="mb-0">Debug Information</h3>
            </div>
            <div class="card-body">
                <dl class="row">
                    <dt class="col-sm-3">Language Directory</dt>
                    <dd class="col-sm-9"><code><?php echo $langDir; ?></code></dd>
                    
                    <dt class="col-sm-3">Session Language</dt>
                    <dd class="col-sm-9"><code><?php echo $_SESSION['user_language'] ?? 'Not set'; ?></code></dd>
                    
                    <dt class="col-sm-3">Missing Log File</dt>
                    <dd class="col-sm-9"><code><?php echo dirname(__DIR__) . '/storage/logs/missing_translations.log'; ?></code></dd>
                    
                    <dt class="col-sm-3">Cache Status</dt>
                    <dd class="col-sm-9">
                        <?php 
                        $cacheEnabled = Language::$cacheEnabled ?? false;
                        echo $cacheEnabled ? '<span class="badge bg-success">Enabled</span>' : '<span class="badge bg-secondary">Disabled</span>';
                        ?>
                    </dd>
                </dl>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>