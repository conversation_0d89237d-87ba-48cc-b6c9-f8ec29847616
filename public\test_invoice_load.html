<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Invoice Page Load Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        h2 { color: #333; }
        code {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        th {
            background: #e9ecef;
        }
    </style>
</head>
<body>
    <h1>🧪 Invoice Page Load Test</h1>

    <div class="test-section">
        <h2>What Should Happen on Page Load</h2>
        
        <h3>1. Regular Invoice (default)</h3>
        <table>
            <tr>
                <th>Field</th>
                <th>Expected Value</th>
                <th>Reason</th>
            </tr>
            <tr>
                <td>Billable Type</td>
                <td><code>client</code> (Clients)</td>
                <td>Default for regular invoices</td>
            </tr>
            <tr>
                <td>Billable Options</td>
                <td>List of clients</td>
                <td>Loaded automatically after billable type is set</td>
            </tr>
            <tr>
                <td>Issue Date</td>
                <td>Today's date</td>
                <td>Set by Twig template: <code>{{ 'now'|date('Y-m-d') }}</code></td>
            </tr>
            <tr>
                <td>Subject</td>
                <td>Empty (placeholder shows)</td>
                <td>User fills this in</td>
            </tr>
            <tr>
                <td>Invoice Items</td>
                <td>1 empty row</td>
                <td>Default behavior</td>
            </tr>
        </table>

        <h3>2. Retrocession Invoice (?type=retrocession_25 or retrocession_30)</h3>
        <table>
            <tr>
                <th>Field</th>
                <th>Expected Value</th>
                <th>Reason</th>
            </tr>
            <tr>
                <td>Billable Type</td>
                <td><code>user</code> (Utilisateurs)</td>
                <td>Auto-set for retrocession</td>
            </tr>
            <tr>
                <td>Billable Options</td>
                <td>List of practitioners only</td>
                <td>Filtered to show practitioners</td>
            </tr>
            <tr>
                <td>Subject</td>
                <td>RÉTROCESSION</td>
                <td>Auto-filled by initialization</td>
            </tr>
            <tr>
                <td>Period</td>
                <td>Previous month in French</td>
                <td>e.g., "JUIN 2025"</td>
            </tr>
            <tr>
                <td>Invoice Items</td>
                <td>2-3 special rows (CNS, Patient, Secretary)</td>
                <td>Special retrocession structure</td>
            </tr>
        </table>

        <h3>3. Location Invoice (?type=location)</h3>
        <table>
            <tr>
                <th>Field</th>
                <th>Expected Value</th>
                <th>Reason</th>
            </tr>
            <tr>
                <td>Billable Type</td>
                <td><code>user</code> (Utilisateurs)</td>
                <td>Auto-set for location</td>
            </tr>
            <tr>
                <td>Billable Options</td>
                <td>List of coaches only</td>
                <td>Filtered to show coaches</td>
            </tr>
            <tr>
                <td>Subject</td>
                <td>LOCATION SALLE</td>
                <td>Auto-filled by initialization</td>
            </tr>
            <tr>
                <td>Period</td>
                <td>Previous month in French</td>
                <td>e.g., "JUIN 2025"</td>
            </tr>
            <tr>
                <td>Invoice Items</td>
                <td>Empty initially</td>
                <td>Courses added when coach selected</td>
            </tr>
        </table>
    </div>

    <div class="test-section">
        <h2>🔍 Troubleshooting Empty Fields</h2>
        
        <h3>If Billable dropdown is empty:</h3>
        <ol>
            <li>Check browser console for errors</li>
            <li>Verify billable type is selected (Client/User)</li>
            <li>Check that <code>loadBillableOptions()</code> was called</li>
            <li>Verify the API endpoints are working:
                <ul>
                    <li>Clients data: Check if <code>clientsData</code> array is populated</li>
                    <li>Users data: Check if <code>usersData</code> array is populated</li>
                </ul>
            </li>
        </ol>

        <h3>If Subject is empty on special invoices:</h3>
        <ol>
            <li>Check URL has correct type parameter</li>
            <li>Verify initialization function ran:
                <ul>
                    <li>Retrocession: <code>initializeRetrocessionInvoiceComplete()</code></li>
                    <li>Location: <code>initializeLocationInvoiceComplete()</code></li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>🚀 Quick Test Links</h2>
        <p>Click these to test each invoice type:</p>
        <ul>
            <li><a href="/fit/public/invoices/create" target="_blank">Regular Invoice</a> - Should default to client type</li>
            <li><a href="/fit/public/invoices/create?type=retrocession_30" target="_blank">Retrocession 30%</a> - Should show practitioners</li>
            <li><a href="/fit/public/invoices/create?type=retrocession_25" target="_blank">Retrocession 25%</a> - Should show practitioners</li>
            <li><a href="/fit/public/invoices/create?type=location" target="_blank">Location Invoice</a> - Should show coaches</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>📋 Console Commands for Debugging</h2>
        <p>Run these in browser console to check data:</p>
        <pre>
// Check loaded data
console.log('Clients:', clientsData.length);
console.log('Users:', usersData.length);
console.log('Coaches:', coachesData.length);
console.log('Practitioners:', practitionersData.length);

// Check billable type
console.log('Billable type:', document.getElementById('billable_type').value);
console.log('Billable options:', document.getElementById('billable_id').options.length);

// Force reload billable options
loadBillableOptions();

// Check invoice type
const invoiceType = document.getElementById('invoice_type_id');
const selectedOption = invoiceType.options[invoiceType.selectedIndex];
console.log('Invoice type:', selectedOption.textContent, 'Code:', selectedOption.getAttribute('data-prefix'));
        </pre>
    </div>
</body>
</html>