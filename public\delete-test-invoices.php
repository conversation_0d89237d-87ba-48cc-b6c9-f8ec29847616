<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html>
<html>
<head>
    <title>Delete Test Invoices</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        table { border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .btn { display: inline-block; padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; color: white; }
        .btn-danger { background-color: #dc3545; }
        .btn-success { background-color: #28a745; }
    </style>
</head>
<body>
    <h1>Delete Test Invoices</h1>";
    
    $userId = 1; // Frank Huet
    $month = 7;
    $year = 2025;
    
    // First, show existing invoices
    echo "<h2>Invoices for User ID $userId (July 2025):</h2>";
    
    $stmt = $pdo->prepare("
        SELECT i.id, i.invoice_number, i.issue_date, i.total, 
               i.financial_month, i.financial_year, it.code, it.name
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.user_id = :user_id 
        AND i.financial_month = :month 
        AND i.financial_year = :year
        ORDER BY i.id DESC
    ");
    $stmt->execute([
        'user_id' => $userId,
        'month' => $month,
        'year' => $year
    ]);
    
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($invoices)) {
        echo "<p class='info'>No invoices found for this period.</p>";
    } else {
        echo "<table>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Type</th><th>Issue Date</th><th>Total</th></tr>";
        
        foreach ($invoices as $invoice) {
            $name = json_decode($invoice['name'], true);
            $typeName = is_array($name) ? ($name['fr'] ?? $invoice['code']) : $invoice['code'];
            
            echo "<tr>";
            echo "<td>{$invoice['id']}</td>";
            echo "<td>{$invoice['invoice_number']}</td>";
            echo "<td>{$typeName}</td>";
            echo "<td>{$invoice['issue_date']}</td>";
            echo "<td>{$invoice['total']}€</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (isset($_GET['confirm'])) {
            echo "<h2>Deleting Invoices...</h2>";
            
            foreach ($invoices as $invoice) {
                // First delete from user_generated_invoices if exists
                $stmt = $pdo->prepare("
                    DELETE FROM user_generated_invoices 
                    WHERE invoice_id = :invoice_id
                ");
                $stmt->execute(['invoice_id' => $invoice['id']]);
                
                // Delete invoice lines
                $stmt = $pdo->prepare("DELETE FROM invoice_lines WHERE invoice_id = :id");
                $stmt->execute(['id' => $invoice['id']]);
                
                // Delete invoice items
                $stmt = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = :id");
                $stmt->execute(['id' => $invoice['id']]);
                
                // Delete the invoice
                $stmt = $pdo->prepare("DELETE FROM invoices WHERE id = :id");
                $stmt->execute(['id' => $invoice['id']]);
                
                echo "<p class='success'>✅ Deleted invoice: {$invoice['invoice_number']}</p>";
            }
            
            // Also check and clean up user_generated_invoices table
            $stmt = $pdo->prepare("
                DELETE FROM user_generated_invoices 
                WHERE user_id = :user_id 
                AND period_month = :month 
                AND period_year = :year
            ");
            $stmt->execute([
                'user_id' => $userId,
                'month' => $month,
                'year' => $year
            ]);
            
            echo "<h2 class='success'>✅ All test invoices deleted!</h2>";
            echo "<a href='/fit/public/test-user-invoice-generation.php' class='btn btn-success'>Test Invoice Generation</a>";
            
        } else {
            echo "<h2>⚠️ Confirm Deletion</h2>";
            echo "<p>This will delete " . count($invoices) . " invoice(s) for July 2025.</p>";
            echo "<a href='?confirm=1' class='btn btn-danger'>Delete All Test Invoices</a>";
            echo " ";
            echo "<a href='/fit/public/test-user-invoice-generation.php' class='btn btn-success'>Cancel</a>";
        }
    }
    
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "<p class='error'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}