<?php

use App\Services\PermissionService;

/**
 * Check if current user has a specific permission
 * 
 * @param string $permission Permission code (e.g., 'users.create', 'invoices.delete')
 * @return bool
 */
function hasPermission($permission)
{
    return PermissionService::getInstance()->hasPermission($permission);
}

/**
 * Check if current user has any of the given permissions
 * 
 * @param array|string $permissions Array of permission codes or comma-separated string
 * @return bool
 */
function hasAnyPermission($permissions)
{
    if (is_string($permissions)) {
        $permissions = array_map('trim', explode(',', $permissions));
    }
    return PermissionService::getInstance()->hasAnyPermission($permissions);
}

/**
 * Check if current user has all the given permissions
 * 
 * @param array|string $permissions Array of permission codes or comma-separated string
 * @return bool
 */
function hasAllPermissions($permissions)
{
    if (is_string($permissions)) {
        $permissions = array_map('trim', explode(',', $permissions));
    }
    return PermissionService::getInstance()->hasAllPermissions($permissions);
}

/**
 * Get all permissions for the current user
 * 
 * @return array
 */
function getUserPermissions()
{
    return PermissionService::getInstance()->getUserPermissions();
}

/**
 * Check if user is super admin
 * 
 * @param int|null $userId User ID or null for current user
 * @return bool
 */
function isSuperAdmin($userId = null)
{
    return PermissionService::getInstance()->isSuperAdmin($userId);
}

/**
 * Require permission or throw exception
 * 
 * @param string $permission Permission code
 * @param string $message Custom error message
 * @throws \Exception
 */
function requirePermission($permission, $message = null)
{
    if (!hasPermission($permission)) {
        $message = $message ?: "You don't have permission to perform this action. Required: {$permission}";
        throw new \Exception($message, 403);
    }
}

/**
 * Require any of the given permissions or throw exception
 * 
 * @param array|string $permissions Array of permission codes or comma-separated string
 * @param string $message Custom error message
 * @throws \Exception
 */
function requireAnyPermission($permissions, $message = null)
{
    if (!hasAnyPermission($permissions)) {
        $permissionsList = is_array($permissions) ? implode(', ', $permissions) : $permissions;
        $message = $message ?: "You don't have permission to perform this action. Required one of: {$permissionsList}";
        throw new \Exception($message, 403);
    }
}

/**
 * Require all of the given permissions or throw exception
 * 
 * @param array|string $permissions Array of permission codes or comma-separated string
 * @param string $message Custom error message
 * @throws \Exception
 */
function requireAllPermissions($permissions, $message = null)
{
    if (!hasAllPermissions($permissions)) {
        $permissionsList = is_array($permissions) ? implode(', ', $permissions) : $permissions;
        $message = $message ?: "You don't have permission to perform this action. Required all of: {$permissionsList}";
        throw new \Exception($message, 403);
    }
}

/**
 * Clear permission cache for a user
 * 
 * @param int|null $userId User ID or null for all users
 */
function clearPermissionCache($userId = null)
{
    PermissionService::getInstance()->clearPermissionCache($userId);
}

/**
 * Get permission status for multiple permissions at once
 * Useful for UI elements that need to check many permissions
 * 
 * @param array $permissions Array of permission codes
 * @return array Associative array with permission => bool
 */
function checkPermissions(array $permissions)
{
    $result = [];
    foreach ($permissions as $permission) {
        $result[$permission] = hasPermission($permission);
    }
    return $result;
}

/**
 * Check if user can perform an action on a module
 * 
 * @param string $module Module code (e.g., 'users', 'invoices')
 * @param string $action Action (e.g., 'create', 'update', 'delete', 'view')
 * @return bool
 */
function canPerformAction($module, $action)
{
    $permission = $module . '.' . $action;
    return hasPermission($permission);
}

/**
 * Get user's permissions organized by module
 * 
 * @return array
 */
function getUserPermissionsByModule()
{
    $permissions = getUserPermissions();
    $organized = [];
    
    foreach ($permissions as $perm) {
        $moduleCode = $perm['module_code'];
        if (!isset($organized[$moduleCode])) {
            $organized[$moduleCode] = [
                'module_name' => $perm['module_name'],
                'permissions' => []
            ];
        }
        $organized[$moduleCode]['permissions'][] = $perm['permission_code'];
    }
    
    return $organized;
}