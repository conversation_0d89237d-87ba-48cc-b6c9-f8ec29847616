# Invoice Month/Year Selection Feature

## Overview

The Fit360 AdminDesk system now supports flexible month and year selection for invoice generation directly from user profiles. This feature allows administrators and managers to generate invoices for any valid month, not just the current or previous month.

## Features

### 1. Month/Year Selectors

Each invoice type in the user profile now has dedicated month/year selectors:

- **Retrocession (RET) Invoices**
  - Month dropdown with French month names
  - Year dropdown (current year -2 to +1)
  - Default: Previous month (e.g., in July, defaults to June)
  
- **<PERSON><PERSON> (LOY) Invoices**
  - Month dropdown integrated with existing year navigation
  - Uses the year selector already present in financial obligations
  - Default: Current month
  
- **Course/Location (LOC) Invoices**
  - Month and year dropdowns
  - Default: Current month and year

### 2. Future Month Validation

The system prevents generation of invoices for future months:
- Client-side validation in JavaScript
- Shows alert message: "Vous ne pouvez pas générer une facture pour un mois futur"
- Validation applies to all three invoice types

### 3. User Interface

#### Location in User Profile
1. Navigate to Users > Edit User
2. Go to the "Financial" tab
3. Find the relevant section:
   - Monthly Retrocession Amounts (for RET)
   - Financial Obligations (for LOY)
   - Monthly Course Counts (for LOC)

#### Visual Design
- Bootstrap form-select styling
- Compact dropdowns with auto-width
- Clear visual grouping with invoice generation button
- French month names for better user experience

## Implementation Details

### JavaScript Functions

#### `generateRetrocessionInvoice(userId)`
```javascript
// Reads selected month/year from dropdowns
const selectedMonth = parseInt(document.getElementById('retInvoiceMonth').value);
const selectedYear = parseInt(document.getElementById('retInvoiceYear').value);

// Validates not future month
if (selectedYear > currentYear || (selectedYear === currentYear && selectedMonth > currentMonth)) {
    alert('Vous ne pouvez pas générer une facture pour un mois futur');
    return;
}
```

#### `generateLoyerInvoiceWithMonth(userId)`
```javascript
// Uses month dropdown and year from obligations display
const selectedMonth = parseInt(document.getElementById('loyInvoiceMonth').value);
const selectedYear = parseInt(document.getElementById('obligationsYearDisplay').textContent);
```

#### `generateCourseInvoice(userId)`
```javascript
// Reads both month and year from dedicated dropdowns
const selectedMonth = parseInt(document.getElementById('locInvoiceMonth').value);
const selectedYear = parseInt(document.getElementById('locInvoiceYear').value);
```

### Backend Compatibility

No backend changes were required:
- All controller methods already accept month/year parameters
- UnifiedInvoiceGenerator service handles date logic
- Existing API endpoints remain unchanged

## Testing

### Test Script Location
`/public/test-user-invoice-generation.php`

### Test Features
1. Month/year selection in test interface
2. Tests all three invoice types with custom dates
3. Future month validation testing
4. Shows detailed results for each test

### How to Test
1. Access the test script in your browser
2. Select a user or enter user ID
3. Choose invoice type (or leave blank for all)
4. Select month and year
5. Click "Test"

The script will:
- Validate prerequisites (amounts, obligations, courses)
- Attempt invoice generation
- Show success/failure with detailed messages
- For future months, verify validation works correctly

## Business Logic

### Month Selection Rules

1. **Retrocession (RET)**
   - Default: Previous month
   - Logic: Retrocession for work done in previous month
   - Example: In July, generate June retrocession

2. **Loyer (LOY)**
   - Default: Current month
   - Logic: Rent for current month
   - Notes: Changed from "next month" to "current month"

3. **Location (LOC)**
   - Default: Current month
   - Logic: Courses given in current month
   - Based on monthly course counts

### Data Dependencies

For successful invoice generation, ensure:

1. **RET**: Monthly amounts configured for selected month
2. **LOY**: Active financial obligations for selected period
3. **LOC**: Course counts entered for selected month

## Troubleshooting

### Common Issues

1. **"Cannot generate future invoice" alert**
   - User selected a future month
   - Solution: Select current month or earlier

2. **"No data for period" error**
   - Missing configuration for selected month
   - Solution: Configure amounts/obligations/courses for that month

3. **Invoice already exists**
   - Invoice previously generated for that period
   - Solution: Delete existing invoice first if regeneration needed

### Debugging

Check browser console for:
- Selected month/year values
- AJAX request details
- Server response messages

## Future Enhancements

Potential improvements:
1. Bulk generation for multiple months
2. Month range selection
3. Automatic detection of missing months
4. Visual calendar interface
5. Server-side future month validation