<?php
/**
 * <PERSON><PERSON>t to automatically generate monthly rental invoices from user financial obligations
 * 
 * This script can be run manually or scheduled as a cron job
 * Usage: php generate_monthly_invoices.php [month] [year]
 * Example: php generate_monthly_invoices.php 01 2025
 * 
 * If no month/year provided, it will use the current month
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set the base path
define('BASE_PATH', __DIR__);

// Load the application bootstrap
require_once BASE_PATH . '/app/config/bootstrap.php';

// Start session (needed for user context)
session_start();

// Set admin user context for created_by fields
$_SESSION['user_id'] = 1; // Admin user

use App\Services\MonthlyInvoiceGenerator;

// Get command line arguments or use current month
$month = isset($argv[1]) && isset($argv[2]) ? $argv[2] . '-' . str_pad($argv[1], 2, '0', STR_PAD_LEFT) : date('Y-m');

echo "=== Monthly Rental Invoice Generation ===\n";
echo "Month: " . $month . "\n\n";

try {
    // Create invoice generator instance
    $generator = new MonthlyInvoiceGenerator();
    
    // Get preview first
    echo "Loading users with financial obligations...\n";
    $preview = $generator->getPreview($month);
    
    if (empty($preview)) {
        echo "No users found with financial obligations.\n";
        exit(0);
    }
    
    // Display preview
    echo "Found " . count($preview) . " users with financial obligations:\n\n";
    
    $toGenerate = [];
    $totalAmount = 0;
    
    foreach ($preview as $user) {
        echo sprintf("- %s (%s): %.2f EUR", 
            $user['user_name'], 
            $user['email'], 
            $user['total']
        );
        
        if ($user['invoice_exists']) {
            echo " [ALREADY EXISTS]\n";
        } else {
            echo " [TO GENERATE]\n";
            $toGenerate[] = $user['user_id'];
            $totalAmount += $user['total'];
        }
    }
    
    echo "\n";
    echo "Summary:\n";
    echo "- Total users: " . count($preview) . "\n";
    echo "- To generate: " . count($toGenerate) . "\n";
    echo "- Already exist: " . (count($preview) - count($toGenerate)) . "\n";
    echo "- Total amount: " . number_format($totalAmount, 2) . " EUR\n\n";
    
    if (empty($toGenerate)) {
        echo "No invoices to generate. All users already have invoices for this period.\n";
        exit(0);
    }
    
    // Ask for confirmation if running interactively
    if (php_sapi_name() === 'cli' && !isset($argv[3])) {
        echo "Do you want to generate " . count($toGenerate) . " invoices? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        if (trim($line) !== 'y') {
            echo "Cancelled.\n";
            exit(0);
        }
        fclose($handle);
    }
    
    // Generate invoices
    echo "\nGenerating invoices...\n";
    $results = $generator->generateMonthlyInvoices($month, $toGenerate);
    
    // Display results
    echo "\n=== Generation Results ===\n";
    echo "Successfully generated: " . $results['success'] . "\n";
    echo "Errors: " . $results['errors'] . "\n";
    
    if (!empty($results['invoices'])) {
        echo "\nGenerated invoices:\n";
        foreach ($results['invoices'] as $invoice) {
            echo sprintf("- %s: %s (%.2f EUR)\n", 
                $invoice['number'],
                $invoice['user'],
                $invoice['amount']
            );
        }
    }
    
    if (!empty($results['messages'])) {
        echo "\nMessages:\n";
        foreach ($results['messages'] as $message) {
            echo "- " . $message . "\n";
        }
    }
    
    echo "\nDone!\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}