<?php
/**
 * <PERSON><PERSON><PERSON> to fix user ID 18 data that was incorrectly inserted
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $db = Flight::db();
    
    // First, let's see what data we have for user 18
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 18");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "User ID 18 not found.\n";
        exit;
    }
    
    echo "Current data for user ID 18:\n";
    echo "username: " . $user['username'] . "\n";
    echo "email: " . $user['email'] . "\n";
    echo "first_name: " . $user['first_name'] . "\n";
    echo "last_name: " . $user['last_name'] . "\n";
    echo "address: " . $user['address'] . "\n";
    echo "postal_code: " . $user['postal_code'] . "\n";
    echo "city: " . $user['city'] . "\n";
    echo "country: " . $user['country'] . "\n";
    echo "\n";
    
    // Based on the HTML output, it looks like:
    // first_name has "remi" (correct)
    // last_name has "<EMAIL>" (should be email)
    // username has password hash
    // email has "Rémi" (should be first name)
    
    // Let's fix it
    echo "Fixing user data...\n";
    
    // Correct mapping based on what we see:
    $correctData = [
        'username' => 'remi',  // Generate proper username
        'email' => '<EMAIL>',  // From last_name field
        'first_name' => 'Rémi',  // From email field
        'last_name' => 'Lamy',  // We'll use a default or ask user to update
        'address' => '15, am Pëtz',  // Default address
        'postal_code' => 'L-9579',  // Default postal code
        'city' => 'Weidingen',  // Default city
        'country' => 'LU',  // Default country
        'language' => 'fr',  // From what was in address field
        'timezone' => 'Europe/Luxembourg',  // From what was in postal_code field
        'is_active' => 1  // From what was in city field
    ];
    
    // Update the user
    $updateFields = [];
    $updateValues = [];
    foreach ($correctData as $field => $value) {
        $updateFields[] = "$field = ?";
        $updateValues[] = $value;
    }
    $updateValues[] = 18; // Add ID for WHERE clause
    
    $updateQuery = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    echo "Update query: $updateQuery\n";
    echo "Values: " . json_encode($updateValues) . "\n\n";
    
    $stmt = $db->prepare($updateQuery);
    $stmt->execute($updateValues);
    
    echo "User ID 18 has been fixed!\n\n";
    
    // Verify the fix
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 18");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Updated data for user ID 18:\n";
    echo "username: " . $user['username'] . "\n";
    echo "email: " . $user['email'] . "\n";
    echo "first_name: " . $user['first_name'] . "\n";
    echo "last_name: " . $user['last_name'] . "\n";
    echo "address: " . $user['address'] . "\n";
    echo "postal_code: " . $user['postal_code'] . "\n";
    echo "city: " . $user['city'] . "\n";
    echo "country: " . $user['country'] . "\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}