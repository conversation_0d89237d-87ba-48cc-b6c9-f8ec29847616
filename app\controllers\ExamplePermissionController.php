<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Services\PermissionService;
use App\Middleware\PermissionMiddleware;

/**
 * Example controller demonstrating permission usage
 * 
 * This is a reference implementation showing different ways to check permissions
 */
class ExamplePermissionController extends Controller
{
    private PermissionService $permissionService;
    
    public function __construct()
    {
        parent::__construct();
        $this->permissionService = PermissionService::getInstance();
    }
    
    /**
     * Example 1: Check permission in controller method
     */
    public function viewProtectedData(Request $request, Response $response)
    {
        // Method 1: Direct permission check
        if (!$this->permissionService->hasPermission('reports.financial')) {
            $_SESSION['flash']['error'] = 'You do not have permission to view financial reports.';
            return $response->redirect('/dashboard');
        }
        
        // User has permission, show the data
        return $this->render('reports/financial', [
            'title' => 'Financial Reports',
            'data' => $this->getFinancialData()
        ]);
    }
    
    /**
     * Example 2: Check multiple permissions (ANY)
     */
    public function manageContent(Request $request, Response $response)
    {
        // User needs at least one of these permissions
        $requiredPermissions = ['content.create', 'content.update', 'content.delete'];
        
        if (!$this->permissionService->hasAnyPermission($requiredPermissions)) {
            return $response->json([
                'success' => false,
                'message' => 'You need content management permissions to access this feature.'
            ], 403);
        }
        
        // Check specific permissions for UI elements
        $canCreate = $this->permissionService->hasPermission('content.create');
        $canUpdate = $this->permissionService->hasPermission('content.update');
        $canDelete = $this->permissionService->hasPermission('content.delete');
        
        return $this->render('content/manage', [
            'title' => 'Content Management',
            'permissions' => [
                'create' => $canCreate,
                'update' => $canUpdate,
                'delete' => $canDelete
            ]
        ]);
    }
    
    /**
     * Example 3: Check multiple permissions (ALL)
     */
    public function systemMaintenance(Request $request, Response $response)
    {
        // User needs ALL of these permissions
        $requiredPermissions = ['system.maintenance', 'system.backup', 'system.restore'];
        
        if (!$this->permissionService->hasAllPermissions($requiredPermissions)) {
            $_SESSION['flash']['error'] = 'You need all system maintenance permissions to access this feature.';
            return $response->redirect('/dashboard');
        }
        
        return $this->render('system/maintenance', [
            'title' => 'System Maintenance'
        ]);
    }
    
    /**
     * Example 4: Super admin only action
     */
    public function dangerousOperation(Request $request, Response $response)
    {
        if (!$this->permissionService->isSuperAdmin()) {
            $_SESSION['flash']['error'] = 'This action is restricted to super administrators only.';
            return $response->redirect('/dashboard');
        }
        
        // Perform dangerous operation
        return $this->render('system/dangerous', [
            'title' => 'Dangerous Operation'
        ]);
    }
    
    /**
     * Example 5: Dynamic permission based on resource
     */
    public function editResource(Request $request, Response $response, $resourceType, $resourceId)
    {
        // Build permission dynamically based on resource type
        $permission = $resourceType . '.update';
        
        if (!$this->permissionService->hasPermission($permission)) {
            return $response->json([
                'success' => false,
                'message' => "You don't have permission to edit {$resourceType}."
            ], 403);
        }
        
        // Additional check: Can user edit this specific resource?
        if (!$this->canEditSpecificResource($resourceType, $resourceId)) {
            return $response->json([
                'success' => false,
                'message' => 'You can only edit your own resources.'
            ], 403);
        }
        
        // Process the edit
        return $response->json(['success' => true]);
    }
    
    /**
     * Example 6: Permission-aware UI rendering
     */
    public function dashboard(Request $request, Response $response)
    {
        // Get user's permissions for UI customization
        $userPermissions = $this->permissionService->getUserPermissions();
        
        // Build menu items based on permissions
        $menuItems = [];
        
        if ($this->permissionService->hasPermission('users.view')) {
            $menuItems[] = ['label' => 'Users', 'url' => '/users', 'icon' => 'fa-users'];
        }
        
        if ($this->permissionService->hasPermission('invoices.view')) {
            $menuItems[] = ['label' => 'Invoices', 'url' => '/invoices', 'icon' => 'fa-file-invoice'];
        }
        
        if ($this->permissionService->hasPermission('reports.view')) {
            $menuItems[] = ['label' => 'Reports', 'url' => '/reports', 'icon' => 'fa-chart-bar'];
        }
        
        return $this->render('dashboard', [
            'title' => 'Dashboard',
            'menuItems' => $menuItems,
            'permissions' => $userPermissions
        ]);
    }
    
    /**
     * Example 7: API endpoint with permission check
     */
    public function apiCreateItem(Request $request, Response $response)
    {
        // For API endpoints, return JSON error
        if (!$this->permissionService->hasPermission('api.items.create')) {
            return $response->json([
                'success' => false,
                'error' => 'Unauthorized',
                'message' => 'You do not have permission to create items via API.',
                'required_permission' => 'api.items.create'
            ], 403);
        }
        
        // Validate and create item
        $data = $request->getBody();
        
        // ... validation and creation logic ...
        
        return $response->json([
            'success' => true,
            'message' => 'Item created successfully',
            'id' => $newItemId
        ]);
    }
    
    /**
     * Helper method to check if user can edit specific resource
     */
    private function canEditSpecificResource($resourceType, $resourceId)
    {
        // Example: Users can edit their own profiles regardless of permission
        if ($resourceType === 'profile' && $resourceId == $_SESSION['user_id']) {
            return true;
        }
        
        // Example: Check if resource belongs to user's department
        // This would involve additional database queries
        
        return false;
    }
    
    /**
     * Example route definitions using middleware
     * 
     * Add these to your routes file:
     * 
     * // Single permission required
     * Flight::route('GET /admin/users', 
     *     PermissionMiddleware::require('users.view'),
     *     [ExamplePermissionController::class, 'viewUsers']
     * );
     * 
     * // Multiple permissions (ANY)
     * Flight::route('POST /admin/content', 
     *     PermissionMiddleware::requireAny(['content.create', 'content.update']),
     *     [ExamplePermissionController::class, 'manageContent']
     * );
     * 
     * // Multiple permissions (ALL)
     * Flight::route('POST /admin/system/maintenance', 
     *     PermissionMiddleware::requireAll(['system.maintenance', 'system.backup']),
     *     [ExamplePermissionController::class, 'systemMaintenance']
     * );
     * 
     * // Super admin only
     * Flight::route('DELETE /admin/system/reset', 
     *     PermissionMiddleware::superAdminOnly(),
     *     [ExamplePermissionController::class, 'dangerousOperation']
     * );
     * 
     * // API endpoint
     * Flight::route('POST /api/items', 
     *     PermissionMiddleware::requireApi('api.items.create'),
     *     [ExamplePermissionController::class, 'apiCreateItem']
     * );
     */
}