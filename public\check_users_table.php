<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Check users table structure
$stmt = $db->query("SHOW CREATE TABLE users");
$result = $stmt->fetch(PDO::FETCH_ASSOC);
echo "<pre>";
echo htmlspecialchars($result['Create Table']);
echo "</pre>";

// Also check column details
echo "\n<h3>Users table columns:</h3><pre>";
$stmt = $db->query("SHOW COLUMNS FROM users");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    if ($row['Field'] == 'id') {
        print_r($row);
    }
}
echo "</pre>";
?>