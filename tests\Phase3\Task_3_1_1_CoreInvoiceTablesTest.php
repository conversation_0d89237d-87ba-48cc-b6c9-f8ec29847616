<?php

namespace Tests\Phase3;

use PDO;
use Exception;

class Task_3_1_1_CoreInvoiceTablesTest
{
    private $db;
    private $passed = 0;
    private $failed = 0;
    
    public function setUp(): void
    {
        try {
            // Database connection
            $this->db = new PDO('mysql:host=localhost;dbname=fitapp', 'root', 'test1234');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (Exception $e) {
            die("Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    private function assertEquals($expected, $actual, $message = '')
    {
        if ($expected == $actual) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: $expected, Got: $actual)");
        }
    }
    
    private function assertTrue($condition, $message = '')
    {
        if ($condition) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: true, Got: false)");
        }
    }
    
    private function assertFalse($condition, $message = '')
    {
        if (!$condition) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: false, Got: true)");
        }
    }
    
    private function assertContains($needle, $haystack, $message = '')
    {
        if (in_array($needle, $haystack)) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ('$needle' not found in array)");
        }
    }
    
    private function assertStringContainsString($needle, $haystack, $message = '')
    {
        if (strpos($haystack, $needle) !== false) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ('$needle' not found in string)");
        }
    }
    
    /**
     * Test 1: Verify table creation
     */
    public function testInvoiceTablesExist()
    {
        // Check invoices table exists
        $stmt = $this->db->query("SHOW TABLES LIKE 'invoices'");
        $this->assertEquals(1, $stmt->rowCount(), "invoices table should exist");
        
        // Check invoice_lines table exists
        $stmt = $this->db->query("SHOW TABLES LIKE 'invoice_lines'");
        $this->assertEquals(1, $stmt->rowCount(), "invoice_lines table should exist");
        
        // Check invoice_retrocessions table exists
        $stmt = $this->db->query("SHOW TABLES LIKE 'invoice_retrocessions'");
        $this->assertEquals(1, $stmt->rowCount(), "invoice_retrocessions table should exist");
        
        // Check invoice_sequences table exists
        $stmt = $this->db->query("SHOW TABLES LIKE 'invoice_sequences'");
        $this->assertEquals(1, $stmt->rowCount(), "invoice_sequences table should exist");
        
        echo "✓ All required tables exist\n";
    }
    
    /**
     * Test 2: Test invoice creation with all fields
     */
    public function testCreateInvoiceWithAllFields()
    {
        // First, ensure we have a client
        $stmt = $this->db->query("SELECT id FROM clients LIMIT 1");
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$client) {
            // Create a test client
            $stmt = $this->db->prepare("INSERT INTO clients (type, name, email) VALUES ('individual', 'Test Client', '<EMAIL>')");
            $stmt->execute();
            $clientId = $this->db->lastInsertId();
        } else {
            $clientId = $client['id'];
        }
        
        // Create invoice
        $sql = "INSERT INTO invoices (
            invoice_number, invoice_type, client_id, issue_date, due_date,
            subtotal, vat_amount, total, secretariat_vat_amount,
            secretariat_vat_note_shown, status
        ) VALUES (
            :invoice_number, :invoice_type, :client_id, :issue_date, :due_date,
            :subtotal, :vat_amount, :total, :secretariat_vat_amount,
            :secretariat_vat_note_shown, :status
        )";
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            ':invoice_number' => '2025-FIT-001',
            ':invoice_type' => 'rental',
            ':client_id' => $clientId,
            ':issue_date' => '2025-01-15',
            ':due_date' => '2025-01-15',
            ':subtotal' => 1500.00,
            ':vat_amount' => 170.00,
            ':total' => 1670.00,
            ':secretariat_vat_amount' => 170.00,
            ':secretariat_vat_note_shown' => 1,
            ':status' => 'draft'
        ]);
        
        $this->assertTrue($result, "Invoice should be created successfully");
        $invoiceId = $this->db->lastInsertId();
        
        // Verify the invoice was created
        $stmt = $this->db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('2025-FIT-001', $invoice['invoice_number']);
        $this->assertEquals(1670.00, $invoice['total']);
        $this->assertEquals(1, $invoice['secretariat_vat_note_shown']);
        
        echo "✓ Invoice created with all fields\n";
        
        return $invoiceId;
    }
    
    /**
     * Test 3: Test VAT calculation formula
     */
    public function testVATCalculationFormula()
    {
        // Get or create an invoice
        $stmt = $this->db->query("SELECT id FROM invoices LIMIT 1");
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        $invoiceId = $invoice ? $invoice['id'] : $this->testCreateInvoiceWithAllFields();
        
        // Create invoice line
        $sql = "INSERT INTO invoice_lines (
            invoice_id, line_type, description, unit_price, quantity,
            vat_rate, line_total
        ) VALUES (
            :invoice_id, :line_type, :description, :unit_price, :quantity,
            :vat_rate, :line_total
        )";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':invoice_id' => $invoiceId,
            ':line_type' => 'secretariat',
            ':description' => 'Frais secrétariat',
            ':unit_price' => 1000.00,
            ':quantity' => 1,
            ':vat_rate' => 17,
            ':line_total' => 1170.00
        ]);
        
        $lineId = $this->db->lastInsertId();
        
        // Fetch the line to check the calculated VAT
        $stmt = $this->db->prepare("SELECT * FROM invoice_lines WHERE id = ?");
        $stmt->execute([$lineId]);
        $line = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // VAT = Amount - (Amount / 1.17)
        $expectedVAT = 1170.00 - (1170.00 / 1.17);
        $actualVAT = $line['vat_amount'];
        
        $this->assertEquals(round($expectedVAT, 2), round($actualVAT, 2), "VAT calculation should be correct");
        
        echo "✓ VAT calculation formula works correctly\n";
        echo "  Expected VAT: " . round($expectedVAT, 2) . "\n";
        echo "  Actual VAT: " . round($actualVAT, 2) . "\n";
    }
    
    /**
     * Test 4: Test draft period functionality
     */
    public function testDraftPeriodLocking()
    {
        // Create invoice with draft_until
        $stmt = $this->db->query("SELECT id FROM clients LIMIT 1");
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        $clientId = $client['id'];
        
        $sql = "INSERT INTO invoices (
            invoice_number, client_id, total, draft_until, status,
            issue_date, due_date, subtotal, vat_amount
        ) VALUES (
            :invoice_number, :client_id, :total, :draft_until, :status,
            :issue_date, :due_date, :subtotal, :vat_amount
        )";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':invoice_number' => 'TEST-DRAFT-001',
            ':client_id' => $clientId,
            ':total' => 100.00,
            ':draft_until' => date('Y-m-d H:i:s', strtotime('+48 hours')),
            ':status' => 'draft',
            ':issue_date' => date('Y-m-d'),
            ':due_date' => date('Y-m-d'),
            ':subtotal' => 100.00,
            ':vat_amount' => 0.00
        ]);
        
        $invoiceId = $this->db->lastInsertId();
        
        // Check if invoice can be edited (draft_until is in future)
        $stmt = $this->db->prepare("SELECT draft_until, locked_at FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $canEdit = is_null($invoice['locked_at']) && 
                   (is_null($invoice['draft_until']) || strtotime($invoice['draft_until']) > time());
        
        $this->assertTrue($canEdit, "Invoice should be editable when draft_until is in future");
        
        // Lock the invoice
        $stmt = $this->db->prepare("UPDATE invoices SET locked_at = NOW() WHERE id = ?");
        $stmt->execute([$invoiceId]);
        
        // Check again
        $stmt = $this->db->prepare("SELECT locked_at FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $canEdit = is_null($invoice['locked_at']);
        $this->assertFalse($canEdit, "Invoice should not be editable when locked");
        
        echo "✓ Draft period locking works correctly\n";
    }
    
    /**
     * Test 5: Check all required columns exist
     */
    public function testInvoiceTableColumns()
    {
        $requiredColumns = [
            'invoice_type', 'invoice_type_id', 'template_id', 'profile_id',
            'secretariat_vat_amount', 'secretariat_vat_note_shown',
            'cns_reference', 'cns_document_id', 'email_template_used',
            'draft_until', 'locked_at', 'locked_by', 'reference_invoice_id',
            'credit_reason'
        ];
        
        $stmt = $this->db->query("SHOW COLUMNS FROM invoices");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in invoices table");
        }
        
        echo "✓ All required columns exist in invoices table\n";
    }
    
    /**
     * Test 6: Check invoice_lines table structure
     */
    public function testInvoiceLinesTableStructure()
    {
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_lines");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[$row['Field']] = $row;
        }
        
        // Check line_type enum values
        $this->assertStringContainsString('enum', $columns['line_type']['Type']);
        $this->assertStringContainsString('secretariat', $columns['line_type']['Type']);
        $this->assertStringContainsString('cns_part', $columns['line_type']['Type']);
        $this->assertStringContainsString('patient_part', $columns['line_type']['Type']);
        
        // Check vat_amount is generated column
        $this->assertEquals('STORED GENERATED', $columns['vat_amount']['Extra']);
        
        echo "✓ invoice_lines table structure is correct\n";
    }
    
    /**
     * Test 7: Check retrocession table
     */
    public function testRetrocessionTableStructure()
    {
        $requiredColumns = [
            'invoice_id', 'total_amount', 'cns_amount', 'patient_amount',
            'cns_percentage', 'patient_percentage', 'secretariat_percentage',
            'secretariat_tvac', 'secretariat_htva', 'vat_amount',
            'has_overrides', 'override_notes'
        ];
        
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_retrocessions");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in invoice_retrocessions table");
        }
        
        echo "✓ invoice_retrocessions table structure is correct\n";
    }
    
    /**
     * Run all tests and provide summary
     */
    public function runAllTests()
    {
        echo "\n=== Task 3.1.1: Core Invoice Tables Tests ===\n\n";
        
        $tests = [
            'testInvoiceTablesExist' => 'Checking if all tables exist',
            'testCreateInvoiceWithAllFields' => 'Testing invoice creation',
            'testVATCalculationFormula' => 'Testing VAT calculation',
            'testDraftPeriodLocking' => 'Testing draft period locking',
            'testInvoiceTableColumns' => 'Checking invoice table columns',
            'testInvoiceLinesTableStructure' => 'Checking invoice_lines structure',
            'testRetrocessionTableStructure' => 'Checking retrocession structure'
        ];
        
        foreach ($tests as $method => $description) {
            echo "\n$description...\n";
            try {
                $this->$method();
            } catch (Exception $e) {
                echo "❌ FAILED: " . $e->getMessage() . "\n";
                continue;
            }
        }
        
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "Test Results: {$this->passed} passed, {$this->failed} failed\n";
        
        if ($this->failed === 0) {
            echo "\n✅ ALL TESTS PASSED for Task 3.1.1\n";
            echo "\nCommon errors to check:\n";
            echo "- Ensure all migrations have been run\n";
            echo "- Check that generated columns are supported (MySQL 5.7+)\n";
            echo "- Verify foreign key constraints are satisfied\n";
            echo "- Ensure proper character encoding (utf8mb4)\n";
        } else {
            echo "\n❌ SOME TESTS FAILED\n";
            echo "Please check the error messages above and:\n";
            echo "1. Ensure migration 015_enhance_invoices_phase3.sql has been run\n";
            echo "2. Check your MySQL version (needs 5.7+ for generated columns)\n";
            echo "3. Verify database connection settings\n";
        }
        
        return $this->failed === 0;
    }
}

// Run tests if executed directly
if (php_sapi_name() === 'cli' && basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    $test = new Task_3_1_1_CoreInvoiceTablesTest();
    $test->setUp();
    $test->runAllTests();
}