#!/bin/bash

# Script to clean up temporary debugging scripts from public directory

PUBLIC_DIR="/mnt/d/wamp64/www/fit/public"
TOOLS_DIR="/mnt/d/wamp64/www/fit/tools"
ARCHIVE_DIR="/mnt/d/wamp64/www/fit/tools/archive"

echo "Starting cleanup of temporary scripts..."

# Create archive directory
mkdir -p "$ARCHIVE_DIR/deleted_scripts"

# Move useful maintenance scripts to tools directory
echo "Moving useful maintenance scripts..."

# Invoice maintenance scripts
mkdir -p "$TOOLS_DIR/maintenance/invoice"
for script in check_invoice_sequences.php check_invoice_structure.php check_invoice_lines.php \
              check_duplicate_invoice_lines.php fix_duplicate_invoice_lines.php \
              clear_invoice_cache.php find_recent_invoices.php check_sequence_and_invoices.php \
              setup_invoice_sequence.php check_invoice_types_prefixes.php update_invoice_type_prefixes.php; do
    if [ -f "$PUBLIC_DIR/$script" ]; then
        echo "Moving $script to maintenance/invoice/"
        mv "$PUBLIC_DIR/$script" "$TOOLS_DIR/maintenance/invoice/"
    fi
done

# Diagnostic scripts
mkdir -p "$TOOLS_DIR/maintenance/diagnostics"
for script in check_column_types.php check_config_table.php check_invoice_table_structure.php \
              check_document_types.php check_payment_terms_data.php debug_payment_terms.php; do
    if [ -f "$PUBLIC_DIR/$script" ]; then
        echo "Moving $script to maintenance/diagnostics/"
        mv "$PUBLIC_DIR/$script" "$TOOLS_DIR/maintenance/diagnostics/"
    fi
done

# User management scripts
mkdir -p "$TOOLS_DIR/maintenance/user-management"
for script in add_users_to_coach_group.php add_coaches_to_group.php create_coach_users.php \
              capitalize_usernames.php check_user_addresses.php add_address_to_all_users.php; do
    if [ -f "$PUBLIC_DIR/$script" ]; then
        echo "Moving $script to maintenance/user-management/"
        mv "$PUBLIC_DIR/$script" "$TOOLS_DIR/maintenance/user-management/"
    fi
done

# Archive (don't delete) one-time fix scripts and test scripts
echo "Archiving one-time fix scripts and test scripts..."
for script in fix_*.php reset_*.php force_*.php update_invoice_sequence.php \
              debug_invoice_*.php diagnose_invoice_*.php test_*.php \
              run_migration_*.php invoice-creation-fix-summary.php \
              invoice-pdf-debug*.php debug_*.php simple_check.php \
              payment_terms_test.html test-*.html add_invoice_items.php \
              check_invoice_submission.php check_invoice_format.php \
              check_invoice_items_table.php check_invoice_lines_structure.php \
              check_payment_terms_translation.php check_pdf_columns.php \
              check_user_address_fields.php check_sequence_issue.php \
              check_all_invoices.php check_draft_invoices.php \
              check_duplicate_lines.php check_invoice.php check_invoice_187.php \
              check_invoice_238_details.php check_invoice_db.php check_invoices.php \
              test_and_fix_translations.php test_translation.php; do
    if [ -f "$PUBLIC_DIR/$script" ]; then
        echo "Archiving $script"
        mv "$PUBLIC_DIR/$script" "$ARCHIVE_DIR/deleted_scripts/"
    fi
done

# Clean up backup and log files
echo "Removing backup and log files..."
rm -f "$PUBLIC_DIR"/*.backup-*
rm -f "$PUBLIC_DIR"/*.log

echo "Cleanup complete!"
echo ""
echo "Summary:"
echo "- Useful maintenance scripts moved to: $TOOLS_DIR/maintenance/"
echo "- One-time scripts archived to: $ARCHIVE_DIR/deleted_scripts/"
echo "- Core files kept in public: index.php, install.php, invoice-pdf.php"