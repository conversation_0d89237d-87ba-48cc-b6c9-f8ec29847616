<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment
if (file_exists(dirname(__DIR__) . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
    $dotenv->load();
}

// Database connection
$host = getenv('DB_HOST') ?: '127.0.0.1';
$dbname = getenv('DB_DATABASE') ?: 'fitapp';
$username = getenv('DB_USERNAME') ?: 'root';
$password = getenv('DB_PASSWORD') ?: 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Creating Export Logs Table</h2>";
    echo "<pre>";
    
    // Create export_logs table
    $sql = "CREATE TABLE IF NOT EXISTS export_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        export_type VARCHAR(20) NOT NULL,
        module VARCHAR(50) NOT NULL,
        filters TEXT,
        row_count INT,
        file_size INT,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_export_type (export_type),
        INDEX idx_module (module),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created export_logs table successfully\n";
    
    echo "\n<strong>✅ Export logs table created!</strong>\n";
    echo "</pre>";
    
    echo "<h3>🎉 All Tables Complete!</h3>";
    echo "<p>Your email automation and export system is now fully set up.</p>";
    
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='/fit/admin/email-automation' class='btn btn-primary btn-lg' style='margin-right: 10px;'>⚙️ Configure Email Automation</a>";
    echo "<a href='/fit/invoices' class='btn btn-success btn-lg'>📊 Try Excel Export</a>";
    echo "</div>";
    
    echo "<h4>Everything is Ready!</h4>";
    echo "<ul>";
    echo "<li>✅ Email variable substitution</li>";
    echo "<li>✅ Automatic invoice sending (configurable)</li>";
    echo "<li>✅ Payment reminder system</li>";
    echo "<li>✅ Excel/CSV/PDF export functionality</li>";
    echo "<li>✅ Email automation settings interface</li>";
    echo "</ul>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
}