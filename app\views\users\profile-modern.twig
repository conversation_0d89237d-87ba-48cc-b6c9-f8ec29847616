{% extends "base-modern.twig" %}

{% block title %}{{ __('users.my_profile') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ isOwnProfile ? __('users.my_profile') : __('users.user_profile') }}</h1>
        {% if canEdit %}
        <button type="button" class="btn btn-primary" onclick="enableEdit()">
            <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
        </button>
        {% endif %}
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-4">
            <!-- Profile Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-body text-center">
                    <div class="mb-3">
                        {% if user.avatar %}
                            <img src="{{ base_url }}/uploads/avatars/{{ user.avatar }}" 
                                 class="rounded-circle" style="width: 150px; height: 150px;" 
                                 alt="{{ user.name }}">
                        {% else %}
                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto" 
                                 style="width: 150px; height: 150px;">
                                <i class="bi bi-person-fill fs-1"></i>
                            </div>
                        {% endif %}
                    </div>
                    <h4 class="mb-1">{{ user.name|default((user.first_name ~ ' ' ~ user.last_name)|trim) }}</h4>
                    <p class="text-muted mb-3">{{ user.email }}</p>
                    <span class="badge bg-{{ user.role == 'admin' ? 'danger' : (user.role == 'manager' ? 'warning' : 'info') }} mb-3">
                        {{ __('users.role_' ~ user.role) }}
                    </span>
                    
                    <hr>
                    
                    <dl class="row mb-0 text-start">
                        <dt class="col-6">{{ __('users.username') }}:</dt>
                        <dd class="col-6">{{ user.username }}</dd>
                        
                        <dt class="col-6">{{ __('users.member_since') }}:</dt>
                        <dd class="col-6">{{ user.created_at|date('d/m/Y') }}</dd>
                        
                        <dt class="col-6">{{ __('users.last_login') }}:</dt>
                        <dd class="col-6">{{ user.last_login|date('d/m/Y H:i') }}</dd>
                    </dl>
                </div>
            </div>

            <!-- Groups Card -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-people me-2"></i>{{ __('users.my_groups') }}</h6>
                </div>
                <div class="card-body">
                    {% if user.groups %}
                        {% for group in user.groups %}
                            <span class="badge mb-2" style="background-color: {{ group.color }};">
                                <i class="{{ group.icon }} me-1"></i>{{ group.name }}
                            </span>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted mb-0">{{ __('users.no_groups_assigned') }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Profile Forms -->
        <div class="col-lg-8">
            <!-- Personal Information -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-person me-2"></i>{{ __('users.personal_information') }}</h6>
                </div>
                <div class="card-body">
                    <form id="profileForm" method="POST" action="{{ base_url }}/users/{{ user.id }}" enctype="multipart/form-data">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="first_name" class="form-label">{{ __('users.first_name') }}</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" 
                                       value="{{ user.first_name }}" disabled>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="last_name" class="form-label">{{ __('users.last_name') }}</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" 
                                       value="{{ user.last_name }}" disabled>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="username" class="form-label">{{ __('users.username') }}</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="{{ user.username }}" disabled>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="email" class="form-label">{{ __('common.email') }}</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ user.email }}" disabled>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="phone" class="form-label">{{ __('common.phone') }}</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ user.phone }}" disabled>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="avatar" class="form-label">{{ __('users.profile_picture') }}</label>
                                <input type="file" class="form-control" id="avatar" name="avatar" 
                                       accept="image/*" disabled>
                            </div>
                            
                            <div class="col-12 d-none" id="saveButtons">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-save me-2"></i>{{ __('common.save_changes') }}
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="cancelEdit()">
                                    <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            {% if isOwnProfile or (canEdit and currentUser and (currentUser.role == 'admin' or currentUser.role == 'manager')) %}
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-shield-lock me-2"></i>{{ __('users.security_settings') }}</h6>
                </div>
                <div class="card-body">
                    {% if isOwnProfile %}
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="bi bi-key me-2"></i>{{ __('users.change_password') }}
                        </button>
                        
                        <hr>
                        
                        <h6>{{ __('users.two_factor_auth') }}</h6>
                        <p class="text-muted">{{ __('users.two_factor_description') }}</p>
                        {% if user.two_factor_enabled %}
                            <button type="button" class="btn btn-danger" onclick="disable2FA()">
                                <i class="bi bi-shield-x me-2"></i>{{ __('users.disable_2fa') }}
                            </button>
                        {% else %}
                            <button type="button" class="btn btn-success" onclick="enable2FA()">
                                <i class="bi bi-shield-check me-2"></i>{{ __('users.enable_2fa') }}
                            </button>
                        {% endif %}
                    {% else %}
                        <!-- Admin password reset for other users -->
                        <p class="text-muted mb-3">{{ __('users.admin_password_reset_description') }}</p>
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#resetPasswordModal">
                            <i class="bi bi-key me-2"></i>{{ __('users.reset_password') }}
                        </button>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Preferences (only for own profile) -->
            {% if isOwnProfile %}
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-gear me-2"></i>{{ __('users.preferences') }}</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ base_url }}/profile/preferences">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        
                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>{{ __('users.save_preferences') }}
                            </button>
                        </div>
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="language" class="form-label">{{ __('users.preferred_language') }}</label>
                                <select class="form-select" id="language" name="language">
                                    <option value="fr" {{ user.language == 'fr' ? 'selected' : '' }}>Français</option>
                                    <option value="en" {{ user.language == 'en' ? 'selected' : '' }}>English</option>
                                    <option value="de" {{ user.language == 'de' ? 'selected' : '' }}>Deutsch</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="timezone" class="form-label">{{ __('users.timezone') }}</label>
                                <select class="form-select" id="timezone" name="timezone">
                                    <option value="Europe/Luxembourg" {{ user.timezone == 'Europe/Luxembourg' ? 'selected' : '' }}>
                                        Europe/Luxembourg
                                    </option>
                                    <option value="Europe/Paris" {{ user.timezone == 'Europe/Paris' ? 'selected' : '' }}>
                                        Europe/Paris
                                    </option>
                                    <option value="Europe/Brussels" {{ user.timezone == 'Europe/Brussels' ? 'selected' : '' }}>
                                        Europe/Brussels
                                    </option>
                                    <option value="Europe/Berlin" {{ user.timezone == 'Europe/Berlin' ? 'selected' : '' }}>
                                        Europe/Berlin
                                    </option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="theme" class="form-label">{{ __('config.theme') }}</label>
                                <select class="form-select" id="theme" name="theme">
                                    <option value="adminlte" {{ user.theme == 'adminlte' ? 'selected' : '' }}>AdminLTE 3</option>
                                    <option value="adminlte4" {{ user.theme == 'adminlte4' ? 'selected' : '' }}>AdminLTE 4</option>
                                    <option value="tabler" {{ user.theme == 'tabler' ? 'selected' : '' }}>Tabler</option>
                                    <option value="modern" {{ user.theme == 'modern' ? 'selected' : '' }}>Modern (Bootstrap 5)</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="notifications" class="form-label">{{ __('users.email_notifications') }}</label>
                                <select class="form-select" id="notifications" name="email_notifications">
                                    <option value="1" {{ user.email_notifications ? 'selected' : '' }}>{{ __('common.enabled') }}</option>
                                    <option value="0" {{ not user.email_notifications ? 'selected' : '' }}>{{ __('common.disabled') }}</option>
                                </select>
                            </div>
                            
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/profile/change-password">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('users.change_password') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">{{ __('users.current_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="current_password" name="current_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('current_password')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password" class="form-label">{{ __('users.new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password" name="new_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">{{ __('users.password_requirements') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ __('users.confirm_new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('users.update_password') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reset Password Modal (for admins) -->
{% if not isOwnProfile and canEdit and currentUser and (currentUser.role == 'admin' or currentUser.role == 'manager') %}
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="resetPasswordForm" method="POST" action="{{ base_url }}/users/{{ user.id }}/reset-password">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('users.reset_password_for') }} {{ user.first_name }} {{ user.last_name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('users.reset_password_warning') }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password_reset" class="form-label">{{ __('users.new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password_reset" name="new_password" required minlength="6">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('new_password_reset')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">{{ __('users.password_requirements') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password_reset" class="form-label">{{ __('users.confirm_new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password_reset" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('confirm_password_reset')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1">
                        <label class="form-check-label" for="send_email">
                            {{ __('users.send_password_email') }}
                        </label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-warning">{{ __('users.reset_password') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<script>
function enableEdit() {
    document.querySelectorAll('#profileForm input').forEach(input => {
        input.disabled = false;
    });
    document.getElementById('saveButtons').classList.remove('d-none');
}

function cancelEdit() {
    document.querySelectorAll('#profileForm input').forEach(input => {
        input.disabled = true;
    });
    document.getElementById('saveButtons').classList.add('d-none');
    document.getElementById('profileForm').reset();
}

// Handle form submission
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
});

function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('bi-eye');
        button.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('bi-eye-slash');
        button.classList.add('bi-eye');
    }
}

function enable2FA() {
    window.location.href = '{{ base_url }}/profile/2fa/enable';
}

function disable2FA() {
    if (confirm('{{ __("users.disable_2fa_confirm") }}')) {
        window.location.href = '{{ base_url }}/profile/2fa/disable';
    }
}

// Handle reset password form submission (for admins)
{% if not isOwnProfile and canEdit %}
document.getElementById('resetPasswordForm')?.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const newPassword = document.getElementById('new_password_reset').value;
    const confirmPassword = document.getElementById('confirm_password_reset').value;
    
    if (newPassword !== confirmPassword) {
        alert('{{ __("users.password_mismatch") }}');
        return;
    }
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message || '{{ __("users.password_reset_success") }}');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
});
{% endif %}
</script>
{% endblock %}