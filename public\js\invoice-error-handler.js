/**
 * Invoice Error Handler - Ensures loading overlay is hidden on errors
 */
(function() {
    'use strict';

document.addEventListener('DOMContentLoaded', function() {
    // Check if we're on the invoice create page and have error messages
    if (window.location.pathname.includes('/invoices/create')) {
        // Look for error alerts
        const errorAlerts = document.querySelectorAll('.alert-danger');
        
        if (errorAlerts.length > 0) {
            console.log('Error alerts detected, hiding loading overlay');
            
            // Hide any loading overlay
            const loadingOverlay = document.getElementById('invoiceLoadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
            }
            
            // Clear any pending submission flags
            window.pendingSubmission = false;
            
            // Log the errors for debugging
            errorAlerts.forEach(alert => {
                console.error('Invoice error:', alert.textContent.trim());
            });
            
            // Make error messages more prominent
            errorAlerts.forEach(alert => {
                alert.style.animation = 'shake 0.5s ease-out';
                alert.style.border = '2px solid #dc3545';
                alert.style.padding = '15px';
                alert.style.marginBottom = '20px';
            });
        }
    }
});

// Add shake animation for errors
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);

// Also listen for any AJAX errors
window.addEventListener('error', function(event) {
    console.error('Window error:', event);
    const loadingOverlay = document.getElementById('invoiceLoadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.remove();
    }
});

})(); // End IIFE