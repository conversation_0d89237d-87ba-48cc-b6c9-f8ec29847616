/* ========================================
   MOBILE-SPECIFIC STYLES FOR PRODUCT PAGES
   Fit360 AdminDesk - Product Management
   ======================================== */

/* ========================================
   1. PRODUCT LIST VIEW - MOBILE OPTIMIZATION
   ======================================== */

@media (max-width: 767px) {
    /* Product filters - stack vertically */
    .product-filters .row {
        margin: 0;
    }
    
    .product-filters .col-md-3,
    .product-filters .col-md-2 {
        width: 100% !important;
        padding: 0 !important;
        margin-bottom: 0.75rem !important;
    }
    
    /* Filter buttons group */
    .product-filters .btn-group {
        display: flex;
        width: 100%;
        gap: 0.5rem;
    }
    
    .product-filters .btn {
        flex: 1;
    }
    
    /* Product table - convert to cards on mobile */
    .products-table.table-mobile-card tbody tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 0;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        position: relative;
    }
    
    /* Product card header (code and name) */
    .products-table.table-mobile-card tbody tr td:nth-child(2),
    .products-table.table-mobile-card tbody tr td:nth-child(3) {
        display: block;
        width: 100%;
        padding: 0.75rem 1rem;
        border: none;
        background: #f8f9fa;
    }
    
    .products-table.table-mobile-card tbody tr td:nth-child(2) {
        padding-bottom: 0.25rem;
        font-family: monospace;
        font-size: 0.875rem;
    }
    
    .products-table.table-mobile-card tbody tr td:nth-child(3) {
        padding-top: 0;
        font-weight: 600;
        font-size: 1.1rem;
    }
    
    /* Hide checkbox column on mobile */
    .products-table.table-mobile-card tbody tr td:first-child {
        display: none;
    }
    
    /* Product details in card body */
    .products-table.table-mobile-card tbody tr td:nth-child(n+4):not(:last-child) {
        display: inline-block;
        width: 50%;
        padding: 0.5rem 1rem;
        border: none;
    }
    
    /* Actions column - full width at bottom */
    .products-table.table-mobile-card tbody tr td:last-child {
        display: block;
        width: 100%;
        padding: 0.75rem 1rem;
        border-top: 1px solid #dee2e6;
        background: #f8f9fa;
        text-align: center !important;
    }
    
    /* Action buttons - horizontal layout */
    .products-table.table-mobile-card .btn-group {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        flex-wrap: nowrap;
    }
    
    .products-table.table-mobile-card .btn-group .btn {
        flex: 1;
        max-width: 60px;
    }
    
    /* Stock status badge styling */
    .products-table .badge {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    /* Quick sale indicator */
    .badge.bg-info {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
    }
}

/* ========================================
   2. PRODUCT FORM - MOBILE OPTIMIZATION
   ======================================== */

@media (max-width: 767px) {
    /* Form sections - better spacing */
    .product-form .card {
        margin-bottom: 1rem;
    }
    
    .product-form .card-header {
        padding: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
    }
    
    .product-form .card-body {
        padding: 1rem;
    }
    
    /* Form groups - stack all inputs */
    .product-form .row {
        margin: 0;
    }
    
    .product-form .col-md-6,
    .product-form .col-md-4,
    .product-form .col-md-3,
    .product-form .col-md-8,
    .product-form .col-md-9 {
        width: 100% !important;
        padding: 0 !important;
    }
    
    /* Price input group - responsive layout */
    .product-form .input-group {
        flex-wrap: wrap;
    }
    
    .product-form .input-group .form-control {
        flex: 1 1 60%;
        min-width: 0;
    }
    
    .product-form .input-group .input-group-text {
        flex: 1 1 40%;
        justify-content: center;
    }
    
    /* Stock fields - horizontal layout on mobile */
    .stock-fields-mobile {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }
    
    /* Image upload area - larger touch target */
    .product-image-upload {
        min-height: 150px;
        border: 2px dashed #dee2e6;
        border-radius: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .product-image-upload:hover,
    .product-image-upload:active {
        border-color: #007bff;
        background: #f0f8ff;
    }
    
    /* Form actions - sticky footer */
    .product-form-actions {
        position: sticky;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #dee2e6;
        padding: 1rem;
        margin: 0 -1rem -1rem;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
        z-index: 10;
    }
    
    .product-form-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .product-form-actions .btn:last-child {
        margin-bottom: 0;
    }
}

/* ========================================
   3. PRODUCT DETAILS VIEW - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Product header - compact layout */
    .product-detail-header {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .product-detail-header h1 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .product-detail-header .product-code {
        font-family: monospace;
        color: #6c757d;
        font-size: 0.875rem;
    }
    
    /* Product info cards */
    .product-info-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1rem;
        margin-bottom: 1rem;
    }
    
    .product-info-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
    }
    
    .product-info-card h5 {
        font-size: 0.875rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    .product-info-card .value {
        font-size: 1.25rem;
        font-weight: 600;
    }
    
    /* Stock status card - prominent display */
    .stock-status-card {
        background: #f8f9fa;
        border: 2px solid;
        text-align: center;
        padding: 1.5rem;
    }
    
    .stock-status-card.in-stock {
        border-color: #28a745;
        color: #28a745;
    }
    
    .stock-status-card.low-stock {
        border-color: #ffc107;
        color: #856404;
        background: #fff3cd;
    }
    
    .stock-status-card.out-of-stock {
        border-color: #dc3545;
        color: #dc3545;
        background: #f8d7da;
    }
    
    /* Action buttons - fixed bottom bar */
    .product-detail-actions {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #dee2e6;
        padding: 1rem;
        display: flex;
        gap: 0.5rem;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
        z-index: 100;
    }
    
    .product-detail-actions .btn {
        flex: 1;
    }
    
    /* Add padding to content for fixed actions */
    .product-detail-content {
        padding-bottom: 5rem;
    }
}

/* ========================================
   4. CATEGORY MANAGEMENT - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Category list - card layout */
    .category-list .list-group-item {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
        padding: 1rem;
    }
    
    .category-list .list-group-item:last-child {
        margin-bottom: 0;
    }
    
    /* Category drag handle - larger touch target */
    .category-drag-handle {
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: move;
        color: #6c757d;
    }
    
    /* Category actions - vertical stack */
    .category-actions {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }
    
    .category-actions .btn {
        width: 100%;
        justify-content: flex-start;
    }
}

/* ========================================
   5. STOCK ADJUSTMENT MODAL - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Stock adjustment form - better layout */
    #stockModal .modal-body {
        padding: 1.5rem;
    }
    
    /* Quantity input with large stepper buttons */
    .stock-quantity-input {
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .stock-quantity-input .btn {
        width: 44px;
        height: 44px;
        padding: 0;
        font-size: 1.5rem;
        line-height: 1;
    }
    
    .stock-quantity-input input {
        text-align: center;
        font-size: 1.25rem;
        font-weight: 600;
    }
    
    /* Adjustment type buttons - grid layout */
    .adjustment-type-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 0.5rem;
    }
    
    .adjustment-type-grid .btn {
        padding: 0.75rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* ========================================
   6. BULK OPERATIONS - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Bulk actions bar - fixed position */
    #bulkActions {
        position: fixed;
        bottom: 4rem;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #dee2e6;
        padding: 0.75rem 1rem;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
        z-index: 99;
        flex-wrap: wrap;
    }
    
    #bulkActions .btn {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
    }
    
    /* Selected count - prominent display */
    #selectedCount {
        font-weight: 600;
        color: #007bff;
    }
}

/* ========================================
   7. QUICK SALE BUTTONS - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Quick sale grid - responsive layout */
    .quick-sale-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }
    
    .quick-sale-item {
        background: #fff;
        border: 2px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .quick-sale-item:active {
        transform: scale(0.95);
        background: #007bff;
        color: #fff;
        border-color: #007bff;
    }
    
    .quick-sale-item .price {
        font-size: 1.25rem;
        font-weight: 600;
        margin-top: 0.5rem;
    }
}

/* ========================================
   8. SEARCH AND AUTOCOMPLETE - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Search input - full screen overlay */
    .mobile-search-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: #fff;
        z-index: 1050;
        display: none;
        flex-direction: column;
    }
    
    .mobile-search-overlay.show {
        display: flex;
    }
    
    .mobile-search-header {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        display: flex;
        align-items: center;
        gap: 1rem;
    }
    
    .mobile-search-header input {
        flex: 1;
        border: none;
        font-size: 1.125rem;
        padding: 0.5rem;
    }
    
    .mobile-search-results {
        flex: 1;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    /* Autocomplete dropdown - full width */
    .product-autocomplete {
        position: fixed;
        left: 0;
        right: 0;
        top: auto;
        bottom: 0;
        max-height: 60vh;
        border-radius: 1rem 1rem 0 0;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
    }
}

/* ========================================
   9. MOBILE-SPECIFIC UTILITIES
   ======================================== */

@media (max-width: 767px) {
    /* Hide desktop-only elements */
    .desktop-only-product {
        display: none !important;
    }
    
    /* Show mobile-only elements */
    .mobile-only-product {
        display: block !important;
    }
    
    /* Full width on mobile */
    .mobile-full-width-product {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* Responsive spacing */
    .mobile-mb-product {
        margin-bottom: 1rem !important;
    }
    
    /* Touch-friendly padding */
    .mobile-touch-padding {
        padding: 1rem !important;
    }
}

/* ========================================
   10. PERFORMANCE OPTIMIZATIONS
   ======================================== */

@media (max-width: 767px) {
    /* Reduce animations for better performance */
    .product-card,
    .product-image,
    .product-badge {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
    
    /* Optimize image loading */
    .product-image {
        background: #f0f0f0;
        min-height: 200px;
    }
    
    .product-image img {
        object-fit: cover;
        width: 100%;
        height: 100%;
    }
}

/* ========================================
   11. FLOATING ACTION BUTTON - PRODUCTS
   ======================================== */

@media (max-width: 767px) {
    /* Product-specific FAB */
    .product-fab {
        position: fixed;
        bottom: 5rem;
        right: 1rem;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: #28a745;
        color: white;
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.2);
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
    }
    
    .product-fab:active {
        transform: scale(0.9);
    }
}