<?php
// Test what the next invoice number will be

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>✅ Invoice Number System Test</h2>";
    
    // Show current invoice
    echo "<h3>Current Status</h3>";
    echo "<p style='font-size: 18px; color: green;'>✓ Your invoice is now correctly numbered!</p>";
    
    // Check current sequence
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $seq = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Next Invoice Numbers</h3>";
    echo "<p>Current sequence: <strong>" . ($seq['last_number'] ?? 'Not set') . "</strong></p>";
    
    $nextNumber = ($seq['last_number'] ?? 0) + 1;
    $nextNumberPadded = str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse; margin-top: 20px;'>";
    echo "<tr style='background: #f0f0f0;'><th>Invoice Type</th><th>Next Invoice Number</th></tr>";
    echo "<tr><td>Loyer (Rental)</td><td style='font-size: 18px; color: #007bff;'><strong>FAC-LOY-2025-" . $nextNumberPadded . "</strong></td></tr>";
    echo "<tr><td>Rétrocession</td><td style='font-size: 18px; color: #007bff;'><strong>FAC-RET-2025-" . $nextNumberPadded . "</strong></td></tr>";
    echo "<tr><td>Divers</td><td style='font-size: 18px; color: #007bff;'><strong>FAC-DIV-2025-" . $nextNumberPadded . "</strong></td></tr>";
    echo "<tr><td>No Type Selected</td><td style='font-size: 18px; color: #666;'><strong>FAC-2025-" . $nextNumberPadded . "</strong></td></tr>";
    echo "</table>";
    
    // Check deleted numbers pool
    echo "<h3>Number Reuse System</h3>";
    $stmt = $db->query("
        SELECT COUNT(*) as count FROM deleted_invoice_numbers 
        WHERE reused_at IS NULL AND year = 2025
    ");
    $deleted = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<p>Numbers available for reuse: <strong>" . $deleted['count'] . "</strong></p>";
    
    // Show tips
    echo "<h3>Important Reminders</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-left: 4px solid #007bff; margin: 20px 0;'>";
    echo "<ul style='margin: 0;'>";
    echo "<li><strong>Always select the correct invoice type</strong> when creating invoices</li>";
    echo "<li>The system will automatically add the type prefix (LOY, RET, DIV)</li>";
    echo "<li>Deleted invoice numbers will be reused automatically</li>";
    echo "<li>The sequence is managed per year (2025, 2026, etc.)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<br>";
    echo "<a href='/fit/public/invoices' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>Go to Invoices</a>";
    echo "<a href='/fit/public/invoices/create' style='font-size: 16px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Create New Invoice</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>