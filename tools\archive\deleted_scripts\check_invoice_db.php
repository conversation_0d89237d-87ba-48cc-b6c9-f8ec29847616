<?php
/**
 * Check Invoice Database
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$invoiceNumber = $_GET['number'] ?? 'FAC-2025-00064';

try {
    $db = Flight::db();
    
    echo "<h1>Database Check</h1>";
    
    // Check connection
    echo "<h2>1. Database Connection:</h2>";
    echo "<p>Connected to database successfully</p>";
    
    // List all invoices
    echo "<h2>2. All Invoices in Database:</h2>";
    $stmt = $db->query("SELECT id, invoice_number, status, created_at FROM invoices ORDER BY id DESC LIMIT 10");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($invoices)) {
        echo "<p style='color: red;'>No invoices found in database!</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Status</th><th>Created</th></tr>";
        foreach ($invoices as $inv) {
            echo "<tr>";
            echo "<td>" . $inv['id'] . "</td>";
            echo "<td>" . htmlspecialchars($inv['invoice_number']) . "</td>";
            echo "<td>" . $inv['status'] . "</td>";
            echo "<td>" . $inv['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Search for specific invoice
    echo "<h2>3. Search for invoice: " . htmlspecialchars($invoiceNumber) . "</h2>";
    $stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
    $stmt->execute([$invoiceNumber]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "<p style='color: green;'>✓ Invoice found!</p>";
        echo "<pre>";
        print_r($invoice);
        echo "</pre>";
    } else {
        echo "<p style='color: red;'>✗ Invoice not found with number: " . htmlspecialchars($invoiceNumber) . "</p>";
        
        // Try LIKE search
        echo "<h3>Trying partial search:</h3>";
        $stmt = $db->prepare("SELECT id, invoice_number FROM invoices WHERE invoice_number LIKE ?");
        $stmt->execute(['%' . $invoiceNumber . '%']);
        $similar = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if ($similar) {
            echo "<p>Found similar invoice numbers:</p>";
            foreach ($similar as $sim) {
                echo "<p>- " . htmlspecialchars($sim['invoice_number']) . " (ID: " . $sim['id'] . ")</p>";
            }
        }
    }
    
    // Check if using correct database
    echo "<h2>4. Database Info:</h2>";
    $stmt = $db->query("SELECT DATABASE()");
    $dbName = $stmt->fetchColumn();
    echo "<p>Current database: <strong>" . $dbName . "</strong></p>";
    
    // Check tables
    $stmt = $db->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "<p>Tables in database: " . count($tables) . "</p>";
    if (in_array('invoices', $tables)) {
        echo "<p style='color: green;'>✓ 'invoices' table exists</p>";
    } else {
        echo "<p style='color: red;'>✗ 'invoices' table NOT found!</p>";
    }
    
} catch (Exception $e) {
    echo "<h1>Database Error:</h1>";
    echo "<p style='color: red;'>" . htmlspecialchars($e->getMessage()) . "</p>";
}