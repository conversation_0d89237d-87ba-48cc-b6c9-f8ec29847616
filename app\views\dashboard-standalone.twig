{% extends "base-modern.twig" %}

{% block title %}{{ __('common.dashboard') }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ base_url }}/css/mobile-responsive.css">
<link rel="stylesheet" href="{{ base_url }}/css/mobile-dashboard.css">
<link rel="stylesheet" href="{{ base_url }}/css/dashboard-loading.css">
<style>
/* Gradient background for welcome card */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #6f42c1 100%);
}

/* Stat card hover effect */
.stat-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Quick action button hover */
.btn-outline-primary:hover i,
.btn-outline-success:hover i,
.btn-outline-info:hover i,
.btn-outline-warning:hover i {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stat-card .fs-1 {
        font-size: 2rem !important;
    }
    
    .bg-gradient-primary h2 {
        font-size: 1.5rem;
    }
}

/* Spinning animation for refresh button */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spinning {
    animation: spin 1s linear infinite;
}
</style>
{% endblock %}

{% block content %}
<!-- Dashboard Controls -->
<div class="row mb-3">
    <div class="col-12 d-flex justify-content-end align-items-center gap-3">
        <div class="form-check form-switch">
            <input class="form-check-input" type="checkbox" id="auto-refresh-toggle">
            <label class="form-check-label" for="auto-refresh-toggle">
                {{ __('dashboard.auto_refresh') | default('Auto Refresh') }}
            </label>
        </div>
        <button class="btn btn-sm btn-outline-primary" id="refresh-dashboard">
            <i class="bi bi-arrow-clockwise"></i> {{ __('common.refresh') | default('Refresh') }}
        </button>
    </div>
</div>

<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 bg-gradient-primary text-white">
            <div class="card-body p-4">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-2">{{ greeting }}, {{ user.name|default('User') }}!</h2>
                        <p class="mb-0 opacity-75">{{ __('dashboard.today_summary') }} - {{ "now"|date('l, F j, Y') }}</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a href="{{ base_url }}/invoices/create" class="btn btn-light btn-lg">
                            <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4 dashboard-stats">
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card stat-card text-white bg-primary h-100">
            <div class="card-body position-relative">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="card-title mb-2">{{ __('dashboard.total_clients') }}</h5>
                        <h2 class="mb-0">{{ stats.total_clients|number_format(0, '.', ',') }}</h2>
                    </div>
                    <i class="bi bi-people fs-1 opacity-50"></i>
                </div>
            </div>
            <a href="{{ base_url }}/clients" class="stretched-link"></a>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card stat-card text-white bg-success h-100">
            <div class="card-body position-relative">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="card-title mb-2">{{ __('dashboard.total_patients') }}</h5>
                        <h2 class="mb-0">{{ stats.total_patients|number_format(0, '.', ',') }}</h2>
                    </div>
                    <i class="bi bi-person-badge fs-1 opacity-50"></i>
                </div>
            </div>
            <a href="{{ base_url }}/patients" class="stretched-link"></a>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card stat-card text-white bg-warning h-100">
            <div class="card-body position-relative">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="card-title mb-2">{{ __('dashboard.pending_invoices') }}</h5>
                        <h2 class="mb-0">{{ stats.pending_invoices|number_format(0, '.', ',') }}</h2>
                    </div>
                    <i class="bi bi-clock-history fs-1 opacity-50"></i>
                </div>
            </div>
            <a href="{{ base_url }}/invoices?status=sent" class="stretched-link"></a>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
        <div class="card stat-card text-white bg-info h-100">
            <div class="card-body position-relative">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h5 class="card-title mb-2">{{ __('dashboard.monthly_revenue') }}</h5>
                        <h2 class="mb-0">€{{ stats.monthly_revenue|number_format(2, ',', '.') }}</h2>
                    </div>
                    <i class="bi bi-currency-euro fs-1 opacity-50"></i>
                </div>
            </div>
            <a href="{{ base_url }}/reports" class="stretched-link"></a>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{{ __('dashboard.quick_actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3 quick-actions-grid">
                    <div class="col-md-3 col-6">
                        <a href="{{ base_url }}/invoices/create" class="btn btn-outline-primary w-100 py-3">
                            <i class="bi bi-file-earmark-plus d-block fs-3 mb-2"></i>
                            {{ __('invoices.create_invoice') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="{{ base_url }}/clients/create" class="btn btn-outline-success w-100 py-3">
                            <i class="bi bi-person-plus d-block fs-3 mb-2"></i>
                            {{ __('clients.add_client') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="{{ base_url }}/patients/create" class="btn btn-outline-info w-100 py-3">
                            <i class="bi bi-person-badge-fill d-block fs-3 mb-2"></i>
                            {{ __('patients.add_patient') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="{{ base_url }}/reports" class="btn btn-outline-warning w-100 py-3">
                            <i class="bi bi-graph-up d-block fs-3 mb-2"></i>
                            {{ __('common.view_reports') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Invoices -->
<div class="row recent-invoices-container">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('dashboard.recent_invoices') }}</h5>
                <a href="{{ base_url }}/invoices" class="btn btn-sm btn-primary">{{ __('common.view_all') }}</a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive recent-invoices-mobile recent-invoices-table">
                    <table class="table table-hover mb-0">
                        <thead>
                            <tr>
                                <th>{{ __('invoices.invoice_number') }}</th>
                                <th>{{ __('invoices.client') }}</th>
                                <th>{{ __('invoices.amount') }}</th>
                                <th>{{ __('common.status') }}</th>
                                <th>{{ __('invoices.issue_date') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if recent_invoices is empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4 text-muted">
                                        <i class="bi bi-inbox d-block fs-1 mb-2"></i>
                                        {{ __('dashboard.no_recent_invoices') }}
                                    </td>
                                </tr>
                            {% else %}
                                {% for invoice in recent_invoices %}
                                    <tr>
                                        <td data-label="{{ __('invoices.invoice_number') }}">
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="text-decoration-none">
                                                {{ invoice.invoice_number }}
                                            </a>
                                        </td>
                                        <td data-label="{{ __('invoices.client') }}">{{ invoice.client_name|default('N/A') }}</td>
                                        <td data-label="{{ __('invoices.amount') }}">€{{ invoice.total|number_format(2, ',', '.') }}</td>
                                        <td data-label="{{ __('common.status') }}">
                                            {% set status_classes = {
                                                'draft': 'secondary',
                                                'sent': 'warning',
                                                'paid': 'success',
                                                'overdue': 'danger',
                                                'cancelled': 'dark'
                                            } %}
                                            <span class="badge bg-{{ status_classes[invoice.status]|default('secondary') }}">
                                                {% if invoice.status == 'paid' %}
                                                    {{ __('invoices.status.paid') }}
                                                {% elseif invoice.status == 'sent' %}
                                                    {{ __('invoices.status.sent') }}
                                                {% elseif invoice.status == 'draft' %}
                                                    {{ __('invoices.status.draft') }}
                                                {% elseif invoice.status == 'overdue' %}
                                                    {{ __('invoices.status.overdue') }}
                                                {% elseif invoice.status == 'cancelled' %}
                                                    {{ __('invoices.status.cancelled') }}
                                                {% else %}
                                                    {{ invoice.status|capitalize }}
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td data-label="{{ __('invoices.issue_date') }}">{{ invoice.issue_date|date('d/m/Y') }}</td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ base_url }}/js/mobile-enhancements.js"></script>
<!-- Temporarily disable enhanced dashboard to avoid 404 errors -->
<!-- <script src="{{ base_url }}/js/dashboard-enhanced.js"></script> -->
<script>
// Auto-refresh dashboard functionality
let refreshInterval;
let autoRefreshEnabled = localStorage.getItem('dashboard_auto_refresh') === 'true';

function startAutoRefresh() {
    stopAutoRefresh(); // Clear any existing interval
    if (autoRefreshEnabled) {
        refreshInterval = setInterval(() => {
            location.reload();
        }, 5 * 60 * 1000); // 5 minutes
    }
}

function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
    }
}

// Initialize auto-refresh based on saved preference
document.addEventListener('DOMContentLoaded', function() {
    // Set toggle state
    const autoRefreshToggle = document.getElementById('auto-refresh-toggle');
    if (autoRefreshToggle) {
        autoRefreshToggle.checked = autoRefreshEnabled;
        autoRefreshToggle.addEventListener('change', function(e) {
            autoRefreshEnabled = e.target.checked;
            localStorage.setItem('dashboard_auto_refresh', autoRefreshEnabled);
            if (autoRefreshEnabled) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
    }
    
    // Refresh button
    const refreshBtn = document.getElementById('refresh-dashboard');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.add('spinning');
            }
            location.reload();
        });
    }
    
    // Start auto-refresh if enabled
    if (autoRefreshEnabled) {
        startAutoRefresh();
    }
});

// Stop auto-refresh when page is hidden
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        stopAutoRefresh();
    } else if (autoRefreshEnabled) {
        startAutoRefresh();
    }
});

// Initialize mobile enhancements
document.addEventListener('DOMContentLoaded', function() {
    if (typeof MobileEnhancements !== 'undefined') {
        MobileEnhancements.init();
    }
    
    // Add dashboard-specific mobile features
    if (window.innerWidth <= 768) {
        // Add floating refresh button
        const refreshBtn = document.createElement('button');
        refreshBtn.className = 'dashboard-fab-refresh';
        refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
        refreshBtn.setAttribute('aria-label', 'Refresh dashboard');
        refreshBtn.addEventListener('click', function() {
            this.classList.add('spinning');
            location.reload();
        });
        document.body.appendChild(refreshBtn);
        
        // Add quick stats bar
        const statsBar = document.createElement('div');
        statsBar.className = 'dashboard-quick-stats dashboard-mobile-only';
        statsBar.innerHTML = `
            <div class="quick-stat-item">
                <div class="quick-stat-value">{{ stats.total_clients|number_format(0) }}</div>
                <div class="quick-stat-label">{{ __('dashboard.clients') | default('Clients') }}</div>
            </div>
            <div class="quick-stat-item">
                <div class="quick-stat-value">{{ stats.pending_invoices|number_format(0) }}</div>
                <div class="quick-stat-label">{{ __('dashboard.pending') | default('Pending') }}</div>
            </div>
            <div class="quick-stat-item">
                <div class="quick-stat-value">€{{ (stats.monthly_revenue/1000)|number_format(1) }}k</div>
                <div class="quick-stat-label">{{ __('dashboard.revenue') | default('Revenue') }}</div>
            </div>
        `;
        document.body.appendChild(statsBar);
    }
});
</script>
{% endblock %}