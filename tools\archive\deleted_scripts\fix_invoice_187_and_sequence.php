<?php
// Fix invoice 187 and reset sequence to 185

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix Invoice 187 and Reset Sequence</h2>";
    
    // Check current invoice
    $stmt = $db->prepare("
        SELECT i.*, it.prefix as type_prefix, it.name as type_name
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.invoice_number = 'FAC-2025-0187'
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<strong>Error:</strong> Invoice FAC-2025-0187 not found!<br>";
        exit;
    }
    
    echo "<h3>Current Invoice Details</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>ID</td><td>" . $invoice['id'] . "</td></tr>";
    echo "<tr><td>Invoice Number</td><td><strong>" . $invoice['invoice_number'] . "</strong></td></tr>";
    echo "<tr><td>Type ID</td><td>" . ($invoice['type_id'] ?? 'NULL') . "</td></tr>";
    echo "<tr><td>Type Name</td><td>" . ($invoice['type_name'] ?? 'No type set') . "</td></tr>";
    echo "<tr><td>Type Prefix</td><td>" . ($invoice['type_prefix'] ?? 'No prefix') . "</td></tr>";
    echo "<tr><td>Status</td><td>" . $invoice['status'] . "</td></tr>";
    echo "<tr><td>Total</td><td>" . $invoice['total'] . " " . $invoice['currency'] . "</td></tr>";
    echo "</table>";
    
    // Check if it should be a rental invoice
    $isRental = false;
    if ($invoice['type_id'] == 1 || strpos(strtolower($invoice['type_name'] ?? ''), 'loyer') !== false) {
        $isRental = true;
        echo "<p>✓ This appears to be a rental invoice (Loyer)</p>";
    }
    
    // Get current sequence
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h3>Current Sequence</h3>";
    echo "<p>Last number: <strong>" . ($sequence['last_number'] ?? 'Not found') . "</strong></p>";
    
    // Action selection
    if (!isset($_POST['action'])) {
        ?>
        <h3>Choose Action</h3>
        <form method="POST">
            <div style="margin: 20px; padding: 20px; border: 1px solid #ccc; border-radius: 5px;">
                <h4>Option A: Update Invoice Number</h4>
                <p>Change FAC-2025-0187 to FAC-LOY-2025-0186 and reset sequence to 185</p>
                <label>
                    <input type="radio" name="action" value="update_to_186" checked>
                    Update to FAC-LOY-2025-0186 (recommended for rental invoice)
                </label><br><br>
                
                <label>
                    <input type="radio" name="action" value="update_add_loy">
                    Keep as 0187 but add LOY prefix: FAC-LOY-2025-0187
                </label>
            </div>
            
            <div style="margin: 20px; padding: 20px; border: 1px solid #ccc; border-radius: 5px;">
                <h4>Option B: Delete Invoice</h4>
                <label>
                    <input type="radio" name="action" value="delete">
                    Delete invoice FAC-2025-0187 completely
                </label>
            </div>
            
            <div style="margin: 20px; padding: 20px; border: 1px solid #ccc; border-radius: 5px;">
                <h4>Option C: Just Reset Sequence</h4>
                <label>
                    <input type="radio" name="action" value="reset_only">
                    Leave invoice as is, just reset sequence to 185
                </label>
            </div>
            
            <div style="margin: 20px;">
                <label>
                    <input type="checkbox" name="reset_sequence" value="1" checked>
                    Also reset sequence to 185 (next invoice will be 0186)
                </label>
            </div>
            
            <button type="submit" style="font-size: 16px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">
                Execute Selected Action
            </button>
        </form>
        <?php
    } else {
        // Execute action
        echo "<h3>Executing Action: " . $_POST['action'] . "</h3>";
        
        $db->beginTransaction();
        
        try {
            switch ($_POST['action']) {
                case 'update_to_186':
                    // Update invoice to 0186 with LOY prefix
                    $newNumber = $isRental ? 'FAC-LOY-2025-0186' : 'FAC-2025-0186';
                    $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
                    $stmt->execute([$newNumber, $invoice['id']]);
                    echo "✓ Updated invoice number to: <strong>$newNumber</strong><br>";
                    
                    // Add 0187 to deleted pool
                    $stmt = $db->prepare("
                        INSERT INTO deleted_invoice_numbers (
                            document_type_id, invoice_number, year, sequence_number, deleted_by
                        ) VALUES (1, 'FAC-2025-0187', 2025, 187, 1)
                    ");
                    $stmt->execute();
                    echo "✓ Added FAC-2025-0187 to deleted numbers pool<br>";
                    break;
                    
                case 'update_add_loy':
                    // Just add LOY prefix
                    if ($isRental) {
                        $newNumber = 'FAC-LOY-2025-0187';
                        $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
                        $stmt->execute([$newNumber, $invoice['id']]);
                        echo "✓ Updated invoice number to: <strong>$newNumber</strong><br>";
                    } else {
                        echo "! Invoice is not a rental type, no prefix added<br>";
                    }
                    break;
                    
                case 'delete':
                    // Delete the invoice
                    $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                    $stmt->execute([$invoice['id']]);
                    
                    $stmt = $db->prepare("DELETE FROM invoices WHERE id = ?");
                    $stmt->execute([$invoice['id']]);
                    echo "✓ Deleted invoice FAC-2025-0187<br>";
                    
                    // Add to deleted pool
                    $stmt = $db->prepare("
                        INSERT INTO deleted_invoice_numbers (
                            document_type_id, invoice_number, year, sequence_number, deleted_by
                        ) VALUES (1, 'FAC-2025-0187', 2025, 187, 1)
                    ");
                    $stmt->execute();
                    echo "✓ Added FAC-2025-0187 to deleted numbers pool<br>";
                    break;
                    
                case 'reset_only':
                    echo "✓ Keeping invoice as is<br>";
                    break;
            }
            
            // Reset sequence if requested
            if (isset($_POST['reset_sequence']) && $_POST['reset_sequence'] == '1') {
                $stmt = $db->prepare("
                    UPDATE document_sequences 
                    SET last_number = 185 
                    WHERE document_type_id = 1 AND year = 2025
                ");
                $stmt->execute();
                echo "✓ Reset sequence to 185 (next invoice will be 0186)<br>";
                
                // Also add 186 to deleted pool if we didn't use it
                if ($_POST['action'] != 'update_to_186') {
                    $stmt = $db->prepare("
                        INSERT IGNORE INTO deleted_invoice_numbers (
                            document_type_id, invoice_number, year, sequence_number, deleted_by
                        ) VALUES (1, 'FAC-2025-0186', 2025, 186, 1)
                    ");
                    $stmt->execute();
                    echo "✓ Added FAC-2025-0186 to deleted numbers pool for future use<br>";
                }
            }
            
            $db->commit();
            echo "<br><strong>✓ All actions completed successfully!</strong><br>";
            
        } catch (Exception $e) {
            $db->rollBack();
            echo "<strong>Error:</strong> " . $e->getMessage();
        }
        
        echo '<br><br>';
        echo '<a href="/fit/public/invoices" style="font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">Go to Invoices</a>';
        echo '<a href="check_invoice_sequences.php" style="font-size: 16px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;">Check Sequences</a>';
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>