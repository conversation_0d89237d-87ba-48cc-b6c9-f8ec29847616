/**
 * Dropdown Positioning Fix
 * Ensures dropdown menus are properly positioned and not cut off
 */

/* Global dropdown fixes */
.dropdown-menu {
    position: absolute !important;
    z-index: 1050 !important;
    min-width: 10rem;
}

/* Fix for dropdowns at the edge of containers */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* Ensure dropdowns in table cells work properly */
.table td .dropdown {
    position: relative;
}

.table td .dropdown-menu {
    /* Let Popper.js handle positioning */
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
}

/* Allow vertical overflow for dropdowns while keeping horizontal scroll */
.table-responsive {
    overflow-x: auto;
    overflow-y: visible;
}

/* Don't force overflow on card elements - let them maintain boundaries */
.card {
    overflow: hidden;
}

.card-body {
    overflow: hidden;
}

/* Fix for Bootstrap 5 dropdowns with fixed strategy */
.dropdown-menu[data-bs-strategy="fixed"] {
    position: fixed !important;
}

/* Bootstrap 5 Popper.js strategy fix */
.dropdown-toggle::after {
    vertical-align: middle;
}

/* Ensure proper positioning on small screens */
@media (max-width: 768px) {
    .dropdown-menu {
        position: fixed !important;
        transform: none !important;
        right: 1rem !important;
        left: auto !important;
        max-width: calc(100vw - 2rem);
    }
    
    /* Adjust dropdown position based on viewport */
    .dropdown-menu[data-popper-placement^="bottom"] {
        top: auto !important;
    }
    
    .dropdown-menu[data-popper-placement^="top"] {
        bottom: auto !important;
    }
}

/* Fix for nested dropdowns */
.dropdown-menu .dropdown-menu {
    position: absolute !important;
    top: 0 !important;
    left: 100% !important;
    margin-left: 0.125rem !important;
}

/* RTL support */
[dir="rtl"] .dropdown-menu-end {
    right: auto !important;
    left: 0 !important;
}

[dir="rtl"] .table td .dropdown-menu {
    transform: translate(100%, 0) !important;
    right: auto !important;
    left: 0 !important;
}

/* Ensure dropdown stays within viewport bounds */
.dropdown-menu[data-bs-popper] {
    max-height: calc(100vh - 3.5rem);
    overflow-y: auto;
}

/* Clearance from navbar when dropdown flips up */
.dropdown-menu[data-popper-placement^="top"] {
    margin-bottom: 0.5rem !important;
}

/* Ensure proper spacing from viewport edges */
.dropdown-menu {
    margin: 0.125rem 0 !important;
}

/* Specific fix for dropdowns that might go under navbar */
.navbar ~ * .dropdown-menu {
    /* Ensure dropdown appears above navbar */
    z-index: 1050 !important;
}

/* Fix for dropdowns in fixed/sticky elements */
.sticky-top .dropdown-menu,
.fixed-top .dropdown-menu,
.fixed-bottom .dropdown-menu {
    position: fixed !important;
}