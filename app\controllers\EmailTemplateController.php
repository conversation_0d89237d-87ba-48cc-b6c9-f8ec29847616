<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Services\TemplateSelector;
use App\Services\EnhancedEmailService;
use App\Models\EmailTemplate;
use App\Models\Invoice;
use App\Models\Client;
use Flight;

class EmailTemplateController extends Controller
{
    private $templateSelector;
    private $emailService;
    
    public function __construct()
    {
        parent::__construct();
        $this->templateSelector = new TemplateSelector();
        $this->emailService = new EnhancedEmailService();
    }
    
    /**
     * Preview template selection for an invoice
     */
    public function previewTemplateSelection()
    {
        $invoiceId = Flight::request()->query->invoice_id;
        
        if (!$invoiceId) {
            return $this->json(['error' => 'Invoice ID required'], 400);
        }
        
        try {
            // Get invoice with details
            $invoice = Invoice::with(['client', 'items', 'payments'])->find($invoiceId);
            
            if (!$invoice) {
                return $this->json(['error' => 'Invoice not found'], 404);
            }
            
            // Build context
            $context = [
                'invoice' => $invoice,
                'client' => $invoice->client,
                'language' => $invoice->client->language ?? 'fr'
            ];
            
            // Try different template types to show selection logic
            $templateTypes = [
                TemplateSelector::TYPE_INVOICE_NEW,
                TemplateSelector::TYPE_INVOICE_REMINDER,
                TemplateSelector::TYPE_INVOICE_OVERDUE,
                TemplateSelector::TYPE_INVOICE_PARTIAL,
                TemplateSelector::TYPE_INVOICE_PAID
            ];
            
            $results = [];
            
            foreach ($templateTypes as $type) {
                $template = $this->templateSelector->selectTemplate($type, $context);
                $metadata = $this->templateSelector->getSelectionMetadata();
                
                $results[$type] = [
                    'selected_template' => $template ? [
                        'id' => $template->id,
                        'name' => $template->name,
                        'code' => $template->code
                    ] : null,
                    'selection_metadata' => $metadata,
                    'would_be_used' => $this->wouldBeUsedForInvoice($type, $invoice)
                ];
            }
            
            return $this->json([
                'invoice' => [
                    'id' => $invoice->id,
                    'number' => $invoice->invoice_number,
                    'type' => $invoice->type,
                    'status' => $invoice->status,
                    'payment_status' => $this->getPaymentStatus($invoice)
                ],
                'client' => [
                    'id' => $invoice->client->id,
                    'name' => $invoice->client->name,
                    'type' => $this->getClientType($invoice->client),
                    'language' => $invoice->client->language ?? 'fr'
                ],
                'template_selections' => $results
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Test template parsing with sample data
     */
    public function testTemplateParsing()
    {
        $templateCode = Flight::request()->data->template_code;
        $invoiceId = Flight::request()->data->invoice_id;
        
        if (!$templateCode || !$invoiceId) {
            return $this->json(['error' => 'Template code and invoice ID required'], 400);
        }
        
        try {
            // Get template
            $template = $this->templateSelector->getTemplateWithInheritance($templateCode);
            
            if (!$template) {
                return $this->json(['error' => 'Template not found'], 404);
            }
            
            // Get invoice for sample data
            $invoice = Invoice::with(['client', 'items'])->find($invoiceId);
            
            if (!$invoice) {
                return $this->json(['error' => 'Invoice not found'], 404);
            }
            
            // Prepare variables
            $variables = $this->prepareTemplateVariables($invoice);
            
            // Parse template
            $parsed = $this->templateSelector->parseWithInheritance($template, $variables);
            
            return $this->json([
                'template' => [
                    'id' => $template->id,
                    'name' => $template->name,
                    'code' => $template->code,
                    'has_parent' => !empty($template->parent_template_id)
                ],
                'variables_used' => array_keys($variables),
                'parsed_content' => $parsed,
                'preview_html' => $this->wrapInEmailLayout($parsed['body'])
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Get template selection rules
     */
    public function getSelectionRules()
    {
        try {
            $stmt = Flight::db()->prepare("
                SELECT * FROM template_selection_rules 
                WHERE is_active = 1 
                ORDER BY priority, name
            ");
            $stmt->execute();
            $rules = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Decode JSON fields
            foreach ($rules as &$rule) {
                $rule['conditions'] = json_decode($rule['conditions'], true);
            }
            
            return $this->json([
                'rules' => $rules,
                'priority_levels' => [
                    1 => 'Critical',
                    2 => 'High',
                    3 => 'Medium',
                    4 => 'Low',
                    5 => 'Default'
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Create or update selection rule
     */
    public function saveSelectionRule()
    {
        $data = Flight::request()->data;
        
        $required = ['name', 'conditions', 'template_code', 'priority'];
        foreach ($required as $field) {
            if (empty($data->$field)) {
                return $this->json(['error' => "Field '$field' is required"], 400);
            }
        }
        
        try {
            $id = $data->id ?? null;
            
            if ($id) {
                // Update existing rule
                $stmt = Flight::db()->prepare("
                    UPDATE template_selection_rules 
                    SET name = ?, description = ?, conditions = ?, 
                        template_code = ?, priority = ?, is_active = ?
                    WHERE id = ?
                ");
                
                $stmt->execute([
                    $data->name,
                    $data->description ?? '',
                    json_encode($data->conditions),
                    $data->template_code,
                    $data->priority,
                    $data->is_active ?? 1,
                    $id
                ]);
            } else {
                // Create new rule
                $stmt = Flight::db()->prepare("
                    INSERT INTO template_selection_rules 
                    (name, description, conditions, template_code, priority, is_active)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $stmt->execute([
                    $data->name,
                    $data->description ?? '',
                    json_encode($data->conditions),
                    $data->template_code,
                    $data->priority,
                    $data->is_active ?? 1
                ]);
                
                $id = Flight::db()->lastInsertId();
            }
            
            // Clear cache
            TemplateSelector::clearCache();
            
            return $this->json([
                'success' => true,
                'rule_id' => $id,
                'message' => 'Selection rule saved successfully'
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Get template usage statistics
     */
    public function getTemplateUsageStats()
    {
        try {
            // Get usage by template
            $stmt = Flight::db()->prepare("
                SELECT 
                    t.id,
                    t.name,
                    t.code,
                    t.usage_count,
                    COUNT(DISTINCT l.id) as email_count,
                    COUNT(DISTINCT l.invoice_id) as invoice_count,
                    MIN(l.created_at) as first_used,
                    MAX(l.created_at) as last_used
                FROM email_templates t
                LEFT JOIN email_logs l ON t.id = l.template_id
                WHERE t.is_active = 1
                GROUP BY t.id
                ORDER BY t.usage_count DESC
            ");
            $stmt->execute();
            $templates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Get usage by time period
            $stmt = Flight::db()->prepare("
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as emails_sent,
                    COUNT(DISTINCT template_id) as templates_used,
                    COUNT(DISTINCT invoice_id) as invoices_emailed
                FROM email_logs
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            ");
            $stmt->execute();
            $dailyStats = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Get selection patterns
            $stmt = Flight::db()->prepare("
                SELECT 
                    template_code,
                    JSON_EXTRACT(selection_metadata, '$.criteria.invoice_type') as invoice_type,
                    JSON_EXTRACT(selection_metadata, '$.criteria.client_type') as client_type,
                    JSON_EXTRACT(selection_metadata, '$.criteria.language') as language,
                    COUNT(*) as selection_count
                FROM template_usage_log
                WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
                GROUP BY template_code, invoice_type, client_type, language
                ORDER BY selection_count DESC
                LIMIT 20
            ");
            $stmt->execute();
            $selectionPatterns = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            return $this->json([
                'templates' => $templates,
                'daily_stats' => $dailyStats,
                'selection_patterns' => $selectionPatterns,
                'summary' => [
                    'total_templates' => count($templates),
                    'total_emails_30d' => array_sum(array_column($dailyStats, 'emails_sent')),
                    'avg_emails_per_day' => round(array_sum(array_column($dailyStats, 'emails_sent')) / 30, 1)
                ]
            ]);
            
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()], 500);
        }
    }
    
    /**
     * Helper methods
     */
    
    private function wouldBeUsedForInvoice($templateType, $invoice)
    {
        $paymentStatus = $this->getPaymentStatus($invoice);
        
        switch ($templateType) {
            case TemplateSelector::TYPE_INVOICE_NEW:
                return $paymentStatus === 'pending' && !$this->isOverdue($invoice);
            case TemplateSelector::TYPE_INVOICE_REMINDER:
                return $paymentStatus === 'pending' && $this->isOverdue($invoice);
            case TemplateSelector::TYPE_INVOICE_OVERDUE:
                return $paymentStatus === 'overdue';
            case TemplateSelector::TYPE_INVOICE_PARTIAL:
                return $paymentStatus === 'partial';
            case TemplateSelector::TYPE_INVOICE_PAID:
                return $paymentStatus === 'paid';
            default:
                return false;
        }
    }
    
    private function getPaymentStatus($invoice)
    {
        if ($invoice->paid_amount >= $invoice->total_amount) {
            return 'paid';
        } elseif ($invoice->paid_amount > 0) {
            return 'partial';
        } elseif ($this->isOverdue($invoice)) {
            return 'overdue';
        }
        return 'pending';
    }
    
    private function isOverdue($invoice)
    {
        return strtotime($invoice->due_date) < time();
    }
    
    private function getClientType($client)
    {
        if (!empty($client->company_name)) {
            return 'company';
        } elseif (isset($client->client_type) && $client->client_type === 'practitioner') {
            return 'practitioner';
        }
        return 'individual';
    }
    
    private function prepareTemplateVariables($invoice)
    {
        $client = $invoice->client;
        
        return [
            'client_name' => $client->name,
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => date('d/m/Y', strtotime($invoice->invoice_date)),
            'due_date' => date('d/m/Y', strtotime($invoice->due_date)),
            'total_amount' => number_format($invoice->total_amount, 2, ',', ' ') . ' €',
            'balance_due' => number_format($invoice->total_amount - $invoice->paid_amount, 2, ',', ' ') . ' €',
            'items' => array_map(function($item) {
                return [
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'total' => number_format($item->total, 2, ',', ' ') . ' €'
                ];
            }, $invoice->items->toArray())
        ];
    }
    
    private function wrapInEmailLayout($body)
    {
        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f4f4f4; padding: 20px; text-align: center; }
        .content { padding: 20px 0; }
        .footer { background: #f4f4f4; padding: 20px; text-align: center; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="content">' . nl2br($body) . '</div>
    </div>
</body>
</html>';
    }
}