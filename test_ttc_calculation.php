<?php
/**
 * Test TTC to NET price calculation for location invoices
 */

// Test case: <PERSON> with Luxembourg VAT
// Course prices: €15 (Individuel) and €30 (Collectif) are TTC prices
// VAT rate: 17%

echo "=== TTC to NET Price Calculation Test ===\n\n";

$courses = [
    ['name' => 'Individuel', 'ttc_price' => 15.00],
    ['name' => 'Collectif', 'ttc_price' => 30.00]
];

$vatRate = 17; // 17% VAT

echo "For <PERSON> (Luxembourg VAT number):\n";
echo "- Should calculate VAT normally\n";
echo "- Prices are TTC (total including tax)\n\n";

foreach ($courses as $course) {
    $priceTTC = $course['ttc_price'];
    $priceNET = $priceTTC / (1 + $vatRate / 100);
    $vatAmount = $priceTTC - $priceNET;
    
    echo "Course: {$course['name']}\n";
    echo "  TTC Price: €" . number_format($priceTTC, 2) . "\n";
    echo "  NET Price: €" . number_format($priceNET, 2) . "\n";
    echo "  VAT Amount: €" . number_format($vatAmount, 2) . "\n";
    echo "  Check: €" . number_format($priceNET, 2) . " + €" . number_format($vatAmount, 2) . " = €" . number_format($priceNET + $vatAmount, 2) . "\n\n";
}

// Calculate totals
$totalTTC = 15.00 + 30.00;
$totalNET = $totalTTC / (1 + $vatRate / 100);
$totalVAT = $totalTTC - $totalNET;

echo "Totals:\n";
echo "  Subtotal (NET): €" . number_format($totalNET, 2) . "\n";
echo "  VAT Amount: €" . number_format($totalVAT, 2) . "\n";
echo "  Total (TTC): €" . number_format($totalTTC, 2) . "\n";

echo "\nWhat should appear on the invoice:\n";
echo "- Individuel: 1 x €" . number_format(15.00 / 1.17, 2) . " = €" . number_format(15.00 / 1.17, 2) . "\n";
echo "- Collectif: 1 x €" . number_format(30.00 / 1.17, 2) . " = €" . number_format(30.00 / 1.17, 2) . "\n";
echo "- Subtotal: €" . number_format($totalNET, 2) . "\n";
echo "- VAT 17%: €" . number_format($totalVAT, 2) . "\n";
echo "- Total: €" . number_format($totalTTC, 2) . "\n";