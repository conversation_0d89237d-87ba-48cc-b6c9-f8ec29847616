<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Helpers\MoneyHelper;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Fixing Invoice 268 ===\n\n";
    
    // Get invoice 268
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 268");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "Invoice 268 not found.\n";
        exit;
    }
    
    echo "Current invoice details:\n";
    echo "Number: " . $invoice['invoice_number'] . "\n";
    echo "Total: €" . number_format($invoice['total'], 2) . "\n";
    echo "Subtotal: €" . number_format($invoice['subtotal'], 2) . "\n";
    echo "VAT: €" . number_format($invoice['vat_amount'], 2) . "\n\n";
    
    // Get invoice items
    $stmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = 268 ORDER BY id");
    $stmt->execute();
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Invoice items:\n";
    $totalQuantity = 0;
    foreach ($items as $item) {
        echo sprintf("- %s: %d x €%.2f (VAT %d%%)\n", 
            $item['description'],
            $item['quantity'],
            $item['unit_price'],
            $item['vat_rate']
        );
        $totalQuantity += $item['quantity'];
    }
    
    echo "\nTotal quantity: $totalQuantity sessions\n";
    
    // For 7 sessions at €30 TTC each, total should be €210
    $expectedTTC = $totalQuantity * 30.00; // €30 per session TTC
    echo "Expected TTC total: €" . number_format($expectedTTC, 2) . "\n\n";
    
    // Recalculate using TTC method
    $vatRate = 17.00; // Luxembourg VAT rate
    $ttcCalc = MoneyHelper::calculateFromTTC($expectedTTC, $vatRate);
    
    echo "Correct calculation:\n";
    echo "TTC Total: €" . number_format($ttcCalc['total'], 2) . "\n";
    echo "NET (Subtotal): €" . number_format($ttcCalc['net'], 2) . "\n";
    echo "VAT: €" . number_format($ttcCalc['vat'], 2) . "\n";
    
    // Update the invoice
    if (abs($invoice['total'] - $expectedTTC) > 0.01) {
        echo "\nUpdating invoice...\n";
        
        $stmt = $pdo->prepare("
            UPDATE invoices 
            SET subtotal = ?,
                vat_amount = ?,
                total = ?,
                updated_at = NOW()
            WHERE id = 268
        ");
        $stmt->execute([$ttcCalc['net'], $ttcCalc['vat'], $ttcCalc['total']]);
        
        // Also update invoice items to match
        $unitTTC = 30.00; // €30 per session
        $unitCalc = MoneyHelper::calculateFromTTC($unitTTC, $vatRate);
        $unitNet = $unitCalc['net'] / 1; // NET price per unit
        
        foreach ($items as $item) {
            $itemTTC = $item['quantity'] * $unitTTC;
            $itemCalc = MoneyHelper::calculateFromTTC($itemTTC, $vatRate);
            
            $stmt = $pdo->prepare("
                UPDATE invoice_items 
                SET unit_price = ?,
                    vat_amount = ?,
                    total_amount = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([
                $unitNet,
                $itemCalc['vat'], 
                $itemCalc['total'], 
                $item['id']
            ]);
        }
        
        echo "✓ Invoice 268 has been fixed!\n";
        echo "New total: €" . number_format($ttcCalc['total'], 2) . "\n";
    } else {
        echo "\n✓ Invoice 268 already has the correct total of €" . number_format($expectedTTC, 2) . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}