<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Patient;
use App\Models\CustomField;
use Flight;

class PatientController extends Controller
{

    /**
     * List all patients with pagination
     */
    public function index()
    {
        $page = intval($_GET['page'] ?? 1);
        $perPage = intval($_GET['per_page'] ?? 20);
        
        $filters = [
            'search' => $this->input('search'),
            'is_active' => $this->input('is_active'),
            'insurance_provider' => $this->input('insurance_provider')
        ];
        
        $patientsData = Patient::getAllPaginated($page, $perPage, $filters);
        $insuranceProviders = Patient::getInsuranceProviders();
        $statistics = Patient::getStatistics();
        
        // Get visible fields for list view
        $visibleFields = CustomField::getVisibleFields('patients', 'list');
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'patients/index-modern';
        
        $this->render($viewFile, [
            'patients' => $patientsData['data'],
            'pagination' => [
                'total' => $patientsData['total'],
                'page' => $patientsData['page'],
                'per_page' => $patientsData['per_page'],
                'total_pages' => $patientsData['total_pages']
            ],
            'filters' => $filters,
            'insurance_providers' => $insuranceProviders,
            'statistics' => $statistics,
            'visible_fields' => $visibleFields,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Show create patient form
     */
    public function create()
    {
        // Get visible fields for form view
        $visibleFields = CustomField::getVisibleFields('patients', 'form');
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'patients/create-modern';
        
        $this->render($viewFile, [
            'patient_number' => Patient::generatePatientNumber(),
            'countries' => $this->getCountriesList(),
            'visible_fields' => $visibleFields,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Store new patient
     */
    public function store()
    {
        if (!$this->validateCsrfToken()) {
            Flight::flash('error', __('common.csrf_error'));
            Flight::redirect('/patients/create');
            return;
        }
        
        $validation = $this->validatePatientData();
        if ($validation !== true) {
            Flight::flash('error', $validation);
            Flight::redirect('/patients/create');
            return;
        }
        
        try {
            $data = [
                'patient_number' => $this->input('patient_number'),
                'first_name' => $this->input('first_name'),
                'last_name' => $this->input('last_name'),
                'birth_date' => (!empty($this->input('birth_date')) && $this->input('birth_date') !== '0000-00-00') ? $this->input('birth_date') : null,
                'gender' => $this->input('gender'),
                'email' => $this->input('email', null, FILTER_SANITIZE_EMAIL),
                'phone' => $this->input('phone'),
                'mobile' => $this->input('mobile'),
                'address_line1' => $this->input('address_line1'),
                'address_line2' => $this->input('address_line2'),
                'city' => $this->input('city'),
                'postal_code' => $this->input('postal_code'),
                'country' => $this->input('country'),
                'national_id' => $this->input('national_id'),
                'insurance_provider' => $this->input('insurance_provider'),
                'insurance_number' => $this->input('insurance_number'),
                'emergency_contact_name' => $this->input('emergency_contact_name'),
                'emergency_contact_phone' => $this->input('emergency_contact_phone'),
                'emergency_contact_relationship' => $this->input('emergency_contact_relationship'),
                'blood_type' => $this->input('blood_type'),
                'allergies' => $this->input('allergies'),
                'chronic_conditions' => $this->input('chronic_conditions'),
                'current_medications' => $this->input('current_medications'),
                'notes' => $this->input('notes'),
                'created_by' => $_SESSION['user_id']
            ];
            
            $patient = new Patient();
            $patientId = $patient->createNew($data);
            
            if ($patientId) {
                // Handle custom fields
                $customValues = [];
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'custom_') === 0) {
                        $fieldName = substr($key, 7); // Remove 'custom_' prefix
                        $customValues[$fieldName] = $value;
                    }
                }
                
                if (!empty($customValues)) {
                    CustomField::saveCustomFieldValues('patients', $patientId, $customValues);
                }
                
                Flight::flash('success', __('patients.created_successfully'));
                Flight::redirect('/patients/' . $patientId);
            } else {
                Flight::flash('error', __('common.error_occurred'));
                Flight::redirect('/patients/create');
            }
        } catch (\Exception $e) {
            Flight::flash('error', __('common.error_occurred'));
            Flight::redirect('/patients/create');
        }
    }

    /**
     * Show patient details
     */
    public function show($id)
    {
        $patient = Patient::getWithDetails($id);
        
        if (!$patient) {
            Flight::notFound();
            return;
        }
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'patients/show-modern';
        
        $this->render($viewFile, [
            'patient' => $patient,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Show edit patient form
     */
    public function edit($id)
    {
        $patient = Patient::findById($id);
        
        if (!$patient) {
            Flight::notFound();
            return;
        }
        
        // Get custom field values
        $patient['custom_fields'] = CustomField::getCustomFieldValues('patients', $id);
        
        // Get visible fields for form view
        $visibleFields = CustomField::getVisibleFields('patients', 'form');
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'patients/edit-modern';
        
        $this->render($viewFile, [
            'patient' => $patient,
            'countries' => $this->getCountriesList(),
            'visible_fields' => $visibleFields,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Update patient
     */
    public function update($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::flash('error', __('common.csrf_error'));
            Flight::redirect('/patients/' . $id . '/edit');
            return;
        }
        
        $patient = Patient::findById($id);
        if (!$patient) {
            Flight::notFound();
            return;
        }
        
        $validation = $this->validatePatientData($id);
        if ($validation !== true) {
            Flight::flash('error', $validation);
            Flight::redirect('/patients/' . $id . '/edit');
            return;
        }
        
        try {
            $data = [
                'first_name' => $this->input('first_name'),
                'last_name' => $this->input('last_name'),
                'birth_date' => (!empty($this->input('birth_date')) && $this->input('birth_date') !== '0000-00-00') ? $this->input('birth_date') : null,
                'gender' => $this->input('gender'),
                'email' => $this->input('email', null, FILTER_SANITIZE_EMAIL),
                'phone' => $this->input('phone'),
                'mobile' => $this->input('mobile'),
                'address_line1' => $this->input('address_line1'),
                'address_line2' => $this->input('address_line2'),
                'city' => $this->input('city'),
                'postal_code' => $this->input('postal_code'),
                'country' => $this->input('country'),
                'national_id' => $this->input('national_id'),
                'insurance_provider' => $this->input('insurance_provider'),
                'insurance_number' => $this->input('insurance_number'),
                'emergency_contact_name' => $this->input('emergency_contact_name'),
                'emergency_contact_phone' => $this->input('emergency_contact_phone'),
                'emergency_contact_relationship' => $this->input('emergency_contact_relationship'),
                'blood_type' => $this->input('blood_type'),
                'allergies' => $this->input('allergies'),
                'chronic_conditions' => $this->input('chronic_conditions'),
                'current_medications' => $this->input('current_medications'),
                'notes' => $this->input('notes'),
                'is_active' => $this->input('is_active') ? 1 : 0,
                'updated_by' => $_SESSION['user_id']
            ];
            
            if (Patient::updateById($id, $data)) {
                // Handle custom fields
                $customValues = [];
                foreach ($_POST as $key => $value) {
                    if (strpos($key, 'custom_') === 0) {
                        $fieldName = substr($key, 7); // Remove 'custom_' prefix
                        $customValues[$fieldName] = $value;
                    }
                }
                
                if (!empty($customValues)) {
                    CustomField::saveCustomFieldValues('patients', $id, $customValues);
                }
                
                Flight::flash('success', __('patients.updated_successfully'));
                Flight::redirect('/patients/' . $id);
            } else {
                Flight::flash('error', __('common.error_occurred'));
                Flight::redirect('/patients/' . $id . '/edit');
            }
        } catch (\Exception $e) {
            Flight::flash('error', __('common.error_occurred'));
            Flight::redirect('/patients/' . $id . '/edit');
        }
    }

    /**
     * Delete patient
     */
    public function destroy($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        $patient = Patient::findById($id);
        if (!$patient) {
            Flight::json(['error' => __('common.not_found')], 404);
            return;
        }
        
        try {
            $patientModel = new Patient();
            if ($patientModel->delete($id)) {
                Flight::json(['success' => true, 'message' => __('patients.deleted_successfully')]);
            } else {
                Flight::json(['error' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['error' => __('common.error_occurred')], 500);
        }
    }

    /**
     * Add note to patient
     */
    public function addNote($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        $patient = Patient::findById($id);
        if (!$patient) {
            Flight::json(['error' => __('common.not_found')], 404);
            return;
        }
        
        $note = $this->input('note');
        if (empty($note)) {
            Flight::json(['error' => __('patients.note_required')], 400);
            return;
        }
        
        try {
            $noteId = Patient::addNote(
                $id,
                $note,
                $this->input('note_type', 'general'),
                $_SESSION['user_id'],
                $this->input('is_private') ? true : false
            );
            
            if ($noteId) {
                Flight::json([
                    'success' => true,
                    'message' => __('patients.note_added'),
                    'note_id' => $noteId
                ]);
            } else {
                Flight::json(['error' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['error' => __('common.error_occurred')], 500);
        }
    }

    /**
     * Search patients for autocomplete
     */
    public function search()
    {
        $query = $this->input('q');
        if (strlen($query) < 2) {
            Flight::json([]);
            return;
        }
        
        try {
            $patients = Patient::search($query, 20);
            Flight::json($patients);
        } catch (\Exception $e) {
            Flight::json([]);
        }
    }

    /**
     * Validate patient data
     */
    private function validatePatientData($id = null): mixed
    {
        $errors = [];
        
        if (empty($this->input('first_name'))) {
            $errors[] = __('patients.first_name_required');
        }
        
        if (empty($this->input('last_name'))) {
            $errors[] = __('patients.last_name_required');
        }
        
        if (empty($this->input('birth_date'))) {
            $errors[] = __('patients.birth_date_required');
        }
        
        $email = $this->input('email');
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = __('common.invalid_email');
        }
        
        if (!empty($errors)) {
            return implode('<br>', $errors);
        }
        
        return true;
    }

    /**
     * Get countries list
     */
    private function getCountriesList(): array
    {
        return [
            'LU' => 'Luxembourg',
            'FR' => 'France',
            'BE' => 'Belgium',
            'DE' => 'Germany',
            'NL' => 'Netherlands',
            'IT' => 'Italy',
            'ES' => 'Spain',
            'PT' => 'Portugal',
            'GB' => 'United Kingdom',
            'CH' => 'Switzerland',
            'AT' => 'Austria',
            'PL' => 'Poland',
            'CZ' => 'Czech Republic',
            'RO' => 'Romania',
            'HU' => 'Hungary',
            'GR' => 'Greece',
            'SE' => 'Sweden',
            'DK' => 'Denmark',
            'NO' => 'Norway',
            'FI' => 'Finland',
            'IE' => 'Ireland'
        ];
    }
}