<?php

namespace App\Models;

use App\Core\Model;
use App\Models\CustomField;
use App\Models\ConfigSettings;
use App\Helpers\NumberFormat;
use PDO;

class Client extends Model
{
    protected $table = 'clients';
    protected $fillable = [
        'client_number', 'client_type', 'first_name', 'last_name', 'birth_date', 'gender',
        'company_name', 'company_registration', 'contact_person',
        'email', 'phone', 'mobile', 'fax', 'website',
        'address_line1', 'address_line2', 'city', 'postal_code', 'country',
        'vat_number', 'tax_exempt', 'payment_terms', 'credit_limit', 'discount_percentage',
        'notes', 'tags', 'is_active', 'created_by', 'updated_by'
    ];
    
    /**
     * Find client by ID
     */
    public static function findById($id)
    {
        $db = \Flight::db();
        $stmt = $db->prepare("SELECT * FROM clients WHERE id = :id LIMIT 1");
        $stmt->execute(['id' => $id]);
        $client = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        // Handle invalid birth_date
        if ($client && isset($client['birth_date'])) {
            if (empty($client['birth_date']) || $client['birth_date'] === '0000-00-00' || $client['birth_date'] === '-0001-11-30') {
                $client['birth_date'] = null;
            }
        }
        
        return $client;
    }
    
    /**
     * Delete client by ID
     */
    public function delete($id = null): bool
    {
        if ($id === null && isset($this->id)) {
            $id = $this->id;
        }
        
        if (!$id) {
            return false;
        }
        
        $db = \Flight::db();
        
        try {
            // Delete from billable_entities first
            $stmt = $db->prepare("DELETE FROM billable_entities WHERE entity_type = 'client' AND entity_id = :id");
            $stmt->execute(['id' => $id]);
            
            // Delete custom field values
            $stmt = $db->prepare("DELETE FROM custom_field_values WHERE module = 'clients' AND entity_id = :id");
            $stmt->execute(['id' => $id]);
            
            // Delete the client
            $stmt = $db->prepare("DELETE FROM clients WHERE id = :id");
            return $stmt->execute(['id' => $id]);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get client by ID (instance method)
     */
    public function getById($id)
    {
        return self::findById($id);
    }
    
    /**
     * Get all clients with optional filters
     */
    public function getAll($filters = [])
    {
        $db = \Flight::db();
        
        $where = ['1=1'];
        $params = [];
        
        if (!empty($filters['client_type'])) {
            if ($filters['client_type'] === 'practitioner') {
                $where[] = 'is_practitioner = 1';
            } else {
                $where[] = 'client_type = :client_type';
                $params[':client_type'] = $filters['client_type'];
            }
        }
        
        if (!empty($filters['is_active'])) {
            $where[] = 'is_active = :is_active';
            $params[':is_active'] = $filters['is_active'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $sql = "SELECT * FROM clients WHERE $whereClause ORDER BY 
                CASE 
                    WHEN client_type = 'individual' THEN CONCAT(first_name, ' ', last_name)
                    ELSE company_name 
                END";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Update client by ID (static method)
     */
    public static function updateById($id, $data): bool
    {
        $db = \Flight::db();
        
        // Remove non-updateable fields
        unset($data['id'], $data['client_number'], $data['created_at'], $data['created_by']);
        
        // Build update query
        $fields = [];
        foreach ($data as $key => $value) {
            $fields[] = "$key = :$key";
        }
        
        $sql = "UPDATE clients SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        $data['id'] = $id;
        
        try {
            $stmt = $db->prepare($sql);
            return $stmt->execute($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get all active clients
     */
    public function getAllActive()
    {
        return $this->getAll(['is_active' => 1]);
    }
    
    /**
     * Create new client (instance method for controller compatibility)
     */
    public function createNew($data)
    {
        $db = \Flight::db();
        
        // Set timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['is_active'] = $data['is_active'] ?? 1;
        
        // Remove 'name' field if it exists (it's a generated column in MySQL)
        if (isset($data['name'])) {
            unset($data['name']);
        }
        
        // Build insert query
        $fields = array_keys($data);
        $placeholders = array_map(function($f) { return ":$f"; }, $fields);
        
        $sql = "INSERT INTO clients (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        try {
            $stmt = $db->prepare($sql);
            error_log('SQL: ' . $sql);
            error_log('Data: ' . json_encode($data));
            
            if ($stmt->execute($data)) {
                return $db->lastInsertId();
            } else {
                // Log the error
                $errorInfo = $stmt->errorInfo();
                error_log('Execute failed. Error info: ' . json_encode($errorInfo));
                return false;
            }
        } catch (\Exception $e) {
            error_log('Exception in createNew: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            return false;
        }
    }
    
    /**
     * Override performUpdate to exclude generated columns
     */
    protected function performUpdate()
    {
        $fields = [];
        $params = [];
        
        foreach ($this->attributes as $key => $value) {
            // Skip primary key and generated columns
            if ($key !== $this->primaryKey && $key !== 'name') {
                $fields[] = "$key = :$key";
                // Convert arrays to JSON for json/array casted fields
                if (is_array($value) && isset($this->casts[$key]) && in_array($this->casts[$key], ['json', 'array'])) {
                    $params[$key] = json_encode($value);
                } else {
                    $params[$key] = $value;
                }
            }
        }
        
        $params['id'] = $this->attributes[$this->primaryKey];
        
        $sql = "UPDATE {$this->table} SET " . implode(', ', $fields) . " WHERE {$this->primaryKey} = :id";
        
        try {
            $stmt = self::db()->prepare($sql);
            return $stmt->execute($params);
        } catch (\PDOException $e) {
            error_log('Client update error: ' . $e->getMessage());
            error_log('SQL: ' . $sql);
            error_log('Params: ' . json_encode($params));
            return false;
        }
    }
    
    /**
     * Generate next client number
     */
    public static function generateClientNumber(): string
    {
        // Get the next sequential number
        $nextNumber = NumberFormat::getNextNumber('client', 'clients', 'client_number');
        
        // Generate the formatted client number
        return NumberFormat::generate('client', $nextNumber);
    }
    
    /**
     * Get display name for client
     */
    public function getDisplayName(): string
    {
        if ($this->client_type === 'company') {
            return $this->company_name;
        } else {
            return trim($this->first_name . ' ' . $this->last_name);
        }
    }
    
    /**
     * Get all clients with pagination
     */
    public static function getAllPaginated($page = 1, $perPage = 20, $filters = []): array
    {
        $db = \Flight::db();
        $offset = ($page - 1) * $perPage;
        
        $where = ['1=1'];
        $params = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $where[] = "(
                client_number LIKE :search OR 
                company_name LIKE :search OR 
                first_name LIKE :search OR 
                last_name LIKE :search OR 
                email LIKE :search OR 
                phone LIKE :search OR
                vat_number LIKE :search
            )";
            $params['search'] = $search;
        }
        
        if (!empty($filters['client_type'])) {
            $where[] = "client_type = :client_type";
            $params['client_type'] = $filters['client_type'];
        }
        
        if (isset($filters['is_active'])) {
            $where[] = "is_active = :is_active";
            $params['is_active'] = $filters['is_active'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Get total count
        $countStmt = $db->prepare("
            SELECT COUNT(*) FROM clients WHERE {$whereClause}
        ");
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // Get clients
        $stmt = $db->prepare("
            SELECT c.*, 
                   u1.first_name as created_by_name,
                   u2.first_name as updated_by_name,
                   CASE 
                       WHEN c.client_type = 'company' THEN c.company_name
                       ELSE CONCAT(c.first_name, ' ', c.last_name)
                   END as display_name
            FROM clients c
            LEFT JOIN users u1 ON c.created_by = u1.id
            LEFT JOIN users u2 ON c.updated_by = u2.id
            WHERE {$whereClause}
            ORDER BY c.created_at DESC
            LIMIT :offset, :limit
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue('offset', $offset, PDO::PARAM_INT);
        $stmt->bindValue('limit', $perPage, PDO::PARAM_INT);
        
        $stmt->execute();
        $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Handle invalid birth_dates in the results
        foreach ($clients as &$client) {
            if (isset($client['birth_date'])) {
                if (empty($client['birth_date']) || $client['birth_date'] === '0000-00-00' || $client['birth_date'] === '-0001-11-30') {
                    $client['birth_date'] = null;
                }
            }
        }
        
        return [
            'data' => $clients,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * Get client by ID with related data
     */
    public static function getWithDetails($id): ?array
    {
        $db = \Flight::db();
        
        // Get client
        $stmt = $db->prepare("
            SELECT c.*, 
                   u1.first_name as created_by_name,
                   u2.first_name as updated_by_name
            FROM clients c
            LEFT JOIN users u1 ON c.created_by = u1.id
            LEFT JOIN users u2 ON c.updated_by = u2.id
            WHERE c.id = :id
        ");
        $stmt->execute(['id' => $id]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$client) {
            return null;
        }
        
        // Handle invalid birth_date
        if (isset($client['birth_date'])) {
            if (empty($client['birth_date']) || $client['birth_date'] === '0000-00-00' || $client['birth_date'] === '-0001-11-30') {
                $client['birth_date'] = null;
            }
        }
        
        // Get additional contacts for companies
        if ($client['client_type'] === 'company') {
            $stmt = $db->prepare("
                SELECT * FROM client_contacts 
                WHERE client_id = :client_id 
                ORDER BY is_primary DESC, contact_name
            ");
            $stmt->execute(['client_id' => $id]);
            $client['contacts'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        // Get documents count
        try {
            $stmt = $db->prepare("
                SELECT COUNT(*) FROM client_documents WHERE client_id = :client_id
            ");
            $stmt->execute(['client_id' => $id]);
            $client['documents_count'] = $stmt->fetchColumn();
        } catch (\PDOException $e) {
            $client['documents_count'] = 0;
        }
        
        // Get invoice statistics
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'invoices'");
            if ($stmt->rowCount() > 0) {
                $stmt = $db->prepare("
                    SELECT 
                        COUNT(*) as total_invoices,
                        SUM(total_amount) as total_revenue,
                        SUM(CASE WHEN status = 'unpaid' THEN total_amount ELSE 0 END) as outstanding_amount
                    FROM invoices 
                    WHERE billable_type = 'client' AND billable_id = :client_id
                ");
                $stmt->execute(['client_id' => $id]);
                $client['invoice_stats'] = $stmt->fetch(PDO::FETCH_ASSOC);
            } else {
                $client['invoice_stats'] = [
                    'total_invoices' => 0,
                    'total_revenue' => 0,
                    'outstanding_amount' => 0
                ];
            }
        } catch (\PDOException $e) {
            $client['invoice_stats'] = [
                'total_invoices' => 0,
                'total_revenue' => 0,
                'outstanding_amount' => 0
            ];
        }
        
        // Get custom field values
        $client['custom_fields'] = CustomField::getCustomFieldValues('clients', $id);
        
        return $client;
    }
    
    /**
     * Search clients for autocomplete
     */
    public static function search($query, $limit = 10): array
    {
        $db = \Flight::db();
        $search = '%' . $query . '%';
        
        $stmt = $db->prepare("
            SELECT 
                id, 
                client_number, 
                client_type,
                company_name,
                first_name, 
                last_name, 
                email,
                phone,
                vat_number,
                CASE 
                    WHEN client_type = 'company' THEN company_name
                    ELSE CONCAT(first_name, ' ', last_name)
                END as display_name
            FROM clients
            WHERE is_active = 1
            AND (
                client_number LIKE :search
                OR company_name LIKE :search
                OR first_name LIKE :search
                OR last_name LIKE :search
                OR CONCAT(first_name, ' ', last_name) LIKE :search
                OR email LIKE :search
                OR vat_number LIKE :search
            )
            ORDER BY display_name
            LIMIT :limit
        ");
        
        $stmt->bindValue('search', $search);
        $stmt->bindValue('limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get client types
     */
    public static function getClientTypes(): array
    {
        return [
            'individual' => __('clients.individual'),
            'company' => __('clients.company')
        ];
    }
    
    /**
     * Get client statistics
     */
    public static function getStatistics(): array
    {
        $db = \Flight::db();
        
        $stats = [];
        
        try {
            // Total clients
            $stmt = $db->query("SELECT COUNT(*) FROM clients WHERE is_active = 1");
            $stats['total_active'] = $stmt->fetchColumn();
            
            // Companies
            $stmt = $db->query("SELECT COUNT(*) FROM clients WHERE is_active = 1 AND client_type = 'company'");
            $stats['total_companies'] = $stmt->fetchColumn();
            
            // Individuals
            $stmt = $db->query("SELECT COUNT(*) FROM clients WHERE is_active = 1 AND client_type = 'individual'");
            $stats['total_individuals'] = $stmt->fetchColumn();
            
            // New clients this month
            $stmt = $db->query("
                SELECT COUNT(*) 
                FROM clients 
                WHERE created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
            ");
            $stats['new_this_month'] = $stmt->fetchColumn();
            
            // By type
            $stmt = $db->query("
                SELECT client_type, COUNT(*) as count 
                FROM clients 
                WHERE is_active = 1 
                GROUP BY client_type
            ");
            $stats['by_type'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        } catch (\PDOException $e) {
            // Default stats if queries fail
            $stats['total_active'] = 0;
            $stats['total_companies'] = 0;
            $stats['total_individuals'] = 0;
            $stats['new_this_month'] = 0;
            $stats['by_type'] = [];
        }
        
        // Total revenue - check if invoices table exists first
        try {
            $stmt = $db->query("SHOW TABLES LIKE 'invoices'");
            if ($stmt->rowCount() > 0) {
                $stmt = $db->query("
                    SELECT 
                        SUM(i.total_amount) as total_revenue,
                        SUM(CASE WHEN i.status = 'unpaid' THEN i.total_amount ELSE 0 END) as outstanding
                    FROM invoices i
                    WHERE i.billable_type = 'client'
                ");
                $revenue = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_revenue'] = $revenue['total_revenue'] ?? 0;
                $stats['outstanding_amount'] = $revenue['outstanding'] ?? 0;
            } else {
                $stats['total_revenue'] = 0;
                $stats['outstanding_amount'] = 0;
            }
        } catch (\PDOException $e) {
            $stats['total_revenue'] = 0;
            $stats['outstanding_amount'] = 0;
        }
        
        return $stats;
    }
    
    /**
     * Create or update billable entity record
     */
    public function updateBillableEntity(): bool
    {
        $db = \Flight::db();
        
        $billingName = $this->client_type === 'company' 
            ? $this->company_name 
            : $this->first_name . ' ' . $this->last_name;
            
        $billingAddress = implode("\n", array_filter([
            $this->address_line1,
            $this->address_line2,
            $this->postal_code . ' ' . $this->city,
            $this->country
        ]));
        
        $stmt = $db->prepare("
            INSERT INTO billable_entities 
            (entity_type, entity_id, billing_name, billing_address, billing_email, 
             billing_phone, vat_number, payment_terms, discount_percentage, is_tax_exempt)
            VALUES 
            ('client', :entity_id, :billing_name, :billing_address, :billing_email,
             :billing_phone, :vat_number, :payment_terms, :discount_percentage, :is_tax_exempt)
            ON DUPLICATE KEY UPDATE
            billing_name = VALUES(billing_name),
            billing_address = VALUES(billing_address),
            billing_email = VALUES(billing_email),
            billing_phone = VALUES(billing_phone),
            vat_number = VALUES(vat_number),
            payment_terms = VALUES(payment_terms),
            discount_percentage = VALUES(discount_percentage),
            is_tax_exempt = VALUES(is_tax_exempt)
        ");
        
        return $stmt->execute([
            'entity_id' => $this->id,
            'billing_name' => $billingName,
            'billing_address' => $billingAddress,
            'billing_email' => $this->email,
            'billing_phone' => $this->phone ?: $this->mobile,
            'vat_number' => $this->vat_number,
            'payment_terms' => $this->payment_terms,
            'discount_percentage' => $this->discount_percentage,
            'is_tax_exempt' => $this->tax_exempt
        ]);
    }
}