<?php
// Quick summary of duplicate invoice lines
require_once __DIR__ . '/vendor/autoload.php';

// Load environment
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Database connection
$db = new PDO(
    "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4", 
    $_ENV['DB_USERNAME'], 
    $_ENV['DB_PASSWORD']
);

// Count exact duplicates
$stmt = $db->query("
    SELECT COUNT(*) as dup_count FROM (
        SELECT invoice_id 
        FROM invoice_lines
        GROUP BY invoice_id, description, quantity, unit_price, vat_rate
        HAVING COUNT(*) > 1
    ) as dups
");
$dupCount = $stmt->fetch()['dup_count'];

// Count affected invoices
$stmt = $db->query("
    SELECT COUNT(DISTINCT invoice_id) as affected FROM (
        SELECT invoice_id 
        FROM invoice_lines
        GROUP BY invoice_id, description, quantity, unit_price, vat_rate
        HAVING COUNT(*) > 1
    ) as dups
");
$affectedCount = $stmt->fetch()['affected'];

// Get most recent affected invoice
$stmt = $db->query("
    SELECT i.invoice_number, i.created_at, COUNT(il.id) as duplicates
    FROM invoices i
    JOIN (
        SELECT invoice_id, COUNT(*) as cnt
        FROM invoice_lines
        GROUP BY invoice_id, description, quantity, unit_price, vat_rate
        HAVING COUNT(*) > 1
    ) dups ON i.id = dups.invoice_id
    JOIN invoice_lines il ON i.id = il.invoice_id
    GROUP BY i.id
    ORDER BY i.created_at DESC
    LIMIT 1
");
$recent = $stmt->fetch();

echo "DUPLICATE INVOICE LINES SUMMARY\n";
echo "==============================\n\n";

if ($dupCount == 0) {
    echo "✓ No duplicate invoice lines found!\n";
    echo "  All invoice lines are unique.\n";
} else {
    echo "⚠️  DUPLICATES FOUND:\n";
    echo "  - Duplicate line groups: $dupCount\n";
    echo "  - Affected invoices: $affectedCount\n";
    if ($recent) {
        echo "  - Most recent: {$recent['invoice_number']} ({$recent['created_at']})\n";
    }
    echo "\nRun /public/check_duplicates_web.php for details.\n";
}

echo "\n";