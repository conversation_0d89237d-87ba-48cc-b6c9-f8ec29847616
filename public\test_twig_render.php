<?php
/**
 * Test Twig Rendering Directly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define APP_PATH
define('APP_PATH', dirname(__DIR__));

// Load vendor autoload
require APP_PATH . '/vendor/autoload.php';

// Load .env
if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

echo "<h1>Testing Twig Template Rendering</h1>";

try {
    // Create Twig environment
    $loader = new \Twig\Loader\FilesystemLoader(APP_PATH . '/app/views');
    $twig = new \Twig\Environment($loader, [
        'cache' => false, // Disable cache for testing
        'debug' => true,
    ]);
    
    // Add debug extension
    $twig->addExtension(new \Twig\Extension\DebugExtension());
    
    // Test data
    $data = [
        'title' => 'Test Dashboard',
        'app_name' => $_ENV['APP_NAME'] ?? 'Fit360 AdminDesk',
        'base_url' => '/fit/public',
        'csrf_token' => $_SESSION['csrf_token'] ?? 'test-token',
        'user' => $_SESSION['user'] ?? ['name' => 'Test User'],
        'greeting' => 'Hello',
        'current_month_name' => date('F Y'),
        'stats' => [
            'total_clients' => 10,
            'total_patients' => 20,
            'total_invoices' => 30,
            'revenue_this_month' => 1000
        ],
        'recent_invoices' => [],
        'monthly_revenue' => [],
        'app' => new \App\Core\TwigArrayWrapper([
            'language' => $_SESSION['user_language'] ?? 'fr',
            'name' => $_ENV['APP_NAME'] ?? 'Fit360 AdminDesk'
        ]),
        'session' => new \App\Core\TwigArrayWrapper($_SESSION),
        'config' => new \App\Core\TwigArrayWrapper([
            'currency_symbol' => '€',
            'date_format' => 'd/m/Y',
            'time_format' => 'H:i'
        ]),
        'activeColorScheme' => null
    ];
    
    // Add Twig functions
    $twig->addFunction(new \Twig\TwigFunction('__', function($key, $params = []) {
        // Simple translation mock
        return $key;
    }));
    
    $twig->addFunction(new \Twig\TwigFunction('url', function($path = '') {
        return '/fit/public' . $path;
    }));
    
    $twig->addFunction(new \Twig\TwigFunction('csrf_field', function() {
        return '<input type="hidden" name="csrf_token" value="' . ($_SESSION['csrf_token'] ?? '') . '">';
    }, ['is_safe' => ['html']]));
    
    $twig->addFunction(new \Twig\TwigFunction('current_language', function() {
        return $_SESSION['user_language'] ?? 'fr';
    }));
    
    // Try to render a simple template first
    echo "<h2>Testing Simple Template:</h2>";
    $simple_template = $twig->createTemplate('<h1>Hello {{ user.name }}!</h1><p>Base URL: {{ base_url }}</p>');
    echo $simple_template->render($data);
    
    // Now try the dashboard template
    echo "<h2>Testing Dashboard Template:</h2>";
    try {
        $output = $twig->render('dashboard-simple.twig', $data);
        echo "<div style='border: 1px solid #0c0; background: #efe; padding: 10px;'>";
        echo "<strong>Success!</strong> Template rendered. Length: " . strlen($output) . " bytes";
        echo "</div>";
        
        // Show a snippet of the output
        echo "<h3>Output Preview:</h3>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; background: #f5f5f5;'>";
        echo htmlspecialchars(substr($output, 0, 500)) . "...";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div style='border: 1px solid #c00; background: #fee; padding: 10px;'>";
        echo "<strong>Template Error:</strong> " . $e->getMessage() . "<br>";
        echo "<small>File: " . $e->getFile() . ":" . $e->getLine() . "</small>";
        echo "</div>";
        
        // If it's a loader error, check which template is missing
        if ($e instanceof \Twig\Error\LoaderError) {
            echo "<p>Checking template files...</p>";
            $templates = ['dashboard-simple.twig', 'base-modern.twig'];
            foreach ($templates as $template) {
                $path = APP_PATH . '/app/views/' . $template;
                echo "- $template: " . (file_exists($path) ? "EXISTS" : "NOT FOUND") . "<br>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='border: 1px solid #c00; background: #fee; padding: 10px;'>";
    echo "<strong>Error:</strong> " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
    echo "</div>";
}