<?php

namespace App\Models;

use Flight;
use PDO;
use Exception;

class DocumentType extends \App\Core\Model
{
    protected $table = 'document_types';
    
    protected $fillable = [
        'code',
        'name',
        'description',
        'prefix',
        'counter_type',
        'current_number',
        'current_year',
        'current_month',
        'is_negative',
        'requires_reference',
        'allowed_references',
        'color',
        'icon',
        'template',
        'email_template',
        'is_active',
        'is_system',
        'sort_order',
        'created_by',
        'updated_by',
        'number_format'
    ];
    
    protected $casts = [
        'name' => 'json',
        'description' => 'json',
        'allowed_references' => 'json',
        'is_negative' => 'boolean',
        'requires_reference' => 'boolean',
        'is_active' => 'boolean',
        'is_system' => 'boolean',
        'current_number' => 'integer',
        'current_year' => 'integer',
        'current_month' => 'integer',
        'sort_order' => 'integer'
    ];
    
    /**
     * Override save to handle JSON encoding
     */
    public function save()
    {
        // Encode JSON fields before saving
        foreach ($this->casts as $key => $type) {
            if ($type === 'json' && isset($this->attributes[$key])) {
                if (is_array($this->attributes[$key])) {
                    $this->attributes[$key] = json_encode($this->attributes[$key], JSON_UNESCAPED_UNICODE);
                }
            }
        }
        
        return parent::save();
    }
    
    /**
     * Get all active document types
     */
    public static function getActiveTypes()
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT dt.*, 
                   JSON_UNQUOTE(JSON_EXTRACT(dt.name, CONCAT('$.', :lang1))) as name,
                   JSON_UNQUOTE(JSON_EXTRACT(dt.description, CONCAT('$.', :lang2))) as description
            FROM document_types dt
            WHERE dt.is_active = 1
            ORDER BY dt.sort_order, dt.name
        ");
        
        $lang = current_language();
        $stmt->execute([':lang1' => $lang, ':lang2' => $lang]);
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get document type by code
     */
    public static function getByCode($code)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM document_types WHERE code = ? AND is_active = 1");
        $stmt->execute([$code]);
        
        $type = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($type) {
            // Decode JSON fields with null checks
            $type['name'] = $type['name'] ? json_decode($type['name'], true) : [];
            $type['description'] = $type['description'] ? json_decode($type['description'], true) : [];
            $type['allowed_references'] = $type['allowed_references'] ? json_decode($type['allowed_references'], true) : [];
        }
        
        return $type;
    }
    
    /**
     * Check if user has permission for document type
     */
    public static function hasPermission($documentTypeCode, $userId = null)
    {
        // For now, allow all document types for logged-in users
        // You can implement proper permission checking later
        $userId = $userId ?? $_SESSION['user_id'] ?? null;
        
        return $userId !== null;
    }
    
    /**
     * Get allowed document types for user
     */
    public static function getUserAllowedTypes($userId = null)
    {
        $allTypes = self::getActiveTypes();
        $allowedTypes = [];
        
        foreach ($allTypes as $type) {
            if (self::hasPermission($type['code'], $userId)) {
                $allowedTypes[] = $type;
            }
        }
        
        return $allowedTypes;
    }
    
    /**
     * Create or update document type
     */
    public function saveDocumentType($data)
    {
        $db = Flight::db();
        
        // Ensure JSON fields are properly encoded
        if (isset($data['name']) && is_array($data['name'])) {
            $data['name'] = json_encode($data['name'], JSON_UNESCAPED_UNICODE);
        }
        
        if (isset($data['description']) && is_array($data['description'])) {
            $data['description'] = json_encode($data['description'], JSON_UNESCAPED_UNICODE);
        }
        
        if (isset($data['allowed_references'])) {
            if (is_array($data['allowed_references']) && !empty($data['allowed_references'])) {
                $data['allowed_references'] = json_encode($data['allowed_references']);
            } elseif (empty($data['allowed_references'])) {
                $data['allowed_references'] = null;
            }
        }
        
        if (!empty($data['id'])) {
            // Update existing
            $allowedFields = [
                'name', 'description', 'prefix', 'counter_type', 
                'is_negative', 'requires_reference', 'allowed_references',
                'color', 'icon', 'template', 'email_template',
                'is_active', 'sort_order'
            ];
            
            $updateData = array_intersect_key($data, array_flip($allowedFields));
            
            if (!empty($updateData)) {
                $setClause = [];
                $params = ['id' => $data['id']];
                
                foreach ($updateData as $field => $value) {
                    $setClause[] = "`{$field}` = :{$field}";
                    $params[$field] = $value;
                }
                
                $sql = "UPDATE document_types SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = :id";
                $stmt = $db->prepare($sql);
                return $stmt->execute($params);
            }
        } else {
            // Create new
            $stmt = $db->prepare("
                INSERT INTO document_types (
                    code, name, description, prefix, counter_type,
                    is_negative, requires_reference, allowed_references,
                    color, icon, template, email_template,
                    is_active, sort_order
                ) VALUES (
                    :code, :name, :description, :prefix, :counter_type,
                    :is_negative, :requires_reference, :allowed_references,
                    :color, :icon, :template, :email_template,
                    :is_active, :sort_order
                )
            ");
            
            return $stmt->execute([
                ':code' => $data['code'],
                ':name' => $data['name'],
                ':description' => $data['description'] ?? null,
                ':prefix' => $data['prefix'],
                ':counter_type' => $data['counter_type'] ?? 'yearly',
                ':is_negative' => $data['is_negative'] ?? 0,
                ':requires_reference' => $data['requires_reference'] ?? 0,
                ':allowed_references' => $data['allowed_references'] ?? null,
                ':color' => $data['color'] ?? '#6c757d',
                ':icon' => $data['icon'] ?? 'bi bi-file-text',
                ':template' => $data['template'] ?? 'default',
                ':email_template' => $data['email_template'] ?? null,
                ':is_active' => $data['is_active'] ?? 1,
                ':sort_order' => $data['sort_order'] ?? 0
            ]);
        }
    }
    
    /**
     * Delete document type (only non-system types)
     */
    public function deleteDocumentType($id)
    {
        $db = Flight::db();
        
        // Check if it's a system type
        $stmt = $db->prepare("SELECT is_system FROM document_types WHERE id = ?");
        $stmt->execute([$id]);
        $type = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($type && $type['is_system']) {
            throw new Exception("Cannot delete system document type");
        }
        
        // Check if there are any documents using this type
        $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE document_type_id = ?");
        $stmt->execute([$id]);
        $count = $stmt->fetchColumn();
        
        if ($count > 0) {
            throw new Exception("Cannot delete document type that has existing documents");
        }
        
        // Delete the type
        $stmt = $db->prepare("DELETE FROM document_types WHERE id = ? AND is_system = 0");
        return $stmt->execute([$id]);
    }
}