{% extends "base-modern.twig" %}

{% block title %}{{ product.name }} - {{ __('products.title') | default('Products') }}{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products">{{ __('products.title') | default('Products') }}</a></li>
    <li class="breadcrumb-item active">{{ product.name }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow-sm">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">{{ product.name }}</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{{ base_url }}/products/{{ product.id }}/edit" class="btn btn-sm btn-primary">
                            <i class="fas fa-edit"></i> {{ __('common.edit') | default('Edit') }}
                        </a>
                        {% if product.is_stockable %}
                        <button class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#adjustStockModal">
                            <i class="fas fa-boxes"></i> {{ __('products.adjust_stock') | default('Adjust Stock') }}
                        </button>
                        {% endif %}
                        <button class="btn btn-sm btn-danger" onclick="deleteProduct({{ product.id }})">
                            <i class="fas fa-trash"></i> {{ __('common.delete') | default('Delete') }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- Product Details -->
                    <div class="col-md-6">
                        <h6 class="mb-3">{{ __('products.basic_info') | default('Basic Information') }}</h6>
                        <table class="table table-sm">
                            <tr>
                                <th width="40%">{{ __('products.code') | default('Code') }}:</th>
                                <td>{{ product.code }}</td>
                            </tr>
                            <tr>
                                <th>{{ __('products.name') | default('Name') }}:</th>
                                <td>{{ product.name }}</td>
                            </tr>
                            <tr>
                                <th>{{ __('products.category') | default('Category') }}:</th>
                                <td>
                                    {% if product.category %}
                                        {% if product.category.icon %}<i class="{{ product.category.icon }}"></i>{% endif %}
                                        {{ product.category.name }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{{ __('products.type') | default('Type') }}:</th>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ __('products.type_' ~ product.item_type) | default(product.item_type|capitalize) }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>{{ __('common.status') | default('Status') }}:</th>
                                <td>
                                    {% if product.is_active %}
                                        <span class="badge bg-success">{{ __('common.active') | default('Active') }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ __('common.inactive') | default('Inactive') }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if product.description %}
                            <tr>
                                <th>{{ __('products.description') | default('Description') }}:</th>
                                <td>{{ product.description }}</td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                    
                    <!-- Pricing & Stock -->
                    <div class="col-md-6">
                        <h6 class="mb-3">{{ __('products.pricing_stock') | default('Pricing & Stock') }}</h6>
                        <table class="table table-sm">
                            <tr>
                                <th width="40%">{{ __('products.unit_price') | default('Unit Price') }}:</th>
                                <td><strong>€{{ product.unit_price|number_format(2) }}</strong></td>
                            </tr>
                            <tr>
                                <th>{{ __('products.vat_rate') | default('VAT Rate') }}:</th>
                                <td>
                                    {% if product.vatRate %}
                                        {{ product.vatRate.name }} ({{ product.vatRate.rate }}%)
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{{ __('products.price_with_vat') | default('Price with VAT') }}:</th>
                                <td><strong>€{{ product.getPriceWithVat()|number_format(2) }}</strong></td>
                            </tr>
                            {% if product.is_stockable %}
                            <tr>
                                <th>{{ __('products.current_stock') | default('Current Stock') }}:</th>
                                <td>
                                    <span class="badge bg-{{ product.getStockStatusClass() }}">
                                        {{ product.current_stock }}
                                    </span>
                                    {% if product.current_stock <= product.low_stock_alert %}
                                        <span class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            {{ __('products.low_stock') | default('Low stock') }}
                                        </span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{{ __('products.low_stock_alert') | default('Low Stock Alert') }}:</th>
                                <td>{{ product.low_stock_alert }}</td>
                            </tr>
                            {% endif %}
                            {% if product.quick_sale_button %}
                            <tr>
                                <th>{{ __('products.quick_sale') | default('Quick Sale') }}:</th>
                                <td>
                                    <span class="badge" style="background-color: {{ product.button_color }}; color: white;">
                                        {{ __('common.enabled') | default('Enabled') }} (Order: {{ product.button_order }})
                                    </span>
                                </td>
                            </tr>
                            {% endif %}
                        </table>
                    </div>
                </div>
                
                <!-- Stock Movements -->
                {% if product.is_stockable and stockMovements %}
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="mb-3">{{ __('products.stock_movements') | default('Recent Stock Movements') }}</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ __('common.date') | default('Date') }}</th>
                                    <th>{{ __('products.movement_type') | default('Type') }}</th>
                                    <th>{{ __('products.quantity') | default('Quantity') }}</th>
                                    <th>{{ __('products.reference') | default('Reference') }}</th>
                                    <th>{{ __('common.user') | default('User') }}</th>
                                    <th>{{ __('common.notes') | default('Notes') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for movement in stockMovements %}
                                <tr>
                                    <td>{{ movement.created_at|date('d/m/Y H:i') }}</td>
                                    <td>
                                        <span class="badge bg-{{ movement.movement_type == 'sale' ? 'danger' : (movement.movement_type == 'purchase' ? 'success' : 'info') }}">
                                            {{ __('products.movement_' ~ movement.movement_type) | default(movement.movement_type|capitalize) }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if movement.quantity > 0 %}
                                            <span class="text-success">+{{ movement.quantity }}</span>
                                        {% else %}
                                            <span class="text-danger">{{ movement.quantity }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ movement.reference_number | default('-') }}</td>
                                    <td>{{ movement.user_name | default('-') }}</td>
                                    <td>{{ movement.notes | default('-') }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
                
                <!-- Sales History -->
                {% if salesHistory %}
                <div class="row mt-4">
                    <div class="col-12">
                        <h6 class="mb-3">{{ __('products.sales_history') | default('Recent Sales') }}</h6>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>{{ __('common.date') | default('Date') }}</th>
                                    <th>{{ __('invoices.invoice_number') | default('Invoice') }}</th>
                                    <th>{{ __('clients.client') | default('Client') }}</th>
                                    <th>{{ __('products.quantity') | default('Quantity') }}</th>
                                    <th>{{ __('products.unit_price') | default('Unit Price') }}</th>
                                    <th>{{ __('common.total') | default('Total') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sale in salesHistory %}
                                <tr>
                                    <td>{{ sale.issue_date|date('d/m/Y') }}</td>
                                    <td>
                                        <a href="{{ base_url }}/invoices/{{ sale.invoice_id }}">{{ sale.invoice_number }}</a>
                                    </td>
                                    <td>{{ sale.client_name }}</td>
                                    <td>{{ sale.quantity|number_format(2) }}</td>
                                    <td>€{{ sale.unit_price|number_format(2) }}</td>
                                    <td>€{{ sale.line_total|number_format(2) }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
{% if product.is_stockable %}
<div class="modal fade" id="adjustStockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="adjustStockForm" method="POST" action="{{ base_url }}/products/{{ product.id }}/adjust-stock">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('products.adjust_stock') | default('Adjust Stock') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ __('products.product') | default('Product') }}</label>
                        <input type="text" class="form-control" value="{{ product.name }}" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('products.current_stock') | default('Current Stock') }}</label>
                        <input type="text" class="form-control" value="{{ product.current_stock }}" readonly>
                    </div>
                    
                    <div class="mb-3">
                        <label for="adjustment_type" class="form-label">
                            {{ __('products.adjustment_type') | default('Adjustment Type') }}
                        </label>
                        <select class="form-select" id="adjustment_type" name="type" required>
                            <option value="adjustment">{{ __('products.adjustment') | default('Adjustment') }}</option>
                            <option value="purchase">{{ __('products.purchase') | default('Purchase') }}</option>
                            <option value="return">{{ __('products.return') | default('Return') }}</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="quantity" class="form-label">
                            {{ __('products.quantity') | default('Quantity') }}
                        </label>
                        <input type="number" class="form-control" id="quantity" name="quantity" 
                               step="1" required>
                        <small class="text-muted">
                            {{ __('products.quantity_help') | default('Use positive numbers to add, negative to remove') }}
                        </small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ __('common.notes') | default('Notes') }}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') | default('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        {{ __('common.save') | default('Save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}

<script>
// Stock adjustment form
{% if product.is_stockable %}
document.getElementById('adjustStockForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    fetch(this.action, {
        method: 'POST',
        body: new FormData(this),
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || 'Error adjusting stock');
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
});
{% endif %}

// Delete product
function deleteProduct(id) {
    if (!confirm('{{ __("common.confirm_delete") | default("Are you sure you want to delete this product?") }}')) {
        return;
    }
    
    fetch(`{{ base_url }}/products/${id}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect;
        } else {
            alert(data.message || 'Error deleting product');
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
}
</script>
{% endblock %}