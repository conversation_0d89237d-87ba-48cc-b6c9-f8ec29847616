<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
.info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; margin: 10px 0; }
button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: black; }
.btn-danger { background: #dc3545; color: white; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>';

echo "<h1>Fix Invoice Number FAC-2025-0186</h1>";

// Check current situation
$stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
$stmt->execute(['FAC-2025-0186']);
$invoice186 = $stmt->fetch(\PDO::FETCH_ASSOC);

$stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
$stmt->execute(['FAC-2025-0187']);
$invoice187 = $stmt->fetch(\PDO::FETCH_ASSOC);

// Get current sequence
$stmt = $db->query("
    SELECT * FROM document_sequences 
    WHERE document_type_id = (SELECT id FROM document_types WHERE code = 'invoice') 
    AND year = 2025 
    ORDER BY id DESC 
    LIMIT 1
");
$sequence = $stmt->fetch(\PDO::FETCH_ASSOC);

echo '<div class="section">';
echo '<h2>Current Situation</h2>';
echo '<table>';
echo '<tr><th>Item</th><th>Status</th></tr>';
echo '<tr><td>Current Sequence</td><td>' . ($sequence['last_number'] ?? 'Not found') . '</td></tr>';
echo '<tr><td>FAC-2025-0186 exists?</td><td>' . ($invoice186 ? 'Yes (ID: ' . $invoice186['id'] . ')' : 'No') . '</td></tr>';
echo '<tr><td>FAC-2025-0187 exists?</td><td>' . ($invoice187 ? 'Yes (ID: ' . $invoice187['id'] . ', Status: ' . $invoice187['status'] . ')' : 'No') . '</td></tr>';
echo '</table>';
echo '</div>';

// Handle different scenarios
if (isset($_POST['action'])) {
    $action = $_POST['action'];
    
    try {
        $db->beginTransaction();
        
        switch ($action) {
            case 'change_187_to_186':
                if (!$invoice186 && $invoice187) {
                    $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
                    $stmt->execute(['FAC-2025-0186', $invoice187['id']]);
                    echo '<div class="success">✓ Changed FAC-2025-0187 to FAC-2025-0186</div>';
                }
                break;
                
            case 'delete_186':
                if ($invoice186) {
                    // Delete related records first
                    $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                    $stmt->execute([$invoice186['id']]);
                    
                    $stmt = $db->prepare("DELETE FROM invoices WHERE id = ?");
                    $stmt->execute([$invoice186['id']]);
                    
                    echo '<div class="success">✓ Deleted existing FAC-2025-0186</div>';
                }
                break;
                
            case 'update_sequence_to_186':
                $stmt = $db->prepare("
                    UPDATE document_sequences 
                    SET last_number = 186 
                    WHERE document_type_id = (SELECT id FROM document_types WHERE code = 'invoice') 
                    AND year = 2025
                ");
                $stmt->execute();
                echo '<div class="success">✓ Updated sequence to 186 (next invoice will be 0187)</div>';
                break;
                
            case 'reset_sequence_to_185':
                $stmt = $db->prepare("
                    UPDATE document_sequences 
                    SET last_number = 185 
                    WHERE document_type_id = (SELECT id FROM document_types WHERE code = 'invoice') 
                    AND year = 2025
                ");
                $stmt->execute();
                echo '<div class="success">✓ Reset sequence to 185 (next invoice will be 0186)</div>';
                break;
        }
        
        $db->commit();
        
        // Refresh page after 2 seconds
        echo '<div class="info">Refreshing page in 2 seconds...</div>';
        echo '<script>setTimeout(() => window.location.reload(), 2000);</script>';
        
    } catch (Exception $e) {
        $db->rollBack();
        echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
    }
}

// Show available actions
echo '<div class="section">';
echo '<h2>Available Actions</h2>';

if (!$invoice186 && $invoice187) {
    echo '<div class="info">';
    echo '<strong>Recommended Action:</strong> Change FAC-2025-0187 to FAC-2025-0186';
    echo '</div>';
    
    echo '<form method="post" style="display: inline;">';
    echo '<input type="hidden" name="action" value="change_187_to_186">';
    echo '<button type="submit" class="btn-success">Change FAC-2025-0187 to FAC-2025-0186</button>';
    echo '</form>';
    
} elseif ($invoice186 && $invoice187) {
    echo '<div class="warning">';
    echo '<strong>Both FAC-2025-0186 and FAC-2025-0187 exist!</strong><br>';
    echo 'You have several options:';
    echo '</div>';
    
    if ($invoice186['status'] === 'draft') {
        echo '<form method="post" style="display: inline;">';
        echo '<input type="hidden" name="action" value="delete_186">';
        echo '<button type="submit" class="btn-danger" onclick="return confirm(\'Delete FAC-2025-0186?\')">Delete FAC-2025-0186 (Draft)</button>';
        echo '</form>';
    }
    
    echo '<br><br>OR<br><br>';
    
    echo '<form method="post" style="display: inline;">';
    echo '<input type="hidden" name="action" value="update_sequence_to_186">';
    echo '<button type="submit" class="btn-warning">Keep both invoices and update sequence to 186</button>';
    echo '</form>';
    
} else {
    echo '<div class="success">';
    echo '<strong>Great!</strong> FAC-2025-0186 is available.';
    echo '</div>';
}

echo '</div>';

// Sequence management
echo '<div class="section">';
echo '<h2>Sequence Management</h2>';
echo '<p>Current sequence: <strong>' . ($sequence['last_number'] ?? 'Not set') . '</strong></p>';
echo '<p>Next new invoice will be: <strong>FAC-2025-' . str_pad(($sequence['last_number'] ?? 0) + 1, 4, '0', STR_PAD_LEFT) . '</strong></p>';

echo '<form method="post" style="display: inline;">';
echo '<input type="hidden" name="action" value="reset_sequence_to_185">';
echo '<button type="submit" class="btn-primary">Set sequence to 185</button>';
echo '</form>';

echo '<form method="post" style="display: inline;">';
echo '<input type="hidden" name="action" value="update_sequence_to_186">';
echo '<button type="submit" class="btn-primary">Set sequence to 186</button>';
echo '</form>';

echo '</div>';

// Manual edit option
if ($invoice187 && $invoice187['status'] === 'draft') {
    echo '<div class="section">';
    echo '<h2>Manual Edit Option</h2>';
    echo '<p>Since FAC-2025-0187 is in draft status, you can also:</p>';
    echo '<a href="/fit/public/invoices/' . $invoice187['id'] . '/edit" class="btn-primary" style="text-decoration: none; display: inline-block;">Edit Invoice FAC-2025-0187 Manually</a>';
    echo '<p><small>You can now change the invoice number in the edit form since it\'s a draft.</small></p>';
    echo '</div>';
}