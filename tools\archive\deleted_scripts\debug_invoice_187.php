<?php
// Debug and fix invoice 187 issue

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Debug Invoice 187 Issue</h2>";
    
    // 1. Check all invoices with 187
    echo "<h3>1. All Invoices containing '187'</h3>";
    $stmt = $db->prepare("
        SELECT i.*, it.name as type_name, it.prefix as type_prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.invoice_number LIKE '%187%'
        ORDER BY i.id DESC
    ");
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invoices) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Invoice Number</th><th>Type ID</th><th>Type Name</th><th>Type Prefix</th><th>Status</th><th>Created</th></tr>";
        foreach ($invoices as $inv) {
            echo "<tr>";
            echo "<td>" . $inv['id'] . "</td>";
            echo "<td><strong>" . $inv['invoice_number'] . "</strong></td>";
            echo "<td>" . ($inv['type_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($inv['type_name'] ?? 'No type') . "</td>";
            echo "<td>" . ($inv['type_prefix'] ?? 'No prefix') . "</td>";
            echo "<td>" . $inv['status'] . "</td>";
            echo "<td>" . $inv['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Get the most recent one
        $currentInvoice = $invoices[0];
        $invoiceId = $currentInvoice['id'];
    } else {
        echo "<p>No invoices found with '187' in the number.</p>";
        $currentInvoice = null;
    }
    
    // 2. Check invoice items
    if ($currentInvoice) {
        echo "<h3>2. Invoice Items</h3>";
        $stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = ?");
        $stmt->execute([$invoiceId]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th></tr>";
        foreach ($items as $item) {
            echo "<tr>";
            echo "<td>" . $item['description'] . "</td>";
            echo "<td>" . $item['quantity'] . "</td>";
            echo "<td>" . $item['unit_price'] . "</td>";
            echo "<td>" . $item['vat_rate'] . "%</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check if it's a rental invoice based on items
        $isRental = false;
        foreach ($items as $item) {
            if (stripos($item['description'], 'loyer') !== false) {
                $isRental = true;
                break;
            }
        }
        
        if ($isRental) {
            echo "<p style='color: orange;'>⚠️ This appears to be a rental invoice but ";
            if ($currentInvoice['type_prefix'] != 'LOY') {
                echo "does NOT have the LOY prefix!</p>";
            } else {
                echo "has the correct LOY prefix.</p>";
            }
        }
    }
    
    // 3. Check current sequence
    echo "<h3>3. Document Sequences</h3>";
    $stmt = $db->query("
        SELECT ds.*, dt.code, dt.prefix
        FROM document_sequences ds
        JOIN document_types dt ON ds.document_type_id = dt.id
        WHERE dt.code = 'invoice' AND ds.year = 2025
    ");
    $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($sequence) {
        echo "<p>Current sequence for invoices (2025): <strong>" . $sequence['last_number'] . "</strong></p>";
        echo "<p>Next invoice will be: <strong>" . ($sequence['last_number'] + 1) . "</strong></p>";
    }
    
    // 4. Check deleted numbers pool
    echo "<h3>4. Deleted Numbers Pool</h3>";
    $stmt = $db->query("
        SELECT * FROM deleted_invoice_numbers 
        WHERE year = 2025 AND sequence_number >= 185
        ORDER BY sequence_number
    ");
    $deleted = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($deleted) > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Invoice Number</th><th>Sequence</th><th>Available</th></tr>";
        foreach ($deleted as $del) {
            echo "<tr>";
            echo "<td>" . $del['invoice_number'] . "</td>";
            echo "<td>" . $del['sequence_number'] . "</td>";
            echo "<td>" . ($del['reused_at'] ? 'No (reused)' : 'Yes') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No numbers in deleted pool for 2025 >= 185</p>";
    }
    
    // 5. Check invoice types configuration
    echo "<h3>5. Invoice Types Configuration</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE prefix IS NOT NULL ORDER BY id");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Prefix</th></tr>";
    foreach ($types as $type) {
        echo "<tr>";
        echo "<td>" . $type['id'] . "</td>";
        echo "<td>" . $type['name'] . "</td>";
        echo "<td><strong>" . $type['prefix'] . "</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Actions
    if ($currentInvoice && $currentInvoice['status'] == 'draft') {
        echo "<h3>Available Actions</h3>";
        echo '<form method="POST">';
        echo '<p><strong>Choose an action:</strong></p>';
        
        if ($isRental && $currentInvoice['type_prefix'] != 'LOY') {
            echo '<label><input type="radio" name="action" value="update_prefix" checked> ';
            echo 'Update invoice to FAC-LOY-2025-0187 (add LOY prefix)</label><br><br>';
        }
        
        echo '<label><input type="radio" name="action" value="delete_and_reset"> ';
        echo 'Delete this invoice and reset sequence to 185</label><br><br>';
        
        echo '<label><input type="radio" name="action" value="update_type_id"> ';
        echo 'Update invoice type_id to 1 (Loyer) if not set</label><br><br>';
        
        echo '<button type="submit" style="font-size: 16px; padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px;">';
        echo 'Execute Action</button>';
        echo '</form>';
        
        // Handle action
        if (isset($_POST['action'])) {
            echo "<h3>Executing Action: " . $_POST['action'] . "</h3>";
            
            switch ($_POST['action']) {
                case 'update_prefix':
                    $newNumber = 'FAC-LOY-2025-0187';
                    $stmt = $db->prepare("UPDATE invoices SET invoice_number = ?, type_id = 1 WHERE id = ?");
                    $stmt->execute([$newNumber, $invoiceId]);
                    echo "✓ Updated invoice number to: <strong>$newNumber</strong><br>";
                    echo "✓ Set type_id to 1 (Loyer)<br>";
                    break;
                    
                case 'delete_and_reset':
                    // Delete invoice items first
                    $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                    $stmt->execute([$invoiceId]);
                    
                    // Delete invoice
                    $stmt = $db->prepare("DELETE FROM invoices WHERE id = ?");
                    $stmt->execute([$invoiceId]);
                    echo "✓ Deleted invoice<br>";
                    
                    // Reset sequence to 185
                    $stmt = $db->prepare("UPDATE document_sequences SET last_number = 185 WHERE document_type_id = 1 AND year = 2025");
                    $stmt->execute();
                    echo "✓ Reset sequence to 185<br>";
                    echo "<strong>Next invoice will be 0186</strong><br>";
                    break;
                    
                case 'update_type_id':
                    if (!$currentInvoice['type_id']) {
                        $stmt = $db->prepare("UPDATE invoices SET type_id = 1 WHERE id = ?");
                        $stmt->execute([$invoiceId]);
                        echo "✓ Updated type_id to 1 (Loyer)<br>";
                    } else {
                        echo "! Invoice already has type_id: " . $currentInvoice['type_id'] . "<br>";
                    }
                    break;
            }
            
            echo '<br><a href="' . $_SERVER['PHP_SELF'] . '" style="text-decoration: none; color: blue;">Refresh page to see changes</a>';
        }
    } elseif ($currentInvoice && $currentInvoice['status'] != 'draft') {
        echo "<p style='color: red;'>⚠️ Invoice is not in draft status. Cannot modify.</p>";
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>