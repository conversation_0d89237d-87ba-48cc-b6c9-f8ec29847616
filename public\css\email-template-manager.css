/**
 * Email Template Manager Styles
 * Modern, responsive CSS for the email template UI
 */

/* Base Layout */
.email-template-manager {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    gap: 20px;
    height: calc(100vh - 150px);
    padding: 20px;
}

@media (max-width: 1400px) {
    .email-template-manager {
        grid-template-columns: 280px 1fr;
    }
    
    .email-template-manager .sidebar-right {
        display: none;
    }
}

@media (max-width: 768px) {
    .email-template-manager {
        grid-template-columns: 1fr;
        height: auto;
    }
}

/* Template List */
.template-list {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.template-list-header {
    padding: 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.template-list-body {
    max-height: calc(100vh - 250px);
    overflow-y: auto;
}

.template-item {
    padding: 16px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s;
    position: relative;
}

.template-item:hover {
    background: #f8f9fa;
}

.template-item.selected {
    background: #e7f3ff;
    border-left: 4px solid #0066cc;
    padding-left: 12px;
}

.template-item.active::after {
    content: 'Active';
    position: absolute;
    top: 8px;
    right: 8px;
    background: #28a745;
    color: white;
    font-size: 11px;
    padding: 2px 8px;
    border-radius: 12px;
}

.template-item.dragging {
    opacity: 0.5;
}

.template-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.template-header h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.priority-badge {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.priority-badge.priority-high,
.priority-badge.priority-9,
.priority-badge.priority-10 {
    background: #dc3545;
    color: white;
}

.priority-badge.priority-medium,
.priority-badge.priority-5,
.priority-badge.priority-6,
.priority-badge.priority-7,
.priority-badge.priority-8 {
    background: #ffc107;
    color: #212529;
}

.priority-badge.priority-low,
.priority-badge.priority-1,
.priority-badge.priority-2,
.priority-badge.priority-3,
.priority-badge.priority-4 {
    background: #6c757d;
    color: white;
}

.template-description {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
}

.condition-indicator {
    display: inline-block;
    margin-top: 8px;
    font-size: 12px;
    color: #0066cc;
    font-weight: 500;
}

/* Editor Panel */
.editor-panel {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.editor-tabs {
    display: flex;
    border-bottom: 2px solid #dee2e6;
    padding: 0 16px;
}

.editor-tab {
    padding: 12px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    border-bottom: 2px solid transparent;
    margin-bottom: -2px;
    transition: all 0.2s;
}

.editor-tab:hover {
    color: #495057;
}

.editor-tab.active {
    color: #0066cc;
    border-bottom-color: #0066cc;
}

.tab-content {
    display: none;
    padding: 20px;
    overflow-y: auto;
    flex: 1;
}

.tab-content.active {
    display: block;
}

/* Form Controls */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.15s;
}

.form-control:focus {
    outline: none;
    border-color: #0066cc;
    box-shadow: 0 0 0 3px rgba(0, 102, 204, 0.1);
}

/* Editor with Line Numbers */
.editor-wrapper {
    position: relative;
    display: flex;
    border: 1px solid #ced4da;
    border-radius: 4px;
    overflow: hidden;
}

.line-numbers {
    background: #f8f9fa;
    padding: 8px;
    text-align: right;
    color: #6c757d;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    line-height: 20px;
    user-select: none;
    border-right: 1px solid #dee2e6;
}

.editor-wrapper textarea {
    flex: 1;
    border: none;
    padding: 8px 12px;
    font-family: 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    line-height: 20px;
    resize: vertical;
    min-height: 300px;
}

/* Variable Picker */
.variable-picker {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 16px;
}

.variable-category {
    margin-bottom: 20px;
}

.category-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #495057;
}

.variable-list {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.variable-tag {
    display: inline-block;
    padding: 4px 10px;
    background: #e7f3ff;
    color: #0066cc;
    border-radius: 16px;
    font-size: 13px;
    font-family: monospace;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 44px;
    display: flex;
    align-items: center;
}

.variable-tag:hover {
    background: #0066cc;
    color: white;
}

/* Autocomplete Dropdown */
.autocomplete-dropdown {
    background: white;
    border: 1px solid #ced4da;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.autocomplete-item {
    padding: 10px 12px;
    cursor: pointer;
    transition: background 0.2s;
    min-height: 44px;
}

.autocomplete-item:hover {
    background: #f8f9fa;
}

.autocomplete-item strong {
    display: block;
    font-family: monospace;
    color: #0066cc;
}

.autocomplete-item small {
    color: #6c757d;
    font-size: 12px;
}

/* Condition Builder */
.condition-builder {
    margin-top: 20px;
}

.condition-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 12px;
}

.condition-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.condition-number {
    font-weight: 600;
    color: #495057;
}

.btn-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 4px 8px;
    min-height: 44px;
    min-width: 44px;
}

.btn-remove:hover {
    background: #dc3545;
    color: white;
    border-radius: 4px;
}

.condition-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 12px;
}

.condition-row label {
    min-width: 80px;
    font-weight: 500;
}

.condition-field,
.condition-operator,
.condition-value,
.condition-template {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    min-height: 44px;
}

/* Preview Panel */
.email-preview {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
}

.preview-header {
    margin-bottom: 20px;
}

.preview-meta {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    font-size: 14px;
    color: #6c757d;
}

.preview-subject,
.preview-body,
.preview-footer {
    margin-bottom: 20px;
}

.preview-subject label,
.preview-body label,
.preview-footer label {
    display: block;
    font-weight: 600;
    margin-bottom: 8px;
    color: #495057;
}

.subject-content {
    font-size: 18px;
    font-weight: 500;
}

.body-content,
.footer-content {
    background: white;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.variable-highlight {
    background: #fff3cd;
    color: #856404;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 0.9em;
}

/* Test Module */
.invoice-details-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.detail-item label {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.detail-item span {
    font-size: 14px;
    font-weight: 500;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-badge.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-badge.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.status-overdue {
    background: #f8d7da;
    color: #721c24;
}

/* Modal */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.modal-dialog {
    background: white;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    width: 44px;
    height: 44px;
}

.modal-body {
    padding: 20px;
    max-height: calc(90vh - 140px);
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 16px 20px;
    background: white;
    border-radius: 4px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 12px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s;
    z-index: 10000;
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-warning {
    border-left: 4px solid #ffc107;
}

.notification-info {
    border-left: 4px solid #17a2b8;
}

/* Buttons */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: #0066cc;
    color: white;
}

.btn-primary:hover {
    background: #0052a3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

/* Touch-friendly adjustments */
@media (pointer: coarse) {
    .template-item,
    .variable-tag,
    .autocomplete-item,
    .btn,
    .form-control,
    .condition-field,
    .condition-operator,
    .condition-value,
    .condition-template {
        min-height: 44px;
    }
    
    .close-btn {
        width: 44px;
        height: 44px;
    }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

/* Log Entries */
.log-entry {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    font-size: 14px;
}

.log-time {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 4px;
}

.log-details {
    color: #495057;
}

/* Loading State */
.icon-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}