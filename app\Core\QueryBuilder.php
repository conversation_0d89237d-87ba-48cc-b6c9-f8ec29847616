<?php

namespace App\Core;

use PDO;

class QueryBuilder
{
    protected $model;
    protected $wheres = [];
    protected $orders = [];
    protected $limit = null;
    protected $offset = null;
    
    public function __construct($model, $wheres = [], $orders = [])
    {
        $this->model = $model;
        $this->wheres = $wheres;
        $this->orders = $orders;
    }
    
    /**
     * Add where clause
     */
    public function where($field, $operator = null, $value = null)
    {
        if (is_array($field)) {
            foreach ($field as $key => $val) {
                $this->wheres[] = [$key, '=', $val];
            }
        } else {
            if ($value === null) {
                $value = $operator;
                $operator = '=';
            }
            $this->wheres[] = [$field, $operator, $value];
        }
        
        return $this;
    }
    
    /**
     * Add where null clause
     */
    public function whereNull($field)
    {
        $this->wheres[] = [$field, 'IS', null];
        return $this;
    }

    /**
     * Add where not null clause
     */
    public function whereNotNull($field)
    {
        $this->wheres[] = [$field, 'IS NOT', null];
        return $this;
    }

    /**
     * Add where in clause
     */
    public function whereIn($field, $values)
    {
        if (empty($values)) {
            // If no values, add a condition that will never match
            $this->wheres[] = [$field, '=', '__NEVER_MATCH__'];
        } else {
            $this->wheres[] = [$field, 'IN', $values];
        }
        return $this;
    }

    /**
     * Add where not in clause
     */
    public function whereNotIn($field, $values)
    {
        if (empty($values)) {
            // If no values, don't add any condition (all records match)
            return $this;
        } else {
            $this->wheres[] = [$field, 'NOT IN', $values];
        }
        return $this;
    }

    /**
     * Add order by
     */
    public function orderBy($field, $direction = 'ASC')
    {
        $this->orders[] = [$field, $direction];
        return $this;
    }
    
    /**
     * Set limit
     */
    public function limit($limit)
    {
        $this->limit = $limit;
        return $this;
    }
    
    /**
     * Set offset
     */
    public function offset($offset)
    {
        $this->offset = $offset;
        return $this;
    }
    
    /**
     * Get all results
     */
    public function get()
    {
        $sql = $this->buildQuery();
        $stmt = $this->model::db()->prepare($sql);
        $stmt->execute($this->getBindings());
        
        $results = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $model = clone $this->model;
            $model->fill($row);
            $results[] = $model;
        }
        
        return new Collection($results);
    }
    
    /**
     * Get first result
     */
    public function first()
    {
        $this->limit = 1;
        $results = $this->get();
        return $results ? $results[0] : null;
    }

    /**
     * Check if any records exist
     */
    public function exists()
    {
        return $this->count() > 0;
    }
    
    /**
     * Update records
     */
    public function update($data)
    {
        $fields = [];
        $params = [];
        
        foreach ($data as $key => $value) {
            $fields[] = "$key = :update_$key";
            $params["update_$key"] = $value;
        }
        
        $sql = "UPDATE {$this->model->getTable()} SET " . implode(', ', $fields);
        
        if ($this->wheres) {
            $sql .= " WHERE " . $this->buildWhereClause();
            $params = array_merge($params, $this->getBindings());
        }
        
        $stmt = $this->model::db()->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Delete records
     */
    public function delete()
    {
        $sql = "DELETE FROM {$this->model->getTable()}";
        
        if ($this->wheres) {
            $sql .= " WHERE " . $this->buildWhereClause();
        }
        
        $stmt = $this->model::db()->prepare($sql);
        return $stmt->execute($this->getBindings());
    }
    
    /**
     * Count records
     */
    public function count()
    {
        $sql = "SELECT COUNT(*) as count FROM {$this->model->getTable()}";
        
        if ($this->wheres) {
            $sql .= " WHERE " . $this->buildWhereClause();
        }
        
        $stmt = $this->model::db()->prepare($sql);
        $stmt->execute($this->getBindings());
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return (int) $result['count'];
    }
    
    /**
     * Build the query
     */
    protected function buildQuery()
    {
        $sql = "SELECT * FROM {$this->model->getTable()}";
        
        if ($this->wheres) {
            $sql .= " WHERE " . $this->buildWhereClause();
        }
        
        if ($this->orders) {
            $sql .= " ORDER BY " . $this->buildOrderClause();
        }
        
        if ($this->limit) {
            $sql .= " LIMIT {$this->limit}";
            if ($this->offset) {
                $sql .= " OFFSET {$this->offset}";
            }
        }
        
        return $sql;
    }
    
    /**
     * Build where clause
     */
    protected function buildWhereClause()
    {
        $clauses = [];
        $index = 0;

        foreach ($this->wheres as $where) {
            list($field, $operator, $value) = $where;

            // Handle NULL values with IS/IS NOT operators
            if ($value === null && in_array($operator, ['IS', 'IS NOT'])) {
                $clauses[] = "$field $operator NULL";
            }
            // Handle IN/NOT IN operators
            elseif (in_array($operator, ['IN', 'NOT IN'])) {
                if (is_array($value) && !empty($value)) {
                    $placeholders = [];
                    foreach ($value as $i => $val) {
                        $placeholders[] = ":where_{$index}_{$i}";
                    }
                    $clauses[] = "$field $operator (" . implode(', ', $placeholders) . ")";
                } else {
                    // Handle empty arrays
                    if ($operator === 'IN') {
                        $clauses[] = "1 = 0"; // Never matches
                    } else {
                        $clauses[] = "1 = 1"; // Always matches
                    }
                }
            } else {
                $clauses[] = "$field $operator :where_$index";
            }
            $index++;
        }

        return implode(' AND ', $clauses);
    }
    
    /**
     * Build order clause
     */
    protected function buildOrderClause()
    {
        $clauses = [];
        
        foreach ($this->orders as $order) {
            list($field, $direction) = $order;
            $clauses[] = "$field $direction";
        }
        
        return implode(', ', $clauses);
    }
    
    /**
     * Get bindings
     */
    protected function getBindings()
    {
        $bindings = [];
        $index = 0;

        foreach ($this->wheres as $where) {
            list($field, $operator, $value) = $where;

            // Skip binding for NULL values with IS/IS NOT operators
            if ($value === null && in_array($operator, ['IS', 'IS NOT'])) {
                // Skip binding
            }
            // Handle IN/NOT IN operators
            elseif (in_array($operator, ['IN', 'NOT IN'])) {
                if (is_array($value) && !empty($value)) {
                    foreach ($value as $i => $val) {
                        $bindings["where_{$index}_{$i}"] = $val;
                    }
                }
                // Skip binding for empty arrays
            } else {
                $bindings["where_$index"] = $value;
            }
            $index++;
        }

        return $bindings;
    }
}