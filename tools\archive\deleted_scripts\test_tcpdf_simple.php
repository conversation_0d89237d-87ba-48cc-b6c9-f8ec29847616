<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>TCPDF Test</h1>";

// Test 1: Check if vendor autoload works
echo "<h2>Test 1: Autoload</h2>";
$autoloadPath = __DIR__ . '/../vendor/autoload.php';
if (file_exists($autoloadPath)) {
    echo "✓ Autoload file exists<br>";
    require_once $autoloadPath;
    echo "✓ Autoload included<br>";
} else {
    die("✗ Autoload file not found at: " . $autoloadPath);
}

// Test 2: Check if TCPDF class exists
echo "<h2>Test 2: TCPDF Class</h2>";
if (class_exists('TCPDF')) {
    echo "✓ TCPDF class exists<br>";
} else {
    echo "✗ TCPDF class not found<br>";
    
    // Try to manually include TCPDF
    $tcpdfPath = __DIR__ . '/../vendor/tecnickcom/tcpdf/tcpdf.php';
    if (file_exists($tcpdfPath)) {
        echo "Trying to manually include TCPDF from: " . $tcpdfPath . "<br>";
        require_once $tcpdfPath;
        
        if (class_exists('TCPDF')) {
            echo "✓ TCPDF class now exists after manual include<br>";
        } else {
            echo "✗ Still no TCPDF class<br>";
        }
    } else {
        echo "✗ TCPDF file not found at: " . $tcpdfPath . "<br>";
    }
}

// Test 3: Try to create TCPDF instance
echo "<h2>Test 3: Create TCPDF Instance</h2>";
try {
    // Define constants if needed
    if (!defined('K_TCPDF_EXTERNAL_CONFIG')) {
        define('K_TCPDF_EXTERNAL_CONFIG', true);
    }
    if (!defined('K_PATH_MAIN')) {
        define('K_PATH_MAIN', dirname(__FILE__) . '/../vendor/tecnickcom/tcpdf/');
    }
    if (!defined('K_PATH_URL')) {
        define('K_PATH_URL', '/fit/vendor/tecnickcom/tcpdf/');
    }
    if (!defined('K_PATH_FONTS')) {
        define('K_PATH_FONTS', K_PATH_MAIN . 'fonts/');
    }
    if (!defined('K_PATH_IMAGES')) {
        define('K_PATH_IMAGES', K_PATH_MAIN . 'examples/images/');
    }
    if (!defined('K_BLANK_IMAGE')) {
        define('K_BLANK_IMAGE', K_PATH_IMAGES . '_blank.png');
    }
    if (!defined('PDF_PAGE_FORMAT')) {
        define('PDF_PAGE_FORMAT', 'A4');
    }
    if (!defined('PDF_PAGE_ORIENTATION')) {
        define('PDF_PAGE_ORIENTATION', 'P');
    }
    if (!defined('PDF_UNIT')) {
        define('PDF_UNIT', 'mm');
    }
    
    $pdf = new TCPDF();
    echo "✓ TCPDF instance created<br>";
    
    // Try to generate simple PDF
    echo "<h2>Test 4: Generate Simple PDF</h2>";
    $pdf->AddPage();
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, 'Hello World', 0, 1);
    
    // Try to output
    $pdfContent = $pdf->Output('test.pdf', 'S');
    if ($pdfContent) {
        echo "✓ PDF generated successfully (" . strlen($pdfContent) . " bytes)<br>";
        echo '<a href="data:application/pdf;base64,' . base64_encode($pdfContent) . '" download="test.pdf">Download Test PDF</a>';
    } else {
        echo "✗ Failed to generate PDF<br>";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "<br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<h2>PHP Info</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . "<br>";

// Check if any required extensions are missing
echo "<h2>Required Extensions</h2>";
$required = ['gd', 'mbstring'];
foreach ($required as $ext) {
    if (extension_loaded($ext)) {
        echo "✓ $ext extension loaded<br>";
    } else {
        echo "✗ $ext extension NOT loaded<br>";
    }
}