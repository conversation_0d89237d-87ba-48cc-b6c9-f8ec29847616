<?php
/**
 * Standalone Dashboard - Uses Twig for proper layout with navigation
 */

// Error reporting - disable notices for production
error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);
ini_set('display_errors', 0);

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check authentication
if (!isset($_SESSION['user_id'])) {
    header('Location: /fit/public/login');
    exit;
}

// Load environment
if (!defined('APP_PATH')) {
    define('APP_PATH', dirname(__DIR__));
}
require APP_PATH . '/vendor/autoload.php';

// Load .env
if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Load helpers
require_once APP_PATH . '/app/helpers/functions.php';
require_once APP_PATH . '/app/helpers/menu-config.php';

// Initialize language
use App\Helpers\Language;
use App\Core\TwigExtensions;
use App\Core\TwigArrayWrapper;

$userLanguage = $_SESSION['user_language'] ?? 'fr';
Language::setLanguage($userLanguage);

// Setup Flight mock for menu functions
if (!class_exists('Flight')) {
    class Flight {
        private static $db;
        
        public static function db() {
            if (!self::$db) {
                $dsn = 'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4';
                self::$db = new PDO($dsn, $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD']);
                self::$db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            }
            return self::$db;
        }
        
        public static function get($key) {
            if ($key === 'flight.base_url') {
                return $_ENV['APP_BASE_URL'] ?? '/fit/public';
            }
            return null;
        }
    }
}

// Initialize Twig
$loader = new \Twig\Loader\FilesystemLoader(APP_PATH . '/app/views');
$twig = new \Twig\Environment($loader, [
    'cache' => false, // Disable cache for development
    'debug' => true,
    'autoescape' => 'html'
]);

// Add the TwigExtensions class which includes all needed functions
$twig->addExtension(new TwigExtensions());

// Add additional functions not in TwigExtensions
$twig->addFunction(new \Twig\TwigFunction('redirect_to', function($url) {
    return redirect_to($url);
}));

$twig->addFunction(new \Twig\TwigFunction('menu_items', function() {
    return menu_items();
}));

$twig->addFunction(new \Twig\TwigFunction('url', function($path = '') {
    $baseUrl = $_ENV['APP_BASE_URL'] ?? '/fit/public';
    $baseUrl = rtrim($baseUrl, '/');
    if ($path && $path[0] !== '/') {
        $path = '/' . $path;
    }
    return $baseUrl . $path;
}));

$twig->addFunction(new \Twig\TwigFunction('current_language', function() {
    return $_SESSION['user_language'] ?? 'fr';
}));

// Add debug extension
if ($_ENV['APP_DEBUG'] === 'true') {
    $twig->addExtension(new \Twig\Extension\DebugExtension());
}

// Add globals
$twig->addGlobal('base_url', $_ENV['APP_BASE_URL'] ?? '/fit/public');
$twig->addGlobal('app_name', $_ENV['APP_NAME'] ?? 'Fit360 AdminDesk');
$twig->addGlobal('session', $_SESSION);
$twig->addGlobal('user', $_SESSION['user'] ?? []);
$twig->addGlobal('current_route', '/');
$twig->addGlobal('currency', '€');
$twig->addGlobal('csrf_token', $_SESSION['csrf_token'] ?? '');

// Add flash messages with TwigArrayWrapper
$flashMessages = new TwigArrayWrapper([
    'success' => isset($_SESSION['flash']['success']) && is_string($_SESSION['flash']['success']) ? $_SESSION['flash']['success'] : null,
    'error' => isset($_SESSION['flash']['error']) && is_string($_SESSION['flash']['error']) ? $_SESSION['flash']['error'] : null,
    'warning' => isset($_SESSION['flash']['warning']) && is_string($_SESSION['flash']['warning']) ? $_SESSION['flash']['warning'] : null,
    'info' => isset($_SESSION['flash']['info']) && is_string($_SESSION['flash']['info']) ? $_SESSION['flash']['info'] : null,
]);
$twig->addGlobal('flash', $flashMessages);

$twig->addGlobal('app', [
    'language' => $_SESSION['user_language'] ?? 'fr',
    'user' => $_SESSION['user'] ?? []
]);

// Database connection
try {
    $dsn = 'mysql:host=' . $_ENV['DB_HOST'] . ';dbname=' . $_ENV['DB_DATABASE'] . ';charset=utf8mb4';
    $pdo = new PDO($dsn, $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (Exception $e) {
    die('Database Error: ' . $e->getMessage());
}

// Get dashboard data
$data = [
    'user' => $_SESSION['user'] ?? [],
    'stats' => [],
    'recent_invoices' => [],
    'error' => null
];

try {
    // Get stats
    $stmt = $pdo->query("
        SELECT 
            (SELECT COUNT(*) FROM clients WHERE is_active = 1) as total_clients,
            (SELECT COUNT(*) FROM patients) as total_patients,
            (SELECT COUNT(*) FROM invoices WHERE status = 'sent') as pending_invoices,
            (SELECT COALESCE(SUM(total), 0) FROM invoices WHERE status = 'paid' AND MONTH(issue_date) = MONTH(CURDATE()) AND YEAR(issue_date) = YEAR(CURDATE())) as monthly_revenue
    ");
    $data['stats'] = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get recent invoices
    $stmt = $pdo->query("
        SELECT 
            i.id,
            i.invoice_number,
            i.total,
            i.status,
            i.issue_date,
            CASE 
                WHEN i.client_id IS NOT NULL THEN COALESCE(c.name, c.company_name, CONCAT(c.first_name, ' ', c.last_name))
                WHEN i.user_id IS NOT NULL THEN CONCAT(u.first_name, ' ', u.last_name)
                ELSE 'N/A'
            END as client_name
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN users u ON i.user_id = u.id
        ORDER BY i.created_at DESC
        LIMIT 5
    ");
    $data['recent_invoices'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    $data['error'] = 'Database query error: ' . $e->getMessage();
}

// Greeting
$hour = date('H');
if ($hour < 12) {
    $greeting = __('dashboard.good_morning');
} elseif ($hour < 18) {
    $greeting = __('dashboard.good_afternoon');
} else {
    $greeting = __('dashboard.good_evening');
}

// Prepare data for template
$templateData = [
    'title' => __('common.dashboard'),
    'user' => $data['user'],
    'stats' => $data['stats'],
    'recent_invoices' => $data['recent_invoices'],
    'greeting' => $greeting,
    'error' => $data['error'] ?? null,
    'activeColorScheme' => null, // Prevent color scheme errors
    'config' => [
        'currency_symbol' => '€',
        'date_format' => 'd/m/Y',
        'time_format' => 'H:i'
    ]
];

// Render the template
try {
    echo $twig->render('dashboard-standalone.twig', $templateData);
} catch (Exception $e) {
    // Fallback to simple error display
    echo '<div class="container mt-5">';
    echo '<div class="alert alert-danger">';
    echo '<h4>Dashboard Error</h4>';
    echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    if ($_ENV['APP_DEBUG'] === 'true') {
        echo '<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
    }
    echo '</div>';
    echo '</div>';
}