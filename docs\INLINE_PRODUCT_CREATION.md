# Inline Product Creation Feature

## Overview

The inline product creation feature allows users to create new products directly from the invoice creation page when using DIV (Divers) invoices. This feature is designed to streamline the workflow for users who need to add products that don't exist in the catalog yet.

## Features

- **Live Product Search**: Type to search for existing products
- **Create New Product**: Option to create a product if it doesn't exist
- **Auto-generation**: Product codes can be auto-generated based on category
- **Security**: CSRF protection, rate limiting (10 products per 5 minutes), and input sanitization
- **Mobile Responsive**: Touch-optimized forms with 44px minimum touch targets
- **Audit Trail**: All product creations are logged for security

## How It Works

1. **Select DIV Invoice Type**: The feature is only active for DIV (Divers) invoices
2. **Type Product Name**: Start typing in the description field
3. **Search or Create**: 
   - If the product exists, select it from the dropdown
   - If not, click "Create new product: [name]"
4. **Fill Product Details**:
   - Product code (auto-generated or manual)
   - Unit price
   - VAT rate
   - Category (defaults to "Divers")
5. **Save and Use**: Click "Save and use" to create the product and apply it to the invoice line

## Implementation Details

### Database Schema

The feature adds several database enhancements:

```sql
-- Track miscellaneous category
ALTER TABLE catalog_categories ADD is_misc_category BOOLEAN DEFAULT FALSE;

-- Track creation source
ALTER TABLE catalog_items ADD creation_source ENUM('manual', 'inline', 'import', 'api');

-- Pending products for rollback support
CREATE TABLE catalog_items_pending (...);

-- Audit trail
CREATE TABLE catalog_creation_audit (...);

-- Sequence management for auto-generated codes
CREATE TABLE catalog_sequences (...);
```

### Security Measures

1. **Rate Limiting**: Maximum 10 products per IP address per 5 minutes
2. **CSRF Protection**: All requests validated with CSRF tokens
3. **Input Sanitization**: 
   - Product codes: Alphanumeric + hyphens only
   - Product names: HTML entities encoded
   - Prices: Numeric validation with 2 decimal places
4. **Authorization**: Requires `invoice.create` permission
5. **Audit Logging**: All creation attempts logged with IP, user, and result

### API Endpoints

- `POST /api/products/quick-create` - Create new product inline
- `GET /api/products/check-name` - Check if product name exists
- `GET /api/products/generate-code` - Generate product code for category

### JavaScript Integration

The feature is implemented in `/public/js/product-live-search.js`:

```javascript
// Initialize on DIV invoices
if (productLiveSearch.isDivInvoice()) {
    productLiveSearch.init();
}
```

### Mobile Responsiveness

- Touch-optimized input fields (44px height)
- Mobile-friendly modals
- Responsive dropdown positioning
- Smooth animations for better UX

## Configuration

### Set Miscellaneous Category

Run the migration to set up the database:
```
http://localhost/fit/public/apply-inline-product-migration.php
```

The migration will:
1. Add necessary database columns and tables
2. Identify and mark the "Divers" category
3. Set up auto-increment sequences
4. Create cleanup events for abandoned products

### Enable/Disable Feature

The feature is automatically enabled for DIV invoices. To disable:
1. Remove the product-live-search.js include from invoice creation template
2. Or modify `isDivInvoice()` function to always return false

## Troubleshooting

### Dropdown Not Appearing
- Check if invoice type is set to DIV
- Verify product-live-search.js is loaded
- Check browser console for errors

### Products Not Saving
- Verify CSRF token is present
- Check rate limiting (max 10 per 5 minutes)
- Ensure user has invoice.create permission

### Missing Translations
- French: `/app/lang/fr/products.php`
- English: `/app/lang/en/products.php`
- German: Add translations to `/app/lang/de/products.php`

## Future Enhancements

1. **Bulk Import**: Allow CSV import of products
2. **Barcode Scanner**: Mobile barcode scanning support
3. **Image Upload**: Add product images inline
4. **Category Management**: Create categories on the fly
5. **Price History**: Track price changes over time