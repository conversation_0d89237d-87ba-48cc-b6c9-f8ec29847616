/* Table Helper Styles */

/* Sorting indicators */
.sort-indicator {
    display: inline-block;
    margin-left: 0.25rem;
    vertical-align: middle;
}

th[data-sortable="true"] {
    position: relative;
    white-space: nowrap;
}

th[data-sortable="true"]:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

/* Dark mode support */
[data-bs-theme="dark"] th[data-sortable="true"]:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* Column reordering disabled - now handled in admin config */

/* Search highlights */
mark.highlight {
    background-color: #fff3cd;
    padding: 0 2px;
    border-radius: 2px;
}

[data-bs-theme="dark"] mark.highlight {
    background-color: #664d03;
    color: #fff;
}

/* Dynamic search styles */
#search {
    padding-right: 2.5rem;
}

.table tr {
    transition: opacity 0.2s ease;
}

.table tr[style*="display: none"] {
    opacity: 0;
}

#search-results-count {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 0.875rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .drag-handle {
        display: none !important;
    }
    
    th[data-sortable="true"] {
        font-size: 0.875rem;
    }
    
    .sort-indicator {
        font-size: 0.75rem;
    }
}

/* Table animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.table tbody tr {
    animation: fadeIn 0.3s ease-out;
}

/* Bulk action styles */
.row-checkbox {
    cursor: pointer;
}

#selectAll {
    cursor: pointer;
}

/* Empty state */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    opacity: 0.3;
    margin-bottom: 1rem;
}

/* Export/Import buttons */
.btn-group .dropdown-menu {
    min-width: 120px;
}

/* Filter card */
.filter-card {
    transition: box-shadow 0.2s ease;
}

.filter-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Column visibility toggle (future feature) */
.column-toggle {
    position: relative;
}

.column-toggle-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    min-width: 200px;
    max-height: 300px;
    overflow-y: auto;
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.25rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

[data-bs-theme="dark"] .column-toggle-menu {
    background: #212529;
    border-color: rgba(255, 255, 255, 0.15);
}

.column-toggle-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.column-toggle-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

[data-bs-theme="dark"] .column-toggle-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Save indicator */
.save-indicator {
    position: fixed;
    top: 80px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    opacity: 0;
    transform: translateX(100px);
    transition: all 0.3s ease;
}

.save-indicator.show {
    opacity: 1;
    transform: translateX(0);
}

.save-indicator i {
    font-size: 1.2rem;
}

/* Dark mode save indicator */
[data-bs-theme="dark"] .save-indicator {
    background: #198754;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}