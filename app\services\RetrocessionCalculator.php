<?php

namespace App\Services;

use App\Models\RateProfile;
use App\Models\Client;
use App\Models\Invoice;
use App\Helpers\MoneyHelper;
use Flight;
use PDO;
use Exception;

class RetrocessionCalculator
{
    private $rateProfile;
    
    public function __construct()
    {
        $this->rateProfile = new RateProfile();
    }
    
    /**
     * Calculate retrocession amounts
     */
    public function calculate($data)
    {
        // Extract input data
        $cnsAmount = MoneyHelper::round($data['cns_amount'] ?? 0);
        $patientAmount = MoneyHelper::round($data['patient_amount'] ?? 0);
        $totalAmount = MoneyHelper::round($cnsAmount + $patientAmount);
        
        // Get percentages (from profile or custom)
        $cnsPercent = $data['cns_percent'] ?? 20;
        $patientPercent = $data['patient_percent'] ?? 20;
        $secretariatPercent = $data['secretariat_percent'] ?? 10;
        
        // Calculate parts
        $cnsPart = MoneyHelper::calculatePercentage($cnsAmount, $cnsPercent);
        $patientPart = MoneyHelper::calculatePercentage($patientAmount, $patientPercent);
        
        // Calculate secretariat with possible tiers
        $secretariatTvac = $this->calculateSecretariatAmount(
            $totalAmount, 
            $secretariatPercent,
            $data['profile_id'] ?? null,
            $data['practitioner_id'] ?? null
        );
        
        // Calculate VAT (Luxembourg formula using inclusive tax calculation)
        $vatRate = $data['vat_rate'] ?? 17;
        $vatAmount = MoneyHelper::calculateTax($secretariatTvac, $vatRate, true);
        $secretariatHtva = MoneyHelper::round($secretariatTvac - $vatAmount);
        
        // Calculate totals
        $practitionerTotal = MoneyHelper::sum([$cnsPart, $patientPart]);
        $invoiceTotal = MoneyHelper::sum([$practitionerTotal, $secretariatTvac]);
        
        return [
            'total_amount' => $totalAmount,
            'cns_amount' => $cnsAmount,
            'patient_amount' => $patientAmount,
            'cns_percent' => $cnsPercent,
            'patient_percent' => $patientPercent,
            'secretariat_percent' => $secretariatPercent,
            'cns_part' => $cnsPart,
            'patient_part' => $patientPart,
            'practitioner_total' => $practitionerTotal,
            'secretariat_tvac' => $secretariatTvac,
            'secretariat_htva' => $secretariatHtva,
            'vat_amount' => $vatAmount,
            'vat_rate' => $vatRate,
            'invoice_total' => $invoiceTotal
        ];
    }
    
    /**
     * Calculate secretariat amount with tier support
     */
    private function calculateSecretariatAmount($totalAmount, $basePercent, $profileId = null, $practitionerId = null)
    {
        // Check for tiered pricing
        if ($profileId || $practitionerId) {
            $tieredPercent = $this->getTieredPercentage($totalAmount, $profileId, $practitionerId);
            if ($tieredPercent !== null) {
                return MoneyHelper::calculatePercentage($totalAmount, $tieredPercent);
            }
        }
        
        // Use base percentage
        return MoneyHelper::calculatePercentage($totalAmount, $basePercent);
    }
    
    /**
     * Get tiered percentage based on amount
     */
    private function getTieredPercentage($amount, $profileId = null, $practitionerId = null)
    {
        $db = Flight::db();
        
        // Get the profile ID if practitioner ID is provided
        if ($practitionerId && !$profileId) {
            $stmt = $db->prepare("
                SELECT profile_id 
                FROM practitioner_rate_assignments
                WHERE practitioner_id = :practitioner_id
                AND assigned_from <= CURDATE()
                AND (assigned_to IS NULL OR assigned_to >= CURDATE())
                ORDER BY assigned_from DESC
                LIMIT 1
            ");
            $stmt->execute([':practitioner_id' => $practitionerId]);
            $assignment = $stmt->fetch(PDO::FETCH_ASSOC);
            $profileId = $assignment ? $assignment['profile_id'] : null;
        }
        
        if (!$profileId) {
            return null;
        }
        
        // Get secretariat rate with tiers
        $stmt = $db->prepare("
            SELECT rpr.id, rpr.base_value
            FROM rate_profile_rates rpr
            WHERE rpr.profile_id = :profile_id
            AND rpr.rate_type = 'secretariat_percent'
            AND rpr.is_current = TRUE
            LIMIT 1
        ");
        $stmt->execute([':profile_id' => $profileId]);
        $rate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$rate) {
            return null;
        }
        
        // Check for tiers
        $stmt = $db->prepare("
            SELECT tier_value
            FROM rate_profile_tiers
            WHERE profile_rate_id = :rate_id
            AND threshold_from <= :amount
            AND (threshold_to IS NULL OR threshold_to > :amount)
            ORDER BY tier_order
            LIMIT 1
        ");
        $stmt->execute([
            ':rate_id' => $rate['id'],
            ':amount' => $amount
        ]);
        $tier = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $tier ? $tier['tier_value'] : $rate['base_value'];
    }
    
    /**
     * Generate retrocession invoice
     */
    public function generateInvoice($practitionerId, $periodMonth, $periodYear, $data = [])
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get practitioner details
            $client = new Client();
            $practitioner = $client->getById($practitionerId);
            
            if (!$practitioner) {
                throw new Exception("Practitioner not found");
            }
            
            // Get retrocession data entry
            error_log("RetrocessionCalculator::generateInvoice - Looking for entry:");
            error_log("- Practitioner ID: " . $practitionerId);
            error_log("- Month/Year: " . $periodMonth . "/" . $periodYear);
            
            $stmt = $db->prepare("
                SELECT * FROM retrocession_data_entry
                WHERE practitioner_id = :practitioner_id
                AND period_month = :month
                AND period_year = :year
                AND status IN ('confirmed', 'draft')
            ");
            $stmt->execute([
                ':practitioner_id' => $practitionerId,
                ':month' => $periodMonth,
                ':year' => $periodYear
            ]);
            $dataEntry = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$dataEntry) {
                // Debug: Check what entries exist for this practitioner
                $stmt = $db->prepare("
                    SELECT id, practitioner_id, period_month, period_year, status 
                    FROM retrocession_data_entry 
                    WHERE practitioner_id = :practitioner_id
                    ORDER BY period_year DESC, period_month DESC
                    LIMIT 5
                ");
                $stmt->execute([':practitioner_id' => $practitionerId]);
                $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                error_log("RetrocessionCalculator::generateInvoice - No entry found!");
                error_log("Recent entries for practitioner " . $practitionerId . ":");
                foreach ($entries as $e) {
                    error_log(sprintf("- ID: %d, Month: %d/%d, Status: %s", 
                        $e['id'], $e['period_month'], $e['period_year'], $e['status']));
                }
                
                throw new Exception("No retrocession data found for this period");
            }
            
            // Get effective rates
            $dateStr = sprintf('%04d-%02d-01', $periodYear, $periodMonth);
            
            // First check for user-specific retrocession settings
            $userRetrocessionSettings = null;
            
            // Get user_id from the practitioner (client)
            $stmt = $db->prepare("SELECT user_id FROM clients WHERE id = :client_id");
            $stmt->execute(['client_id' => $practitionerId]);
            $clientData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($clientData && $clientData['user_id']) {
                try {
                    $userRetrocessionSettings = \App\Models\UserRetrocessionSetting::getActiveSettingsForUser($clientData['user_id'], $dateStr);
                } catch (\Exception $e) {
                    // Table might not exist, continue with default flow
                }
            }
            
            if ($userRetrocessionSettings) {
                // Use user-specific settings
                $cnsPercent = $dataEntry['override_cns_percent'] ?? 
                             ($userRetrocessionSettings['cns_type'] == 'percentage' ? $userRetrocessionSettings['cns_value'] : 20);
                $patientPercent = $dataEntry['override_patient_percent'] ?? 
                                 ($userRetrocessionSettings['patient_type'] == 'percentage' ? $userRetrocessionSettings['patient_value'] : 20);
                $secretariatPercent = $dataEntry['override_secretariat_percent'] ?? 
                                     ($userRetrocessionSettings['secretary_type'] == 'percentage' ? $userRetrocessionSettings['secretary_value'] : 10);
            } else {
                // Fall back to rate profile system
                $cnsPercent = $dataEntry['override_cns_percent'] ?? 
                             $this->rateProfile->getEffectiveRate($practitionerId, 'cns_percent', $dateStr) ?? 
                             20;
                $patientPercent = $dataEntry['override_patient_percent'] ?? 
                                 $this->rateProfile->getEffectiveRate($practitionerId, 'patient_percent', $dateStr) ?? 
                                 20;
                $secretariatPercent = $dataEntry['override_secretariat_percent'] ?? 
                                     $this->rateProfile->getEffectiveRate($practitionerId, 'secretariat_percent', $dateStr) ?? 
                                     10;
            }
            
            // Calculate amounts
            $calculation = $this->calculate([
                'cns_amount' => $dataEntry['cns_amount'],
                'patient_amount' => $dataEntry['patient_amount'],
                'cns_percent' => $cnsPercent,
                'patient_percent' => $patientPercent,
                'secretariat_percent' => $secretariatPercent,
                'practitioner_id' => $practitionerId
            ]);
            
            // Save calculation history
            $this->saveCalculationHistory($dataEntry['id'], $calculation);
            
            // Determine invoice type
            $invoiceType = $secretariatPercent == 10 ? 'retrocession_30' : 'retrocession_25';
            
            // Get invoice type ID
            $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE code = 'RET'");
            $stmt->execute();
            $invoiceTypeData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get practitioner info to check exclude_patient_retrocession setting
            // First get the user_id from the client record
            $stmt = $db->prepare("SELECT user_id FROM clients WHERE id = :client_id");
            $stmt->execute(['client_id' => $practitionerId]);
            $clientData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $excludePatientRetrocession = 0;
            if ($clientData && $clientData['user_id']) {
                $stmt = $db->prepare("SELECT exclude_patient_retrocession FROM users WHERE id = :user_id");
                $stmt->execute(['user_id' => $clientData['user_id']]);
                $userData = $stmt->fetch(PDO::FETCH_ASSOC);
                $excludePatientRetrocession = $userData['exclude_patient_retrocession'] ?? 0;
            }
            
            // Adjust totals if patient retrocession is excluded
            $adjustedPractitionerTotal = $calculation['practitioner_total'];
            $adjustedInvoiceTotal = $calculation['invoice_total'];
            
            if ($excludePatientRetrocession) {
                // Subtract patient part from totals
                $adjustedPractitionerTotal -= $calculation['patient_part'];
                $adjustedInvoiceTotal -= $calculation['patient_part'];
                
                error_log("RetrocessionCalculator - Excluding patient retrocession for practitioner $practitionerId");
                error_log("RetrocessionCalculator - Patient part excluded: " . $calculation['patient_part']);
                error_log("RetrocessionCalculator - Adjusted totals: practitioner=" . $adjustedPractitionerTotal . ", invoice=" . $adjustedInvoiceTotal);
            }
            
            // Prepare invoice data
            $monthName = $this->getMonthName($periodMonth);
            $invoiceData = array_merge($data, [
                'type_id' => $invoiceTypeData['id'],
                'invoice_type' => $invoiceType,
                'client_id' => $practitionerId,
                'user_id' => $clientData['user_id'] ?? null, // Add user_id for retrocession invoices
                'status' => 'draft', // Start as draft
                'issue_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'subtotal' => $adjustedPractitionerTotal + $calculation['secretariat_htva'],
                'vat_amount' => $calculation['vat_amount'],
                'secretariat_vat_amount' => $calculation['vat_amount'],
                'total' => $adjustedInvoiceTotal,
                'secretariat_vat_note_shown' => true,
                'payment_terms' => 'Net 30 jours',
                'notes' => sprintf('Rétrocession %s %d', $monthName, $periodYear),
                // Retrocession specific data
                'retro_total_amount' => $calculation['total_amount'],
                'retro_cns_amount' => $calculation['cns_amount'],
                'retro_patient_amount' => $calculation['patient_amount'],
                'retro_cns_percentage' => $calculation['cns_percent'],
                'retro_patient_percentage' => $calculation['patient_percent'],
                'retro_secretariat_percentage' => $calculation['secretariat_percent'],
                'secretariat_tvac' => $calculation['secretariat_tvac'],
                'retro_has_overrides' => !empty($dataEntry['override_notes']),
                'retro_override_notes' => $dataEntry['override_notes']
            ]);
            
            // Get custom labels from user settings if available
            $cnsLabel = ($userRetrocessionSettings && !empty($userRetrocessionSettings['cns_label'])) 
                        ? $userRetrocessionSettings['cns_label'] 
                        : 'RÉTROCESSION CNS';
            $patientLabel = ($userRetrocessionSettings && !empty($userRetrocessionSettings['patient_label'])) 
                            ? $userRetrocessionSettings['patient_label'] 
                            : 'RÉTROCESSION PATIENTS';
            $secretaryLabel = ($userRetrocessionSettings && !empty($userRetrocessionSettings['secretary_label'])) 
                              ? $userRetrocessionSettings['secretary_label'] 
                              : 'FRAIS SECRÉTARIAT';
            
            // Create invoice lines
            $invoiceData['lines'] = [
                [
                    'line_type' => 'cns_part',
                    'description' => $cnsLabel,
                    'quantity' => 1,
                    'unit_price' => $calculation['cns_part'],
                    'vat_rate' => 0
                ]
            ];
            
            // Only add patient retrocession line if not excluded
            if (!$excludePatientRetrocession) {
                $invoiceData['lines'][] = [
                    'line_type' => 'patient_part',
                    'description' => $patientLabel,
                    'quantity' => 1,
                    'unit_price' => $calculation['patient_part'],
                    'vat_rate' => 0
                ];
            }
            
            // Add secretariat line
            $invoiceData['lines'][] = [
                'line_type' => 'secretariat',
                'description' => $secretaryLabel,
                'quantity' => 1,
                'unit_price' => $calculation['secretariat_tvac'],
                'vat_rate' => $calculation['vat_rate']
            ];
            
            // Create invoice
            $invoice = new Invoice();
            $newInvoice = $invoice->createInvoice($invoiceData);
            
            // Update data entry status
            $stmt = $db->prepare("
                UPDATE retrocession_data_entry
                SET status = 'invoiced',
                    invoice_id = :invoice_id,
                    updated_at = NOW()
                WHERE id = :id
            ");
            $stmt->execute([
                ':invoice_id' => $newInvoice['id'],
                ':id' => $dataEntry['id']
            ]);
            
            $db->commit();
            
            return $newInvoice;
            
        } catch (Exception $e) {
            error_log("RetrocessionCalculator::generateInvoice - Error: " . $e->getMessage());
            error_log("RetrocessionCalculator::generateInvoice - File: " . $e->getFile() . " Line: " . $e->getLine());
            
            // Only rollback if transaction is active
            if ($db->inTransaction()) {
                $db->rollBack();
            }
            throw $e;
        }
    }
    
    /**
     * Save calculation history
     */
    private function saveCalculationHistory($dataEntryId, $calculation)
    {
        $db = Flight::db();
        
        // Debug: Log calculation data
        error_log("RetrocessionCalculator::saveCalculationHistory - dataEntryId: " . $dataEntryId);
        error_log("RetrocessionCalculator::saveCalculationHistory - calculation keys: " . implode(', ', array_keys($calculation)));
        error_log("RetrocessionCalculator::saveCalculationHistory - calculation data: " . json_encode($calculation));
        
        $sql = "
            INSERT INTO retrocession_calculations (
                data_entry_id, total_amount, cns_amount, patient_amount,
                cns_percentage, patient_percentage, secretariat_percentage,
                cns_part, patient_part, secretariat_htva, secretariat_tvac,
                vat_rate, vat_amount, rate_source, calculation_details, created_by
            ) VALUES (
                :data_entry_id, :total_amount, :cns_amount, :patient_amount,
                :cns_percentage, :patient_percentage, :secretariat_percentage,
                :cns_part, :patient_part, :secretariat_htva, :secretariat_tvac,
                :vat_rate, :vat_amount, :rate_source, :calculation_details, :created_by
            )
        ";
        
        $params = [
            'data_entry_id' => $dataEntryId,
            'total_amount' => $calculation['total_amount'],
            'cns_amount' => $calculation['cns_amount'],
            'patient_amount' => $calculation['patient_amount'],
            'cns_percentage' => $calculation['cns_percent'],
            'patient_percentage' => $calculation['patient_percent'],
            'secretariat_percentage' => $calculation['secretariat_percent'],
            'cns_part' => $calculation['cns_part'],
            'patient_part' => $calculation['patient_part'],
            'secretariat_htva' => $calculation['secretariat_htva'],
            'secretariat_tvac' => $calculation['secretariat_tvac'],
            'vat_rate' => $calculation['vat_rate'],
            'vat_amount' => $calculation['vat_amount'],
            'rate_source' => 'profile',
            'calculation_details' => json_encode($calculation),
            'created_by' => $_SESSION['user_id'] ?? 1
        ];
        
        // Debug: Log parameters
        error_log("RetrocessionCalculator::saveCalculationHistory - SQL: " . $sql);
        error_log("RetrocessionCalculator::saveCalculationHistory - params keys: " . implode(', ', array_keys($params)));
        
        try {
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
        } catch (PDOException $e) {
            error_log("RetrocessionCalculator::saveCalculationHistory - PDO Error: " . $e->getMessage());
            error_log("RetrocessionCalculator::saveCalculationHistory - PDO Code: " . $e->getCode());
            throw $e;
        }
    }
    
    /**
     * Get month name in French
     */
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
    
    /**
     * Calculate auto-fill suggestion
     */
    public function generateAutoFillSuggestion($practitionerId, $month, $year)
    {
        $db = Flight::db();
        
        // Get last 3 months of data
        $stmt = $db->prepare("
            SELECT cns_amount, patient_amount
            FROM retrocession_data_entry
            WHERE practitioner_id = :practitioner_id
            AND ((period_year = :year AND period_month < :month) 
                 OR (period_year = :prev_year))
            AND status IN ('confirmed', 'invoiced')
            ORDER BY period_year DESC, period_month DESC
            LIMIT 3
        ");
        
        $prevYear = $year - 1;
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':year' => $year,
            ':month' => $month,
            ':prev_year' => $prevYear
        ]);
        $history = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($history) < 2) {
            // Not enough history
            return null;
        }
        
        // Calculate averages
        $avgCns = array_sum(array_column($history, 'cns_amount')) / count($history);
        $avgPatient = array_sum(array_column($history, 'patient_amount')) / count($history);
        
        // Calculate confidence based on variance
        $cnsValues = array_column($history, 'cns_amount');
        $patientValues = array_column($history, 'patient_amount');
        
        $cnsVariance = $this->calculateVariance($cnsValues);
        $patientVariance = $this->calculateVariance($patientValues);
        
        // High confidence if low variance
        $confidence = 1 - (($cnsVariance + $patientVariance) / 2);
        $confidence = max(0, min(1, $confidence));
        
        // Save suggestion
        $stmt = $db->prepare("
            INSERT INTO retrocession_autofill (
                practitioner_id, period_month, period_year,
                cns_suggested, patient_suggested, confidence_score,
                calculation_method, factors_considered
            ) VALUES (
                :practitioner_id, :month, :year,
                :cns_suggested, :patient_suggested, :confidence,
                :method, :factors
            )
        ");
        
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':month' => $month,
            ':year' => $year,
            ':cns_suggested' => MoneyHelper::round($avgCns),
            ':patient_suggested' => MoneyHelper::round($avgPatient),
            ':confidence' => round($confidence, 2),
            ':method' => 'average_3m',
            ':factors' => json_encode(['months_analyzed' => count($history)])
        ]);
        
        return [
            'cns_suggested' => MoneyHelper::round($avgCns),
            'patient_suggested' => MoneyHelper::round($avgPatient),
            'confidence_score' => round($confidence, 2),
            'method' => 'average_3m'
        ];
    }
    
    /**
     * Calculate variance for confidence scoring
     */
    private function calculateVariance($values)
    {
        $mean = array_sum($values) / count($values);
        $variance = 0;
        
        foreach ($values as $value) {
            $variance += pow($value - $mean, 2);
        }
        
        $variance = $variance / count($values);
        
        // Normalize variance to 0-1 range
        return min(1, $variance / ($mean * $mean));
    }
}