<?php
// Fix invoice to add LOY prefix

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix Invoice LOY Prefix</h2>";
    
    // Find invoice FAC-2025-0187
    $stmt = $db->prepare("
        SELECT i.*, it.prefix as type_prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.invoice_number = 'FAC-2025-0187'
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice FAC-2025-0187 not found!</p>";
        exit;
    }
    
    echo "<h3>Current Invoice Details</h3>";
    echo "<p>Invoice ID: " . $invoice['id'] . "</p>";
    echo "<p>Current Number: <strong>" . $invoice['invoice_number'] . "</strong></p>";
    echo "<p>Type ID: " . ($invoice['type_id'] ?? 'NULL') . "</p>";
    echo "<p>Type Prefix: <strong>" . ($invoice['type_prefix'] ?? 'None') . "</strong></p>";
    
    if ($invoice['type_prefix'] == 'LOY') {
        echo "<h3 style='color: green;'>✓ This invoice should have LOY prefix!</h3>";
        
        // Update the invoice number
        $newNumber = 'FAC-LOY-2025-0187';
        $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
        $stmt->execute([$newNumber, $invoice['id']]);
        
        echo "<h2 style='color: green;'>✓ Updated invoice number to: " . $newNumber . "</h2>";
        
        // Also ensure type_id is set
        if (!$invoice['type_id']) {
            $stmt = $db->prepare("UPDATE invoices SET type_id = 1 WHERE id = ?");
            $stmt->execute([$invoice['id']]);
            echo "<p>✓ Also set type_id to 1 (Loyer)</p>";
        }
        
        echo "<br><a href='/fit/public/invoices/" . $invoice['id'] . "' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>View Updated Invoice</a>";
        
    } else {
        echo "<p style='color: orange;'>This invoice type doesn't have LOY prefix configured.</p>";
    }
    
    // Add JavaScript debugging
    echo '
    <h3>Debug Info for Console</h3>
    <p>Copy and paste this into your browser console when creating invoices:</p>
    <pre style="background: #f4f4f4; padding: 10px; border: 1px solid #ddd;">
// Debug invoice creation
window.debugInvoiceCreation = true;

// Override fetch to log invoice creation requests
const originalFetch = window.fetch;
window.fetch = function(...args) {
    const [url, options] = args;
    
    if (url && url.includes("/invoices") && options && options.method === "POST") {
        console.log("=== INVOICE CREATION REQUEST ===");
        console.log("URL:", url);
        console.log("Method:", options.method);
        
        if (options.body instanceof FormData) {
            console.log("Form Data:");
            for (let [key, value] of options.body.entries()) {
                console.log(`  ${key}:`, value);
            }
        }
        console.log("==============================");
    }
    
    return originalFetch.apply(this, args).then(response => {
        if (url && url.includes("/invoices")) {
            const clonedResponse = response.clone();
            clonedResponse.json().then(data => {
                console.log("=== INVOICE RESPONSE ===");
                console.log("Status:", response.status);
                console.log("Data:", data);
                if (data.invoice) {
                    console.log("Invoice Number:", data.invoice.invoice_number);
                    console.log("Type ID:", data.invoice.type_id);
                }
                console.log("=======================");
            }).catch(() => {});
        }
        return response;
    });
};

console.log("Invoice creation debugging enabled!");
</pre>
    ';
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>