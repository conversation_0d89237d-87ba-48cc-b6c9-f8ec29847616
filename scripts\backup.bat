@echo off
REM Fit360 AdminDesk - Database Backup Batch Script
REM Simple wrapper for the PHP backup script

REM Get the directory where this script is located
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

REM Change to project directory
cd /d "%PROJECT_DIR%"

REM Default options
set COMPRESS=--compress
set RETENTION=--retention=7
set OUTPUT_DIR=--output-dir=storage/backups

REM Parse command line arguments
:parse_args
if "%1"=="" goto run_backup
if "%1"=="--no-compress" (
    set COMPRESS=--no-compress
    shift
    goto parse_args
)
if "%1"=="--list" (
    php scripts/database-backup.php --list
    goto end
)
if "%1"=="--help" (
    echo Usage: %0 [options]
    echo Options:
    echo   --no-compress       Don't compress backup files
    echo   --retention=N       Keep backups for N days (default: 7)
    echo   --output-dir=DIR    Output directory (default: storage/backups)
    echo   --list              List existing backups
    echo   --help              Show this help message
    goto end
)
if "%1"=="--retention" (
    set RETENTION=--retention=%2
    shift
    shift
    goto parse_args
)
if "%1"=="--output-dir" (
    set OUTPUT_DIR=--output-dir=%2
    shift
    shift
    goto parse_args
)
shift
goto parse_args

:run_backup
REM Check if PHP is available
php --version >nul 2>&1
if errorlevel 1 (
    echo Error: PHP is not installed or not in PATH
    exit /b 1
)

REM Check if the backup script exists
if not exist "scripts\database-backup.php" (
    echo Error: Backup script not found at scripts\database-backup.php
    exit /b 1
)

REM Run the backup
echo Starting database backup...
php scripts/database-backup.php %COMPRESS% %RETENTION% %OUTPUT_DIR%

if errorlevel 1 (
    echo Backup failed!
    exit /b 1
) else (
    echo Backup completed successfully!
)

:end
