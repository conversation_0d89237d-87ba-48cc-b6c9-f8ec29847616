{% extends "base-modern.twig" %}

{% block title %}{{ __('patients.add_new') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('patients.add_new') }}</h1>
        <a href="{{ base_url }}/patients" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ base_url }}/patients" class="needs-validation" novalidate>
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <!-- Basic Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-fill me-2"></i>{{ __('common.basic_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label for="title" class="form-label">{{ __('patients.title') }}</label>
                                <select class="form-select" id="title" name="title" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    <option value="Mr">M.</option>
                                    <option value="Mrs">Mme</option>
                                    <option value="Ms">Mlle</option>
                                    <option value="Dr">Dr</option>
                                </select>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-5">
                                <label for="first_name" class="form-label">{{ __('common.first_name') }} *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-5">
                                <label for="last_name" class="form-label">{{ __('common.last_name') }} *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="birth_date" class="form-label">{{ __('common.birth_date') }} *</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="gender" class="form-label">{{ __('patients.gender') }} *</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    <option value="M">{{ __('common.male') }}</option>
                                    <option value="F">{{ __('common.female') }}</option>
                                </select>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="social_security_number" class="form-label">{{ __('patients.social_security_number') }}</label>
                                <input type="text" class="form-control" id="social_security_number" name="social_security_number">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-telephone-fill me-2"></i>{{ __('patients.contact_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">{{ __('common.phone') }}</label>
                                <input type="tel" class="form-control" id="phone" name="phone">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="mobile" class="form-label">{{ __('patients.mobile') }}</label>
                                <input type="tel" class="form-control" id="mobile" name="mobile">
                            </div>
                            
                            <div class="col-md-12">
                                <label for="email" class="form-label">{{ __('common.email') }}</label>
                                <input type="email" class="form-control" id="email" name="email">
                                <div class="invalid-feedback">
                                    {{ __('validation.email') }}
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="address" class="form-label">{{ __('common.address') }}</label>
                                <input type="text" class="form-control" id="address" name="address">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">{{ __('common.postal_code') }}</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="city" class="form-label">{{ __('common.city') }}</label>
                                <input type="text" class="form-control" id="city" name="city">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="country" class="form-label">{{ __('common.country') }}</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="LU" selected>Luxembourg</option>
                                    <option value="FR">France</option>
                                    <option value="BE">Belgique</option>
                                    <option value="DE">Allemagne</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medical Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-heart-pulse-fill me-2"></i>{{ __('patients.medical_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="blood_type" class="form-label">{{ __('common.blood_type') }}</label>
                                <select class="form-select" id="blood_type" name="blood_type">
                                    <option value="">{{ __('common.unknown') }}</option>
                                    <option value="A+">A+</option>
                                    <option value="A-">A-</option>
                                    <option value="B+">B+</option>
                                    <option value="B-">B-</option>
                                    <option value="AB+">AB+</option>
                                    <option value="AB-">AB-</option>
                                    <option value="O+">O+</option>
                                    <option value="O-">O-</option>
                                </select>
                            </div>
                            
                            <div class="col-md-9">
                                <label for="allergies" class="form-label">{{ __('common.allergies') }}</label>
                                <input type="text" class="form-control" id="allergies" name="allergies" 
                                       placeholder="{{ __('patients.allergies_placeholder') }}">
                            </div>
                            
                            <div class="col-md-12">
                                <label for="medical_history" class="form-label">{{ __('patients.medical_history') }}</label>
                                <textarea class="form-control" id="medical_history" name="medical_history" rows="3"></textarea>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="notes" class="form-label">{{ __('common.notes') }}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ base_url }}/patients" class="btn btn-secondary">
                        <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Side Information -->
        <div class="col-lg-4">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-info-circle-fill me-2"></i>{{ __('common.information') }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-2"><i class="bi bi-check-circle text-success me-2"></i>Les champs marqués d'un * sont obligatoires</p>
                    <p class="mb-2"><i class="bi bi-shield-check text-primary me-2"></i>Les données médicales sont cryptées et sécurisées</p>
                    <p class="mb-0"><i class="bi bi-clock-history text-warning me-2"></i>Le numéro patient sera généré automatiquement</p>
                </div>
            </div>
            
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-lightbulb-fill me-2"></i>{{ __('common.tips') }}</h5>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <ul class="mb-0">
                            <li>Vérifiez l'existence du patient avant de créer un doublon</li>
                            <li>Remplissez le maximum d'informations pour un meilleur suivi</li>
                            <li>Les allergies sont importantes pour la sécurité du patient</li>
                        </ul>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Bootstrap validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}