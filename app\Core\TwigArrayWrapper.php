<?php

namespace App\Core;

/**
 * A wrapper class that makes arrays safe for Twig output
 * while maintaining array access syntax
 */
class TwigArrayWrapper implements \ArrayAccess, \IteratorAggregate, \Countable
{
    private $data;
    
    public function __construct(array $data)
    {
        $this->data = $data;
    }
    
    // ArrayAccess implementation
    public function offsetExists($offset): bool
    {
        return isset($this->data[$offset]);
    }
    
    public function offsetGet($offset): mixed
    {
        return $this->data[$offset] ?? null;
    }
    
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->data[] = $value;
        } else {
            $this->data[$offset] = $value;
        }
    }
    
    public function offsetUnset($offset): void
    {
        unset($this->data[$offset]);
    }
    
    // IteratorAggregate implementation
    public function getIterator(): \ArrayIterator
    {
        return new \ArrayIterator($this->data);
    }
    
    // Countable implementation
    public function count(): int
    {
        return count($this->data);
    }
    
    // Magic method to allow property access
    public function __get($name)
    {
        return $this->data[$name] ?? null;
    }
    
    public function __isset($name)
    {
        return isset($this->data[$name]);
    }
    
    // Convert to string without error
    public function __toString()
    {
        return json_encode($this->data);
    }
    
    // Get raw array data
    public function toArray()
    {
        return $this->data;
    }
}