<?php

use App\Middleware\PermissionMiddleware;

/**
 * Example route configurations using PermissionMiddleware
 * 
 * This file demonstrates how to use the PermissionMiddleware
 * to protect routes with permission checks
 */

// Basic permission check - single permission required
Flight::route('/users/create', 
    PermissionMiddleware::require('users.create'), 
    [$userController, 'create']
);

// Multiple middleware can be chained
Flight::route('/users/@id/edit', 
    [
        $authMiddleware,  // First check if logged in
        PermissionMiddleware::require('users.update')  // Then check permission
    ],
    [$userController, 'edit']
);

// Check if user has ANY of the listed permissions
Flight::route('/reports', 
    PermissionMiddleware::requireAny([
        'reports.view',
        'reports.generate',
        'reports.export'
    ]), 
    [$reportController, 'index']
);

// Check if user has ALL of the listed permissions
Flight::route('/system/backup', 
    PermissionMiddleware::requireAll([
        'system.manage',
        'backup.create',
        'backup.download'
    ]), 
    [$systemController, 'backup']
);

// Super admin only routes
Flight::route('/system/dangerous-operation', 
    PermissionMiddleware::superAdminOnly(), 
    [$systemController, 'dangerousOperation']
);

// API routes with JSON error responses
Flight::route('POST /api/users', 
    PermissionMiddleware::requireApi('users.create'), 
    [$apiController, 'createUser']
);

Flight::route('DELETE /api/users/@id', 
    PermissionMiddleware::requireApi('users.delete'), 
    [$apiController, 'deleteUser']
);

// Module-based permission checking
Flight::route('/invoices/create', 
    PermissionMiddleware::requireModuleAction('invoices', 'create'), 
    [$invoiceController, 'create']
);

// Dynamic permission based on request
Flight::route('/documents/@type/create', 
    PermissionMiddleware::requireDynamic(function($request) {
        $type = $request->params['type'];
        
        // Map document types to permissions
        $permissionMap = [
            'invoice' => 'invoices.create',
            'quote' => 'quotes.create',
            'order' => 'orders.create'
        ];
        
        return $permissionMap[$type] ?? null;
    }), 
    [$documentController, 'create']
);

// Custom redirect and message
Flight::route('/admin/users', 
    PermissionMiddleware::require(
        'users.manage',
        '/dashboard',  // Redirect to dashboard if denied
        'You need admin privileges to manage users.'  // Custom message
    ), 
    [$adminController, 'users']
);

// Group routes with common permission
Flight::group('/admin', function() {
    
    // All routes in this group require admin.access permission
    Flight::before('route', PermissionMiddleware::require('admin.access'));
    
    Flight::route('/dashboard', [$adminController, 'dashboard']);
    Flight::route('/settings', [$adminController, 'settings']);
    Flight::route('/logs', [$adminController, 'logs']);
});

// RESTful resource with different permissions per action
$userRoutes = [
    ['GET', '',         'index',   'users.view'],     // List users
    ['GET', '/@id',     'show',    'users.view'],     // View user
    ['GET', '/create',  'create',  'users.create'],   // Create form
    ['POST', '',        'store',   'users.create'],   // Store user
    ['GET', '/@id/edit', 'edit',   'users.update'],   // Edit form
    ['PUT', '/@id',     'update',  'users.update'],   // Update user
    ['DELETE', '/@id',  'destroy', 'users.delete']    // Delete user
];

foreach ($userRoutes as [$method, $path, $action, $permission]) {
    Flight::route(
        "$method /users$path",
        PermissionMiddleware::require($permission),
        [$userController, $action]
    );
}

// Complex permission logic
Flight::route('/invoices/@id/approve', 
    function() {
        $invoiceId = Flight::request()->params['id'];
        
        // Custom permission logic
        $invoice = Invoice::find($invoiceId);
        $userId = $_SESSION['user_id'];
        
        // Check various conditions
        if (isSuperAdmin()) {
            return true;  // Super admin can always approve
        }
        
        if (hasPermission('invoices.approve.all')) {
            return true;  // Has permission to approve all invoices
        }
        
        if (hasPermission('invoices.approve.own') && $invoice->created_by == $userId) {
            return true;  // Can approve own invoices
        }
        
        if (hasPermission('invoices.approve.department')) {
            // Check if invoice belongs to user's department
            $userDept = User::find($userId)['department_id'];
            $invoiceDept = $invoice->department_id;
            if ($userDept == $invoiceDept) {
                return true;
            }
        }
        
        // No permission
        $_SESSION['flash']['error'] = 'You do not have permission to approve this invoice.';
        Flight::redirect('/invoices');
        exit;
    },
    [$invoiceController, 'approve']
);