// Temporary fix to disable the invoice-billable-fix.js override
document.addEventListener('DOMContentLoaded', function() {
    console.log('Disabling invoice billable fix override');
    
    // Wait a bit to ensure the other script has loaded
    setTimeout(function() {
        // Restore the original loadBillableOptions function from the template
        const billableType = document.getElementById('billable_type');
        if (billableType) {
            // Trigger the change event to reload options using server-side data
            billableType.dispatchEvent(new Event('change'));
        }
    }, 100);
});