{% extends "base-modern.twig" %}

{% block title %}{{ __('patients.title') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('patients.title') }}</h1>
        <a href="{{ base_url }}/patients/create" class="btn btn-primary">
            <i class="bi bi-plus-circle me-2"></i>{{ __('patients.add_new') }}
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('patients.total_active') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_active }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people-fill text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('patients.new_this_month') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.new_this_month }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-plus-fill text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('patients.visits_this_month') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.visits_this_month }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-calendar-check text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('patients.average_age') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.average_age }} ans</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-graph-up text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patients Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 fw-bold text-primary">{{ __('patients.list') }}</h6>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" placeholder="{{ __('common.search') }}..." id="searchInput">
                        <button class="btn btn-sm btn-outline-secondary" type="button">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="patientsTable">
                    <thead>
                        <tr>
                            <th>{{ __('patients.patient_number') }}</th>
                            <th>{{ __('common.name') }}</th>
                            <th>{{ __('common.birth_date') }}</th>
                            <th>{{ __('common.phone') }}</th>
                            <th>{{ __('common.email') }}</th>
                            <th>{{ __('patients.last_visit') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for patient in patients %}
                        <tr>
                            <td>{{ patient.patient_number }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2">
                                        <span class="avatar-text rounded-circle bg-primary">
                                            {{ patient.first_name|first|upper }}{{ patient.last_name|first|upper }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ patient.first_name }} {{ patient.last_name }}</div>
                                        <small class="text-muted">{{ patient.gender == 'M' ? __('common.male') : __('common.female') }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ patient.birth_date|date('d/m/Y') }}</td>
                            <td>{{ patient.phone|default('-') }}</td>
                            <td>{{ patient.email|default('-') }}</td>
                            <td>
                                {% if patient.last_visit %}
                                    {{ patient.last_visit|date('d/m/Y') }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if patient.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown" data-bs-boundary="viewport" data-bs-flip="true">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ base_url }}/patients/{{ patient.id }}">
                                                <i class="bi bi-eye me-2"></i>{{ __('common.view') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="{{ base_url }}/patients/{{ patient.id }}/edit">
                                                <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="confirmDelete({{ patient.id }})">
                                                <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if patients|length > 0 %}
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        Affichage de <strong>{{ (current_page - 1) * per_page + 1 }}</strong> à 
                        <strong>{{ min(current_page * per_page, total_patients) }}</strong> sur 
                        <strong>{{ total_patients }}</strong> patients
                    </div>
                    {{ include('_partials/pagination.twig', {
                        'current_page': current_page,
                        'total_pages': total_pages,
                        'base_url': base_url ~ '/patients'
                    }) }}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#patientsTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});

function confirmDelete(id) {
    if (confirm('{{ __("common.are_you_sure") }}')) {
        window.location.href = '{{ base_url }}/patients/' + id + '/delete';
    }
}
</script>

<style>
/* Fix for dropdown positioning in tables */
.table-responsive .dropdown {
    position: static;
}

.table-responsive .dropdown-menu {
    position: absolute !important;
    transform: translate3d(0px, 0px, 0px) !important;
    will-change: transform !important;
}
</style>
{% endblock %}