/**
 * TableHelper v2 - Enhanced table functionality for FIT application
 * Features: Live search, filter persistence, import/export, bulk actions, sorting, column reordering
 */

class TableHelper {
    constructor(options) {
        this.options = {
            tableId: options.tableId || 'data-table',
            searchInputId: options.searchInputId || 'search',
            searchColumns: options.searchColumns || [], // Column indices to search
            filters: options.filters || [], // Array of filter configurations
            storageKey: options.storageKey || 'table_filters',
            exportFormats: options.exportFormats || ['csv', 'excel', 'pdf'],
            exportUrl: options.exportUrl || '',
            bulkActions: options.bulkActions || [],
            bulkActionUrl: options.bulkActionUrl || '',
            sortable: options.sortable !== false, // Enable sorting by default
            reorderable: false, // Disable column reordering - now handled in config
            defaultSort: options.defaultSort || null, // { column: 0, direction: 'asc' }
            columnOrder: options.columnOrder || null, // Predefined column order from config
            translations: options.translations || {
                search: 'Search...',
                noResults: 'No results found',
                bulkActions: 'Bulk Actions',
                export: 'Export',
                import: 'Import',
                reset: 'Reset Filters',
                results: 'results',
                selected: 'selected',
                sortAsc: 'Sort ascending',
                sortDesc: 'Sort descending',
                dragToReorder: 'Drag to reorder columns',
                stateSaved: 'Table state saved'
            }
        };
        
        this.currentSort = null;
        this.sortOrder = []; // Array to store multi-column sort
        this.columnOrder = [];
        this.originalColumnOrder = [];
        this.draggedColumn = null;
        this.saveIndicator = null;
        
        this.init();
    }
    
    init() {
        this.table = document.getElementById(this.options.tableId);
        this.searchInput = document.getElementById(this.options.searchInputId);
        
        if (!this.table) {
            console.error('Table not found:', this.options.tableId);
            return;
        }
        
        // Initialize column order arrays first
        this.initializeColumnOrder();
        
        // Setup features
        this.setupSearch();
        this.setupFilters();
        this.setupBulkActions();
        
        // Load saved state before setting up sorting and reordering
        this.loadSavedState();
        
        // Setup sorting and reordering after state is loaded
        this.setupSorting();
        this.setupColumnReordering();
        
        // Apply saved sort if exists
        if (this.currentSort) {
            setTimeout(() => {
                this.sortTable(this.currentSort.column, this.currentSort.direction);
            }, 100);
        }
        
        // Create save indicator
        this.createSaveIndicator();
    }
    
    // Initialize column order arrays
    initializeColumnOrder() {
        const headers = this.table.querySelectorAll('thead th');
        headers.forEach((header, index) => {
            header.setAttribute('data-original-index', index);
            this.originalColumnOrder.push(index);
        });
        
        // Use predefined column order from config if available
        if (this.options.columnOrder && Array.isArray(this.options.columnOrder)) {
            this.columnOrder = [...this.options.columnOrder];
            // Apply the configured column order
            this.applyColumnOrder();
        } else {
            // Default order
            this.columnOrder = [...this.originalColumnOrder];
        }
    }
    
    // Sorting functionality
    setupSorting() {
        if (!this.options.sortable) return;
        
        const headers = this.table.querySelectorAll('thead th');
        
        headers.forEach((header, index) => {
            // Skip checkbox column and action columns
            if (header.querySelector('input[type="checkbox"]') || 
                header.classList.contains('no-sort') ||
                header.textContent.toLowerCase().includes('action')) {
                return;
            }
            
            header.style.cursor = 'pointer';
            header.style.userSelect = 'none';
            header.setAttribute('data-sortable', 'true');
            // Store both current position and original index
            header.setAttribute('data-column', index);
            if (!header.hasAttribute('data-original-index')) {
                header.setAttribute('data-original-index', index);
            }
            header.setAttribute('title', 'Click to sort, Shift+Click for multi-column sort');
            
            // Add sort indicator if not already present
            if (!header.querySelector('.sort-indicator')) {
                const sortIndicator = document.createElement('span');
                sortIndicator.className = 'sort-indicator ms-1';
                sortIndicator.innerHTML = '<i class="bi bi-arrow-down-up text-muted small"></i>';
                header.appendChild(sortIndicator);
            }
            
            // Remove existing click listener if any
            const existingListener = header._sortClickHandler;
            if (existingListener) {
                header.removeEventListener('click', existingListener);
            }
            
            // Create new click handler
            const clickHandler = (e) => {
                // Don't sort if dragging or clicking on drag handle
                if (e.target.closest('.drag-handle')) return;
                
                // Check if shift key is held for multi-column sort
                const isMultiSort = e.shiftKey;
                // Use the original index for sorting
                const originalIndex = parseInt(header.getAttribute('data-original-index'));
                this.sortTable(originalIndex, null, isMultiSort);
            };
            
            // Store reference to handler so we can remove it later
            header._sortClickHandler = clickHandler;
            header.addEventListener('click', clickHandler);
        });
        
        // Apply saved sort if exists, otherwise use default sort
        if (this.sortOrder && this.sortOrder.length > 0) {
            // Apply the loaded multi-column sort state
            this.applyMultiColumnSort();
        } else if (this.options.defaultSort) {
            // Apply default sort if no saved sort
            this.sortTable(this.options.defaultSort.column, this.options.defaultSort.direction);
        }
    }
    
    sortTable(column, direction = null, isMultiSort = false) {
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr')).filter(row => 
            !row.querySelector('td[colspan]') // Exclude empty state rows
        );
        
        // Handle multi-column sorting
        if (!isMultiSort) {
            // Single column sort - clear existing sorts
            this.sortOrder = [];
        }
        
        // Find if this column is already in sort order
        const existingSortIndex = this.sortOrder.findIndex(s => s.column === column);
        
        if (existingSortIndex >= 0) {
            // Column already sorted, toggle direction
            const currentDirection = this.sortOrder[existingSortIndex].direction;
            if (direction === null) {
                direction = currentDirection === 'asc' ? 'desc' : 'asc';
            }
            this.sortOrder[existingSortIndex].direction = direction;
        } else {
            // New column to sort
            if (direction === null) {
                direction = 'asc';
            }
            this.sortOrder.push({ column, direction });
        }
        
        // Update indicators for all sorted columns
        this.updateMultiSortIndicators();
        
        // Sort rows using all sort criteria
        rows.sort((a, b) => {
            for (const sort of this.sortOrder) {
                // Find the actual column position based on original index
                let actualColumnIndex = sort.column;
                
                // If columns have been reordered, find the current position
                if (this.columnOrder && this.columnOrder.length > 0) {
                    // Find where the original column is now positioned
                    actualColumnIndex = this.columnOrder.indexOf(sort.column);
                    if (actualColumnIndex === -1) {
                        // Fallback to original if not found
                        actualColumnIndex = sort.column;
                    }
                }
                
                const aValue = this.getCellValue(a.cells[actualColumnIndex]);
                const bValue = this.getCellValue(b.cells[actualColumnIndex]);
                
                let compareResult = 0;
                
                // Check for data-sort attribute first
                const aCell = a.cells[actualColumnIndex];
                const bCell = b.cells[actualColumnIndex];
                const aSortValue = aCell ? aCell.getAttribute('data-sort') : null;
                const bSortValue = bCell ? bCell.getAttribute('data-sort') : null;
                
                if (aSortValue && bSortValue) {
                    // Check if these look like dates first (YYYY-MM-DD format)
                    const datePattern = /^\d{4}-\d{2}-\d{2}/;
                    if (datePattern.test(aSortValue) && datePattern.test(bSortValue)) {
                        // Parse as dates
                        const aDate = new Date(aSortValue);
                        const bDate = new Date(bSortValue);
                        
                        if (!isNaN(aDate.getTime()) && !isNaN(bDate.getTime())) {
                            compareResult = sort.direction === 'asc' ? aDate.getTime() - bDate.getTime() : bDate.getTime() - aDate.getTime();
                        } else {
                            // Fallback for invalid dates
                            compareResult = aSortValue.localeCompare(bSortValue, undefined, { numeric: true, sensitivity: 'base' });
                            if (sort.direction === 'desc') compareResult = -compareResult;
                        }
                    } else {
                        // Try to parse as numbers
                        const aNum = parseFloat(aSortValue);
                        const bNum = parseFloat(bSortValue);
                        
                        if (!isNaN(aNum) && !isNaN(bNum)) {
                            compareResult = sort.direction === 'asc' ? aNum - bNum : bNum - aNum;
                        } else {
                            // String comparison
                            compareResult = aSortValue.localeCompare(bSortValue, undefined, { numeric: true, sensitivity: 'base' });
                            if (sort.direction === 'desc') compareResult = -compareResult;
                        }
                    }
                } else {
                    // Try to parse as number first
                    const aNum = parseFloat(aValue);
                    const bNum = parseFloat(bValue);
                    
                    if (!isNaN(aNum) && !isNaN(bNum)) {
                        compareResult = sort.direction === 'asc' ? aNum - bNum : bNum - aNum;
                    } else {
                        // Try to parse as date
                        const aDate = Date.parse(aValue);
                        const bDate = Date.parse(bValue);
                        
                        if (!isNaN(aDate) && !isNaN(bDate)) {
                            compareResult = sort.direction === 'asc' ? aDate - bDate : bDate - aDate;
                        } else {
                            // Fall back to string comparison
                            compareResult = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
                            if (sort.direction === 'desc') compareResult = -compareResult;
                        }
                    }
                }
                
                // If values are different, return the comparison result
                if (compareResult !== 0) {
                    return compareResult;
                }
                // If values are equal, continue to next sort column
            }
            return 0;
        });
        
        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
        
        // Save current sort state
        this.currentSort = this.sortOrder.length > 0 ? this.sortOrder[0] : null;
        this.saveTableState();
        
        // Dispatch table updated event
        this.table.dispatchEvent(new CustomEvent('tableUpdated'));
    }
    
    getCellValue(cell) {
        // Get text content, but handle special cases
        const input = cell.querySelector('input:not([type="checkbox"])');
        if (input) return input.value;
        
        const select = cell.querySelector('select');
        if (select) return select.options[select.selectedIndex].text;
        
        // Remove any HTML and trim
        return cell.textContent.replace(/<[^>]*>/g, '').trim();
    }
    
    updateSortIndicators(column, direction) {
        // Reset all indicators
        this.table.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.innerHTML = '<i class="bi bi-arrow-down-up text-muted small"></i>';
        });
        
        // Find header by original index
        const headers = this.table.querySelectorAll('thead th');
        headers.forEach((header) => {
            const originalIndex = parseInt(header.getAttribute('data-original-index'));
            if (originalIndex === column) {
                const indicator = header.querySelector('.sort-indicator');
                if (indicator) {
                    if (direction === 'asc') {
                        indicator.innerHTML = '<i class="bi bi-arrow-up text-primary"></i>';
                    } else {
                        indicator.innerHTML = '<i class="bi bi-arrow-down text-primary"></i>';
                    }
                }
            }
        });
    }
    
    updateMultiSortIndicators() {
        // Reset all indicators
        this.table.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.innerHTML = '<i class="bi bi-arrow-down-up text-muted small"></i>';
            indicator.parentElement.removeAttribute('data-sort-order');
        });
        
        // Update indicators for all sorted columns
        this.sortOrder.forEach((sort, index) => {
            // Find header by original index
            const header = this.table.querySelector(`thead th[data-original-index="${sort.column}"]`);
            if (header) {
                const indicator = header.querySelector('.sort-indicator');
                if (indicator) {
                    const orderNumber = this.sortOrder.length > 1 ? `<small class="ms-1">${index + 1}</small>` : '';
                    if (sort.direction === 'asc') {
                        indicator.innerHTML = `<i class="bi bi-arrow-up text-primary"></i>${orderNumber}`;
                    } else {
                        indicator.innerHTML = `<i class="bi bi-arrow-down text-primary"></i>${orderNumber}`;
                    }
                }
                header.setAttribute('data-sort-order', index + 1);
            }
        });
    }
    
    applyMultiColumnSort() {
        // Apply the saved multi-column sort without changing the sort order
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr')).filter(row => 
            !row.querySelector('td[colspan]')
        );
        
        // Update indicators
        this.updateMultiSortIndicators();
        
        // Sort rows using all sort criteria
        rows.sort((a, b) => {
            for (const sort of this.sortOrder) {
                // Find the actual column position based on original index
                let actualColumnIndex = sort.column;
                
                // If columns have been reordered, find the current position
                if (this.columnOrder && this.columnOrder.length > 0) {
                    // Find where the original column is now positioned
                    actualColumnIndex = this.columnOrder.indexOf(sort.column);
                    if (actualColumnIndex === -1) {
                        // Fallback to original if not found
                        actualColumnIndex = sort.column;
                    }
                }
                
                const aCell = a.cells[actualColumnIndex];
                const bCell = b.cells[actualColumnIndex];
                
                if (!aCell || !bCell) continue;
                
                // Check for data-sort attribute first
                const aSortValue = aCell.getAttribute('data-sort');
                const bSortValue = bCell.getAttribute('data-sort');
                
                let aValue, bValue;
                if (aSortValue && bSortValue) {
                    aValue = aSortValue;
                    bValue = bSortValue;
                } else {
                    aValue = this.getCellValue(aCell);
                    bValue = this.getCellValue(bCell);
                }
                
                let compareResult = 0;
                
                // Try to parse as number first
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    compareResult = sort.direction === 'asc' ? aNum - bNum : bNum - aNum;
                } else {
                    // Try to parse as date
                    const aDate = Date.parse(aValue);
                    const bDate = Date.parse(bValue);
                    
                    if (!isNaN(aDate) && !isNaN(bDate)) {
                        compareResult = sort.direction === 'asc' ? aDate - bDate : bDate - aDate;
                    } else {
                        // Fall back to string comparison
                        compareResult = aValue.localeCompare(bValue, undefined, { numeric: true, sensitivity: 'base' });
                        if (sort.direction === 'desc') compareResult = -compareResult;
                    }
                }
                
                if (compareResult !== 0) {
                    return compareResult;
                }
            }
            return 0;
        });
        
        // Re-append sorted rows
        rows.forEach(row => tbody.appendChild(row));
    }
    
    // Column reordering functionality
    setupColumnReordering() {
        if (!this.options.reorderable) return;
        
        const headers = this.table.querySelectorAll('thead th');
        const headerRow = this.table.querySelector('thead tr');
        
        // Process each header
        headers.forEach((header, index) => {
            // Skip checkbox and action columns
            if (header.querySelector('input[type="checkbox"]') || 
                header.classList.contains('no-reorder') ||
                header.textContent.toLowerCase().includes('action')) {
                return;
            }
            
            // Add drag handle if not already present
            if (!header.querySelector('.drag-handle')) {
                const dragHandle = document.createElement('span');
                dragHandle.className = 'drag-handle ms-2';
                dragHandle.innerHTML = '<i class="bi bi-grip-vertical text-muted small"></i>';
                dragHandle.style.cursor = 'move';
                dragHandle.title = this.options.translations.dragToReorder;
                
                header.appendChild(dragHandle);
            }
            
            // Make header draggable
            header.draggable = true;
            header.style.position = 'relative';
            
            // Drag events
            header.addEventListener('dragstart', this.handleDragStart.bind(this));
            header.addEventListener('dragover', this.handleDragOver.bind(this));
            header.addEventListener('drop', this.handleDrop.bind(this));
            header.addEventListener('dragend', this.handleDragEnd.bind(this));
            header.addEventListener('dragenter', this.handleDragEnter.bind(this));
            header.addEventListener('dragleave', this.handleDragLeave.bind(this));
        });
    }
    
    handleDragStart(e) {
        this.draggedColumn = e.target;
        e.target.style.opacity = '0.5';
        e.dataTransfer.effectAllowed = 'move';
        e.dataTransfer.setData('text/html', e.target.innerHTML);
        
        // Add dragging class to table
        this.table.classList.add('dragging-column');
    }
    
    handleDragOver(e) {
        if (e.preventDefault) {
            e.preventDefault();
        }
        e.dataTransfer.dropEffect = 'move';
        return false;
    }
    
    handleDragEnter(e) {
        if (e.target.tagName === 'TH' && e.target !== this.draggedColumn) {
            e.target.classList.add('drag-over');
        }
    }
    
    handleDragLeave(e) {
        if (e.target.tagName === 'TH') {
            e.target.classList.remove('drag-over');
        }
    }
    
    handleDrop(e) {
        if (e.stopPropagation) {
            e.stopPropagation();
        }
        
        if (e.target.tagName === 'TH' && this.draggedColumn !== e.target) {
            // Get column indices
            const draggedIndex = parseInt(this.draggedColumn.getAttribute('data-original-index'));
            const targetIndex = parseInt(e.target.getAttribute('data-original-index'));
            
            // Reorder columns
            this.reorderColumns(draggedIndex, targetIndex);
        }
        
        return false;
    }
    
    handleDragEnd(e) {
        e.target.style.opacity = '';
        
        // Remove all drag classes
        this.table.classList.remove('dragging-column');
        this.table.querySelectorAll('.drag-over').forEach(el => {
            el.classList.remove('drag-over');
        });
        
        this.draggedColumn = null;
    }
    
    reorderColumns(fromIndex, toIndex) {
        const headerRow = this.table.querySelector('thead tr');
        const bodyRows = this.table.querySelectorAll('tbody tr');
        
        // Update column order array
        const currentFromPos = this.columnOrder.indexOf(fromIndex);
        const currentToPos = this.columnOrder.indexOf(toIndex);
        
        if (currentFromPos !== -1 && currentToPos !== -1) {
            const [removed] = this.columnOrder.splice(currentFromPos, 1);
            this.columnOrder.splice(currentToPos, 0, removed);
        }
        
        // Reorder header cells
        const headers = Array.from(headerRow.children);
        const newHeaders = this.columnOrder.map(i => headers[i]);
        headerRow.innerHTML = '';
        newHeaders.forEach(header => headerRow.appendChild(header));
        
        // Reorder body cells
        bodyRows.forEach(row => {
            if (!row.querySelector('td[colspan]')) { // Skip empty state rows
                const cells = Array.from(row.children);
                const newCells = this.columnOrder.map(i => cells[i]);
                row.innerHTML = '';
                newCells.forEach(cell => row.appendChild(cell));
            }
        });
        
        // Save column order
        this.saveTableState();
    }
    
    // Live Search Functionality (Enhanced)
    setupSearch() {
        if (!this.searchInput) return;
        
        const tbody = this.table.querySelector('tbody');
        const rows = tbody ? Array.from(tbody.querySelectorAll('tr')) : [];
        
        // Store original display state
        rows.forEach(row => {
            row.dataset.originalDisplay = row.style.display || '';
        });
        
        // Create search results counter
        const counter = document.createElement('small');
        counter.id = 'search-results-count';
        counter.className = 'text-muted ms-2';
        
        if (this.searchInput.parentElement) {
            this.searchInput.parentElement.appendChild(counter);
        }
        
        // Debounced search function
        let searchTimeout;
        const performSearch = () => {
            const searchTerm = this.searchInput.value.toLowerCase().trim();
            
            // Save to localStorage
            if (searchTerm) {
                localStorage.setItem(this.options.storageKey + '_search', searchTerm);
            } else {
                localStorage.removeItem(this.options.storageKey + '_search');
            }
            
            if (searchTerm === '') {
                rows.forEach(row => {
                    row.style.display = row.dataset.originalDisplay;
                    this.clearHighlights(row);
                });
                this.updateResultsCount(rows.length);
                // Dispatch table updated event
                this.table.dispatchEvent(new CustomEvent('tableUpdated'));
                return;
            }
            
            let visibleCount = 0;
            
            rows.forEach(row => {
                let found = false;
                
                // Search in columns based on current order
                const searchIndices = this.options.searchColumns.length > 0 
                    ? this.options.searchColumns 
                    : this.columnOrder;
                
                searchIndices.forEach(colIndex => {
                    const actualIndex = this.columnOrder.indexOf(colIndex);
                    if (actualIndex !== -1) {
                        const cell = row.cells[actualIndex];
                        if (cell && cell.textContent.toLowerCase().includes(searchTerm)) {
                            found = true;
                            this.highlightText(cell, searchTerm);
                        }
                    }
                });
                
                if (found) {
                    row.style.display = row.dataset.originalDisplay;
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                    this.clearHighlights(row);
                }
            });
            
            this.updateResultsCount(visibleCount);
            
            // Dispatch table updated event
            this.table.dispatchEvent(new CustomEvent('tableUpdated'));
        };
        
        // Add event listeners
        this.searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(performSearch, 300);
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.searchInput.value = '';
                performSearch();
            }
        });
        
        // Add clear button
        this.addClearButton(this.searchInput, performSearch);
        
        // Load saved search
        const savedSearch = localStorage.getItem(this.options.storageKey + '_search');
        if (savedSearch && !this.searchInput.value) {
            this.searchInput.value = savedSearch;
            performSearch();
        }
    }
    
    // Filter Management
    setupFilters() {
        this.options.filters.forEach(filter => {
            const element = document.getElementById(filter.id);
            if (!element) return;
            
            element.addEventListener('change', () => {
                this.saveFilters();
                
                if (filter.autoSubmit !== false) {
                    const form = element.closest('form');
                    if (form) {
                        form.submit();
                    }
                }
            });
        });
    }
    
    saveFilters() {
        const filters = {};
        
        this.options.filters.forEach(filter => {
            const element = document.getElementById(filter.id);
            if (element && element.value) {
                filters[filter.id] = element.value;
            }
        });
        
        localStorage.setItem(this.options.storageKey, JSON.stringify(filters));
    }
    
    // Save table state (sort only - column order now in config)
    saveTableState() {
        const state = {
            sort: this.currentSort,
            sortOrder: this.sortOrder // Save multi-column sort
        };
        
        localStorage.setItem(this.options.storageKey + '_state', JSON.stringify(state));
        
        // Show save indicator
        this.showSaveIndicator();
    }
    
    loadSavedState() {
        // Load filters
        const savedFilters = localStorage.getItem(this.options.storageKey);
        if (savedFilters) {
            try {
                const filters = JSON.parse(savedFilters);
                const urlParams = new URLSearchParams(window.location.search);
                
                // Only apply saved filters if no URL parameters exist
                if (urlParams.toString() === '') {
                    Object.entries(filters).forEach(([id, value]) => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.value = value;
                        }
                    });
                    
                    // If we have filters but no URL params, reload with filters
                    if (Object.keys(filters).length > 0) {
                        const newUrl = new URL(window.location.href);
                        Object.entries(filters).forEach(([key, value]) => {
                            if (value) newUrl.searchParams.set(key, value);
                        });
                        window.location.href = newUrl.toString();
                        return;
                    }
                }
            } catch (e) {
                console.error('Error loading saved filters:', e);
            }
        }
        
        // Load table state (sort and column order)
        const savedState = localStorage.getItem(this.options.storageKey + '_state');
        if (savedState) {
            try {
                const state = JSON.parse(savedState);
                // Restore sort state
                if (state.sortOrder && Array.isArray(state.sortOrder)) {
                    // New multi-column sort format
                    this.sortOrder = state.sortOrder;
                    this.currentSort = this.sortOrder.length > 0 ? this.sortOrder[0] : null;
                } else if (state.sort) {
                    // Legacy single sort format
                    this.sortOrder = [state.sort];
                    this.currentSort = state.sort;
                }
            } catch (e) {
                console.error('Error loading saved table state:', e);
            }
        }
    }
    
    applyColumnOrder() {
        const headerRow = this.table.querySelector('thead tr');
        const bodyRows = this.table.querySelectorAll('tbody tr');
        
        // Reorder header cells
        const headers = Array.from(headerRow.children);
        const newHeaders = this.columnOrder.map(i => headers[i]);
        headerRow.innerHTML = '';
        newHeaders.forEach((header, index) => {
            // Update the current position data attribute
            header.setAttribute('data-column', index);
            headerRow.appendChild(header);
        });
        
        // Reorder body cells
        bodyRows.forEach(row => {
            if (!row.querySelector('td[colspan]')) { // Skip empty state rows
                const cells = Array.from(row.children);
                const newCells = this.columnOrder.map(i => cells[i]);
                row.innerHTML = '';
                newCells.forEach(cell => row.appendChild(cell));
            }
        });
        
        // Re-setup sorting to update event listeners with new positions
        this.setupSorting();
    }
    
    clearFilters() {
        localStorage.removeItem(this.options.storageKey);
        localStorage.removeItem(this.options.storageKey + '_search');
        localStorage.removeItem(this.options.storageKey + '_state');
        
        // Clear sort order
        this.sortOrder = [];
        this.currentSort = null;
        this.updateMultiSortIndicators();
        
        // Clear all checkboxes
        const selectAllCheckbox = this.table.querySelector('#selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
        
        const checkboxes = this.table.querySelectorAll('.row-checkbox');
        checkboxes.forEach(cb => cb.checked = false);
        this.updateBulkActionButton();
        
        // Reset column order
        this.columnOrder = [...this.originalColumnOrder];
        this.applyColumnOrder();
        
        // Clear sort
        this.currentSort = null;
        this.table.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.innerHTML = '<i class="bi bi-arrow-down-up text-muted small"></i>';
        });
        
        // Clear form inputs
        this.options.filters.forEach(filter => {
            const element = document.getElementById(filter.id);
            if (element) {
                element.value = '';
            }
        });
        
        if (this.searchInput) {
            this.searchInput.value = '';
        }
    }
    
    // Bulk Actions
    setupBulkActions() {
        const selectAllCheckbox = this.table.querySelector('#selectAll');
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        
        if (!selectAllCheckbox) return;
        
        // Select all functionality - only select visible rows
        selectAllCheckbox.addEventListener('change', function() {
            const visibleCheckboxes = this.getVisibleCheckboxes();
            visibleCheckboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
            this.updateBulkActionButton();
        }.bind(this));
        
        // Use event delegation for individual checkbox changes
        this.table.addEventListener('change', (e) => {
            if (e.target.classList.contains('row-checkbox')) {
                this.updateBulkActionButton();
                this.updateSelectAllState();
            }
        });
        
        // Update state when table is sorted or searched
        this.table.addEventListener('tableUpdated', () => {
            this.updateSelectAllState();
            this.updateBulkActionButton();
        });
    }
    
    updateBulkActionButton() {
        const selected = this.getSelectedIds().length;
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        const bulkActionsCount = document.getElementById('bulkActionsCount');
        
        if (bulkActionsBtn) {
            // Enable/disable the button
            const wasDisabled = bulkActionsBtn.disabled;
            bulkActionsBtn.disabled = selected === 0;
            
            // Update the count display
            if (bulkActionsCount) {
                bulkActionsCount.textContent = selected > 0 ? ` (${selected})` : '';
            }
            
            // Don't interfere with Bootstrap dropdown when updating count
            // Bootstrap will handle the dropdown functionality
        }
    }
    
    updateSelectAllState() {
        const selectAllCheckbox = this.table.querySelector('#selectAll');
        const visibleCheckboxes = this.getVisibleCheckboxes();
        const visibleCheckedBoxes = visibleCheckboxes.filter(cb => cb.checked);
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = visibleCheckboxes.length > 0 && visibleCheckboxes.length === visibleCheckedBoxes.length;
            selectAllCheckbox.indeterminate = visibleCheckedBoxes.length > 0 && visibleCheckedBoxes.length < visibleCheckboxes.length;
        }
    }
    
    getSelectedIds() {
        return Array.from(this.table.querySelectorAll('.row-checkbox:checked')).map(cb => cb.value);
    }
    
    getVisibleCheckboxes() {
        const rows = Array.from(this.table.querySelectorAll('tbody tr'));
        const visibleRows = rows.filter(row => {
            // Check if row is visible (not hidden by search or other filters)
            return row.style.display !== 'none' && !row.querySelector('td[colspan]'); // Skip empty state rows
        });
        
        return visibleRows.map(row => row.querySelector('.row-checkbox')).filter(cb => cb !== null);
    }
    
    // Export functionality
    exportData(format) {
        const url = new URL(this.options.exportUrl);
        url.searchParams.set('format', format);
        
        // Add current filters
        const urlParams = new URLSearchParams(window.location.search);
        for (const [key, value] of urlParams) {
            if (key !== 'format') {
                url.searchParams.set(key, value);
            }
        }
        
        // Add column order
        url.searchParams.set('column_order', JSON.stringify(this.columnOrder));
        
        // Add sort
        if (this.currentSort) {
            url.searchParams.set('sort_column', this.currentSort.column);
            url.searchParams.set('sort_direction', this.currentSort.direction);
        }
        
        window.location.href = url.toString();
    }
    
    // Import functionality
    showImportDialog() {
        // This would typically open a modal or redirect to import page
        if (this.options.importUrl) {
            window.location.href = this.options.importUrl;
        }
    }
    
    // Bulk action execution
    executeBulkAction(action) {
        const selectedIds = this.getSelectedIds();
        if (selectedIds.length === 0) return;
        
        // Find action configuration
        const actionConfig = this.options.bulkActions.find(a => a.action === action);
        if (!actionConfig) return;
        
        // Confirm if needed
        if (actionConfig.confirm) {
            if (!confirm(actionConfig.confirmMessage || 'Are you sure?')) {
                return;
            }
        }
        
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = actionConfig.url || this.options.bulkActionUrl;
        
        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }
        
        // Add action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;
        form.appendChild(actionInput);
        
        // Add selected IDs
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
    
    // Helper methods
    highlightText(element, searchTerm) {
        const text = element.textContent;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        element.innerHTML = text.replace(regex, '<mark class="highlight">$1</mark>');
    }
    
    clearHighlights(element) {
        element.querySelectorAll('mark.highlight').forEach(mark => {
            const text = mark.textContent;
            mark.replaceWith(text);
        });
    }
    
    updateResultsCount(count) {
        const counter = document.getElementById('search-results-count');
        if (!counter) return;
        
        if (this.searchInput && this.searchInput.value.trim() !== '') {
            counter.textContent = `(${count} ${this.options.translations.results})`;
        } else {
            counter.textContent = '';
        }
    }
    
    addClearButton(input, onClear) {
        const clearBtn = document.createElement('button');
        clearBtn.type = 'button';
        clearBtn.className = 'btn btn-sm position-absolute end-0 top-50 translate-middle-y me-5';
        clearBtn.style.display = 'none';
        clearBtn.style.zIndex = '10';
        clearBtn.innerHTML = '<i class="bi bi-x-circle"></i>';
        
        clearBtn.onclick = () => {
            input.value = '';
            localStorage.removeItem(this.options.storageKey + '_search');
            onClear();
            clearBtn.style.display = 'none';
        };
        
        input.parentElement.style.position = 'relative';
        input.parentElement.appendChild(clearBtn);
        
        input.addEventListener('input', () => {
            clearBtn.style.display = input.value ? 'block' : 'none';
        });
        
        // Initial state
        if (input.value) {
            clearBtn.style.display = 'block';
        }
    }
    
    // Create save indicator element
    createSaveIndicator() {
        // Check if indicator already exists
        this.saveIndicator = document.querySelector('.save-indicator');
        if (!this.saveIndicator) {
            this.saveIndicator = document.createElement('div');
            this.saveIndicator.className = 'save-indicator';
            this.saveIndicator.innerHTML = `
                <i class="bi bi-check-circle-fill"></i>
                <span>${this.options.translations.stateSaved || 'Table state saved'}</span>
            `;
            document.body.appendChild(this.saveIndicator);
        }
    }
    
    // Show save indicator with animation
    showSaveIndicator() {
        if (!this.saveIndicator) return;
        
        // Show the indicator
        this.saveIndicator.classList.add('show');
        
        // Hide after 2 seconds
        clearTimeout(this.saveIndicatorTimeout);
        this.saveIndicatorTimeout = setTimeout(() => {
            this.saveIndicator.classList.remove('show');
        }, 2000);
    }
}

// Global function for easy initialization
window.initTableHelper = function(options) {
    return new TableHelper(options);
};

// Global function to get current table helper instance
window.getTableHelper = function() {
    return window.tableHelper;
};

// Debug function to check saved state
window.debugTableState = function(storageKey) {
    const key = storageKey || 'invoices_filters_v2';
    const state = localStorage.getItem(key + '_state');
    const filters = localStorage.getItem(key);
    
    console.log('=== Table State Debug ===');
    console.log('Storage Key:', key);
    
    if (state) {
        try {
            const parsedState = JSON.parse(state);
            console.log('Saved State:', parsedState);
            console.log('- Sort (legacy):', parsedState.sort);
            console.log('- Multi-column Sort:', parsedState.sortOrder);
            console.log('- Column Order:', parsedState.columnOrder);
        } catch (e) {
            console.error('Error parsing state:', e);
        }
    } else {
        console.log('No saved state found');
    }
    
    if (filters) {
        try {
            const parsedFilters = JSON.parse(filters);
            console.log('Saved Filters:', parsedFilters);
        } catch (e) {
            console.error('Error parsing filters:', e);
        }
    } else {
        console.log('No saved filters found');
    }
    
    console.log('===================');
};