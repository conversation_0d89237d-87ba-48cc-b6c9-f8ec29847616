<?php
/**
 * Test rounding fix for TTC invoice calculations
 * This tests the case where <PERSON> has total of 794,97 instead of 795,00
 */

echo "=== ROUNDING FIX TEST ===\n\n";

// Test case: <PERSON> invoice
// 30 Collectif @ €30 TTC
// 19 Individuel @ €15 TTC

$items = [
    ['name' => 'Collectif', 'quantity' => 30, 'ttc_price' => 30.00],
    ['name' => 'Individuel', 'quantity' => 19, 'ttc_price' => 15.00]
];

$vatRate = 17; // 17% VAT

echo "Nicolas Moineau Invoice (Luxembourg VAT):\n";
echo "========================================\n\n";

$totalExpectedTTC = 0;
$totalCalculatedNET = 0;
$totalCalculatedVAT = 0;

foreach ($items as $item) {
    $itemTotalTTC = $item['quantity'] * $item['ttc_price'];
    $totalExpectedTTC += $itemTotalTTC;
    
    // Calculate NET price per unit
    $priceNET = $item['ttc_price'] / (1 + $vatRate / 100);
    $itemSubtotal = $item['quantity'] * $priceNET;
    $itemVAT = $itemSubtotal * ($vatRate / 100);
    
    $totalCalculatedNET += $itemSubtotal;
    $totalCalculatedVAT += $itemVAT;
    
    echo "{$item['name']}:\n";
    echo "  Quantity: {$item['quantity']}\n";
    echo "  TTC Price: €" . number_format($item['ttc_price'], 2) . "\n";
    echo "  NET Price: €" . number_format($priceNET, 2) . " (exact: €" . number_format($priceNET, 6) . ")\n";
    echo "  Subtotal NET: €" . number_format($itemSubtotal, 2) . " (exact: €" . number_format($itemSubtotal, 6) . ")\n";
    echo "  VAT Amount: €" . number_format($itemVAT, 2) . " (exact: €" . number_format($itemVAT, 6) . ")\n";
    echo "  Expected TTC Total: €" . number_format($itemTotalTTC, 2) . "\n\n";
}

$totalCalculated = $totalCalculatedNET + $totalCalculatedVAT;
$difference = $totalExpectedTTC - $totalCalculated;

echo "TOTALS:\n";
echo "=======\n";
echo "Subtotal NET: €" . number_format($totalCalculatedNET, 2) . " (exact: €" . number_format($totalCalculatedNET, 6) . ")\n";
echo "VAT Amount: €" . number_format($totalCalculatedVAT, 2) . " (exact: €" . number_format($totalCalculatedVAT, 6) . ")\n";
echo "Calculated Total: €" . number_format($totalCalculated, 2) . " (exact: €" . number_format($totalCalculated, 6) . ")\n";
echo "Expected Total: €" . number_format($totalExpectedTTC, 2) . "\n";
echo "Difference: €" . number_format($difference, 2) . " (exact: €" . number_format($difference, 6) . ")\n\n";

// Show the rounding adjustment needed
if (abs($difference) > 0.001 && abs($difference) < 0.05) {
    $adjustedVAT = $totalCalculatedVAT + $difference;
    echo "ROUNDING ADJUSTMENT:\n";
    echo "===================\n";
    echo "Original VAT: €" . number_format($totalCalculatedVAT, 2) . "\n";
    echo "Adjustment: €" . number_format($difference, 2) . " (€" . number_format($difference, 6) . ")\n";
    echo "Adjusted VAT: €" . number_format($adjustedVAT, 2) . "\n";
    echo "Final Total: €" . number_format($totalExpectedTTC, 2) . " ✓\n";
} else {
    echo "No rounding adjustment needed.\n";
}

echo "\n\nThe JavaScript implementation will:\n";
echo "1. Store expected TTC totals when adding items (€30 and €15)\n";
echo "2. Calculate totals normally\n";
echo "3. Compare expected vs calculated total\n";
echo "4. If difference is small (<0.05), adjust VAT amount\n";
echo "5. Display the exact expected total (€795.00)\n";