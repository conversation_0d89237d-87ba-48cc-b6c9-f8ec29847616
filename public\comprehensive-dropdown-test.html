<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Dropdown Test - Location & Retrocession</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #dc3545, #007bff); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .success { border-left: 5px solid #28a745; background: #f8fff9; padding: 15px; margin: 10px 0; }
        .error { border-left: 5px solid #dc3545; background: #fff5f5; padding: 15px; margin: 10px 0; }
        .warning { border-left: 5px solid #ffc107; background: #fffdf5; padding: 15px; margin: 10px 0; }
        .info { border-left: 5px solid #17a2b8; background: #f0f8ff; padding: 15px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 14px; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .tab { display: inline-block; padding: 10px 20px; margin: 5px; border: none; border-radius: 5px 5px 0 0; cursor: pointer; background: #e9ecef; }
        .tab.active { background: #007bff; color: white; }
        .tab-content { display: none; }
        .tab-content.active { display: block; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Comprehensive Dropdown Test</h1>
            <p>Test both Location and Retrocession invoice dropdown functionality</p>
        </div>

        <div class="card">
            <h2>🔄 Quick Test Links</h2>
            <div class="grid">
                <div>
                    <h3>Real Invoice Pages</h3>
                    <a href="/fit/public/invoices/create?type=location" class="btn btn-success" target="_blank">🏠 Location Invoice</a>
                    <a href="/fit/public/invoices/create?type=retrocession_30" class="btn btn-warning" target="_blank">📊 Retrocession 30%</a>
                    <a href="/fit/public/invoices/create?type=retrocession_25" class="btn btn-info" target="_blank">📈 Retrocession 25%</a>
                </div>
                <div>
                    <h3>Test Tools</h3>
                    <a href="/fit/public/firefox-addon-detector.html" class="btn btn-primary" target="_blank">🔍 Firefox Detector</a>
                    <a href="/fit/public/force-fresh-test.html" class="btn btn-danger" target="_blank">🔄 Force Fresh Test</a>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📋 Tab Navigation</h2>
            <div>
                <button class="tab active" onclick="showTab('location')">🏠 Location Test</button>
                <button class="tab" onclick="showTab('retrocession')">📊 Retrocession Test</button>
                <button class="tab" onclick="showTab('comparison')">🔍 Comparison</button>
                <button class="tab" onclick="showTab('debugging')">🛠️ Debugging</button>
            </div>
        </div>

        <!-- Location Test Tab -->
        <div id="location" class="tab-content active">
            <div class="card">
                <h2>🏠 Location Invoice Test</h2>
                <form id="locationForm">
                    <div class="form-group">
                        <label for="location_invoice_type">Invoice Type:</label>
                        <select id="location_invoice_type" name="invoice_type_id">
                            <option value="">Select Invoice Type</option>
                            <option value="12" selected>Location Invoice (ID: 12)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="location_billable_type">Billable Type:</label>
                        <select id="location_billable_type" name="billable_type">
                            <option value="">Select Type</option>
                            <option value="user" selected>User (Coaches)</option>
                            <option value="client">Client</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="location_billable_id">Coach:</label>
                        <select id="location_billable_id" name="billable_id">
                            <option value="">Select coach...</option>
                        </select>
                    </div>
                </form>
                <div>
                    <button class="btn btn-success" onclick="testLocationPopulation()">👥 Populate Coaches</button>
                    <button class="btn btn-primary" onclick="testLocationAuto()">⚡ Auto Test</button>
                    <button class="btn btn-warning" onclick="clearLocationDropdown()">🧹 Clear</button>
                </div>
                <div id="locationResults"></div>
            </div>
        </div>

        <!-- Retrocession Test Tab -->
        <div id="retrocession" class="tab-content">
            <div class="card">
                <h2>📊 Retrocession Invoice Test</h2>
                <form id="retrocessionForm">
                    <div class="form-group">
                        <label for="retro_invoice_type">Invoice Type:</label>
                        <select id="retro_invoice_type" name="invoice_type_id">
                            <option value="">Select Invoice Type</option>
                            <option value="2" selected>Retrocession 30% (ID: 2)</option>
                            <option value="15">Retrocession 25% (ID: 15)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="retro_billable_type">Billable Type:</label>
                        <select id="retro_billable_type" name="billable_type">
                            <option value="">Select Type</option>
                            <option value="user" selected>User (Practitioners)</option>
                            <option value="client">Client</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="retro_billable_id">Practitioner:</label>
                        <select id="retro_billable_id" name="billable_id">
                            <option value="">Select practitioner...</option>
                        </select>
                    </div>
                </form>
                <div>
                    <button class="btn btn-success" onclick="testRetrocessionPopulation()">👥 Populate Practitioners</button>
                    <button class="btn btn-primary" onclick="testRetrocessionAuto()">⚡ Auto Test</button>
                    <button class="btn btn-warning" onclick="clearRetrocessionDropdown()">🧹 Clear</button>
                </div>
                <div id="retrocessionResults"></div>
            </div>
        </div>

        <!-- Comparison Tab -->
        <div id="comparison" class="tab-content">
            <div class="card">
                <h2>🔍 Side-by-Side Comparison</h2>
                <div class="grid">
                    <div>
                        <h3>Location Invoice</h3>
                        <div class="code">
                            Expected Data: Coaches<br>
                            Source: coachesData array<br>
                            Invoice Type ID: 12<br>
                            Billable Type: user<br>
                            Detection: type=location OR ID=12
                        </div>
                        <div id="locationStatus">Status: Not tested</div>
                    </div>
                    <div>
                        <h3>Retrocession Invoice</h3>
                        <div class="code">
                            Expected Data: Practitioners<br>
                            Source: practitionersData array<br>
                            Invoice Type ID: 2 (30%) or 15 (25%)<br>
                            Billable Type: user<br>
                            Detection: type=retrocession_* OR RET prefix
                        </div>
                        <div id="retrocessionStatus">Status: Not tested</div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="runComparison()">🔍 Run Comparison Test</button>
            </div>
        </div>

        <!-- Debugging Tab -->
        <div id="debugging" class="tab-content">
            <div class="card">
                <h2>🛠️ Debugging Information</h2>
                <div id="debugInfo">
                    <p>Click "Run Full Debug" to see comprehensive information.</p>
                </div>
                <button class="btn btn-primary" onclick="runFullDebug()">🔍 Run Full Debug</button>
                <button class="btn btn-warning" onclick="clearDebugLog()">🧹 Clear Log</button>
            </div>
        </div>

        <div class="card">
            <h2>📝 Debug Log</h2>
            <div id="debugLog" class="code" style="max-height: 300px; overflow-y: auto;">
                <div id="logContent">Debug logs will appear here...</div>
            </div>
        </div>
    </div>

    <script>
        // Mock data (same as from PHP)
        const mockCoachesData = [
            { id: 16, name: 'Isabelle Lamy', username: 'Isabelle', email: '<EMAIL>', course_name: null },
            { id: 8, name: 'Justine Deremiens', username: 'Justine', email: '<EMAIL>', course_name: null },
            { id: 14, name: 'Malaurie Zéler', username: 'Malaurie', email: '<EMAIL>', course_name: null },
            { id: 15, name: 'Nicolas Moineau', username: 'Nicolas', email: '<EMAIL>', course_name: null }
        ];

        const mockPractitionersData = [
            { id: 16, name: 'Isabelle Lamy', username: 'Isabelle', email: '<EMAIL>' },
            { id: 8, name: 'Justine Deremiens', username: 'Justine', email: '<EMAIL>' },
            { id: 14, name: 'Malaurie Zéler', username: 'Malaurie', email: '<EMAIL>' },
            { id: 15, name: 'Nicolas Moineau', username: 'Nicolas', email: '<EMAIL>' }
        ];

        // Debug logging function
        function debugLog(message, data = null) {
            const timestamp = new Date().toISOString();
            const logDiv = document.getElementById('logContent');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = '🔍 [' + timestamp + '] ' + message + (data ? ' | Data: ' + JSON.stringify(data) : '');
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log('🔍 [' + timestamp + '] ' + message, data || '');
        }

        // Show result function
        function showResult(containerId, type, message) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = type;
            resultDiv.innerHTML = '<strong>' + type.toUpperCase() + ':</strong> ' + message;
            container.appendChild(resultDiv);
        }

        // Tab switching
        function showTab(tabName) {
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));
            
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // Location Tests
        function testLocationPopulation() {
            debugLog('=== LOCATION POPULATION TEST START ===');
            
            try {
                const billableSelect = document.getElementById('location_billable_id');
                const billableType = document.getElementById('location_billable_type');
                const invoiceTypeSelect = document.getElementById('location_invoice_type');
                
                // Set proper values
                invoiceTypeSelect.value = '12';
                billableType.value = 'user';
                
                // Clear and populate
                billableSelect.innerHTML = '<option value="">Select coach...</option>';
                
                let addedCount = 0;
                mockCoachesData.forEach(coach => {
                    const courseName = coach.course_name ? ' - ' + coach.course_name : '';
                    const option = new Option(coach.name + ' (' + coach.username + ')' + courseName, 'user_' + coach.id);
                    billableSelect.add(option);
                    addedCount++;
                });
                
                debugLog('Location population completed', { addedCount });
                showResult('locationResults', 'success', 'Location population successful! Added ' + addedCount + ' coaches.');
                document.getElementById('locationStatus').textContent = 'Status: ✅ Working (' + addedCount + ' coaches)';
                
            } catch (error) {
                debugLog('Location population error', { error: error.message });
                showResult('locationResults', 'error', 'Location population failed: ' + error.message);
                document.getElementById('locationStatus').textContent = 'Status: ❌ Error - ' + error.message;
            }
        }

        function testLocationAuto() {
            debugLog('=== LOCATION AUTO TEST START ===');
            // Simulate URL detection
            setTimeout(() => {
                testLocationPopulation();
            }, 100);
        }

        function clearLocationDropdown() {
            document.getElementById('location_billable_id').innerHTML = '<option value="">Select coach...</option>';
            document.getElementById('locationResults').innerHTML = '';
            document.getElementById('locationStatus').textContent = 'Status: Cleared';
        }

        // Retrocession Tests
        function testRetrocessionPopulation() {
            debugLog('=== RETROCESSION POPULATION TEST START ===');
            
            try {
                const billableSelect = document.getElementById('retro_billable_id');
                const billableType = document.getElementById('retro_billable_type');
                const invoiceTypeSelect = document.getElementById('retro_invoice_type');
                
                // Set proper values
                invoiceTypeSelect.value = '2'; // Retrocession 30%
                billableType.value = 'user';
                
                // Clear and populate
                billableSelect.innerHTML = '<option value="">Select practitioner...</option>';
                
                let addedCount = 0;
                mockPractitionersData.forEach(practitioner => {
                    const option = new Option(practitioner.name + ' (' + practitioner.username + ')', 'user_' + practitioner.id);
                    billableSelect.add(option);
                    addedCount++;
                });
                
                debugLog('Retrocession population completed', { addedCount });
                showResult('retrocessionResults', 'success', 'Retrocession population successful! Added ' + addedCount + ' practitioners.');
                document.getElementById('retrocessionStatus').textContent = 'Status: ✅ Working (' + addedCount + ' practitioners)';
                
            } catch (error) {
                debugLog('Retrocession population error', { error: error.message });
                showResult('retrocessionResults', 'error', 'Retrocession population failed: ' + error.message);
                document.getElementById('retrocessionStatus').textContent = 'Status: ❌ Error - ' + error.message;
            }
        }

        function testRetrocessionAuto() {
            debugLog('=== RETROCESSION AUTO TEST START ===');
            // Simulate URL detection
            setTimeout(() => {
                testRetrocessionPopulation();
            }, 100);
        }

        function clearRetrocessionDropdown() {
            document.getElementById('retro_billable_id').innerHTML = '<option value="">Select practitioner...</option>';
            document.getElementById('retrocessionResults').innerHTML = '';
            document.getElementById('retrocessionStatus').textContent = 'Status: Cleared';
        }

        // Comparison test
        function runComparison() {
            debugLog('=== COMPARISON TEST START ===');
            
            // Test both simultaneously
            testLocationPopulation();
            setTimeout(() => {
                testRetrocessionPopulation();
            }, 500);
        }

        // Full debug
        function runFullDebug() {
            const debugInfo = document.getElementById('debugInfo');
            
            const info = {
                mockData: {
                    coaches: mockCoachesData.length,
                    practitioners: mockPractitionersData.length
                },
                browser: {
                    userAgent: navigator.userAgent,
                    language: navigator.language
                },
                page: {
                    url: window.location.href,
                    readyState: document.readyState
                },
                forms: {
                    locationForm: !!document.getElementById('locationForm'),
                    retrocessionForm: !!document.getElementById('retrocessionForm')
                }
            };
            
            debugInfo.innerHTML = '<h3>Full Debug Results</h3><div class="code">' + JSON.stringify(info, null, 2) + '</div>';
            debugLog('Full debug completed', info);
        }

        function clearDebugLog() {
            document.getElementById('logContent').innerHTML = 'Debug logs will appear here...';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('Comprehensive dropdown test initialized');
            debugLog('Mock data loaded', { coaches: mockCoachesData.length, practitioners: mockPractitionersData.length });
        });
    </script>
</body>
</html>