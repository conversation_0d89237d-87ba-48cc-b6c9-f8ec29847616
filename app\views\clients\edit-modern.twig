{% extends "base-modern.twig" %}

{% block title %}{{ __('clients.edit_client') }} - {{ client.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('clients.edit_client') }}</h1>
        <a href="{{ base_url }}/clients/{{ client.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    <form method="POST" action="{{ base_url }}/clients/{{ client.id }}" class="needs-validation" novalidate>
        <input type="hidden" name="_method" value="PUT">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-building me-2"></i>{{ __('clients.company_information') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="name" class="form-label">{{ __('common.name') }} *</label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ client.name }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="client_number" class="form-label">{{ __('clients.client_number') }}</label>
                                <input type="text" class="form-control" id="client_number" name="client_number" 
                                       value="{{ client.client_number }}" readonly>
                                <small class="text-muted">{{ __('clients.cannot_change_number') }}</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="company_type" class="form-label">{{ __('clients.company_type') }}</label>
                                <select class="form-select" id="company_type" name="company_type">
                                    <option value="">{{ __('common.select') }}</option>
                                    <option value="SA" {{ client.company_type == 'SA' ? 'selected' : '' }}>SA</option>
                                    <option value="SARL" {{ client.company_type == 'SARL' ? 'selected' : '' }}>SARL</option>
                                    <option value="SARL-S" {{ client.company_type == 'SARL-S' ? 'selected' : '' }}>SARL-S</option>
                                    <option value="SNC" {{ client.company_type == 'SNC' ? 'selected' : '' }}>SNC</option>
                                    <option value="Individual" {{ client.company_type == 'Individual' ? 'selected' : '' }}>{{ __('clients.individual') }}</option>
                                    <option value="Other" {{ client.company_type == 'Other' ? 'selected' : '' }}>{{ __('common.other') }}</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="registration_number" class="form-label">{{ __('clients.registration_number') }}</label>
                                <input type="text" class="form-control" id="registration_number" name="registration_number" 
                                       value="{{ client.registration_number }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="vat_number" class="form-label">{{ __('clients.vat_number') }}</label>
                                <input type="text" class="form-control" id="vat_number" name="vat_number" 
                                       value="{{ client.vat_number }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-telephone me-2"></i>{{ __('clients.contact_information') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="email" class="form-label">{{ __('common.email') }}</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ client.email }}">
                                <div class="invalid-feedback">
                                    {{ __('validation.email') }}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="phone" class="form-label">{{ __('common.phone') }}</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ client.phone }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="fax" class="form-label">{{ __('clients.fax') }}</label>
                                <input type="tel" class="form-control" id="fax" name="fax" 
                                       value="{{ client.fax }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="website" class="form-label">{{ __('clients.website') }}</label>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="{{ client.website }}" placeholder="https://">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Address -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-geo-alt me-2"></i>{{ __('common.address') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <label for="address_line1" class="form-label">{{ __('common.address') }}</label>
                                <input type="text" class="form-control" id="address_line1" name="address_line1" 
                                       value="{{ client.address_line1 }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="address_line2" class="form-label">{{ __('common.address') }} 2 <small class="text-muted">({{ __('common.optional') }})</small></label>
                                <input type="text" class="form-control" id="address_line2" name="address_line2" 
                                       value="{{ client.address_line2 }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">{{ __('common.postal_code') }}</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                       value="{{ client.postal_code }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="city" class="form-label">{{ __('common.city') }}</label>
                                <input type="text" class="form-control" id="city" name="city" 
                                       value="{{ client.city }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="country" class="form-label">{{ __('common.country') }}</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="Luxembourg" {{ client.country == 'Luxembourg' ? 'selected' : '' }}>Luxembourg</option>
                                    <option value="France" {{ client.country == 'France' ? 'selected' : '' }}>France</option>
                                    <option value="Belgium" {{ client.country == 'Belgium' ? 'selected' : '' }}>Belgique</option>
                                    <option value="Germany" {{ client.country == 'Germany' ? 'selected' : '' }}>Allemagne</option>
                                    <option value="Other" {{ client.country not in ['Luxembourg', 'France', 'Belgium', 'Germany'] ? 'selected' : '' }}>{{ __('common.other') }}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Settings -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="bi bi-gear me-2"></i>{{ __('common.settings') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="payment_terms" class="form-label">{{ __('clients.payment_terms') }}</label>
                            <select class="form-select" id="payment_terms" name="payment_terms">
                                <option value="0" {{ client.payment_terms == 0 ? 'selected' : '' }}>{{ __('clients.due_on_receipt') }}</option>
                                <option value="15" {{ client.payment_terms == 15 ? 'selected' : '' }}>{{ __('clients.net_15') }}</option>
                                <option value="30" {{ client.payment_terms == 30 ? 'selected' : '' }}>{{ __('clients.net_30') }}</option>
                                <option value="45" {{ client.payment_terms == 45 ? 'selected' : '' }}>{{ __('clients.net_45') }}</option>
                                <option value="60" {{ client.payment_terms == 60 ? 'selected' : '' }}>{{ __('clients.net_60') }}</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="discount" class="form-label">{{ __('clients.default_discount') }}</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="discount" name="discount" 
                                       min="0" max="100" step="0.01" value="{{ client.discount|default(0) }}">
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="credit_limit" class="form-label">{{ __('clients.credit_limit') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ currency }}</span>
                                <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                       min="0" step="0.01" value="{{ client.credit_limit }}">
                            </div>
                            <small class="text-muted">{{ __('clients.credit_limit_hint') }}</small>
                        </div>
                        
                        <div class="form-check form-switch">
                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                   value="1" {{ client.is_active ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                {{ __('common.active') }}
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Notes -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-sticky me-2"></i>{{ __('common.notes') }}</h6>
                    </div>
                    <div class="card-body">
                        <textarea class="form-control" id="notes" name="notes" rows="5" 
                                  placeholder="{{ __('clients.internal_notes') }}">{{ client.notes }}</textarea>
                    </div>
                </div>

                <!-- Metadata -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.information') }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-6">{{ __('common.created_at') }}:</dt>
                            <dd class="col-6">{{ client.created_at|date('d/m/Y H:i') }}</dd>
                            
                            <dt class="col-6">{{ __('common.updated_at') }}:</dt>
                            <dd class="col-6">{{ client.updated_at|date('d/m/Y H:i') }}</dd>
                            
                            <dt class="col-6">{{ __('clients.total_invoices') }}:</dt>
                            <dd class="col-6">{{ client.invoice_count|default(0) }}</dd>
                            
                            <dt class="col-6">{{ __('clients.total_revenue') }}:</dt>
                            <dd class="col-6">{{ currency }}{{ client.total_revenue|number_format(2, ',', ' ') }}</dd>
                        </dl>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>{{ __('common.save_changes') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}