/**
 * Enhanced Product Selection for Invoice Creation
 * Handles special "Divers" category products with provider selection
 */

class InvoiceProductSelection {
    constructor() {
        this.searchBtn = document.getElementById('searchProductBtn');
        this.modal = null;
        this.searchModal = null;
        this.providerModal = null;
        this.searchInput = null;
        this.resultsContainer = null;
        this.loadingDiv = null;
        this.emptyDiv = null;
        this.searchTimeout = null;
        
        this.init();
    }
    
    init() {
        // Initialize search modal
        const searchModalEl = document.getElementById('productSearchModal');
        if (searchModalEl) {
            this.searchModal = new bootstrap.Modal(searchModalEl);
            this.searchInput = document.getElementById('productSearchInput');
            this.resultsContainer = document.getElementById('productSearchResults');
            this.loadingDiv = document.getElementById('productSearchLoading');
            this.emptyDiv = document.getElementById('productSearchEmpty');
        }
        
        // Create provider selection modal if it doesn't exist
        this.createProviderModal();
        
        // Setup event listeners
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        // Search button click
        if (this.searchBtn) {
            this.searchBtn.addEventListener('click', () => {
                this.searchModal.show();
                this.searchInput.focus();
            });
        }
        
        // Search input
        if (this.searchInput) {
            this.searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                
                clearTimeout(this.searchTimeout);
                this.resultsContainer.innerHTML = '';
                this.emptyDiv.classList.add('d-none');
                
                if (query.length < 2) {
                    return;
                }
                
                this.searchTimeout = setTimeout(() => this.searchProducts(query), 300);
            });
        }
    }
    
    createProviderModal() {
        // Check if modal already exists
        if (document.getElementById('providerSelectionModal')) {
            this.providerModal = new bootstrap.Modal(document.getElementById('providerSelectionModal'));
            return;
        }
        
        // Create modal HTML
        const modalHTML = `
        <div class="modal fade" id="providerSelectionModal" tabindex="-1" aria-labelledby="providerSelectionModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="providerSelectionModalLabel">Détails du produit</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Ce produit de la catégorie "Divers" nécessite des informations supplémentaires.</p>
                        
                        <div class="form-group mb-3">
                            <label for="providerDescription" class="form-label">Description personnalisée (optionnel):</label>
                            <input type="text" class="form-control" id="providerDescription" 
                                   placeholder="Ex: Prestation de service pour...">
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="providerPrice" class="form-label">Prix unitaire:</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="providerPrice" 
                                       step="0.01" min="0" value="0.00">
                                <span class="input-group-text">€</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-primary" id="confirmProviderBtn">Ajouter à la facture</button>
                    </div>
                </div>
            </div>
        </div>`;
        
        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // Initialize modal
        this.providerModal = new bootstrap.Modal(document.getElementById('providerSelectionModal'));
    }
    
    async searchProducts(query) {
        this.loadingDiv.classList.remove('d-none');
        this.emptyDiv.classList.add('d-none');
        this.resultsContainer.innerHTML = '';
        
        try {
            const response = await fetch(`/fit/public/api/products/search?term=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            this.loadingDiv.classList.add('d-none');
            
            if (!data.success || !data.items || data.items.length === 0) {
                this.emptyDiv.classList.remove('d-none');
                return;
            }
            
            // Check each product's category to see if it's misc
            for (const product of data.items) {
                const item = document.createElement('a');
                item.href = '#';
                item.className = 'list-group-item list-group-item-action';
                
                // We'll need to check if this product's category is misc
                // For now, we'll check based on category name
                const isMisc = product.category && 
                    (product.category.toLowerCase().includes('divers') || 
                     product.category.toLowerCase().includes('misc'));
                
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-0">${product.name} 
                                <small class="text-muted">${product.code}</small>
                                ${isMisc ? '<span class="badge bg-info ms-2">Divers</span>' : ''}
                            </h6>
                            <small class="text-muted">${product.category || ''}</small>
                        </div>
                        <div class="text-end">
                            <strong>${product.price_formatted || product.price + ' €'}</strong>
                            <br>
                            <small class="text-muted">TVA: ${product.vat_rate}%</small>
                            ${product.in_stock === false ? '<br><small class="text-danger">Rupture de stock</small>' : ''}
                        </div>
                    </div>`;
                
                item.addEventListener('click', (e) => {
                    e.preventDefault();
                    
                    // Store product data
                    item.dataset.product = JSON.stringify(product);
                    item.dataset.isMisc = isMisc ? 'true' : 'false';
                    
                    // Add all products directly without modal
                    this.addProductToInvoice(product);
                    this.searchModal.hide();
                });
                
                this.resultsContainer.appendChild(item);
            }
        } catch (error) {
            console.error('Error searching products:', error);
            this.loadingDiv.classList.add('d-none');
            this.emptyDiv.classList.remove('d-none');
        }
    }
    
    showProviderSelection(product) {
        // Store current product
        this.currentProduct = product;
        
        // Hide search modal
        this.searchModal.hide();
        
        // Reset form
        document.getElementById('providerDescription').value = product.name;
        document.getElementById('providerPrice').value = product.price || '0.00';
        
        // Setup confirm button
        const confirmBtn = document.getElementById('confirmProviderBtn');
        confirmBtn.onclick = () => this.confirmProviderSelection();
        
        // Show provider modal
        this.providerModal.show();
    }
    
    
    confirmProviderSelection() {
        const description = document.getElementById('providerDescription').value || this.currentProduct.name;
        const price = document.getElementById('providerPrice').value || '0.00';
        
        // Add product with custom information
        this.addProductToInvoice(this.currentProduct, {
            description: description,
            unit_price: price
        });
        
        // Hide modal
        this.providerModal.hide();
    }
    
    addProductToInvoice(product, providerInfo = null) {
        // Find VAT rate ID from the rate value
        const vatSelects = document.querySelectorAll('.item-vat');
        let vatRateId = '';
        
        if (vatSelects.length > 0) {
            const firstVatSelect = vatSelects[0];
            for (let option of firstVatSelect.options) {
                if (parseFloat(option.dataset.rate) === parseFloat(product.vat_rate)) {
                    vatRateId = option.value;
                    break;
                }
            }
        }
        
        // Prepare item data
        const itemData = {
            description: providerInfo?.description || product.name,
            quantity: '1',
            unit_price: providerInfo?.unit_price || product.price,
            vat_rate_id: vatRateId,
            item_id: product.id,
            code: product.code
        };
        
        // Use the global addItemWithData function if available
        if (typeof window.addItemWithData === 'function') {
            window.addItemWithData(itemData);
        } else {
            console.error('addItemWithData function not found');
            // Fallback: try to add manually
            this.addItemManually(itemData);
        }
    }
    
    addItemManually(itemData) {
        // This is a fallback method if addItemWithData is not available
        const addBtn = document.getElementById('addItemBtn');
        if (addBtn) {
            addBtn.click();
            
            // Try to fill the last added row
            setTimeout(() => {
                const rows = document.querySelectorAll('.invoice-item');
                const lastRow = rows[rows.length - 1];
                
                if (lastRow) {
                    const descInput = lastRow.querySelector('.item-description');
                    const qtyInput = lastRow.querySelector('.item-quantity');
                    const priceInput = lastRow.querySelector('.item-price');
                    const vatSelect = lastRow.querySelector('.item-vat');
                    
                    if (descInput) descInput.value = itemData.description;
                    if (qtyInput) qtyInput.value = itemData.quantity;
                    if (priceInput) {
                        priceInput.value = itemData.unit_price;
                        priceInput.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                    if (vatSelect && itemData.vat_rate_id) {
                        vatSelect.value = itemData.vat_rate_id;
                        vatSelect.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
            }, 100);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if we're on the invoice create page
    if (document.getElementById('productSearchModal')) {
        window.invoiceProductSelection = new InvoiceProductSelection();
    }
});