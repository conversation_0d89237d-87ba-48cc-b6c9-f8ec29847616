<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CREATING COACH USERS ===\n\n";
    
    // Users to create
    $newUsers = [
        [
            'first_name' => 'Malaurie',
            'last_name' => '<PERSON><PERSON><PERSON>',
            'username' => 'malaurie',
            'email' => '<EMAIL>',
            'password' => 'Fit360@2025' // Default password
        ],
        [
            'first_name' => '<PERSON>',
            'last_name' => '<PERSON><PERSON><PERSON>',
            'username' => 'nicolas',
            'email' => '<EMAIL>',
            'password' => 'Fit360@2025' // Default password
        ],
        [
            'first_name' => 'Isabelle',
            'last_name' => 'Lamy',
            'username' => 'isabelle',
            'email' => '<EMAIL>',
            'password' => 'Fit360@2025' // Default password
        ]
    ];
    
    $createdUsers = [];
    
    foreach ($newUsers as $userData) {
        echo "Creating user: {$userData['first_name']} {$userData['last_name']}\n";
        
        // Check if user already exists by email or username
        $stmt = $pdo->prepare("
            SELECT id, username, email 
            FROM users 
            WHERE email = :email OR username = :username
        ");
        $stmt->execute([
            ':email' => $userData['email'],
            ':username' => $userData['username']
        ]);
        $existing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($existing) {
            echo "  ⚠️  User already exists: ";
            if ($existing['email'] == $userData['email']) {
                echo "email '{$existing['email']}'";
            }
            if ($existing['username'] == $userData['username']) {
                echo "username '{$existing['username']}'";
            }
            echo " (ID: {$existing['id']})\n";
            $createdUsers[] = $existing['id'];
            echo "\n";
            continue;
        }
        
        // Check if username needs adjustment
        $baseUsername = $userData['username'];
        $username = $baseUsername;
        $counter = 1;
        
        while (true) {
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = :username");
            $stmt->execute([':username' => $username]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] == 0) {
                break;
            }
            
            $username = $baseUsername . $counter;
            $counter++;
        }
        
        if ($username != $baseUsername) {
            echo "  ℹ️  Username adjusted to: {$username}\n";
        }
        
        // Create user
        $stmt = $pdo->prepare("
            INSERT INTO users (
                username, email, password, first_name, last_name,
                is_active, language, timezone, 
                address, postal_code, city, country,
                created_at, updated_at
            ) VALUES (
                :username, :email, :password, :first_name, :last_name,
                1, 'fr', 'Europe/Luxembourg',
                '15, am Pëtz', 'L-9579', 'Weidingen', 'LU',
                NOW(), NOW()
            )
        ");
        
        $stmt->execute([
            ':username' => $username,
            ':email' => $userData['email'],
            ':password' => password_hash($userData['password'], PASSWORD_BCRYPT),
            ':first_name' => $userData['first_name'],
            ':last_name' => $userData['last_name']
        ]);
        
        $userId = $pdo->lastInsertId();
        $createdUsers[] = $userId;
        
        echo "  ✅ Created successfully (ID: {$userId})\n";
        echo "     Username: {$username}\n";
        echo "     Email: {$userData['email']}\n";
        echo "     Password: {$userData['password']}\n\n";
    }
    
    // Now add all users to Coach group
    echo "\n=== ADDING USERS TO COACH GROUP ===\n\n";
    
    // Find the Coach group
    $stmt = $pdo->prepare("SELECT id, name FROM user_groups WHERE name LIKE '%Coach%' OR name LIKE '%coach%'");
    $stmt->execute();
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($groups) == 0) {
        echo "❌ No Coach group found! Creating one...\n";
        
        // Create Coach group
        $stmt = $pdo->prepare("
            INSERT INTO user_groups (name, description, color, icon, is_active, created_at)
            VALUES ('Coach', 'Groupe des coachs', '#28a745', 'bi bi-person-badge', 1, NOW())
        ");
        $stmt->execute();
        $groupId = $pdo->lastInsertId();
        echo "✅ Created Coach group (ID: {$groupId})\n\n";
    } else {
        $groupId = $groups[0]['id'];
        echo "✓ Found Coach group: {$groups[0]['name']} (ID: {$groupId})\n\n";
    }
    
    // Add users to group
    foreach ($createdUsers as $userId) {
        // Get user info
        $stmt = $pdo->prepare("SELECT username, first_name, last_name FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "Adding {$user['first_name']} {$user['last_name']} to Coach group...\n";
        
        // Check if already in group
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM user_group_members 
            WHERE user_id = :user_id AND group_id = :group_id
        ");
        $stmt->execute([
            ':user_id' => $userId,
            ':group_id' => $groupId
        ]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "  ℹ️  Already in Coach group\n";
        } else {
            // Add to group
            $stmt = $pdo->prepare("
                INSERT INTO user_group_members (user_id, group_id, joined_at) 
                VALUES (:user_id, :group_id, NOW())
            ");
            $stmt->execute([
                ':user_id' => $userId,
                ':group_id' => $groupId
            ]);
            echo "  ✅ Added to Coach group\n";
        }
    }
    
    echo "\n=== SUMMARY ===\n\n";
    echo "All Coach group members:\n";
    
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.first_name, u.last_name, u.email
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = :group_id
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute([':group_id' => $groupId]);
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($members as $member) {
        echo "- {$member['first_name']} {$member['last_name']} ({$member['username']}) - {$member['email']}\n";
    }
    
    echo "\nTotal members in Coach group: " . count($members) . "\n";
    
    echo "\n📝 Note: All new users have been created with the password: Fit360@2025\n";
    echo "   Please ask them to change their password on first login.\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}