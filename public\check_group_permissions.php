<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo "<h3>group_permissions table structure:</h3><pre>";
$stmt = $db->query("SHOW COLUMNS FROM group_permissions");
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    print_r($row);
}
echo "</pre>";

echo "<h3>group_permissions table CREATE statement:</h3><pre>";
$stmt = $db->query("SHOW CREATE TABLE group_permissions");
$result = $stmt->fetch(PDO::FETCH_ASSOC);
echo htmlspecialchars($result['Create Table']);
echo "</pre>";
?>