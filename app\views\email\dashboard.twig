{% extends "layouts/app.twig" %}

{% block title %}Email Management{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <h1 class="h3 mb-4">Email Management Dashboard</h1>
            
            <!-- Stats Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted">Pending</h5>
                            <h2 class="mb-0">{{ stats.queue_by_status.pending|default(0) }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-muted">Processing</h5>
                            <h2 class="mb-0">{{ stats.queue_by_status.processing|default(0) }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-success">Sent</h5>
                            <h2 class="mb-0">{{ stats.queue_by_status.sent|default(0) }}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title text-danger">Failed</h5>
                            <h2 class="mb-0">{{ stats.queue_by_status.failed|default(0) }}</h2>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Tabs -->
            <ul class="nav nav-tabs mb-4" id="emailTabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="queue-tab" data-toggle="tab" href="#queue" role="tab">Email Queue</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="logs-tab" data-toggle="tab" href="#logs" role="tab">Email Logs</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="blacklist-tab" data-toggle="tab" href="#blacklist" role="tab">Blacklist</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="settings-tab" data-toggle="tab" href="#settings" role="tab">Settings</a>
                </li>
            </ul>
            
            <!-- Tab Content -->
            <div class="tab-content" id="emailTabContent">
                <!-- Queue Tab -->
                <div class="tab-pane fade show active" id="queue" role="tabpanel">
                    <div class="d-flex justify-content-between mb-3">
                        <h4>Email Queue</h4>
                        <div>
                            <button class="btn btn-primary" onclick="processQueue()">
                                <i class="fas fa-play"></i> Process Queue
                            </button>
                            <button class="btn btn-secondary" onclick="refreshQueue()">
                                <i class="fas fa-sync"></i> Refresh
                            </button>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped" id="queueTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>To</th>
                                    <th>Subject</th>
                                    <th>Priority</th>
                                    <th>Status</th>
                                    <th>Scheduled</th>
                                    <th>Retries</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Logs Tab -->
                <div class="tab-pane fade" id="logs" role="tabpanel">
                    <div class="d-flex justify-content-between mb-3">
                        <h4>Email Logs</h4>
                        <input type="text" class="form-control w-25" id="logSearch" placeholder="Search emails...">
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped" id="logsTable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Recipient</th>
                                    <th>Subject</th>
                                    <th>Status</th>
                                    <th>Sent At</th>
                                    <th>Message ID</th>
                                    <th>Error</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Blacklist Tab -->
                <div class="tab-pane fade" id="blacklist" role="tabpanel">
                    <div class="d-flex justify-content-between mb-3">
                        <h4>Email Blacklist</h4>
                        <button class="btn btn-primary" onclick="showAddBlacklistModal()">
                            <i class="fas fa-plus"></i> Add Email
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-striped" id="blacklistTable">
                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Reason</th>
                                    <th>Added By</th>
                                    <th>Date</th>
                                    <th>Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Loaded via AJAX -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Settings Tab -->
                <div class="tab-pane fade" id="settings" role="tabpanel">
                    <h4>Email Settings</h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>SMTP Configuration</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Host:</strong> {{ config.MAIL_HOST|default('localhost') }}</p>
                                    <p><strong>Port:</strong> {{ config.MAIL_PORT|default('1025') }}</p>
                                    <p><strong>Encryption:</strong> {{ config.MAIL_ENCRYPTION|default('None') }}</p>
                                    <p><strong>From:</strong> {{ config.MAIL_FROM_ADDRESS|default('<EMAIL>') }}</p>
                                    
                                    <button class="btn btn-primary" onclick="testEmailConfig()">
                                        <i class="fas fa-envelope"></i> Send Test Email
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Queue Configuration</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>Queue Enabled:</strong> 
                                        <span class="badge badge-{{ config.MAIL_QUEUE_ENABLED ? 'success' : 'warning' }}">
                                            {{ config.MAIL_QUEUE_ENABLED ? 'Yes' : 'No' }}
                                        </span>
                                    </p>
                                    <p><strong>Batch Size:</strong> {{ config.MAIL_QUEUE_BATCH_SIZE|default(50) }}</p>
                                    <p><strong>Retry Attempts:</strong> {{ config.MAIL_QUEUE_RETRY_ATTEMPTS|default(3) }}</p>
                                    <p><strong>Rate Limit:</strong> {{ config.MAIL_RATE_LIMIT_PER_MINUTE|default(30) }}/min</p>
                                    
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        To process the queue automatically, set up a cron job:<br>
                                        <code>* * * * * php {{ app_path }}/app/cron/process_email_queue.php</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Blacklist Modal -->
<div class="modal fade" id="addBlacklistModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Email to Blacklist</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="addBlacklistForm">
                    <div class="form-group">
                        <label>Email Address</label>
                        <input type="email" class="form-control" name="email" required>
                    </div>
                    <div class="form-group">
                        <label>Reason</label>
                        <select class="form-control" name="reason" required>
                            <option value="unsubscribed">Unsubscribed</option>
                            <option value="bounced">Bounced</option>
                            <option value="complaint">Complaint</option>
                            <option value="manual">Manual</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Notes</label>
                        <textarea class="form-control" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addToBlacklist()">Add to Blacklist</button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    loadQueue();
    loadLogs();
    loadBlacklist();
    
    // Auto-refresh queue every 30 seconds
    setInterval(loadQueue, 30000);
    
    // Search functionality
    $('#logSearch').on('keyup', debounce(function() {
        loadLogs($(this).val());
    }, 500));
});

function loadQueue() {
    $.get('/api/email/queue', function(response) {
        if (response.success) {
            var tbody = $('#queueTable tbody');
            tbody.empty();
            
            response.data.forEach(function(email) {
                var statusClass = {
                    'pending': 'warning',
                    'processing': 'info',
                    'sent': 'success',
                    'failed': 'danger',
                    'cancelled': 'secondary'
                }[email.status];
                
                var row = `
                    <tr>
                        <td>${email.id}</td>
                        <td>${email.to_email}</td>
                        <td>${email.subject}</td>
                        <td><span class="badge badge-${email.priority === 'high' ? 'danger' : 'secondary'}">${email.priority}</span></td>
                        <td><span class="badge badge-${statusClass}">${email.status}</span></td>
                        <td>${email.scheduled_at || 'Immediate'}</td>
                        <td>${email.retry_count}/${email.max_retries}</td>
                        <td>
                            ${email.status === 'failed' ? '<button class="btn btn-sm btn-primary" onclick="resendEmail(' + email.id + ')">Resend</button>' : ''}
                            ${email.status === 'pending' ? '<button class="btn btn-sm btn-danger" onclick="cancelEmail(' + email.id + ')">Cancel</button>' : ''}
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

function loadLogs(search = '') {
    $.get('/api/email/logs', { search: search }, function(response) {
        if (response.success) {
            var tbody = $('#logsTable tbody');
            tbody.empty();
            
            response.data.forEach(function(log) {
                var statusClass = log.status === 'sent' ? 'success' : 'danger';
                var row = `
                    <tr>
                        <td>${log.id}</td>
                        <td>${log.recipient_email}</td>
                        <td>${log.subject}</td>
                        <td><span class="badge badge-${statusClass}">${log.status}</span></td>
                        <td>${log.sent_at || '-'}</td>
                        <td>${log.message_id || '-'}</td>
                        <td>${log.error_message || '-'}</td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

function loadBlacklist() {
    $.get('/api/email/blacklist', function(response) {
        if (response.success) {
            var tbody = $('#blacklistTable tbody');
            tbody.empty();
            
            response.data.forEach(function(item) {
                var row = `
                    <tr>
                        <td>${item.email}</td>
                        <td>${item.reason}</td>
                        <td>${item.added_by_name || 'System'}</td>
                        <td>${item.created_at}</td>
                        <td>${item.notes || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-danger" onclick="removeFromBlacklist('${item.email}')">Remove</button>
                        </td>
                    </tr>
                `;
                tbody.append(row);
            });
        }
    });
}

function processQueue() {
    if (confirm('Process email queue now?')) {
        $.post('/api/email/queue/process', { limit: 10 }, function(response) {
            if (response.success) {
                showAlert('success', `Processed ${response.results.processed} emails. Sent: ${response.results.sent}, Failed: ${response.results.failed}`);
                loadQueue();
            } else {
                showAlert('danger', response.error);
            }
        });
    }
}

function resendEmail(id) {
    $.post('/api/email/queue/' + id + '/resend', function(response) {
        if (response.success) {
            showAlert('success', 'Email scheduled for resend');
            loadQueue();
        }
    });
}

function cancelEmail(id) {
    if (confirm('Cancel this email?')) {
        $.post('/api/email/queue/' + id + '/cancel', function(response) {
            if (response.success) {
                showAlert('success', 'Email cancelled');
                loadQueue();
            }
        });
    }
}

function showAddBlacklistModal() {
    $('#addBlacklistModal').modal('show');
}

function addToBlacklist() {
    var formData = $('#addBlacklistForm').serialize();
    $.post('/api/email/blacklist', formData, function(response) {
        if (response.success) {
            showAlert('success', 'Email added to blacklist');
            $('#addBlacklistModal').modal('hide');
            $('#addBlacklistForm')[0].reset();
            loadBlacklist();
        } else {
            showAlert('danger', response.error);
        }
    });
}

function removeFromBlacklist(email) {
    if (confirm('Remove ' + email + ' from blacklist?')) {
        $.ajax({
            url: '/api/email/blacklist/' + encodeURIComponent(email),
            type: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showAlert('success', 'Email removed from blacklist');
                    loadBlacklist();
                }
            }
        });
    }
}

function testEmailConfig() {
    var testEmail = prompt('Enter email address for test:', '{{ user.email }}');
    if (testEmail) {
        $.post('/api/email/test', { to: testEmail }, function(response) {
            if (response.success) {
                showAlert('success', 'Test email sent successfully! Check your inbox.');
            } else {
                showAlert('danger', 'Failed to send test email: ' + response.error);
            }
        });
    }
}

function refreshQueue() {
    loadQueue();
    showAlert('info', 'Queue refreshed');
}

function showAlert(type, message) {
    var alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    $('.container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}