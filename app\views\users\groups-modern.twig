{% extends "base-modern.twig" %}

{% block title %}{{ __('users.user_groups') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('users.user_groups') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/users" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('users.back_to_users') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#groupModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('users.create_group') }}
            </button>
        </div>
    </div>

    <!-- Groups Grid -->
    <div class="row g-4">
        {% for group in groups %}
        <div class="col-xl-4 col-lg-6">
            <div class="card shadow-sm h-100">
                <div class="card-header" style="background-color: {{ group.color }}; color: white;">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="{{ group.icon }} me-2"></i>{{ group.name }}
                        </h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown" 
                                    data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                                <i class="bi bi-three-dots-vertical"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li>
                                    <a class="dropdown-item" href="#" onclick="editGroup({{ group.id }})">
                                        <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="{{ base_url }}/users/groups/{{ group.id }}/members">
                                        <i class="bi bi-people me-2"></i>{{ __('users.manage_members') }}
                                    </a>
                                </li>
                                {% if not group.is_system %}
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item text-danger" href="#" onclick="deleteGroup({{ group.id }})">
                                        <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">{{ group.description }}</p>
                    
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="text-muted">{{ __('users.members') }}:</span>
                        <span class="badge bg-primary">{{ group.member_count }} {{ __('users.users') }}</span>
                    </div>
                    
                    {% if group.is_system %}
                    <div class="alert alert-info py-2 mb-3">
                        <i class="bi bi-info-circle me-1"></i>
                        <small>{{ __('users.system_group_notice') }}</small>
                    </div>
                    {% endif %}
                    
                    <h6 class="mb-2">{{ __('users.permissions') }}:</h6>
                    <div class="permissions-list" style="max-height: 150px; overflow-y: auto;">
                        {% if group.permissions %}
                            {% for permission in group.permissions %}
                                <span class="badge bg-light text-dark me-1 mb-1">{{ permission.name }}</span>
                            {% endfor %}
                        {% else %}
                            <p class="text-muted small mb-0">{{ __('users.no_permissions_assigned') }}</p>
                        {% endif %}
                    </div>
                </div>
                <div class="card-footer bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            {{ __('common.created_at') }}: {{ group.created_at|date('d/m/Y') }}
                        </small>
                        <a href="{{ base_url }}/users/groups/{{ group.id }}/members" class="btn btn-sm btn-outline-primary">
                            {{ __('users.view_members') }} <i class="bi bi-arrow-right"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% else %}
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-people fs-1 text-muted mb-3"></i>
                    <p class="text-muted mb-0">{{ __('users.no_groups_found') }}</p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Group Modal -->
<div class="modal fade" id="groupModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/users/groups" id="groupForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="group_id" id="group_id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="groupModalTitle">{{ __('users.create_group') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="group_name" class="form-label">{{ __('common.name') }} *</label>
                            <input type="text" class="form-control" id="group_name" name="name" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="group_color" class="form-label">{{ __('common.color') }} *</label>
                            <input type="color" class="form-control form-control-color" id="group_color" 
                                   name="color" value="#6366f1" required>
                        </div>
                        
                        <div class="col-md-12">
                            <label for="group_icon" class="form-label">{{ __('common.icon') }}</label>
                            <select class="form-select" id="group_icon" name="icon">
                                <option value="bi bi-people">bi bi-people ({{ __('users.default') }})</option>
                                <option value="bi bi-shield-check">bi bi-shield-check</option>
                                <option value="bi bi-person-badge">bi bi-person-badge</option>
                                <option value="bi bi-briefcase">bi bi-briefcase</option>
                                <option value="bi bi-heart">bi bi-heart</option>
                                <option value="bi bi-star">bi bi-star</option>
                                <option value="bi bi-gear">bi bi-gear</option>
                                <option value="bi bi-eye">bi bi-eye</option>
                            </select>
                        </div>
                        
                        <div class="col-md-12">
                            <label for="group_description" class="form-label">{{ __('common.description') }}</label>
                            <textarea class="form-control" id="group_description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="col-md-12">
                            <label class="form-label">{{ __('users.permissions') }}</label>
                            <div class="permissions-container border rounded p-3" style="max-height: 300px; overflow-y: auto;">
                                {% for module, permissions in available_permissions %}
                                <div class="mb-3">
                                    <h6 class="text-muted mb-2">{{ module|capitalize }}</h6>
                                    <div class="row">
                                        {% for permission in permissions %}
                                        <div class="col-md-6 mb-2">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input permission-checkbox" 
                                                       id="perm_{{ permission.id }}" name="permissions[]" 
                                                       value="{{ permission.id }}">
                                                <label class="form-check-label" for="perm_{{ permission.id }}">
                                                    {{ permission.name }}
                                                    <small class="text-muted d-block">{{ permission.description }}</small>
                                                </label>
                                            </div>
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editGroup(id) {
    // Load group data and populate modal
    fetch('{{ base_url }}/users/groups/' + id + '/data')
        .then(response => response.json())
        .then(data => {
            document.getElementById('group_id').value = id;
            document.getElementById('group_name').value = data.name;
            document.getElementById('group_color').value = data.color;
            document.getElementById('group_icon').value = data.icon;
            document.getElementById('group_description').value = data.description;
            
            // Clear all permissions
            document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = false);
            
            // Check assigned permissions
            data.permissions.forEach(permId => {
                const checkbox = document.getElementById('perm_' + permId);
                if (checkbox) checkbox.checked = true;
            });
            
            // Update modal title and form action
            document.getElementById('groupModalTitle').textContent = '{{ __("users.edit_group") }}';
            document.getElementById('groupForm').action = '{{ base_url }}/users/groups/' + id;
            
            // Show modal
            new bootstrap.Modal(document.getElementById('groupModal')).show();
        });
}

function deleteGroup(id) {
    if (confirm('{{ __("users.delete_group_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/users/groups/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Reset modal when closed
document.getElementById('groupModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('groupForm').reset();
    document.getElementById('group_id').value = '';
    document.getElementById('groupModalTitle').textContent = '{{ __("users.create_group") }}';
    document.getElementById('groupForm').action = '{{ base_url }}/users/groups';
    document.querySelectorAll('.permission-checkbox').forEach(cb => cb.checked = false);
});
</script>
{% endblock %}