<?php
/**
 * Fix the location of exclude patient line checkbox
 */

echo "<pre>";
echo "=== Adding Exclude Patient Line Checkbox in Correct Location ===\n\n";

$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($createFile);

// Create backup
$backupFile = $createFile . '.backup.' . date('YmdHis');
file_put_contents($backupFile, $content);
echo "✓ Created backup: " . basename($backupFile) . "\n\n";

// Find the end of the invoice details form fields (after billable selection)
$searchPattern = '                </div>
            </div>
        </div>

        <!-- User/Client Information Display -->';

$insertBefore = strpos($content, $searchPattern);

if ($insertBefore !== false) {
    // Insert the checkbox before this pattern
    $insertHtml = '
                
                <!-- Exclude Patient Line (for retrocession invoices only) -->
                <div class="row g-3 mt-3" id="exclude_patient_line_container" style="display: none;">
                    <div class="col-md-12">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="exclude_patient_line" 
                                   name="exclude_patient_line" value="1">
                            <label class="form-check-label" for="exclude_patient_line">
                                {{ __("retrocession.exclude_patient_line")|default("Exclure la ligne patient") }}
                                <small class="text-muted d-block">{{ __("retrocession.exclude_patient_line_help")|default("Si coché, la facture ne contiendra pas la ligne RÉTROCESSION PATIENTS") }}</small>
                            </label>
                        </div>
                    </div>
                </div>
';
    
    $content = substr($content, 0, $insertBefore) . $insertHtml . substr($content, $insertBefore);
    echo "✓ Added exclude patient line checkbox in the correct location\n";
    
    // Also need to update the JavaScript to use invoice_type_id instead of document_type_id
    $jsSearch = "const typeSelect = document.getElementById('document_type_id');";
    $jsReplace = "const typeSelect = document.getElementById('invoice_type_id');";
    
    $content = str_replace($jsSearch, $jsReplace, $content);
    echo "✓ Fixed JavaScript to use correct invoice type selector\n";
    
    // Save the content
    file_put_contents($createFile, $content);
    
    echo "\n✅ Checkbox added successfully!\n\n";
    echo "The checkbox will now appear:\n";
    echo "1. Below the 'Bill To' selection in the invoice details section\n";
    echo "2. Only when you select a retrocession invoice type (RET, RET25, RET30)\n";
    echo "3. When checked, it will exclude the patient line from the invoice\n";
    
    echo "\nTo see it:\n";
    echo "1. Go to: http://localhost/fit/public/invoices/create\n";
    echo "2. Select Document Type: 'Facture'\n";
    echo "3. Select Invoice Type: 'Rétrocession 25%' or 'Rétrocession 30%'\n";
    echo "4. The checkbox will appear below the billable selection\n";
} else {
    echo "❌ Could not find the correct insertion point\n";
    echo "The form structure may have changed\n";
}

echo "</pre>";