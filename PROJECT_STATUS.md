# FIT360 AdminDesk - Project Status
*Last Updated: 2025-07-08*

## Current State Summary

### Recently Fixed Issues

1. **Invoice Saving** ✅
   - Fixed form submission that wasn't saving invoices
   - Form sends 'items' array but model expected 'lines'
   - Added automatic calculation of subtotal, vat_amount, and total
   - Fixed invoice_type mapping

2. **PDF Generation** ✅
   - Fixed SQL error (changed from invoice_items to invoice_lines table)
   - Fixed total calculation to include VAT
   - Added payment terms display ("Conditions de paiement")
   - Fixed to hide "N° client" for user invoices
   - Added period display for user invoices (e.g., "JUIN 2025")

3. **Invoice Numbering** ⚠️
   - Created reset scripts to fix sequence
   - Target: FAC-2025-0186 for first invoice
   - Still showing FAC-2025-0187 (needs force reset)

### Known Issues

1. **PDF Duplicate Lines**
   - PDF showing "Loyer mensuel" twice instead of showing both invoice lines
   - Created diagnostic scripts to investigate

2. **Invoice Sequence**
   - Run `http://localhost/fit/public/force_reset_sequence.php` to reset to 185

### Diagnostic Scripts Created

1. `/public/check_invoice_187.php` - Check specific invoice data
2. `/public/check_invoice_lines.php` - Check invoice lines
3. `/public/check_duplicate_lines.php` - Check for duplicate lines in database
4. `/public/test_pdf_data.php` - Test PDF data retrieval
5. `/public/force_reset_sequence.php` - Force reset invoice sequence
6. `/public/reset_sequence_to_185.php` - Reset sequence to 185

### Code Changes Made

#### 1. InvoiceController.php
```php
// Added in store() method to process form data:
if (isset($data['items']) && is_array($data['items'])) {
    $subtotal = 0;
    $vatAmount = 0;
    $lines = [];
    
    // Get VAT rates for calculation
    $db = Flight::db();
    $vatRates = [];
    $stmt = $db->query("SELECT id, rate FROM config_vat_rates");
    while ($rate = $stmt->fetch(\PDO::FETCH_ASSOC)) {
        $vatRates[$rate['id']] = $rate['rate'];
    }
    
    foreach ($data['items'] as $item) {
        if (!empty($item['description'])) {
            $quantity = floatval($item['quantity'] ?? 1);
            $unitPrice = floatval($item['unit_price'] ?? 0);
            $lineTotal = $quantity * $unitPrice;
            $subtotal += $lineTotal;
            
            // Calculate VAT for this line
            $vatRateId = $item['vat_rate_id'] ?? null;
            $vatRate = $vatRateId ? ($vatRates[$vatRateId] ?? 0) : 0;
            $lineVat = $lineTotal * ($vatRate / 100);
            $vatAmount += $lineVat;
            
            // Add to lines array
            $lines[] = [
                'description' => $item['description'],
                'quantity' => $quantity,
                'unit_price' => $unitPrice,
                'vat_rate' => $vatRate,
                'vat_rate_id' => $vatRateId,
                'total' => $lineTotal
            ];
        }
    }
    
    // Set calculated values
    $data['lines'] = $lines;
    $data['subtotal'] = $subtotal;
    $data['vat_amount'] = $vatAmount;
    $data['total'] = $subtotal + $vatAmount;
}
```

#### 2. invoice-pdf.php
- Fixed SQL to use invoice_lines instead of invoice_items
- Added period calculation for user invoices
- Hide client number for user invoices
- Added payment terms section
- Fixed total calculation to include VAT

#### 3. DocumentTypeColumnConfig.php
- Changed from 'invoice_items' to 'invoice_lines' in getVisibleInvoiceColumns()

### Database State
- Using database: `fitapp` (not healthcenter_billing)
- Invoice sequence for 2025 should be at 185 (next invoice: 0186)

### Pending Tasks (TODO List)

1. **High Priority**
   - Create package views (index, create, edit, show)
   
2. **Medium Priority**
   - Implement bank reconciliation tables and models
   - Create CashReconciliationController
   - Create ReportController for analytics
   - Implement Practitioner Portal

3. **Low Priority**
   - Create CatalogController for Phase 5
   - Implement catalog search with autocomplete
   - Create price management system

### Next Steps

1. Run `http://localhost/fit/public/force_reset_sequence.php` to fix invoice numbering
2. Run `http://localhost/fit/public/check_duplicate_lines.php` to investigate duplicate lines
3. Test creating a new invoice to verify all fixes are working
4. Continue with Phase 4 and 5 implementations

### Important Notes

- Payment terms default: "Dès réception"
- VAT calculation is now automatic based on selected VAT rates
- Period for user invoices shows current month, for rental invoices shows previous month
- PDF margins and formatting are configurable through database settings