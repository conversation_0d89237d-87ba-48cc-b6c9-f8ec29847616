<?php

return array (
  'active' => 'Active',
  'add_new' => 'Add New',
  'total_patients' => 'Total Patients',
  'add_patient' => 'Add Patient',
  'new_patient_added' => 'New patient added',
  'add_note' => 'Add Note',
  'address' => 'Address',
  'address_line1' => 'Address Line 1',
  'address_line2' => 'Address Line 2',
  'age' => 'Age',
  'allergies' => 'Allergies',
  'appointments' => 'Appointments',
  'basic_information' => 'Basic Information',
  'birth_date' => 'Birth Date',
  'birth_date_required' => 'Birth Date Required',
  'blood_group' => 'Blood Group',
  'blood_type' => 'Blood Type',
  'chronic_conditions' => 'Chronic Conditions',
  'city' => 'City',
  'confirm_delete' => 'Are you sure you want to delete this patient?',
  'contact_information' => 'Contact Information',
  'contact_name' => 'Contact Name',
  'contact_phone' => 'Contact Phone',
  'contact_relationship' => 'Contact Relationship',
  'country' => 'Country',
  'create_patient' => 'Create Patient',
  'created_successfully' => 'Created Successfully',
  'current_medications' => 'Current Medications',
  'date_of_birth' => 'Date of Birth',
  'delete_patient' => 'Delete Patient',
  'deleted_successfully' => 'Deleted Successfully',
  'details' => 'Details',
  'diagnosis' => 'Diagnosis',
  'documents' => 'Documents',
  'edit_patient' => 'Edit Patient',
  'edit_profile' => 'Edit Profile',
  'email' => 'Email',
  'emergency_contact' => 'Emergency Contact',
  'emergency_phone' => 'Emergency Phone',
  'female' => 'Female',
  'first_name' => 'First Name',
  'first_name_required' => 'First Name Required',
  'follow_up' => 'Follow Up',
  'full_name' => 'Full Name',
  'gender' => 'Gender',
  'inactive' => 'Inactive',
  'insurance_information' => 'Insurance Information',
  'insurance_number' => 'Insurance Number',
  'insurance_provider' => 'Insurance Provider',
  'is_active' => 'Is Active',
  'last_name' => 'Last Name',
  'last_name_required' => 'Last Name Required',
  'male' => 'Male',
  'medical_history' => 'Medical History',
  'medical_info' => 'Medical Info',
  'medical_information' => 'Medical Information',
  'medical_records' => 'Medical Records',
  'mobile' => 'Mobile',
  'national_id' => 'National ID',
  'new_this_month' => 'New This Month',
  'no_notes' => 'No Notes',
  'no_visits' => 'No Visits',
  'note_added' => 'Note Added',
  'note_appointment' => 'Appointment Note',
  'note_billing' => 'Billing Note',
  'note_general' => 'General Note',
  'note_medical' => 'Medical Note',
  'note_required' => 'Note Required',
  'notes' => 'Notes',
  'other' => 'Other',
  'patient' => 'Patient',
  'patient_created' => 'Patient created successfully',
  'patient_deleted' => 'Patient deleted successfully',
  'patient_details' => 'Patient Details',
  'patient_list' => 'Patient List',
  'patient_not_found' => 'Patient not found',
  'patient_number' => 'Patient Number',
  'patient_updated' => 'Patient updated successfully',
  'patients' => 'Patients',
  'personal_information' => 'Personal Information',
  'phone' => 'Phone',
  'postal_code' => 'Postal Code',
  'prescriptions' => 'Prescriptions',
  'private_note' => 'Private Note',
  'reason' => 'Reason',
  'relationship' => 'Relationship',
  'relationship_placeholder' => 'e.g., Spouse, Parent, Sibling',
  'search_placeholder' => 'Search...',
  'state' => 'State',
  'title' => 'Patients',
  'total_active' => 'Total Active',
  'treatment' => 'Treatment',
  'updated_successfully' => 'Updated Successfully',
  'view_profile' => 'View Profile',
  'visit_type' => 'Visit Type',
  'visits' => 'Visits',
  'add_patient' => 'Add Patient',
);
