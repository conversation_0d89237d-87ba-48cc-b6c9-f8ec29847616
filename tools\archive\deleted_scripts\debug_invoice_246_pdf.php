<?php
// Debug invoice 246 PDF generation issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Load application bootstrap
require_once __DIR__ . '/../app/config/bootstrap.php';

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background: #007bff; color: white; }
</style>';

echo "<h1>Debug Invoice 246 PDF Generation</h1>";

$invoiceId = 246;

try {
    // Step 1: Clear all caches
    echo '<div class="section">';
    echo '<h2>Step 1: Clearing All Caches</h2>';
    
    $cache = Flight::cache();
    
    // Clear specific invoice cache
    $cacheKey = "invoice_details_{$invoiceId}";
    $cache->forget($cacheKey);
    echo '<p>✓ Cleared cache key: ' . $cacheKey . '</p>';
    
    // Also clear by prefix
    $cache->forgetByPrefix("invoice_{$invoiceId}");
    echo '<p>✓ Cleared all cache entries starting with: invoice_' . $invoiceId . '</p>';
    
    // Clear file-based cache if exists
    $cacheDir = dirname(__DIR__) . '/storage/cache/data/';
    if (is_dir($cacheDir)) {
        $files = glob($cacheDir . '*invoice*246*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                echo '<p>✓ Deleted cache file: ' . basename($file) . '</p>';
            }
        }
    }
    
    echo '</div>';
    
    // Step 2: Get invoice data using the model
    echo '<div class="section">';
    echo '<h2>Step 2: Loading Invoice Data via Model</h2>';
    
    $invoiceModel = new \App\Models\Invoice();
    $invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);
    
    if (!$invoice) {
        echo '<div class="error">Invoice not found!</div>';
        exit;
    }
    
    echo '<p>Invoice Number: <strong>' . $invoice['invoice_number'] . '</strong></p>';
    echo '<p>Lines loaded: <strong>' . count($invoice['lines'] ?? []) . '</strong></p>';
    
    // Show the lines
    if (!empty($invoice['lines'])) {
        echo '<h3>Invoice Lines from Model:</h3>';
        echo '<table>';
        echo '<tr><th>ID</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th></tr>';
        foreach ($invoice['lines'] as $line) {
            echo '<tr>';
            echo '<td>' . $line['id'] . '</td>';
            echo '<td>' . htmlspecialchars($line['description']) . '</td>';
            echo '<td>' . $line['quantity'] . '</td>';
            echo '<td>' . $line['unit_price'] . '</td>';
            echo '<td>' . $line['vat_rate'] . '%</td>';
            echo '</tr>';
        }
        echo '</table>';
    }
    
    echo '</div>';
    
    // Step 3: Debug what the PDF sees
    echo '<div class="section">';
    echo '<h2>Step 3: Simulating PDF Data Processing</h2>';
    
    // This is exactly what the PDF does
    $items = $invoice['lines'] ?? [];
    
    echo '<p>$items array count: <strong>' . count($items) . '</strong></p>';
    
    // Show raw items array
    echo '<h3>Raw $items array:</h3>';
    echo '<pre>' . htmlspecialchars(print_r($items, true)) . '</pre>';
    
    // Calculate total for each item (as PDF does)
    foreach ($items as &$item) {
        if (!isset($item['total'])) {
            $item['total'] = $item['quantity'] * $item['unit_price'];
        }
    }
    
    echo '<h3>After adding totals:</h3>';
    echo '<pre>' . htmlspecialchars(print_r($items, true)) . '</pre>';
    
    echo '</div>';
    
    // Step 4: Check for any cache contamination
    echo '<div class="section">';
    echo '<h2>Step 4: Re-checking Cache After Load</h2>';
    
    // Get from cache again to see if it was re-cached
    $cachedData = $cache->get($cacheKey);
    if ($cachedData !== null) {
        echo '<div class="warning">⚠️ Data was re-cached after loading!</div>';
        if (isset($cachedData['lines'])) {
            echo '<p>Cached lines count: <strong>' . count($cachedData['lines']) . '</strong></p>';
            echo '<h3>Cached lines:</h3>';
            foreach ($cachedData['lines'] as $line) {
                echo '<p>- ' . htmlspecialchars($line['description']) . ' (ID: ' . $line['id'] . ')</p>';
            }
        }
    } else {
        echo '<p style="color: green;">✓ No cached data found (good!)</p>';
    }
    
    echo '</div>';
    
    // Step 5: Add debug output to PDF
    echo '<div class="section">';
    echo '<h2>Step 5: Adding Debug to PDF Generation</h2>';
    
    // Create a modified PDF URL with debug flag
    $debugPdfUrl = '/fit/public/invoice-pdf.php?id=' . $invoiceId . '&debug=1';
    
    echo '<p>I will now create a debug version of the PDF that logs the exact data being rendered.</p>';
    echo '<p><a href="' . $debugPdfUrl . '" target="_blank" class="button">View Debug PDF</a></p>';
    
    echo '</div>';
    
    // Step 6: Direct database check
    echo '<div class="section">';
    echo '<h2>Step 6: Direct Database Query</h2>';
    
    $db = Flight::db();
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order, id
    ");
    $stmt->execute([$invoiceId]);
    $dbLines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo '<p>Direct DB query returned: <strong>' . count($dbLines) . ' lines</strong></p>';
    echo '<pre>' . htmlspecialchars(print_r($dbLines, true)) . '</pre>';
    
    echo '</div>';
    
    // Summary
    echo '<div class="section">';
    echo '<h2>Summary</h2>';
    
    $modelLineCount = count($invoice['lines'] ?? []);
    $dbLineCount = count($dbLines);
    
    if ($modelLineCount !== $dbLineCount) {
        echo '<div class="error">⚠️ Mismatch: Model returned ' . $modelLineCount . ' lines, but DB has ' . $dbLineCount . ' lines!</div>';
    } else {
        echo '<div class="success">✓ Model and DB both return ' . $modelLineCount . ' lines</div>';
    }
    
    // Check for duplicates in model data
    $descriptions = [];
    $hasDuplicates = false;
    foreach ($invoice['lines'] ?? [] as $line) {
        if (isset($descriptions[$line['description']])) {
            echo '<div class="error">⚠️ Duplicate found in model data: "' . $line['description'] . '"</div>';
            $hasDuplicates = true;
        }
        $descriptions[$line['description']] = true;
    }
    
    if (!$hasDuplicates) {
        echo '<div class="success">✓ No duplicates found in model data</div>';
    }
    
    echo '</div>';
    
    // Action buttons
    echo '<div class="section">';
    echo '<h2>Actions</h2>';
    echo '<p><a href="/fit/public/invoices/246" target="_blank">View Invoice Web Page</a></p>';
    echo '<p><a href="/fit/public/invoice-pdf.php?id=246" target="_blank">View Normal PDF</a></p>';
    echo '<p><a href="/fit/public/clear_invoice_cache.php?invoice_id=246">Clear Invoice Cache</a></p>';
    echo '</div>';
    
} catch (Exception $e) {
    echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
    echo '<pre>' . $e->getTraceAsString() . '</pre>';
}
?>