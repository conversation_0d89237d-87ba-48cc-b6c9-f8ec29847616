<?php
/**
 * Verify and display the exact syntax around line 3638
 */

$file = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$lines = file($file, FILE_IGNORE_NEW_LINES);

echo "<pre style='font-family: monospace; background: #f8f9fa; padding: 20px;'>";
echo "<h2>Checking Syntax Around Line 3638</h2>\n\n";

// Display lines 3630-3645 with syntax highlighting
for ($i = 3629; $i < 3645 && $i < count($lines); $i++) {
    $lineNum = $i + 1;
    $line = $lines[$i];
    
    // Highlight line 3638
    if ($lineNum == 3638) {
        echo "<span style='background: yellow; font-weight: bold;'>";
    }
    
    echo sprintf("%4d: ", $lineNum);
    
    // Color code different elements
    $highlighted = htmlspecialchars($line);
    $highlighted = preg_replace('/(\{\{[^}]+\}\})/', '<span style="color: red; font-weight: bold;">$1</span>', $highlighted);
    $highlighted = preg_replace('/(`[^`]*`)/', '<span style="color: green;">$1</span>', $highlighted);
    $highlighted = preg_replace('/(\/\/.*)$/', '<span style="color: #666;">$1</span>', $highlighted);
    
    echo $highlighted;
    
    if ($lineNum == 3638) {
        echo "</span>";
    }
    
    echo "\n";
}

echo "\n<h3>Checking for Syntax Issues:</h3>\n";

// Check parentheses balance in this section
$section = array_slice($lines, 3620, 30);
$parens = 0;
$braces = 0;
$brackets = 0;

foreach ($section as $i => $line) {
    $parens += substr_count($line, '(') - substr_count($line, ')');
    $braces += substr_count($line, '{') - substr_count($line, '}');
    $brackets += substr_count($line, '[') - substr_count($line, ']');
    
    if (preg_match('/\{\{.*\}\}/', $line)) {
        echo "Line " . (3621 + $i) . " contains Twig expression: " . htmlspecialchars($line) . "\n";
    }
}

echo "\nBalance check:\n";
echo "Parentheses: $parens " . ($parens == 0 ? "✅" : "❌") . "\n";
echo "Braces: $braces " . ($braces == 0 ? "✅" : "❌") . "\n";
echo "Brackets: $brackets " . ($brackets == 0 ? "✅" : "❌") . "\n";

// Check the actual JavaScript syntax by extracting the function
echo "\n<h3>Extracting the forEach block:</h3>\n";

$forEachStart = null;
$forEachEnd = null;

for ($i = 3625; $i < 3645; $i++) {
    if (strpos($lines[$i], 'vatRates.forEach') !== false) {
        $forEachStart = $i;
    }
    if ($forEachStart !== null && strpos($lines[$i], '});') !== false && $forEachEnd === null) {
        $forEachEnd = $i;
    }
}

if ($forEachStart !== null && $forEachEnd !== null) {
    echo "forEach block found from line " . ($forEachStart + 1) . " to " . ($forEachEnd + 1) . "\n\n";
    
    for ($i = $forEachStart; $i <= $forEachEnd; $i++) {
        echo ($i + 1) . ": " . htmlspecialchars($lines[$i]) . "\n";
    }
}

echo "\n<h3>Solution:</h3>\n";
echo "The syntax appears correct in the source file.\n";
echo "The error is caused by browser caching an old version.\n\n";

echo "To fix this:\n";
echo "1. Visit: http://localhost/fit/public/aggressive_cache_clear.php\n";
echo "2. Follow the instructions there\n";
echo "3. The cache-busting mechanism will force a reload\n";

echo "</pre>";

// Show the actual rendered JavaScript
echo "<h3>Testing JavaScript Syntax:</h3>";
echo "<script>
try {
    // Test the exact code that's causing issues
    const vatRates = [{id: 1, name: 'Standard', rate: 17, is_default: true}];
    const itemIndex = 0;
    const td = document.createElement('td');
    
    const vatSelect = document.createElement('select');
    vatSelect.className = 'form-select form-select-sm item-vat';
    vatSelect.name = `items[\${itemIndex}][vat_rate_id]`;
    vatSelect.required = true;
    
    vatRates.forEach(vat => {
        const option = document.createElement('option');
        option.value = vat.id;
        option.setAttribute('data-rate', vat.rate);
        option.textContent = `\${vat.name} (\${vat.rate}%)`;
        if (vat.is_default) {
            option.selected = true;
        }
        vatSelect.appendChild(option);
    });
    
    td.appendChild(vatSelect);
    
    document.write('<div style=\"background: #d4edda; padding: 20px; margin: 20px; border-radius: 5px;\">');
    document.write('<strong>✅ JavaScript syntax is valid!</strong><br>');
    document.write('The code executes without errors. The issue is definitely browser caching.</div>');
} catch (e) {
    document.write('<div style=\"background: #f8d7da; padding: 20px; margin: 20px; border-radius: 5px;\">');
    document.write('<strong>❌ JavaScript Error:</strong> ' + e.message + '</div>');
}
</script>";
?>