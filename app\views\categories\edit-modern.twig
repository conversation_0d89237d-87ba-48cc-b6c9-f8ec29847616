{% extends "base-modern.twig" %}

{% block title %}{{ __('categories.edit') | default('Edit Category') }}{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products">{{ __('products.title') | default('Products') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products/categories">{{ __('categories.title') | default('Categories') }}</a></li>
    <li class="breadcrumb-item active">{{ __('common.edit') | default('Edit') }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <form method="POST" action="{{ base_url }}/products/categories/{{ category.id }}">
                <input type="hidden" name="_method" value="PUT">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('categories.edit') | default('Edit Category') }}: {{ category.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    {{ __('categories.name') | default('Category Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control {% if errors.name is defined %}is-invalid{% endif %}" 
                                       id="name" name="name" value="{{ old.name | default(category.name) }}" required>
                                {% if errors.name is defined %}
                                    <div class="invalid-feedback">{{ errors.name }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">
                                    {{ __('categories.sort_order') | default('Sort Order') }}
                                </label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="{{ old.sort_order | default(category.sort_order) }}" min="0">
                                <small class="text-muted">{{ __('categories.sort_order_help') | default('Leave empty for automatic ordering') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            {{ __('categories.description') | default('Description') }}
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ old.description | default(category.description) }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icon" class="form-label">
                                    {{ __('categories.icon') | default('Icon') }}
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="icon" name="icon" 
                                           value="{{ old.icon | default(category.icon) }}" placeholder="fas fa-box">
                                    <span class="input-group-text" id="icon-preview">
                                        <i class="{{ old.icon | default(category.icon) | default('fas fa-box') }}"></i>
                                    </span>
                                </div>
                                <small class="text-muted">
                                    {{ __('categories.icon_help') | default('FontAwesome icon class') }}
                                    <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a>
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">
                                    {{ __('categories.color') | default('Color') }}
                                </label>
                                <input type="color" class="form-control form-control-color" id="color" 
                                       name="color" value="{{ old.color | default(category.color) | default('#007bff') }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {% if old.is_active is defined ? old.is_active : category.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                {{ __('categories.is_active') | default('Active') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{{ __('common.save_changes') | default('Save Changes') }}
                    </button>
                    <a href="{{ base_url }}/products/categories" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>{{ __('common.cancel') | default('Cancel') }}
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">{{ __('common.info') | default('Information') }}</h5>
            </div>
            <div class="card-body">
                <dl class="row mb-0">
                    <dt class="col-sm-6">{{ __('common.created_at') | default('Created') }}:</dt>
                    <dd class="col-sm-6">{{ category.created_at|date('d/m/Y H:i') }}</dd>
                    
                    <dt class="col-sm-6">{{ __('common.updated_at') | default('Updated') }}:</dt>
                    <dd class="col-sm-6">{{ category.updated_at|date('d/m/Y H:i') }}</dd>
                    
                    <dt class="col-sm-6">{{ __('categories.products_count') | default('Products') }}:</dt>
                    <dd class="col-sm-6">
                        <span class="badge bg-secondary">{{ category.products_count | default(0) }}</span>
                    </dd>
                </dl>
            </div>
        </div>
    </div>
</div>

<script>
// Icon preview
document.getElementById('icon').addEventListener('input', function() {
    const preview = document.getElementById('icon-preview');
    preview.innerHTML = `<i class="${this.value || 'fas fa-box'}"></i>`;
});
</script>
{% endblock %}