<?php
/**
 * Add missing translations for retrocession options
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment variables FIRST
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Now require bootstrap which will use the env vars
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

echo "<pre>";
echo "=== Adding Retrocession Options Translations ===\n\n";

try {
    $db = Flight::db();
    
    // French translations
    $frenchTranslations = [
        'users.retrocession_options' => 'Options de rétrocession',
        'users.exclude_patient_line_default' => 'Exclure la ligne patient par défaut dans les factures de rétrocession',
        'users.exclude_patient_line_default_help' => 'Si coché, la ligne patient sera exclue par défaut lors de la création de factures de rétrocession. Vous pourrez toujours modifier ce choix pour chaque facture individuellement.'
    ];
    
    // English translations
    $englishTranslations = [
        'users.retrocession_options' => 'Retrocession Options',
        'users.exclude_patient_line_default' => 'Exclude patient line by default in retrocession invoices',
        'users.exclude_patient_line_default_help' => 'If checked, the patient line will be excluded by default when creating retrocession invoices. You can still change this choice for each invoice individually.'
    ];
    
    // Add to language files
    $frFile = dirname(__DIR__) . '/app/lang/fr/users.php';
    $enFile = dirname(__DIR__) . '/app/lang/en/users.php';
    
    // Update French file
    if (file_exists($frFile)) {
        $content = file_get_contents($frFile);
        
        // Find the last closing bracket
        $lastBracket = strrpos($content, '];');
        
        if ($lastBracket !== false) {
            $additions = "\n    // Retrocession options\n";
            foreach ($frenchTranslations as $key => $value) {
                $shortKey = str_replace('users.', '', $key);
                $additions .= "    '$shortKey' => '$value',\n";
            }
            
            $newContent = substr($content, 0, $lastBracket) . $additions . substr($content, $lastBracket);
            file_put_contents($frFile, $newContent);
            echo "✅ Updated French translations\n";
        }
    }
    
    // Update English file
    if (file_exists($enFile)) {
        $content = file_get_contents($enFile);
        
        // Find the last closing bracket
        $lastBracket = strrpos($content, '];');
        
        if ($lastBracket !== false) {
            $additions = "\n    // Retrocession options\n";
            foreach ($englishTranslations as $key => $value) {
                $shortKey = str_replace('users.', '', $key);
                $additions .= "    '$shortKey' => '$value',\n";
            }
            
            $newContent = substr($content, 0, $lastBracket) . $additions . substr($content, $lastBracket);
            file_put_contents($enFile, $newContent);
            echo "✅ Updated English translations\n";
        }
    }
    
    echo "\n✅ Translations added successfully!\n";
    echo "\nNow refresh the page to see the proper text.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";