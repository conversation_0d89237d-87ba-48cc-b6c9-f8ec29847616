# Currency and Percentage Formatting Update

## Summary
Updated the entire application to add spaces before currency (€) and percentage (%) symbols for better readability.

### Changes Made:

1. **Created FormatHelper Class** (`/app/helpers/FormatHelper.php`)
   - `formatCurrency()` - Formats numbers with space before €
   - `formatPercentage()` - Formats numbers with space before %
   - `formatNumber()` - Formats numbers without symbols

2. **Updated Invoice PDF Generation** (`/public/invoice-pdf.php`)
   - 6 currency format updates (€)
   - 2 percentage format updates (%)

3. **Updated Twig Templates** (20+ files)
   - Dashboard, invoices, products, users, retrocession modules
   - All instances of `}}€` changed to `}} €`
   - All instances of `}}%` changed to `}} %`

4. **Updated PdfService** (`/app/services/PdfService.php`)
   - 10 currency format updates
   - 1 percentage format update

### Format Examples:
- **Before**: `160,00€` and `15%`
- **After**: `160,00 €` and `15 %`

### Testing:
To verify the changes:
1. Generate any invoice PDF
2. Check invoice listings
3. View product prices
4. Check VAT rate displays

All currency and percentage values should now have proper spacing throughout the application.