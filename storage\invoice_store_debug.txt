2025-07-15 10:30:06 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-15 12:53:12 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-15 13:39:02 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 12:31:18 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 12:52:03 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 12:53:10 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 13:25:40 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 13:30:26 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 13:30:26 - Processing billable_id: user_16
2025-07-16 13:30:26 - Billable type: user, ID: 16
2025-07-16 13:30:26 - After conversion - client_id: null, user_id: 16
2025-07-16 13:56:03 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 13:56:03 - Processing billable_id: user_16
2025-07-16 13:56:03 - Billable type: user, ID: 16
2025-07-16 13:56:03 - After conversion - client_id: null, user_id: 16
2025-07-16 13:56:03 - Converted issue_date to: 2025-07-16
2025-07-16 13:57:59 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 13:57:59 - Processing billable_id: user_16
2025-07-16 13:57:59 - Billable type: user, ID: 16
2025-07-16 13:57:59 - After conversion - client_id: null, user_id: 16
2025-07-16 13:57:59 - Converted issue_date to: 2025-07-16
2025-07-16 13:57:59 - Starting to process items
2025-07-16 13:57:59 - Processing 1 items
2025-07-16 13:57:59 - EXCEPTION in store method:
Message: strpos(): Passing null to parameter #1 ($haystack) of type string is deprecated
File: D:\wamp64\www\fit\app\controllers\InvoiceController.php
Line: 631
Trace:
#0 [internal function]: flight\Engine->handleError(8192, 'strpos(): Passi...', 'D:\\wamp64\\www\\f...', 631)
#1 D:\wamp64\www\fit\app\controllers\InvoiceController.php(631): strpos(NULL, 'LOC')
#2 [internal function]: App\Controllers\InvoiceController->store()
#3 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(378): call_user_func_array(Array, Array)
#4 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(289): flight\core\Dispatcher->invokeCallable(Array, Array)
#5 D:\wamp64\www\fit\vendor\mikecao\flight\flight\Engine.php(604): flight\core\Dispatcher->execute(Array, Array)
#6 [internal function]: flight\Engine->_start()
#7 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(378): call_user_func_array(Array, Array)
#8 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(289): flight\core\Dispatcher->invokeCallable(Array, Array)
#9 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(133): flight\core\Dispatcher->execute(Array, Array)
#10 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(97): flight\core\Dispatcher->runEvent('start', Array)
#11 D:\wamp64\www\fit\vendor\mikecao\flight\flight\Engine.php(153): flight\core\Dispatcher->run('start', Array)
#12 D:\wamp64\www\fit\vendor\mikecao\flight\flight\Flight.php(138): flight\Engine->__call('start', Array)
#13 D:\wamp64\www\fit\public\index.php(41): Flight::__callStatic('start', Array)
#14 {main}

2025-07-16 13:59:26 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 13:59:26 - Processing billable_id: user_16
2025-07-16 13:59:26 - Billable type: user, ID: 16
2025-07-16 13:59:26 - After conversion - client_id: null, user_id: 16
2025-07-16 13:59:26 - Converted issue_date to: 2025-07-16
2025-07-16 13:59:26 - Starting to process items
2025-07-16 13:59:26 - Processing 1 items
2025-07-16 13:59:26 - Creating invoice with data:
{
    "csrf_token": "584c3834baca68d7415f8610a11602d4df3d17a240a40024922f98f10c90167a",
    "action": "save",
    "document_type_id": "1",
    "invoice_type_id": "12",
    "template_id": "",
    "invoice_number": "FAC-LOC-2025-0197",
    "issue_date_display": "16\/07\/2025",
    "subject": "LOCATION SALLE",
    "period": "JUIN 2025",
    "billable_type": "user",
    "billable_id": "user_16",
    "items": [
        {
            "item_id": "",
            "description": "Pilates",
            "quantity": "3",
            "unit_price": "25.00",
            "vat_rate_id": "35"
        }
    ],
    "notes": "",
    "internal_notes": "",
    "payment_term_id": "1",
    "due_date_display": "",
    "issue_date": "2025-07-16",
    "due_date": "",
    "user_id": 16,
    "client_id": null,
    "status": "draft",
    "lines": [
        {
            "description": "Pilates",
            "quantity": 3,
            "unit_price": 25,
            "vat_rate": "0.00",
            "vat_rate_id": "35",
            "total": 75,
            "sort_order": 0
        }
    ],
    "subtotal": 75,
    "vat_amount": 0,
    "total": 75,
    "invoice_type": "LOC",
    "type_id": "12"
}

2025-07-16 13:59:26 - Invoice model createInvoice called
2025-07-16 13:59:26 - Executing INSERT with params:
{
    ":invoice_number": "FAC-LOC-2025-0197",
    ":document_type_id": "1",
    ":type_id": "12",
    ":invoice_type": "LOC",
    ":template_id": "",
    ":profile_id": null,
    ":client_id": null,
    ":user_id": 16,
    ":status": "draft",
    ":issue_date": "2025-07-16",
    ":due_date": "2025-08-15",
    ":subtotal": 75,
    ":vat_amount": 0,
    ":cns_base_amount": 0,
    ":secretariat_vat_amount": 0,
    ":total": 75,
    ":original_amount": null,
    ":staff_limit_applied": false,
    ":staff_limit_amount": null,
    ":secretariat_vat_note_shown": false,
    ":cns_reference": null,
    ":draft_until": "2025-07-18 13:59:26",
    ":currency": "EUR",
    ":notes": "",
    ":internal_notes": "",
    ":payment_terms": "D\u00e8s r\u00e9ception",
    ":reference_document_id": null,
    ":reference_document_number": null,
    ":credit_reason": null,
    ":created_by": 1,
    ":is_archived": 0,
    ":subject": "LOCATION SALLE",
    ":period": "JUIN 2025"
}

2025-07-16 13:59:26 - Invoice inserted successfully with ID: 264

2025-07-16 13:59:26 - Invoice created successfully: ID 264

2025-07-16 14:41:45 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 14:41:45 - Processing billable_id: user_14
2025-07-16 14:41:45 - Billable type: user, ID: 14
2025-07-16 14:41:45 - After conversion - client_id: null, user_id: 14
2025-07-16 14:41:45 - Converted issue_date to: 2025-07-16
2025-07-16 14:41:45 - Starting to process items
2025-07-16 14:41:45 - Processing 1 items
2025-07-16 14:41:45 - Creating invoice with data:
{
    "csrf_token": "584c3834baca68d7415f8610a11602d4df3d17a240a40024922f98f10c90167a",
    "action": "save",
    "document_type_id": "1",
    "invoice_type_id": "12",
    "template_id": "",
    "invoice_number": "FAC-LOC-2025-0198",
    "issue_date_display": "16\/07\/2025",
    "subject": "LOCATION SALLE",
    "period": "JUIN 2025",
    "billable_type": "user",
    "billable_id": "user_14",
    "items": [
        {
            "item_id": "",
            "description": "Yoga",
            "quantity": "10",
            "unit_price": "30.00",
            "vat_rate_id": "35"
        }
    ],
    "notes": "",
    "internal_notes": "",
    "payment_term_id": "1",
    "due_date_display": "",
    "issue_date": "2025-07-16",
    "due_date": "",
    "user_id": 14,
    "client_id": null,
    "status": "draft",
    "lines": [
        {
            "description": "Yoga",
            "quantity": 10,
            "unit_price": 30,
            "vat_rate": "0.00",
            "vat_rate_id": "35",
            "total": 300,
            "sort_order": 0
        }
    ],
    "subtotal": 300,
    "vat_amount": 0,
    "total": 300,
    "invoice_type": "LOC",
    "type_id": "12"
}

2025-07-16 14:41:45 - Invoice model createInvoice called
2025-07-16 14:41:45 - Executing INSERT with params:
{
    ":invoice_number": "FAC-LOC-2025-0198",
    ":document_type_id": "1",
    ":type_id": "12",
    ":invoice_type": "LOC",
    ":template_id": "",
    ":profile_id": null,
    ":client_id": null,
    ":user_id": 14,
    ":status": "draft",
    ":issue_date": "2025-07-16",
    ":due_date": "2025-08-15",
    ":subtotal": 300,
    ":vat_amount": 0,
    ":cns_base_amount": 0,
    ":secretariat_vat_amount": 0,
    ":total": 300,
    ":original_amount": null,
    ":staff_limit_applied": false,
    ":staff_limit_amount": null,
    ":secretariat_vat_note_shown": false,
    ":cns_reference": null,
    ":draft_until": "2025-07-18 14:41:45",
    ":currency": "EUR",
    ":notes": "",
    ":internal_notes": "",
    ":payment_terms": "D\u00e8s r\u00e9ception",
    ":reference_document_id": null,
    ":reference_document_number": null,
    ":credit_reason": null,
    ":created_by": 1,
    ":is_archived": 0,
    ":subject": "LOCATION SALLE",
    ":period": "JUIN 2025"
}

2025-07-16 14:41:45 - Invoice inserted successfully with ID: 265

2025-07-16 14:41:45 - Invoice created successfully: ID 265

2025-07-16 15:17:33 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 15:17:33 - Processing billable_id: user_18
2025-07-16 15:17:33 - Billable type: user, ID: 18
2025-07-16 15:17:33 - After conversion - client_id: null, user_id: 18
2025-07-16 15:17:33 - Converted issue_date to: 2025-07-16
2025-07-16 15:17:33 - Starting to process items
2025-07-16 15:17:33 - Processing 3 items
2025-07-16 15:17:33 - Creating invoice with data:
{
    "csrf_token": "584c3834baca68d7415f8610a11602d4df3d17a240a40024922f98f10c90167a",
    "action": "save",
    "document_type_id": "1",
    "invoice_type_id": "15",
    "template_id": "",
    "invoice_number": "FAC-RET25-2025-0198",
    "issue_date_display": "16\/07\/2025",
    "subject": "R\u00c9TROCESSION",
    "period": "JUIN 2025",
    "billable_type": "user",
    "billable_id": "user_18",
    "items": [
        {
            "quantity": "1",
            "item_id": "",
            "description": "R\u00c9TROCESSION CNS 20%",
            "unit_price": "1315.18",
            "vat_rate_id": "35"
        },
        {
            "quantity": "1",
            "item_id": "",
            "description": "R\u00c9TROCESSION PATIENTS 20%",
            "unit_price": "0.00",
            "vat_rate_id": "35"
        },
        {
            "quantity": "1",
            "item_id": "",
            "description": "FRAIS SECR\u00c9TARIAT ET MISE \u00c0 DISPOSITION MAT\u00c9RIEL 5%",
            "unit_price": "281.02",
            "vat_rate_id": "14"
        }
    ],
    "notes": "",
    "internal_notes": "",
    "payment_term_id": "1",
    "due_date_display": "",
    "issue_date": "2025-07-16",
    "due_date": "",
    "user_id": 18,
    "client_id": null,
    "status": "draft",
    "lines": [
        {
            "description": "R\u00c9TROCESSION CNS 20%",
            "quantity": 1,
            "unit_price": 1315.18,
            "vat_rate": "0.00",
            "vat_rate_id": "35",
            "total": 1315.18,
            "sort_order": 0
        },
        {
            "description": "R\u00c9TROCESSION PATIENTS 20%",
            "quantity": 1,
            "unit_price": 0,
            "vat_rate": "0.00",
            "vat_rate_id": "35",
            "total": 0,
            "sort_order": 1
        },
        {
            "description": "FRAIS SECR\u00c9TARIAT ET MISE \u00c0 DISPOSITION MAT\u00c9RIEL 5%",
            "quantity": 1,
            "unit_price": 281.02,
            "vat_rate": "17.00",
            "vat_rate_id": "14",
            "total": 281.02,
            "sort_order": 2
        }
    ],
    "subtotal": 1596.2,
    "vat_amount": 47.7734,
    "total": 1643.9734,
    "invoice_type": "retrocession_25",
    "type_id": "15"
}

2025-07-16 15:17:33 - Invoice model createInvoice called
2025-07-16 15:17:33 - Executing INSERT with params:
{
    ":invoice_number": "FAC-RET25-2025-0198",
    ":document_type_id": "1",
    ":type_id": "15",
    ":invoice_type": "retrocession_25",
    ":template_id": "",
    ":profile_id": null,
    ":client_id": null,
    ":user_id": 18,
    ":status": "draft",
    ":issue_date": "2025-07-16",
    ":due_date": "2025-08-15",
    ":subtotal": 1596.2,
    ":vat_amount": 47.77,
    ":cns_base_amount": 0,
    ":secretariat_vat_amount": 0,
    ":total": 1643.97,
    ":original_amount": null,
    ":staff_limit_applied": false,
    ":staff_limit_amount": null,
    ":secretariat_vat_note_shown": false,
    ":cns_reference": null,
    ":draft_until": "2025-07-18 15:17:33",
    ":currency": "EUR",
    ":notes": "",
    ":internal_notes": "",
    ":payment_terms": "D\u00e8s r\u00e9ception",
    ":reference_document_id": null,
    ":reference_document_number": null,
    ":credit_reason": null,
    ":created_by": 1,
    ":is_archived": 0,
    ":subject": "R\u00c9TROCESSION",
    ":period": "JUIN 2025"
}

2025-07-16 15:17:33 - Invoice inserted successfully with ID: 266

2025-07-16 15:17:33 - Invoice created successfully: ID 266

2025-07-16 15:20:52 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-16 15:20:52 - Processing billable_id: user_18
2025-07-16 15:20:52 - Billable type: user, ID: 18
2025-07-16 15:20:52 - After conversion - client_id: null, user_id: 18
2025-07-16 15:20:52 - Converted issue_date to: 2025-07-16
2025-07-16 15:20:52 - Starting to process items
2025-07-16 15:20:52 - Processing 3 items
2025-07-16 15:20:52 - Creating invoice with data:
{
    "csrf_token": "584c3834baca68d7415f8610a11602d4df3d17a240a40024922f98f10c90167a",
    "action": "save",
    "document_type_id": "1",
    "invoice_type_id": "15",
    "template_id": "",
    "invoice_number": "FAC-RET25-2025-0198",
    "issue_date_display": "16\/07\/2025",
    "subject": "R\u00c9TROCESSION",
    "period": "AVRIL + MAI 2025",
    "billable_type": "user",
    "billable_id": "user_18",
    "items": [
        {
            "quantity": "1",
            "item_id": "",
            "description": "R\u00c9TROCESSION CNS 20%",
            "unit_price": "1315.18",
            "vat_rate_id": "35"
        },
        {
            "quantity": "1",
            "item_id": "",
            "description": "R\u00c9TROCESSION PATIENTS 20%",
            "unit_price": "0.00",
            "vat_rate_id": "35"
        },
        {
            "quantity": "1",
            "item_id": "",
            "description": "FRAIS SECR\u00c9TARIAT ET MISE \u00c0 DISPOSITION MAT\u00c9RIEL 5%",
            "unit_price": "281.02",
            "vat_rate_id": "14"
        }
    ],
    "notes": "",
    "internal_notes": "",
    "payment_term_id": "1",
    "due_date_display": "",
    "issue_date": "2025-07-16",
    "due_date": "",
    "user_id": 18,
    "client_id": null,
    "status": "draft",
    "lines": [
        {
            "description": "R\u00c9TROCESSION CNS 20%",
            "quantity": 1,
            "unit_price": 1315.18,
            "vat_rate": "0.00",
            "vat_rate_id": "35",
            "total": 1315.18,
            "sort_order": 0
        },
        {
            "description": "R\u00c9TROCESSION PATIENTS 20%",
            "quantity": 1,
            "unit_price": 0,
            "vat_rate": "0.00",
            "vat_rate_id": "35",
            "total": 0,
            "sort_order": 1
        },
        {
            "description": "FRAIS SECR\u00c9TARIAT ET MISE \u00c0 DISPOSITION MAT\u00c9RIEL 5%",
            "quantity": 1,
            "unit_price": 281.02,
            "vat_rate": "17.00",
            "vat_rate_id": "14",
            "total": 281.02,
            "sort_order": 2
        }
    ],
    "subtotal": 1596.2,
    "vat_amount": 47.7734,
    "total": 1643.9734,
    "invoice_type": "retrocession_25",
    "type_id": "15"
}

2025-07-16 15:20:52 - Invoice model createInvoice called
2025-07-16 15:20:52 - Executing INSERT with params:
{
    ":invoice_number": "FAC-RET25-2025-0198",
    ":document_type_id": "1",
    ":type_id": "15",
    ":invoice_type": "retrocession_25",
    ":template_id": "",
    ":profile_id": null,
    ":client_id": null,
    ":user_id": 18,
    ":status": "draft",
    ":issue_date": "2025-07-16",
    ":due_date": "2025-08-15",
    ":subtotal": 1596.2,
    ":vat_amount": 47.77,
    ":cns_base_amount": 0,
    ":secretariat_vat_amount": 0,
    ":total": 1643.97,
    ":original_amount": null,
    ":staff_limit_applied": false,
    ":staff_limit_amount": null,
    ":secretariat_vat_note_shown": false,
    ":cns_reference": null,
    ":draft_until": "2025-07-18 15:20:52",
    ":currency": "EUR",
    ":notes": "",
    ":internal_notes": "",
    ":payment_terms": "D\u00e8s r\u00e9ception",
    ":reference_document_id": null,
    ":reference_document_number": null,
    ":credit_reason": null,
    ":created_by": 1,
    ":is_archived": 0,
    ":subject": "R\u00c9TROCESSION",
    ":period": "AVRIL + MAI 2025"
}

2025-07-16 15:20:52 - Invoice inserted successfully with ID: 267

2025-07-16 15:20:52 - Invoice created successfully: ID 267

2025-07-17 07:50:38 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-17 07:50:38 - Processing billable_id: user_8
2025-07-17 07:50:38 - Billable type: user, ID: 8
2025-07-17 07:50:38 - After conversion - client_id: null, user_id: 8
2025-07-17 07:50:38 - Converted issue_date to: 2025-07-17
2025-07-17 07:50:38 - Starting to process items
2025-07-17 07:50:38 - Processing 1 items
2025-07-17 07:50:38 - Validation error: Ce champ est obligatoire
Data at validation: {"client_id":"null","user_id":8,"issue_date":"2025-07-17","due_date":"","status":"sent"}

2025-07-17 07:50:38 - EXCEPTION in store method:
Message: Ce champ est obligatoire
File: D:\wamp64\www\fit\app\controllers\InvoiceController.php
Line: 1445
Trace:
#0 D:\wamp64\www\fit\app\controllers\InvoiceController.php(702): App\Controllers\InvoiceController->validateInvoiceData(Array)
#1 [internal function]: App\Controllers\InvoiceController->store()
#2 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(378): call_user_func_array(Array, Array)
#3 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(289): flight\core\Dispatcher->invokeCallable(Array, Array)
#4 D:\wamp64\www\fit\vendor\mikecao\flight\flight\Engine.php(604): flight\core\Dispatcher->execute(Array, Array)
#5 [internal function]: flight\Engine->_start()
#6 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(378): call_user_func_array(Array, Array)
#7 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(289): flight\core\Dispatcher->invokeCallable(Array, Array)
#8 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(133): flight\core\Dispatcher->execute(Array, Array)
#9 D:\wamp64\www\fit\vendor\mikecao\flight\flight\core\Dispatcher.php(97): flight\core\Dispatcher->runEvent('start', Array)
#10 D:\wamp64\www\fit\vendor\mikecao\flight\flight\Engine.php(153): flight\core\Dispatcher->run('start', Array)
#11 D:\wamp64\www\fit\vendor\mikecao\flight\flight\Flight.php(138): flight\Engine->__call('start', Array)
#12 D:\wamp64\www\fit\public\index.php(41): Flight::__callStatic('start', Array)
#13 {main}

2025-07-17 07:51:17 - Store method called
Method: POST
URL: /invoices
Session user: 1
POST data keys: csrf_token, action, document_type_id, invoice_type_id, template_id, invoice_number, issue_date_display, subject, period, billable_type, billable_id, items, notes, internal_notes, payment_term_id, due_date_display, issue_date, due_date

2025-07-17 07:51:17 - Processing billable_id: user_8
2025-07-17 07:51:17 - Billable type: user, ID: 8
2025-07-17 07:51:17 - After conversion - client_id: null, user_id: 8
2025-07-17 07:51:17 - Converted issue_date to: 2025-07-17
2025-07-17 07:51:17 - Starting to process items
2025-07-17 07:51:17 - Processing 1 items
2025-07-17 07:51:17 - Creating invoice with data:
{
    "csrf_token": "77365dc260686ae42b6ab53d68210d4d4d9fb9da82b168fae29a48f3aa8150a1",
    "action": "save",
    "document_type_id": "1",
    "invoice_type_id": "12",
    "template_id": "",
    "invoice_number": "FAC-LOC-2025-0199",
    "issue_date_display": "17\/07\/2025",
    "subject": "LOCATION SALLE",
    "period": "JUIN 2025",
    "billable_type": "user",
    "billable_id": "user_8",
    "items": [
        {
            "item_id": "",
            "description": "Collectif",
            "quantity": "7",
            "unit_price": "25.64",
            "vat_rate_id": "31"
        }
    ],
    "notes": "",
    "internal_notes": "",
    "payment_term_id": "1",
    "due_date_display": "",
    "issue_date": "2025-07-17",
    "due_date": "",
    "user_id": 8,
    "client_id": null,
    "status": "draft",
    "lines": [
        {
            "description": "Collectif",
            "quantity": 7,
            "unit_price": 25.64,
            "vat_rate": "17.00",
            "vat_rate_id": "31",
            "total": 179.48000000000002,
            "sort_order": 0
        }
    ],
    "subtotal": 179.48000000000002,
    "vat_amount": 30.511600000000005,
    "total": 209.99160000000003,
    "invoice_type": "LOC",
    "type_id": "12"
}

2025-07-17 07:51:17 - Invoice model createInvoice called
2025-07-17 07:51:17 - Executing INSERT with params:
{
    ":invoice_number": "FAC-LOC-2025-0199",
    ":document_type_id": "1",
    ":type_id": "12",
    ":invoice_type": "LOC",
    ":template_id": "",
    ":profile_id": null,
    ":client_id": null,
    ":user_id": 8,
    ":status": "draft",
    ":issue_date": "2025-07-17",
    ":due_date": "2025-08-16",
    ":subtotal": 179.48,
    ":vat_amount": 30.51,
    ":cns_base_amount": 0,
    ":secretariat_vat_amount": 0,
    ":total": 209.99,
    ":original_amount": null,
    ":staff_limit_applied": false,
    ":staff_limit_amount": null,
    ":secretariat_vat_note_shown": false,
    ":cns_reference": null,
    ":draft_until": "2025-07-19 07:51:17",
    ":currency": "EUR",
    ":notes": "",
    ":internal_notes": "",
    ":payment_terms": "D\u00e8s r\u00e9ception",
    ":reference_document_id": null,
    ":reference_document_number": null,
    ":credit_reason": null,
    ":created_by": 1,
    ":is_archived": 0,
    ":subject": "LOCATION SALLE",
    ":period": "JUIN 2025"
}

2025-07-17 07:51:17 - Invoice inserted successfully with ID: 268

2025-07-17 07:51:17 - Invoice created successfully: ID 268

