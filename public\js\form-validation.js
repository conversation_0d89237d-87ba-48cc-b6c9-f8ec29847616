/**
 * Client-side Form Validation
 * Provides real-time validation for forms
 */

(function() {
    'use strict';

    // Validation rules
    const validators = {
        required: function(value, param) {
            return value !== null && value !== undefined && value.toString().trim() !== '';
        },
        
        email: function(value) {
            if (!value) return true;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(value);
        },
        
        number: function(value, params) {
            if (!value) return true;
            const num = parseFloat(value);
            if (isNaN(num)) return false;
            
            if (params.min !== undefined && num < params.min) return false;
            if (params.max !== undefined && num > params.max) return false;
            
            return true;
        },
        
        integer: function(value, params) {
            if (!value) return true;
            const num = parseInt(value);
            if (isNaN(num) || num !== parseFloat(value)) return false;
            
            if (params.min !== undefined && num < params.min) return false;
            if (params.max !== undefined && num > params.max) return false;
            
            return true;
        },
        
        price: function(value) {
            if (!value) return true;
            // Remove currency symbols and spaces
            const cleanValue = value.toString().replace(/[^0-9.,\-]/g, '').replace(',', '.');
            const num = parseFloat(cleanValue);
            return !isNaN(num) && num >= 0;
        },
        
        date: function(value, params) {
            if (!value) return true;
            const format = params.format || 'YYYY-MM-DD';
            
            // Simple date validation for common formats
            if (format === 'YYYY-MM-DD') {
                const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
                if (!dateRegex.test(value)) return false;
                
                const date = new Date(value);
                return date instanceof Date && !isNaN(date);
            }
            
            return true;
        },
        
        length: function(value, params) {
            if (!value) return true;
            const length = value.toString().length;
            
            if (params.min !== undefined && length < params.min) return false;
            if (params.max !== undefined && length > params.max) return false;
            
            return true;
        },
        
        phone: function(value) {
            if (!value) return true;
            // Remove spaces, dashes, parentheses
            const cleanPhone = value.replace(/[\s\-\(\)]/g, '');
            const phoneRegex = /^\+?\d{7,15}$/;
            return phoneRegex.test(cleanPhone);
        },
        
        url: function(value) {
            if (!value) return true;
            try {
                new URL(value);
                return true;
            } catch (e) {
                return false;
            }
        },
        
        match: function(value, params, form) {
            const matchField = form.querySelector(`[name="${params.field}"]`);
            return matchField && value === matchField.value;
        }
    };

    // Error messages
    const errorMessages = {
        required: 'This field is required.',
        email: 'Please enter a valid email address.',
        number: 'Please enter a valid number.',
        integer: 'Please enter a whole number.',
        price: 'Please enter a valid price.',
        date: 'Please enter a valid date.',
        length: 'Please check the length of this field.',
        phone: 'Please enter a valid phone number.',
        url: 'Please enter a valid URL.',
        match: 'This field must match.'
    };

    // Get error message with parameter substitution
    function getErrorMessage(rule, params, fieldName) {
        let message = errorMessages[rule] || 'Invalid value.';
        
        if (rule === 'length') {
            if (params.min && params.max) {
                message = `This field must be between ${params.min} and ${params.max} characters.`;
            } else if (params.min) {
                message = `This field must be at least ${params.min} characters.`;
            } else if (params.max) {
                message = `This field must not exceed ${params.max} characters.`;
            }
        } else if (rule === 'number' || rule === 'integer') {
            if (params.min !== undefined && params.max !== undefined) {
                message = `Please enter a value between ${params.min} and ${params.max}.`;
            } else if (params.min !== undefined) {
                message = `Please enter a value of at least ${params.min}.`;
            } else if (params.max !== undefined) {
                message = `Please enter a value no greater than ${params.max}.`;
            }
        } else if (rule === 'match' && params.fieldLabel) {
            message = `This field must match ${params.fieldLabel}.`;
        }
        
        return message;
    }

    // Validate a single field
    function validateField(field, rules, form) {
        const value = field.value;
        const errors = [];
        
        for (const [rule, params] of Object.entries(rules)) {
            const validator = validators[rule];
            if (validator && !validator(value, params, form)) {
                errors.push({
                    rule: rule,
                    message: getErrorMessage(rule, params, field.name)
                });
            }
        }
        
        return errors;
    }

    // Show field error
    function showFieldError(field, error) {
        field.classList.add('is-invalid');
        field.classList.remove('is-valid');
        
        // Remove existing error message
        const existingError = field.parentElement.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
        
        // Add new error message
        if (error) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'invalid-feedback';
            errorDiv.textContent = error.message;
            field.parentElement.appendChild(errorDiv);
        }
    }

    // Clear field error
    function clearFieldError(field) {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');
        
        const existingError = field.parentElement.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }
    }

    // Initialize form validation
    function initializeFormValidation(form, validationRules) {
        // Add novalidate to use custom validation
        form.setAttribute('novalidate', true);
        
        // Track touched fields
        const touchedFields = new Set();
        
        // Validate on field blur
        Object.keys(validationRules).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (!field) return;
            
            field.addEventListener('blur', function() {
                touchedFields.add(fieldName);
                const errors = validateField(field, validationRules[fieldName], form);
                if (errors.length > 0) {
                    showFieldError(field, errors[0]);
                } else {
                    clearFieldError(field);
                }
            });
            
            // Clear error on input
            field.addEventListener('input', function() {
                if (touchedFields.has(fieldName)) {
                    const errors = validateField(field, validationRules[fieldName], form);
                    if (errors.length === 0) {
                        clearFieldError(field);
                    }
                }
            });
        });
        
        // Validate on form submit
        form.addEventListener('submit', function(e) {
            let hasErrors = false;
            const allErrors = [];
            
            Object.entries(validationRules).forEach(([fieldName, rules]) => {
                const field = form.querySelector(`[name="${fieldName}"]`);
                if (!field) return;
                
                const errors = validateField(field, rules, form);
                if (errors.length > 0) {
                    hasErrors = true;
                    showFieldError(field, errors[0]);
                    allErrors.push({ field: fieldName, errors: errors });
                } else {
                    clearFieldError(field);
                }
            });
            
            if (hasErrors) {
                e.preventDefault();
                
                // Show summary error message
                showFormErrors(form, allErrors);
                
                // Focus first error field
                const firstErrorField = form.querySelector('.is-invalid');
                if (firstErrorField) {
                    firstErrorField.focus();
                }
            }
        });
    }

    // Show form-level errors
    function showFormErrors(form, errors) {
        // Remove existing alert
        const existingAlert = form.querySelector('.validation-alert');
        if (existingAlert) {
            existingAlert.remove();
        }
        
        // Create new alert
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show validation-alert';
        alert.setAttribute('role', 'alert');
        
        let html = '<strong>Please correct the following errors:</strong><ul class="mb-0 mt-2">';
        errors.forEach(({ field, errors }) => {
            errors.forEach(error => {
                html += `<li>${error.message}</li>`;
            });
        });
        html += '</ul>';
        html += '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
        
        alert.innerHTML = html;
        form.insertBefore(alert, form.firstChild);
    }

    // Validation rules for different forms
    const formValidationRules = {
        // Invoice form validation
        invoice: {
            invoice_type_id: { required: true },
            client_name: { required: true, length: { min: 2, max: 255 } },
            client_email: { email: true },
            client_phone: { phone: true },
            invoice_date: { required: true, date: { format: 'YYYY-MM-DD' } },
            due_date: { date: { format: 'YYYY-MM-DD' } }
        },
        
        // User registration/edit form
        user: {
            first_name: { required: true, length: { min: 2, max: 50 } },
            last_name: { required: true, length: { min: 2, max: 50 } },
            email: { required: true, email: true },
            username: { required: true, length: { min: 3, max: 50 } },
            password: { required: true, length: { min: 6 } },
            confirm_password: { required: true, match: { field: 'password', fieldLabel: 'Password' } },
            phone: { phone: true },
            company: { length: { max: 100 } }
        },
        
        // Product form
        product: {
            name: { required: true, length: { min: 2, max: 255 } },
            category_id: { required: true },
            price: { required: true, price: true },
            cost: { price: true },
            stock: { integer: { min: 0 } },
            sku: { length: { max: 50 } },
            barcode: { length: { max: 50 } }
        },
        
        // Login form
        login: {
            username: { required: true },
            password: { required: true }
        },
        
        // Email template form
        emailTemplate: {
            template_name: { required: true, length: { min: 2, max: 100 } },
            subject: { required: true, length: { min: 2, max: 200 } },
            body: { required: true }
        }
    };

    // Auto-initialize forms with data-validate attribute
    document.addEventListener('DOMContentLoaded', function() {
        const forms = document.querySelectorAll('form[data-validate]');
        forms.forEach(form => {
            const validationType = form.getAttribute('data-validate');
            const rules = formValidationRules[validationType];
            
            if (rules) {
                // Check if it's an edit form (for user forms, password is not required on edit)
                if (validationType === 'user' && form.querySelector('input[name="user_id"]')) {
                    // Remove password requirement for edit forms
                    const editRules = { ...rules };
                    delete editRules.password;
                    delete editRules.confirm_password;
                    initializeFormValidation(form, editRules);
                } else {
                    initializeFormValidation(form, rules);
                }
            }
        });
    });

    // Export for manual initialization
    window.FormValidation = {
        init: initializeFormValidation,
        rules: formValidationRules,
        validators: validators
    };
})();