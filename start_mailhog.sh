#!/bin/bash
# Script to start Mailhog for email testing

echo "Starting Mailhog..."

# Check if mailhog is installed
if ! command -v mailhog &> /dev/null; then
    echo "Mailhog is not installed. Installing..."
    
    # Try to install with go
    if command -v go &> /dev/null; then
        go install github.com/mailhog/MailHog@latest
    else
        echo "Please install Mailhog manually:"
        echo "  - Download from: https://github.com/mailhog/MailHog/releases"
        echo "  - Or install Go and run: go install github.com/mailhog/MailHog@latest"
        exit 1
    fi
fi

# Check if already running
if nc -z localhost 1025 2>/dev/null; then
    echo "Mailhog is already running on port 1025"
    echo "Web UI: http://localhost:8025"
else
    # Start mailhog in background
    nohup mailhog > /tmp/mailhog.log 2>&1 &
    echo "Mailhog started!"
    echo "SMTP: localhost:1025"
    echo "Web UI: http://localhost:8025"
    echo "Logs: /tmp/mailhog.log"
fi