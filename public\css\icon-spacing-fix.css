/**
 * Global Icon Spacing Fix
 * Ensures proper spacing between icons and text in all UI elements
 */

/* Dropdown items */
.dropdown-item > i[class*="bi-"]:first-child,
.dropdown-item > .bi:first-child,
.dropdown-item i[class*="bi-"]:not(:last-child),
.dropdown-item .bi:not(:last-child) {
    margin-right: 0.5rem !important;
}

/* Buttons */
.btn > i[class*="bi-"]:first-child,
.btn > .bi:first-child,
.btn i[class*="bi-"]:not(:last-child),
.btn .bi:not(:last-child) {
    margin-right: 0.375rem !important;
}

/* Links */
a > i[class*="bi-"]:first-child,
a > .bi:first-child,
a i[class*="bi-"]:not(:last-child),
a .bi:not(:last-child) {
    margin-right: 0.375rem !important;
}

/* List items */
li > i[class*="bi-"]:first-child,
li > .bi:first-child,
li i[class*="bi-"]:not(:last-child),
li .bi:not(:last-child) {
    margin-right: 0.5rem !important;
}

/* Table cells */
td > i[class*="bi-"]:first-child,
td > .bi:first-child,
td i[class*="bi-"]:not(:last-child),
td .bi:not(:last-child) {
    margin-right: 0.375rem !important;
}

/* Nav items */
.nav-link > i[class*="bi-"]:first-child,
.nav-link > .bi:first-child,
.nav-link i[class*="bi-"]:not(:last-child),
.nav-link .bi:not(:last-child) {
    margin-right: 0.5rem !important;
}

/* Alert messages */
.alert > i[class*="bi-"]:first-child,
.alert > .bi:first-child,
.alert i[class*="bi-"]:not(:last-child),
.alert .bi:not(:last-child) {
    margin-right: 0.5rem !important;
}

/* Badge items */
.badge > i[class*="bi-"]:first-child,
.badge > .bi:first-child,
.badge i[class*="bi-"]:not(:last-child),
.badge .bi:not(:last-child) {
    margin-right: 0.25rem !important;
}

/* Override for icons that should not have spacing */
i[class*="bi-"].no-spacing,
.bi.no-spacing,
.icon-only i[class*="bi-"],
.icon-only .bi {
    margin-right: 0 !important;
}

/* Icons at the end should have left margin instead */
i[class*="bi-"]:last-child:not(:first-child),
.bi:last-child:not(:first-child) {
    margin-left: 0.375rem !important;
    margin-right: 0 !important;
}

/* Fix for RTL languages */
[dir="rtl"] i[class*="bi-"]:first-child,
[dir="rtl"] .bi:first-child,
[dir="rtl"] i[class*="bi-"]:not(:last-child),
[dir="rtl"] .bi:not(:last-child) {
    margin-right: 0 !important;
    margin-left: 0.5rem !important;
}

[dir="rtl"] i[class*="bi-"]:last-child:not(:first-child),
[dir="rtl"] .bi:last-child:not(:first-child) {
    margin-left: 0 !important;
    margin-right: 0.375rem !important;
}