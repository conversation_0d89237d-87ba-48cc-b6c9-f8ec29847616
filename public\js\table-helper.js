/**
 * TableHelper - Reusable table functionality for FIT application
 * Provides: Live search, filter persistence, import/export, bulk actions
 */

class TableHelper {
    constructor(options) {
        this.options = {
            tableId: options.tableId || 'data-table',
            searchInputId: options.searchInputId || 'search',
            searchColumns: options.searchColumns || [], // Column indices to search
            filters: options.filters || [], // Array of filter configurations
            storageKey: options.storageKey || 'table_filters',
            exportFormats: options.exportFormats || ['csv', 'excel', 'pdf'],
            exportUrl: options.exportUrl || '',
            bulkActions: options.bulkActions || [],
            bulkActionUrl: options.bulkActionUrl || '',
            translations: options.translations || {
                search: 'Search...',
                noResults: 'No results found',
                bulkActions: 'Bulk Actions',
                export: 'Export',
                import: 'Import',
                reset: 'Reset Filters',
                results: 'results',
                selected: 'selected'
            }
        };
        
        this.init();
    }
    
    init() {
        this.table = document.getElementById(this.options.tableId);
        this.searchInput = document.getElementById(this.options.searchInputId);
        
        if (!this.table) {
            console.error('Table not found:', this.options.tableId);
            return;
        }
        
        this.setupSearch();
        this.setupFilters();
        this.setupBulkActions();
        this.loadSavedState();
    }
    
    // Live Search Functionality
    setupSearch() {
        if (!this.searchInput) return;
        
        const tbody = this.table.querySelector('tbody');
        const rows = tbody ? Array.from(tbody.querySelectorAll('tr')) : [];
        
        // Store original display state
        rows.forEach(row => {
            row.dataset.originalDisplay = row.style.display || '';
        });
        
        // Create search results counter
        const counter = document.createElement('small');
        counter.id = 'search-results-count';
        counter.className = 'text-muted ms-2';
        
        if (this.searchInput.parentElement) {
            this.searchInput.parentElement.appendChild(counter);
        }
        
        // Debounced search function
        let searchTimeout;
        const performSearch = () => {
            const searchTerm = this.searchInput.value.toLowerCase().trim();
            
            // Save to localStorage
            if (searchTerm) {
                localStorage.setItem(this.options.storageKey + '_search', searchTerm);
            } else {
                localStorage.removeItem(this.options.storageKey + '_search');
            }
            
            if (searchTerm === '') {
                rows.forEach(row => {
                    row.style.display = row.dataset.originalDisplay;
                    this.clearHighlights(row);
                });
                this.updateResultsCount(rows.length);
                return;
            }
            
            let visibleCount = 0;
            
            rows.forEach(row => {
                let found = false;
                
                // Search in specified columns or all if not specified
                if (this.options.searchColumns.length > 0) {
                    this.options.searchColumns.forEach(colIndex => {
                        const cell = row.cells[colIndex];
                        if (cell && cell.textContent.toLowerCase().includes(searchTerm)) {
                            found = true;
                            this.highlightText(cell, searchTerm);
                        }
                    });
                } else {
                    // Search all cells
                    Array.from(row.cells).forEach(cell => {
                        if (cell.textContent.toLowerCase().includes(searchTerm)) {
                            found = true;
                            this.highlightText(cell, searchTerm);
                        }
                    });
                }
                
                if (found) {
                    row.style.display = row.dataset.originalDisplay;
                    visibleCount++;
                } else {
                    row.style.display = 'none';
                    this.clearHighlights(row);
                }
            });
            
            this.updateResultsCount(visibleCount);
        };
        
        // Add search event listeners
        this.searchInput.addEventListener('input', () => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(performSearch, 300);
        });
        
        this.searchInput.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.searchInput.value = '';
                performSearch();
            }
        });
        
        // Add clear button
        this.addClearButton(this.searchInput, performSearch);
        
        // Load saved search
        const savedSearch = localStorage.getItem(this.options.storageKey + '_search');
        if (savedSearch && !this.searchInput.value) {
            this.searchInput.value = savedSearch;
            performSearch();
        }
    }
    
    // Filter Management
    setupFilters() {
        this.options.filters.forEach(filter => {
            const element = document.getElementById(filter.id);
            if (!element) return;
            
            element.addEventListener('change', () => {
                this.saveFilters();
                
                if (filter.autoSubmit !== false) {
                    const form = element.closest('form');
                    if (form) {
                        form.submit();
                    }
                }
            });
        });
    }
    
    saveFilters() {
        const filters = {};
        
        this.options.filters.forEach(filter => {
            const element = document.getElementById(filter.id);
            if (element && element.value) {
                filters[filter.id] = element.value;
            }
        });
        
        localStorage.setItem(this.options.storageKey, JSON.stringify(filters));
    }
    
    loadSavedState() {
        // Load filters
        const savedFilters = localStorage.getItem(this.options.storageKey);
        if (savedFilters) {
            try {
                const filters = JSON.parse(savedFilters);
                const urlParams = new URLSearchParams(window.location.search);
                
                // Only apply saved filters if no URL parameters exist
                if (urlParams.toString() === '') {
                    Object.entries(filters).forEach(([id, value]) => {
                        const element = document.getElementById(id);
                        if (element) {
                            element.value = value;
                        }
                    });
                    
                    // If we have filters but no URL params, reload with filters
                    if (Object.keys(filters).length > 0) {
                        const newUrl = new URL(window.location.href);
                        Object.entries(filters).forEach(([key, value]) => {
                            if (value) newUrl.searchParams.set(key, value);
                        });
                        window.location.href = newUrl.toString();
                        return;
                    }
                }
            } catch (e) {
                console.error('Error loading saved filters:', e);
            }
        }
    }
    
    clearFilters() {
        localStorage.removeItem(this.options.storageKey);
        localStorage.removeItem(this.options.storageKey + '_search');
        
        // Clear form inputs
        this.options.filters.forEach(filter => {
            const element = document.getElementById(filter.id);
            if (element) {
                element.value = '';
            }
        });
        
        if (this.searchInput) {
            this.searchInput.value = '';
        }
    }
    
    // Bulk Actions
    setupBulkActions() {
        const selectAllCheckbox = this.table.querySelector('#selectAll');
        const checkboxes = this.table.querySelectorAll('.row-checkbox');
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        
        if (!selectAllCheckbox || checkboxes.length === 0) return;
        
        // Select all functionality
        selectAllCheckbox.addEventListener('change', function() {
            checkboxes.forEach(cb => cb.checked = this.checked);
            this.updateBulkActionButton();
        }.bind(this));
        
        // Individual checkbox changes
        checkboxes.forEach(cb => {
            cb.addEventListener('change', () => {
                this.updateBulkActionButton();
                this.updateSelectAllState();
            });
        });
    }
    
    updateBulkActionButton() {
        const selected = this.getSelectedIds().length;
        const bulkActionsBtn = document.getElementById('bulkActionsBtn');
        
        if (bulkActionsBtn) {
            bulkActionsBtn.disabled = selected === 0;
            
            // Update button text to show count
            const originalText = bulkActionsBtn.textContent.replace(/ \(\d+\)/, '');
            if (selected > 0) {
                bulkActionsBtn.textContent = `${originalText} (${selected})`;
            } else {
                bulkActionsBtn.textContent = originalText;
            }
        }
    }
    
    updateSelectAllState() {
        const selectAllCheckbox = this.table.querySelector('#selectAll');
        const checkboxes = this.table.querySelectorAll('.row-checkbox');
        const checkedBoxes = this.table.querySelectorAll('.row-checkbox:checked');
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = checkboxes.length === checkedBoxes.length && checkboxes.length > 0;
            selectAllCheckbox.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < checkboxes.length;
        }
    }
    
    getSelectedIds() {
        return Array.from(this.table.querySelectorAll('.row-checkbox:checked')).map(cb => cb.value);
    }
    
    // Export functionality
    exportData(format) {
        const url = new URL(this.options.exportUrl);
        url.searchParams.set('format', format);
        
        // Add current filters
        const urlParams = new URLSearchParams(window.location.search);
        for (const [key, value] of urlParams) {
            if (key !== 'format') {
                url.searchParams.set(key, value);
            }
        }
        
        window.location.href = url.toString();
    }
    
    // Import functionality
    showImportDialog() {
        // This would typically open a modal or redirect to import page
        if (this.options.importUrl) {
            window.location.href = this.options.importUrl;
        }
    }
    
    // Bulk action execution
    executeBulkAction(action) {
        const selectedIds = this.getSelectedIds();
        if (selectedIds.length === 0) return;
        
        // Find action configuration
        const actionConfig = this.options.bulkActions.find(a => a.action === action);
        if (!actionConfig) return;
        
        // Confirm if needed
        if (actionConfig.confirm) {
            if (!confirm(actionConfig.confirmMessage || 'Are you sure?')) {
                return;
            }
        }
        
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = actionConfig.url || this.options.bulkActionUrl;
        
        // Add CSRF token
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }
        
        // Add action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = action;
        form.appendChild(actionInput);
        
        // Add selected IDs
        selectedIds.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        document.body.appendChild(form);
        form.submit();
    }
    
    // Helper methods
    highlightText(element, searchTerm) {
        const text = element.textContent;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        element.innerHTML = text.replace(regex, '<mark class="highlight">$1</mark>');
    }
    
    clearHighlights(element) {
        element.querySelectorAll('mark.highlight').forEach(mark => {
            const text = mark.textContent;
            mark.replaceWith(text);
        });
    }
    
    updateResultsCount(count) {
        const counter = document.getElementById('search-results-count');
        if (!counter) return;
        
        if (this.searchInput && this.searchInput.value.trim() !== '') {
            counter.textContent = `(${count} ${this.options.translations.results})`;
        } else {
            counter.textContent = '';
        }
    }
    
    addClearButton(input, onClear) {
        const clearBtn = document.createElement('button');
        clearBtn.type = 'button';
        clearBtn.className = 'btn btn-sm position-absolute end-0 top-50 translate-middle-y me-5';
        clearBtn.style.display = 'none';
        clearBtn.style.zIndex = '10';
        clearBtn.innerHTML = '<i class="bi bi-x-circle"></i>';
        
        clearBtn.onclick = () => {
            input.value = '';
            localStorage.removeItem(this.options.storageKey + '_search');
            onClear();
            clearBtn.style.display = 'none';
        };
        
        input.parentElement.style.position = 'relative';
        input.parentElement.appendChild(clearBtn);
        
        input.addEventListener('input', () => {
            clearBtn.style.display = input.value ? 'block' : 'none';
        });
        
        // Initial state
        if (input.value) {
            clearBtn.style.display = 'block';
        }
    }
}

// Global function for easy initialization
window.initTableHelper = function(options) {
    return new TableHelper(options);
};