{% extends "base-modern.twig" %}

{% block title %}{{ __('config.vat_rates') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.vat_rates') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#vatRateModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_vat_rate') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.vat_rates_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- VAT Rates Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ __('config.current_vat_rates') }}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="50">{{ __('common.default') }}</th>
                            <th>{{ __('common.name') }}</th>
                            <th>{{ __('config.vat_percentage') }}</th>
                            <th>{{ __('common.description') }}</th>
                            <th>{{ __('config.effective_from') }}</th>
                            <th>{{ __('config.effective_to') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody id="vatRatesTable">
                        {% for rate in vatRates %}
                        <tr data-id="{{ rate.id }}">
                            <td class="text-center">
                                <div class="form-check">
                                    <input type="radio" class="form-check-input" name="default_rate" 
                                           value="{{ rate.id }}" {{ rate.is_default ? 'checked' : '' }}
                                           onchange="setDefaultRate({{ rate.id }})">
                                </div>
                            </td>
                            <td>
                                <strong>{{ rate.name }}</strong>
                                {% if rate.code %}
                                    <br><small class="text-muted">{{ rate.code }}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ rate.rate }} %</span>
                            </td>
                            <td>{{ rate.description|default('-') }}</td>
                            <td>
                                {% if rate.effective_from %}
                                    {{ rate.effective_from|date('d/m/Y') }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rate.effective_to %}
                                    {{ rate.effective_to|date('d/m/Y') }}
                                    {% if rate.effective_to < 'now'|date('Y-m-d') %}
                                        <i class="bi bi-exclamation-circle text-warning ms-1" title="{{ __('config.expired') }}"></i>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if rate.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" 
                                            class="btn btn-outline-secondary" 
                                            onclick="editRate({{ rate.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-info" 
                                            onclick="duplicateRate({{ rate.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.duplicate') }}">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    {% if not rate.is_default %}
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="deleteRate({{ rate.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center py-4 text-muted">
                                {{ __('config.no_vat_rates_found') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="bi bi-download me-2"></i>{{ __('config.export_import') }}</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex gap-2">
                        <a href="{{ base_url }}/config/vat-rates/export" class="btn btn-outline-info">
                            <i class="bi bi-download me-2"></i>{{ __('common.export') }} CSV
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="showImportModal()">
                            <i class="bi bi-upload me-2"></i>{{ __('common.import') }} CSV
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.tips') }}</h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0 small">
                        <li>{{ __('config.vat_rate_tip_1') }}</li>
                        <li>{{ __('config.vat_rate_tip_2') }}</li>
                        <li>{{ __('config.vat_rate_tip_3') }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- VAT Rate Modal -->
<div class="modal fade" id="vatRateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/config/vat-rates" id="vatRateForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="_method" id="form_method" value="POST">
                <input type="hidden" name="rate_id" id="rate_id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="vatRateModalTitle">{{ __('config.add_vat_rate') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-8">
                            <label for="rate_name" class="form-label">{{ __('common.name') }} *</label>
                            <input type="text" class="form-control" id="rate_name" name="name" required>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="rate_code" class="form-label">{{ __('common.code') }}</label>
                            <input type="text" class="form-control" id="rate_code" name="code" maxlength="10">
                        </div>
                        
                        <div class="col-md-6">
                            <label for="rate_percentage" class="form-label">{{ __('config.vat_percentage') }} *</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="rate_percentage" name="rate" 
                                       min="0" max="100" step="0.01" required>
                                <span class="input-group-text">%</span>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="rate_status" class="form-label">{{ __('common.status') }}</label>
                            <select class="form-select" id="rate_status" name="is_active">
                                <option value="1">{{ __('common.active') }}</option>
                                <option value="0">{{ __('common.inactive') }}</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="rate_effective_from" class="form-label">{{ __('config.effective_from') }}</label>
                            <input type="date" class="form-control" id="rate_effective_from" name="effective_from">
                        </div>
                        
                        <div class="col-md-6">
                            <label for="rate_effective_to" class="form-label">{{ __('config.effective_to') }}</label>
                            <input type="date" class="form-control" id="rate_effective_to" name="effective_to">
                        </div>
                        
                        <div class="col-md-12">
                            <label for="rate_description" class="form-label">{{ __('common.description') }}</label>
                            <textarea class="form-control" id="rate_description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="col-md-12">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="rate_is_default" name="is_default">
                                <label class="form-check-label" for="rate_is_default">
                                    {{ __('config.set_as_default') }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/config/vat-rates/import" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('config.import_vat_rates') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="import_file" class="form-label">{{ __('common.select_file') }}</label>
                        <input type="file" class="form-control" id="import_file" name="file" accept=".csv" required>
                        <small class="text-muted">{{ __('config.csv_format_hint') }}</small>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        {{ __('config.import_format_info') }}
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.import') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// VAT rates data for editing
const vatRates = {{ vatRates|json_encode|raw }};

function editRate(id) {
    const rate = vatRates.find(r => r.id === id);
    if (!rate) return;
    
    document.getElementById('rate_id').value = id;
    document.getElementById('rate_name').value = rate.name;
    document.getElementById('rate_code').value = rate.code || '';
    document.getElementById('rate_percentage').value = rate.rate;
    document.getElementById('rate_status').value = rate.is_active ? '1' : '0';
    document.getElementById('rate_effective_from').value = rate.effective_from || '';
    document.getElementById('rate_effective_to').value = rate.effective_to || '';
    document.getElementById('rate_description').value = rate.description || '';
    document.getElementById('rate_is_default').checked = rate.is_default;
    
    // Update modal for editing
    document.getElementById('vatRateModalTitle').textContent = '{{ __("config.edit_vat_rate") }}';
    document.getElementById('vatRateForm').action = '{{ base_url }}/config/vat-rates/' + id;
    document.getElementById('form_method').value = 'PUT';
    
    new bootstrap.Modal(document.getElementById('vatRateModal')).show();
}

function duplicateRate(id) {
    const rate = vatRates.find(r => r.id === id);
    if (!rate) return;
    
    document.getElementById('rate_id').value = '';
    document.getElementById('rate_name').value = rate.name + ' ({{ __("common.copy") }})';
    document.getElementById('rate_code').value = '';
    document.getElementById('rate_percentage').value = rate.rate;
    document.getElementById('rate_status').value = '1';
    document.getElementById('rate_effective_from').value = '';
    document.getElementById('rate_effective_to').value = '';
    document.getElementById('rate_description').value = rate.description || '';
    document.getElementById('rate_is_default').checked = false;
    
    // Update modal for creating
    document.getElementById('vatRateModalTitle').textContent = '{{ __("config.add_vat_rate") }}';
    document.getElementById('vatRateForm').action = '{{ base_url }}/config/vat-rates';
    document.getElementById('form_method').value = 'POST';
    
    new bootstrap.Modal(document.getElementById('vatRateModal')).show();
}

function deleteRate(id) {
    if (confirm('{{ __("config.delete_vat_rate_confirm") }}')) {
        fetch('{{ base_url }}/config/vat-rates/' + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                '_method': 'DELETE',
                'csrf_token': '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                toastr.success(data.message);
                // Remove the row from the table
                const row = document.querySelector(`tr[data-id="${id}"]`);
                if (row) {
                    row.remove();
                }
                // Check if table is empty
                const tbody = document.getElementById('vatRatesTable');
                if (tbody.children.length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-muted">{{ __("config.no_vat_rates_found") }}</td></tr>';
                }
            } else {
                toastr.error(data.message || 'Une erreur est survenue');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('Une erreur est survenue lors de la suppression');
        });
    }
}

function setDefaultRate(id) {
    fetch('{{ base_url }}/config/vat-rates/' + id + '/default', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            '_method': 'PUT',
            'csrf_token': '{{ csrf_token }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            // Update the UI - uncheck all radios first
            document.querySelectorAll('input[name="default_rate"]').forEach(radio => {
                radio.checked = false;
            });
            // Check the selected one
            const selectedRadio = document.querySelector(`input[name="default_rate"][value="${id}"]`);
            if (selectedRadio) {
                selectedRadio.checked = true;
            }
        } else {
            toastr.error(data.message || 'Une erreur est survenue');
            // Revert the radio button change
            location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('Une erreur est survenue');
        location.reload();
    });
}

function showImportModal() {
    new bootstrap.Modal(document.getElementById('importModal')).show();
}

// Handle form submission via AJAX
document.getElementById('vatRateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const isEdit = formData.get('_method') === 'PUT';
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message);
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('vatRateModal')).hide();
            // Reload page to show updated data
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            toastr.error(data.message || 'Une erreur est survenue');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('Une erreur est survenue lors de l\'enregistrement');
    });
});

// Reset modal when closed
document.getElementById('vatRateModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('vatRateForm').reset();
    document.getElementById('rate_id').value = '';
    document.getElementById('form_method').value = 'POST';
    document.getElementById('vatRateModalTitle').textContent = '{{ __("config.add_vat_rate") }}';
    document.getElementById('vatRateForm').action = '{{ base_url }}/config/vat-rates';
});
</script>

<style>
/* Fix for dropdown positioning in tables */
.table-responsive .dropdown {
    position: static;
}

.table-responsive .dropdown-menu {
    position: absolute !important;
    transform: translate3d(0px, 0px, 0px) !important;
    will-change: transform !important;
}
</style>
{% endblock %}