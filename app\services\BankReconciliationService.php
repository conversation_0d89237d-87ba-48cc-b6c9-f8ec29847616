<?php

namespace App\Services;

use Flight;
use App\Models\Invoice;
use App\Models\Payment;
use Exception;
use PDO;

class BankReconciliationService
{
    private $db;
    private $invoice;
    private $payment;
    
    // Matching confidence thresholds
    const MATCH_EXACT = 100;
    const MATCH_HIGH = 90;
    const MATCH_MEDIUM = 70;
    const MATCH_LOW = 50;
    
    public function __construct()
    {
        $this->db = Flight::db();
        $this->invoice = new Invoice();
        $this->payment = new Payment();
    }
    
    /**
     * Import bank statement
     */
    public function importBankStatement($file, $format = 'csv')
    {
        try {
            switch ($format) {
                case 'csv':
                    return $this->importCsv($file);
                case 'mt940':
                    return $this->importMt940($file);
                case 'camt053':
                    return $this->importCamt053($file);
                default:
                    throw new Exception('Unsupported format: ' . $format);
            }
        } catch (Exception $e) {
            throw new Exception('Import failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Process bank transactions for reconciliation
     */
    public function processTransactions($transactions)
    {
        $results = [
            'total' => count($transactions),
            'matched' => 0,
            'unmatched' => 0,
            'suggestions' => [],
            'errors' => []
        ];
        
        foreach ($transactions as $transaction) {
            try {
                $match = $this->findMatch($transaction);
                
                if ($match) {
                    if ($match['confidence'] >= self::MATCH_HIGH) {
                        // Auto-match high confidence matches
                        $this->createPayment($transaction, $match);
                        $results['matched']++;
                    } else {
                        // Add to suggestions for manual review
                        $results['suggestions'][] = [
                            'transaction' => $transaction,
                            'matches' => [$match]
                        ];
                    }
                } else {
                    // Try to find multiple possible matches
                    $possibleMatches = $this->findPossibleMatches($transaction);
                    
                    if (!empty($possibleMatches)) {
                        $results['suggestions'][] = [
                            'transaction' => $transaction,
                            'matches' => $possibleMatches
                        ];
                    } else {
                        $results['unmatched']++;
                    }
                }
            } catch (Exception $e) {
                $results['errors'][] = [
                    'transaction' => $transaction,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Find match for bank transaction
     */
    private function findMatch($transaction)
    {
        // Try exact amount match first
        $exactMatch = $this->findExactAmountMatch($transaction);
        if ($exactMatch) {
            return $exactMatch;
        }
        
        // Try reference match
        $refMatch = $this->findReferenceMatch($transaction);
        if ($refMatch) {
            return $refMatch;
        }
        
        // Try fuzzy matching
        return $this->findFuzzyMatch($transaction);
    }
    
    /**
     * Find exact amount match
     */
    private function findExactAmountMatch($transaction)
    {
        $stmt = $this->db->prepare("
            SELECT 
                i.*,
                c.name as client_name,
                c.bank_account
            FROM invoices i
            JOIN clients c ON i.client_id = c.id
            WHERE i.total = :amount
            AND i.status IN ('sent', 'unpaid')
            AND i.issue_date <= :transaction_date
            ORDER BY 
                CASE WHEN c.bank_account = :account THEN 0 ELSE 1 END,
                ABS(DATEDIFF(:transaction_date, i.due_date))
            LIMIT 1
        ");
        
        $stmt->execute([
            ':amount' => $transaction['amount'],
            ':transaction_date' => $transaction['date'],
            ':account' => $transaction['account_number'] ?? ''
        ]);
        
        $invoice = $stmt->fetch();
        
        if ($invoice) {
            $confidence = self::MATCH_HIGH;
            
            // Increase confidence if account matches
            if ($invoice['bank_account'] === ($transaction['account_number'] ?? '')) {
                $confidence = self::MATCH_EXACT;
            }
            
            // Decrease confidence if dates are far apart
            $daysDiff = abs(strtotime($transaction['date']) - strtotime($invoice['due_date'])) / 86400;
            if ($daysDiff > 30) {
                $confidence -= 10;
            }
            
            return [
                'invoice_id' => $invoice['id'],
                'invoice_number' => $invoice['invoice_number'],
                'client_name' => $invoice['client_name'],
                'confidence' => $confidence,
                'match_type' => 'exact_amount'
            ];
        }
        
        return null;
    }
    
    /**
     * Find reference match
     */
    private function findReferenceMatch($transaction)
    {
        if (empty($transaction['reference'])) {
            return null;
        }
        
        // Extract potential invoice number from reference
        $invoiceNumber = $this->extractInvoiceNumber($transaction['reference']);
        
        if (!$invoiceNumber) {
            return null;
        }
        
        $stmt = $this->db->prepare("
            SELECT 
                i.*,
                c.name as client_name
            FROM invoices i
            JOIN clients c ON i.client_id = c.id
            WHERE i.invoice_number = :invoice_number
            AND i.status IN ('sent', 'unpaid')
        ");
        
        $stmt->execute([':invoice_number' => $invoiceNumber]);
        $invoice = $stmt->fetch();
        
        if ($invoice) {
            $confidence = self::MATCH_HIGH;
            
            // Check if amount matches
            if (abs($invoice['total'] - $transaction['amount']) < 0.01) {
                $confidence = self::MATCH_EXACT;
            } elseif (abs($invoice['total'] - $transaction['amount']) > 1) {
                $confidence = self::MATCH_MEDIUM;
            }
            
            return [
                'invoice_id' => $invoice['id'],
                'invoice_number' => $invoice['invoice_number'],
                'client_name' => $invoice['client_name'],
                'confidence' => $confidence,
                'match_type' => 'reference'
            ];
        }
        
        return null;
    }
    
    /**
     * Find fuzzy match
     */
    private function findFuzzyMatch($transaction)
    {
        // Search by similar amount and date range
        $amountTolerance = $transaction['amount'] * 0.02; // 2% tolerance
        
        $stmt = $this->db->prepare("
            SELECT 
                i.*,
                c.name as client_name,
                c.bank_account,
                ABS(i.total - :amount) as amount_diff,
                ABS(DATEDIFF(:transaction_date, i.due_date)) as date_diff
            FROM invoices i
            JOIN clients c ON i.client_id = c.id
            WHERE i.total BETWEEN :min_amount AND :max_amount
            AND i.status IN ('sent', 'unpaid')
            AND i.issue_date <= :transaction_date
            ORDER BY 
                amount_diff + (date_diff * 0.1),
                date_diff
            LIMIT 1
        ");
        
        $stmt->execute([
            ':amount' => $transaction['amount'],
            ':min_amount' => $transaction['amount'] - $amountTolerance,
            ':max_amount' => $transaction['amount'] + $amountTolerance,
            ':transaction_date' => $transaction['date']
        ]);
        
        $invoice = $stmt->fetch();
        
        if ($invoice) {
            $confidence = self::MATCH_LOW;
            
            // Adjust confidence based on differences
            if ($invoice['amount_diff'] < 0.01) {
                $confidence += 20;
            }
            
            if ($invoice['date_diff'] < 7) {
                $confidence += 10;
            }
            
            // Check description similarity
            if (!empty($transaction['description']) && !empty($invoice['client_name'])) {
                $similarity = $this->calculateStringSimilarity(
                    strtolower($transaction['description']),
                    strtolower($invoice['client_name'])
                );
                
                if ($similarity > 0.7) {
                    $confidence += 15;
                }
            }
            
            return [
                'invoice_id' => $invoice['id'],
                'invoice_number' => $invoice['invoice_number'],
                'client_name' => $invoice['client_name'],
                'confidence' => min($confidence, self::MATCH_MEDIUM),
                'match_type' => 'fuzzy',
                'amount_diff' => $invoice['amount_diff'],
                'date_diff' => $invoice['date_diff']
            ];
        }
        
        return null;
    }
    
    /**
     * Find possible matches
     */
    private function findPossibleMatches($transaction)
    {
        $matches = [];
        
        // Search by amount range
        $amountTolerance = $transaction['amount'] * 0.05; // 5% tolerance
        
        $stmt = $this->db->prepare("
            SELECT 
                i.*,
                c.name as client_name,
                ABS(i.total - :amount) as amount_diff
            FROM invoices i
            JOIN clients c ON i.client_id = c.id
            WHERE i.total BETWEEN :min_amount AND :max_amount
            AND i.status IN ('sent', 'unpaid')
            AND i.issue_date <= :transaction_date
            ORDER BY amount_diff
            LIMIT 5
        ");
        
        $stmt->execute([
            ':amount' => $transaction['amount'],
            ':min_amount' => $transaction['amount'] - $amountTolerance,
            ':max_amount' => $transaction['amount'] + $amountTolerance,
            ':transaction_date' => $transaction['date']
        ]);
        
        $invoices = $stmt->fetchAll();
        
        foreach ($invoices as $invoice) {
            $confidence = self::MATCH_LOW;
            
            if ($invoice['amount_diff'] < 1) {
                $confidence += 10;
            }
            
            $matches[] = [
                'invoice_id' => $invoice['id'],
                'invoice_number' => $invoice['invoice_number'],
                'client_name' => $invoice['client_name'],
                'confidence' => $confidence,
                'match_type' => 'possible',
                'amount_diff' => $invoice['amount_diff']
            ];
        }
        
        return $matches;
    }
    
    /**
     * Create payment from bank transaction
     */
    public function createPayment($transaction, $match)
    {
        $db = Flight::db();
        $db->beginTransaction();
        
        try {
            // Create payment record
            $paymentData = [
                'invoice_id' => $match['invoice_id'],
                'amount' => $transaction['amount'],
                'payment_date' => $transaction['date'],
                'payment_method' => 'bank_transfer',
                'reference' => $transaction['reference'] ?? null,
                'bank_reference' => $transaction['transaction_id'] ?? null,
                'notes' => 'Auto-matched from bank reconciliation (Confidence: ' . $match['confidence'] . '%)'
            ];
            
            $paymentId = $this->payment->create($paymentData);
            
            // Update invoice status
            $this->invoice->applyPayment($match['invoice_id'], $transaction['amount']);
            
            // Create reconciliation record
            $stmt = $db->prepare("
                INSERT INTO bank_reconciliations (
                    transaction_date, transaction_reference, transaction_amount,
                    account_number, description, payment_id, invoice_id,
                    match_confidence, match_type, reconciled_at, reconciled_by
                ) VALUES (
                    :transaction_date, :transaction_reference, :transaction_amount,
                    :account_number, :description, :payment_id, :invoice_id,
                    :match_confidence, :match_type, NOW(), :reconciled_by
                )
            ");
            
            $stmt->execute([
                ':transaction_date' => $transaction['date'],
                ':transaction_reference' => $transaction['transaction_id'] ?? null,
                ':transaction_amount' => $transaction['amount'],
                ':account_number' => $transaction['account_number'] ?? null,
                ':description' => $transaction['description'] ?? null,
                ':payment_id' => $paymentId,
                ':invoice_id' => $match['invoice_id'],
                ':match_confidence' => $match['confidence'],
                ':match_type' => $match['match_type'],
                ':reconciled_by' => $_SESSION['user_id'] ?? 1
            ]);
            
            $db->commit();
            
            return $paymentId;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Import CSV bank statement
     */
    private function importCsv($file)
    {
        $transactions = [];
        $handle = fopen($file, 'r');
        
        if (!$handle) {
            throw new Exception('Cannot open CSV file');
        }
        
        // Skip header
        $header = fgetcsv($handle);
        
        // Detect CSV format based on header
        $format = $this->detectCsvFormat($header);
        
        while (($row = fgetcsv($handle)) !== false) {
            $transaction = $this->parseCsvRow($row, $format);
            if ($transaction && $transaction['amount'] > 0) {
                $transactions[] = $transaction;
            }
        }
        
        fclose($handle);
        
        return $transactions;
    }
    
    /**
     * Import MT940 bank statement
     */
    private function importMt940($file)
    {
        // MT940 is a SWIFT standard format
        // Implementation would parse the specific format
        // For now, return empty array
        return [];
    }
    
    /**
     * Import CAMT.053 bank statement
     */
    private function importCamt053($file)
    {
        // CAMT.053 is an ISO 20022 XML format
        // Implementation would parse the XML
        // For now, return empty array
        return [];
    }
    
    /**
     * Detect CSV format from header
     */
    private function detectCsvFormat($header)
    {
        $headerStr = implode(',', array_map('strtolower', $header));
        
        // Common bank CSV formats
        if (strpos($headerStr, 'amount') !== false && strpos($headerStr, 'date') !== false) {
            return 'generic';
        } elseif (strpos($headerStr, 'montant') !== false && strpos($headerStr, 'date') !== false) {
            return 'french';
        }
        
        return 'unknown';
    }
    
    /**
     * Parse CSV row based on format
     */
    private function parseCsvRow($row, $format)
    {
        switch ($format) {
            case 'generic':
                return [
                    'date' => $this->parseDate($row[0]),
                    'description' => $row[1] ?? '',
                    'reference' => $row[2] ?? '',
                    'amount' => $this->parseAmount($row[3] ?? 0),
                    'account_number' => $row[4] ?? '',
                    'transaction_id' => uniqid('txn_')
                ];
                
            case 'french':
                return [
                    'date' => $this->parseDate($row[0]),
                    'description' => $row[1] ?? '',
                    'reference' => $row[2] ?? '',
                    'amount' => $this->parseAmount($row[3] ?? 0),
                    'account_number' => $row[4] ?? '',
                    'transaction_id' => uniqid('txn_')
                ];
                
            default:
                // Try to parse with best guess
                return [
                    'date' => $this->parseDate($row[0]),
                    'description' => $row[1] ?? '',
                    'reference' => '',
                    'amount' => $this->parseAmount($row[2] ?? $row[3] ?? 0),
                    'account_number' => '',
                    'transaction_id' => uniqid('txn_')
                ];
        }
    }
    
    /**
     * Extract invoice number from reference
     */
    private function extractInvoiceNumber($reference)
    {
        // Try different patterns
        $patterns = [
            '/INV[-\/]?(\d{4}[-\/]\d{4})/i',  // INV-2024-0001
            '/FACT[-\/]?(\d{4}[-\/]\d{4})/i',  // FACT-2024-0001
            '/(?:INV|INVOICE|FACT|FACTURE)[\s#]*(\d+)/i',  // INV 12345
            '/(\d{4}[-\/]\d{4,})/i'  // 2024-0001
        ];
        
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $reference, $matches)) {
                return $matches[1];
            }
        }
        
        return null;
    }
    
    /**
     * Calculate string similarity
     */
    private function calculateStringSimilarity($str1, $str2)
    {
        $len1 = strlen($str1);
        $len2 = strlen($str2);
        
        if ($len1 == 0 || $len2 == 0) {
            return 0;
        }
        
        // Use Levenshtein distance
        $distance = levenshtein($str1, $str2);
        $maxLen = max($len1, $len2);
        
        return 1 - ($distance / $maxLen);
    }
    
    /**
     * Parse date from various formats
     */
    private function parseDate($dateStr)
    {
        $formats = [
            'Y-m-d',
            'd/m/Y',
            'd-m-Y',
            'm/d/Y',
            'd.m.Y'
        ];
        
        foreach ($formats as $format) {
            $date = \DateTime::createFromFormat($format, $dateStr);
            if ($date !== false) {
                return $date->format('Y-m-d');
            }
        }
        
        // Try strtotime as fallback
        $timestamp = strtotime($dateStr);
        if ($timestamp !== false) {
            return date('Y-m-d', $timestamp);
        }
        
        return date('Y-m-d');
    }
    
    /**
     * Parse amount from various formats
     */
    private function parseAmount($amountStr)
    {
        // Remove currency symbols and spaces
        $amount = preg_replace('/[^\d,.-]/', '', $amountStr);
        
        // Handle European format (comma as decimal separator)
        if (strpos($amount, ',') !== false && strpos($amount, '.') !== false) {
            // Both separators present, assume comma is decimal
            $amount = str_replace('.', '', $amount);
            $amount = str_replace(',', '.', $amount);
        } elseif (substr_count($amount, ',') == 1 && strpos($amount, '.') === false) {
            // Only comma present, likely decimal separator
            $amount = str_replace(',', '.', $amount);
        }
        
        return floatval($amount);
    }
    
    /**
     * Get reconciliation summary
     */
    public function getReconciliationSummary($startDate, $endDate)
    {
        $stmt = $this->db->prepare("
            SELECT 
                COUNT(*) as total_transactions,
                SUM(transaction_amount) as total_amount,
                SUM(CASE WHEN payment_id IS NOT NULL THEN 1 ELSE 0 END) as reconciled_count,
                SUM(CASE WHEN payment_id IS NOT NULL THEN transaction_amount ELSE 0 END) as reconciled_amount,
                AVG(match_confidence) as avg_confidence
            FROM bank_reconciliations
            WHERE transaction_date BETWEEN :start_date AND :end_date
        ");
        
        $stmt->execute([
            ':start_date' => $startDate,
            ':end_date' => $endDate
        ]);
        
        return $stmt->fetch();
    }
}