<?php
/**
 * Clear all caches and redirect to invoice page
 */

// Prevent any caching of this page
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: 0");

// Clear opcache if available
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✓ OPcache cleared<br>";
}

// Clear Twig cache
$twigCacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($twigCacheDir)) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($twigCacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($files as $fileinfo) {
        $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
        $todo($fileinfo->getRealPath());
    }
    echo "✓ Twig cache cleared<br>";
}

// Add timestamp to force fresh load
$timestamp = time();
$invoiceUrl = "http://localhost/fit/public/invoices/create?_t=" . $timestamp;

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Cache Clear and Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .info {
            background: #cce5ff;
            color: #004085;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        button {
            background: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
            text-decoration: none;
        }
        button:hover {
            background: #218838;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Cache Cleared!</h1>
        
        <div class="success">
            <strong>✅ Server-side caches have been cleared</strong>
        </div>
        
        <div class="warning">
            <h3>⚠️ Browser Cache Still Needs Clearing</h3>
            <p>Your browser is still showing the cached JavaScript file with the error.</p>
            <p><strong>Please do ONE of the following:</strong></p>
            
            <h4>Option 1: Force Refresh (Easiest)</h4>
            <ol>
                <li>Click the button below to open the invoice page</li>
                <li>Once on the page, press <code>Ctrl + F5</code> (Windows) or <code>Cmd + Shift + R</code> (Mac)</li>
            </ol>
            
            <h4>Option 2: Developer Tools</h4>
            <ol>
                <li>Open Developer Tools (F12)</li>
                <li>Go to Network tab</li>
                <li>Check "Disable cache"</li>
                <li>Keep DevTools open and refresh the page</li>
            </ol>
            
            <h4>Option 3: New Incognito Window</h4>
            <ol>
                <li>Open a new incognito/private browser window</li>
                <li>Visit the invoice page there</li>
            </ol>
        </div>
        
        <a href="<?php echo $invoiceUrl; ?>" target="_blank">
            <button>Open Invoice Page (New Tab)</button>
        </a>
        
        <div class="info">
            <strong>What was fixed:</strong>
            <ul>
                <li>Template expressions inside JavaScript backticks</li>
                <li>All instances of <code>{{ __('...') }}</code> in template literals</li>
                <li>All instances of <code>{{ base_url }}</code> in template literals</li>
            </ul>
            <p>The syntax error at line 3638 has been completely fixed in the source code.</p>
        </div>
    </div>
</body>
</html>