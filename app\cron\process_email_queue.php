<?php
/**
 * Email Queue Processor
 * Run this script via cron to process pending emails
 * 
 * Suggested cron entry:
 * * * * * * php /path/to/fit/app/cron/process_email_queue.php
 */

// Bootstrap the application
require_once dirname(__DIR__, 2) . '/vendor/autoload.php';
require_once dirname(__DIR__, 2) . '/app/bootstrap.php';

use App\Services\EmailQueueService;

// Check if running from CLI
if (php_sapi_name() !== 'cli') {
    die('This script must be run from the command line');
}

// Get command line options
$options = getopt('', ['limit:', 'dry-run', 'verbose']);

$limit = isset($options['limit']) ? (int)$options['limit'] : 50;
$dryRun = isset($options['dry-run']);
$verbose = isset($options['verbose']);

if ($verbose) {
    echo "Email Queue Processor\n";
    echo "====================\n";
    echo "Limit: $limit emails\n";
    echo "Mode: " . ($dryRun ? 'DRY RUN' : 'LIVE') . "\n";
    echo "Starting at: " . date('Y-m-d H:i:s') . "\n\n";
}

try {
    // Create queue service
    $queueService = new EmailQueueService();
    
    if ($dryRun) {
        // Just show what would be processed
        echo "DRY RUN: Would process up to $limit emails from the queue\n";
        
        // Get queue stats
        $stats = $queueService->getQueueStats();
        echo "\nCurrent Queue Status:\n";
        foreach ($stats['queue_by_status'] as $status => $count) {
            echo "  - $status: $count emails\n";
        }
        
    } else {
        // Process the queue
        $results = $queueService->processQueue($limit);
        
        if ($verbose) {
            echo "Processing Results:\n";
            echo "  - Processed: {$results['processed']} emails\n";
            echo "  - Sent: {$results['sent']} emails\n";
            echo "  - Failed: {$results['failed']} emails\n";
            
            if (!empty($results['errors'])) {
                echo "\nErrors:\n";
                foreach ($results['errors'] as $error) {
                    echo "  - $error\n";
                }
            }
        }
        
        // Log results
        error_log(sprintf(
            'Email Queue: Processed %d, Sent %d, Failed %d',
            $results['processed'],
            $results['sent'],
            $results['failed']
        ));
    }
    
    // Clean old entries (older than 30 days)
    if (!$dryRun) {
        $cleaned = $queueService->cleanOldEntries(30);
        if ($verbose && $cleaned > 0) {
            echo "\nCleaned $cleaned old queue entries\n";
        }
    }
    
    if ($verbose) {
        echo "\nCompleted at: " . date('Y-m-d H:i:s') . "\n";
    }
    
} catch (Exception $e) {
    $errorMsg = 'Email Queue Processor Error: ' . $e->getMessage();
    error_log($errorMsg);
    
    if ($verbose) {
        echo "\nERROR: $errorMsg\n";
    }
    
    exit(1);
}

exit(0);