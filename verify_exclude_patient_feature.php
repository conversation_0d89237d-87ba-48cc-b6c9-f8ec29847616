<?php
/**
 * Verify that the exclude patient line feature is fully implemented
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

echo "=== Verifying Exclude Patient Line Feature ===\n\n";

$results = [];

// 1. Check database column
try {
    $db = Flight::db();
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $results['database_column'] = in_array('exclude_patient_line', $columns);
} catch (Exception $e) {
    $results['database_column'] = false;
}

// 2. Check UI template
$templateFile = __DIR__ . '/app/views/retrocession/data-entry-modern.twig';
$templateContent = file_get_contents($templateFile);
$results['ui_checkbox'] = strpos($templateContent, 'name="exclude_patient_line"') !== false;
$results['ui_label'] = strpos($templateContent, 'retrocession.exclude_patient_line') !== false;

// 3. Check translations
$enTransFile = __DIR__ . '/app/lang/en/retrocession.php';
$enTrans = include $enTransFile;
$results['en_translation'] = isset($enTrans['exclude_patient_line']);

$frTransFile = __DIR__ . '/app/lang/fr/retrocession.php';
if (file_exists($frTransFile)) {
    $frTrans = include $frTransFile;
    $results['fr_translation'] = isset($frTrans['exclude_patient_line']);
} else {
    $results['fr_translation'] = 'File not found';
}

// 4. Check RetrocessionCalculator
$calculatorFile = __DIR__ . '/app/services/RetrocessionCalculator.php';
$calculatorContent = file_get_contents($calculatorFile);
$results['calculator_logic'] = strpos($calculatorContent, 'exclude_patient_line') !== false;

// 5. Check controller save method
$controllerFile = __DIR__ . '/app/controllers/RetrocessionController.php';
$controllerContent = file_get_contents($controllerFile);
$results['controller_save'] = strpos($controllerContent, 'exclude_patient_line') !== false;

// Display results
echo "Feature Implementation Status:\n";
echo "-----------------------------\n";
foreach ($results as $check => $status) {
    $statusText = $status === true ? '✓ PASS' : ($status === false ? '✗ FAIL' : '⚠ ' . $status);
    $checkName = str_replace('_', ' ', ucfirst($check));
    echo sprintf("%-25s: %s\n", $checkName, $statusText);
}

// Summary
$passed = array_filter($results, function($v) { return $v === true; });
$total = count($results);
$passCount = count($passed);

echo "\n-----------------------------\n";
echo "Summary: $passCount/$total checks passed\n";

if ($passCount === $total) {
    echo "\n✅ The exclude patient line feature is FULLY IMPLEMENTED!\n";
    echo "\nHow to use:\n";
    echo "1. Go to Retrocession > Data Entry for any practitioner\n";
    echo "2. Check the 'Exclude patient line' checkbox\n";
    echo "3. Save the data entry\n";
    echo "4. When generating the invoice, the patient line will be excluded\n";
} else {
    echo "\n⚠️  Some components are missing. Please check the failed items above.\n";
}

// Check for recent usage
echo "\n=== Recent Usage ===\n";
try {
    $stmt = $db->query("
        SELECT COUNT(*) as count 
        FROM retrocession_data_entry 
        WHERE exclude_patient_line = 1
    ");
    $count = $stmt->fetchColumn();
    echo "Entries with patient line excluded: $count\n";
    
    if ($count > 0) {
        $stmt = $db->query("
            SELECT practitioner_id, period_month, period_year, status
            FROM retrocession_data_entry 
            WHERE exclude_patient_line = 1
            ORDER BY id DESC
            LIMIT 5
        ");
        $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "\nRecent entries:\n";
        foreach ($entries as $entry) {
            echo sprintf("- Practitioner %d, Period %02d/%d, Status: %s\n",
                $entry['practitioner_id'],
                $entry['period_month'],
                $entry['period_year'],
                $entry['status']
            );
        }
    }
} catch (Exception $e) {
    echo "Could not check usage: " . $e->getMessage() . "\n";
}