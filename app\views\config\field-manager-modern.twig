{% extends "base-modern.twig" %}

{% block title %}{{ __('config.field_management') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.field_management') }} - {{ modules[current_module] }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFieldModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_custom_field') }}
            </button>
        </div>
    </div>

    <!-- Module Selector Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <label class="form-label">{{ __('config.select_module') }}</label>
                    <select class="form-select" id="module-selector">
                        {% for key, label in modules %}
                        <option value="{{ key }}" {% if key == current_module %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6 text-md-end mt-3 mt-md-0">
                    <p class="text-muted mb-0">
                        <i class="bi bi-info-circle me-1"></i>
                        {{ __('config.manage_module_fields') }}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs -->
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link active" data-bs-toggle="tab" data-bs-target="#list-fields" role="tab">
                <i class="bi bi-list-ul me-2"></i>{{ __('config.list_view_fields') }}
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" data-bs-target="#form-fields" role="tab">
                <i class="bi bi-input-cursor-text me-2"></i>{{ __('config.form_view_fields') }}
            </a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-bs-toggle="tab" data-bs-target="#all-fields" role="tab">
                <i class="bi bi-grid-3x3-gap me-2"></i>{{ __('config.all_fields') }}
            </a>
        </li>
    </ul>

    <div class="tab-content">
        <!-- List View Fields -->
        <div class="tab-pane fade show active" id="list-fields" role="tabpanel">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="mb-3">{{ __('config.visible_in_list') }}</h5>
                    <p class="text-muted">{{ __('config.drag_to_reorder') }}</p>
                    
                    <div class="row">
                        <div class="col-md-5">
                            <h6 class="mb-3">{{ __('config.available_fields') }}</h6>
                            <ul class="list-group sortable-list" id="available-list-fields">
                                {% for field in all_fields %}
                                {% if not visibility_map.list[field.field_name] %}
                                <li class="list-group-item d-flex justify-content-between align-items-center" data-field="{{ field.field_name }}">
                                    <div>
                                        <i class="bi bi-grip-vertical me-2"></i>
                                        {{ __(field.field_label) }}
                                    </div>
                                    {% if field.is_system %}
                                        <span class="badge bg-info">{{ __('config.system') }}</span>
                                    {% elseif field.field_source == 'custom' %}
                                        <span class="badge bg-success">{{ __('config.custom') }}</span>
                                    {% endif %}
                                </li>
                                {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        
                        <div class="col-md-2 text-center d-flex flex-column justify-content-center">
                            <button type="button" class="btn btn-sm btn-primary mb-3" onclick="moveFields('list', 'add')">
                                <i class="bi bi-arrow-right"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" onclick="moveFields('list', 'remove')">
                                <i class="bi bi-arrow-left"></i>
                            </button>
                        </div>
                        
                        <div class="col-md-5">
                            <h6 class="mb-3">{{ __('config.visible_fields') }}</h6>
                            <ul class="list-group sortable-list" id="visible-list-fields">
                                {% for field in all_fields %}
                                {% if visibility_map.list[field.field_name] %}
                                <li class="list-group-item d-flex justify-content-between align-items-center" data-field="{{ field.field_name }}">
                                    <div>
                                        <i class="bi bi-grip-vertical me-2"></i>
                                        {{ __(field.field_label) }}
                                    </div>
                                    {% if field.is_system %}
                                        <span class="badge bg-info">{{ __('config.system') }}</span>
                                    {% elseif field.field_source == 'custom' %}
                                        <span class="badge bg-success">{{ __('config.custom') }}</span>
                                    {% endif %}
                                </li>
                                {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="button" class="btn btn-primary" onclick="saveVisibility('list')">
                            <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }} {{ __('config.list_view_settings') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Form View Fields -->
        <div class="tab-pane fade" id="form-fields" role="tabpanel">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="mb-3">{{ __('config.visible_in_form') }}</h5>
                    <p class="text-muted">{{ __('config.drag_to_reorder') }}</p>
                    
                    <div class="row">
                        <div class="col-md-5">
                            <h6 class="mb-3">{{ __('config.available_fields') }}</h6>
                            <ul class="list-group sortable-list" id="available-form-fields">
                                {% for field in all_fields %}
                                {% if not visibility_map.form[field.field_name] %}
                                <li class="list-group-item d-flex justify-content-between align-items-center" data-field="{{ field.field_name }}">
                                    <div>
                                        <i class="bi bi-grip-vertical me-2"></i>
                                        {{ __(field.field_label) }}
                                    </div>
                                    {% if field.is_system %}
                                        <span class="badge bg-info">{{ __('config.system') }}</span>
                                    {% elseif field.field_source == 'custom' %}
                                        <span class="badge bg-success">{{ __('config.custom') }}</span>
                                    {% endif %}
                                </li>
                                {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                        
                        <div class="col-md-2 text-center d-flex flex-column justify-content-center">
                            <button type="button" class="btn btn-sm btn-primary mb-3" onclick="moveFields('form', 'add')">
                                <i class="bi bi-arrow-right"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-primary" onclick="moveFields('form', 'remove')">
                                <i class="bi bi-arrow-left"></i>
                            </button>
                        </div>
                        
                        <div class="col-md-5">
                            <h6 class="mb-3">{{ __('config.visible_fields') }}</h6>
                            <ul class="list-group sortable-list" id="visible-form-fields">
                                {% for field in all_fields %}
                                {% if visibility_map.form[field.field_name] %}
                                <li class="list-group-item d-flex justify-content-between align-items-center" data-field="{{ field.field_name }}">
                                    <div>
                                        <i class="bi bi-grip-vertical me-2"></i>
                                        {{ __(field.field_label) }}
                                    </div>
                                    {% if field.is_system %}
                                        <span class="badge bg-info">{{ __('config.system') }}</span>
                                    {% elseif field.field_source == 'custom' %}
                                        <span class="badge bg-success">{{ __('config.custom') }}</span>
                                    {% endif %}
                                </li>
                                {% endif %}
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <button type="button" class="btn btn-primary" onclick="saveVisibility('form')">
                            <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }} {{ __('config.form_view_settings') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- All Fields -->
        <div class="tab-pane fade" id="all-fields" role="tabpanel">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="mb-3">{{ __('config.all_available_fields') }}</h5>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('config.field_name') }}</th>
                                    <th>{{ __('config.field_label') }}</th>
                                    <th>{{ __('config.field_type') }}</th>
                                    <th>{{ __('config.source') }}</th>
                                    <th>{{ __('config.visible_in_list') }}</th>
                                    <th>{{ __('config.visible_in_form') }}</th>
                                    <th>{{ __('common.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in all_fields %}
                                <tr>
                                    <td><code>{{ field.field_name }}</code></td>
                                    <td>{{ __(field.field_label) }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ field.field_type }}</span>
                                    </td>
                                    <td>
                                        {% if field.is_system %}
                                            <span class="badge bg-info">{{ __('config.system') }}</span>
                                        {% elseif field.field_source == 'custom' %}
                                            <span class="badge bg-success">{{ __('config.custom') }}</span>
                                        {% else %}
                                            <span class="badge bg-primary">{{ __('config.core') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if visibility_map.list[field.field_name] %}
                                            <i class="bi bi-check-circle-fill text-success"></i>
                                        {% else %}
                                            <i class="bi bi-x-circle text-muted"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if visibility_map.form[field.field_name] %}
                                            <i class="bi bi-check-circle-fill text-success"></i>
                                        {% else %}
                                            <i class="bi bi-x-circle text-muted"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if field.field_source == 'custom' %}
                                            <button class="btn btn-sm btn-outline-primary" onclick="editField('{{ field.id }}')">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteField('{{ field.id }}')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Custom Field Modal -->
<div class="modal fade" id="addFieldModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="addFieldForm" onsubmit="return addCustomField(event)">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('config.add_custom_field') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="field_name" class="form-label">{{ __('config.field_name') }} *</label>
                        <input type="text" class="form-control" id="field_name" name="field_name" required pattern="[a-z0-9_]+" maxlength="50">
                        <small class="text-muted">{{ __('config.field_name_hint') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="field_label" class="form-label">{{ __('config.field_label') }} *</label>
                        <input type="text" class="form-control" id="field_label" name="field_label" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="field_type" class="form-label">{{ __('config.field_type') }} *</label>
                        <select class="form-select" id="field_type" name="field_type" required onchange="toggleFieldOptions()">
                            <option value="">{{ __('common.select') }}</option>
                            <option value="text">{{ __('config.type_text') }}</option>
                            <option value="textarea">{{ __('config.type_textarea') }}</option>
                            <option value="number">{{ __('config.type_number') }}</option>
                            <option value="email">{{ __('config.type_email') }}</option>
                            <option value="phone">{{ __('config.type_phone') }}</option>
                            <option value="date">{{ __('config.type_date') }}</option>
                            <option value="datetime">{{ __('config.type_datetime') }}</option>
                            <option value="select">{{ __('config.type_select') }}</option>
                            <option value="checkbox">{{ __('config.type_checkbox') }}</option>
                            <option value="url">{{ __('config.type_url') }}</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="field_options_container" style="display: none;">
                        <label for="field_options" class="form-label">{{ __('config.field_options') }}</label>
                        <textarea class="form-control" id="field_options" name="field_options" rows="3"></textarea>
                        <small class="text-muted">{{ __('config.field_options_hint') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="default_value" class="form-label">{{ __('config.default_value') }}</label>
                        <input type="text" class="form-control" id="default_value" name="default_value">
                    </div>
                    
                    <div class="mb-3">
                        <label for="placeholder" class="form-label">{{ __('config.placeholder') }}</label>
                        <input type="text" class="form-control" id="placeholder" name="placeholder">
                    </div>
                    
                    <div class="mb-3">
                        <label for="help_text" class="form-label">{{ __('config.help_text') }}</label>
                        <input type="text" class="form-control" id="help_text" name="help_text">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_required" name="is_required" value="1">
                                <label class="form-check-label" for="is_required">{{ __('config.is_required') }}</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_searchable" name="is_searchable" value="1">
                                <label class="form-check-label" for="is_searchable">{{ __('config.is_searchable') }}</label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_sortable" name="is_sortable" value="1">
                                <label class="form-check-label" for="is_sortable">{{ __('config.is_sortable') }}</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// Initialize sortable lists
document.addEventListener('DOMContentLoaded', function() {
    // Make all sortable lists draggable
    const sortableLists = document.querySelectorAll('.sortable-list');
    sortableLists.forEach(list => {
        new Sortable(list, {
            group: list.id.includes('list') ? 'list-fields' : 'form-fields',
            animation: 150,
            ghostClass: 'bg-light'
        });
    });
});

// Module selector change
document.getElementById('module-selector').addEventListener('change', function() {
    window.location.href = '{{ base_url }}/config/fields/' + this.value;
});

// Toggle field options for select type
function toggleFieldOptions() {
    const fieldType = document.getElementById('field_type').value;
    const optionsContainer = document.getElementById('field_options_container');
    optionsContainer.style.display = (fieldType === 'select') ? 'block' : 'none';
}

// Move fields between lists
function moveFields(viewType, action) {
    let sourceId, targetId;
    
    if (action === 'add') {
        sourceId = `available-${viewType}-fields`;
        targetId = `visible-${viewType}-fields`;
    } else {
        sourceId = `visible-${viewType}-fields`;
        targetId = `available-${viewType}-fields`;
    }
    
    const sourceList = document.getElementById(sourceId);
    const targetList = document.getElementById(targetId);
    
    // Get selected items (you might want to add selection functionality)
    // For now, this is a placeholder
    toastr.info('Select fields to move');
}

// Save visibility settings
function saveVisibility(viewType) {
    const visibleFields = [];
    const fieldsList = document.getElementById(`visible-${viewType}-fields`);
    const items = fieldsList.querySelectorAll('li');
    
    items.forEach((item, index) => {
        visibleFields.push({
            field_name: item.dataset.field,
            display_order: index + 1
        });
    });
    
    // Send AJAX request
    fetch('{{ base_url }}/config/fields/visibility', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            module: '{{ current_module }}',
            view_type: viewType,
            fields: visibleFields,
            csrf_token: '{{ csrf_token }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message || '{{ __("config.visibility_saved") }}');
        } else {
            toastr.error(data.error || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('{{ __("common.error_occurred") }}');
    });
}

// Add custom field
function addCustomField(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    formData.append('module', '{{ current_module }}');
    formData.append('csrf_token', '{{ csrf_token }}');
    
    fetch('{{ base_url }}/config/fields/add', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success(data.message || '{{ __("config.field_created") }}');
            bootstrap.Modal.getInstance(document.getElementById('addFieldModal')).hide();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            toastr.error(data.error || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('{{ __("common.error_occurred") }}');
    });
    
    return false;
}

// Edit field
function editField(fieldId) {
    // TODO: Implement edit functionality
    toastr.info('Edit functionality coming soon');
}

// Delete field
function deleteField(fieldId) {
    if (confirm('{{ __("common.confirm_delete") }}')) {
        fetch(`{{ base_url }}/config/fields/${fieldId}`, {
            method: 'DELETE',
            headers: {
                'X-CSRF-Token': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                csrf_token: '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message || '{{ __("config.field_deleted") }}');
                setTimeout(() => window.location.reload(), 1000);
            } else {
                toastr.error(data.error || '{{ __("common.error_occurred") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ __("common.error_occurred") }}');
        });
    }
}
</script>

<style>
.sortable-list {
    min-height: 200px;
    max-height: 400px;
    overflow-y: auto;
}

.sortable-list .list-group-item {
    cursor: move;
}

.sortable-list .list-group-item:hover {
    background-color: #f8f9fa;
}

.bg-light {
    opacity: 0.5;
}
</style>
{% endblock %}