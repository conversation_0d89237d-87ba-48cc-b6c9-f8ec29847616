<!DOCTYPE html>
<html>
<head>
    <title>Test Column Rendering</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Column Rendering</h2>
        <div id="columnList" class="list-group"></div>
        
        <button class="btn btn-primary mt-3" onclick="testRender()">Test Render Columns</button>
    </div>

    <script>
    // Simplified version of the column rendering
    const tableColumns = {
        invoice_items: [
            { id: 'description', name: 'Description', required: true },
            { id: 'reference', name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
            { id: 'quantity', name: 'Quantité' },
            { id: 'unit', name: 'Unité' },
            { id: 'unit_price', name: 'Prix unitaire' },
            { id: 'discount', name: 'Remise' },
            { id: 'vat_rate', name: 'Taux TVA' },
            { id: 'subtotal', name: 'Sous-total' },
            { id: 'total', name: 'Total', required: true }
        ]
    };
    
    // Test configuration from database
    const testConfig = [
        {"order": 0, "column": "description", "visible": true, "required": true},
        {"column": "reference", "visible": false, "order": 1, "required": false},
        {"order": 2, "column": "quantity", "visible": true, "required": false},
        {"column": "unit", "visible": false, "order": 3, "required": false},
        {"order": 4, "column": "unit_price", "visible": true, "required": false},
        {"order": 5, "column": "vat_rate", "visible": true, "required": false},
        {"order": 6, "column": "discount", "visible": false, "required": false},
        {"column": "subtotal", "visible": false, "order": 7, "required": false},
        {"order": 8, "column": "total", "visible": true, "required": true}
    ];
    
    function testRender() {
        console.log('Starting render test...');
        console.log('Table columns:', tableColumns.invoice_items);
        console.log('Config from DB:', testConfig);
        
        const columnList = document.getElementById('columnList');
        columnList.innerHTML = '';
        
        // Create config map
        const configMap = {};
        testConfig.forEach(config => {
            configMap[config.column] = config;
        });
        
        console.log('Config map:', configMap);
        
        // Merge and render
        const columns = tableColumns.invoice_items;
        const mergedColumns = columns.map(col => {
            const config = configMap[col.id] || {};
            return {
                ...col,
                visible: config.visible !== undefined ? config.visible : true,
                order: config.order !== undefined ? config.order : columns.findIndex(c => c.id === col.id)
            };
        });
        
        mergedColumns.sort((a, b) => a.order - b.order);
        
        console.log('Merged columns:', mergedColumns);
        
        // Render each column
        mergedColumns.forEach((column, index) => {
            const item = document.createElement('div');
            item.className = 'list-group-item d-flex align-items-center';
            if (!column.visible) {
                item.classList.add('opacity-50');
            }
            
            item.innerHTML = `
                <div class="flex-grow-1">
                    <strong>${column.name}</strong>
                    ${column.required ? '<span class="badge bg-danger ms-2">Required</span>' : ''}
                    ${!column.visible ? '<span class="badge bg-secondary ms-2">Hidden</span>' : ''}
                </div>
                <div>
                    <span class="badge bg-primary">${index + 1}</span>
                </div>
            `;
            
            columnList.appendChild(item);
        });
        
        console.log('Render complete!');
    }
    </script>
</body>
</html>