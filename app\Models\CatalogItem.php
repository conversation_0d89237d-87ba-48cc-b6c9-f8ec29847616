<?php
namespace App\Models;

use App\Core\Model;
use App\Core\Collection;
use App\Helpers\MoneyHelper;

class CatalogItem extends Model
{
    protected $table = 'catalog_items';
    
    /**
     * Fields that contain monetary values
     */
    protected $monetary = [
        'unit_price'
    ];
    
    protected $fillable = [
        'code',
        'name',
        'category_id',
        'item_type',
        'description',
        'unit_price',
        'vat_rate_id',
        'is_stockable',
        'current_stock',
        'low_stock_alert',
        'quick_sale_button',
        'button_color',
        'button_order',
        'is_active',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'unit_price' => 'decimal:2',
        'is_stockable' => 'boolean',
        'current_stock' => 'integer',
        'low_stock_alert' => 'integer',
        'quick_sale_button' => 'boolean',
        'button_order' => 'integer',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get the category this item belongs to
     */
    public function getCategory()
    {
        if ($this->category_id) {
            return CatalogCategory::find($this->category_id);
        }
        return null;
    }
    
    /**
     * Get the VAT rate
     */
    public function getVatRate()
    {
        if ($this->vat_rate_id) {
            return ConfigVatRate::find($this->vat_rate_id);
        }
        return null;
    }
    
    /**
     * Check if item is in stock
     */
    public function isInStock()
    {
        if (!$this->is_stockable) {
            return true; // Non-stockable items are always "in stock"
        }
        
        return $this->current_stock > 0;
    }
    
    /**
     * Check if item has low stock
     */
    public function hasLowStock()
    {
        if (!$this->is_stockable) {
            return false;
        }
        
        return $this->current_stock <= $this->low_stock_alert;
    }
    
    /**
     * Get stock status
     */
    public function getStockStatus()
    {
        if (!$this->is_stockable) {
            return 'not_applicable';
        }
        
        if ($this->current_stock <= 0) {
            return 'out_of_stock';
        }
        
        if ($this->current_stock <= $this->low_stock_alert) {
            return 'low_stock';
        }
        
        return 'in_stock';
    }
    
    /**
     * Get stock status label
     */
    public function getStockStatusLabel()
    {
        $statuses = [
            'not_applicable' => 'N/A',
            'out_of_stock' => 'Out of Stock',
            'low_stock' => 'Low Stock',
            'in_stock' => 'In Stock'
        ];
        
        return $statuses[$this->getStockStatus()];
    }
    
    /**
     * Get stock status color
     */
    public function getStockStatusColor()
    {
        $colors = [
            'not_applicable' => 'secondary',
            'out_of_stock' => 'danger',
            'low_stock' => 'warning',
            'in_stock' => 'success'
        ];
        
        return $colors[$this->getStockStatus()];
    }
    
    /**
     * Adjust stock level
     */
    public function adjustStock($quantity, $type = 'adjustment', $reference = null, $userId = null)
    {
        if (!$this->is_stockable) {
            return false;
        }
        
        // Create stock movement record
        $movement = StockMovement::create([
            'item_id' => $this->id,
            'movement_type' => $type,
            'quantity' => $quantity,
            'reference_type' => $reference ? get_class($reference) : null,
            'reference_id' => $reference ? $reference->id : null,
            'created_by' => $userId ?: ($_SESSION['user']['id'] ?? null)
        ]);
        
        // Update current stock
        $this->current_stock += $quantity;
        $this->save();
        
        return $movement;
    }
    
    /**
     * Get VAT amount
     */
    public function getVatAmount()
    {
        $vatRate = $this->getVatRate();
        if ($vatRate && $vatRate->rate > 0) {
            return MoneyHelper::calculateTax($this->unit_price, $vatRate->rate, false);
        }
        return 0;
    }
    
    /**
     * Get price with VAT
     */
    public function getPriceWithVat()
    {
        $vatRate = $this->getVatRate();
        if ($vatRate && $vatRate->rate > 0) {
            $vatAmount = MoneyHelper::calculateTax($this->unit_price, $vatRate->rate, false);
            return MoneyHelper::round($this->unit_price + $vatAmount);
        }
        return MoneyHelper::round($this->unit_price);
    }
    
    /**
     * Format price
     */
    public function formatPrice($withVat = false)
    {
        $price = $withVat ? $this->getPriceWithVat() : $this->unit_price;
        return MoneyHelper::format($price, 'EUR', true);
    }
    
    /**
     * Get quick sale items
     */
    public static function getQuickSaleItems()
    {
        return static::where('quick_sale_button', '=', 1)
                    ->where('is_active', '=', 1)
                    ->orderBy('button_order')
                    ->get();
    }
    
    /**
     * Get low stock items
     */
    public static function getLowStockItems()
    {
        $db = self::db();
        $sql = "SELECT * FROM catalog_items 
                WHERE is_active = 1 
                AND is_stockable = 1 
                AND current_stock <= low_stock_alert
                ORDER BY current_stock ASC";
        $stmt = $db->query($sql);
        
        $results = [];
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $model = new static();
            $model->fill($row);
            $results[] = $model;
        }
        
        return $results;
    }
    
    /**
     * Search items
     */
    public static function search($term)
    {
        $db = self::db();
        $search = '%' . $term . '%';
        
        $sql = "SELECT * FROM catalog_items 
                WHERE (code LIKE :search 
                OR name LIKE :search 
                OR description LIKE :search)
                AND is_active = 1";
        
        $stmt = $db->prepare($sql);
        $stmt->execute(['search' => $search]);
        
        $results = [];
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $model = new static();
            $model->fill($row);
            $results[] = $model;
        }
        
        return new Collection($results);
    }
    
    /**
     * Generate unique code
     */
    public static function generateCode($prefix = 'ITEM')
    {
        $db = self::db();
        $sql = "SELECT code FROM catalog_items 
                WHERE code LIKE :prefix 
                ORDER BY code DESC 
                LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute(['prefix' => $prefix . '%']);
        $lastCode = $stmt->fetchColumn();
        
        if (!$lastCode) {
            return $prefix . '001';
        }
        
        // Extract number from last code
        $number = intval(substr($lastCode, strlen($prefix)));
        return $prefix . str_pad($number + 1, 3, '0', STR_PAD_LEFT);
    }
    
    /**
     * Load category relationship
     */
    public function loadCategory()
    {
        $this->category = $this->getCategory();
        return $this;
    }
    
    /**
     * Load VAT rate relationship
     */
    public function loadVatRate()
    {
        $this->vatRate = $this->getVatRate();
        return $this;
    }
}