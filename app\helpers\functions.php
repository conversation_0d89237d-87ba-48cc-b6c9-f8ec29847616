<?php

use App\Helpers\Language;

/**
 * Translation helper function
 * 
 * @param string $key Translation key
 * @param array $params Parameters to replace in the translation
 * @param string|null $language Language to use (null for current language)
 * @return string
 */
if (!function_exists('__')) {
    function __($key, $params = [], $language = null)
    {
        return Language::get($key, $params, $language);
    }
}

/**
 * Check if translation exists
 * 
 * @param string $key Translation key
 * @param string|null $language Language to check (null for current language)
 * @return bool
 */
if (!function_exists('trans_has')) {
    function trans_has($key, $language = null)
    {
        return Language::has($key, $language);
    }
}

/**
 * Get current language
 * 
 * @return string
 */
if (!function_exists('current_language')) {
    function current_language()
    {
        return Language::getCurrentLanguage();
    }
}

/**
 * Set current language
 * 
 * @param string $language
 * @return void
 */
if (!function_exists('set_language')) {
    function set_language($language)
    {
        Language::setLanguage($language);
    }
}

/**
 * Redirect to a URL with proper base URL handling
 * 
 * @param string $path The path to redirect to (e.g., '/login', '/users')
 * @param int $code HTTP response code
 * @return void
 */
if (!function_exists('redirect_to')) {
    function redirect_to($path, $code = 303)
    {
        $baseUrl = Flight::get('flight.base_url');
        
        // If path doesn't start with /, add it
        if (strpos($path, '/') !== 0) {
            $path = '/' . $path;
        }
        
        // Construct the full URL
        $url = $baseUrl . $path;
        
        // Use header redirect directly to avoid Flight's redirect issues
        header('Location: ' . $url, true, $code);
        exit;
    }
}

/**
 * Get system date format
 * 
 * @return string
 */
if (!function_exists('get_date_format')) {
    function get_date_format()
    {
        static $format = null;
        
        if ($format === null) {
            try {
                $db = Flight::db();
                $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'date_format' AND category = 'system'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $format = $result['value'] ?? 'd/m/Y';
            } catch (Exception $e) {
                $format = 'd/m/Y'; // Default format
            }
        }
        
        return $format;
    }
}

if (!function_exists('get_date_format_short')) {
    function get_date_format_short()
    {
        static $format = null;
        
        if ($format === null) {
            try {
                $db = Flight::db();
                $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'date_format_short' AND category = 'system'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                $format = $result['value'] ?? 'd/m';
            } catch (Exception $e) {
                $format = 'd/m'; // Default format
            }
        }
        
        return $format;
    }
}

/**
 * Format date using system format
 * 
 * @param string|DateTime $date
 * @param string|null $format Override format (null uses system format)
 * @return string
 */
if (!function_exists('format_date')) {
    function format_date($date, $format = null)
    {
        if (empty($date) || $date === '0000-00-00' || $date === '-0001-11-30') {
            return '';
        }
        
        if ($format === null) {
            $format = get_date_format();
        }
        
        if ($date instanceof DateTime) {
            return $date->format($format);
        }
        
        try {
            $dateObj = new DateTime($date);
            return $dateObj->format($format);
        } catch (Exception $e) {
            return '';
        }
    }
}

/**
 * Set a flash message
 * 
 * @param string $type Message type (success, error, warning, info)
 * @param string $message The message to display
 * @return void
 */
if (!function_exists('setMessage')) {
    function setMessage($type, $message)
    {
        if (!isset($_SESSION['flash_messages'])) {
            $_SESSION['flash_messages'] = [];
        }
        $_SESSION['flash_messages'][] = [
            'type' => $type,
            'message' => $message
        ];
    }
}

/**
 * Get and clear flash messages
 * 
 * @return array
 */
if (!function_exists('getMessages')) {
    function getMessages()
    {
        if (!isset($_SESSION['flash_messages'])) {
            return [];
        }
        $messages = $_SESSION['flash_messages'];
        unset($_SESSION['flash_messages']);
        return $messages;
    }
}