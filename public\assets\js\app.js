// Application JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Configure DataTables defaults for Bootstrap 5
    if (typeof $.fn.dataTable !== 'undefined') {
        $.extend(true, $.fn.dataTable.defaults, {
            dom: "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
                 "<'row'<'col-sm-12'tr>>" +
                 "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
            renderer: 'bootstrap',
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search...",
                lengthMenu: "_MENU_ records per page",
            }
        });
    }
    // AdminLTE 4 initialization
    if (typeof overlayscrollbarsGlobal !== 'undefined') {
        const { 
            OverlayScrollbars, 
            ScrollbarsHidingPlugin, 
            SizeObserverPlugin, 
            ClickScrollPlugin 
        } = overlayscrollbarsGlobal;

        // Initialize overlay scrollbars on sidebar
        const sidebarWrapper = document.querySelector('.sidebar-wrapper');
        if (sidebarWrapper) {
            OverlayScrollbars(sidebarWrapper, {
                scrollbars: {
                    autoHide: 'leave',
                    autoHideDelay: 200
                }
            });
        }
    }

    // Bootstrap 5.3 Components Initialization
    // Initialize all tooltips
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));

    // Initialize all popovers
    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl));

    // Initialize all toasts
    const toastElList = document.querySelectorAll('.toast');
    const toastList = [...toastElList].map(toastEl => new bootstrap.Toast(toastEl));

    // Handle dropdown on hover for desktop
    if (window.innerWidth > 992) {
        document.querySelectorAll('.navbar .dropdown').forEach(function(dropdown) {
            dropdown.addEventListener('mouseenter', function() {
                const dropdownToggle = this.querySelector('[data-bs-toggle="dropdown"]');
                if (dropdownToggle) {
                    bootstrap.Dropdown.getOrCreateInstance(dropdownToggle).show();
                }
            });
            
            dropdown.addEventListener('mouseleave', function() {
                const dropdownToggle = this.querySelector('[data-bs-toggle="dropdown"]');
                if (dropdownToggle) {
                    bootstrap.Dropdown.getOrCreateInstance(dropdownToggle).hide();
                }
            });
        });
    }

    // Set active class on current menu item
    const currentLocation = location.pathname;
    const menuItems = document.querySelectorAll('.nav-link');
    menuItems.forEach(item => {
        if (item.getAttribute('href') === currentLocation) {
            item.classList.add('active');
            // If it's in a treeview, expand parent
            const treeview = item.closest('.nav-treeview');
            if (treeview) {
                const parentItem = treeview.closest('.nav-item');
                if (parentItem) {
                    parentItem.classList.add('menu-open');
                    const parentLink = parentItem.querySelector('.nav-link');
                    if (parentLink) {
                        parentLink.classList.add('active');
                    }
                }
            }
        }
    });
});

// Standardized AJAX form handler
function setupAjaxForm(formSelector, options = {}) {
    const form = document.querySelector(formSelector);
    if (!form) return;
    
    form.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        // Clear previous errors
        form.querySelectorAll('.is-invalid').forEach(el => {
            el.classList.remove('is-invalid');
        });
        form.querySelectorAll('.invalid-feedback').forEach(el => {
            el.remove();
        });
        
        // Get submit button and disable it
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';
        
        try {
            const formData = new FormData(form);
            
            // Add CSRF token if not in form
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
            if (csrfToken && !formData.has('csrf_token')) {
                formData.append('csrf_token', csrfToken);
            }
            
            const response = await fetch(form.action || form.getAttribute('data-action'), {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            });
            
            const data = await response.json();
            
            if (response.ok && data.success) {
                // Success handling
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message || 'Operation completed successfully');
                } else if (typeof Swal !== 'undefined') {
                    await Swal.fire({
                        icon: 'success',
                        title: 'Success',
                        text: data.message || 'Operation completed successfully',
                        timer: 1500,
                        showConfirmButton: false
                    });
                }
                
                // Handle redirect or callback
                if (data.redirect) {
                    setTimeout(() => window.location.href = data.redirect, 1000);
                } else if (options.onSuccess) {
                    options.onSuccess(data);
                }
            } else {
                // Error handling
                handleFormErrors(form, data);
                
                const errorMessage = data.message || 'An error occurred. Please check the form and try again.';
                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                } else if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: 'Error',
                        text: errorMessage
                    });
                } else {
                    alert(errorMessage);
                }
            }
        } catch (error) {
            console.error('Form submission error:', error);
            const errorMessage = 'Network error. Please check your connection and try again.';
            
            if (typeof toastr !== 'undefined') {
                toastr.error(errorMessage);
            } else if (typeof Swal !== 'undefined') {
                Swal.fire({
                    icon: 'error',
                    title: 'Network Error',
                    text: errorMessage
                });
            } else {
                alert(errorMessage);
            }
        } finally {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalBtnText;
        }
    });
}

// Handle form validation errors
function handleFormErrors(form, data) {
    if (data.errors && typeof data.errors === 'object') {
        Object.keys(data.errors).forEach(fieldName => {
            const field = form.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');
                
                // Create error message
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = Array.isArray(data.errors[fieldName]) 
                    ? data.errors[fieldName][0] 
                    : data.errors[fieldName];
                
                // Insert after field or input group
                const inputGroup = field.closest('.input-group');
                if (inputGroup) {
                    inputGroup.parentNode.insertBefore(feedback, inputGroup.nextSibling);
                } else {
                    field.parentNode.insertBefore(feedback, field.nextSibling);
                }
                
                // Focus first error field
                if (fieldName === Object.keys(data.errors)[0]) {
                    field.focus();
                }
            }
        });
    }
}

// Enhanced file upload handler
function setupFileUpload(inputSelector, options = {}) {
    const input = document.querySelector(inputSelector);
    if (!input) return;
    
    const config = {
        maxSize: options.maxSize || 5 * 1024 * 1024, // 5MB default
        allowedTypes: options.allowedTypes || ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
        preview: options.preview || null,
        onSelect: options.onSelect || null
    };
    
    input.addEventListener('change', function(e) {
        const file = this.files[0];
        if (!file) return;
        
        // Validate file size
        if (file.size > config.maxSize) {
            const sizeMB = (config.maxSize / 1024 / 1024).toFixed(1);
            if (typeof toastr !== 'undefined') {
                toastr.error(`File size must be less than ${sizeMB}MB`);
            } else {
                alert(`File size must be less than ${sizeMB}MB`);
            }
            this.value = '';
            return;
        }
        
        // Validate file type
        if (config.allowedTypes.length && !config.allowedTypes.includes(file.type)) {
            const types = config.allowedTypes.map(t => t.split('/')[1]).join(', ');
            if (typeof toastr !== 'undefined') {
                toastr.error(`Please upload a valid file type: ${types}`);
            } else {
                alert(`Please upload a valid file type: ${types}`);
            }
            this.value = '';
            return;
        }
        
        // Show preview if element provided
        if (config.preview) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewEl = document.querySelector(config.preview);
                if (previewEl) {
                    if (previewEl.tagName === 'IMG') {
                        previewEl.src = e.target.result;
                    } else {
                        previewEl.style.backgroundImage = `url(${e.target.result})`;
                    }
                }
            };
            reader.readAsDataURL(file);
        }
        
        // Call custom handler
        if (config.onSelect) {
            config.onSelect(file);
        }
    });
}