{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.create_credit_note') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.create_credit_note') }}</h1>
        <a href="{{ base_url }}/invoices/{{ originalInvoice.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    <!-- Original Invoice Info -->
    <div class="alert alert-info">
        <h5 class="alert-heading">{{ __('invoices.reference_invoice') }}</h5>
        <p class="mb-2">
            <strong>{{ __('invoices.invoice_number') }}:</strong> {{ originalInvoice.invoice_number }}<br>
            <strong>{{ __('invoices.amount') }}:</strong> {{ currency }}{{ originalInvoice.total|number_format(2, ',', ' ') }}<br>
            <strong>{{ __('invoices.issue_date') }}:</strong> {{ originalInvoice.issue_date|date('d/m/Y') }}
        </p>
    </div>

    <form method="POST" action="{{ base_url }}/invoices/{{ originalInvoice.id }}/credit-note" id="creditNoteForm" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="card shadow-sm">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="bi bi-file-earmark-minus me-2"></i>{{ __('invoices.credit_note_details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label for="issue_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                        <input type="date" class="form-control" id="issue_date" name="issue_date" 
                               value="{{ 'now'|date('Y-m-d') }}" required>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="amount" class="form-label">{{ __('invoices.credit_amount') }} *</label>
                        <div class="input-group">
                            <span class="input-group-text">{{ currency }}</span>
                            <input type="number" class="form-control" id="amount" name="amount" 
                                   step="0.01" min="0.01" max="{{ maxAmount }}" required>
                        </div>
                        <small class="text-muted">{{ __('invoices.max_amount') }}: {{ currency }}{{ maxAmount|number_format(2, ',', ' ') }}</small>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <label for="credit_reason" class="form-label">{{ __('invoices.credit_reason') }} *</label>
                        <select class="form-select" id="credit_reason" name="credit_reason" required>
                            <option value="">{{ __('common.select') }}</option>
                            <option value="error">{{ __('invoices.credit_reason_error') }}</option>
                            <option value="return">{{ __('invoices.credit_reason_return') }}</option>
                            <option value="discount">{{ __('invoices.credit_reason_discount') }}</option>
                            <option value="cancellation">{{ __('invoices.credit_reason_cancellation') }}</option>
                            <option value="other">{{ __('invoices.credit_reason_other') }}</option>
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-12">
                        <label for="notes" class="form-label">{{ __('invoices.notes') }}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                  placeholder="{{ __('invoices.credit_note_notes_placeholder') }}"></textarea>
                    </div>
                    
                    <div class="col-12">
                        <label for="internal_notes" class="form-label">{{ __('invoices.internal_notes') }}</label>
                        <textarea class="form-control" id="internal_notes" name="internal_notes" rows="2" 
                                  placeholder="{{ __('invoices.internal_notes_placeholder') }}"></textarea>
                        <small class="text-muted">{{ __('invoices.internal_notes_hint') }}</small>
                    </div>
                    
                    <div class="col-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="copy_lines" name="copy_lines" value="1">
                            <label class="form-check-label" for="copy_lines">
                                {{ __('invoices.copy_invoice_lines') }}
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-footer">
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="history.back()">
                        <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-file-earmark-minus me-2"></i>{{ __('invoices.create_credit_note') }}
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Form validation
(function() {
    'use strict';
    
    const forms = document.querySelectorAll('.needs-validation');
    
    Array.prototype.slice.call(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
})();

// Auto-calculate VAT when amount changes
document.getElementById('amount').addEventListener('input', function() {
    const amount = parseFloat(this.value) || 0;
    const maxAmount = {{ maxAmount }};
    
    if (amount > maxAmount) {
        this.value = maxAmount;
    }
});
</script>

{% endblock %}