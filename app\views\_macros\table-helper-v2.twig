{# Table Helper Macros v2 - Enhanced with sorting and column reordering #}

{# Main table wrapper with filters #}
{% macro tableWithFilters(config) %}
{# Include table helper CSS #}
<link rel="stylesheet" href="{{ config.baseUrl|default('') }}/css/table-helper.css">

<div class="table-wrapper" data-table-id="{{ config.tableId|default('data-table') }}">
    {# Filters Card #}
    {% if config.filters is defined or config.showSearch|default(true) %}
    <div class="card shadow-sm mb-4 filter-card">
        <div class="card-body">
            <form method="GET" action="{{ config.formAction|default('') }}" class="row g-3">
                {# Custom filters #}
                {% if config.filters is defined %}
                    {% for filter in config.filters %}
                        <div class="col-md-{{ filter.width|default(3) }}">
                            <label for="{{ filter.id }}" class="form-label">{{ filter.label }}</label>
                            {% if filter.type == 'select' %}
                                <select class="form-select" id="{{ filter.id }}" name="{{ filter.name|default(filter.id) }}" 
                                        {% if filter.autoSubmit|default(true) %}onchange="this.form.submit()"{% endif %}>
                                    <option value="">{{ filter.placeholder|default('All') }}</option>
                                    {% for value, label in filter.options %}
                                        <option value="{{ value }}" {{ filter.value == value ? 'selected' : '' }}>{{ label }}</option>
                                    {% endfor %}
                                </select>
                            {% elseif filter.type == 'date' %}
                                <input type="date" class="form-control" id="{{ filter.id }}" 
                                       name="{{ filter.name|default(filter.id) }}" value="{{ filter.value }}"
                                       {% if filter.autoSubmit|default(true) %}onchange="this.form.submit()"{% endif %}>
                            {% else %}
                                <input type="text" class="form-control" id="{{ filter.id }}" 
                                       name="{{ filter.name|default(filter.id) }}" value="{{ filter.value }}"
                                       placeholder="{{ filter.placeholder|default('') }}">
                            {% endif %}
                        </div>
                    {% endfor %}
                {% endif %}
                
                {# Search field #}
                {% if config.showSearch|default(true) %}
                <div class="col-md-{{ config.searchWidth|default(3) }}">
                    <label for="search" class="form-label">{{ __('common.search') }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ config.searchValue|default('') }}" 
                               placeholder="{{ config.searchPlaceholder|default(__('common.search_placeholder')) }}"
                               data-dynamic-search="true">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                    </div>
                </div>
                {% endif %}
                
                {# Action buttons #}
                <div class="col-12">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-secondary" onclick="clearTableFilters()">
                                <i class="bi bi-x-circle me-2"></i>{{ __('common.reset_filters') }}
                            </button>
                            
                            {# Column visibility toggle (optional) #}
                            {% if config.showColumnToggle|default(false) %}
                            <div class="dropdown column-toggle">
                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-eye me-2"></i>{{ __('common.columns') }}
                                </button>
                                <div class="dropdown-menu column-toggle-menu">
                                    {# Column checkboxes will be populated by JavaScript #}
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex gap-2">
                            {# Import button #}
                            {% if config.showImport|default(true) and config.importUrl %}
                            <a href="{{ config.importUrl }}" class="btn btn-outline-primary">
                                <i class="bi bi-upload me-2"></i>{{ __('common.import') }}
                            </a>
                            {% endif %}
                            
                            {# Export dropdown #}
                            {% if config.showExport|default(true) %}
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-download me-2"></i>{{ __('common.export') }}
                                </button>
                                <ul class="dropdown-menu">
                                    {% for format in config.exportFormats|default(['csv', 'excel', 'pdf']) %}
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="tableHelper.exportData('{{ format }}')">
                                            <i class="bi bi-file-earmark-{{ format == 'csv' ? 'spreadsheet' : (format == 'excel' ? 'excel' : 'pdf') }} me-2"></i>
                                            {{ format|upper }}
                                        </a>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
    {% endif %}
    
    {# Table Card #}
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="{{ config.tableId|default('data-table') }}">
                    {{ config.tableContent|raw }}
                </table>
            </div>
            
            {# Bulk Actions and Pagination #}
            {% if config.showBulkActions|default(true) or config.pagination is defined %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                {# Bulk Actions #}
                {% if config.showBulkActions|default(true) and config.bulkActions is defined %}
                <div class="dropdown">
                    <button type="button" class="btn btn-secondary dropdown-toggle" id="bulkActionsBtn"
                            data-bs-toggle="dropdown" aria-expanded="false" disabled>
                        <span id="bulkActionsText">{{ __('common.bulk_actions') }}</span>
                        <span id="bulkActionsCount"></span>
                    </button>
                    <ul class="dropdown-menu">
                        {% if config.bulkActions is empty %}
                            <li><span class="dropdown-item text-muted">No bulk actions available</span></li>
                        {% else %}
                            {% for action in config.bulkActions %}
                            <li>
                                <a class="dropdown-item {{ action.class|default('') }}" href="#" 
                                   onclick="tableHelper.executeBulkAction('{{ action.action }}'); return false;">
                                    <i class="{{ action.icon }} me-2"></i>{{ action.label }}
                                </a>
                            </li>
                            {% if action.divider|default(false) %}
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            {% endfor %}
                        {% endif %}
                    </ul>
                </div>
                {% endif %}
                
                {# Pagination #}
                {% if config.pagination is defined %}
                    {{ include('_partials/pagination.twig', config.pagination) }}
                {% endif %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

{# Initialize table helper #}
<script>
// Store configuration for later initialization
window.tableHelperConfig = {
    tableId: {{ config.tableId|default('data-table')|json_encode|raw }},
    searchInputId: 'search',
    searchColumns: {{ config.searchColumns|default([])|json_encode|raw }},
    filters: {{ config.filterConfigs|default([])|json_encode|raw }},
    storageKey: {{ config.storageKey|default('table_filters')|json_encode|raw }},
    exportFormats: {{ config.exportFormats|default(['csv', 'excel', 'pdf'])|json_encode|raw }},
    exportUrl: {{ config.exportUrl|default('')|json_encode|raw }},
    bulkActions: {{ config.bulkActions|default([])|json_encode|raw }},
    bulkActionUrl: {{ config.bulkActionUrl|default('')|json_encode|raw }},
    sortable: {{ config.sortable|default(true) ? 'true' : 'false' }},
    reorderable: {{ config.reorderable|default(true) ? 'true' : 'false' }},
    columnOrder: {{ config.columnOrder|default('null')|json_encode|raw }},
    defaultSort: {{ config.defaultSort|default('null')|json_encode|raw }},
    translations: {
        search: {{ __('common.search')|json_encode|raw }},
        noResults: {{ __('common.no_results')|json_encode|raw }},
        bulkActions: {{ __('common.bulk_actions')|json_encode|raw }},
        export: {{ __('common.export')|json_encode|raw }},
        import: {{ __('common.import')|json_encode|raw }},
        reset: {{ __('common.reset_filters')|json_encode|raw }},
        results: {{ __('common.results')|json_encode|raw }},
        selected: {{ __('common.selected')|json_encode|raw }},
        sortAsc: {{ __('common.sort_asc')|default('Sort ascending')|json_encode|raw }},
        sortDesc: {{ __('common.sort_desc')|default('Sort descending')|json_encode|raw }},
        dragToReorder: {{ __('common.drag_to_reorder')|default('Drag to reorder columns')|json_encode|raw }},
        stateSaved: {{ __('common.state_saved')|default('Table state saved')|json_encode|raw }}
    }
};

// This will be initialized after the script loads
window.tableHelperResetUrl = '{{ config.resetUrl|default('?reset_filters=1') }}';

function clearTableFilters() {
    if (window.tableHelper) {
        window.tableHelper.clearFilters();
        window.location.href = window.tableHelperResetUrl;
    }
}
</script>
{% endmacro %}

{# Enhanced table header with sorting support #}
{% macro tableHeader(columns, showCheckbox = true, config = {}) %}
<thead>
    <tr>
        {% if showCheckbox %}
        <th width="30" class="no-sort no-reorder">
            <input type="checkbox" class="form-check-input" id="selectAll">
        </th>
        {% endif %}
        {% for column in columns %}
        <th class="{{ column.class|default('') }} {{ column.sortable|default(true) and not column.isAction|default(false) ? '' : 'no-sort' }} {{ column.reorderable|default(true) and not column.isAction|default(false) ? '' : 'no-reorder' }}" 
            {% if column.width %}width="{{ column.width }}"{% endif %}>
            {{ column.label }}
        </th>
        {% endfor %}
    </tr>
</thead>
{% endmacro %}

{# Table row with checkbox #}
{% macro tableRow(data, columns, showCheckbox = true, rowId = null) %}
<tr data-id="{{ rowId }}">
    {% if showCheckbox %}
    <td>
        <input type="checkbox" class="form-check-input row-checkbox" value="{{ rowId }}">
    </td>
    {% endif %}
    {% for column in columns %}
    <td class="{{ column.class|default('') }}">
        {% if column.template is defined %}
            {{ include(column.template, { 'value': data[column.key], 'row': data }) }}
        {% else %}
            {{ data[column.key]|default('') }}
        {% endif %}
    </td>
    {% endfor %}
</tr>
{% endmacro %}

{# Empty state #}
{% macro emptyState(message, icon = 'bi-inbox', colspan = 10) %}
<tr>
    <td colspan="{{ colspan }}" class="empty-state">
        <i class="{{ icon }}"></i>
        <p class="mb-0">{{ message }}</p>
    </td>
</tr>
{% endmacro %}

{# Quick status badge #}
{% macro statusBadge(status, labels = {}) %}
{% set statusMap = {
    'active': 'success',
    'inactive': 'secondary',
    'pending': 'warning',
    'completed': 'success',
    'cancelled': 'danger',
    'draft': 'secondary',
    'sent': 'info',
    'paid': 'success',
    'overdue': 'danger'
} %}
<span class="badge bg-{{ statusMap[status]|default('secondary') }}">
    {{ labels[status]|default(status|capitalize) }}
</span>
{% endmacro %}