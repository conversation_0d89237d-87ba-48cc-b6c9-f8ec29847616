{% extends "base-modern.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{{ base_url }}/config/invoice-templates" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    <!-- Form -->
    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ base_url }}/config/invoice-templates{% if mode == 'edit' %}/{{ template.id }}{% endif %}">
                {% if mode == 'edit' %}
                    <input type="hidden" name="_method" value="PUT">
                {% endif %}
                
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ __('config.template_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="name" class="form-label">
                                    {{ __('config.template_name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{{ template.name|default('') }}"
                                       required>
                                <div class="form-text">{{ __('config.template_name_hint') }}</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="code" class="form-label">
                                    {{ __('config.template_code') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       name="code" 
                                       value="{{ template.code|default('') }}"
                                       pattern="[A-Z0-9_\-]+"
                                       style="text-transform: uppercase;"
                                       required>
                                <div class="form-text">{{ __('config.template_code_hint') }}</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="invoice_type" class="form-label">
                                    {{ __('invoices.invoice_type') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="invoice_type" name="invoice_type" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    {% for key, value in invoice_types %}
                                        <option value="{{ key }}" {{ template.invoice_type == key ? 'selected' : '' }}>
                                            {{ value }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="owner_type" class="form-label">
                                    {{ __('config.owner_type') }}
                                </label>
                                <select class="form-select" id="owner_type" name="owner_type">
                                    <option value="system" {{ template.owner_type == 'system' ? 'selected' : '' }}>
                                        {{ __('config.owner_type_system') }}
                                    </option>
                                    <option value="group" {{ template.owner_type == 'group' ? 'selected' : '' }}>
                                        {{ __('config.owner_type_group') }}
                                    </option>
                                    <option value="user" {{ template.owner_type == 'user' ? 'selected' : '' }}>
                                        {{ __('config.owner_type_user') }}
                                    </option>
                                </select>
                                <div class="form-text">{{ __('config.owner_type_hint') }}</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="parent_template_id" class="form-label">
                                    {{ __('config.parent_template') }}
                                </label>
                                <select class="form-select" id="parent_template_id" name="parent_template_id">
                                    <option value="">{{ __('common.none') }}</option>
                                    {% for parent in parentTemplates %}
                                        <option value="{{ parent.id }}" {{ template.parent_template_id == parent.id ? 'selected' : '' }}>
                                            {{ parent.name }} ({{ parent.code }})
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">{{ __('config.parent_template_hint') }}</div>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="is_active" class="form-label">
                                    {{ __('common.status') }}
                                </label>
                                <select class="form-select" id="is_active" name="is_active">
                                    <option value="1" {{ template.is_active == 1 ? 'selected' : '' }}>
                                        {{ __('common.active') }}
                                    </option>
                                    <option value="0" {{ template.is_active == 0 ? 'selected' : '' }}>
                                        {{ __('common.inactive') }}
                                    </option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <label for="description" class="form-label">
                                    {{ __('common.description') }}
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3">{{ template.description|default('') }}</textarea>
                                <div class="form-text">{{ __('config.template_description_hint') }}</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if mode == 'edit' %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">{{ __('config.template_details') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="bi bi-list-ul fs-1 text-primary mb-2"></i>
                                    <h6>{{ __('config.template_items') }}</h6>
                                    <p class="text-muted">{{ template.item_count|default(0) }} {{ __('config.items') }}</p>
                                    <a href="{{ base_url }}/config/invoice-templates/{{ template.id }}/items" 
                                       class="btn btn-sm btn-outline-primary">
                                        {{ __('config.manage_items') }}
                                    </a>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="bi bi-gear fs-1 text-info mb-2"></i>
                                    <h6>{{ __('config.template_settings') }}</h6>
                                    <p class="text-muted">{{ template.setting_count|default(0) }} {{ __('config.settings') }}</p>
                                    <button type="button" class="btn btn-sm btn-outline-info" disabled>
                                        {{ __('common.coming_soon') }}
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <i class="bi bi-percent fs-1 text-success mb-2"></i>
                                    <h6>{{ __('config.vat_configurations') }}</h6>
                                    <p class="text-muted">{{ template.vat_configs|length|default(0) }} {{ __('config.configurations') }}</p>
                                    <button type="button" class="btn btn-sm btn-outline-success" disabled>
                                        {{ __('common.coming_soon') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="d-flex justify-content-end gap-2">
                    <a href="{{ base_url }}/config/invoice-templates" class="btn btn-secondary">
                        {{ __('common.cancel') }}
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>
                        {% if mode == 'create' %}
                            {{ __('common.create') }}
                        {% else %}
                            {{ __('common.save_changes') }}
                        {% endif %}
                    </button>
                </div>
            </form>
        </div>
        
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('config.template_help') }}</h5>
                </div>
                <div class="card-body">
                    <h6>{{ __('config.what_are_templates') }}</h6>
                    <p class="text-muted">{{ __('config.templates_help_text') }}</p>
                    
                    <h6 class="mt-4">{{ __('config.template_types') }}</h6>
                    <ul class="text-muted">
                        <li><strong>{{ __('config.owner_type_system') }}:</strong> {{ __('config.system_template_help') }}</li>
                        <li><strong>{{ __('config.owner_type_group') }}:</strong> {{ __('config.group_template_help') }}</li>
                        <li><strong>{{ __('config.owner_type_user') }}:</strong> {{ __('config.user_template_help') }}</li>
                    </ul>
                    
                    <h6 class="mt-4">{{ __('config.template_inheritance') }}</h6>
                    <p class="text-muted">{{ __('config.inheritance_help_text') }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-uppercase the code field
document.getElementById('code').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9_-]/g, '');
});

// Filter parent templates based on invoice type
document.getElementById('invoice_type').addEventListener('change', function() {
    const selectedType = this.value;
    const parentSelect = document.getElementById('parent_template_id');
    const options = parentSelect.querySelectorAll('option');
    
    // Show/hide options based on type
    // This would need AJAX to properly filter, but for now we'll just show all
});
</script>
{% endblock %}