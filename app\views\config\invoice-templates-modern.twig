{% extends "base-modern.twig" %}

{% block title %}{{ __('config.invoice_templates') }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ base_url }}/css/dropdown-positioning-fix.css">
<style>
    /* Responsive table improvements */
    @media (max-width: 768px) {
        .table-responsive {
            font-size: 0.875rem;
        }
    }
    
    /* Table responsiveness */
    #templates-table {
        width: 100%;
        table-layout: auto;
    }
    
    /* Better spacing on mobile */
    @media (max-width: 576px) {
        .container-fluid {
            padding-left: 0.5rem;
            padding-right: 0.5rem;
        }
        
        .card-body {
            padding: 0.75rem;
        }
    }
    
    
    /* Table constraints to prevent overflow */
    #templates-table {
        table-layout: auto;
        width: 100%;
    }
    
    /* Ensure text wraps properly */
    #templates-table td {
        word-wrap: break-word;
        word-break: break-word;
    }
    
    /* Action buttons styling */
    .btn-group-sm > .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    /* Ensure action column has enough space */
    #templates-table th:last-child,
    #templates-table td:last-child {
        white-space: nowrap;
        width: auto;
        min-width: auto;
    }
    
    /* Checkbox column width */
    #templates-table th:nth-child(1),
    #templates-table td:nth-child(1) {
        width: 40px;
    }
    
    /* Default table-responsive behavior */
    .table-responsive {
        overflow-x: auto;
        overflow-y: hidden;
        -webkit-overflow-scrolling: touch;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('config.invoice_templates') }}</h1>
            <p class="text-muted mb-0">{{ __('config.invoice_templates_description') }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <a href="{{ base_url }}/config/invoice-templates/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.create_invoice_template') }}
            </a>
        </div>
    </div>
    
    {# Check if user has permission to manage templates #}
    {% set can_manage_templates = session.user.is_admin %}
    {# Additional check for managers - you can add group-based permissions here #}
    {% if not can_manage_templates %}
        {% for group in session.user.groups|default([]) %}
            {% if group.name|lower == 'manager' or group.name|lower == 'managers' or group.can_manage_templates|default(false) %}
                {% set can_manage_templates = true %}
            {% endif %}
        {% endfor %}
    {% endif %}
    
    {# Show admin/manager notice #}
    {% if can_manage_templates %}
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        <strong>{{ __('config.template_manager_notice') }}</strong>
        {{ __('config.template_manager_notice_text') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/config/invoice-templates">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="invoice_type" class="form-label">{{ __('invoices.invoice_type') }}</label>
                        <select class="form-select" id="invoice_type" name="invoice_type" onchange="this.form.submit()">
                            <option value="">{{ __('common.all') }}</option>
                            {% for key, value in invoice_types %}
                                <option value="{{ key }}" {{ filters.invoice_type == key ? 'selected' : '' }}>{{ value }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="owner_type" class="form-label">{{ __('config.owner_type') }}</label>
                        <select class="form-select" id="owner_type" name="owner_type" onchange="this.form.submit()">
                            <option value="">{{ __('common.all') }}</option>
                            {% for key, value in owner_types %}
                                <option value="{{ key }}" {{ filters.owner_type == key ? 'selected' : '' }}>{{ value }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="is_active" class="form-label">{{ __('common.status') }}</label>
                        <select class="form-select" id="is_active" name="is_active" onchange="this.form.submit()">
                            <option value="">{{ __('common.all') }}</option>
                            <option value="1" {{ filters.is_active == '1' ? 'selected' : '' }}>{{ __('common.active') }}</option>
                            <option value="0" {{ filters.is_active == '0' ? 'selected' : '' }}>{{ __('common.inactive') }}</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="d-none justify-content-between align-items-center mb-3" id="bulk-actions">
        <div>
            <span class="text-muted">{{ __('common.selected') }}: <strong id="selected-count">0</strong></span>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                <i class="bi bi-trash me-1"></i>{{ __('common.delete_selected') }}
            </button>
            <button type="button" class="btn btn-warning btn-sm" onclick="bulkToggleStatus()">
                <i class="bi bi-toggle-on me-1"></i>{{ __('config.toggle_status') }}
            </button>
        </div>
    </div>

    <!-- Templates Table -->
    <div class="card shadow-sm">
        <div class="card-body">
            {% if templates is empty %}
                <div class="text-center py-5">
                    <i class="bi bi-file-text fs-1 text-muted"></i>
                    <p class="text-muted mt-3">{{ __('config.no_templates_found') }}</p>
                    <a href="{{ base_url }}/config/invoice-templates/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>{{ __('config.create_first_template') }}
                    </a>
                </div>
            {% else %}
                <div class="table-responsive">
                    <table class="table table-hover" id="templates-table" data-no-table-helper="true">
                        <thead>
                            <tr>
                                {% if visibleColumns.checkbox is not defined or visibleColumns.checkbox %}
                                <th class="text-center" style="width: 40px;">
                                    <input type="checkbox" class="form-check-input" id="select-all" onchange="toggleSelectAll()">
                                </th>
                                {% endif %}
                                {% if visibleColumns.template_name is not defined or visibleColumns.template_name %}
                                <th>{{ __('config.template_name') }}</th>
                                {% endif %}
                                {% if visibleColumns.template_code is not defined or visibleColumns.template_code %}
                                <th class="d-none d-sm-table-cell">{{ __('config.template_code') }}</th>
                                {% endif %}
                                {% if visibleColumns.invoice_type is not defined or visibleColumns.invoice_type %}
                                <th>{{ __('invoices.invoice_type') }}</th>
                                {% endif %}
                                {% if visibleColumns.owner_type is not defined or visibleColumns.owner_type %}
                                <th class="d-none d-md-table-cell">{{ __('config.owner_type') }}</th>
                                {% endif %}
                                {% if visibleColumns.parent_template is not defined or visibleColumns.parent_template %}
                                <th class="d-none d-lg-table-cell">{{ __('config.parent_template') }}</th>
                                {% endif %}
                                {% if visibleColumns.items_count is not defined or visibleColumns.items_count %}
                                <th class="text-center d-none d-sm-table-cell">{{ __('config.items_count') }}</th>
                                {% endif %}
                                {% if visibleColumns.status is not defined or visibleColumns.status %}
                                <th class="text-center">{{ __('common.status') }}</th>
                                {% endif %}
                                {% if visibleColumns.created_by is not defined or visibleColumns.created_by %}
                                <th class="d-none d-lg-table-cell">{{ __('common.created_by') }}</th>
                                {% endif %}
                                {% if visibleColumns.actions is not defined or visibleColumns.actions %}
                                <th class="text-end">{{ __('common.actions') }}</th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {% for template in templates %}
                                <tr data-id="{{ template.id }}">
                                    {% if visibleColumns.checkbox is not defined or visibleColumns.checkbox %}
                                    <td class="text-center">
                                        <input type="checkbox" class="form-check-input template-checkbox" value="{{ template.id }}" onchange="updateBulkActions()">
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.template_name is not defined or visibleColumns.template_name %}
                                    <td>
                                        <strong>{{ template.name }}</strong>
                                        {% if template.description %}
                                            <br><small class="text-muted">{{ template.description }}</small>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.template_code is not defined or visibleColumns.template_code %}
                                    <td class="d-none d-sm-table-cell"><code>{{ template.code }}</code></td>
                                    {% endif %}
                                    {% if visibleColumns.invoice_type is not defined or visibleColumns.invoice_type %}
                                    <td>
                                        <span class="badge bg-secondary">
                                            {{ invoice_types[template.invoice_type]|default(template.invoice_type) }}
                                        </span>
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.owner_type is not defined or visibleColumns.owner_type %}
                                    <td class="d-none d-md-table-cell">
                                        {% if template.owner_type == 'system' %}
                                            <span class="badge bg-primary">{{ __('config.owner_type_system') }}</span>
                                        {% elseif template.owner_type == 'group' %}
                                            <span class="badge bg-info">{{ __('config.owner_type_group') }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ __('config.owner_type_user') }}</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.parent_template is not defined or visibleColumns.parent_template %}
                                    <td class="d-none d-lg-table-cell">
                                        {% if template.parent_template_name %}
                                            {{ template.parent_template_name }}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.items_count is not defined or visibleColumns.items_count %}
                                    <td class="text-center d-none d-sm-table-cell">
                                        <span class="badge bg-light text-dark">{{ template.item_count }}</span>
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.status is not defined or visibleColumns.status %}
                                    <td class="text-center">
                                        {% if template.is_active %}
                                            <span class="badge bg-success">{{ __('common.active') }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ __('common.inactive') }}</span>
                                        {% endif %}
                                    </td>
                                    {% endif %}
                                    {% if visibleColumns.created_by is not defined or visibleColumns.created_by %}
                                    <td class="d-none d-lg-table-cell">{{ template.creator_name }}</td>
                                    {% endif %}
                                    {% if visibleColumns.actions is not defined or visibleColumns.actions %}
                                    <td class="text-end">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ base_url }}/config/invoice-templates/{{ template.id }}/items" 
                                               class="btn btn-outline-primary" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('config.manage_items') }}">
                                                <i class="bi bi-list-ul"></i>
                                            </a>
                                            <a href="{{ base_url }}/config/invoice-templates/{{ template.id }}/edit" 
                                               class="btn btn-outline-secondary" 
                                               data-bs-toggle="tooltip" 
                                               title="{{ __('common.edit') }}">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-outline-info" 
                                                    onclick="duplicateTemplate({{ template.id }}); return false;"
                                                    data-bs-toggle="tooltip" 
                                                    title="{{ __('common.duplicate') }}">
                                                <i class="bi bi-files"></i>
                                            </button>
                                            {% set can_delete = false %}
                                            {% if can_manage_templates %}
                                                {# Admins and managers can delete any non-system template #}
                                                {% set can_delete = true %}
                                            {% elseif template.owner_type == 'user' and template.created_by == session.user_id %}
                                                {# Users can delete their own templates #}
                                                {% set can_delete = true %}
                                            {% elseif template.owner_type == 'group' %}
                                                {# Check if user is in the group that owns this template #}
                                                {% for group_id in session.user.group_ids|default([]) %}
                                                    {% if group_id == template.owner_id %}
                                                        {% set can_delete = true %}
                                                    {% endif %}
                                                {% endfor %}
                                            {% endif %}
                                            
                                            {% if can_delete and template.owner_type != 'system' %}
                                            <button type="button" 
                                                    class="btn btn-outline-danger" 
                                                    onclick="deleteTemplate({{ template.id }}, '{{ template.name|escape('js') }}'); return false;"
                                                    data-bs-toggle="tooltip" 
                                                    title="{{ __('common.delete') }}">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                    {% endif %}
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function deleteTemplate(id, name) {
    if (confirm('{{ __("config.delete_template_confirm") }}'.replace(':name', name))) {
        fetch('{{ base_url }}/config/invoice-templates/' + id, {
            method: 'DELETE',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            return response.json().then(data => {
                if (!response.ok) {
                    throw new Error(data.message || 'Network response was not ok: ' + response.status);
                }
                return data;
            });
        })
        .then(data => {
            if (data.success) {
                // Use toastr if available, otherwise reload
                if (typeof toastr !== 'undefined') {
                    toastr.success(data.message || '{{ __("config.template_deleted_successfully") }}');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    location.reload();
                }
            } else {
                // Show specific error message
                const errorMsg = data.message || '{{ __("common.error_occurred") }}';
                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMsg);
                } else {
                    alert(errorMsg);
                }
                console.error('Template deletion failed:', data);
            }
        })
        .catch(error => {
            console.error('Error deleting template:', error);
            const errorMsg = error.message || '{{ __("common.error_occurred") }}';
            if (typeof toastr !== 'undefined') {
                toastr.error(errorMsg);
            } else {
                alert('Error: ' + errorMsg);
            }
        });
    }
}

function duplicateTemplate(id) {
    const name = prompt('{{ __("config.enter_new_template_name") }}');
    if (name) {
        const code = prompt('{{ __("config.enter_new_template_code") }}');
        if (code) {
            fetch('{{ base_url }}/config/invoice-templates/' + id + '/duplicate', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name: name, code: code })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.redirect) {
                        window.location.href = '{{ base_url }}' + data.redirect;
                    } else {
                        location.reload();
                    }
                } else {
                    alert(data.message || '{{ __("common.error_occurred") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ __("common.error_occurred") }}');
            });
        }
    }
}
</script>

<script>
// Bulk actions functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.template-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = selectAll.checked;
    });
    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    const count = checkboxes.length;
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    if (count > 0) {
        bulkActions.classList.remove('d-none');
        bulkActions.classList.add('d-flex');
        selectedCount.textContent = count;
    } else {
        bulkActions.classList.remove('d-flex');
        bulkActions.classList.add('d-none');
    }
    
    // Update select all checkbox state
    const selectAll = document.getElementById('select-all');
    const totalCheckboxes = document.querySelectorAll('.template-checkbox');
    selectAll.checked = count === totalCheckboxes.length && count > 0;
    selectAll.indeterminate = count > 0 && count < totalCheckboxes.length;
}

function getSelectedIds() {
    const checkboxes = document.querySelectorAll('.template-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function bulkDelete() {
    const ids = getSelectedIds();
    if (ids.length === 0) return;
    
    if (confirm('{{ __("config.bulk_delete_templates_confirm") }}'.replace(':count', ids.length))) {
        // Show loading
        const btn = event.target.closest('button');
        const originalHtml = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.deleting") }}';
        
        // Process deletions and collect results
        Promise.all(ids.map((id, index) => 
            fetch(`{{ base_url }}/config/invoice-templates/${id}`, {
                method: 'DELETE',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json().then(data => ({
                id: id,
                success: response.ok && data.success,
                message: data.message || (response.ok ? 'Success' : 'Failed'),
                status: response.status
            })))
            .catch(error => ({
                id: id,
                success: false,
                message: error.message || 'Network error'
            }))
        ))
        .then(results => {
            btn.disabled = false;
            btn.innerHTML = originalHtml;
            
            // Count successes and failures
            const successes = results.filter(r => r.success);
            const failures = results.filter(r => !r.success);
            
            if (failures.length === 0) {
                toastr.success('{{ __("config.templates_deleted_successfully") }}'.replace(':count', successes.length));
                setTimeout(() => location.reload(), 1500);
            } else if (successes.length === 0) {
                toastr.error('{{ __("config.all_templates_failed_delete") }}');
                // Show detailed errors
                failures.forEach(f => {
                    toastr.error(`Template ID ${f.id}: ${f.message}`, '', {timeOut: 10000});
                });
            } else {
                toastr.warning(`{{ __("config.partial_delete_success") }}`.replace(':success', successes.length).replace(':total', ids.length));
                // Show which ones failed
                failures.forEach(f => {
                    toastr.error(`Template ID ${f.id}: ${f.message}`, '', {timeOut: 10000});
                });
                setTimeout(() => location.reload(), 3000);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ __("common.error_occurred") }}');
            btn.disabled = false;
            btn.innerHTML = originalHtml;
        });
    }
}

function bulkToggleStatus() {
    const ids = getSelectedIds();
    if (ids.length === 0) return;
    
    // Show loading
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    btn.disabled = true;
    btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.updating") }}';
    
    // For now, we'll just reload - you can implement the actual toggle logic
    toastr.info('{{ __("config.feature_coming_soon") }}');
    setTimeout(() => {
        btn.disabled = false;
        btn.innerHTML = originalHtml;
    }, 1000);
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    updateBulkActions();
    
    // Initialize Bootstrap tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Prevent TableHelper from initializing on this table
    if (window.tableHelperConfig) {
        delete window.tableHelperConfig;
    }
    
    // Also prevent any table with data-no-table-helper attribute
    const preventInit = function() {
        const table = document.getElementById('templates-table');
        if (table) {
            table.setAttribute('data-no-init', 'true');
            table.classList.add('no-table-helper');
        }
    };
    
    // Run immediately and after a delay to ensure it takes effect
    preventInit();
    setTimeout(preventInit, 10);
});

// Override any TableHelper initialization for this specific table
if (window.TableHelper) {
    const originalInit = window.TableHelper.prototype.init;
    window.TableHelper.prototype.init = function() {
        if (this.options.tableId === 'templates-table') {
            console.log('Skipping TableHelper init for templates-table');
            return;
        }
        originalInit.call(this);
    };
}

</script>
{% endblock %}