<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background: #007bff; color: white; }
tr:nth-child(even) { background: #f2f2f2; }
</style>';

echo "<h1>Check for Duplicate Invoice Lines</h1>";

// Check for duplicate lines per invoice
echo '<div class="section">';
echo '<h2>Checking for duplicate invoice lines...</h2>';

$stmt = $db->prepare("
    SELECT 
        invoice_id, 
        COUNT(*) as line_count,
        COUNT(DISTINCT CONCAT(description, quantity, unit_price, vat_rate)) as unique_lines
    FROM invoice_lines
    GROUP BY invoice_id
    HAVING line_count > unique_lines
");
$stmt->execute();
$duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($duplicates)) {
    echo '<div class="success">✓ No duplicate invoice lines found based on description, quantity, unit_price, and vat_rate.</div>';
} else {
    echo '<div class="error">⚠️ Found ' . count($duplicates) . ' invoices with potential duplicate lines!</div>';
    
    echo '<table>';
    echo '<tr><th>Invoice ID</th><th>Total Lines</th><th>Unique Lines</th><th>Duplicates</th><th>Invoice Number</th></tr>';
    
    foreach ($duplicates as $dup) {
        // Get invoice number
        $stmt = $db->prepare("SELECT invoice_number FROM invoices WHERE id = ?");
        $stmt->execute([$dup['invoice_id']]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo '<tr>';
        echo '<td>' . $dup['invoice_id'] . '</td>';
        echo '<td>' . $dup['line_count'] . '</td>';
        echo '<td>' . $dup['unique_lines'] . '</td>';
        echo '<td>' . ($dup['line_count'] - $dup['unique_lines']) . '</td>';
        echo '<td>' . ($invoice['invoice_number'] ?? 'N/A') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}
echo '</div>';

// Check for exact duplicates (same invoice_id and all fields)
echo '<div class="section">';
echo '<h2>Checking for exact duplicate entries...</h2>';

$stmt = $db->prepare("
    SELECT 
        invoice_id, 
        description, 
        quantity, 
        unit_price, 
        vat_rate,
        COUNT(*) as count
    FROM invoice_lines
    GROUP BY invoice_id, description, quantity, unit_price, vat_rate
    HAVING count > 1
    ORDER BY invoice_id, count DESC
");
$stmt->execute();
$exactDuplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($exactDuplicates)) {
    echo '<div class="success">✓ No exact duplicate lines found.</div>';
} else {
    echo '<div class="error">⚠️ Found exact duplicate lines!</div>';
    
    echo '<table>';
    echo '<tr><th>Invoice ID</th><th>Description</th><th>Qty</th><th>Unit Price</th><th>VAT</th><th>Count</th></tr>';
    
    foreach ($exactDuplicates as $dup) {
        echo '<tr>';
        echo '<td>' . $dup['invoice_id'] . '</td>';
        echo '<td>' . htmlspecialchars($dup['description']) . '</td>';
        echo '<td>' . $dup['quantity'] . '</td>';
        echo '<td>' . number_format($dup['unit_price'], 2) . '€</td>';
        echo '<td>' . $dup['vat_rate'] . '%</td>';
        echo '<td style="background: #ffcccc; font-weight: bold;">' . $dup['count'] . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}
echo '</div>';

// Check specific invoice if provided
if (isset($_GET['invoice_id'])) {
    $invoiceId = $_GET['invoice_id'];
    
    echo '<div class="section">';
    echo '<h2>Details for Invoice ID: ' . $invoiceId . '</h2>';
    
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order, id
    ");
    $stmt->execute([$invoiceId]);
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo '<table>';
    echo '<tr><th>ID</th><th>Description</th><th>Qty</th><th>Unit Price</th><th>VAT</th><th>Sort Order</th></tr>';
    
    foreach ($lines as $line) {
        echo '<tr>';
        echo '<td>' . $line['id'] . '</td>';
        echo '<td>' . htmlspecialchars($line['description']) . '</td>';
        echo '<td>' . $line['quantity'] . '</td>';
        echo '<td>' . number_format($line['unit_price'], 2) . '€</td>';
        echo '<td>' . $line['vat_rate'] . '%</td>';
        echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '</div>';
}

echo '<div class="section">';
echo '<p>To check a specific invoice, add <code>?invoice_id=XXX</code> to the URL.</p>';
echo '</div>';