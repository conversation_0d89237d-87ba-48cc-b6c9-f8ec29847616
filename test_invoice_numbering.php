<?php
/**
 * Test script for the new global invoice numbering system
 */

// Include the bootstrap file to initialize the environment
require_once __DIR__ . '/app/config/bootstrap.php';

// Test the invoice numbering system
echo "=== Testing Global Invoice Numbering System ===\n\n";

// Create an instance of the Invoice model
$invoice = new App\Models\Invoice();

// Test 1: Get current highest number in the system
echo "1. Testing current highest invoice number detection:\n";
$db = Flight::db();
$stmt = $db->prepare("
    SELECT invoice_number, status 
    FROM invoices 
    WHERE invoice_number LIKE 'FAC-%-2025-%' 
    ORDER BY CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED) DESC
    LIMIT 5
");
$stmt->execute();
$results = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($results as $result) {
    echo "   - {$result['invoice_number']} (Status: {$result['status']})\n";
}

// Test 2: Test the getNextGlobalInvoiceNumber method
echo "\n2. Testing getNextGlobalInvoiceNumber method:\n";
$reflection = new ReflectionClass($invoice);
$method = $reflection->getMethod('getNextGlobalInvoiceNumber');
$method->setAccessible(true);

$nextNumber = $method->invoke($invoice, 2025, 07, 'yearly');
echo "   - Next suggested number: {$nextNumber}\n";

// Test 3: Test suggestDocumentNumber for different invoice types
echo "\n3. Testing suggestDocumentNumber for different invoice types:\n";

// Get document type ID for invoices
$stmt = $db->prepare("SELECT id FROM document_types WHERE code = 'invoice'");
$stmt->execute();
$docTypeId = $stmt->fetchColumn();

// Get different invoice type IDs
$stmt = $db->prepare("SELECT id, prefix, display_name FROM config_invoice_types ORDER BY id");
$stmt->execute();
$invoiceTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);

foreach ($invoiceTypes as $type) {
    $suggestedNumber = $invoice->suggestDocumentNumber($docTypeId, $type['id']);
    echo "   - {$type['display_name']} ({$type['prefix']}): {$suggestedNumber}\n";
}

// Test 4: Compare with sent invoices only
echo "\n4. Testing that only sent invoices are considered:\n";
$stmt = $db->prepare("
    SELECT MAX(CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED)) as max_number
    FROM invoices 
    WHERE invoice_number LIKE 'FAC-%-2025-%' 
      AND status != 'draft'
      AND invoice_number IS NOT NULL
      AND invoice_number != ''
");
$stmt->execute();
$result = $stmt->fetch(PDO::FETCH_ASSOC);
$maxFromSent = $result['max_number'] ?? 0;
echo "   - Max number from sent invoices: {$maxFromSent}\n";
echo "   - Next number would be: " . ($maxFromSent + 1) . "\n";

// Test 5: Show draft invoices that would be ignored
echo "\n5. Draft invoices that are ignored in sequence:\n";
$stmt = $db->prepare("
    SELECT invoice_number, status 
    FROM invoices 
    WHERE invoice_number LIKE 'FAC-%-2025-%' 
      AND status = 'draft'
    ORDER BY CAST(SUBSTRING(invoice_number, -4) AS UNSIGNED) DESC
    LIMIT 3
");
$stmt->execute();
$drafts = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($drafts)) {
    echo "   - No draft invoices found\n";
} else {
    foreach ($drafts as $draft) {
        echo "   - {$draft['invoice_number']} (Status: {$draft['status']}) - IGNORED\n";
    }
}

echo "\n=== Test Complete ===\n";
?>