<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

try {
    $pdo = new PDO(
        "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_DATABASE']};charset=utf8mb4",
        $_ENV['DB_USERNAME'],
        $_ENV['DB_PASSWORD']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<!DOCTYPE html>
<html>
<head>
    <title>Final Invoice Types Cleanup</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Final Invoice Types Cleanup</h1>";
    
    // First, let's see what's currently in the database
    echo "<h2>Current Invoice Types:</h2>";
    $stmt = $pdo->query("
        SELECT id, code, name, prefix, 
               (SELECT COUNT(*) FROM invoices WHERE type_id = config_invoice_types.id) as invoice_count
        FROM config_invoice_types 
        ORDER BY code
    ");
    
    echo "<table>";
    echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th><th>Used by Invoices</th></tr>";
    $types = [];
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $types[] = $row;
        $name = json_decode($row['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
        echo "<tr>";
        echo "<td>{$row['id']}</td>";
        echo "<td><strong>{$row['code']}</strong></td>";
        echo "<td>{$displayName}</td>";
        echo "<td>{$row['prefix']}</td>";
        echo "<td>{$row['invoice_count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    if (isset($_GET['execute'])) {
        echo "<h2>Executing Final Cleanup...</h2>";
        
        try {
            // Check if index exists
            $stmt = $pdo->query("SHOW INDEX FROM config_invoice_types WHERE Key_name = 'idx_code'");
            $indexExists = $stmt->rowCount() > 0;
            
            if (!$indexExists) {
                echo "<p class='info'>Index already dropped.</p>";
            }
            
            // Step 1: Use shorter codes that won't conflict
            echo "<h3>Step 1: Updating to Shorter Codes...</h3>";
            
            $updates = [
                "UPDATE config_invoice_types SET code = 'LOY' WHERE id = 1",
                "UPDATE config_invoice_types SET code = 'LOC' WHERE id = 12", 
                "UPDATE config_invoice_types SET code = 'RET25' WHERE id = 37",
                "UPDATE config_invoice_types SET code = 'RET30' WHERE id = 38"
            ];
            
            foreach ($updates as $sql) {
                try {
                    $pdo->exec($sql);
                    echo "<p>✅ " . substr($sql, 35, 30) . "...</p>";
                } catch (Exception $e) {
                    echo "<p class='warning'>⚠️ " . substr($sql, 35, 30) . " - may already be updated</p>";
                }
            }
            
            // Step 2: Update config mappings to match
            echo "<h3>Step 2: Updating Config Mappings...</h3>";
            $configs = [
                "UPDATE config SET `value` = 'RET25' WHERE `key` = 'ret_invoice_type'",
                "UPDATE config SET `value` = 'LOY' WHERE `key` = 'loy_invoice_type'", 
                "UPDATE config SET `value` = 'LOC' WHERE `key` = 'loc_invoice_type'"
            ];
            
            foreach ($configs as $sql) {
                $pdo->exec($sql);
                echo "<p>✅ Updated config mapping</p>";
            }
            
            // Step 3: Try to create index if it doesn't exist
            if (!$indexExists) {
                echo "<h3>Step 3: Creating Unique Index...</h3>";
                try {
                    // Create index without length restriction
                    $pdo->exec("CREATE UNIQUE INDEX idx_code ON config_invoice_types(code)");
                    echo "<p class='success'>✅ Created unique index on code</p>";
                } catch (Exception $e) {
                    echo "<p class='warning'>⚠️ Index creation failed: " . $e->getMessage() . "</p>";
                    echo "<p>This is OK if the index already exists.</p>";
                }
            }
            
            // Show final state
            echo "<h2 class='success'>✅ Cleanup Complete!</h2>";
            echo "<h3>Final Invoice Types:</h3>";
            
            $stmt = $pdo->query("
                SELECT id, code, name, prefix, color, numbering_pattern
                FROM config_invoice_types 
                WHERE is_active = 1
                ORDER BY id
            ");
            
            echo "<table>";
            echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th><th>Pattern</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $name = json_decode($row['name'], true);
                $displayName = is_array($name) ? ($name['fr'] ?? $row['name']) : $row['name'];
                echo "<tr>";
                echo "<td>{$row['id']}</td>";
                echo "<td><strong>{$row['code']}</strong></td>";
                echo "<td>{$displayName}</td>";
                echo "<td>{$row['prefix']}</td>";
                echo "<td><small>{$row['numbering_pattern']}</small></td>";
                echo "</tr>";
            }
            echo "</table>";
            
            // Show config mappings
            echo "<h3>Config Mappings:</h3>";
            $stmt = $pdo->query("SELECT `key`, `value` FROM config WHERE `key` LIKE '%_invoice_type' ORDER BY `key`");
            echo "<table>";
            echo "<tr><th>Key</th><th>Value</th></tr>";
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr><td>{$row['key']}</td><td><strong>{$row['value']}</strong></td></tr>";
            }
            echo "</table>";
            
        } catch (Exception $e) {
            echo "<p class='error'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        echo "<br><a href='/fit/public/test-user-invoice-generation.php' style='background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Invoice Generation</a>";
        
    } else {
        echo "<h2>⚠️ This will update invoice type codes to shorter versions</h2>";
        echo "<p>The following changes will be made:</p>";
        echo "<ul>";
        echo "<li>loyer → LOY</li>";
        echo "<li>location → LOC</li>";
        echo "<li>retrocession_25 → RET25</li>";
        echo "<li>retrocession_30 → RET30</li>";
        echo "</ul>";
        echo "<p>This avoids the 4-character index limitation issue.</p>";
        echo "<br><a href='?execute=1' style='background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Execute Final Cleanup</a>";
    }
    
    echo "</body></html>";
    
} catch (PDOException $e) {
    echo "<p class='error'>Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}