<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.number { font-weight: bold; color: #007bff; font-size: 1.1em; }
</style>';

echo "<h1>Test New Invoice Numbering System</h1>";
echo "<p>Format: <strong>FAC-{TYPE_PREFIX}-{YYYY}-{NNNN}</strong></p>";

// Get document type for invoices
$stmt = $db->prepare("SELECT id, prefix FROM document_types WHERE code = 'invoice'");
$stmt->execute();
$docType = $stmt->fetch(\PDO::FETCH_ASSOC);

if (!$docType) {
    echo '<div class="error">Document type "invoice" not found!</div>';
    exit;
}

// Get all invoice types
echo '<div class="section">';
echo '<h2>Available Invoice Types</h2>';
$stmt = $db->query("
    SELECT id, name, code, prefix 
    FROM config_invoice_types 
    WHERE is_active = 1 
    ORDER BY id
");
$types = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<table>';
echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Next Invoice Number</th></tr>';

$invoice = new Invoice();

foreach ($types as $type) {
    // Decode JSON name
    $name = json_decode($type['name'], true);
    $displayName = is_array($name) ? ($name['fr'] ?? $name['en'] ?? $type['name']) : $type['name'];
    
    // Generate test number
    try {
        // Get current sequence number
        $stmt = $db->prepare("
            SELECT last_number 
            FROM document_sequences 
            WHERE document_type_id = ? AND year = ? AND month IS NULL
        ");
        $stmt->execute([$docType['id'], date('Y')]);
        $seq = $stmt->fetch(\PDO::FETCH_ASSOC);
        $nextNum = ($seq['last_number'] ?? 0) + 1;
        
        // Build the number manually to show what it would be
        $prefix = $type['prefix'] ?? 'GEN';
        $testNumber = 'FAC-' . $prefix . '-' . date('Y') . '-' . str_pad($nextNum, 4, '0', STR_PAD_LEFT);
        
        echo '<tr>';
        echo '<td>' . $type['id'] . '</td>';
        echo '<td>' . htmlspecialchars($displayName) . '</td>';
        echo '<td>' . $type['code'] . '</td>';
        echo '<td>' . ($type['prefix'] ?? 'NULL') . '</td>';
        echo '<td class="number">' . $testNumber . '</td>';
        echo '</tr>';
    } catch (Exception $e) {
        echo '<tr>';
        echo '<td>' . $type['id'] . '</td>';
        echo '<td>' . htmlspecialchars($displayName) . '</td>';
        echo '<td>' . $type['code'] . '</td>';
        echo '<td>' . ($type['prefix'] ?? 'NULL') . '</td>';
        echo '<td class="error">Error: ' . $e->getMessage() . '</td>';
        echo '</tr>';
    }
}
echo '</table>';
echo '</div>';

// Test actual generation
echo '<div class="section">';
echo '<h2>Test Invoice Number Generation</h2>';

if (isset($_POST['test_type'])) {
    $testTypeId = $_POST['test_type'];
    
    try {
        // Find the type
        $typeToTest = null;
        foreach ($types as $type) {
            if ($type['id'] == $testTypeId) {
                $typeToTest = $type;
                break;
            }
        }
        
        if ($typeToTest) {
            // Generate a test number (without actually incrementing sequence)
            $invoice = new Invoice();
            
            // Temporarily store current sequence
            $stmt = $db->prepare("
                SELECT last_number 
                FROM document_sequences 
                WHERE document_type_id = ? AND year = ? AND month IS NULL
            ");
            $stmt->execute([$docType['id'], date('Y')]);
            $currentSeq = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            // Generate number
            $generatedNumber = $invoice->generateDocumentNumber($docType['id'], $testTypeId);
            
            // Reset sequence (since generateDocumentNumber increments it)
            if ($currentSeq) {
                $stmt = $db->prepare("
                    UPDATE document_sequences 
                    SET last_number = ? 
                    WHERE document_type_id = ? AND year = ? AND month IS NULL
                ");
                $stmt->execute([$currentSeq['last_number'], $docType['id'], date('Y')]);
            }
            
            echo '<div class="success">';
            echo '<strong>Generated Invoice Number:</strong><br>';
            echo '<span class="number" style="font-size: 1.5em;">' . $generatedNumber . '</span><br><br>';
            echo 'Type: ' . $typeToTest['code'] . ' (Prefix: ' . $typeToTest['prefix'] . ')';
            echo '</div>';
        }
    } catch (Exception $e) {
        echo '<div class="error">Error generating number: ' . $e->getMessage() . '</div>';
    }
}

echo '<form method="post">';
echo '<label>Select invoice type to test:</label><br>';
echo '<select name="test_type" style="padding: 5px; margin: 10px 0;">';
foreach ($types as $type) {
    $name = json_decode($type['name'], true);
    $displayName = is_array($name) ? ($name['fr'] ?? $name['en'] ?? $type['name']) : $type['name'];
    echo '<option value="' . $type['id'] . '">' . htmlspecialchars($displayName) . ' (' . $type['code'] . ')</option>';
}
echo '</select><br>';
echo '<button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">Generate Test Number</button>';
echo '</form>';
echo '</div>';

// Current sequence info
echo '<div class="section">';
echo '<h2>Current Sequence Information</h2>';
$stmt = $db->query("
    SELECT ds.*, dt.code 
    FROM document_sequences ds
    JOIN document_types dt ON ds.document_type_id = dt.id
    WHERE dt.code = 'invoice' AND ds.year = " . date('Y') . "
    ORDER BY ds.id DESC
");
$sequences = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<table>';
echo '<tr><th>Doc Type</th><th>Year</th><th>Month</th><th>Last Number</th><th>Next Number</th></tr>';
foreach ($sequences as $seq) {
    echo '<tr>';
    echo '<td>' . $seq['code'] . '</td>';
    echo '<td>' . $seq['year'] . '</td>';
    echo '<td>' . ($seq['month'] ?? 'NULL') . '</td>';
    echo '<td>' . $seq['last_number'] . '</td>';
    echo '<td>' . ($seq['last_number'] + 1) . '</td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';