{% extends "base-modern.twig" %}

{% block title %}{{ __('config.table_column_config') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.table_column_config') }}</h1>
        <a href="{{ base_url }}/config" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    <!-- Table Selection -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <label for="tableSelect" class="form-label">{{ __('config.select_table') }}</label>
                    <select class="form-select" id="tableSelect" onchange="loadTableColumns(this.value)">
                        <option value="">{{ __('common.select') }}...</option>
                        <option value="invoices">{{ __('config.invoices_table') }}</option>
                        <option value="users">{{ __('config.users_table') }}</option>
                        <option value="clients">{{ __('config.clients_table') }}</option>
                        <option value="patients">{{ __('config.patients_table') }}</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Column Configuration -->
    <div class="card shadow-sm" id="columnConfig" style="display: none;">
        <div class="card-header">
            <h5 class="mb-0">{{ __('config.column_configuration') }}</h5>
        </div>
        <div class="card-body">
            <p class="text-muted mb-3">
                <i class="bi bi-info-circle me-1"></i>
                {{ __('config.drag_to_reorder') }}
            </p>
            
            <form id="columnOrderForm" method="POST" action="{{ base_url }}/config/table-columns/save">
                {{ csrf_field()|raw }}
                <input type="hidden" name="table" id="tableName" value="">
                
                <div id="columnList" class="list-group">
                    <!-- Columns will be loaded here dynamically -->
                </div>
                
                <div class="mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle me-2"></i>{{ __('config.save_column_order') }}
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetToDefault()">
                        <i class="bi bi-arrow-clockwise me-2"></i>{{ __('common.reset') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
#columnList {
    max-height: 500px;
    overflow-y: auto;
}

.list-group-item {
    cursor: move;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.list-group-item.dragging {
    opacity: 0.5;
}

.list-group-item.drag-over {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: #0d6efd;
}

.drag-handle {
    cursor: move;
    color: #6c757d;
}

.position-badge {
    min-width: 30px;
    display: inline-block;
    text-align: center;
}
</style>

<script>
// Table column configurations
const tableColumns = {
    invoices: [
        { id: 'checkbox', name: '{{ __('common.checkbox') }}', fixed: true },
        { id: 'invoice_number', name: '{{ __('invoices.invoice_number') }}' },
        { id: 'document_type', name: '{{ __('invoices.document_type') }}' },
        { id: 'invoice_type', name: '{{ __('invoices.invoice_type') }}' },
        { id: 'client_patient', name: '{{ __('clients.client') }}/{{ __('patients.patient') }}' },
        { id: 'issue_date', name: '{{ __('invoices.issue_date') }}' },
        { id: 'due_date', name: '{{ __('invoices.due_date') }}' },
        { id: 'amount', name: '{{ __('invoices.amount') }}' },
        { id: 'status', name: '{{ __('common.status') }}' },
        { id: 'actions', name: '{{ __('common.actions') }}', fixed: true }
    ],
    users: [
        { id: 'checkbox', name: '{{ __('common.checkbox') }}', fixed: true },
        { id: 'name', name: '{{ __('users.name') }}' },
        { id: 'email', name: '{{ __('users.email') }}' },
        { id: 'username', name: '{{ __('users.username') }}' },
        { id: 'role', name: '{{ __('users.role') }}' },
        { id: 'status', name: '{{ __('common.status') }}' },
        { id: 'last_login', name: '{{ __('users.last_login') }}' },
        { id: 'actions', name: '{{ __('common.actions') }}', fixed: true }
    ],
    clients: [
        { id: 'checkbox', name: '{{ __('common.checkbox') }}', fixed: true },
        { id: 'name', name: '{{ __('clients.name') }}' },
        { id: 'email', name: '{{ __('clients.email') }}' },
        { id: 'phone', name: '{{ __('clients.phone') }}' },
        { id: 'type', name: '{{ __('clients.type') }}' },
        { id: 'status', name: '{{ __('common.status') }}' },
        { id: 'created_at', name: '{{ __('common.created_at') }}' },
        { id: 'actions', name: '{{ __('common.actions') }}', fixed: true }
    ],
    patients: [
        { id: 'checkbox', name: '{{ __('common.checkbox') }}', fixed: true },
        { id: 'first_name', name: '{{ __('patients.first_name') }}' },
        { id: 'last_name', name: '{{ __('patients.last_name') }}' },
        { id: 'birth_date', name: '{{ __('patients.birth_date') }}' },
        { id: 'gender', name: '{{ __('patients.gender') }}' },
        { id: 'phone', name: '{{ __('patients.phone') }}' },
        { id: 'email', name: '{{ __('patients.email') }}' },
        { id: 'actions', name: '{{ __('common.actions') }}', fixed: true }
    ]
};

// Current table column order (will be loaded from server)
let currentColumnOrder = {};
let draggedElement = null;

function loadTableColumns(tableName) {
    if (!tableName) {
        document.getElementById('columnConfig').style.display = 'none';
        return;
    }
    
    document.getElementById('tableName').value = tableName;
    document.getElementById('columnConfig').style.display = 'block';
    
    // Load saved configuration
    fetch(`{{ base_url }}/config/table-columns/${tableName}`)
        .then(response => response.json())
        .then(data => {
            currentColumnOrder = data.columnOrder || {};
            renderColumns(tableName);
        })
        .catch(error => {
            console.error('Error loading column configuration:', error);
            currentColumnOrder = {};
            renderColumns(tableName);
        });
}

function renderColumns(tableName) {
    const columns = tableColumns[tableName] || [];
    const columnList = document.getElementById('columnList');
    columnList.innerHTML = '';
    
    // Translation for fixed text
    const fixedText = '{{ __("common.fixed") }}';
    
    // Sort columns by saved order
    const sortedColumns = [...columns].sort((a, b) => {
        const orderA = currentColumnOrder[a.id] !== undefined ? currentColumnOrder[a.id] : columns.findIndex(c => c.id === a.id);
        const orderB = currentColumnOrder[b.id] !== undefined ? currentColumnOrder[b.id] : columns.findIndex(c => c.id === b.id);
        return orderA - orderB;
    });
    
    sortedColumns.forEach((column, index) => {
        const item = document.createElement('div');
        item.className = 'list-group-item d-flex align-items-center';
        item.dataset.columnId = column.id;
        
        if (!column.fixed) {
            item.draggable = true;
            item.addEventListener('dragstart', handleDragStart);
            item.addEventListener('dragover', handleDragOver);
            item.addEventListener('drop', handleDrop);
            item.addEventListener('dragend', handleDragEnd);
        }
        
        item.innerHTML = `
            <div class="me-3">
                ${column.fixed ? '<i class="bi bi-lock text-muted"></i>' : '<i class="bi bi-grip-vertical drag-handle"></i>'}
            </div>
            <div class="flex-grow-1">
                <strong>${column.name}</strong>
                ${column.fixed ? `<small class="text-muted ms-2">(${fixedText})</small>` : ''}
            </div>
            <div>
                <span class="badge bg-secondary position-badge">${index + 1}</span>
            </div>
            <input type="hidden" name="columns[${column.id}]" value="${index}">
        `;
        
        columnList.appendChild(item);
    });
}

// Drag and drop handlers
function handleDragStart(e) {
    draggedElement = e.target;
    e.target.classList.add('dragging');
}

function handleDragOver(e) {
    e.preventDefault();
    const afterElement = getDragAfterElement(document.getElementById('columnList'), e.clientY);
    const dragging = document.querySelector('.dragging');
    
    if (afterElement == null) {
        document.getElementById('columnList').appendChild(dragging);
    } else {
        document.getElementById('columnList').insertBefore(dragging, afterElement);
    }
}

function handleDrop(e) {
    e.preventDefault();
    updatePositions();
}

function handleDragEnd(e) {
    e.target.classList.remove('dragging');
    draggedElement = null;
    updatePositions();
}

function getDragAfterElement(container, y) {
    const draggableElements = [...container.querySelectorAll('.list-group-item:not(.dragging):not([data-column-id="checkbox"]):not([data-column-id="actions"])')];
    
    return draggableElements.reduce((closest, child) => {
        const box = child.getBoundingClientRect();
        const offset = y - box.top - box.height / 2;
        
        if (offset < 0 && offset > closest.offset) {
            return { offset: offset, element: child };
        } else {
            return closest;
        }
    }, { offset: Number.NEGATIVE_INFINITY }).element;
}

function updatePositions() {
    const items = document.querySelectorAll('#columnList .list-group-item');
    items.forEach((item, index) => {
        item.querySelector('.position-badge').textContent = index + 1;
        item.querySelector('input[type="hidden"]').value = index;
    });
}

function resetToDefault() {
    currentColumnOrder = {};
    const tableName = document.getElementById('tableName').value;
    if (tableName) {
        renderColumns(tableName);
    }
}

// Form submission
document.getElementById('columnOrderForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            toastr.success('{{ __('config.column_order_saved') }}');
        } else {
            toastr.error(data.message || '{{ __('common.error_occurred') }}');
        }
    })
    .catch(error => {
        toastr.error('{{ __('common.error_occurred') }}');
        console.error('Error:', error);
    });
});
</script>
{% endblock %}