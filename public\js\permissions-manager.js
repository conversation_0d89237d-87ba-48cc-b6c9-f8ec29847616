/**
 * Permissions Manager - Enhanced permission configuration for Fit360
 * Features: Dynamic matrix, group management, templates, audit log, mobile support
 */

class PermissionsManager {
    constructor(options = {}) {
        // Get base URL from current location
        const pathParts = window.location.pathname.split('/');
        const baseUrlParts = [];
        for (let i = 0; i < pathParts.length; i++) {
            if (pathParts[i] === 'public') {
                baseUrlParts.push(pathParts[i]);
                break;
            }
            if (pathParts[i]) {
                baseUrlParts.push(pathParts[i]);
            }
        }
        const defaultBaseUrl = '/' + baseUrlParts.join('/') + '/admin/permissions';
        
        this.options = {
            apiBaseUrl: options.apiBaseUrl || defaultBaseUrl,
            csrfToken: options.csrfToken || document.querySelector('meta[name="csrf-token"]')?.content,
            translations: options.translations || {
                saving: 'Saving...',
                saved: 'Permissions saved successfully',
                error: 'Error saving permissions',
                confirmReset: 'Are you sure you want to reset to default permissions?',
                confirmCopy: 'Copy permissions from which group?',
                loading: 'Loading...',
                noPermissions: 'No permissions available',
                searchPlaceholder: 'Search modules...',
                selectAll: 'Select All',
                deselectAll: 'Deselect All'
            },
            modules: options.modules || [],
            autoSave: options.autoSave || false,
            autoSaveDelay: options.autoSaveDelay || 2000
        };

        this.currentGroupId = null;
        this.currentPermissions = {};
        this.originalPermissions = {};
        this.isDirty = false;
        this.autoSaveTimer = null;
        this.touchStartX = null;
        this.touchStartY = null;

        this.init();
    }

    init() {
        this.bindElements();
        this.bindEvents();
        this.initializeTouch();
        this.loadInitialGroup();
        this.setupKeyboardShortcuts();
    }

    bindElements() {
        this.elements = {
            groupList: document.getElementById('groupList'),
            permissionsForm: document.getElementById('permissionsForm'),
            permissionsBody: document.getElementById('permissionsBody'),
            currentGroupId: document.getElementById('currentGroupId'),
            currentGroupName: document.getElementById('currentGroupName'),
            moduleSearch: document.getElementById('moduleSearch'),
            saveButton: document.querySelector('button[type="submit"]'),
            templateModal: document.getElementById('templateModal'),
            copyModal: document.getElementById('copyFromGroupModal'),
            auditModal: document.getElementById('auditLogModal'),
            loadingOverlay: null
        };

        // Create loading overlay
        this.createLoadingOverlay();
    }

    bindEvents() {
        // Group selection
        if (this.elements.groupList) {
            this.elements.groupList.addEventListener('click', (e) => {
                const groupItem = e.target.closest('.list-group-item');
                if (groupItem && !groupItem.classList.contains('active')) {
                    const groupId = groupItem.dataset.groupId;
                    this.loadGroupPermissions(groupId);
                }
            });
        }

        // Form submission
        if (this.elements.permissionsForm) {
            this.elements.permissionsForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.savePermissions();
            });
        }

        // Module search
        if (this.elements.moduleSearch) {
            this.elements.moduleSearch.addEventListener('input', debounce(() => {
                this.filterModules();
            }, 300));
        }

        // Permission toggles
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('permission-toggle')) {
                this.togglePermissionColumn(e.target);
            } else if (e.target.type === 'checkbox' && e.target.name?.startsWith('permissions')) {
                this.markAsDirty();
                this.updateRowHighlight(e.target);
                this.handlePermissionDependencies(e.target);
            }
        });

        // Bulk selection
        document.addEventListener('click', (e) => {
            const button = e.target.closest('.select-all-module');
            if (button) {
                e.preventDefault();
                e.stopPropagation();
                this.selectAllModulePermissions(button);
            }
        });

        // Window resize for responsive handling
        window.addEventListener('resize', debounce(() => {
            this.handleResponsive();
        }, 250));
    }

    initializeTouch() {
        // Touch support for mobile
        if ('ontouchstart' in window) {
            document.addEventListener('touchstart', (e) => {
                if (e.target.closest('.module-row')) {
                    this.touchStartX = e.touches[0].clientX;
                    this.touchStartY = e.touches[0].clientY;
                }
            });

            document.addEventListener('touchend', (e) => {
                if (e.target.closest('.module-row') && this.touchStartX) {
                    const touchEndX = e.changedTouches[0].clientX;
                    const diffX = touchEndX - this.touchStartX;

                    // Swipe to reveal actions
                    if (Math.abs(diffX) > 50) {
                        const row = e.target.closest('.module-row');
                        this.toggleRowActions(row, diffX > 0 ? 'left' : 'right');
                    }

                    this.touchStartX = null;
                    this.touchStartY = null;
                }
            });
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + S to save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (this.isDirty) {
                    this.savePermissions();
                }
            }

            // Ctrl/Cmd + F to focus search
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                this.elements.moduleSearch?.focus();
            }
        });
    }

    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'permissions-loading-overlay';
        overlay.innerHTML = `
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">${this.options.translations.loading}</span>
            </div>
        `;
        document.body.appendChild(overlay);
        this.elements.loadingOverlay = overlay;
    }

    showLoading() {
        this.elements.loadingOverlay?.classList.add('show');
    }

    hideLoading() {
        this.elements.loadingOverlay?.classList.remove('show');
    }

    async loadInitialGroup() {
        const firstGroup = this.elements.groupList?.querySelector('.list-group-item');
        if (firstGroup) {
            const groupId = firstGroup.dataset.groupId;
            await this.loadGroupPermissions(groupId);
        }
    }

    async loadGroupPermissions(groupId) {
        // Check for unsaved changes
        if (this.isDirty) {
            const confirmed = await this.confirmUnsavedChanges();
            if (!confirmed) return;
        }

        this.showLoading();

        try {
            const url = `${this.options.apiBaseUrl}/group?group_id=${groupId}`;
            
            const response = await fetch(url, {
                headers: {
                    'X-CSRF-Token': this.options.csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });

            if (!response.ok) {
                let errorMsg = `Failed to load permissions: ${response.status} ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    if (errorData.error) errorMsg = errorData.error;
                } catch (e) {
                    // Response wasn't JSON, probably HTML error page
                    console.error('Response was not JSON:', response);
                }
                throw new Error(errorMsg);
            }

            const data = await response.json();
            
            this.currentGroupId = groupId;
            this.currentPermissions = data.permissions || {};
            this.originalPermissions = JSON.parse(JSON.stringify(this.currentPermissions));
            this.isDirty = false;

            this.updateUI(groupId, data.group);
            this.renderPermissionsTable();

        } catch (error) {
            console.error('Error loading permissions:', error);
            this.showError(this.options.translations.error);
        } finally {
            this.hideLoading();
        }
    }

    updateUI(groupId, groupData) {
        // Update active group
        this.elements.groupList?.querySelectorAll('.list-group-item').forEach(item => {
            item.classList.toggle('active', item.dataset.groupId === groupId);
        });

        // Update current group display
        if (this.elements.currentGroupId) {
            this.elements.currentGroupId.value = groupId;
        }
        if (this.elements.currentGroupName) {
            this.elements.currentGroupName.textContent = groupData?.name || '';
        }

        // Update save button state
        this.updateSaveButton();
    }

    renderPermissionsTable() {
        if (!this.elements.permissionsBody) return;

        const tbody = this.elements.permissionsBody;
        tbody.innerHTML = '';

        // Render modules
        this.options.modules.forEach(module => {
            this.renderModuleRow(module, tbody);
            
            // Render submodules
            if (module.submodules) {
                Object.entries(module.submodules).forEach(([subCode, submodule]) => {
                    this.renderModuleRow(submodule, tbody, true, module.code, subCode);
                });
            }
        });

        // Add mobile swipe indicators
        if (this.isMobile()) {
            this.addMobileIndicators();
        }
        
        // Initialize select-all button states
        this.options.modules.forEach(module => {
            this.updateSelectAllButton(module.code);
            if (module.submodules) {
                Object.entries(module.submodules).forEach(([subCode, submodule]) => {
                    this.updateSelectAllButton(subCode);
                });
            }
        });
    }

    renderModuleRow(module, tbody, isSubmodule = false, parentCode = null, moduleCode = null) {
        const code = moduleCode || module.code;
        const row = document.createElement('tr');
        row.className = isSubmodule ? 'submodule-row' : 'module-row';
        row.dataset.module = code;

        // Module name cell
        const nameCell = document.createElement('td');
        nameCell.innerHTML = `
            <div class="d-flex align-items-center ${isSubmodule ? 'ps-4' : ''}">
                <i class="fas ${module.icon} me-2 ${isSubmodule ? 'text-muted' : ''}"></i>
                <div class="flex-grow-1">
                    <strong class="module-name">${module.name}</strong>
                    ${module.description ? `<br><small class="text-muted">${module.description}</small>` : ''}
                </div>
                <button type="button" class="btn btn-sm btn-link text-muted select-all-module d-none d-md-inline-block"
                        data-module="${code}" title="${this.options.translations.selectAll}">
                    <i class="fas fa-check-square"></i>
                </button>
            </div>
        `;
        row.appendChild(nameCell);

        // Permission checkboxes
        ['view', 'create', 'edit', 'delete'].forEach(perm => {
            const td = document.createElement('td');
            td.className = 'text-center';
            
            if (module.permissions && module.permissions.includes(perm)) {
                const isChecked = this.hasPermission(code, perm);
                const checkboxId = `${code}_${perm}`;
                
                td.innerHTML = `
                    <div class="form-check form-check-inline">
                        <input type="checkbox" class="form-check-input permission-checkbox"
                               id="${checkboxId}"
                               name="permissions[${code}][${perm}]"
                               value="1"
                               ${isChecked ? 'checked' : ''}
                               data-module="${code}"
                               data-permission="${perm}">
                        <label class="form-check-label" for="${checkboxId}">
                            <span class="d-md-none">${this.getPermissionIcon(perm)}</span>
                        </label>
                    </div>
                `;
            }
            row.appendChild(td);
        });

        // Special permissions
        const specialTd = document.createElement('td');
        specialTd.className = 'text-center';
        
        if (module.special && module.special.length > 0) {
            const specialPerms = module.special.map(sp => {
                const isChecked = this.hasPermission(code, sp);
                const checkboxId = `${code}_${sp}`;
                const label = sp.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                
                return `
                    <div class="form-check form-check-inline mb-1">
                        <input type="checkbox" class="form-check-input permission-checkbox"
                               id="${checkboxId}"
                               name="permissions[${code}][${sp}]"
                               value="1"
                               ${isChecked ? 'checked' : ''}
                               data-module="${code}"
                               data-permission="${sp}"
                               data-special="true">
                        <label class="form-check-label small" for="${checkboxId}">
                            ${label}
                        </label>
                    </div>
                `;
            }).join('');
            
            specialTd.innerHTML = `<div class="special-perms-wrapper">${specialPerms}</div>`;
        }
        
        row.appendChild(specialTd);
        tbody.appendChild(row);
    }

    hasPermission(moduleCode, permission) {
        return this.currentPermissions[moduleCode] && 
               this.currentPermissions[moduleCode].includes(permission);
    }

    getPermissionIcon(permission) {
        const icons = {
            view: '<i class="fas fa-eye"></i>',
            create: '<i class="fas fa-plus"></i>',
            edit: '<i class="fas fa-edit"></i>',
            delete: '<i class="fas fa-trash"></i>'
        };
        return icons[permission] || '';
    }

    togglePermissionColumn(toggle) {
        const permission = toggle.dataset.permission;
        const isChecked = toggle.checked;
        
        document.querySelectorAll(`input[data-permission="${permission}"]:not([data-special])`).forEach(checkbox => {
            checkbox.checked = isChecked;
            this.updatePermissionState(checkbox);
        });
        
        this.markAsDirty();
    }

    selectAllModulePermissions(button) {
        const moduleCode = button.dataset.module;
        const checkboxes = document.querySelectorAll(`input[data-module="${moduleCode}"]`);
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = !allChecked;
            this.updatePermissionState(checkbox);
        });
        
        // Update button icon
        const icon = button.querySelector('i');
        if (icon) {
            if (!allChecked) {
                icon.classList.remove('fa-check-square');
                icon.classList.add('fa-check-square', 'text-success');
            } else {
                icon.classList.remove('text-success');
            }
        }
        
        this.markAsDirty();
        this.updateRowHighlight(checkboxes[0]);
    }

    updatePermissionState(checkbox) {
        const moduleCode = checkbox.dataset.module;
        const permission = checkbox.dataset.permission;
        
        if (!this.currentPermissions[moduleCode]) {
            this.currentPermissions[moduleCode] = [];
        }
        
        if (checkbox.checked) {
            if (!this.currentPermissions[moduleCode].includes(permission)) {
                this.currentPermissions[moduleCode].push(permission);
            }
        } else {
            const index = this.currentPermissions[moduleCode].indexOf(permission);
            if (index > -1) {
                this.currentPermissions[moduleCode].splice(index, 1);
            }
        }
        
        // Update select-all button state
        this.updateSelectAllButton(moduleCode);
    }
    
    updateSelectAllButton(moduleCode) {
        const button = document.querySelector(`.select-all-module[data-module="${moduleCode}"]`);
        if (!button) return;
        
        const checkboxes = document.querySelectorAll(`input[data-module="${moduleCode}"]`);
        const allChecked = Array.from(checkboxes).every(cb => cb.checked);
        const someChecked = Array.from(checkboxes).some(cb => cb.checked);
        
        const icon = button.querySelector('i');
        if (icon) {
            if (allChecked) {
                icon.classList.remove('fa-square', 'fa-minus-square');
                icon.classList.add('fa-check-square');
                button.classList.add('text-success');
                button.classList.remove('text-muted', 'text-warning');
            } else if (someChecked) {
                icon.classList.remove('fa-square', 'fa-check-square');
                icon.classList.add('fa-minus-square');
                button.classList.add('text-warning');
                button.classList.remove('text-muted', 'text-success');
            } else {
                icon.classList.remove('fa-check-square', 'fa-minus-square');
                icon.classList.add('fa-square');
                button.classList.add('text-muted');
                button.classList.remove('text-success', 'text-warning');
            }
        }
    }

    handlePermissionDependencies(checkbox) {
        const moduleCode = checkbox.dataset.module;
        const permission = checkbox.dataset.permission;
        
        // Handle permission dependencies
        if (checkbox.checked) {
            // If checking create/edit/delete, also check view
            if (['create', 'edit', 'delete'].includes(permission)) {
                const viewCheckbox = document.querySelector(`input[data-module="${moduleCode}"][data-permission="view"]`);
                if (viewCheckbox && !viewCheckbox.checked) {
                    viewCheckbox.checked = true;
                    this.updatePermissionState(viewCheckbox);
                }
            }
        } else {
            // If unchecking view, uncheck all others
            if (permission === 'view') {
                ['create', 'edit', 'delete'].forEach(perm => {
                    const checkbox = document.querySelector(`input[data-module="${moduleCode}"][data-permission="${perm}"]`);
                    if (checkbox && checkbox.checked) {
                        checkbox.checked = false;
                        this.updatePermissionState(checkbox);
                    }
                });
            }
        }
    }

    updateRowHighlight(checkbox) {
        const row = checkbox.closest('tr');
        if (!row) return;
        
        const checkboxes = row.querySelectorAll('input[type="checkbox"]');
        const hasAnyChecked = Array.from(checkboxes).some(cb => cb.checked);
        
        row.classList.toggle('table-active', hasAnyChecked);
    }

    filterModules() {
        const searchTerm = this.elements.moduleSearch?.value.toLowerCase() || '';
        const rows = this.elements.permissionsBody?.querySelectorAll('tr');
        
        if (!rows) return;
        
        let visibleCount = 0;
        
        rows.forEach(row => {
            const moduleName = row.querySelector('.module-name')?.textContent.toLowerCase() || '';
            const isVisible = !searchTerm || moduleName.includes(searchTerm);
            
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) visibleCount++;
        });
        
        // Show/hide no results message
        this.toggleNoResults(visibleCount === 0);
    }

    toggleNoResults(show) {
        let noResultsRow = this.elements.permissionsBody?.querySelector('.no-results-row');
        
        if (show && !noResultsRow) {
            noResultsRow = document.createElement('tr');
            noResultsRow.className = 'no-results-row';
            noResultsRow.innerHTML = `
                <td colspan="6" class="text-center text-muted py-4">
                    <i class="fas fa-search fa-2x mb-2"></i>
                    <p>${this.options.translations.noResults}</p>
                </td>
            `;
            this.elements.permissionsBody?.appendChild(noResultsRow);
        } else if (!show && noResultsRow) {
            noResultsRow.remove();
        }
    }

    markAsDirty() {
        this.isDirty = true;
        this.updateSaveButton();
        
        if (this.options.autoSave) {
            this.scheduleAutoSave();
        }
    }

    updateSaveButton() {
        if (this.elements.saveButton) {
            this.elements.saveButton.disabled = !this.isDirty;
            
            const icon = this.elements.saveButton.querySelector('i');
            if (icon) {
                icon.className = this.isDirty ? 'fas fa-save text-warning' : 'fas fa-save';
            }
        }
    }

    scheduleAutoSave() {
        clearTimeout(this.autoSaveTimer);
        this.autoSaveTimer = setTimeout(() => {
            if (this.isDirty) {
                this.savePermissions(true);
            }
        }, this.options.autoSaveDelay);
    }

    async savePermissions(isAutoSave = false) {
        if (!this.isDirty && !isAutoSave) return;
        
        this.showLoading();
        const saveButton = this.elements.saveButton;
        const originalText = saveButton?.innerHTML;
        
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.innerHTML = `<i class="fas fa-spinner fa-spin"></i> ${this.options.translations.saving}`;
        }
        
        try {
            const formData = new FormData(this.elements.permissionsForm);
            formData.append('group_id', this.currentGroupId);
            
            // Convert permissions to proper format
            const permissions = {};
            for (const [key, value] of formData.entries()) {
                if (key.startsWith('permissions[')) {
                    const matches = key.match(/permissions\[([^\]]+)\]\[([^\]]+)\]/);
                    if (matches) {
                        const [, moduleCode, permission] = matches;
                        if (!permissions[moduleCode]) {
                            permissions[moduleCode] = [];
                        }
                        permissions[moduleCode].push(permission);
                    }
                }
            }
            
            const response = await fetch(`${this.options.apiBaseUrl}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.options.csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    group_id: this.currentGroupId,
                    permissions: permissions,
                    csrf_token: this.options.csrfToken
                })
            });
            
            if (!response.ok) {
                let errorMsg = `Failed to save permissions: ${response.status} ${response.statusText}`;
                try {
                    const errorData = await response.json();
                    if (errorData.error) errorMsg = errorData.error;
                } catch (e) {
                    // Response wasn't JSON
                    const textError = await response.text();
                    console.error('Server response:', textError);
                }
                throw new Error(errorMsg);
            }
            
            // Check if response has content
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                console.error('Response is not JSON:', contentType);
                throw new Error('Server returned non-JSON response');
            }
            
            const responseText = await response.text();
            if (!responseText) {
                throw new Error('Server returned empty response');
            }
            
            let result;
            try {
                result = JSON.parse(responseText);
            } catch (e) {
                console.error('Failed to parse JSON:', responseText);
                throw new Error('Invalid JSON response from server');
            }
            
            this.isDirty = false;
            this.originalPermissions = JSON.parse(JSON.stringify(this.currentPermissions));
            this.updateSaveButton();
            
            if (!isAutoSave) {
                this.showSuccess(this.options.translations.saved);
            }
            
            // Log audit event
            this.logAuditEvent('permissions_updated', {
                group_id: this.currentGroupId,
                changes: result.changes
            });
            
        } catch (error) {
            console.error('Error saving permissions:', error);
            this.showError(this.options.translations.error);
        } finally {
            this.hideLoading();
            if (saveButton) {
                saveButton.disabled = false;
                saveButton.innerHTML = originalText;
            }
        }
    }

    async confirmUnsavedChanges() {
        return new Promise((resolve) => {
            if (window.confirm('You have unsaved changes. Do you want to continue?')) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }

    toggleRowActions(row, direction) {
        // Mobile swipe actions
        const actionsDiv = row.querySelector('.mobile-actions') || this.createMobileActions(row);
        
        if (direction === 'left') {
            actionsDiv.classList.add('show');
        } else {
            actionsDiv.classList.remove('show');
        }
    }

    createMobileActions(row) {
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'mobile-actions';
        actionsDiv.innerHTML = `
            <button class="btn btn-sm btn-primary" onclick="window.permissionsManager && window.permissionsManager.selectAllModulePermissions(this)" 
                    data-module="${row.dataset.module}">
                <i class="fas fa-check-square"></i>
            </button>
            <button class="btn btn-sm btn-danger" onclick="window.permissionsManager && window.permissionsManager.clearModulePermissions(this)"
                    data-module="${row.dataset.module}">
                <i class="fas fa-times"></i>
            </button>
        `;
        row.appendChild(actionsDiv);
        return actionsDiv;
    }

    clearModulePermissions(button) {
        const moduleCode = button.dataset.module;
        const checkboxes = document.querySelectorAll(`input[data-module="${moduleCode}"]`);
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            this.updatePermissionState(checkbox);
        });
        
        this.markAsDirty();
        this.updateRowHighlight(checkboxes[0]);
    }

    addMobileIndicators() {
        const rows = this.elements.permissionsBody?.querySelectorAll('.module-row');
        rows?.forEach(row => {
            if (!row.querySelector('.swipe-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'swipe-indicator';
                indicator.innerHTML = '<i class="fas fa-chevron-left"></i>';
                row.appendChild(indicator);
            }
        });
    }

    isMobile() {
        return window.innerWidth < 768;
    }

    handleResponsive() {
        if (this.isMobile()) {
            this.enableMobileMode();
        } else {
            this.disableMobileMode();
        }
    }

    enableMobileMode() {
        document.body.classList.add('permissions-mobile-mode');
        this.addMobileIndicators();
    }

    disableMobileMode() {
        document.body.classList.remove('permissions-mobile-mode');
        document.querySelectorAll('.mobile-actions.show').forEach(el => {
            el.classList.remove('show');
        });
    }

    async logAuditEvent(action, data) {
        try {
            await fetch(`${this.options.apiBaseUrl}/audit/log`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.options.csrfToken
                },
                body: JSON.stringify({
                    action: action,
                    data: data,
                    timestamp: new Date().toISOString()
                })
            });
        } catch (error) {
            console.error('Failed to log audit event:', error);
        }
    }

    showSuccess(message) {
        // Use existing notification system if available
        if (window.toastr) {
            toastr.success(message);
        } else if (window.Swal) {
            Swal.fire({
                icon: 'success',
                title: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        } else {
            alert(message);
        }
    }

    showError(message) {
        if (window.toastr) {
            toastr.error(message);
        } else if (window.Swal) {
            Swal.fire({
                icon: 'error',
                title: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        } else {
            alert(message);
        }
    }

    // Public methods for external use
    async loadTemplate(templateName) {
        this.showLoading();
        
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/template?name=${templateName}`, {
                headers: {
                    'X-CSRF-Token': this.options.csrfToken,
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            if (!response.ok) throw new Error('Failed to load template');
            
            const data = await response.json();
            if (!data.success || !data.template) {
                throw new Error('Invalid template response');
            }
            this.currentPermissions = data.template.permissions || {};
            this.renderPermissionsTable();
            this.markAsDirty();
            
            // Close modal if exists
            const modal = bootstrap.Modal.getInstance(this.elements.templateModal);
            modal?.hide();
            
            this.showSuccess(`Template "${templateName}" loaded successfully`);
            
        } catch (error) {
            console.error('Error loading template:', error);
            this.showError('Failed to load template');
        } finally {
            this.hideLoading();
        }
    }

    async copyFromGroup() {
        // Show group selection modal
        const modal = new bootstrap.Modal(this.elements.copyModal || this.createCopyModal());
        modal.show();
    }

    createCopyModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'copyFromGroupModal';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Copy Permissions From Group</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="list-group" id="copyGroupList">
                            <!-- Groups will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        this.elements.copyModal = modal;
        
        // Load groups
        this.loadGroupsForCopy();
        
        return modal;
    }

    async loadGroupsForCopy() {
        const listContainer = document.getElementById('copyGroupList');
        if (!listContainer) return;
        
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/groups`);
            const groups = await response.json();
            
            listContainer.innerHTML = groups.map(group => {
                if (group.id === this.currentGroupId) return '';
                
                return `
                    <a href="#" class="list-group-item list-group-item-action"
                       onclick="window.permissionsManager && window.permissionsManager.executeCopyFromGroup(${group.id})">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${group.name}</h6>
                                ${group.description ? `<p class="mb-0 text-muted small">${group.description}</p>` : ''}
                            </div>
                            <span class="badge bg-primary">${group.user_count || 0} users</span>
                        </div>
                    </a>
                `;
            }).join('');
            
        } catch (error) {
            console.error('Error loading groups:', error);
            listContainer.innerHTML = '<p class="text-danger">Failed to load groups</p>';
        }
    }

    async executeCopyFromGroup(sourceGroupId) {
        this.showLoading();
        
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/group/${sourceGroupId}`);
            if (!response.ok) throw new Error('Failed to load source permissions');
            
            const data = await response.json();
            this.currentPermissions = data.permissions || {};
            this.renderPermissionsTable();
            this.markAsDirty();
            
            // Close modal
            const modal = bootstrap.Modal.getInstance(this.elements.copyModal);
            modal?.hide();
            
            this.showSuccess('Permissions copied successfully');
            
        } catch (error) {
            console.error('Error copying permissions:', error);
            this.showError('Failed to copy permissions');
        } finally {
            this.hideLoading();
        }
    }

    async resetToDefault() {
        if (!confirm(this.options.translations.confirmReset)) return;
        
        await this.loadTemplate('default');
    }

    async showAuditLog() {
        // This will be implemented in the audit log view
        window.location.href = '/permissions/audit';
    }

    async createNewGroup() {
        // Show create group modal
        const modal = new bootstrap.Modal(this.createNewGroupModal());
        modal.show();
    }

    createNewGroupModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'newGroupModal';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <form onsubmit="window.permissionsManager && window.permissionsManager.saveNewGroup(event); return false;">
                        <div class="modal-header">
                            <h5 class="modal-title">Create New Group</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="groupName" class="form-label">Group Name</label>
                                <input type="text" class="form-control" id="groupName" required>
                            </div>
                            <div class="mb-3">
                                <label for="groupDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="groupDescription" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Base Permissions</label>
                                <select class="form-select" id="baseTemplate">
                                    <option value="">None (Start Empty)</option>
                                    <option value="default">Default</option>
                                    <option value="admin">Administrator</option>
                                    <option value="manager">Manager</option>
                                    <option value="staff">Staff</option>
                                    <option value="readonly">Read Only</option>
                                </select>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Create Group
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
        return modal;
    }

    async saveNewGroup(event) {
        event.preventDefault();
        
        const name = document.getElementById('groupName').value;
        const description = document.getElementById('groupDescription').value;
        const baseTemplate = document.getElementById('baseTemplate').value;
        
        this.showLoading();
        
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/groups/create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': this.options.csrfToken
                },
                body: JSON.stringify({
                    name: name,
                    description: description,
                    base_template: baseTemplate
                })
            });
            
            if (!response.ok) throw new Error('Failed to create group');
            
            const newGroup = await response.json();
            
            // Reload page to show new group
            window.location.reload();
            
        } catch (error) {
            console.error('Error creating group:', error);
            this.showError('Failed to create group');
            this.hideLoading();
        }
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize on DOM ready (only if not already initialized by the view)
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if not already done by the view with custom options
    if (document.getElementById('permissionsForm') && !window.permissionsManager) {
        // Store globally for access from other scripts
        window.permissionsManager = new PermissionsManager({
            modules: window.permissionModules || [],
            csrfToken: document.querySelector('meta[name="csrf-token"]')?.content
        });
    }
});