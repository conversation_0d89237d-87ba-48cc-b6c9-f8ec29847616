<?php
/**
 * Test script to verify practitioner filtering for retrocession invoices
 * 
 * This script checks:
 * 1. That group 4 (practitioners) exists
 * 2. Which users are members of group 4
 * 3. That the invoice creation form properly filters these users
 */

require_once __DIR__ . '/app/config/bootstrap.php';

try {
    $db = Flight::db();
    
    echo "=== Testing Practitioner Group Filtering ===\n\n";
    
    // 1. Check if group 4 exists
    echo "1. Checking if group 4 exists:\n";
    $stmt = $db->prepare("SELECT * FROM user_groups WHERE id = 4");
    $stmt->execute();
    $group = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($group) {
        echo "   ✓ Group 4 found: " . $group['name'] . " (" . $group['description'] . ")\n\n";
    } else {
        echo "   ✗ Group 4 not found! You may need to create it.\n\n";
    }
    
    // 2. Check practitioner group configuration
    echo "2. Checking practitioner group configuration:\n";
    $stmt = $db->prepare("SELECT * FROM config_settings WHERE category = 'users' AND `key` = 'practitioner_group_id'");
    $stmt->execute();
    $config = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($config) {
        echo "   ✓ Configuration found: practitioner_group_id = " . $config['value'] . "\n\n";
    } else {
        echo "   ✗ Configuration not found! Run migration 018_add_practitioner_group_config.sql\n\n";
    }
    
    // 3. List members of group 4
    echo "3. Members of group 4 (practitioners):\n";
    $stmt = $db->prepare("
        SELECT u.id, u.username, u.email, CONCAT(u.first_name, ' ', u.last_name) as name,
               u.is_active, u.can_be_invoiced
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 4
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute();
    $practitioners = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($practitioners) {
        echo "   Found " . count($practitioners) . " practitioners:\n";
        foreach ($practitioners as $p) {
            $status = [];
            if (!$p['is_active']) $status[] = 'inactive';
            if (!$p['can_be_invoiced']) $status[] = 'non-billable';
            $statusStr = $status ? ' (' . implode(', ', $status) . ')' : '';
            echo "   - " . $p['name'] . " (" . $p['username'] . ")" . $statusStr . "\n";
        }
        echo "\n";
        
        // Count active and billable
        $activeBillable = array_filter($practitioners, function($p) {
            return $p['is_active'] && $p['can_be_invoiced'];
        });
        echo "   Active & billable practitioners: " . count($activeBillable) . "\n\n";
    } else {
        echo "   ✗ No practitioners found in group 4\n\n";
    }
    
    // 4. Compare with coach group (24)
    echo "4. For comparison - Members of group 24 (coaches):\n";
    $stmt = $db->prepare("
        SELECT u.id, u.username, CONCAT(u.first_name, ' ', u.last_name) as name
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 24 AND u.is_active = 1 AND u.can_be_invoiced = 1
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute();
    $coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "   Found " . count($coaches) . " active & billable coaches\n\n";
    
    // 5. Test the actual query used in InvoiceController
    echo "5. Testing the query from InvoiceController:\n";
    $stmt = $db->prepare("
        SELECT u.id, u.username, u.email, 
               CONCAT(u.first_name, ' ', u.last_name) as name,
               CASE WHEN ugm.user_id IS NOT NULL THEN 1 ELSE 0 END as is_coach,
               CASE WHEN ugm2.user_id IS NOT NULL THEN 1 ELSE 0 END as is_practitioner
        FROM users u
        LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.group_id = 24
        LEFT JOIN user_group_members ugm2 ON u.id = ugm2.user_id AND ugm2.group_id = 4
        WHERE u.is_active = 1 AND u.can_be_invoiced = 1
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $practitionerCount = count(array_filter($users, function($u) { return $u['is_practitioner'] == 1; }));
    $coachCount = count(array_filter($users, function($u) { return $u['is_coach'] == 1; }));
    
    echo "   Total active & billable users: " . count($users) . "\n";
    echo "   - Practitioners (group 4): " . $practitionerCount . "\n";
    echo "   - Coaches (group 24): " . $coachCount . "\n\n";
    
    echo "=== Test Complete ===\n";
    echo "\nTo test in the UI:\n";
    echo "1. Go to Invoices > Create Invoice\n";
    echo "2. Select a retrocession invoice type (RT30 or RT25)\n";
    echo "3. The 'Bill to' dropdown should show only practitioners when 'User' is selected\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}