/* Tailwind CSS Theme System - Color Schemes */

/* Base theme variables */
:root {
  --color-primary: 59 130 246; /* blue-500 */
  --color-primary-hover: 37 99 235; /* blue-600 */
  --color-secondary: 107 114 128; /* gray-500 */
  --color-accent: 168 85 247; /* purple-500 */
  --color-success: 34 197 94; /* green-500 */
  --color-warning: 251 146 60; /* orange-400 */
  --color-danger: 239 68 68; /* red-500 */
  --color-info: 14 165 233; /* sky-500 */
}

/* Ocean Blue Theme (Default) */
[data-theme="ocean"] {
  --color-primary: 59 130 246; /* blue-500 */
  --color-primary-hover: 37 99 235; /* blue-600 */
  --color-secondary: 107 114 128; /* gray-500 */
  --color-accent: 6 182 212; /* cyan-500 */
  --color-success: 34 197 94; /* green-500 */
  --color-warning: 251 146 60; /* orange-400 */
  --color-danger: 239 68 68; /* red-500 */
  --color-info: 14 165 233; /* sky-500 */
}

/* Emerald Green Theme */
[data-theme="emerald"] {
  --color-primary: 16 185 129; /* emerald-500 */
  --color-primary-hover: 5 150 105; /* emerald-600 */
  --color-secondary: 107 114 128; /* gray-500 */
  --color-accent: 20 184 166; /* teal-500 */
  --color-success: 34 197 94; /* green-500 */
  --color-warning: 251 191 36; /* amber-400 */
  --color-danger: 239 68 68; /* red-500 */
  --color-info: 6 182 212; /* cyan-500 */
}

/* Purple Haze Theme */
[data-theme="purple"] {
  --color-primary: 147 51 234; /* purple-600 */
  --color-primary-hover: 124 58 237; /* purple-700 */
  --color-secondary: 107 114 128; /* gray-500 */
  --color-accent: 236 72 153; /* pink-500 */
  --color-success: 134 239 172; /* green-300 */
  --color-warning: 253 224 71; /* yellow-300 */
  --color-danger: 251 113 133; /* rose-400 */
  --color-info: 165 180 252; /* indigo-300 */
}

/* Sunset Orange Theme */
[data-theme="sunset"] {
  --color-primary: 251 146 60; /* orange-400 */
  --color-primary-hover: 249 115 22; /* orange-500 */
  --color-secondary: 120 113 108; /* stone-500 */
  --color-accent: 244 114 182; /* pink-400 */
  --color-success: 134 239 172; /* green-300 */
  --color-warning: 252 211 77; /* amber-300 */
  --color-danger: 248 113 113; /* red-400 */
  --color-info: 125 211 252; /* sky-300 */
}

/* Midnight Dark Theme */
[data-theme="midnight"] {
  --color-primary: 99 102 241; /* indigo-500 */
  --color-primary-hover: 79 70 229; /* indigo-600 */
  --color-secondary: 148 163 184; /* slate-400 */
  --color-accent: 168 85 247; /* purple-500 */
  --color-success: 74 222 128; /* green-400 */
  --color-warning: 251 191 36; /* amber-400 */
  --color-danger: 248 113 113; /* red-400 */
  --color-info: 96 165 250; /* blue-400 */
}

/* Rose Gold Theme */
[data-theme="rose"] {
  --color-primary: 244 63 94; /* rose-500 */
  --color-primary-hover: 225 29 72; /* rose-600 */
  --color-secondary: 161 161 170; /* zinc-400 */
  --color-accent: 236 72 153; /* pink-500 */
  --color-success: 134 239 172; /* green-300 */
  --color-warning: 254 215 170; /* orange-200 */
  --color-danger: 251 113 133; /* rose-400 */
  --color-info: 191 219 254; /* blue-200 */
}

/* Monochrome Theme */
[data-theme="mono"] {
  --color-primary: 71 85 105; /* slate-600 */
  --color-primary-hover: 51 65 85; /* slate-700 */
  --color-secondary: 148 163 184; /* slate-400 */
  --color-accent: 100 116 139; /* slate-500 */
  --color-success: 107 114 128; /* gray-500 */
  --color-warning: 120 113 108; /* stone-500 */
  --color-danger: 87 83 78; /* stone-600 */
  --color-info: 148 163 184; /* slate-400 */
}

/* Neon Theme */
[data-theme="neon"] {
  --color-primary: 34 211 238; /* cyan-400 */
  --color-primary-hover: 6 182 212; /* cyan-500 */
  --color-secondary: 156 163 175; /* gray-400 */
  --color-accent: 217 70 239; /* fuchsia-500 */
  --color-success: 74 222 128; /* green-400 */
  --color-warning: 254 240 138; /* yellow-200 */
  --color-danger: 251 113 133; /* rose-400 */
  --color-info: 56 189 248; /* sky-400 */
}

/* Nature Theme */
[data-theme="nature"] {
  --color-primary: 132 204 22; /* lime-500 */
  --color-primary-hover: 101 163 13; /* lime-600 */
  --color-secondary: 120 113 108; /* stone-500 */
  --color-accent: 22 163 74; /* green-600 */
  --color-success: 34 197 94; /* green-500 */
  --color-warning: 217 119 6; /* amber-600 */
  --color-danger: 185 28 28; /* red-700 */
  --color-info: 2 132 199; /* sky-600 */
}

/* Corporate Blue Theme */
[data-theme="corporate"] {
  --color-primary: 30 64 175; /* blue-800 */
  --color-primary-hover: 30 58 138; /* blue-900 */
  --color-secondary: 71 85 105; /* slate-600 */
  --color-accent: 29 78 216; /* blue-700 */
  --color-success: 21 128 61; /* green-700 */
  --color-warning: 180 83 9; /* amber-700 */
  --color-danger: 185 28 28; /* red-700 */
  --color-info: 12 74 110; /* sky-800 */
}

/* Utility classes using CSS variables */
.bg-primary { background-color: rgb(var(--color-primary)) !important; }
.bg-primary-hover { background-color: rgb(var(--color-primary-hover)) !important; }
.bg-secondary { background-color: rgb(var(--color-secondary)) !important; }
.bg-accent { background-color: rgb(var(--color-accent)) !important; }
.bg-success { background-color: rgb(var(--color-success)) !important; }
.bg-warning { background-color: rgb(var(--color-warning)) !important; }
.bg-danger { background-color: rgb(var(--color-danger)) !important; }
.bg-info { background-color: rgb(var(--color-info)) !important; }

.text-primary { color: rgb(var(--color-primary)) !important; }
.text-primary-hover { color: rgb(var(--color-primary-hover)) !important; }
.text-secondary { color: rgb(var(--color-secondary)) !important; }
.text-accent { color: rgb(var(--color-accent)) !important; }
.text-success { color: rgb(var(--color-success)) !important; }
.text-warning { color: rgb(var(--color-warning)) !important; }
.text-danger { color: rgb(var(--color-danger)) !important; }
.text-info { color: rgb(var(--color-info)) !important; }

.border-primary { border-color: rgb(var(--color-primary)) !important; }
.border-primary-hover { border-color: rgb(var(--color-primary-hover)) !important; }
.border-secondary { border-color: rgb(var(--color-secondary)) !important; }
.border-accent { border-color: rgb(var(--color-accent)) !important; }
.border-success { border-color: rgb(var(--color-success)) !important; }
.border-warning { border-color: rgb(var(--color-warning)) !important; }
.border-danger { border-color: rgb(var(--color-danger)) !important; }
.border-info { border-color: rgb(var(--color-info)) !important; }

/* Ring colors for focus states */
.ring-primary { --tw-ring-color: rgb(var(--color-primary)) !important; }
.ring-accent { --tw-ring-color: rgb(var(--color-accent)) !important; }

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--color-primary)) 0%, rgb(var(--color-accent)) 100%);
}

.gradient-dark {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Theme transition */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}