<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

$db = Flight::db();

// Check invoice 238 or FAC-2025-0187
$stmt = $db->prepare("
    SELECT * FROM invoices 
    WHERE id = 238 OR invoice_number = 'FAC-2025-0187'
    LIMIT 1
");
$stmt->execute();
$invoice = $stmt->fetch(\PDO::FETCH_ASSOC);

if ($invoice) {
    echo "=== INVOICE DETAILS ===\n";
    echo "ID: {$invoice['id']}\n";
    echo "Number: {$invoice['invoice_number']}\n";
    echo "Payment Terms: " . ($invoice['payment_terms'] ?? 'NULL') . "\n";
    echo "Period: " . ($invoice['period'] ?? 'NULL') . "\n";
    echo "Subject: " . ($invoice['subject'] ?? 'NULL') . "\n";
    echo "User ID: " . ($invoice['user_id'] ?? 'NULL') . "\n";
    echo "Client ID: " . ($invoice['client_id'] ?? 'NULL') . "\n\n";
    
    // Get invoice lines
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ?
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute([$invoice['id']]);
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo "=== INVOICE LINES (" . count($lines) . " total) ===\n";
    foreach ($lines as $i => $line) {
        echo "\nLine " . ($i + 1) . ":\n";
        echo "- ID: {$line['id']}\n";
        echo "- Description: {$line['description']}\n";
        echo "- Quantity: {$line['quantity']}\n";
        echo "- Unit Price: {$line['unit_price']}\n";
        echo "- VAT Rate: {$line['vat_rate']}\n";
        echo "- Sort Order: " . ($line['sort_order'] ?? 'NULL') . "\n";
    }
    
    // Check for duplicates
    echo "\n=== CHECKING FOR DUPLICATE LINES ===\n";
    $descriptions = array_column($lines, 'description');
    $counts = array_count_values($descriptions);
    foreach ($counts as $desc => $count) {
        if ($count > 1) {
            echo "DUPLICATE: '$desc' appears $count times\n";
        }
    }
} else {
    echo "Invoice not found\n";
}

// Check current sequence
echo "\n=== CURRENT SEQUENCE ===\n";
$stmt = $db->query("
    SELECT * FROM document_sequences 
    WHERE document_type_id = 1 AND year = 2025 AND month IS NULL
");
$seq = $stmt->fetch(\PDO::FETCH_ASSOC);
if ($seq) {
    echo "Last number: {$seq['last_number']}\n";
    echo "Next invoice will be: FAC-2025-" . str_pad($seq['last_number'] + 1, 4, '0', STR_PAD_LEFT) . "\n";
}