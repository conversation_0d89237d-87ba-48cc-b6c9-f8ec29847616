<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test Client Dropdown</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
    </style>
</head>
<body>
    <h1>Client Dropdown Diagnostic</h1>
    
    <div class="test-section">
        <h2>Test 1: Check if invoice-create-fix.js is causing issues</h2>
        <p>Open the invoice creation page and check browser console for these messages:</p>
        <ul>
            <li>✓ "Invoice create script - Loading client/user data..."</li>
            <li>✓ "Clients data loaded successfully: X clients"</li>
            <li>✓ "Invoice form fix loaded"</li>
            <li>✓ "Billable type changed to: client" (when selecting Client)</li>
            <li>✓ "loadBillableOptions called with type: client"</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Check Browser Console</h2>
        <ol>
            <li>Open invoice creation page: <a href="/fit/public/invoices/create" target="_blank">/fit/public/invoices/create</a></li>
            <li>Open browser developer tools (F12)</li>
            <li>Go to Console tab</li>
            <li>Clear console</li>
            <li>Select "Client" in "Adresse de facturation" dropdown</li>
            <li>You should see console messages about loading clients</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Manual JavaScript Test</h2>
        <p>In the browser console on the invoice page, type:</p>
        <pre>
// Check if clients data is loaded
console.log('Clients data:', clientsData);

// Check if function exists
console.log('loadBillableOptions exists:', typeof loadBillableOptions);

// Manually trigger the function
loadBillableOptions();

// Check dropdown content
console.log('Billable dropdown options:', document.getElementById('billable_id').options.length);
        </pre>
    </div>
    
    <div class="test-section">
        <h2>Test 4: Check for JavaScript Errors</h2>
        <p>Look for any red error messages in the console, especially:</p>
        <ul>
            <li>Syntax errors</li>
            <li>Reference errors (undefined variables)</li>
            <li>Network errors (failed API calls)</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Expected Behavior</h2>
        <p class="success">When you select "Client" in "Adresse de facturation":</p>
        <ul>
            <li>The second dropdown should be enabled</li>
            <li>It should show a list of clients (6 clients based on your HTML)</li>
            <li>The green "+" button should be enabled</li>
            <li>Hint text should show "Client introuvable ? Cliquez sur + pour en ajouter un nouveau"</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>If Still Not Working</h2>
        <p>Try these steps:</p>
        <ol>
            <li>Hard refresh the page (Ctrl+Shift+R or Cmd+Shift+R)</li>
            <li>Clear browser cache</li>
            <li>Check if any browser extensions are blocking JavaScript</li>
            <li>Try in an incognito/private window</li>
            <li>Try a different browser</li>
        </ol>
    </div>
</body>
</html>