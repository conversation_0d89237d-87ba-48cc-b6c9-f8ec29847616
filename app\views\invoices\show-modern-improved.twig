{% extends 'base-modern.twig' %}

{% block title %}{{ __('invoices.invoice') }} #{{ invoice.invoice_number }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="page-header d-print-none">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">
                    {{ __('invoices.invoice') }} #{{ invoice.invoice_number }}
                </h2>
                <div class="text-muted mt-1">
                    <span class="badge bg-{{ invoice.status == 'paid' ? 'success' : (invoice.status == 'overdue' ? 'danger' : 'warning') }}">
                        {{ __('invoices.status.' ~ invoice.status) }}
                    </span>
                </div>
            </div>
            <div class="col-auto ms-auto d-print-none">
                <div class="btn-list">
                    <button type="button" class="btn btn-primary" onclick="window.print()">
                        <i class="bi bi-printer"></i> {{ __('common.print') }}
                    </button>
                    <a href="{{ url('/invoices/' ~ invoice.id ~ '/pdf') }}" class="btn btn-secondary">
                        <i class="bi bi-file-pdf"></i> {{ __('common.download_pdf') }}
                    </a>
                    {% if canEdit %}
                        <a href="{{ url('/invoices/' ~ invoice.id ~ '/edit') }}" class="btn btn-warning">
                            <i class="bi bi-pencil"></i> {{ __('common.edit') }}
                        </a>
                    {% endif %}
                    <a href="{{ url('/invoices') }}" class="btn btn-light">
                        <i class="bi bi-arrow-left"></i> {{ __('common.back') }}
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <!-- Enhanced Header Section with Better Layout -->
                    <div class="row mb-5">
                        <!-- Company Information - Left Side -->
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="border rounded p-4 h-100">
                                <h5 class="text-primary mb-3">{{ __('invoices.from') }}</h5>
                                <div class="mb-2">
                                    <h4 class="mb-1">{{ company_name|default('Company Name') }}</h4>
                                    {% if company_logo %}
                                        <img src="{{ company_logo }}" alt="{{ company_name }}" class="mb-3" style="max-height: 60px;">
                                    {% endif %}
                                </div>
                                
                                <div class="address-block">
                                    {% if company_address %}
                                        <p class="mb-1">
                                            <i class="bi bi-geo-alt text-muted me-2"></i>
                                            {{ company_address }}
                                        </p>
                                    {% endif %}
                                    {% if company_city or company_postal_code %}
                                        <p class="mb-1">
                                            <i class="bi bi-pin-map text-muted me-2"></i>
                                            {{ company_postal_code }} {{ company_city }}
                                        </p>
                                    {% endif %}
                                    {% if company_country %}
                                        <p class="mb-2">
                                            <i class="bi bi-globe text-muted me-2"></i>
                                            {{ company_country }}
                                        </p>
                                    {% endif %}
                                    
                                    <hr class="my-2">
                                    
                                    {% if company_phone %}
                                        <p class="mb-1">
                                            <i class="bi bi-telephone text-muted me-2"></i>
                                            {{ company_phone }}
                                        </p>
                                    {% endif %}
                                    {% if company_email %}
                                        <p class="mb-1">
                                            <i class="bi bi-envelope text-muted me-2"></i>
                                            {{ company_email }}
                                        </p>
                                    {% endif %}
                                    {% if company_vat_number %}
                                        <p class="mb-0">
                                            <i class="bi bi-briefcase text-muted me-2"></i>
                                            {{ __('config.vat_number') }}: {{ company_vat_number }}
                                        </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Recipient Information - Right Side -->
                        <div class="col-lg-6 col-md-12 mb-4">
                            <div class="border rounded p-4 h-100 bg-light">
                                <h5 class="text-primary mb-3">{{ __('invoices.bill_to') }}</h5>
                                
                                {% if invoice.patient_id %}
                                    <!-- Patient Information -->
                                    <div class="recipient-info">
                                        <h4 class="mb-3">{{ invoice.patient.first_name }} {{ invoice.patient.last_name }}</h4>
                                        
                                        <div class="address-block">
                                            {% if invoice.patient.address %}
                                                <p class="mb-1">
                                                    <i class="bi bi-house text-muted me-2"></i>
                                                    {{ invoice.patient.address }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.patient.address_line2 %}
                                                <p class="mb-1 ms-4">
                                                    {{ invoice.patient.address_line2 }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.patient.postal_code or invoice.patient.city %}
                                                <p class="mb-1">
                                                    <i class="bi bi-pin-map text-muted me-2"></i>
                                                    {{ invoice.patient.postal_code }} {{ invoice.patient.city }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.patient.country %}
                                                <p class="mb-2">
                                                    <i class="bi bi-globe text-muted me-2"></i>
                                                    {{ invoice.patient.country }}
                                                </p>
                                            {% endif %}
                                            
                                            <hr class="my-2">
                                            
                                            {% if invoice.patient.phone %}
                                                <p class="mb-1">
                                                    <i class="bi bi-telephone text-muted me-2"></i>
                                                    {{ invoice.patient.phone }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.patient.email %}
                                                <p class="mb-1">
                                                    <i class="bi bi-envelope text-muted me-2"></i>
                                                    {{ invoice.patient.email }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.patient.patient_number %}
                                                <p class="mb-0">
                                                    <i class="bi bi-person-badge text-muted me-2"></i>
                                                    {{ __('patients.patient_number') }}: {{ invoice.patient.patient_number }}
                                                </p>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% else %}
                                    <!-- Client Information -->
                                    <div class="recipient-info">
                                        <h4 class="mb-3">
                                            {% if invoice.client.company_name %}
                                                {{ invoice.client.company_name }}
                                            {% else %}
                                                {{ invoice.client.first_name }} {{ invoice.client.last_name }}
                                            {% endif %}
                                        </h4>
                                        
                                        <div class="address-block">
                                            {% if invoice.client.vat_number %}
                                                <p class="mb-2">
                                                    <i class="bi bi-briefcase text-muted me-2"></i>
                                                    <strong>{{ __('config.vat_number') }}:</strong> {{ invoice.client.vat_number }}
                                                </p>
                                            {% endif %}
                                            
                                            {% if invoice.client.address %}
                                                <p class="mb-1">
                                                    <i class="bi bi-building text-muted me-2"></i>
                                                    {{ invoice.client.address }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.client.address_line2 %}
                                                <p class="mb-1 ms-4">
                                                    {{ invoice.client.address_line2 }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.client.postal_code or invoice.client.city %}
                                                <p class="mb-1">
                                                    <i class="bi bi-pin-map text-muted me-2"></i>
                                                    {{ invoice.client.postal_code }} {{ invoice.client.city }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.client.country %}
                                                <p class="mb-2">
                                                    <i class="bi bi-globe text-muted me-2"></i>
                                                    {{ invoice.client.country }}
                                                </p>
                                            {% endif %}
                                            
                                            <hr class="my-2">
                                            
                                            {% if invoice.client.phone %}
                                                <p class="mb-1">
                                                    <i class="bi bi-telephone text-muted me-2"></i>
                                                    {{ invoice.client.phone }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.client.email %}
                                                <p class="mb-1">
                                                    <i class="bi bi-envelope text-muted me-2"></i>
                                                    {{ invoice.client.email }}
                                                </p>
                                            {% endif %}
                                            {% if invoice.client.client_number %}
                                                <p class="mb-0">
                                                    <i class="bi bi-person-badge text-muted me-2"></i>
                                                    {{ __('clients.client_number') }}: {{ invoice.client.client_number }}
                                                </p>
                                            {% endif %}
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Details Section -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="bg-light rounded p-3">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <strong>{{ __('invoices.invoice_number') }}:</strong><br>
                                        <span class="h5 text-primary">{{ invoice.invoice_number }}</span>
                                    </div>
                                    <div class="col-sm-3">
                                        <strong>{{ __('invoices.issue_date') }}:</strong><br>
                                        {{ invoice.issue_date|date('d/m/Y') }}
                                    </div>
                                    <div class="col-sm-3">
                                        <strong>{{ __('invoices.due_date') }}:</strong><br>
                                        {{ invoice.due_date|date('d/m/Y') }}
                                    </div>
                                    <div class="col-sm-3">
                                        <strong>{{ __('invoices.status') }}:</strong><br>
                                        <span class="badge bg-{{ invoice.status == 'paid' ? 'success' : (invoice.status == 'overdue' ? 'danger' : 'warning') }}">
                                            {{ __('invoices.status.' ~ invoice.status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="table-responsive mb-4">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 50%">{{ __('invoices.description') }}</th>
                                    <th class="text-center" style="width: 10%">{{ __('invoices.quantity') }}</th>
                                    <th class="text-end" style="width: 15%">{{ __('invoices.unit_price') }}</th>
                                    <th class="text-center" style="width: 10%">{{ __('invoices.vat_rate') }}</th>
                                    <th class="text-end" style="width: 15%">{{ __('invoices.total') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in invoice.lines %}
                                <tr>
                                    <td>
                                        {{ item.description }}
                                        {% if item.long_description %}
                                            <br><small class="text-muted">{{ item.long_description }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">{{ item.quantity|number_format(2) }}</td>
                                    <td class="text-end">{{ item.unit_price|number_format(2) }} €</td>
                                    <td class="text-center">{{ item.vat_rate }} %</td>
                                    <td class="text-end">{{ item.total_amount|number_format(2) }} €</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                    <td class="text-end"><strong>{{ invoice.subtotal|number_format(2) }} €</strong></td>
                                </tr>
                                <tr>
                                    <td colspan="4" class="text-end">{{ __('invoices.vat') }}:</td>
                                    <td class="text-end">{{ invoice.total_vat|number_format(2) }} €</td>
                                </tr>
                                <tr class="table-active">
                                    <td colspan="4" class="text-end"><strong>{{ __('invoices.total') }}:</strong></td>
                                    <td class="text-end"><strong class="h5 mb-0">{{ invoice.total_amount|number_format(2) }} €</strong></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <!-- Payment Information -->
                    {% if invoice.payments and invoice.payments|length > 0 %}
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>{{ __('invoices.payment_history') }}</h5>
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{{ __('invoices.payment_date') }}</th>
                                        <th>{{ __('invoices.payment_method') }}</th>
                                        <th>{{ __('invoices.reference') }}</th>
                                        <th class="text-end">{{ __('invoices.amount') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in invoice.payments %}
                                    <tr>
                                        <td>{{ payment.payment_date|date('d/m/Y') }}</td>
                                        <td>{{ payment.payment_method_name|default(payment.payment_method) }}</td>
                                        <td>{{ payment.reference|default('-') }}</td>
                                        <td class="text-end">{{ payment.amount|number_format(2) }} €</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>{{ __('invoices.total_paid') }}:</strong></td>
                                        <td class="text-end"><strong>{{ invoice.paid_amount|number_format(2) }} €</strong></td>
                                    </tr>
                                    <tr>
                                        <td colspan="3" class="text-end"><strong>{{ __('invoices.balance_due') }}:</strong></td>
                                        <td class="text-end"><strong>{{ (invoice.total_amount - invoice.paid_amount)|number_format(2) }} €</strong></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Notes Section -->
                    {% if invoice.notes %}
                    <div class="row">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6 class="alert-heading">{{ __('common.notes') }}</h6>
                                <p class="mb-0">{{ invoice.notes|nl2br }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom styles for better address display */
.address-block {
    font-size: 0.95rem;
    line-height: 1.6;
}

.address-block i {
    width: 20px;
    font-size: 0.9rem;
}

.recipient-info h4 {
    color: #2c3e50;
    font-weight: 600;
}

.border.rounded {
    border-color: #dee2e6 !important;
}

@media print {
    .page-header,
    .btn-list {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
    
    .address-block {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}