<?php
/**
 * Test Authentication and Session
 */
session_start();

echo "<h1>Authentication Test</h1>";
echo "<pre>";

// Check session
echo "Session Status:\n";
echo "Session ID: " . session_id() . "\n";
echo "Session Data: " . print_r($_SESSION, true) . "\n";

// Check if user is logged in
if (isset($_SESSION['user'])) {
    echo "\nUser is logged in:\n";
    echo "User ID: " . $_SESSION['user']['id'] . "\n";
    echo "User Name: " . $_SESSION['user']['name'] . "\n";
    echo "User Email: " . $_SESSION['user']['email'] . "\n";
} else {
    echo "\nUser is NOT logged in.\n";
    echo "This might be why the dashboard shows an error.\n";
}

echo "</pre>";

echo "<hr>";
echo "<h2>Options:</h2>";
echo "<ol>";
echo "<li><a href='/fit/public/login'>Go to Login Page</a></li>";
echo "<li><a href='/fit/public/'>Try Dashboard Again</a></li>";
echo "<li><a href='debug_dashboard.php'>Run Full Debug</a></li>";
echo "</ol>";

// Try to set a test user session if not logged in
if (!isset($_SESSION['user'])) {
    echo "<hr>";
    echo "<h3>Want to bypass login for testing?</h3>";
    echo "<form method='post' action='test_auth.php'>";
    echo "<button type='submit' name='bypass_login' value='1'>Set Test User Session</button>";
    echo "</form>";
}

// Handle bypass login
if (isset($_POST['bypass_login'])) {
    $_SESSION['user'] = [
        'id' => 1,
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'role' => 'admin',
        'language' => 'fr'
    ];
    $_SESSION['user_id'] = 1;
    $_SESSION['user_language'] = 'fr';
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    
    echo "<p style='color: green;'>Test user session created! <a href='/fit/public/'>Go to Dashboard</a></p>";
}