<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CLEARING PAYMENT TERMS CACHE ===\n\n";

try {
    $cache = Flight::cache();
    
    // Clear all possible language variations
    $languages = ['fr', 'en', 'de'];
    foreach ($languages as $lang) {
        $cacheKey = 'payment_terms_active_' . $lang;
        $cache->delete($cacheKey);
        echo "Cleared cache for language: $lang\n";
    }
    
    // Also check and fix any data issues
    $db = Flight::db();
    
    echo "\n=== CHECKING PAYMENT TERMS DATA ===\n";
    $stmt = $db->query("SELECT * FROM config_payment_terms ORDER BY sort_order ASC, id ASC");
    $terms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($terms as $term) {
        echo "\nTerm ID {$term['id']} - Code: {$term['code']}\n";
        echo "  Days: {$term['days']}\n";
        echo "  Active: " . ($term['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  Default: " . ($term['is_default'] ? 'Yes' : 'No') . "\n";
        
        // Decode and show names
        if (!empty($term['name']) && $term['name'] !== 'null') {
            $names = json_decode($term['name'], true);
            if ($names) {
                echo "  Names: " . json_encode($names) . "\n";
            }
        }
        
        // Check for issues
        if (strpos($term['code'], 'net_30') !== false && $term['days'] != 30) {
            echo "  ⚠️  WARNING: Net 30 has {$term['days']} days instead of 30!\n";
            $updateStmt = $db->prepare("UPDATE config_payment_terms SET days = 30 WHERE id = ?");
            $updateStmt->execute([$term['id']]);
            echo "  ✓ Fixed to 30 days\n";
        }
    }
    
    echo "\n=== DONE ===\n";
    echo "Cache cleared and data verified. Please refresh the invoice creation page.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}