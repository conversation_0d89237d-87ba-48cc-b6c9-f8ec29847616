<?php

use App\Controllers\ClientController;

// Client search (AJAX) - must come before other client routes
Flight::route('GET /clients/search', [ClientController::class, 'search']);

// Quick add client (AJAX) - for invoice form
Flight::route('POST /clients/quick-add', [ClientController::class, 'quickAdd']);

// API endpoints
Flight::route('GET /api/clients/active', [ClientController::class, 'getActiveClients']);

// Client routes
Flight::route('GET /clients', [ClientController::class, 'index']);
Flight::route('GET /clients/create', [ClientController::class, 'create']);
Flight::route('POST /clients', [ClientController::class, 'store']);
Flight::route('POST /clients/store', [ClientController::class, 'store']); // Alias for form compatibility
Flight::route('GET /clients/@id', [ClientController::class, 'show']);
Flight::route('GET /clients/@id/', [ClientController::class, 'show']); // Handle trailing slash
Flight::route('GET /clients/@id/edit', [ClientController::class, 'edit']);
Flight::route('PUT /clients/@id', [ClientController::class, 'update']);
Flight::route('DELETE /clients/@id', [ClientController::class, 'delete']);
Flight::route('POST /clients/@id/toggle-status', [ClientController::class, 'toggleStatus']);
Flight::route('POST /clients/bulk-delete', [ClientController::class, 'bulkDelete']);