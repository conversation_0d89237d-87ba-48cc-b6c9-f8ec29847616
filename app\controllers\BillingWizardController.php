<?php

namespace App\Controllers;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Services\RetrocessionCalculator;
use Exception;
use PDO;

class BillingWizardController extends \App\Core\Controller
{
    private $invoice;
    private $client;
    private $calculator;
    
    // Wizard steps
    const STEP_SELECTION = 1;
    const STEP_RECURRING = 2;
    const STEP_RETROCESSION = 3;
    const STEP_REVIEW = 4;
    const STEP_CONFIRM = 5;
    
    public function __construct()
    {
        $this->invoice = new Invoice();
        $this->client = new Client();
        $this->calculator = new RetrocessionCalculator();
    }
    
    /**
     * Start billing wizard
     */
    public function index()
    {
        $month = Flight::request()->query->month ?: date('n');
        $year = Flight::request()->query->year ?: date('Y');
        
        // Check if wizard session exists
        $session = $this->getWizardSession($month, $year);
        
        if ($session && $session['status'] !== 'completed') {
            // Resume existing session
            Flight::redirect($this->url('/billing-wizard/step/' . $session['current_step'] . '?month=' . $month . '&year=' . $year));
            return;
        }
        
        // Start new wizard
        $this->render('billing-wizard/start', [
            'month' => $month,
            'year' => $year,
            'monthName' => $this->getMonthName($month),
            'existingSession' => $session
        ]);
    }
    
    /**
     * Start new wizard session
     */
    public function start()
    {
        try {
            $month = Flight::request()->data->month;
            $year = Flight::request()->data->year;
            
            // Check for existing session
            $existing = $this->getWizardSession($month, $year);
            if ($existing && $existing['status'] !== 'completed') {
                throw new Exception(__('wizard.session_already_exists'));
            }
            
            // Create new session
            $sessionId = $this->createWizardSession($month, $year);
            
            Flight::redirect($this->url('/billing-wizard/step/1?month=' . $month . '&year=' . $year));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard'));
        }
    }
    
    /**
     * Show wizard step
     */
    public function step($stepNumber)
    {
        $month = Flight::request()->query->month;
        $year = Flight::request()->query->year;
        
        $session = $this->getWizardSession($month, $year);
        if (!$session) {
            Flight::flash('error', __('wizard.no_session'));
            Flight::redirect($this->url('/billing-wizard'));
            return;
        }
        
        // Update current step
        $this->updateSessionStep($session['id'], $stepNumber);
        
        switch ($stepNumber) {
            case self::STEP_SELECTION:
                $this->stepSelection($session);
                break;
                
            case self::STEP_RECURRING:
                $this->stepRecurring($session);
                break;
                
            case self::STEP_RETROCESSION:
                $this->stepRetrocession($session);
                break;
                
            case self::STEP_REVIEW:
                $this->stepReview($session);
                break;
                
            case self::STEP_CONFIRM:
                $this->stepConfirm($session);
                break;
                
            default:
                Flight::notFound();
        }
    }
    
    /**
     * Step 1: Invoice type selection
     */
    private function stepSelection($session)
    {
        $this->render('billing-wizard/step-selection', [
            'session' => $session,
            'monthName' => $this->getMonthName($session['month']),
            'invoiceTypes' => [
                'recurring' => __('wizard.invoice_type.recurring'),
                'retrocession' => __('wizard.invoice_type.retrocession'),
                'both' => __('wizard.invoice_type.both')
            ]
        ]);
    }
    
    /**
     * Save step 1 and proceed
     */
    public function saveSelection()
    {
        try {
            $sessionId = Flight::request()->data->session_id;
            $types = Flight::request()->data->invoice_types;
            
            if (empty($types)) {
                throw new Exception(__('wizard.select_invoice_type'));
            }
            
            // Save selection
            $this->saveStepData($sessionId, self::STEP_SELECTION, [
                'invoice_types' => $types
            ]);
            
            // Determine next step
            $nextStep = in_array('recurring', $types) ? self::STEP_RECURRING : self::STEP_RETROCESSION;
            
            $session = $this->getWizardSessionById($sessionId);
            Flight::redirect($this->url('/billing-wizard/step/' . $nextStep . '?month=' . $session['month'] . '&year=' . $session['year']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard/step/1'));
        }
    }
    
    /**
     * Step 2: Recurring invoices
     */
    private function stepRecurring($session)
    {
        // Get practitioners with recurring invoices
        $practitioners = $this->getPractitionersWithRecurring($session['month'], $session['year']);
        
        $this->render('billing-wizard/step-recurring', [
            'session' => $session,
            'monthName' => $this->getMonthName($session['month']),
            'practitioners' => $practitioners,
            'previousStep' => self::STEP_SELECTION
        ]);
    }
    
    /**
     * Generate recurring invoices
     */
    public function generateRecurring()
    {
        try {
            $sessionId = Flight::request()->data->session_id;
            $practitionerIds = Flight::request()->data->practitioner_ids ?? [];
            
            $session = $this->getWizardSessionById($sessionId);
            $generated = [];
            $errors = [];
            
            foreach ($practitionerIds as $practitionerId) {
                try {
                    $invoice = $this->generateRecurringInvoice($practitionerId, $session['month'], $session['year']);
                    $generated[] = [
                        'practitioner_id' => $practitionerId,
                        'invoice_id' => $invoice['id'],
                        'invoice_number' => $invoice['invoice_number']
                    ];
                    
                    // Track in wizard
                    $this->addWizardInvoice($sessionId, $invoice['id'], 'recurring', self::STEP_RECURRING);
                    
                } catch (Exception $e) {
                    $errors[] = [
                        'practitioner_id' => $practitionerId,
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            // Save step data
            $this->saveStepData($sessionId, self::STEP_RECURRING, [
                'generated' => $generated,
                'errors' => $errors
            ]);
            
            // Determine next step
            $stepData = json_decode($session['step_data'], true);
            $types = $stepData[self::STEP_SELECTION]['invoice_types'] ?? [];
            $nextStep = in_array('retrocession', $types) ? self::STEP_RETROCESSION : self::STEP_REVIEW;
            
            Flight::redirect($this->url('/billing-wizard/step/' . $nextStep . '?month=' . $session['month'] . '&year=' . $session['year']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard/step/2'));
        }
    }
    
    /**
     * Step 3: Retrocession invoices
     */
    private function stepRetrocession($session)
    {
        // Get retrocession data
        $retrocessions = $this->getRetrocessionData($session['month'], $session['year']);
        
        // Calculate previews
        foreach ($retrocessions as &$retro) {
            if ($retro['status'] === 'confirmed') {
                $retro['preview'] = $this->calculator->calculate([
                    'cns_amount' => $retro['cns_amount'],
                    'patient_amount' => $retro['patient_amount'],
                    'practitioner_id' => $retro['practitioner_id']
                ]);
            }
        }
        
        $this->render('billing-wizard/step-retrocession', [
            'session' => $session,
            'monthName' => $this->getMonthName($session['month']),
            'retrocessions' => $retrocessions,
            'previousStep' => $this->getPreviousStep($session, self::STEP_RETROCESSION)
        ]);
    }
    
    /**
     * Generate retrocession invoices
     */
    public function generateRetrocession()
    {
        try {
            $sessionId = Flight::request()->data->session_id;
            $dataEntryIds = Flight::request()->data->data_entry_ids ?? [];
            
            $session = $this->getWizardSessionById($sessionId);
            $generated = [];
            $errors = [];
            
            foreach ($dataEntryIds as $dataEntryId) {
                try {
                    // Get data entry details
                    $dataEntry = $this->getDataEntryById($dataEntryId);
                    
                    // Generate invoice
                    $invoice = $this->calculator->generateInvoice(
                        $dataEntry['practitioner_id'],
                        $dataEntry['period_month'],
                        $dataEntry['period_year']
                    );
                    
                    $generated[] = [
                        'practitioner_id' => $dataEntry['practitioner_id'],
                        'invoice_id' => $invoice['id'],
                        'invoice_number' => $invoice['invoice_number']
                    ];
                    
                    // Track in wizard
                    $this->addWizardInvoice($sessionId, $invoice['id'], 'retrocession', self::STEP_RETROCESSION);
                    
                } catch (Exception $e) {
                    $errors[] = [
                        'data_entry_id' => $dataEntryId,
                        'error' => $e->getMessage()
                    ];
                }
            }
            
            // Save step data
            $this->saveStepData($sessionId, self::STEP_RETROCESSION, [
                'generated' => $generated,
                'errors' => $errors
            ]);
            
            Flight::redirect($this->url('/billing-wizard/step/' . self::STEP_REVIEW . '?month=' . $session['month'] . '&year=' . $session['year']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard/step/3'));
        }
    }
    
    /**
     * Step 4: Review generated invoices
     */
    private function stepReview($session)
    {
        // Get all generated invoices
        $invoices = $this->getWizardInvoices($session['id']);
        
        // Group by type
        $groupedInvoices = [
            'recurring' => [],
            'retrocession' => []
        ];
        
        foreach ($invoices as $invoice) {
            $groupedInvoices[$invoice['invoice_type']][] = $invoice;
        }
        
        // Calculate totals
        $totals = $this->calculateWizardTotals($invoices);
        
        $this->render('billing-wizard/step-review', [
            'session' => $session,
            'monthName' => $this->getMonthName($session['month']),
            'invoices' => $groupedInvoices,
            'totals' => $totals,
            'previousStep' => $this->getPreviousStep($session, self::STEP_REVIEW)
        ]);
    }
    
    /**
     * Validate invoices
     */
    public function validateInvoices()
    {
        try {
            $sessionId = Flight::request()->data->session_id;
            $invoiceIds = Flight::request()->data->invoice_ids ?? [];
            
            // Update invoice statuses
            foreach ($invoiceIds as $invoiceId) {
                $this->updateWizardInvoiceStatus($sessionId, $invoiceId, 'validated');
            }
            
            $session = $this->getWizardSessionById($sessionId);
            Flight::redirect($this->url('/billing-wizard/step/' . self::STEP_CONFIRM . '?month=' . $session['month'] . '&year=' . $session['year']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard/step/4'));
        }
    }
    
    /**
     * Step 5: Confirm and send
     */
    private function stepConfirm($session)
    {
        // Get validated invoices
        $invoices = $this->getWizardInvoices($session['id'], 'validated');
        
        // Email preview
        $emailPreviews = [];
        foreach ($invoices as $invoice) {
            $emailPreviews[$invoice['invoice_id']] = $this->getEmailPreview($invoice['invoice_id']);
        }
        
        $this->render('billing-wizard/step-confirm', [
            'session' => $session,
            'monthName' => $this->getMonthName($session['month']),
            'invoices' => $invoices,
            'emailPreviews' => $emailPreviews,
            'previousStep' => self::STEP_REVIEW
        ]);
    }
    
    /**
     * Complete wizard and send invoices
     */
    public function complete()
    {
        try {
            $sessionId = Flight::request()->data->session_id;
            $sendEmails = Flight::request()->data->send_emails ?? false;
            $emailInvoiceIds = Flight::request()->data->email_invoice_ids ?? [];
            
            $db = Flight::db();
            $db->beginTransaction();
            
            try {
                // Get all validated invoices
                $invoices = $this->getWizardInvoices($sessionId, 'validated');
                
                $sent = 0;
                $errors = [];
                
                foreach ($invoices as $wizardInvoice) {
                    // Update invoice status
                    $this->invoice->updateStatus($wizardInvoice['invoice_id'], Invoice::STATUS_SENT);
                    
                    // Send email if requested
                    if ($sendEmails && in_array($wizardInvoice['invoice_id'], $emailInvoiceIds)) {
                        try {
                            $emailService = new \App\Services\EmailService();
                            $emailService->sendInvoiceEmail($wizardInvoice['invoice_id']);
                            $sent++;
                        } catch (Exception $e) {
                            $errors[] = $wizardInvoice['invoice_number'] . ': ' . $e->getMessage();
                        }
                    }
                    
                    // Update wizard invoice status
                    $this->updateWizardInvoiceStatus($sessionId, $wizardInvoice['invoice_id'], 'sent');
                }
                
                // Complete wizard session
                $this->completeWizardSession($sessionId, [
                    'total_invoices' => count($invoices),
                    'emails_sent' => $sent,
                    'errors' => $errors
                ]);
                
                $db->commit();
                
                // Set flash messages
                Flight::flash('success', __('wizard.completed_successfully', [
                    'count' => count($invoices),
                    'sent' => $sent
                ]));
                
                if (!empty($errors)) {
                    Flight::flash('warning', __('wizard.email_errors') . '<br>' . implode('<br>', $errors));
                }
                
                Flight::redirect($this->url('/billing-wizard/summary/' . $sessionId));
                
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard/step/5'));
        }
    }
    
    /**
     * Show wizard summary
     */
    public function summary($sessionId)
    {
        $session = $this->getWizardSessionById($sessionId);
        
        if (!$session || $session['status'] !== 'completed') {
            Flight::notFound();
            return;
        }
        
        $invoices = $this->getWizardInvoices($sessionId);
        $summary = json_decode($session['summary_data'], true);
        
        $this->render('billing-wizard/summary', [
            'session' => $session,
            'monthName' => $this->getMonthName($session['month']),
            'invoices' => $invoices,
            'summary' => $summary
        ]);
    }
    
    /**
     * Cancel wizard
     */
    public function cancel()
    {
        try {
            $sessionId = Flight::request()->data->session_id;
            
            // Delete draft invoices
            $this->deleteWizardDraftInvoices($sessionId);
            
            // Cancel session
            $this->cancelWizardSession($sessionId);
            
            Flight::flash('info', __('wizard.cancelled'));
            Flight::redirect($this->url('/billing-wizard'));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/billing-wizard'));
        }
    }
    
    /**
     * Private helper methods
     */
    
    private function getWizardSession($month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT * FROM billing_wizard_sessions
            WHERE month = :month AND year = :year
        ");
        
        $stmt->execute([':month' => $month, ':year' => $year]);
        return $stmt->fetch();
    }
    
    private function getWizardSessionById($id)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("SELECT * FROM billing_wizard_sessions WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    private function createWizardSession($month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            INSERT INTO billing_wizard_sessions (
                month, year, status, current_step, started_by
            ) VALUES (
                :month, :year, 'started', 1, :started_by
            )
        ");
        
        $stmt->execute([
            ':month' => $month,
            ':year' => $year,
            ':started_by' => $_SESSION['user_id'] ?? 1
        ]);
        
        return $db->lastInsertId();
    }
    
    private function updateSessionStep($sessionId, $step)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE billing_wizard_sessions 
            SET current_step = :step, 
                status = 'in_progress',
                updated_at = NOW() 
            WHERE id = :id
        ");
        
        $stmt->execute([':step' => $step, ':id' => $sessionId]);
    }
    
    private function saveStepData($sessionId, $step, $data)
    {
        $db = Flight::db();
        
        // Get existing step data
        $stmt = $db->prepare("SELECT step_data FROM billing_wizard_sessions WHERE id = ?");
        $stmt->execute([$sessionId]);
        $session = $stmt->fetch();
        
        $stepData = json_decode($session['step_data'] ?? '{}', true);
        $stepData[$step] = $data;
        
        // Update session
        $stmt = $db->prepare("
            UPDATE billing_wizard_sessions 
            SET step_data = :step_data, 
                updated_at = NOW() 
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':step_data' => json_encode($stepData),
            ':id' => $sessionId
        ]);
    }
    
    private function addWizardInvoice($sessionId, $invoiceId, $type, $step)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            INSERT INTO billing_wizard_invoices (
                session_id, invoice_id, invoice_type, step_created, status
            ) VALUES (
                :session_id, :invoice_id, :invoice_type, :step_created, 'draft'
            )
        ");
        
        $stmt->execute([
            ':session_id' => $sessionId,
            ':invoice_id' => $invoiceId,
            ':invoice_type' => $type,
            ':step_created' => $step
        ]);
    }
    
    private function getWizardInvoices($sessionId, $status = null)
    {
        $db = Flight::db();
        
        $sql = "
            SELECT 
                bwi.*,
                i.invoice_number,
                i.total,
                c.name as client_name
            FROM billing_wizard_invoices bwi
            JOIN invoices i ON bwi.invoice_id = i.id
            JOIN clients c ON i.client_id = c.id
            WHERE bwi.session_id = :session_id
        ";
        
        if ($status) {
            $sql .= " AND bwi.status = :status";
        }
        
        $sql .= " ORDER BY bwi.invoice_type, i.invoice_number";
        
        $stmt = $db->prepare($sql);
        $params = [':session_id' => $sessionId];
        
        if ($status) {
            $params[':status'] = $status;
        }
        
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    private function updateWizardInvoiceStatus($sessionId, $invoiceId, $status)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE billing_wizard_invoices 
            SET status = :status, 
                updated_at = NOW() 
            WHERE session_id = :session_id 
            AND invoice_id = :invoice_id
        ");
        
        $stmt->execute([
            ':status' => $status,
            ':session_id' => $sessionId,
            ':invoice_id' => $invoiceId
        ]);
    }
    
    private function completeWizardSession($sessionId, $summary)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE billing_wizard_sessions 
            SET status = 'completed',
                completed_at = NOW(),
                summary_data = :summary_data,
                updated_at = NOW() 
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':summary_data' => json_encode($summary),
            ':id' => $sessionId
        ]);
    }
    
    private function cancelWizardSession($sessionId)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE billing_wizard_sessions 
            SET status = 'cancelled',
                updated_at = NOW() 
            WHERE id = :id
        ");
        
        $stmt->execute([':id' => $sessionId]);
    }
    
    private function deleteWizardDraftInvoices($sessionId)
    {
        $db = Flight::db();
        
        // Get draft invoices
        $stmt = $db->prepare("
            SELECT invoice_id 
            FROM billing_wizard_invoices 
            WHERE session_id = :session_id 
            AND status = 'draft'
        ");
        $stmt->execute([':session_id' => $sessionId]);
        $invoiceIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Delete invoices
        foreach ($invoiceIds as $invoiceId) {
            $this->invoice->delete($invoiceId);
        }
    }
    
    private function getPractitionersWithRecurring($month, $year)
    {
        $db = Flight::db();
        
        // This is a simplified version - in production, you'd have more complex logic
        // to determine which practitioners should have recurring invoices
        $stmt = $db->prepare("
            SELECT 
                c.id,
                c.name,
                c.client_number,
                'rental' as invoice_type,
                1000 as monthly_amount
            FROM clients c
            WHERE c.client_type = 'practitioner'
            AND c.is_active = TRUE
            AND NOT EXISTS (
                SELECT 1 FROM invoices i
                WHERE i.client_id = c.id
                AND i.invoice_type = 'rental'
                AND MONTH(i.issue_date) = :month
                AND YEAR(i.issue_date) = :year
            )
            ORDER BY c.name
        ");
        
        $stmt->execute([':month' => $month, ':year' => $year]);
        return $stmt->fetchAll();
    }
    
    private function getRetrocessionData($month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                rde.*,
                c.name as practitioner_name,
                c.client_number
            FROM retrocession_data_entry rde
            JOIN clients c ON rde.practitioner_id = c.id
            WHERE rde.period_month = :month
            AND rde.period_year = :year
            AND rde.status IN ('confirmed', 'draft')
            AND rde.invoice_id IS NULL
            ORDER BY c.name
        ");
        
        $stmt->execute([':month' => $month, ':year' => $year]);
        return $stmt->fetchAll();
    }
    
    private function getDataEntryById($id)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("SELECT * FROM retrocession_data_entry WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    private function generateRecurringInvoice($practitionerId, $month, $year)
    {
        // This is a simplified version - implement your recurring invoice logic
        $data = [
            'client_id' => $practitionerId,
            'invoice_type' => 'rental',
            'issue_date' => sprintf('%04d-%02d-01', $year, $month),
            'due_date' => sprintf('%04d-%02d-01', $year, $month),
            'lines' => [
                [
                    'line_type' => 'rent',
                    'description' => 'Loyer mensuel',
                    'quantity' => 1,
                    'unit_price' => 1000,
                    'vat_rate' => 17
                ]
            ]
        ];
        
        return $this->invoice->createInvoice($data);
    }
    
    private function calculateWizardTotals($invoices)
    {
        $totals = [
            'count' => count($invoices),
            'subtotal' => 0,
            'vat' => 0,
            'total' => 0
        ];
        
        foreach ($invoices as $invoice) {
            $totals['total'] += $invoice['total'];
        }
        
        return $totals;
    }
    
    private function getEmailPreview($invoiceId)
    {
        // Get email template preview
        $emailService = new \App\Services\EmailService();
        return $emailService->getInvoiceEmailPreview($invoiceId);
    }
    
    private function getPreviousStep($session, $currentStep)
    {
        $stepData = json_decode($session['step_data'] ?? '{}', true);
        $types = $stepData[self::STEP_SELECTION]['invoice_types'] ?? [];
        
        switch ($currentStep) {
            case self::STEP_RECURRING:
                return self::STEP_SELECTION;
                
            case self::STEP_RETROCESSION:
                return in_array('recurring', $types) ? self::STEP_RECURRING : self::STEP_SELECTION;
                
            case self::STEP_REVIEW:
                return in_array('retrocession', $types) ? self::STEP_RETROCESSION : self::STEP_RECURRING;
                
            case self::STEP_CONFIRM:
                return self::STEP_REVIEW;
                
            default:
                return null;
        }
    }
    
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
}