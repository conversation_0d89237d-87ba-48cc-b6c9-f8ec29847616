<?php
/**
 * Add Items to Existing Invoice
 * Quick way to add items to invoices that were created without them
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

$db = Flight::db();
$message = '';
$error = '';

// Get invoice ID from URL or form
$invoiceId = $_GET['id'] ?? $_POST['invoice_id'] ?? null;
$invoiceNumber = $_GET['number'] ?? null;

// If we have a number but no ID, look it up
if (!$invoiceId && $invoiceNumber) {
    $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ?");
    $stmt->execute([$invoiceNumber]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result) {
        $invoiceId = $result['id'];
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $invoiceId) {
    try {
        // Start transaction
        $db->beginTransaction();
        
        // Get current invoice data
        $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$invoice) {
            throw new Exception("Invoice not found");
        }
        
        // Add the item - using only existing columns
        $stmt = $db->prepare("
            INSERT INTO invoice_items 
            (invoice_id, description, quantity, unit_price, vat_rate_id) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $quantity = floatval($_POST['quantity']);
        $unitPrice = floatval($_POST['unit_price']);
        $vatRateId = intval($_POST['vat_rate_id']);
        
        $stmt->execute([
            $invoiceId,
            $_POST['description'],
            $quantity,
            $unitPrice,
            $vatRateId
        ]);
        
        // Update invoice totals - calculate from quantity * unit_price
        // First calculate subtotal
        $stmt = $db->prepare("
            SELECT SUM(quantity * unit_price) as subtotal 
            FROM invoice_items 
            WHERE invoice_id = ?
        ");
        $stmt->execute([$invoiceId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $subtotal = $result['subtotal'] ?? 0;
        
        // Calculate VAT amount
        $stmt = $db->prepare("
            SELECT SUM(ii.quantity * ii.unit_price * (vr.rate / 100)) as vat_amount
            FROM invoice_items ii
            LEFT JOIN config_vat_rates vr ON ii.vat_rate_id = vr.id
            WHERE ii.invoice_id = ?
        ");
        $stmt->execute([$invoiceId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $vatAmount = $result['vat_amount'] ?? 0;
        
        // Update invoice
        $total = $subtotal + $vatAmount;
        $stmt = $db->prepare("
            UPDATE invoices 
            SET subtotal = ?, vat_amount = ?, total = ?
            WHERE id = ?
        ");
        $stmt->execute([$subtotal, $vatAmount, $total, $invoiceId]);
        
        $db->commit();
        
        $message = "Item added successfully! Total updated.";
        
        // Redirect to invoice view
        if ($_POST['redirect'] === 'view') {
            header("Location: /fit/public/invoices/" . $invoiceId);
            exit;
        }
        
    } catch (Exception $e) {
        $db->rollBack();
        $error = "Error: " . $e->getMessage();
    }
}

// Get invoice details
$invoice = null;
$items = [];
if ($invoiceId) {
    $stmt = $db->prepare("
        SELECT i.*, c.first_name, c.last_name, c.company_name 
        FROM invoices i 
        LEFT JOIN clients c ON i.client_id = c.id 
        WHERE i.id = ?
    ");
    $stmt->execute([$invoiceId]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get existing items
    $stmt = $db->prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
    $stmt->execute([$invoiceId]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get VAT rates
$stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1 ORDER BY rate");
$vatRates = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Add Invoice Items</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h1>Add Items to Invoice</h1>
    
    <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <?php if ($invoice): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3>Invoice: <?php echo htmlspecialchars($invoice['invoice_number']); ?></h3>
            </div>
            <div class="card-body">
                <p><strong>Client:</strong> 
                    <?php 
                    $clientName = $invoice['company_name'] ?: 
                                 $invoice['first_name'] . ' ' . $invoice['last_name'];
                    echo htmlspecialchars($clientName);
                    ?>
                </p>
                <p><strong>Current Total:</strong> €<?php echo number_format($invoice['total'], 2); ?></p>
                <p><strong>Status:</strong> <?php echo $invoice['status']; ?></p>
            </div>
        </div>
        
        <?php if (count($items) > 0): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h4>Existing Items</h4>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>Qty</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td>€<?php echo number_format($item['unit_price'], 2); ?></td>
                                    <td>€<?php echo number_format($item['quantity'] * $item['unit_price'], 2); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h4>Add New Item</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="invoice_id" value="<?php echo $invoiceId; ?>">
                    
                    <div class="mb-3">
                        <label class="form-label">Description *</label>
                        <input type="text" name="description" class="form-control" required 
                               placeholder="e.g., Consulting services, Product name, etc.">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Quantity *</label>
                                <input type="number" name="quantity" class="form-control" 
                                       value="1" min="0.01" step="0.01" required>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Unit Price (€) *</label>
                                <input type="number" name="unit_price" class="form-control" 
                                       min="0" step="0.01" required placeholder="100.00">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">VAT Rate *</label>
                                <select name="vat_rate_id" class="form-control" required>
                                    <?php foreach ($vatRates as $rate): ?>
                                        <option value="<?php echo $rate['id']; ?>" 
                                                <?php echo $rate['is_default'] ? 'selected' : ''; ?>>
                                            <?php echo $rate['rate']; ?>% - <?php echo htmlspecialchars($rate['code']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">After adding:</label>
                        <select name="redirect" class="form-control">
                            <option value="stay">Stay on this page (add more items)</option>
                            <option value="view">Go to invoice view</option>
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Add Item</button>
                    <a href="/fit/public/invoices/<?php echo $invoiceId; ?>" class="btn btn-secondary">
                        Back to Invoice
                    </a>
                </form>
            </div>
        </div>
        
    <?php else: ?>
        <div class="alert alert-warning">
            <h4>Select an Invoice</h4>
            <p>Please provide an invoice ID or number to add items.</p>
            
            <form method="GET" class="mt-3">
                <div class="input-group">
                    <input type="text" name="number" class="form-control" 
                           placeholder="Invoice number (e.g., FAC-2025-00064)">
                    <button type="submit" class="btn btn-primary">Load Invoice</button>
                </div>
            </form>
        </div>
    <?php endif; ?>
    
    <div class="mt-4">
        <a href="/fit/public/invoices" class="btn btn-outline-secondary">Back to Invoices</a>
    </div>
</div>
</body>
</html>