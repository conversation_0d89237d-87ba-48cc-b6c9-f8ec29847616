<?php
// Fixed migration with correct column types

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Running Migration: Add deleted invoice numbers pool (Fixed)</h2>";
    echo "<p>Database: $dbname</p>";
    
    // Check if table already exists
    $stmt = $db->query("SHOW TABLES LIKE 'deleted_invoice_numbers'");
    if ($stmt->fetch()) {
        echo "! Table deleted_invoice_numbers already exists<br>";
        // Drop it to recreate with correct types
        $db->exec("DROP TABLE deleted_invoice_numbers");
        echo "✓ Dropped existing table to recreate with correct column types<br>";
    }
    
    // Create deleted_invoice_numbers table with correct column types
    $sql = "CREATE TABLE deleted_invoice_numbers (
        id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
        document_type_id INT NOT NULL,
        invoice_type_id INT UNSIGNED NULL,
        invoice_number VARCHAR(100) NOT NULL,
        year INT NULL,
        month INT NULL,
        sequence_number INT NOT NULL,
        deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        deleted_by INT UNSIGNED NULL,
        reused_at TIMESTAMP NULL,
        reused_by INT UNSIGNED NULL,
        UNIQUE KEY unique_number (invoice_number),
        INDEX idx_available (document_type_id, year, month, reused_at),
        INDEX idx_sequence (document_type_id, invoice_type_id, year, month, sequence_number),
        FOREIGN KEY (document_type_id) REFERENCES document_types(id) ON DELETE CASCADE,
        FOREIGN KEY (invoice_type_id) REFERENCES config_invoice_types(id) ON DELETE SET NULL,
        FOREIGN KEY (deleted_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (reused_by) REFERENCES users(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $db->exec($sql);
    echo "✓ Created deleted_invoice_numbers table with correct column types<br>";
    
    // Add configuration option
    try {
        $stmt = $db->prepare("
            INSERT INTO config (config_key, config_value, created_at, updated_at) 
            VALUES ('reuse_deleted_invoice_numbers', 'true', NOW(), NOW())
            ON DUPLICATE KEY UPDATE config_value = 'true', updated_at = NOW()
        ");
        $stmt->execute();
        echo "✓ Added/Updated configuration: reuse_deleted_invoice_numbers = true<br>";
    } catch (Exception $e) {
        echo "! Error adding config: " . $e->getMessage() . "<br>";
    }
    
    // Check if index exists on invoices table
    try {
        $stmt = $db->query("SHOW INDEX FROM invoices WHERE Key_name = 'idx_invoice_number'");
        if ($stmt->fetch()) {
            echo "! Index idx_invoice_number already exists<br>";
        } else {
            $db->exec("ALTER TABLE invoices ADD INDEX idx_invoice_number (invoice_number)");
            echo "✓ Added index on invoices.invoice_number<br>";
        }
    } catch (Exception $e) {
        echo "! Could not add index: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><strong>Migration completed successfully!</strong><br>";
    echo "<br>The system will now reuse deleted invoice numbers when creating new invoices.";
    echo "<br><br>Next step:<br>";
    echo "<a href='fix_invoice_number_186.php' style='font-size: 16px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Fix Invoice Number FAC-2025-0190 → FAC-LOY-2025-0186</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
    echo "<br><br>";
    if (strpos($e->getMessage(), 'Access denied') !== false) {
        echo "Database connection failed. Check your .env file settings.";
    }
}
?>