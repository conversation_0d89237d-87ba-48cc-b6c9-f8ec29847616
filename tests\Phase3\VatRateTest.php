<?php

namespace Tests\Phase3;

use PHPUnit\Framework\TestCase;
use App\Models\ConfigVatRate;
use Flight;

class VatRateTest extends TestCase
{
    protected static $db;
    protected $testVatRates = [];
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        self::$db->exec("DELETE FROM config_vat_rates WHERE code LIKE 'TEST-%'");
    }
    
    public function testCreateVatRate()
    {
        $vatRate = ConfigVatRate::create([
            'code' => 'TEST-VAT-01',
            'name' => json_encode(['en' => 'Test VAT Rate', 'fr' => 'Taux TVA Test']),
            'rate' => 20.00,
            'is_default' => 0,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->assertNotNull($vatRate);
        $this->assertNotNull($vatRate->id);
        $this->assertEquals('TEST-VAT-01', $vatRate->code);
        $this->assertEquals(20.00, $vatRate->rate);
        
        $this->testVatRates[] = $vatRate->id;
        
        return $vatRate;
    }
    
    public function testGetLocalizedName()
    {
        // Create a VAT rate for this test
        $vatRate = ConfigVatRate::create([
            'code' => 'TEST-LOCALIZED',
            'name' => json_encode(['en' => 'Test VAT Rate', 'fr' => 'Taux TVA Test']),
            'rate' => 15.00,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testVatRates[] = $vatRate->id;
        
        // Test English name
        $englishName = $vatRate->getLocalizedName('en');
        $this->assertEquals('Test VAT Rate', $englishName);
        
        // Test French name
        $frenchName = $vatRate->getLocalizedName('fr');
        $this->assertEquals('Taux TVA Test', $frenchName);
        
        // Test fallback to English
        $germanName = $vatRate->getLocalizedName('de');
        $this->assertEquals('Test VAT Rate', $germanName);
    }
    
    public function testDefaultVatRate()
    {
        // Create a default VAT rate
        $defaultVat = ConfigVatRate::create([
            'code' => 'TEST-DEFAULT',
            'name' => json_encode(['en' => 'Default VAT', 'fr' => 'TVA par défaut']),
            'rate' => 17.00,
            'is_default' => 1,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testVatRates[] = $defaultVat->id;
        
        // Test getting default VAT rate
        $foundDefault = ConfigVatRate::where('is_default', '=', 1)
                                    ->where('is_active', '=', 1)
                                    ->first();
        
        $this->assertNotNull($foundDefault);
        $this->assertEquals($defaultVat->id, $foundDefault->id);
    }
    
    public function testVatCalculations()
    {
        $vatRate = new ConfigVatRate();
        $vatRate->rate = 17.00;
        
        // Test VAT calculation on amount
        $amount = 100.00;
        $vatAmount = $amount * ($vatRate->rate / 100);
        $totalWithVat = $amount + $vatAmount;
        
        $this->assertEquals(17.00, $vatAmount);
        $this->assertEquals(117.00, $totalWithVat);
        
        // Test reverse calculation (from total with VAT to get base amount)
        $totalAmount = 117.00;
        $baseAmount = $totalAmount / (1 + $vatRate->rate / 100);
        $calculatedVat = $totalAmount - $baseAmount;
        
        $this->assertEquals(100.00, round($baseAmount, 2));
        $this->assertEquals(17.00, round($calculatedVat, 2));
    }
    
    public function testMultipleVatRates()
    {
        // Create multiple VAT rates for Luxembourg
        $rates = [
            ['code' => 'TEST-SUPER-REDUCED', 'name' => 'Super Reduced', 'rate' => 3.00],
            ['code' => 'TEST-REDUCED', 'name' => 'Reduced', 'rate' => 8.00],
            ['code' => 'TEST-PARKING', 'name' => 'Parking', 'rate' => 14.00],
            ['code' => 'TEST-STANDARD', 'name' => 'Standard', 'rate' => 17.00],
            ['code' => 'TEST-EXEMPT', 'name' => 'Exempt', 'rate' => 0.00]
        ];
        
        foreach ($rates as $rateData) {
            $vatRate = ConfigVatRate::create([
                'code' => $rateData['code'],
                'name' => json_encode(['en' => $rateData['name']]),
                'rate' => $rateData['rate'],
                'is_active' => 1,
                'created_by' => 1
            ]);
            
            $this->testVatRates[] = $vatRate->id;
        }
        
        // Test retrieving all active rates
        $activeRates = ConfigVatRate::where('is_active', '=', 1)
                                   ->where('code', 'LIKE', 'TEST-%')
                                   ->orderBy('rate', 'ASC')
                                   ->get();
        
        $this->assertGreaterThanOrEqual(5, count($activeRates));
        
        // Verify rates are sorted correctly
        $previousRate = -1;
        foreach ($activeRates as $rate) {
            $this->assertGreaterThanOrEqual($previousRate, $rate->rate);
            $previousRate = $rate->rate;
        }
    }
    
    public function testVatRateStatus()
    {
        $vatRate = ConfigVatRate::create([
            'code' => 'TEST-STATUS',
            'name' => json_encode(['en' => 'Status Test']),
            'rate' => 15.00,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testVatRates[] = $vatRate->id;
        
        // Test active status
        $this->assertTrue($vatRate->is_active);
        
        // Deactivate
        $vatRate->is_active = 0;
        $vatRate->save();
        
        // Verify deactivated
        $deactivated = ConfigVatRate::find($vatRate->id);
        $this->assertFalse($deactivated->is_active);
    }
    
    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        self::$db->exec("DELETE FROM config_vat_rates WHERE code LIKE 'TEST-%'");
    }
}