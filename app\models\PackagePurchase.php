<?php

namespace App\Models;

use App\Core\Model;
use Exception;
use DateTime;

class PackagePurchase extends Model
{
    protected $table = 'package_purchases';
    
    protected $fillable = [
        'package_id',
        'client_id',
        'invoice_id',
        'purchase_date',
        'expiry_date',
        'sessions_total',
        'sessions_used',
        'status'
    ];
    
    protected $casts = [
        'purchase_date' => 'date',
        'expiry_date' => 'date',
        'sessions_total' => 'integer',
        'sessions_used' => 'integer'
    ];
    
    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_COMPLETED = 'completed';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';
    
    /**
     * Get the package
     */
    public function package()
    {
        return $this->belongsTo(ServicePackage::class, 'package_id');
    }
    
    /**
     * Get the client
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }
    
    /**
     * Get the invoice
     */
    public function invoice()
    {
        return $this->belongsTo(SalesInvoice::class, 'invoice_id');
    }
    
    /**
     * Get usage history
     */
    public function usage()
    {
        return $this->hasMany(PackageUsage::class, 'purchase_id');
    }
    
    /**
     * Get remaining sessions
     */
    public function getRemainingSessions()
    {
        return $this->sessions_total - $this->sessions_used;
    }
    
    /**
     * Get remaining sessions attribute
     */
    public function getRemainingSessionsAttribute()
    {
        return $this->getRemainingSessions();
    }
    
    /**
     * Check if package is expired
     */
    public function isExpired()
    {
        return $this->expiry_date < date('Y-m-d');
    }
    
    /**
     * Check if package is active
     */
    public function isActive()
    {
        return $this->status === self::STATUS_ACTIVE && !$this->isExpired();
    }
    
    /**
     * Check if package has available sessions
     */
    public function hasAvailableSessions()
    {
        return $this->getRemainingSessions() > 0;
    }
    
    /**
     * Use a session
     */
    public function useSession($serviceId, $therapistId = null, $invoiceId = null, $notes = null)
    {
        if (!$this->isActive()) {
            throw new Exception('Package is not active');
        }
        
        if (!$this->hasAvailableSessions()) {
            throw new Exception('No sessions remaining in this package');
        }
        
        // Check if service is included in package
        $package = $this->package;
        if ($package && !$package->includesService($serviceId)) {
            throw new Exception('Service is not included in this package');
        }
        
        // Create usage record
        $usage = PackageUsage::create([
            'purchase_id' => $this->id,
            'usage_date' => date('Y-m-d H:i:s'),
            'service_id' => $serviceId,
            'therapist_id' => $therapistId,
            'invoice_id' => $invoiceId,
            'notes' => $notes
        ]);
        
        // Update sessions used
        $this->sessions_used++;
        
        // Update status if all sessions are used
        if ($this->sessions_used >= $this->sessions_total) {
            $this->status = self::STATUS_COMPLETED;
        }
        
        $this->save();
        
        return $usage;
    }
    
    /**
     * Cancel the package
     */
    public function cancel($reason = null)
    {
        if ($this->status === self::STATUS_CANCELLED) {
            throw new Exception('Package is already cancelled');
        }
        
        $this->status = self::STATUS_CANCELLED;
        $this->save();
        
        // Log cancellation if needed
        if ($reason) {
            // Could log to an audit table
        }
        
        return true;
    }
    
    /**
     * Check and update status
     */
    public function checkAndUpdateStatus()
    {
        if ($this->status === self::STATUS_ACTIVE) {
            if ($this->isExpired()) {
                $this->status = self::STATUS_EXPIRED;
                $this->save();
            } elseif ($this->sessions_used >= $this->sessions_total) {
                $this->status = self::STATUS_COMPLETED;
                $this->save();
            }
        }
        
        return $this->status;
    }
    
    /**
     * Get usage percentage
     */
    public function getUsagePercentage()
    {
        if ($this->sessions_total == 0) {
            return 0;
        }
        
        return round(($this->sessions_used / $this->sessions_total) * 100, 2);
    }
    
    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiry()
    {
        $today = new DateTime();
        $expiry = new DateTime($this->expiry_date);
        
        if ($today > $expiry) {
            return -1; // Already expired
        }
        
        $interval = $today->diff($expiry);
        return $interval->days;
    }
    
    /**
     * Get status badge HTML
     */
    public function getStatusBadge()
    {
        $badges = [
            self::STATUS_ACTIVE => '<span class="badge bg-success">Active</span>',
            self::STATUS_COMPLETED => '<span class="badge bg-info">Completed</span>',
            self::STATUS_EXPIRED => '<span class="badge bg-warning">Expired</span>',
            self::STATUS_CANCELLED => '<span class="badge bg-danger">Cancelled</span>'
        ];
        
        return $badges[$this->status] ?? '<span class="badge bg-secondary">Unknown</span>';
    }
    
    /**
     * Scope for active packages
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                     ->where('expiry_date', '>=', date('Y-m-d'));
    }
    
    /**
     * Scope for expiring soon (within 30 days)
     */
    public function scopeExpiringSoon($query, $days = 30)
    {
        return $query->where('status', self::STATUS_ACTIVE)
                     ->where('expiry_date', '>=', date('Y-m-d'))
                     ->where('expiry_date', '<=', date('Y-m-d', strtotime("+{$days} days")));
    }
    
    /**
     * Get formatted purchase info
     */
    public function getFormattedInfo()
    {
        $package = $this->package;
        return sprintf(
            '%s - %d/%d sessions used (expires %s)',
            $package ? $package->name : 'Unknown Package',
            $this->sessions_used,
            $this->sessions_total,
            date('d/m/Y', strtotime($this->expiry_date))
        );
    }
}