{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.billing_wizard') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.billing_wizard') }}</h1>
        <a href="{{ base_url }}/invoices" class="btn btn-secondary">
            <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
        </a>
    </div>

    <!-- Wizard Description -->
    <div class="row mb-4">
        <div class="col-lg-8 mx-auto">
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-magic fs-1 text-primary mb-3"></i>
                    <h3 class="mb-3">{{ __('billing.wizard_welcome') }}</h3>
                    <p class="lead text-muted mb-4">
                        {{ __('billing.wizard_description') }}
                    </p>
                    
                    <!-- Wizard Features -->
                    <div class="row g-4 mb-4">
                        <div class="col-md-4">
                            <div class="d-flex flex-column align-items-center">
                                <div class="rounded-circle bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                    <i class="bi bi-calendar-check fs-2 text-primary"></i>
                                </div>
                                <h5>{{ __('billing.recurring_invoices') }}</h5>
                                <p class="text-muted">{{ __('billing.recurring_description') }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex flex-column align-items-center">
                                <div class="rounded-circle bg-success bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                    <i class="bi bi-percent fs-2 text-success"></i>
                                </div>
                                <h5>{{ __('billing.retrocession_calculation') }}</h5>
                                <p class="text-muted">{{ __('billing.retrocession_description') }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex flex-column align-items-center">
                                <div class="rounded-circle bg-info bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                    <i class="bi bi-lightning fs-2 text-info"></i>
                                </div>
                                <h5>{{ __('billing.batch_processing') }}</h5>
                                <p class="text-muted">{{ __('billing.batch_description') }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Start Button -->
                    <form method="POST" action="{{ base_url }}/billing-wizard/start">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-play-circle me-2"></i>{{ __('billing.start_wizard') }}
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Sessions -->
    {% if recent_sessions %}
    <div class="row">
        <div class="col-lg-10 mx-auto">
            <h4 class="mb-3">{{ __('billing.recent_sessions') }}</h4>
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ __('common.date') }}</th>
                                    <th>{{ __('billing.session_type') }}</th>
                                    <th>{{ __('billing.invoices_generated') }}</th>
                                    <th>{{ __('billing.total_amount') }}</th>
                                    <th>{{ __('common.status') }}</th>
                                    <th>{{ __('common.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in recent_sessions %}
                                <tr>
                                    <td>{{ session.created_at|date('d/m/Y H:i') }}</td>
                                    <td>
                                        {% if session.type == 'recurring' %}
                                            <span class="badge bg-primary">{{ __('billing.recurring') }}</span>
                                        {% elseif session.type == 'retrocession' %}
                                            <span class="badge bg-success">{{ __('billing.retrocession') }}</span>
                                        {% else %}
                                            <span class="badge bg-info">{{ __('billing.mixed') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ session.invoice_count }}</td>
                                    <td>{{ currency }}{{ session.total_amount|number_format(2, ',', ' ') }}</td>
                                    <td>
                                        {% if session.status == 'completed' %}
                                            <span class="badge bg-success">{{ __('common.completed') }}</span>
                                        {% elseif session.status == 'cancelled' %}
                                            <span class="badge bg-danger">{{ __('common.cancelled') }}</span>
                                        {% else %}
                                            <span class="badge bg-warning">{{ __('common.in_progress') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ base_url }}/billing-wizard/summary/{{ session.id }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> {{ __('common.view') }}
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}