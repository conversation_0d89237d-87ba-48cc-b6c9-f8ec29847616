<?php

namespace App\Models;

use PDO;
use Flight;
use Exception;

class User
{
    /**
     * Get all users with their groups
     */
    public static function getAllWithGroups()
    {
        $db = Flight::db();
        
        $query = "
            SELECT 
                u.*,
                GROUP_CONCAT(DISTINCT g.name ORDER BY g.name SEPARATOR ', ') as `groups`,
                GROUP_CONCAT(DISTINCT g.id) as group_ids
            FROM users u
            LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
            LEFT JOIN user_groups g ON ugm.group_id = g.id
            GROUP BY u.id
            ORDER BY u.first_name, u.last_name
        ";
        
        $stmt = $db->query($query);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find user by ID
     */
    public static function find($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find user by email
     */
    public static function findByEmail($email)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find user by username
     */
    public static function findByUsername($username)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM users WHERE username = ?");
        $stmt->execute([$username]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find user by username or email
     */
    public static function findByUsernameOrEmail($identifier)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$identifier, $identifier]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get user with groups
     */
    public static function getWithGroups($id)
    {
        $user = self::find($id);
        if (!$user) return null;
        
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT g.* 
            FROM user_groups g
            JOIN user_group_members ugm ON g.id = ugm.group_id
            WHERE ugm.user_id = ?
            ORDER BY g.name
        ");
        $stmt->execute([$id]);
        $user['groups'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $user;
    }
    
    /**
     * Create new user
     */
    public static function create($data)
    {
        $db = Flight::db();
        
        // Check if email already exists
        if (self::findByEmail($data['email'])) {
            throw new Exception('Email already exists');
        }
        
        // Check if username already exists
        if (isset($data['username']) && self::findByUsername($data['username'])) {
            throw new Exception('Username already exists');
        }
        
        // Hash password if provided
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        }
        
        $fields = [
            'username', 'email', 'password', 'first_name', 'last_name', 
            'phone', 'language', 'timezone', 'is_active', 'avatar'
        ];
        
        $values = [];
        $placeholders = [];
        
        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $values[] = $data[$field];
                $placeholders[] = '?';
            }
        }
        
        $fieldList = implode(', ', array_keys(array_intersect_key($data, array_flip($fields))));
        $placeholderList = implode(', ', $placeholders);
        
        $query = "INSERT INTO users ($fieldList) VALUES ($placeholderList)";
        $stmt = $db->prepare($query);
        $stmt->execute($values);
        
        return $db->lastInsertId();
    }
    
    /**
     * Update user
     */
    public static function update($id, $data)
    {
        $db = Flight::db();
        
        // Check if email already exists for another user
        if (isset($data['email'])) {
            $stmt = $db->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
            $stmt->execute([$data['email'], $id]);
            if ($stmt->fetch()) {
                throw new Exception('Email already exists');
            }
        }
        
        // Check if username already exists for another user
        if (isset($data['username'])) {
            $stmt = $db->prepare("SELECT id FROM users WHERE username = ? AND id != ?");
            $stmt->execute([$data['username'], $id]);
            if ($stmt->fetch()) {
                throw new Exception('Username already exists');
            }
        }
        
        // Hash password if provided
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
            $data['password_changed_at'] = date('Y-m-d H:i:s');
        } else {
            unset($data['password']); // Don't update if empty
        }
        
        $fields = [
            'username', 'email', 'password', 'first_name', 'last_name', 
            'phone', 'language', 'timezone', 'is_active', 'avatar',
            'password_changed_at'
        ];
        
        $sets = [];
        $values = [];
        
        foreach ($fields as $field) {
            if (isset($data[$field])) {
                $sets[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($sets)) {
            return true; // Nothing to update
        }
        
        $values[] = $id; // Add ID for WHERE clause
        
        $query = "UPDATE users SET " . implode(', ', $sets) . " WHERE id = ?";
        $stmt = $db->prepare($query);
        return $stmt->execute($values);
    }
    
    /**
     * Delete user
     */
    public static function delete($id)
    {
        $db = Flight::db();
        
        // Don't delete the last admin
        $stmt = $db->prepare("
            SELECT COUNT(*) as admin_count
            FROM users u
            JOIN user_group_members ugm ON u.id = ugm.user_id
            JOIN user_groups g ON ugm.group_id = g.id
            WHERE g.name = 'Administrators' AND u.is_active = 1
        ");
        $stmt->execute();
        $adminCount = $stmt->fetch(PDO::FETCH_ASSOC)['admin_count'];
        
        // Check if this user is an admin
        $stmt = $db->prepare("
            SELECT COUNT(*) as is_admin
            FROM user_group_members ugm
            JOIN user_groups g ON ugm.group_id = g.id
            WHERE ugm.user_id = ? AND g.name = 'Administrators'
        ");
        $stmt->execute([$id]);
        $isAdmin = $stmt->fetch(PDO::FETCH_ASSOC)['is_admin'] > 0;
        
        if ($isAdmin && $adminCount <= 1) {
            throw new Exception('Cannot delete the last administrator');
        }
        
        $stmt = $db->prepare("DELETE FROM users WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Sync user groups
     */
    public static function syncGroups($userId, $groupIds)
    {
        $db = Flight::db();
        
        // Remove existing groups
        $stmt = $db->prepare("DELETE FROM user_group_members WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // Add new groups
        if (!empty($groupIds)) {
            $stmt = $db->prepare("INSERT INTO user_group_members (user_id, group_id, added_by) VALUES (?, ?, ?)");
            $addedBy = $_SESSION['user_id'] ?? null;
            
            foreach ($groupIds as $groupId) {
                $stmt->execute([$userId, $groupId, $addedBy]);
            }
        }
        
        return true;
    }
    
    /**
     * Get user permissions through groups
     */
    public static function getPermissions($userId)
    {
        $db = Flight::db();
        
        $query = "
            SELECT DISTINCT p.*
            FROM permissions p
            JOIN group_permissions gp ON p.id = gp.permission_id
            JOIN user_group_members ugm ON gp.group_id = ugm.group_id
            WHERE ugm.user_id = ?
            ORDER BY p.category, p.sort_order
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if user has specific permission
     */
    public static function hasPermission($userId, $permissionCode)
    {
        $db = Flight::db();
        
        $query = "
            SELECT COUNT(*) as has_permission
            FROM permissions p
            JOIN group_permissions gp ON p.id = gp.permission_id
            JOIN user_group_members ugm ON gp.group_id = ugm.group_id
            WHERE ugm.user_id = ? AND p.code = ?
            LIMIT 1
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$userId, $permissionCode]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['has_permission'] > 0;
    }
    
    /**
     * Update last login
     */
    public static function updateLastLogin($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Toggle user active status
     */
    public static function toggleActive($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("UPDATE users SET is_active = NOT is_active WHERE id = ?");
        return $stmt->execute([$id]);
    }
}