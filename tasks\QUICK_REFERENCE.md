# Fit360 AdminDesk - Quick Reference Guide

## Common Tasks

### Invoice Management

#### Bulk Loyer (Rental) Invoice Generation
1. Go to **Invoices → Bulk Loyer Generation**
2. Select month and year
3. Optionally filter by user type (medical/support staff)
4. Review list of users with financial obligations
5. Select users to invoice (or use Select All)
6. Click "Generate Selected"
7. Monitor progress in modal
8. Review results (success/failed counts)

#### Create a Draft Invoice
1. Go to **Invoices → Create Invoice**
2. Select Document Type (usually "Facture")
3. Select Client
4. Set Issue Date and Due Date
5. Click "Add Item" to add line items
6. Enter description, quantity, and price
7. Click "Save as Draft"

#### Send an Invoice
1. Open the invoice (click on invoice number)
2. Click "Mark as Sent" or use the dropdown menu
3. Invoice status changes to "Sent"
4. Invoice can no longer be edited

#### Generate PDF
1. Open any invoice (draft or sent)
2. Click "Download PDF" button
3. Professional PDF opens in new tab
4. Use browser's print function if needed

#### Record a Payment
1. Open the sent invoice
2. Click "Record Payment" 
3. Enter payment amount and date
4. Select payment method
5. Click "Save Payment"

#### Delete a Draft
1. Only draft invoices can be deleted
2. Use the dropdown menu (3 dots) → Delete
3. Or open the draft and click the red Delete button

### Client Management

#### Add a New Client
1. Go to **Clients → Add Client**
2. Choose type: Individual or Company
3. Fill in required information
4. Save

### Color Scheme Management

#### Change System Colors
1. Go to **Configuration → Color Schemes**
2. Select existing scheme or create new
3. Customize primary, secondary, accent colors
4. Save and apply to system

#### Create Custom Color Scheme
1. Configuration → Color Schemes → Add New
2. Enter scheme name and description
3. Set all color values (hex codes)
4. Save and set as active

### Common Issues & Solutions

#### Draft Invoices Not Showing
1. Clear browser cache
2. Click "Reset Filters" in invoice list
3. Check URL doesn't have `?status=sent`

#### Can't Create Invoice
1. Make sure to select:
   - Document Type
   - Client
   - Issue Date
   - Due Date
2. Add at least one line item

#### Payment Recording Error
1. Invoice must be "Sent" first
2. Cannot record payment on drafts
3. Payment amount cannot exceed invoice total

### Mobile Development Reference

#### Mobile CSS Classes
```css
.mobile-only         /* Show only on mobile */
.desktop-only        /* Hide on mobile */
.hide-mobile         /* Hide specific elements */
.mobile-full-width   /* Full width on mobile */
.mobile-text-center  /* Center text on mobile */
.mobile-mt-3         /* Mobile top margin */
.mobile-mb-3         /* Mobile bottom margin */
.mobile-p-2          /* Mobile padding */
```

#### Mobile Data Attributes
```html
<!-- Floating Action Button -->
<a data-mobile-fab="true" 
   data-mobile-fab-icon="<i class='fas fa-plus'></i>"
   data-mobile-fab-label="Create Invoice">

<!-- Table Mobile Features -->
<table class="table mobile-cards">
<td data-label="Invoice Number">
```

#### Mobile JavaScript API
```javascript
// Initialize mobile features
MobileEnhancements.init();

// Individual features
MobileEnhancements.initTableResponsive();
MobileEnhancements.initFormEnhancements();
MobileEnhancements.initSwipeGestures();
MobileEnhancements.initPullToRefresh();
```

#### Mobile Gestures
- **Swipe Right**: Open sidebar (from left edge)
- **Swipe Left**: Close sidebar
- **Pull Down**: Refresh page (from top)
- **Tap Outside**: Close menus/modals

### Keyboard Shortcuts
- `Ctrl + S` - Save form (where applicable)
- `Esc` - Close modals
- `Enter` - Submit search

### Invoice Status Flow
```
Draft → Sent → Paid
         ↓
      Partial → Paid
```

### Important URLs
- Invoice List: `/invoices`
- Create Invoice: `/invoices/create`
- Client List: `/clients`
- Dashboard: `/dashboard`
- Settings: `/settings`

### Database Info
- Invoice numbers: FAC-YYYY-NNNN
- Default VAT: 17% (Luxembourg)
- Default Currency: EUR
- Default Payment Terms: 30 days

### Context7 Usage
To get real-time documentation in prompts, append `use context7`:
```
"How to implement authentication in Flight PHP? use context7"
"Create a Bootstrap 5.3 modal use context7"
"PHP 8.3 type hints best practices use context7"
```

### Mobile Development Tips
1. **Touch Targets**: Ensure all buttons/links are at least 44px
2. **Tables**: Add `mobile-cards` class for card view on mobile
3. **Forms**: Use `form-floating` for better mobile labels
4. **Navigation**: Mobile menu opens with hamburger button
5. **Testing**: Use Chrome DevTools device emulation

### Troubleshooting
1. **Page not loading**: Check .htaccess and Apache mod_rewrite
2. **500 Error**: Check PHP error logs
3. **Database Error**: Verify .env credentials
4. **Missing Translations**: Clear cache at `/translations`
5. **Mobile Issues**: Check mobile-enhancements.js is loaded
6. **Gestures Not Working**: Verify touch events aren't blocked