<?php

return [
    // General
    'title' => 'Retrocession',
    'retrocession' => 'Retrocession',
    'rate_profiles' => 'Rate Profiles',
    'data_entry' => 'Data Entry',
    'monthly_data' => 'Monthly Data',
    'calculation_preview' => 'Calculation Preview',
    
    // Exclude patient line feature
    'exclude_patient_line' => 'Exclude patient line',
    'exclude_patient_line_help' => 'If checked, the invoice will not contain the "Patient Part" line',
    'exclude_patient_line_tooltip' => 'Patient line excluded',
    
    // Profile Management
    'create_profile' => 'Create Profile',
    'edit_profile' => 'Edit Profile',
    'create_rate_profile' => 'Create Rate Profile',
    'edit_rate_profile' => 'Edit Rate Profile',
    'profile' => 'Profile',
    'profile_information' => 'Profile Information',
    'profile_statistics' => 'Profile Statistics',
    'profile_types' => 'Profile Types',
    'profile_type' => 'Profile Type',
    'assign_profile' => 'Assign Profile',
    'assign_rate_profile' => 'Assign Rate Profile',
    'assign_profile_to' => 'Assign',
    'profile_assigned' => 'Profile assigned successfully',
    'no_rate_profiles' => 'No rate profiles found',
    'code_hint' => 'Uppercase letters and underscores only',
    'set_as_default_profile' => 'Set as default profile',
    
    // Profile Types
    'type_retrocession' => 'Retrocession',
    'type_hourly' => 'Hourly',
    'type_rental' => 'Rental',
    'type_mixed' => 'Mixed',
    'type_retrocession_desc' => 'Calculation based on CNS and patient percentages',
    'type_hourly_desc' => 'Billing based on hourly rate',
    'type_rental_desc' => 'Office rental with fixed charges',
    'type_mixed_desc' => 'Combination of multiple calculation modes',
    
    // Rates
    'rates' => 'Rates',
    'rate_configuration' => 'Rate Configuration',
    'current_rates' => 'Current Rates',
    'retrocession_rates' => 'Retrocession Rates',
    'cns' => 'CNS',
    'patient' => 'Patient',
    'secretariat' => 'Secretariat',
    'cns_rate' => 'CNS Rate',
    'patient_rate' => 'Patient Rate',
    'secretariat_rate' => 'Secretariat Rate',
    'hourly' => 'Hourly',
    'hourly_rate' => 'Hourly Rate',
    'hourly_rate_amount' => 'Hourly Amount',
    'rental_configuration' => 'Rental Configuration',
    'rent_amount' => 'Rent Amount',
    'charges_amount' => 'Charges Amount',
    'rates_valid_from' => 'Rates valid from',
    
    // Practitioners
    'practitioner' => 'Practitioner',
    'practitioners' => 'Practitioners',
    'select_practitioner' => 'Select practitioner',
    'active_practitioners' => 'Active Practitioners',
    'last_updated' => 'Last Updated',
    
    // Data Entry
    'cns_amount' => 'CNS Amount',
    'patient_amount' => 'Patient Amount',
    'total_amount' => 'Total Amount',
    'enter_amounts_to_preview' => 'Enter amounts to preview calculation',
    'data_already_confirmed' => 'Data has already been confirmed for this period',
    'suggestion_available' => 'An automatic suggestion is available',
    'apply_suggestion' => 'Apply Suggestion',
    'save_and_confirm' => 'Save and Confirm',
    'cns_imports' => 'CNS Imports',
    'historical_data' => 'Historical Data',
    
    // Retrocession Calculation
    'cns_retrocession' => 'CNS Retrocession',
    'patient_retrocession' => 'Patient Retrocession',
    'secretariat_fee' => 'Secretariat Fee',
    'total_retrocession' => 'Total Retrocession',
    
    // Validity
    'valid_from' => 'Valid From',
    'valid_to' => 'Valid To',
    
    // Messages
    'profile_created' => 'Profile created successfully',
    'profile_updated' => 'Profile updated successfully',
    'profile_deleted' => 'Profile deleted successfully',
    'data_saved' => 'Data saved successfully',
    'data_confirmed' => 'Data confirmed successfully',
    
    // Bulk Operations
    'bulk_generate' => 'Bulk Generate',
    'bulk_generate_invoices' => 'Bulk Generate Invoices',
    'bulk_generate_info' => 'This action will generate invoices for all selected practitioners with confirmed data.',
    'generate_invoices' => 'Generate Invoices',
    'generate_invoice_confirm' => 'Are you sure you want to generate the invoice for this practitioner?',
    'select_practitioners' => 'Select Practitioners',
    
    // Statistics
    'current_month_total' => 'Current Month Total',
    'pending_entries' => 'Pending Entries',
    'practitioner_summary' => 'Practitioner Summary',
    'services_count' => 'Services Count',
    'gross_amount' => 'Gross Amount',
    'retrocession_amount' => 'Retrocession Amount',
    'no_data_found' => 'No data found',
    
    // Period
    'period' => 'Period',
    
    // Status
    'pending' => 'Pending',
    'confirmed' => 'Confirmed',
    'invoiced' => 'Invoiced',
];