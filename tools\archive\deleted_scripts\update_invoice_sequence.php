<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== UPDATING INVOICE SEQUENCE ===\n\n";
    
    // Get invoice document type
    $stmt = $pdo->prepare("SELECT id, code, counter_type FROM document_types WHERE code = 'invoice'");
    $stmt->execute();
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$docType) {
        echo "ERROR: Invoice document type not found!\n";
        exit(1);
    }
    
    echo "Found invoice document type: ID={$docType['id']}, Counter Type={$docType['counter_type']}\n\n";
    
    // Update the yearly sequence to 185 (so next will be 186)
    $stmt = $pdo->prepare("
        UPDATE document_sequences 
        SET last_number = 185, updated_at = NOW() 
        WHERE document_type_id = ? 
        AND year = 2025 
        AND month IS NULL
    ");
    $stmt->execute([$docType['id']]);
    
    echo "Updated yearly sequence for 2025 to last_number = 185\n";
    echo "Next invoice will be: FAC-2025-00186\n\n";
    
    // Show current sequences
    echo "Current sequences:\n";
    $stmt = $pdo->prepare("SELECT * FROM document_sequences WHERE document_type_id = ? ORDER BY year DESC, month DESC");
    $stmt->execute([$docType['id']]);
    $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo "- Year: {$seq['year']}, Month: " . ($seq['month'] ?? 'N/A') . ", Last Number: {$seq['last_number']}\n";
    }
    
    echo "\nDone!";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}