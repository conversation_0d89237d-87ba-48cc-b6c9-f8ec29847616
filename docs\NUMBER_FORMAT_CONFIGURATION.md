# Number Format Configuration

This document explains how to configure the automatic number generation formats for Clients, Patients, and Invoices in the FIT360 system.

## Overview

The system now uses configurable formats stored in the `config_settings` table for generating:
- Client numbers (e.g., C-2024-0001)
- Patient numbers (e.g., P-2024-0001)
- Invoice numbers (e.g., INV-2024-00001)

## Configuration Options

Each number format configuration supports the following options:

| Option | Description | Example |
|--------|-------------|---------|
| `prefix` | The prefix for the number | 'C', 'P', 'INV' |
| `year_format` | PHP date format for year part | 'Y' (2024), 'y' (24), 'Y-m' (2024-12) |
| `separator` | Character to separate parts | '-', '/', '.' |
| `sequence_digits` | Number of digits for the sequence | 4 (0001), 6 (000001) |
| `reset_yearly` | Whether to reset sequence each year | true/false |

## Default Configurations

### Client Numbers
```json
{
    "prefix": "C",
    "year_format": "Y",
    "separator": "-",
    "sequence_digits": 4,
    "reset_yearly": true
}
```
Output: `C-2024-0001`

### Patient Numbers
```json
{
    "prefix": "P",
    "year_format": "Y",
    "separator": "-",
    "sequence_digits": 4,
    "reset_yearly": true
}
```
Output: `P-2024-0001`

### Invoice Numbers
```json
{
    "prefix": "INV",
    "year_format": "Y",
    "separator": "-",
    "sequence_digits": 5,
    "reset_yearly": true
}
```
Output: `INV-2024-00001`

Note: For invoices, if an invoice type is specified, the type's code will override the prefix setting.

## Examples of Custom Formats

### Sequential without year
```json
{
    "prefix": "CLI",
    "year_format": "",
    "separator": "/",
    "sequence_digits": 6,
    "reset_yearly": false
}
```
Output: `CLI/000001`, `CLI/000002`, etc.

### Monthly reset
```json
{
    "prefix": "CM",
    "year_format": "Y-m",
    "separator": "-",
    "sequence_digits": 3,
    "reset_yearly": true
}
```
Output: `CM-2024-12-001`, `CM-2025-01-001`, etc.

### Short year format
```json
{
    "prefix": "PAT",
    "year_format": "y",
    "separator": ".",
    "sequence_digits": 5,
    "reset_yearly": true
}
```
Output: `PAT.24.00001`

## Updating Configuration

To update a number format configuration, you can:

1. **Via Database**: Update the `config_settings` table directly
```sql
UPDATE config_settings 
SET value = '{"prefix": "CLIENT", "year_format": "Y", "separator": "-", "sequence_digits": 5, "reset_yearly": true}'
WHERE `key` = 'client_number_format';
```

2. **Via Code**: Use the ConfigSettings model
```php
use App\Models\ConfigSettings;

ConfigSettings::setValue('client_number_format', [
    'prefix' => 'CLIENT',
    'year_format' => 'Y',
    'separator' => '-',
    'sequence_digits' => 5,
    'reset_yearly' => true
], 'client', 'json');
```

## Implementation Details

The number generation methods (`generateClientNumber()`, `generatePatientNumber()`, and `generateInvoiceNumber()`) now:

1. Read the format configuration from the database
2. Build the number pattern based on the configuration
3. Find the last used number matching the pattern
4. Increment the sequence
5. Format the new number according to the configuration

The system handles both yearly and non-yearly resets, and supports various date formats for flexible numbering schemes.

## Migration

To apply the number format configurations to your database, run:
```bash
mysql -u your_username -p your_database < database/migrations/010_add_number_format_configs.sql
```

This will add the default configurations to your `config_settings` table.