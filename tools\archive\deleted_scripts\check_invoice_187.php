<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING INVOICE FAC-2025-0187 ===\n\n";

try {
    $db = Flight::db();
    
    // Get invoice by number
    $stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
    $stmt->execute(['FAC-2025-0187']);
    $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "Invoice found:\n";
        echo "- ID: {$invoice['id']}\n";
        echo "- Number: {$invoice['invoice_number']}\n";
        echo "- User ID: " . ($invoice['user_id'] ?? 'NULL') . "\n";
        echo "- Client ID: " . ($invoice['client_id'] ?? 'NULL') . "\n";
        echo "- Period: " . ($invoice['period'] ?? 'NULL') . "\n";
        echo "- Subject: " . ($invoice['subject'] ?? 'NULL') . "\n";
        echo "- Subtotal: {$invoice['subtotal']}\n";
        echo "- VAT Amount: {$invoice['vat_amount']}\n";
        echo "- Total: {$invoice['total']}\n\n";
        
        // Get invoice lines
        echo "Invoice lines:\n";
        $stmt = $db->prepare("
            SELECT * FROM invoice_lines 
            WHERE invoice_id = ? 
            ORDER BY sort_order ASC, id ASC
        ");
        $stmt->execute([$invoice['id']]);
        $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $calculatedSubtotal = 0;
        $calculatedVat = 0;
        
        foreach ($lines as $i => $line) {
            $lineTotal = $line['quantity'] * $line['unit_price'];
            $calculatedSubtotal += $lineTotal;
            $lineVat = $lineTotal * ($line['vat_rate'] / 100);
            $calculatedVat += $lineVat;
            
            echo sprintf("%d. %s\n", $i + 1, $line['description']);
            echo sprintf("   Qty: %.2f x %.2f€ = %.2f€ (VAT: %.2f%%)\n",
                $line['quantity'],
                $line['unit_price'],
                $lineTotal,
                $line['vat_rate']
            );
            
            // Check if 'total' column exists in line
            if (isset($line['total'])) {
                echo sprintf("   Stored total: %.2f€\n", $line['total']);
            }
        }
        
        echo "\nCalculated totals:\n";
        echo sprintf("- Subtotal: %.2f€ (stored: %.2f€)\n", $calculatedSubtotal, $invoice['subtotal']);
        echo sprintf("- VAT: %.2f€ (stored: %.2f€)\n", $calculatedVat, $invoice['vat_amount']);
        echo sprintf("- Total: %.2f€ (stored: %.2f€)\n", $calculatedSubtotal + $calculatedVat, $invoice['total']);
    } else {
        echo "Invoice FAC-2025-0187 not found!\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}