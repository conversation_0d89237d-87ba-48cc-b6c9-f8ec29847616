<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\SalesInvoice;
use App\Models\SalesInvoiceLine;
use App\Models\SalesPayment;
use App\Models\CatalogItem;
use App\Models\Client;
use App\Models\Voucher;
use App\Models\VoucherRedemption;
use App\Services\PdfService;
use Flight;
use Exception;

class SalesInvoiceController extends Controller
{
    protected $salesInvoice;
    protected $catalogItem;
    protected $pdfService;
    
    public function __construct()
    {
        // Don't call parent constructor as it doesn't exist
        $this->salesInvoice = new SalesInvoice();
        $this->catalogItem = new CatalogItem();
        $this->pdfService = new PdfService();
    }
    
    /**
     * List sales invoices
     */
    public function index()
    {
        $page = Flight::request()->query->page ?? 1;
        $perPage = Flight::request()->query->per_page ?? 20;
        $search = Flight::request()->query->search ?? '';
        $status = Flight::request()->query->status ?? '';
        $dateFrom = Flight::request()->query->date_from ?? '';
        $dateTo = Flight::request()->query->date_to ?? '';
        
        $query = $this->salesInvoice->with(['client', 'lines']);
        
        // Apply filters
        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('invoice_number', 'LIKE', "%{$search}%")
                  ->orWhereHas('client', function($q2) use ($search) {
                      $q2->where('name', 'LIKE', "%{$search}%");
                  });
            });
        }
        
        if ($status) {
            $query->where('payment_status', $status);
        }
        
        if ($dateFrom) {
            $query->where('issue_date', '>=', $dateFrom);
        }
        
        if ($dateTo) {
            $query->where('issue_date', '<=', $dateTo);
        }
        
        $invoices = $query->orderBy('issue_date', 'desc')
                          ->orderBy('id', 'desc')
                          ->paginate($perPage, ['*'], 'page', $page);
        
        $this->render('sales/invoices/index', [
            'invoices' => $invoices,
            'pageTitle' => __('sales.invoices'),
            'filters' => [
                'search' => $search,
                'status' => $status,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ]
        ]);
    }
    
    /**
     * Show create form
     */
    public function create()
    {
        $clients = Client::where('is_active', true)->orderBy('name')->get();
        $products = $this->catalogItem->where('is_active', true)->orderBy('name')->get();
        
        $this->render('sales/invoices/create', [
            'clients' => $clients,
            'products' => $products,
            'pageTitle' => __('sales.create_invoice')
        ]);
    }
    
    /**
     * Store new sales invoice
     */
    public function store()
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Validation
            $rules = [
                'client_id' => 'required|integer',
                'issue_date' => 'required|date',
                'lines' => 'required|array|min:1'
            ];
            
            $validation = $this->validate($data, $rules);
            if (!$validation['valid']) {
                $this->jsonResponse(['success' => false, 'errors' => $validation['errors']], 422);
                return;
            }
            
            // Calculate totals
            $subtotal = 0;
            $totalVat = 0;
            
            foreach ($data['lines'] as $line) {
                $lineTotal = $line['quantity'] * $line['unit_price'];
                $discount = ($lineTotal * $line['discount_percent']) / 100;
                $lineTotal -= $discount;
                
                $vatAmount = ($lineTotal * $line['vat_rate']) / 100;
                
                $subtotal += $lineTotal;
                $totalVat += $vatAmount;
            }
            
            $totalAmount = $subtotal + $totalVat;
            
            // Create invoice
            $invoice = $this->salesInvoice->create([
                'invoice_type' => $data['invoice_type'] ?? 'invoice',
                'client_id' => $data['client_id'],
                'client_type' => $data['client_type'] ?? 'patient',
                'issue_date' => $data['issue_date'],
                'subtotal' => $subtotal,
                'discount_amount' => $data['discount_amount'] ?? 0,
                'total_vat' => $totalVat,
                'total_amount' => $totalAmount,
                'payment_status' => 'unpaid',
                'amount_paid' => 0,
                'notes' => $data['notes'] ?? null,
                'internal_notes' => $data['internal_notes'] ?? null,
                'therapist_id' => $data['therapist_id'] ?? null,
                'created_by' => $_SESSION['user_id'] ?? 1
            ]);
            
            // Generate invoice number
            $invoice->invoice_number = $invoice->generateNumber();
            $invoice->save();
            
            // Create invoice lines
            foreach ($data['lines'] as $line) {
                SalesInvoiceLine::create([
                    'invoice_id' => $invoice->id,
                    'line_type' => $line['line_type'] ?? 'product',
                    'item_id' => $line['item_id'],
                    'description' => $line['description'],
                    'quantity' => $line['quantity'],
                    'unit_price' => $line['unit_price'],
                    'discount_percent' => $line['discount_percent'] ?? 0,
                    'vat_rate' => $line['vat_rate'],
                    'line_total' => $line['quantity'] * $line['unit_price'] * (1 - ($line['discount_percent'] ?? 0) / 100),
                    'therapist_id' => $line['therapist_id'] ?? null
                ]);
            }
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('sales.invoice_created'),
                'redirect' => '/sales/invoices/' . $invoice->id
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Show invoice details
     */
    public function show($id)
    {
        $invoice = $this->salesInvoice->with(['client', 'lines.item', 'payments'])->findOrFail($id);
        
        $this->render('sales/invoices/show', [
            'invoice' => $invoice,
            'pageTitle' => $invoice->invoice_number
        ]);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $invoice = $this->salesInvoice->with(['lines'])->findOrFail($id);
        
        if ($invoice->payment_status !== 'unpaid') {
            Flight::redirect('/sales/invoices/' . $id);
            return;
        }
        
        $clients = Client::where('is_active', true)->orderBy('name')->get();
        $products = $this->catalogItem->where('is_active', true)->orderBy('name')->get();
        
        $this->render('sales/invoices/edit', [
            'invoice' => $invoice,
            'clients' => $clients,
            'products' => $products,
            'pageTitle' => __('sales.edit_invoice')
        ]);
    }
    
    /**
     * Update invoice
     */
    public function update($id)
    {
        try {
            $invoice = $this->salesInvoice->findOrFail($id);
            
            if ($invoice->payment_status !== 'unpaid') {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.cannot_edit_paid_invoice')
                ], 400);
                return;
            }
            
            $data = Flight::request()->data->getData();
            
            // Validation
            $rules = [
                'client_id' => 'required|integer',
                'issue_date' => 'required|date',
                'lines' => 'required|array|min:1'
            ];
            
            $validation = $this->validate($data, $rules);
            if (!$validation['valid']) {
                $this->jsonResponse(['success' => false, 'errors' => $validation['errors']], 422);
                return;
            }
            
            // Calculate totals
            $subtotal = 0;
            $totalVat = 0;
            
            foreach ($data['lines'] as $line) {
                $lineTotal = $line['quantity'] * $line['unit_price'];
                $discount = ($lineTotal * $line['discount_percent']) / 100;
                $lineTotal -= $discount;
                
                $vatAmount = ($lineTotal * $line['vat_rate']) / 100;
                
                $subtotal += $lineTotal;
                $totalVat += $vatAmount;
            }
            
            $totalAmount = $subtotal + $totalVat;
            
            // Update invoice
            $invoice->update([
                'client_id' => $data['client_id'],
                'issue_date' => $data['issue_date'],
                'subtotal' => $subtotal,
                'discount_amount' => $data['discount_amount'] ?? 0,
                'total_vat' => $totalVat,
                'total_amount' => $totalAmount,
                'notes' => $data['notes'] ?? null,
                'internal_notes' => $data['internal_notes'] ?? null,
                'therapist_id' => $data['therapist_id'] ?? null
            ]);
            
            // Delete existing lines and recreate
            SalesInvoiceLine::where('invoice_id', $invoice->id)->delete();
            
            foreach ($data['lines'] as $line) {
                SalesInvoiceLine::create([
                    'invoice_id' => $invoice->id,
                    'line_type' => $line['line_type'] ?? 'product',
                    'item_id' => $line['item_id'],
                    'description' => $line['description'],
                    'quantity' => $line['quantity'],
                    'unit_price' => $line['unit_price'],
                    'discount_percent' => $line['discount_percent'] ?? 0,
                    'vat_rate' => $line['vat_rate'],
                    'line_total' => $line['quantity'] * $line['unit_price'] * (1 - ($line['discount_percent'] ?? 0) / 100),
                    'therapist_id' => $line['therapist_id'] ?? null
                ]);
            }
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('sales.invoice_updated'),
                'redirect' => '/sales/invoices/' . $invoice->id
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Delete invoice
     */
    public function destroy($id)
    {
        try {
            $invoice = $this->salesInvoice->findOrFail($id);
            
            if ($invoice->payment_status !== 'unpaid') {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.cannot_delete_paid_invoice')
                ], 400);
                return;
            }
            
            // Delete lines first
            SalesInvoiceLine::where('invoice_id', $invoice->id)->delete();
            
            // Delete invoice
            $invoice->delete();
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('sales.invoice_deleted'),
                'redirect' => '/sales/invoices'
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Generate PDF
     */
    public function pdf($id)
    {
        $invoice = $this->salesInvoice->with(['client', 'lines.item', 'payments'])->findOrFail($id);
        
        $html = Flight::view()->fetch('sales/invoices/pdf', [
            'invoice' => $invoice
        ]);
        
        $pdf = $this->pdfService->generateFromHtml($html, [
            'title' => $invoice->invoice_number,
            'orientation' => 'portrait'
        ]);
        
        $this->pdfService->outputPdf($pdf, $invoice->invoice_number . '.pdf', 'I');
    }
    
    /**
     * Download PDF
     */
    public function download($id)
    {
        $invoice = $this->salesInvoice->with(['client', 'lines.item', 'payments'])->findOrFail($id);
        
        $html = Flight::view()->fetch('sales/invoices/pdf', [
            'invoice' => $invoice
        ]);
        
        $pdf = $this->pdfService->generateFromHtml($html, [
            'title' => $invoice->invoice_number,
            'orientation' => 'portrait'
        ]);
        
        $this->pdfService->outputPdf($pdf, $invoice->invoice_number . '.pdf', 'D');
    }
    
    /**
     * Record payment
     */
    public function recordPayment($id)
    {
        try {
            $invoice = $this->salesInvoice->findOrFail($id);
            $data = Flight::request()->data->getData();
            
            // Validation
            $rules = [
                'payment_method' => 'required|in:cash,card,transfer,voucher,insurance,other',
                'amount' => 'required|numeric|min:0.01'
            ];
            
            $validation = $this->validate($data, $rules);
            if (!$validation['valid']) {
                $this->jsonResponse(['success' => false, 'errors' => $validation['errors']], 422);
                return;
            }
            
            // Check if amount exceeds balance
            $balance = $invoice->total_amount - $invoice->amount_paid;
            if ($data['amount'] > $balance) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.payment_exceeds_balance')
                ], 400);
                return;
            }
            
            // Handle voucher payment
            if ($data['payment_method'] === 'voucher' && !empty($data['voucher_code'])) {
                $redemption = VoucherRedemption::redeem(
                    $data['voucher_code'],
                    $data['amount'],
                    $invoice->id,
                    $_SESSION['user_id'] ?? 1
                );
                
                $data['reference'] = 'Voucher: ' . $data['voucher_code'];
                $data['voucher_id'] = $redemption->voucher_id;
            }
            
            // Create payment record
            SalesPayment::create([
                'invoice_id' => $invoice->id,
                'payment_date' => date('Y-m-d H:i:s'),
                'payment_method' => $data['payment_method'],
                'amount' => $data['amount'],
                'reference' => $data['reference'] ?? null,
                'voucher_id' => $data['voucher_id'] ?? null,
                'notes' => $data['notes'] ?? null,
                'recorded_by' => $_SESSION['user_id'] ?? 1
            ]);
            
            // Update invoice
            $invoice->amount_paid += $data['amount'];
            
            if ($invoice->amount_paid >= $invoice->total_amount) {
                $invoice->payment_status = 'paid';
            } else {
                $invoice->payment_status = 'partial';
            }
            
            $invoice->save();
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('sales.payment_recorded'),
                'balance' => $invoice->total_amount - $invoice->amount_paid
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Check voucher
     */
    public function checkVoucher()
    {
        try {
            $voucherCode = Flight::request()->query->code;
            
            if (empty($voucherCode)) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.voucher_code_required')
                ], 400);
                return;
            }
            
            $voucher = Voucher::where('voucher_code', $voucherCode)->first();
            
            if (!$voucher) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.voucher_not_found')
                ], 404);
                return;
            }
            
            // Check validity
            if ($voucher->status === 'expired' || $voucher->expiry_date < date('Y-m-d')) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.voucher_expired')
                ], 400);
                return;
            }
            
            if ($voucher->status === 'used') {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.voucher_already_used')
                ], 400);
                return;
            }
            
            if ($voucher->status === 'cancelled') {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('sales.voucher_cancelled')
                ], 400);
                return;
            }
            
            $this->jsonResponse([
                'success' => true,
                'voucher' => [
                    'code' => $voucher->voucher_code,
                    'type' => $voucher->voucher_type,
                    'remaining_value' => $voucher->remaining_value,
                    'expiry_date' => $voucher->expiry_date,
                    'recipient_name' => $voucher->recipient_name
                ]
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Quick sale (receipt mode)
     */
    public function quickSale()
    {
        $quickSaleItems = $this->catalogItem
            ->where('quick_sale_button', true)
            ->where('is_active', true)
            ->orderBy('button_order')
            ->get();
        
        $this->render('sales/invoices/quick-sale', [
            'quickSaleItems' => $quickSaleItems,
            'pageTitle' => __('sales.quick_sale')
        ]);
    }
}