/**
 * Product Live Search Module
 * Provides live search functionality for invoice item description fields
 */
class ProductLiveSearch {
    constructor() {
        this.searchTimeout = null;
        this.activeDropdown = null;
        this.selectedIndex = -1;
        this.searchCache = new Map();
        this.currentInput = null;
        this.dropdownId = 'product-live-search-dropdown';
        this.isClickingDropdown = false;
        this.vatRates = [];
        
        // Get current language from HTML or default to 'fr'
        const lang = document.documentElement.lang || 'fr';
        
        // Translation strings
        const translations = {
            en: {
                'products.create_new_product': 'Create new product: ":name"',
                'products.create_new_product_hint': 'Click to create this product',
                'products.create_new_product_title': 'Create a new product',
                'products.name': 'Name',
                'products.code': 'Code',
                'products.auto_generate': 'Auto-generate',
                'products.category': 'Category',
                'products.category_divers': 'Miscellaneous (default)',
                'products.unit_price': 'Unit Price',
                'products.vat_rate': 'VAT Rate',
                'products.description': 'Description',
                'common.cancel': 'Cancel',
                'products.save_and_use': 'Save and use',
                'products.name_already_exists': 'A product with this name already exists',
                'common.saving': 'Saving...',
                'products.created_successfully': 'Product ":name" created successfully',
                'common.error_occurred': 'An error occurred'
            },
            fr: {
                'products.create_new_product': 'Créer nouveau produit : ":name"',
                'products.create_new_product_hint': 'Cliquez pour créer ce produit',
                'products.create_new_product_title': 'Créer un nouveau produit',
                'products.name': 'Nom',
                'products.code': 'Code',
                'products.auto_generate': 'Auto-générer',
                'products.category': 'Catégorie',
                'products.category_divers': 'Divers (par défaut)',
                'products.unit_price': 'Prix unitaire',
                'products.vat_rate': 'Taux TVA',
                'products.description': 'Description',
                'common.cancel': 'Annuler',
                'products.save_and_use': 'Enregistrer et utiliser',
                'products.name_already_exists': 'Un produit avec ce nom existe déjà',
                'common.saving': 'Enregistrement...',
                'products.created_successfully': 'Produit ":name" créé avec succès',
                'common.error_occurred': 'Une erreur s\'est produite'
            }
        };
        
        // Set translations based on language
        this.translations = translations[lang] || translations['fr'];
        
        // Check if translations are available globally (from server)
        if (typeof window.translations !== 'undefined') {
            this.translations = { ...this.translations, ...window.translations };
        }
        
        // Initialize on DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }
    
    // Translation helper method
    __(key, params = {}) {
        let text = this.translations[key] || key;
        
        // Replace parameters
        Object.keys(params).forEach(param => {
            text = text.replace(`:${param}`, params[param]);
        });
        
        return text;
    }
    
    // Check if current invoice is DIV type
    isDivInvoice() {
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        if (!invoiceTypeSelect) return false;
        
        const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
        const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        
        return typePrefix === 'DIV';
    }
    
    init() {
        
        // Wait a bit for DOM to be fully ready
        setTimeout(() => {
            // Load VAT rates first
            this.loadVatRates();
            
            // Attach to existing description inputs
            this.attachToExistingInputs();
            
            // Watch for new items added dynamically
            this.observeNewItems();
            
            // Watch for invoice type changes
            this.watchInvoiceTypeChanges();
            
            // Global click handler to close dropdown
            document.addEventListener('click', (e) => {
                if (!e.target.closest('.item-description') && !e.target.closest(`#${this.dropdownId}`)) {
                    this.hideDropdown();
                }
            });
            
            
            // Check current state
            const inputs = document.querySelectorAll('.item-description');
        }, 100);
    }
    
    async loadVatRates() {
        try {
            const baseUrl = window.location.pathname.includes('/fit/public') ? '/fit/public' : '';
            const response = await fetch(`${baseUrl}/api/vat-rates`);
            const data = await response.json();
            
            if (data.success && data.rates) {
                this.vatRates = data.rates;
            } else {
                // Fallback VAT rates
                this.vatRates = [
                    {id: 1, rate: 17, is_default: true},
                    {id: 2, rate: 8, is_default: false},
                    {id: 3, rate: 3, is_default: false},
                    {id: 4, rate: 14, is_default: false},
                    {id: 5, rate: 0, is_default: false}
                ];
            }
        } catch (error) {
            console.error('ProductLiveSearch: Error loading VAT rates:', error);
            // Use fallback rates
            this.vatRates = [
                {id: 1, rate: 17, is_default: true},
                {id: 2, rate: 8, is_default: false},
                {id: 3, rate: 3, is_default: false},
                {id: 4, rate: 14, is_default: false},
                {id: 5, rate: 0, is_default: false}
            ];
        }
    }
    
    watchInvoiceTypeChanges() {
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        if (invoiceTypeSelect) {
            invoiceTypeSelect.addEventListener('change', () => {
                // Invoice type changed
                // Re-attach to inputs based on new invoice type
                this.attachToExistingInputs();
            });
        }
    }
    
    attachToExistingInputs() {
        const inputs = document.querySelectorAll('.item-description');
        
        // Check if DIV invoice
        const isDivInvoice = this.isDivInvoice();
        
        inputs.forEach((input, index) => {
            // Remove any existing live search
            input.dataset.liveSearchAttached = 'false';
            
            if (isDivInvoice) {
                this.attachToInput(input);
            }
        });
    }
    
    observeNewItems() {
        let itemsContainer = document.getElementById('itemsBody');
        if (!itemsContainer) {
            // itemsBody not found, trying alternative containers
            // Try alternative container names
            const alternatives = ['itemsContainer', 'items-container', 'items-body'];
            for (const id of alternatives) {
                const container = document.getElementById(id);
                if (container) {
                    itemsContainer = container;
                    // Found container
                    break;
                }
            }
            if (!itemsContainer) {
                console.warn('ProductLiveSearch: No items container found');
                return;
            }
        }
        
        // Observing container for new items
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        const input = node.querySelector('.item-description');
                        if (input) {
                            this.attachToInput(input);
                        }
                    }
                });
            });
        });
        
        observer.observe(itemsContainer, {
            childList: true,
            subtree: true
        });
    }
    
    attachToInput(input) {
        // Skip if already attached or not a DIV invoice
        if (input.dataset.liveSearchAttached === 'true') return;
        if (!this.isDivInvoice()) return;
        
        input.dataset.liveSearchAttached = 'true';
        
        // Focus event - show dropdown if we have cached results
        input.addEventListener('focus', (e) => {
            this.currentInput = input;
            const query = input.value.trim();
            
            if (query.length >= 2 && this.searchCache.has(query)) {
                this.showDropdown(this.searchCache.get(query), input);
            }
        });
        
        // Input event - trigger search
        input.addEventListener('input', (e) => {
            this.currentInput = input;
            const query = input.value.trim();
            
            // Clear timeout from previous input
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
            
            // Hide dropdown if query is too short
            if (query.length < 2) {
                this.hideDropdown();
                return;
            }
            
            // Debounce search
            this.searchTimeout = setTimeout(() => {
                this.performSearch(query, input);
            }, 300);
        });
        
        // Keyboard navigation
        input.addEventListener('keydown', (e) => {
            this.handleKeyboardNavigation(e);
        });
        
        // Blur event - hide dropdown after delay
        input.addEventListener('blur', (e) => {
            // Use a flag to prevent hiding when clicking on dropdown
            setTimeout(() => {
                // Check debug mode
                if (window.debugProductSearch) {
                    // Debug mode - keeping dropdown visible
                    return;
                }
                
                if (!this.isClickingDropdown && !document.activeElement.closest(`#${this.dropdownId}`)) {
                    this.hideDropdown();
                }
            }, 500); // Increased delay to ensure click registers
        });
    }
    
    async performSearch(query, input) {
        try {
            
            // For testing: always show some results to verify dropdown display
            const testMode = false; // Set to true for testing
            if (testMode) {
                // TEST MODE - Creating test items
                const testItems = [
                    {
                        id: 1,
                        name: 'Test Product ' + query,
                        code: 'TEST001',
                        unit_price: 100,
                        vat_rate: 17,
                        category_is_misc: false
                    }
                ];
                // TEST MODE - Calling showDropdown with test items
                this.showDropdown(testItems, input);
                return;
            }
            
            // Check cache first
            if (this.searchCache.has(query)) {
                // Using cached results
                this.showDropdown(this.searchCache.get(query), input);
                return;
            }
            
            // Perform search
            const baseUrl = window.location.pathname.includes('/fit/public') ? '/fit/public' : '';
            const url = `${baseUrl}/api/products/search?term=${encodeURIComponent(query)}`;
            // Fetching from API
            
            const response = await fetch(url);
            
            if (!response.ok) {
                console.error('ProductLiveSearch: API error', response.status, response.statusText);
                // Show empty results with create option
                if (this.currentInput === input) {
                    // Showing dropdown with empty results due to API error
                    this.showDropdown([], input);
                }
                return;
            }
            
            let data;
            try {
                data = await response.json();
                // API response received
            } catch (jsonError) {
                console.error('ProductLiveSearch: Error parsing JSON:', jsonError);
                // Response details logged
                // Show empty dropdown on JSON error
                this.showDropdown([], input);
                return;
            }
            
            if (data.success && data.items) {
                // Cache results
                this.searchCache.set(query, data.items);
                
                // Show dropdown if this input is still active
                if (this.currentInput === input) {
                    // Showing dropdown with results
                    this.showDropdown(data.items, input);
                } else {
                    // Input changed, not showing dropdown
                }
            } else {
                // Show empty results with create option
                if (this.currentInput === input) {
                    // No items in response, showing empty dropdown
                    this.showDropdown([], input);
                }
            }
        } catch (error) {
            console.error('ProductLiveSearch: Search error:', error);
            // Show empty results with create option on error
            if (this.currentInput === input) {
                this.showDropdown([], input);
            }
        }
    }
    
    showDropdown(items, input) {
        // Remove existing dropdown
        this.hideDropdown();
        
        // Get search query from input
        const searchQuery = input.value.trim();
        
        
        // Create dropdown
        const dropdown = document.createElement('div');
        dropdown.id = this.dropdownId;
        dropdown.className = 'product-search-dropdown';
        
        // Add inline styles to ensure visibility
        dropdown.style.cssText = `
            position: absolute;
            z-index: 99999 !important;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            max-height: 300px;
            overflow-y: auto;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
        `;
        
        // Add mouse event handlers to prevent blur
        dropdown.addEventListener('mousedown', (e) => {
            this.isClickingDropdown = true;
            e.preventDefault(); // Prevent input blur
        });
        
        dropdown.addEventListener('mouseup', () => {
            this.isClickingDropdown = false;
        });
        
        // Position dropdown with better viewport handling
        const rect = input.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        dropdown.style.position = 'absolute';
        dropdown.style.top = `${rect.bottom + scrollTop}px`;
        dropdown.style.left = `${rect.left + scrollLeft}px`;
        dropdown.style.width = `${rect.width}px`;
        
        // Ensure dropdown is visible in viewport
        setTimeout(() => {
            const dropdownRect = dropdown.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;
            
            // Adjust if dropdown goes below viewport
            if (dropdownRect.bottom > viewportHeight) {
                const newTop = rect.top + scrollTop - dropdown.offsetHeight - 5;
                dropdown.style.top = `${newTop}px`;
            }
            
            // Adjust if dropdown goes beyond right edge
            if (dropdownRect.right > viewportWidth) {
                const newLeft = viewportWidth - dropdown.offsetWidth - 10;
                dropdown.style.left = `${newLeft}px`;
            }
        }, 0);
        
        // Add existing product items
        if (items && items.length > 0) {
            items.forEach((item, index) => {
                const itemElement = this.createItemElement(item, index);
                dropdown.appendChild(itemElement);
            });
        }
        
        // Add "Create new product" option if search query exists
        // Show it when: no items found OR no exact match
        if (searchQuery.length >= 2) {
            const hasExactMatch = items && items.length > 0 && items.some(item => 
                item.name.toLowerCase() === searchQuery.toLowerCase()
            );
            
            // Show create option if no products found or no exact match
            if (!items || items.length === 0 || !hasExactMatch) {
                const createOption = this.createNewProductOption(searchQuery);
                dropdown.appendChild(createOption);
            }
        }
        
        // Only show dropdown if there are items or create option
        if (dropdown.children.length > 0) {
            
            // Add to body
            document.body.appendChild(dropdown);
            this.activeDropdown = dropdown;
            this.selectedIndex = -1;
            
            // Debug: Log dropdown position and visibility
            const dropdownRect = dropdown.getBoundingClientRect();
            // Dropdown positioned correctly
            
            // Ensure dropdown is visible by forcing display
            dropdown.style.display = 'block';
            dropdown.style.visibility = 'visible';
        } else {
            // No items to show in dropdown
        }
    }
    
    createItemElement(item, index) {
        const div = document.createElement('div');
        div.className = 'product-search-item';
        div.dataset.index = index;
        
        // Add inline styles for visibility
        div.style.cssText = `
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f0f0f0;
        `;
        
        // Format price
        const price = parseFloat(item.unit_price || 0).toFixed(2);
        const vatRate = item.vat_rate || 0;
        
        div.innerHTML = `
            <div class="product-search-item-name">${this.escapeHtml(item.name)}</div>
            <div class="product-search-item-details">
                <span class="text-muted">${this.escapeHtml(item.code)}</span>
                <span class="float-end">
                    €${price} 
                    <small class="text-muted">(TVA ${vatRate}%)</small>
                </span>
            </div>
        `;
        
        // Click handler
        div.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
            // Item clicked
        });
        
        div.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            // Item click event
            
            // Ensure we have the current input
            if (!this.currentInput) {
                console.error('ProductLiveSearch: No current input set');
                return;
            }
            
            this.selectProduct(item, this.currentInput);
        });
        
        // Hover effect
        div.addEventListener('mouseenter', () => {
            div.style.backgroundColor = '#f0f0f0';
            this.selectedIndex = index;
            this.updateSelection();
        });
        
        div.addEventListener('mouseleave', () => {
            div.style.backgroundColor = '';
        });
        
        return div;
    }
    
    hideDropdown() {
        if (this.activeDropdown) {
            this.activeDropdown.remove();
            this.activeDropdown = null;
            this.selectedIndex = -1;
        }
    }
    
    selectProduct(product, input) {
        // Selecting product
        
        if (!input) {
            console.error('ProductLiveSearch: No input provided');
            return;
        }
        
        // Get the item container - it's a <tr> with class invoice-item
        let itemRow = input.closest('tr.invoice-item');
        if (!itemRow) {
            // Try alternative selector
            const itemRow2 = input.closest('tr');
            if (itemRow2 && itemRow2.classList.contains('invoice-item')) {
                itemRow = itemRow2;
            } else {
                console.error('ProductLiveSearch: Could not find item row. Input:', input);
                console.error('ProductLiveSearch: Parent elements:', input.parentElement, input.parentElement?.parentElement);
                return;
            }
        }
        
        // Extract item index from row position in tbody
        const tbody = itemRow.parentElement;
        const rowIndex = Array.from(tbody.children).indexOf(itemRow);
        const itemIndex = rowIndex; // Use row index as item index
        
        // Found row at index
        
        // No modal needed - add all products directly to invoice
        
        // Populate fields
        // Setting description
        
        // Set value and trigger events to ensure it's registered
        input.value = product.name;
        input.dispatchEvent(new Event('input', { bubbles: true }));
        input.dispatchEvent(new Event('change', { bubbles: true }));
        
        // Update other fields
        const quantityInput = itemRow.querySelector(`input[name="items[${itemIndex}][quantity]"]`);
        const priceInput = itemRow.querySelector(`input[name="items[${itemIndex}][unit_price]"]`);
        const vatSelect = itemRow.querySelector(`select[name="items[${itemIndex}][vat_rate_id]"]`);
        
        // Found all necessary fields
        
        if (priceInput) {
            // Setting price
            priceInput.value = product.unit_price || 0;
            priceInput.dispatchEvent(new Event('input', { bubbles: true }));
            priceInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        if (vatSelect && product.vat_rate !== undefined) {
            // Find the option with matching VAT rate
            let found = false;
            for (let option of vatSelect.options) {
                const optionRate = parseFloat(option.getAttribute('data-rate') || '0');
                if (optionRate === parseFloat(product.vat_rate)) {
                    vatSelect.value = option.value;
                    found = true;
                    break;
                }
            }
            if (!found) {
                console.warn(`ProductLiveSearch: Could not find VAT rate ${product.vat_rate}%`);
            } else {
                vatSelect.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
        
        // Set quantity to 1 if it's 0
        if (quantityInput && (quantityInput.value == 0 || quantityInput.value == '')) {
            quantityInput.value = 1;
        }
        
        // Add hidden product ID field
        let productIdInput = itemRow.querySelector(`input[name="items[${itemIndex}][product_id]"]`);
        if (!productIdInput) {
            productIdInput = document.createElement('input');
            productIdInput.type = 'hidden';
            productIdInput.name = `items[${itemIndex}][product_id]`;
            itemRow.appendChild(productIdInput);
        }
        productIdInput.value = product.id;
        
        // Trigger calculation - try multiple possible function names
        try {
            if (window.calculateItemTotal) {
                // calculateItemTotal expects the row element, not the index
                window.calculateItemTotal(itemRow);
            } else if (window.calculateTotals) {
                window.calculateTotals();
            } else if (window.updateInvoiceTotals) {
                window.updateInvoiceTotals();
            }
        } catch (calcError) {
            console.warn('ProductLiveSearch: Error calculating totals:', calcError);
            // Continue anyway - the product selection should still work
        }
        
        // Trigger input event to notify any listeners
        if (priceInput) {
            priceInput.dispatchEvent(new Event('input', { bubbles: true }));
        }
        
        // Hide dropdown immediately
        this.hideDropdown();
        
        // Set focus after a short delay to ensure values are set
        setTimeout(() => {
            // Re-set the value in case it was cleared
            if (input.value !== product.name) {
                // Re-setting description value
                input.value = product.name;
                input.setAttribute('value', product.name); // Also set attribute
                input.dispatchEvent(new Event('input', { bubbles: true }));
            }
            
            // Double-check other fields too
            if (priceInput && priceInput.value != (product.unit_price || 0)) {
                // Re-setting price value
                priceInput.value = product.unit_price || 0;
                priceInput.setAttribute('value', product.unit_price || 0);
                priceInput.dispatchEvent(new Event('input', { bubbles: true }));
            }
            
            // Focus next field (quantity) if desired
            if (quantityInput) {
                quantityInput.focus();
            }
        }, 50);
        
        // Product selection completed
        
        // Final check after a longer delay
        setTimeout(() => {
            // Final values set correctly
        }, 500);
    }
    
    handleKeyboardNavigation(e) {
        if (!this.activeDropdown) return;
        
        const items = this.activeDropdown.querySelectorAll('.product-search-item');
        
        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                this.selectedIndex = Math.min(this.selectedIndex + 1, items.length - 1);
                this.updateSelection();
                break;
                
            case 'ArrowUp':
                e.preventDefault();
                this.selectedIndex = Math.max(this.selectedIndex - 1, 0);
                this.updateSelection();
                break;
                
            case 'Enter':
                e.preventDefault();
                if (this.selectedIndex >= 0 && items[this.selectedIndex]) {
                    items[this.selectedIndex].click();
                }
                break;
                
            case 'Escape':
                e.preventDefault();
                this.hideDropdown();
                break;
        }
    }
    
    updateSelection() {
        if (!this.activeDropdown) return;
        
        const items = this.activeDropdown.querySelectorAll('.product-search-item');
        items.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('selected');
                // Scroll into view if needed
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    createNewProductOption(searchQuery) {
        const div = document.createElement('div');
        div.className = 'product-search-item product-search-create-new';
        div.dataset.action = 'create-new';
        
        // Add inline styles for visibility
        div.style.cssText = `
            padding: 10px 12px;
            cursor: pointer;
            background-color: #f8f9fa;
            border-top: 2px solid #007bff;
            font-weight: 500;
        `;
        
        div.innerHTML = `
            <div class="product-search-item-name">
                <i class="fas fa-plus-circle text-primary me-2"></i>
                ${this.escapeHtml(this.__('products.create_new_product', { name: searchQuery }))}
            </div>
            <div class="product-search-item-details">
                <span class="text-muted">${this.escapeHtml(this.__('products.create_new_product_hint'))}</span>
            </div>
        `;
        
        // Click handler
        div.addEventListener('mousedown', (e) => {
            e.preventDefault();
            e.stopPropagation();
        });
        
        div.addEventListener('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.showInlineCreationForm(searchQuery, this.currentInput);
        });
        
        // Hover effect
        div.addEventListener('mouseenter', () => {
            div.style.backgroundColor = '#e9ecef';
            // Clear selection from other items
            const items = this.activeDropdown.querySelectorAll('.product-search-item');
            items.forEach(item => item.classList.remove('selected'));
            div.classList.add('selected');
        });
        
        div.addEventListener('mouseleave', () => {
            div.style.backgroundColor = '#f8f9fa';
        });
        
        return div;
    }
    
    showInlineCreationForm(productName, input) {
        if (!input) {
            console.error('ProductLiveSearch: No input provided for inline creation');
            return;
        }
        
        // Hide dropdown
        this.hideDropdown();
        
        // Get the item row
        const itemRow = input.closest('tr.invoice-item');
        if (!itemRow) {
            console.error('ProductLiveSearch: Could not find item row');
            return;
        }
        
        // Store original row HTML for cancellation
        const originalRowHtml = itemRow.innerHTML;
        const rowIndex = Array.from(itemRow.parentElement.children).indexOf(itemRow);
        
        // Replace row content with inline creation form
        const formHtml = this.buildCreationForm(productName, rowIndex);
        itemRow.innerHTML = formHtml;
        itemRow.classList.add('product-creation-row');
        
        // Initialize form handlers
        this.initializeCreationForm(itemRow, originalRowHtml, rowIndex);
        
        // Focus on the first input
        const firstInput = itemRow.querySelector('input[name="new_product_name"]');
        if (firstInput) {
            firstInput.focus();
            firstInput.select();
        }
    }
    
    buildVatOptions() {
        let options = '';
        
        if (this.vatRates.length === 0) {
            // If no rates loaded, use defaults
            return '<option value="1" selected>17%</option>';
        }
        
        this.vatRates.forEach(rate => {
            const selected = rate.is_default ? ' selected' : '';
            options += `<option value="${rate.id}"${selected}>${rate.rate}%</option>`;
        });
        
        return options;
    }
    
    buildCreationForm(productName, rowIndex) {
        return `
            <td colspan="100%" class="p-3">
                <div class="product-creation-form">
                    <div class="card">
                        <div class="card-header bg-primary text-white py-2">
                            <h6 class="mb-0">
                                <i class="fas fa-plus-circle me-2"></i>
                                ${this.escapeHtml(this.__('products.create_new_product_title'))}
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">${this.escapeHtml(this.__('products.name'))} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="new_product_name" value="${this.escapeHtml(productName)}" required>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">${this.escapeHtml(this.__('products.code'))}</label>
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="new_product_code" placeholder="${this.escapeHtml(this.__('products.auto_generate'))}">
                                        <button type="button" class="btn btn-outline-secondary" id="generate-code-btn">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    </div>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">${this.escapeHtml(this.__('products.category'))}</label>
                                    <select class="form-control" name="new_product_category">
                                        <option value="1">Services</option>
                                        <option value="733">${this.escapeHtml(this.__('products.category_divers'))}</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">${this.escapeHtml(this.__('products.unit_price'))} <span class="text-danger">*</span></label>
                                    <div class="input-group">
                                        <span class="input-group-text">€</span>
                                        <input type="number" class="form-control" name="new_product_price" step="0.01" min="0" required>
                                    </div>
                                    <div class="invalid-feedback"></div>
                                </div>
                                <div class="col-md-3">
                                    <label class="form-label">${this.escapeHtml(this.__('products.vat_rate'))}</label>
                                    <select class="form-control" name="new_product_vat">
                                        ${this.buildVatOptions()}
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">${this.escapeHtml(this.__('products.description'))}</label>
                                    <textarea class="form-control" name="new_product_description" rows="2"></textarea>
                                </div>
                            </div>
                            <div class="mt-3 d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-secondary btn-cancel-creation">
                                    <i class="fas fa-times me-1"></i>
                                    ${this.escapeHtml(this.__('common.cancel'))}
                                </button>
                                <button type="button" class="btn btn-primary btn-save-product">
                                    <i class="fas fa-save me-1"></i>
                                    ${this.escapeHtml(this.__('products.save_and_use'))}
                                </button>
                            </div>
                            <div class="alert alert-danger mt-3 d-none" id="creation-error"></div>
                        </div>
                    </div>
                </div>
            </td>
        `;
    }
    
    initializeCreationForm(itemRow, originalRowHtml, rowIndex) {
        // Cancel button handler
        const cancelBtn = itemRow.querySelector('.btn-cancel-creation');
        if (cancelBtn) {
            cancelBtn.addEventListener('click', () => {
                itemRow.innerHTML = originalRowHtml;
                itemRow.classList.remove('product-creation-row');
                // Re-attach live search to the description input
                const descInput = itemRow.querySelector('.item-description');
                if (descInput) {
                    this.attachToInput(descInput);
                }
            });
        }
        
        // Generate code button handler
        const generateBtn = itemRow.querySelector('#generate-code-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', async () => {
                try {
                    const baseUrl = window.location.pathname.includes('/fit/public') ? '/fit/public' : '';
                    const response = await fetch(`${baseUrl}/api/products/generate-code`);
                    const data = await response.json();
                    if (data.success && data.code) {
                        const codeInput = itemRow.querySelector('input[name="new_product_code"]');
                        if (codeInput) {
                            codeInput.value = data.code;
                        }
                    }
                } catch (error) {
                    console.error('ProductLiveSearch: Error generating code:', error);
                }
            });
        }
        
        // Save button handler
        const saveBtn = itemRow.querySelector('.btn-save-product');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => {
                this.saveNewProduct(itemRow, originalRowHtml, rowIndex);
            });
        }
        
        // Enter key handler for form inputs
        const inputs = itemRow.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && e.target.type !== 'textarea') {
                    e.preventDefault();
                    this.saveNewProduct(itemRow, originalRowHtml, rowIndex);
                }
            });
        });
        
        // Real-time duplicate name check
        const nameInput = itemRow.querySelector('input[name="new_product_name"]');
        if (nameInput) {
            let checkTimeout;
            nameInput.addEventListener('input', () => {
                clearTimeout(checkTimeout);
                const value = nameInput.value.trim();
                if (value.length >= 2) {
                    checkTimeout = setTimeout(() => {
                        this.checkProductName(value, nameInput);
                    }, 300);
                }
            });
        }
    }
    
    async checkProductName(name, input) {
        try {
            const baseUrl = window.location.pathname.includes('/fit/public') ? '/fit/public' : '';
            const response = await fetch(`${baseUrl}/api/products/check-name?name=${encodeURIComponent(name)}`);
            
            // If the endpoint returns an error, just ignore the check
            if (!response.ok) {
                // Remove any validation feedback since we can't check
                input.classList.remove('is-invalid');
                const feedback = input.nextElementSibling;
                if (feedback) {
                    feedback.textContent = '';
                }
                return;
            }
            
            const data = await response.json();
            
            const feedback = input.nextElementSibling;
            if (data.exists) {
                input.classList.add('is-invalid');
                if (feedback) {
                    feedback.textContent = this.__('products.name_already_exists');
                }
            } else {
                input.classList.remove('is-invalid');
                if (feedback) {
                    feedback.textContent = '';
                }
            }
        } catch (error) {
            // Silently ignore errors - the duplicate check is not critical
            // Just ensure the field is not marked as invalid
            input.classList.remove('is-invalid');
        }
    }
    
    async saveNewProduct(itemRow, originalRowHtml, rowIndex) {
        // Get form data
        const formData = {
            name: itemRow.querySelector('input[name="new_product_name"]').value.trim(),
            code: itemRow.querySelector('input[name="new_product_code"]').value.trim(),
            unit_price: itemRow.querySelector('input[name="new_product_price"]').value,
            vat_rate_id: itemRow.querySelector('select[name="new_product_vat"]').value || '1',
            category_id: itemRow.querySelector('input[name="new_product_category_id"]')?.value || itemRow.querySelector('select[name="new_product_category"]')?.value || '1',
            description: itemRow.querySelector('textarea[name="new_product_description"]').value.trim(),
            item_type: 'product',
            csrf_token: document.querySelector('meta[name="csrf-token"]')?.content || 
                        document.querySelector('input[name="csrf_token"]')?.value || ''
        };
        
        // Clear previous errors
        itemRow.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        const errorAlert = itemRow.querySelector('#creation-error');
        if (errorAlert) {
            errorAlert.classList.add('d-none');
        }
        
        // Validate required fields
        let hasError = false;
        if (!formData.name) {
            itemRow.querySelector('input[name="new_product_name"]').classList.add('is-invalid');
            hasError = true;
        }
        if (!formData.unit_price || parseFloat(formData.unit_price) < 0) {
            itemRow.querySelector('input[name="new_product_price"]').classList.add('is-invalid');
            hasError = true;
        }
        
        if (hasError) {
            return;
        }
        
        // Show loading state
        const saveBtn = itemRow.querySelector('.btn-save-product');
        const originalBtnHtml = saveBtn.innerHTML;
        saveBtn.disabled = true;
        saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> ' + this.__('common.saving');
        
        try {
            const baseUrl = window.location.pathname.includes('/fit/public') ? '/fit/public' : '';
            const response = await fetch(`${baseUrl}/api/products/quick-create`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-Token': formData.csrf_token
                },
                body: JSON.stringify(formData)
            });
            
            const data = await response.json();
            
            if (data.success && data.product) {
                // Restore original row
                itemRow.innerHTML = originalRowHtml;
                itemRow.classList.remove('product-creation-row');
                
                // Get the description input
                const descInput = itemRow.querySelector('.item-description');
                if (descInput) {
                    // Set the product in the row
                    this.currentInput = descInput;
                    // Add flag to indicate this product was created inline
                    data.product.created_inline = true;
                    this.selectProduct(data.product, descInput);
                    
                    // Show success message
                    this.showSuccessMessage(this.__('products.created_successfully', { name: data.product.name }));
                }
            } else {
                // Show error
                if (errorAlert) {
                    let errorMessage = data.message || this.__('common.error_occurred');
                    
                    // If there are field-specific errors, show them
                    if (data.errors && typeof data.errors === 'object') {
                        const errorList = Object.entries(data.errors).map(([field, message]) => {
                            return `${field}: ${message}`;
                        }).join('<br>');
                        errorMessage += '<br>' + errorList;
                        
                        // Also highlight the invalid fields
                        Object.keys(data.errors).forEach(field => {
                            let input;
                            if (field === 'name') input = itemRow.querySelector('input[name="new_product_name"]');
                            else if (field === 'code') input = itemRow.querySelector('input[name="new_product_code"]');
                            else if (field === 'unit_price') input = itemRow.querySelector('input[name="new_product_price"]');
                            else if (field === 'category_id') input = itemRow.querySelector('select[name="new_product_category"]');
                            
                            if (input) {
                                input.classList.add('is-invalid');
                                const feedback = input.parentElement.querySelector('.invalid-feedback') || 
                                               input.parentElement.parentElement.querySelector('.invalid-feedback');
                                if (feedback) {
                                    feedback.textContent = data.errors[field];
                                }
                            }
                        });
                    }
                    
                    errorAlert.innerHTML = errorMessage;
                    errorAlert.classList.remove('d-none');
                }
                saveBtn.disabled = false;
                saveBtn.innerHTML = originalBtnHtml;
            }
        } catch (error) {
            console.error('ProductLiveSearch: Error saving product:', error);
            if (errorAlert) {
                errorAlert.textContent = this.__('common.error_occurred');
                errorAlert.classList.remove('d-none');
            }
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalBtnHtml;
        }
    }
    
    showSuccessMessage(message) {
        // Create toast notification
        const toast = document.createElement('div');
        toast.className = 'position-fixed top-0 end-0 p-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="toast show align-items-center text-white bg-success border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-check-circle me-2"></i>
                        ${this.escapeHtml(message)}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        document.body.appendChild(toast);
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// Initialize when module loads
const productLiveSearch = new ProductLiveSearch();

// Also expose to window for manual initialization if needed
window.ProductLiveSearch = ProductLiveSearch;
window.productLiveSearch = productLiveSearch;

// Add a manual initialization function
window.initProductLiveSearch = function() {
    // Manual ProductLiveSearch initialization
    if (productLiveSearch.isDivInvoice()) {
        productLiveSearch.attachToExistingInputs();
        // Initialized for DIV invoice
    } else {
        // Not a DIV invoice, skipping initialization
    }
    return productLiveSearch;
};