<?php
/**
 * Check and add exclude_patient_line column
 */

// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found!");
}

$envContent = file_get_contents($envFile);
$lines = explode("\n", $envContent);
$env = [];

foreach ($lines as $line) {
    $line = trim($line);
    if (empty($line) || strpos($line, '#') === 0) continue;
    
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $env[$key] = $value;
    }
}

// Database configuration from .env
$dbHost = $env['DB_HOST'] ?? '127.0.0.1';
$dbPort = $env['DB_PORT'] ?? '3306';
$dbName = $env['DB_DATABASE'] ?? 'fitapp';
$dbUser = $env['DB_USERNAME'] ?? 'root';
$dbPass = $env['DB_PASSWORD'] ?? '';

echo "<pre>";
echo "Using database configuration from .env:\n";
echo "Database: $dbName\n";
echo "Host: $dbHost\n";
echo "User: $dbUser\n\n";

try {
    // Connect to database using .env credentials
    $dsn = "mysql:host=$dbHost;port=$dbPort;dbname=$dbName;charset=utf8mb4";
    $db = new PDO($dsn, $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if column exists
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasColumn = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'exclude_patient_line') {
            $hasColumn = true;
            echo "✓ Column 'exclude_patient_line' exists in retrocession_data_entry table\n";
            echo "  Type: " . $column['Type'] . "\n";
            echo "  Default: " . $column['Default'] . "\n";
            break;
        }
    }
    
    if (!$hasColumn) {
        echo "✗ Column 'exclude_patient_line' NOT FOUND\n";
        echo "Running migration to add the column...\n";
        
        // Run the migration
        $sql = "ALTER TABLE retrocession_data_entry 
                ADD COLUMN exclude_patient_line TINYINT(1) DEFAULT 0 AFTER status";
        
        try {
            $db->exec($sql);
            echo "✓ Column added successfully!\n";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate column') !== false) {
                echo "Note: Column already exists\n";
            } else {
                throw $e;
            }
        }
        
        // Try to add index (might already exist)
        try {
            $db->exec("ALTER TABLE retrocession_data_entry ADD INDEX idx_exclude_patient (exclude_patient_line)");
            echo "✓ Index added successfully!\n";
        } catch (PDOException $e) {
            echo "Note: Index might already exist\n";
        }
        
        // Update any existing entries
        $db->exec("UPDATE retrocession_data_entry SET exclude_patient_line = 0 WHERE exclude_patient_line IS NULL");
        echo "✓ Existing entries updated\n";
    }
    
    // Check some existing data
    $stmt = $db->query("SELECT id, practitioner_id, period_month, period_year, exclude_patient_line, status FROM retrocession_data_entry ORDER BY id DESC LIMIT 5");
    $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nRecent retrocession entries:\n";
    if (empty($entries)) {
        echo "No retrocession entries found.\n";
    } else {
        foreach ($entries as $entry) {
            echo sprintf("ID: %d, Practitioner: %d, Period: %02d/%d, Exclude Patient: %s, Status: %s\n",
                $entry['id'],
                $entry['practitioner_id'],
                $entry['period_month'],
                $entry['period_year'],
                $entry['exclude_patient_line'] ? 'Yes' : 'No',
                $entry['status']
            );
        }
    }
    
    echo "\n✅ Column check complete!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "\nPlease check:\n";
    echo "1. MySQL is running\n";
    echo "2. Database '$dbName' exists\n";
    echo "3. User '$dbUser' has the correct password\n";
}
echo "</pre>";