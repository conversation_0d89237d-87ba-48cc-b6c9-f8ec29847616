# Phase 9: Legacy Advanced Financial Features (Week 14)

## Task 6.1: Multi-Currency Support
### Subtask 6.1.1: Currency Database Schema
**Files to Create:**
- Create currency tables migration

**Code to Implement:**
```sql
CREATE TABLE `currencies` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(3) NOT NULL UNIQUE,
    `name` VARCHAR(50) NOT NULL,
    `symbol` VARCHAR(10) NOT NULL,
    `decimal_places` TINYINT DEFAULT 2,
    `is_default` BOOLEAN DEFAULT FALSE,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE `exchange_rates` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `from_currency_id` INT UNSIGNED NOT NULL,
    `to_currency_id` INT UNSIGNED NOT NULL,
    `rate` DECIMAL(10,6) NOT NULL,
    `effective_date` DATE NOT NULL,
    `source` VARCHAR(50) DEFAULT 'manual',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`from_currency_id`) REFERENCES `currencies` (`id`),
    FOREIGN KEY (`to_currency_id`) REFERENCES `currencies` (`id`),
    UNIQUE KEY `currency_date` (`from_currency_id`, `to_currency_id`, `effective_date`)
);
```

**Test Cases:**
- [ ] Currency tables created
- [ ] Exchange rates store correctly
- [ ] Default currency enforced
- [ ] Historical rates maintained

### Subtask 6.1.2: Currency Integration
**Files to Update:**
- Update Invoice model for currency
- Update Client model for preferred currency
- Update payment tables

**Features to Implement:**
- Invoice currency selection
- Automatic conversion for reports
- Client preferred currency
- Multi-currency payments
- Exchange rate updates

**Test Cases:**
- [ ] Invoices display correct currency
- [ ] Conversions calculate accurately
- [ ] Reports handle multi-currency
- [ ] Payment allocation works
- [ ] Rate updates apply correctly

## Task 6.2: Advanced Payment Management
### Subtask 6.2.1: Payment Methods Configuration
**Files to Create:**
- Create payment methods tables
- Create `/app/models/PaymentMethod.php`

**Features to Implement:**
- Multiple payment methods per invoice
- Bank account management
- Payment fees/charges
- Automatic reconciliation rules
- Payment gateway integration prep

**Test Cases:**
- [ ] Multiple payments per invoice
- [ ] Fees calculate correctly
- [ ] Bank accounts link properly
- [ ] Reconciliation rules match
- [ ] Gateway placeholders work

### Subtask 6.2.2: Payment Plans
**Files to Create:**
- Create payment plans tables
- Create `/app/models/PaymentPlan.php`

**Features to Implement:**
- Installment plans creation
- Automatic invoice generation
- Payment schedule tracking
- Late payment fees
- Plan modification workflow

**Test Cases:**
- [ ] Plans generate correctly
- [ ] Schedules create invoices
- [ ] Late fees calculate
- [ ] Modifications tracked
- [ ] Notifications sent

## Task 6.3: Financial Reporting Suite
### Subtask 6.3.1: Report Templates System
**Files to Create:**
- Create report templates tables
- Create `/app/services/ReportGenerator.php`

**Standard Reports to Include:**
- Income Statement
- Balance Sheet
- Cash Flow Statement
- Aged Receivables
- Tax Summary
- Client Statement

**Test Cases:**
- [ ] Reports generate accurately
- [ ] Multi-currency handled
- [ ] Date ranges work
- [ ] Filters apply correctly
- [ ] Export formats work

### Subtask 6.3.2: Custom Report Builder
**Files to Create:**
- Create `/app/views/reports/builder.twig`
- Create report builder JavaScript

**Features to Implement:**
- Drag-drop report builder
- Custom calculations
- Conditional formatting
- Scheduled reports
- Email delivery

**Test Cases:**
- [ ] Builder saves reports
- [ ] Calculations accurate
- [ ] Formatting applies
- [ ] Schedules execute
- [ ] Emails deliver

## Task 6.4: Accounting Integration
### Subtask 6.4.1: Chart of Accounts
**Files to Create:**
- Create chart of accounts tables
- Create `/app/models/Account.php`

**Features to Implement:**
- Account hierarchy
- Account types
- Opening balances
- Period closing
- Journal entries

**Test Cases:**
- [ ] Accounts organize hierarchically
- [ ] Types categorize correctly
- [ ] Balances calculate
- [ ] Periods close properly
- [ ] Journals balance

### Subtask 6.4.2: Export Formats
**Files to Create:**
- Create `/app/services/AccountingExport.php`

**Formats to Support:**
- QuickBooks IIF
- Sage CSV
- Generic CSV
- DATEV (German)
- FEC (French)

**Test Cases:**
- [ ] QuickBooks format valid
- [ ] Sage import works
- [ ] CSV includes all fields
- [ ] DATEV compliance met
- [ ] FEC format correct

## Task 6.5: Compliance Features
### Subtask 6.5.1: Tax Compliance
**Features to Implement:**
- VAT/GST reporting
- Reverse charge mechanism
- Intrastat reporting
- Digital services VAT
- Tax audit trail

**Test Cases:**
- [ ] VAT reports accurate
- [ ] Reverse charge works
- [ ] Intrastat data complete
- [ ] Digital VAT calculated
- [ ] Audit trail comprehensive

### Subtask 6.5.2: Data Protection
**Features to Implement:**
- GDPR compliance tools
- Data retention policies
- Right to erasure
- Data portability
- Consent management

**Test Cases:**
- [ ] Retention policies apply
- [ ] Erasure removes data
- [ ] Export includes all data
- [ ] Consent tracked
- [ ] Anonymization works