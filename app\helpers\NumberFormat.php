<?php

namespace App\Helpers;

use Flight;

class NumberFormat
{
    /**
     * Generate a formatted number based on the type
     * 
     * @param string $type The type of number (client, patient, invoice)
     * @param int $nextNumber The next sequential number to use
     * @return string The formatted number
     */
    public static function generate($type, $nextNumber)
    {
        try {
            $db = Flight::db();
            
            // Get number format settings from config
            $stmt = $db->prepare("SELECT `key`, `value` FROM config WHERE category = 'number_format' AND `key` LIKE ?");
            $stmt->execute([$type . '_%']);
            $results = $stmt->fetchAll(\PDO::FETCH_KEY_PAIR);
            
            // Default settings
            $defaults = [
                'client' => [
                    'format' => 'CLT-{YEAR}-{NUMBER:4}',
                    'prefix' => 'CLT',
                    'length' => 4,
                    'separator' => '-'
                ],
                'patient' => [
                    'format' => 'PAT-{YEAR}-{NUMBER:4}',
                    'prefix' => 'PAT',
                    'length' => 4,
                    'separator' => '-'
                ],
                'invoice' => [
                    'format' => 'INV-{YEAR}-{NUMBER:4}',
                    'prefix' => 'INV',
                    'length' => 4,
                    'separator' => '-'
                ]
            ];
            
            // Get settings or use defaults
            $format = $results[$type . '_number_format'] ?? $defaults[$type]['format'];
            $length = intval($results[$type . '_number_length'] ?? $defaults[$type]['length']);
            
            // Generate the formatted number
            return self::formatNumber($format, $nextNumber, $length);
        } catch (\Exception $e) {
            // Fallback to simple format if database error
            return strtoupper($type) . '-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
        }
    }
    
    /**
     * Format a number according to the given pattern
     * 
     * @param string $format The format pattern
     * @param int $number The sequential number
     * @param int $defaultLength Default padding length
     * @return string The formatted number
     */
    private static function formatNumber($format, $number, $defaultLength = 4)
    {
        $now = new \DateTime();
        
        // Replace date variables
        $formatted = str_replace('{YEAR}', $now->format('Y'), $format);
        $formatted = str_replace('{YEAR:2}', $now->format('y'), $formatted);
        $formatted = str_replace('{MONTH}', $now->format('m'), $formatted);
        $formatted = str_replace('{DAY}', $now->format('d'), $formatted);
        
        // Replace number variables with specific length
        if (preg_match('/\{NUMBER:(\d+)\}/', $formatted, $matches)) {
            $length = intval($matches[1]);
            $paddedNumber = str_pad($number, $length, '0', STR_PAD_LEFT);
            $formatted = str_replace($matches[0], $paddedNumber, $formatted);
        }
        
        // Replace simple {NUMBER} with default length
        $formatted = str_replace('{NUMBER}', str_pad($number, $defaultLength, '0', STR_PAD_LEFT), $formatted);
        
        return $formatted;
    }
    
    /**
     * Get the next number for a specific type
     * 
     * @param string $type The type of number (client, patient, invoice)
     * @param string $table The database table to check
     * @param string $column The column containing the formatted numbers
     * @return int The next sequential number
     */
    public static function getNextNumber($type, $table, $column = 'number')
    {
        try {
            $db = Flight::db();
            
            // Get the current year
            $currentYear = date('Y');
            
            // Check if yearly reset is needed
            $lastYearKey = $type . '_number_last_year';
            $lastNumberKey = $type . '_number_last_number';
            
            $stmt = $db->prepare("SELECT `value` FROM config WHERE `key` = ? AND category = 'number_format'");
            $stmt->execute([$lastYearKey]);
            $lastYear = $stmt->fetchColumn();
            
            // If year has changed, reset the counter
            if ($lastYear && $lastYear != $currentYear) {
                // Update the last year
                $stmt = $db->prepare("UPDATE config SET `value` = ? WHERE `key` = ? AND category = 'number_format'");
                $stmt->execute([$currentYear, $lastYearKey]);
                
                // Reset the counter to 1
                $stmt = $db->prepare("UPDATE config SET `value` = '1' WHERE `key` = ? AND category = 'number_format'");
                $stmt->execute([$lastNumberKey]);
                
                return 1;
            }
            
            // Get format settings to extract prefix
            $stmt = $db->prepare("SELECT `value` FROM config WHERE `key` = ? AND category = 'number_format'");
            $stmt->execute([$type . '_number_prefix']);
            $prefix = $stmt->fetchColumn() ?: strtoupper(substr($type, 0, 3));
            
            // Find the highest number for this year
            $pattern = $prefix . '%' . $currentYear . '%';
            $stmt = $db->prepare("SELECT MAX(CAST(SUBSTRING_INDEX($column, '-', -1) AS UNSIGNED)) as max_num 
                                 FROM $table 
                                 WHERE $column LIKE ?");
            $stmt->execute([$pattern]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            $nextNumber = ($result['max_num'] ?? 0) + 1;
            
            // Update tracking values
            $stmt = $db->prepare("UPDATE config SET `value` = ? WHERE `key` = ? AND category = 'number_format'");
            $stmt->execute([$currentYear, $lastYearKey]);
            
            $stmt = $db->prepare("UPDATE config SET `value` = ? WHERE `key` = ? AND category = 'number_format'");
            $stmt->execute([$nextNumber, $lastNumberKey]);
            
            return $nextNumber;
        } catch (\Exception $e) {
            // If error, try to get any max number
            try {
                $stmt = $db->prepare("SELECT MAX(CAST(SUBSTRING_INDEX($column, '-', -1) AS UNSIGNED)) as max_num FROM $table");
                $stmt->execute();
                $result = $stmt->fetch(\PDO::FETCH_ASSOC);
                return ($result['max_num'] ?? 0) + 1;
            } catch (\Exception $e2) {
                return 1; // Start from 1 if all else fails
            }
        }
    }
    
    /**
     * Validate a number format pattern
     * 
     * @param string $format The format pattern to validate
     * @return bool True if valid, false otherwise
     */
    public static function validateFormat($format)
    {
        // Check if format contains at least one variable
        $hasVariable = false;
        $validVariables = ['{YEAR}', '{YEAR:2}', '{MONTH}', '{DAY}', '{NUMBER}'];
        
        foreach ($validVariables as $var) {
            if (strpos($format, $var) !== false) {
                $hasVariable = true;
                break;
            }
        }
        
        // Check for {NUMBER:n} pattern
        if (preg_match('/\{NUMBER:\d+\}/', $format)) {
            $hasVariable = true;
        }
        
        return $hasVariable;
    }
}