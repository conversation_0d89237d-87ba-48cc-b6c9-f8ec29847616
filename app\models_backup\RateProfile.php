<?php

namespace App\Models;

use Flight;
use PDO;
use Exception;

class RateProfile extends \App\Core\Model
{
    protected $table = 'rate_profiles';
    
    protected $fillable = [
        'name', 'code', 'profile_type', 'description',
        'is_active', 'is_default', 'created_by', 
        'created_at', 'updated_at'
    ];
    
    // Profile types
    const TYPE_RETROCESSION = 'retrocession';
    const TYPE_HOURLY = 'hourly';
    const TYPE_RENTAL = 'rental';
    const TYPE_MIXED = 'mixed';
    
    // Rate types
    const RATE_CNS_PERCENT = 'cns_percent';
    const RATE_PATIENT_PERCENT = 'patient_percent';
    const RATE_SECRETARIAT_PERCENT = 'secretariat_percent';
    const RATE_HOURLY_RATE = 'hourly_rate';
    const RATE_RENT_AMOUNT = 'rent_amount';
    const RATE_CHARGES_AMOUNT = 'charges_amount';
    
    /**
     * Get profile by ID
     */
    public function getById($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM rate_profiles WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create rate profile
     */
    public function createProfile($data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Create profile
            $stmt = $db->prepare("
                INSERT INTO rate_profiles (
                    name, code, profile_type, description,
                    is_active, is_default, created_by
                ) VALUES (
                    :name, :code, :profile_type, :description,
                    :is_active, :is_default, :created_by
                )
            ");
            
            $stmt->execute([
                ':name' => $data['name'],
                ':code' => $data['code'],
                ':profile_type' => $data['profile_type'],
                ':description' => $data['description'] ?? null,
                ':is_active' => $data['is_active'] ?? true,
                ':is_default' => $data['is_default'] ?? false,
                ':created_by' => $data['created_by'] ?? $_SESSION['user_id'] ?? 1
            ]);
            
            $profileId = $db->lastInsertId();
            
            // Add initial rates if provided
            if (!empty($data['rates'])) {
                foreach ($data['rates'] as $rateType => $rateValue) {
                    $this->addRate($profileId, $rateType, $rateValue, $data['valid_from'] ?? date('Y-m-d'));
                }
            }
            
            $db->commit();
            
            return $this->getById($profileId);
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Add rate to profile
     */
    public function addRate($profileId, $rateType, $baseValue, $validFrom, $validTo = null, $notes = null)
    {
        $db = Flight::db();
        
        // Mark previous rates as not current
        $stmt = $db->prepare("
            UPDATE rate_profile_rates 
            SET is_current = FALSE, updated_at = NOW() 
            WHERE profile_id = ? AND rate_type = ? AND is_current = TRUE
        ");
        $stmt->execute([$profileId, $rateType]);
        
        // Insert new rate
        $stmt = $db->prepare("
            INSERT INTO rate_profile_rates (
                profile_id, rate_type, base_value,
                valid_from, valid_to, is_current, notes
            ) VALUES (
                :profile_id, :rate_type, :base_value,
                :valid_from, :valid_to, :is_current, :notes
            )
        ");
        
        $stmt->execute([
            ':profile_id' => $profileId,
            ':rate_type' => $rateType,
            ':base_value' => $baseValue,
            ':valid_from' => $validFrom,
            ':valid_to' => $validTo,
            ':is_current' => $validTo === null ? true : false,
            ':notes' => $notes
        ]);
        
        return $db->lastInsertId();
    }
    
    /**
     * Add tier to rate
     */
    public function addTier($profileRateId, $tiers)
    {
        $db = Flight::db();
        
        // Delete existing tiers
        $stmt = $db->prepare("DELETE FROM rate_profile_tiers WHERE profile_rate_id = ?");
        $stmt->execute([$profileRateId]);
        
        // Insert new tiers
        $stmt = $db->prepare("
            INSERT INTO rate_profile_tiers (
                profile_rate_id, tier_order, threshold_from,
                threshold_to, tier_value, threshold_type
            ) VALUES (
                :profile_rate_id, :tier_order, :threshold_from,
                :threshold_to, :tier_value, :threshold_type
            )
        ");
        
        foreach ($tiers as $order => $tier) {
            $stmt->execute([
                ':profile_rate_id' => $profileRateId,
                ':tier_order' => $order + 1,
                ':threshold_from' => $tier['from'],
                ':threshold_to' => $tier['to'],
                ':tier_value' => $tier['value'],
                ':threshold_type' => $tier['type'] ?? 'amount'
            ]);
        }
    }
    
    /**
     * Assign profile to practitioner
     */
    public function assignToPractitioner($profileId, $practitionerId, $assignedFrom, $assignedTo = null, $notes = null)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // End current assignment if exists
            $stmt = $db->prepare("
                UPDATE practitioner_rate_assignments 
                SET assigned_to = DATE_SUB(:assigned_from, INTERVAL 1 DAY),
                    updated_at = NOW()
                WHERE practitioner_id = :practitioner_id 
                AND assigned_to IS NULL
            ");
            $stmt->execute([
                ':practitioner_id' => $practitionerId,
                ':assigned_from' => $assignedFrom
            ]);
            
            // Create new assignment
            $stmt = $db->prepare("
                INSERT INTO practitioner_rate_assignments (
                    practitioner_id, profile_id, assigned_from,
                    assigned_to, assigned_by, notes
                ) VALUES (
                    :practitioner_id, :profile_id, :assigned_from,
                    :assigned_to, :assigned_by, :notes
                )
            ");
            
            $stmt->execute([
                ':practitioner_id' => $practitionerId,
                ':profile_id' => $profileId,
                ':assigned_from' => $assignedFrom,
                ':assigned_to' => $assignedTo,
                ':assigned_by' => $_SESSION['user_id'] ?? 1,
                ':notes' => $notes
            ]);
            
            $assignmentId = $db->lastInsertId();
            
            // Update practitioner's default profile
            $stmt = $db->prepare("
                UPDATE clients 
                SET retrocession_profile_id = :profile_id 
                WHERE id = :practitioner_id
            ");
            $stmt->execute([
                ':profile_id' => $profileId,
                ':practitioner_id' => $practitionerId
            ]);
            
            $db->commit();
            
            return $assignmentId;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get effective rate for practitioner
     */
    public function getEffectiveRate($practitionerId, $rateType, $date = null)
    {
        $db = Flight::db();
        $date = $date ?: date('Y-m-d');
        
        // First check for individual overrides
        $stmt = $db->prepare("
            SELECT override_value as rate
            FROM practitioner_rate_overrides
            WHERE practitioner_id = :practitioner_id
            AND rate_type = :rate_type
            AND valid_from <= :date
            AND (valid_to IS NULL OR valid_to >= :date)
            ORDER BY valid_from DESC
            LIMIT 1
        ");
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':rate_type' => $rateType,
            ':date' => $date
        ]);
        $override = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($override) {
            return $override['rate'];
        }
        
        // Get assigned profile rate
        $stmt = $db->prepare("
            SELECT rpr.base_value as rate, rpr.id as rate_id
            FROM practitioner_rate_assignments pra
            JOIN rate_profile_rates rpr ON pra.profile_id = rpr.profile_id
            WHERE pra.practitioner_id = :practitioner_id
            AND rpr.rate_type = :rate_type
            AND pra.assigned_from <= :date
            AND (pra.assigned_to IS NULL OR pra.assigned_to >= :date)
            AND rpr.valid_from <= :date
            AND (rpr.valid_to IS NULL OR rpr.valid_to >= :date)
            ORDER BY pra.assigned_from DESC, rpr.valid_from DESC
            LIMIT 1
        ");
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':rate_type' => $rateType,
            ':date' => $date
        ]);
        $profileRate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$profileRate) {
            return null;
        }
        
        return $profileRate['rate'];
    }
    
    /**
     * Calculate tiered rate
     */
    public function calculateTieredRate($profileRateId, $amount)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT tier_value
            FROM rate_profile_tiers
            WHERE profile_rate_id = :profile_rate_id
            AND threshold_from <= :amount
            AND (threshold_to IS NULL OR threshold_to > :amount)
            ORDER BY tier_order
            LIMIT 1
        ");
        $stmt->execute([
            ':profile_rate_id' => $profileRateId,
            ':amount' => $amount
        ]);
        $tier = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $tier ? $tier['tier_value'] : null;
    }
    
    /**
     * Preview cascade update
     */
    public function previewCascadeUpdate($profileId, $changes)
    {
        $db = Flight::db();
        
        // Count affected draft invoices
        $stmt = $db->prepare("
            SELECT COUNT(*) as count
            FROM invoices
            WHERE profile_id = :profile_id
            AND status = 'draft'
            AND (locked_at IS NULL)
        ");
        $stmt->execute([':profile_id' => $profileId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'affected_count' => $result['count'],
            'changes' => $changes
        ];
    }
    
    /**
     * Apply cascade update to draft invoices
     */
    public function applyCascadeUpdate($profileId, $changes)
    {
        $db = Flight::db();
        $updatedCount = 0;
        
        try {
            $db->beginTransaction();
            
            // Get affected invoices
            $stmt = $db->prepare("
                SELECT id, invoice_type
                FROM invoices
                WHERE profile_id = :profile_id
                AND status = 'draft'
                AND (locked_at IS NULL)
            ");
            $stmt->execute([':profile_id' => $profileId]);
            $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($invoices as $invoice) {
                // Recalculate invoice based on new rates
                if (in_array($invoice['invoice_type'], ['retrocession_30', 'retrocession_25'])) {
                    $this->recalculateRetrocessionInvoice($invoice['id'], $changes);
                }
                $updatedCount++;
            }
            
            $db->commit();
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
        
        return $updatedCount;
    }
    
    /**
     * Recalculate retrocession invoice with new rates
     */
    private function recalculateRetrocessionInvoice($invoiceId, $newRates)
    {
        $db = Flight::db();
        
        // Get retrocession data
        $stmt = $db->prepare("
            SELECT * FROM invoice_retrocessions 
            WHERE invoice_id = ?
        ");
        $stmt->execute([$invoiceId]);
        $retro = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$retro) {
            return;
        }
        
        // Apply new rates
        $cnsPercent = $newRates['cns_percent'] ?? $retro['cns_percentage'];
        $patientPercent = $newRates['patient_percent'] ?? $retro['patient_percentage'];
        $secretariatPercent = $newRates['secretariat_percent'] ?? $retro['secretariat_percentage'];
        
        // Recalculate amounts
        $totalAmount = $retro['cns_amount'] + $retro['patient_amount'];
        $secretariatTvac = $totalAmount * ($secretariatPercent / 100);
        $vatAmount = $secretariatTvac - ($secretariatTvac / 1.17);
        $secretariatHtva = $secretariatTvac - $vatAmount;
        
        // Update retrocession record
        $stmt = $db->prepare("
            UPDATE invoice_retrocessions
            SET cns_percentage = :cns_percentage,
                patient_percentage = :patient_percentage,
                secretariat_percentage = :secretariat_percentage,
                secretariat_tvac = :secretariat_tvac,
                secretariat_htva = :secretariat_htva,
                vat_amount = :vat_amount,
                updated_at = NOW()
            WHERE invoice_id = :invoice_id
        ");
        
        $stmt->execute([
            ':cns_percentage' => $cnsPercent,
            ':patient_percentage' => $patientPercent,
            ':secretariat_percentage' => $secretariatPercent,
            ':secretariat_tvac' => $secretariatTvac,
            ':secretariat_htva' => $secretariatHtva,
            ':vat_amount' => $vatAmount,
            ':invoice_id' => $invoiceId
        ]);
        
        // Update invoice lines for secretariat
        $stmt = $db->prepare("
            UPDATE invoice_lines
            SET unit_price = :unit_price,
                line_total = :line_total
            WHERE invoice_id = :invoice_id
            AND line_type = 'secretariat'
        ");
        
        $stmt->execute([
            ':unit_price' => $secretariatTvac,
            ':line_total' => $secretariatTvac,
            ':invoice_id' => $invoiceId
        ]);
        
        // Recalculate invoice totals
        $invoiceModel = new Invoice();
        $invoiceModel->calculateTotals($invoiceId);
    }
    
    /**
     * Get profile with all rates
     */
    public function getProfileWithRates($profileId)
    {
        $profile = $this->getById($profileId);
        
        if (!$profile) {
            return null;
        }
        
        $db = Flight::db();
        
        // Get current rates
        $stmt = $db->prepare("
            SELECT * FROM rate_profile_rates
            WHERE profile_id = ? AND is_current = TRUE
            ORDER BY rate_type
        ");
        $stmt->execute([$profileId]);
        $ratesData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Organize rates by type for easier access in template
        $profile['rates'] = [];
        foreach ($ratesData as $rate) {
            $profile['rates'][$rate['rate_type']] = $rate['base_value'];
        }
        $profile['rates_data'] = $ratesData; // Keep original data too
        
        // Get tiers for each rate
        foreach ($profile['rates_data'] as &$rate) {
            $stmt = $db->prepare("
                SELECT * FROM rate_profile_tiers
                WHERE profile_rate_id = ?
                ORDER BY tier_order
            ");
            $stmt->execute([$rate['id']]);
            $rate['tiers'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        return $profile;
    }
}