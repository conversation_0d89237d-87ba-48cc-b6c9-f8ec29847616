<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>';

echo "<h1>Fix Invoice FAC-2025-0187 Duplicate Lines</h1>";

try {
    // Start transaction
    $db->beginTransaction();
    
    // Get invoice
    $stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
    $stmt->execute(['FAC-2025-0187']);
    $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        throw new Exception("Invoice FAC-2025-0187 not found!");
    }
    
    echo '<div class="section">';
    echo '<h2>Current Invoice Lines</h2>';
    
    // Get all current lines
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute([$invoice['id']]);
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo '<table>';
    echo '<tr><th>ID</th><th>Description</th><th>Qty</th><th>Unit Price</th><th>VAT Rate</th><th>Sort Order</th></tr>';
    foreach ($lines as $line) {
        echo '<tr>';
        echo '<td>' . $line['id'] . '</td>';
        echo '<td>' . htmlspecialchars($line['description']) . '</td>';
        echo '<td>' . $line['quantity'] . '</td>';
        echo '<td>' . $line['unit_price'] . '</td>';
        echo '<td>' . $line['vat_rate'] . '%</td>';
        echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    echo '</div>';
    
    // Check for duplicates
    $uniqueLines = [];
    $duplicateIds = [];
    $keepIds = [];
    
    foreach ($lines as $line) {
        $key = $line['description'] . '|' . $line['quantity'] . '|' . $line['unit_price'] . '|' . $line['vat_rate'];
        
        if (!isset($uniqueLines[$key])) {
            $uniqueLines[$key] = $line;
            $keepIds[] = $line['id'];
        } else {
            $duplicateIds[] = $line['id'];
        }
    }
    
    if (empty($duplicateIds)) {
        echo '<div class="success">';
        echo '<strong>✓ No duplicate lines found!</strong><br>';
        echo 'This invoice has ' . count($lines) . ' unique lines.';
        echo '</div>';
        
        // Check if both lines have the same description
        if (count($lines) == 2 && $lines[0]['description'] == $lines[1]['description']) {
            echo '<div class="warning">';
            echo '<strong>⚠️ Both lines have the same description!</strong><br>';
            echo 'This might appear as duplicates in the PDF even though they are separate lines.<br>';
            echo 'Consider updating one of the descriptions to make them distinct.';
            echo '</div>';
            
            echo '<div class="section">';
            echo '<h3>Suggested Fix: Update Line Descriptions</h3>';
            echo '<p>Would you like to update the descriptions to make them distinct?</p>';
            echo '<form method="post">';
            echo '<input type="hidden" name="action" value="update_descriptions">';
            echo '<table>';
            echo '<tr><th>Line</th><th>Current Description</th><th>New Description</th></tr>';
            
            $suggestions = [
                'Loyer mensuel' => ['Loyer mensuel - Local', 'Loyer mensuel - Charges'],
                'Monthly rent' => ['Monthly rent - Premises', 'Monthly rent - Charges']
            ];
            
            $baseDesc = $lines[0]['description'];
            $suggested = isset($suggestions[$baseDesc]) ? $suggestions[$baseDesc] : [$baseDesc . ' (1)', $baseDesc . ' (2)'];
            
            foreach ($lines as $i => $line) {
                echo '<tr>';
                echo '<td>Line ' . ($i + 1) . '</td>';
                echo '<td>' . htmlspecialchars($line['description']) . '</td>';
                echo '<td><input type="text" name="desc[' . $line['id'] . ']" value="' . htmlspecialchars($suggested[$i]) . '" style="width: 300px;"></td>';
                echo '</tr>';
            }
            echo '</table>';
            echo '<button type="submit" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">Update Descriptions</button>';
            echo '</form>';
            echo '</div>';
        }
    } else {
        echo '<div class="warning">';
        echo '<strong>⚠️ Found ' . count($duplicateIds) . ' duplicate lines!</strong><br>';
        echo 'Lines to remove: IDs ' . implode(', ', $duplicateIds) . '<br>';
        echo 'Lines to keep: IDs ' . implode(', ', $keepIds);
        echo '</div>';
        
        // Remove duplicates
        if (isset($_POST['confirm']) && $_POST['confirm'] == 'yes') {
            $stmt = $db->prepare("DELETE FROM invoice_lines WHERE id IN (" . implode(',', $duplicateIds) . ")");
            $stmt->execute();
            
            echo '<div class="success">';
            echo '<strong>✓ Removed ' . count($duplicateIds) . ' duplicate lines!</strong>';
            echo '</div>';
            
            // Recalculate totals
            $stmt = $db->prepare("
                SELECT 
                    SUM(quantity * unit_price) as subtotal,
                    SUM(quantity * unit_price * vat_rate / 100) as vat_amount
                FROM invoice_lines
                WHERE invoice_id = ?
            ");
            $stmt->execute([$invoice['id']]);
            $totals = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            $newSubtotal = $totals['subtotal'] ?? 0;
            $newVat = $totals['vat_amount'] ?? 0;
            $newTotal = $newSubtotal + $newVat;
            
            // Update invoice totals
            $stmt = $db->prepare("
                UPDATE invoices 
                SET subtotal = ?, vat_amount = ?, total = ?
                WHERE id = ?
            ");
            $stmt->execute([$newSubtotal, $newVat, $newTotal, $invoice['id']]);
            
            echo '<div class="success">';
            echo '<strong>✓ Updated invoice totals:</strong><br>';
            echo 'Subtotal: €' . number_format($newSubtotal, 2) . '<br>';
            echo 'VAT: €' . number_format($newVat, 2) . '<br>';
            echo 'Total: €' . number_format($newTotal, 2);
            echo '</div>';
        } else {
            echo '<form method="post">';
            echo '<p>Do you want to remove the duplicate lines?</p>';
            echo '<input type="hidden" name="confirm" value="yes">';
            echo '<button type="submit" style="padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">Remove Duplicates</button>';
            echo '</form>';
        }
    }
    
    // Handle description updates
    if (isset($_POST['action']) && $_POST['action'] == 'update_descriptions' && isset($_POST['desc'])) {
        foreach ($_POST['desc'] as $lineId => $newDesc) {
            $stmt = $db->prepare("UPDATE invoice_lines SET description = ? WHERE id = ?");
            $stmt->execute([$newDesc, $lineId]);
        }
        
        echo '<div class="success">';
        echo '<strong>✓ Updated line descriptions!</strong><br>';
        echo '<a href="' . $_SERVER['PHP_SELF'] . '">Refresh to see changes</a>';
        echo '</div>';
    }
    
    $db->commit();
    
} catch (Exception $e) {
    $db->rollBack();
    echo '<div class="error">';
    echo '<strong>Error:</strong> ' . $e->getMessage();
    echo '</div>';
}

echo '<div class="section">';
echo '<h3>Next Steps</h3>';
echo '<ul>';
echo '<li><a href="/fit/public/diagnose_invoice_187.php">Run full diagnostics</a></li>';
echo '<li><a href="/fit/public/invoices/' . ($invoice['id'] ?? '') . '/pdf" target="_blank">View PDF</a></li>';
echo '<li><a href="/fit/public/invoices">Back to invoices list</a></li>';
echo '</ul>';
echo '</div>';