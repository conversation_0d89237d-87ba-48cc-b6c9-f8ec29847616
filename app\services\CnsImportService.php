<?php

namespace App\Services;

use Flight;
use Exception;
use PDO;

class CnsImportService
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Process OCR on PDF file
     */
    public function processOcr($filePath)
    {
        try {
            // In production, you would use an OCR service like:
            // - Tesseract OCR
            // - Google Cloud Vision API
            // - Amazon Textract
            // - Azure Computer Vision
            
            // For now, we'll simulate OCR processing
            $simulatedData = $this->simulateOcrExtraction($filePath);
            
            return [
                'success' => true,
                'data' => $simulatedData,
                'confidence' => 0.95,
                'pages_processed' => 1
            ];
            
        } catch (Exception $e) {
            throw new Exception('OCR processing failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Get OCR preview
     */
    public function getOcrPreview($importId)
    {
        $stmt = $this->db->prepare("
            SELECT ocr_result, ocr_confidence 
            FROM cns_imports 
            WHERE id = ?
        ");
        
        $stmt->execute([$importId]);
        $result = $stmt->fetch();
        
        if (!$result || !$result['ocr_result']) {
            return null;
        }
        
        $ocrData = json_decode($result['ocr_result'], true);
        
        return [
            'data' => $ocrData,
            'confidence' => $result['ocr_confidence'],
            'fields' => $this->extractFieldsFromOcr($ocrData)
        ];
    }
    
    /**
     * Get OCR data
     */
    public function getOcrData($importId)
    {
        $stmt = $this->db->prepare("
            SELECT ocr_result 
            FROM cns_imports 
            WHERE id = ?
        ");
        
        $stmt->execute([$importId]);
        $result = $stmt->fetch();
        
        if (!$result || !$result['ocr_result']) {
            return null;
        }
        
        $ocrData = json_decode($result['ocr_result'], true);
        return $this->extractFieldsFromOcr($ocrData);
    }
    
    /**
     * Update OCR data
     */
    public function updateOcrData($importId, $data)
    {
        $stmt = $this->db->prepare("
            UPDATE cns_imports 
            SET ocr_result = :ocr_result,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        $ocrData = [
            'extracted_fields' => $data,
            'reviewed' => true,
            'reviewed_at' => date('Y-m-d H:i:s')
        ];
        
        $stmt->execute([
            ':ocr_result' => json_encode($ocrData),
            ':id' => $importId
        ]);
    }
    
    /**
     * Get import preview
     */
    public function getImportPreview($importId)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM cns_imports WHERE id = ?
        ");
        
        $stmt->execute([$importId]);
        $import = $stmt->fetch();
        
        if (!$import) {
            return null;
        }
        
        switch ($import['file_type']) {
            case 'pdf':
                return $this->getOcrData($importId);
                
            case 'excel':
                return $this->previewExcel($import['file_path']);
                
            case 'csv':
                return $this->previewCsv($import['file_path']);
                
            case 'xml':
                return $this->previewXml($import['file_path']);
                
            default:
                return null;
        }
    }
    
    /**
     * Process import
     */
    public function processImport($import)
    {
        try {
            $data = null;
            
            switch ($import['file_type']) {
                case 'pdf':
                    $data = $this->processPdfImport($import);
                    break;
                    
                case 'excel':
                    $data = $this->processExcelImport($import['file_path']);
                    break;
                    
                case 'csv':
                    $data = $this->processCsvImport($import['file_path']);
                    break;
                    
                case 'xml':
                    $data = $this->processXmlImport($import['file_path']);
                    break;
                    
                default:
                    throw new Exception('Unsupported file type');
            }
            
            if (!$data) {
                throw new Exception('No data extracted from file');
            }
            
            // Validate extracted data
            $this->validateImportData($data);
            
            return [
                'success' => true,
                'data' => $data,
                'summary' => [
                    'processed' => 1,
                    'cns_total' => $data['cns_amount'],
                    'patient_total' => $data['patient_amount']
                ]
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process PDF import (from OCR data)
     */
    private function processPdfImport($import)
    {
        if ($import['ocr_status'] !== 'completed') {
            throw new Exception('OCR processing not completed');
        }
        
        $ocrData = json_decode($import['ocr_result'], true);
        
        if (!$ocrData) {
            throw new Exception('No OCR data available');
        }
        
        // Extract fields
        if (isset($ocrData['extracted_fields'])) {
            return $ocrData['extracted_fields'];
        }
        
        return $this->extractFieldsFromOcr($ocrData);
    }
    
    /**
     * Process Excel import
     */
    private function processExcelImport($filePath)
    {
        // In production, use PHPSpreadsheet
        // For now, simulate Excel processing
        
        return [
            'cns_amount' => 5000.00,
            'patient_amount' => 2000.00,
            'practitioner_code' => 'P001',
            'period' => date('m/Y')
        ];
    }
    
    /**
     * Process CSV import
     */
    private function processCsvImport($filePath)
    {
        $data = [];
        $handle = fopen($filePath, 'r');
        
        if (!$handle) {
            throw new Exception('Cannot open CSV file');
        }
        
        // Read header
        $header = fgetcsv($handle);
        
        // Map headers to fields
        $fieldMap = [
            'CNS Amount' => 'cns_amount',
            'Patient Amount' => 'patient_amount',
            'Practitioner Code' => 'practitioner_code',
            'Period' => 'period'
        ];
        
        // Read data row
        $row = fgetcsv($handle);
        if ($row) {
            foreach ($header as $index => $column) {
                if (isset($fieldMap[$column]) && isset($row[$index])) {
                    $data[$fieldMap[$column]] = $row[$index];
                }
            }
        }
        
        fclose($handle);
        
        // Convert amounts to float
        if (isset($data['cns_amount'])) {
            $data['cns_amount'] = floatval(str_replace(',', '.', $data['cns_amount']));
        }
        if (isset($data['patient_amount'])) {
            $data['patient_amount'] = floatval(str_replace(',', '.', $data['patient_amount']));
        }
        
        return $data;
    }
    
    /**
     * Process XML import
     */
    private function processXmlImport($filePath)
    {
        $xml = simplexml_load_file($filePath);
        
        if (!$xml) {
            throw new Exception('Invalid XML file');
        }
        
        // Extract data based on expected XML structure
        $data = [
            'cns_amount' => (float) $xml->cns_amount,
            'patient_amount' => (float) $xml->patient_amount,
            'practitioner_code' => (string) $xml->practitioner_code,
            'period' => (string) $xml->period
        ];
        
        return $data;
    }
    
    /**
     * Preview Excel file
     */
    private function previewExcel($filePath)
    {
        // In production, use PHPSpreadsheet to read actual data
        return [
            'headers' => ['CNS Amount', 'Patient Amount', 'Practitioner Code', 'Period'],
            'data' => [
                ['5000.00', '2000.00', 'P001', date('m/Y')]
            ],
            'total_rows' => 1
        ];
    }
    
    /**
     * Preview CSV file
     */
    private function previewCsv($filePath)
    {
        $preview = [
            'headers' => [],
            'data' => [],
            'total_rows' => 0
        ];
        
        $handle = fopen($filePath, 'r');
        if (!$handle) {
            return $preview;
        }
        
        // Read header
        $preview['headers'] = fgetcsv($handle);
        
        // Read up to 5 data rows for preview
        $count = 0;
        while (($row = fgetcsv($handle)) !== false && $count < 5) {
            $preview['data'][] = $row;
            $count++;
        }
        
        // Count total rows
        while (fgetcsv($handle) !== false) {
            $count++;
        }
        
        $preview['total_rows'] = $count;
        
        fclose($handle);
        
        return $preview;
    }
    
    /**
     * Preview XML file
     */
    private function previewXml($filePath)
    {
        $xml = simplexml_load_file($filePath);
        
        if (!$xml) {
            return null;
        }
        
        // Convert to array for preview
        $data = json_decode(json_encode($xml), true);
        
        return [
            'structure' => array_keys($data),
            'data' => $data,
            'valid' => true
        ];
    }
    
    /**
     * Simulate OCR extraction
     */
    private function simulateOcrExtraction($filePath)
    {
        // In production, this would use actual OCR
        // For simulation, generate realistic CNS data
        
        $text = "CAISSE NATIONALE DE SANTÉ\n\n";
        $text .= "Décompte mensuel\n";
        $text .= "Période: " . date('m/Y') . "\n\n";
        $text .= "Praticien: Dr. Jean Dupont\n";
        $text .= "Code: P001\n\n";
        $text .= "Montant CNS: 5,234.50 €\n";
        $text .= "Part patient: 2,115.30 €\n";
        $text .= "Total: 7,349.80 €\n";
        
        return [
            'raw_text' => $text,
            'structured_data' => [
                'period' => date('m/Y'),
                'practitioner_name' => 'Dr. Jean Dupont',
                'practitioner_code' => 'P001',
                'amounts' => [
                    'cns' => '5,234.50',
                    'patient' => '2,115.30',
                    'total' => '7,349.80'
                ]
            ]
        ];
    }
    
    /**
     * Extract fields from OCR data
     */
    private function extractFieldsFromOcr($ocrData)
    {
        $fields = [
            'cns_amount' => 0,
            'patient_amount' => 0,
            'practitioner_code' => '',
            'period' => ''
        ];
        
        // Extract from structured data
        if (isset($ocrData['structured_data'])) {
            $structured = $ocrData['structured_data'];
            
            if (isset($structured['amounts']['cns'])) {
                $fields['cns_amount'] = $this->parseAmount($structured['amounts']['cns']);
            }
            
            if (isset($structured['amounts']['patient'])) {
                $fields['patient_amount'] = $this->parseAmount($structured['amounts']['patient']);
            }
            
            if (isset($structured['practitioner_code'])) {
                $fields['practitioner_code'] = $structured['practitioner_code'];
            }
            
            if (isset($structured['period'])) {
                $fields['period'] = $structured['period'];
            }
        }
        
        // Extract from raw text using regex patterns
        elseif (isset($ocrData['raw_text'])) {
            $text = $ocrData['raw_text'];
            
            // Extract CNS amount
            if (preg_match('/CNS\s*:?\s*([\d\s,\.]+)\s*€?/i', $text, $matches)) {
                $fields['cns_amount'] = $this->parseAmount($matches[1]);
            }
            
            // Extract patient amount
            if (preg_match('/patient\s*:?\s*([\d\s,\.]+)\s*€?/i', $text, $matches)) {
                $fields['patient_amount'] = $this->parseAmount($matches[1]);
            }
            
            // Extract practitioner code
            if (preg_match('/code\s*:?\s*([A-Z0-9]+)/i', $text, $matches)) {
                $fields['practitioner_code'] = $matches[1];
            }
            
            // Extract period
            if (preg_match('/période\s*:?\s*(\d{1,2}\/\d{4})/i', $text, $matches)) {
                $fields['period'] = $matches[1];
            }
        }
        
        return $fields;
    }
    
    /**
     * Parse amount string to float
     */
    private function parseAmount($amountString)
    {
        // Remove spaces and convert comma to dot
        $amount = str_replace(' ', '', $amountString);
        $amount = str_replace(',', '.', $amount);
        
        // Remove any non-numeric characters except dot
        $amount = preg_replace('/[^0-9.]/', '', $amount);
        
        return floatval($amount);
    }
    
    /**
     * Validate import data
     */
    private function validateImportData($data)
    {
        if (!isset($data['cns_amount']) || !is_numeric($data['cns_amount'])) {
            throw new Exception('Invalid CNS amount');
        }
        
        if (!isset($data['patient_amount']) || !is_numeric($data['patient_amount'])) {
            throw new Exception('Invalid patient amount');
        }
        
        if ($data['cns_amount'] < 0 || $data['patient_amount'] < 0) {
            throw new Exception('Amounts cannot be negative');
        }
        
        if ($data['cns_amount'] == 0 && $data['patient_amount'] == 0) {
            throw new Exception('Both amounts cannot be zero');
        }
    }
}