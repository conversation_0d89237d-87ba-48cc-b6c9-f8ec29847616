<?php

namespace App\Models;

use PDO;
use Flight;

class GroupPermission
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Get all permissions for a group
     * OLD schema compatibility - using permissions table
     */
    public function getGroupPermissions($groupId)
    {
        // Get all available permissions and check which ones are granted to the group
        $stmt = $this->db->prepare("
            SELECT 
                p.id as permission_id,
                p.category as module_code,
                p.category as module_name,
                NULL as parent_id,
                p.code as permission_code,
                p.name as permission_name,
                p.description,
                CASE WHEN gp.permission_id IS NOT NULL THEN 1 ELSE 0 END as is_granted,
                gp.granted_by,
                gp.granted_at
            FROM permissions p
            LEFT JOIN group_permissions gp ON 
                p.id = gp.permission_id 
                AND gp.group_id = ?
            ORDER BY p.category, p.code
        ");
        
        $stmt->execute([$groupId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get permissions organized by module
     * OLD schema compatibility
     */
    public function getGroupPermissionsByModule($groupId)
    {
        $permissions = $this->getGroupPermissions($groupId);
        $organized = [];
        
        foreach ($permissions as $perm) {
            $moduleCode = $perm['module_code'];
            if (!isset($organized[$moduleCode])) {
                $organized[$moduleCode] = [
                    'module_id' => $moduleCode, // Use module code as ID in OLD schema
                    'module_code' => $moduleCode,
                    'module_name' => $perm['module_name'],
                    'parent_id' => $perm['parent_id'],
                    'permissions' => []
                ];
            }
            
            $organized[$moduleCode]['permissions'][] = [
                'permission_id' => $perm['permission_id'],
                'permission_code' => $perm['permission_code'],
                'permission_name' => $perm['permission_name'],
                'description' => $perm['description'],
                'is_granted' => (bool)$perm['is_granted'],
                'granted_by' => $perm['granted_by'],
                'granted_at' => $perm['granted_at']
            ];
        }
        
        return array_values($organized);
    }
    
    /**
     * Update group permission
     * OLD schema compatibility - uses permission_id instead of module_id/permission_code
     */
    public function updatePermission($groupId, $moduleId, $permissionCode, $isGranted, $grantedBy)
    {
        // OLD schema: First get the permission_id from the permissions table
        // The permission code in OLD schema is the full code (e.g., 'invoices.view')
        $stmt = $this->db->prepare("SELECT id FROM permissions WHERE code = ?");
        $stmt->execute([$permissionCode]);
        $permission = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$permission) {
            return false; // Permission doesn't exist
        }
        
        $permissionId = $permission['id'];
        
        if ($isGranted) {
            // Grant permission - insert or update
            $stmt = $this->db->prepare("
                INSERT INTO group_permissions (group_id, permission_id, granted_by, granted_at)
                VALUES (?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE granted_by = VALUES(granted_by), granted_at = VALUES(granted_at)
            ");
            return $stmt->execute([$groupId, $permissionId, $grantedBy]);
        } else {
            // Revoke permission - delete the record
            $stmt = $this->db->prepare("
                DELETE FROM group_permissions 
                WHERE group_id = ? AND permission_id = ?
            ");
            return $stmt->execute([$groupId, $permissionId]);
        }
    }
    
    /**
     * Update multiple permissions at once
     */
    public function updateMultiplePermissions($groupId, $permissions, $grantedBy)
    {
        $this->db->beginTransaction();
        
        try {
            foreach ($permissions as $perm) {
                $this->updatePermission(
                    $groupId,
                    $perm['module_id'],
                    $perm['permission_code'],
                    $perm['is_granted'],
                    $grantedBy
                );
                
                // Log the change
                $this->logPermissionChange(
                    $groupId,
                    $perm['module_id'],
                    $perm['permission_code'],
                    $perm['is_granted'] ? 'granted' : 'revoked',
                    $grantedBy
                );
            }
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Apply permission template to a group
     */
    public function applyTemplate($groupId, $templateId, $appliedBy)
    {
        // Get template data
        $stmt = $this->db->prepare("SELECT template_data FROM permission_templates WHERE id = ?");
        $stmt->execute([$templateId]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            return false;
        }
        
        $templateData = json_decode($template['template_data'], true);
        
        $this->db->beginTransaction();
        
        try {
            // Clear existing permissions
            $stmt = $this->db->prepare("DELETE FROM group_permissions WHERE group_id = ?");
            $stmt->execute([$groupId]);
            
            // Apply new permissions based on template - OLD schema compatibility
            if (isset($templateData['all_permissions']) && $templateData['all_permissions']) {
                // Grant all permissions
                $stmt = $this->db->prepare("
                    INSERT INTO group_permissions (group_id, permission_id, granted_by, granted_at)
                    SELECT ?, id, ?, NOW()
                    FROM permissions
                ");
                $stmt->execute([$groupId, $appliedBy]);
            } elseif (isset($templateData['modules'])) {
                // Apply specific module permissions
                foreach ($templateData['modules'] as $moduleCode => $permissionActions) {
                    foreach ($permissionActions as $action) {
                        // Build full permission code (module.action format)
                        $fullPermissionCode = $moduleCode . '.' . $action;
                        $this->updatePermission($groupId, null, $fullPermissionCode, true, $appliedBy);
                    }
                }
            }
            
            $this->db->commit();
            return true;
        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Log permission change
     * Note: permission_audit_log table may not exist in OLD schema
     */
    private function logPermissionChange($groupId, $moduleId, $permissionCode, $action, $changedBy)
    {
        // Check if permission_audit_log table exists
        $stmt = $this->db->prepare("SHOW TABLES LIKE 'permission_audit_log'");
        $stmt->execute();
        if (!$stmt->fetch()) {
            // Table doesn't exist in OLD schema, skip logging
            return true;
        }
        
        // If table exists, log the change
        // Extract module from permission code for OLD schema compatibility
        $parts = explode('.', $permissionCode);
        $moduleCode = $parts[0] ?? 'unknown';
        
        $stmt = $this->db->prepare("
            INSERT INTO permission_audit_log 
            (group_id, module_id, permission_code, action, changed_by, changed_at, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, NOW(), ?, ?)
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'CLI';
        
        return $stmt->execute([
            $groupId,
            0, // Use 0 as module_id since OLD schema doesn't have system_modules
            $permissionCode,
            $action,
            $changedBy,
            $ipAddress,
            $userAgent
        ]);
    }
    
    /**
     * Get permission audit log
     */
    public function getAuditLog($filters = [], $limit = 50)
    {
        $sql = "
            SELECT 
                pal.*,
                ug.name as group_name,
                sm.name as module_name,
                u.username as changed_by_username
            FROM permission_audit_log pal
            JOIN user_groups ug ON pal.group_id = ug.id
            JOIN system_modules sm ON pal.module_id = sm.id
            JOIN users u ON pal.changed_by = u.id
            WHERE 1=1
        ";
        
        $params = [];
        
        if (!empty($filters['group_id'])) {
            $sql .= " AND pal.group_id = ?";
            $params[] = $filters['group_id'];
        }
        
        if (!empty($filters['module_id'])) {
            $sql .= " AND pal.module_id = ?";
            $params[] = $filters['module_id'];
        }
        
        if (!empty($filters['changed_by'])) {
            $sql .= " AND pal.changed_by = ?";
            $params[] = $filters['changed_by'];
        }
        
        $sql .= " ORDER BY pal.changed_at DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}