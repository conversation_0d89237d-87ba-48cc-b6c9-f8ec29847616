<?php

namespace Tests\Unit;

use PHPUnit\Framework\TestCase;
use App\Controllers\ProductController;
use App\Models\CatalogItem;
use App\Models\CatalogCategory;
use Flight;

class InlineProductCreationTest extends TestCase
{
    protected $controller;
    protected $db;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock database connection
        $this->db = $this->createMock(\PDO::class);
        Flight::register('db', function() {
            return $this->db;
        });
        
        // Mock session
        $_SESSION = [
            'user_id' => 1,
            'csrf_token' => 'test_token'
        ];
        
        // Create controller instance
        $this->controller = new ProductController();
    }
    
    protected function tearDown(): void
    {
        parent::tearDown();
        $_SESSION = [];
        Flight::clear();
    }
    
    /**
     * Test CSRF protection
     */
    public function testCsrfProtection()
    {
        // Mock request without CSRF token
        $_POST = [
            'name' => 'Test Product',
            'unit_price' => '10.00'
        ];
        
        // Mock Flight response
        $response = [];
        Flight::map('json', function($data, $code = 200) use (&$response) {
            $response = ['data' => $data, 'code' => $code];
        });
        
        // Call quickCreate without CSRF token
        $this->controller->quickCreate();
        
        // Assert CSRF error response
        $this->assertEquals(403, $response['code']);
        $this->assertFalse($response['data']['success']);
        $this->assertEquals('CSRF_INVALID', $response['data']['code']);
    }
    
    /**
     * Test rate limiting
     */
    public function testRateLimiting()
    {
        // Mock session with rate limit data
        $_SESSION['rate_limits'] = [
            'product_quick_create' => [
                'count' => 11, // Exceeds limit of 10
                'window_start' => time() - 100 // Within 5 minute window
            ]
        ];
        
        $_POST = [
            'csrf_token' => 'test_token',
            'name' => 'Test Product',
            'unit_price' => '10.00'
        ];
        
        // Mock Flight response
        $response = [];
        Flight::map('json', function($data, $code = 200) use (&$response) {
            $response = ['data' => $data, 'code' => $code];
        });
        
        // Call quickCreate
        $this->controller->quickCreate();
        
        // Assert rate limit error
        $this->assertEquals(429, $response['code']);
        $this->assertFalse($response['data']['success']);
        $this->assertEquals('RATE_LIMIT_EXCEEDED', $response['data']['code']);
    }
    
    /**
     * Test input sanitization
     */
    public function testInputSanitization()
    {
        // Test XSS attempt in product name
        $maliciousName = '<script>alert("XSS")</script>Product';
        $maliciousCode = 'PROD<script>alert("XSS")</script>';
        
        // Use reflection to test protected methods
        $reflection = new \ReflectionClass($this->controller);
        
        $sanitizeNameMethod = $reflection->getMethod('sanitizeProductName');
        $sanitizeNameMethod->setAccessible(true);
        
        $sanitizeCodeMethod = $reflection->getMethod('sanitizeProductCode');
        $sanitizeCodeMethod->setAccessible(true);
        
        // Test sanitization
        $cleanName = $sanitizeNameMethod->invoke($this->controller, $maliciousName);
        $cleanCode = $sanitizeCodeMethod->invoke($this->controller, $maliciousCode);
        
        // Assert malicious content removed
        $this->assertStringNotContainsString('<script>', $cleanName);
        $this->assertStringNotContainsString('<script>', $cleanCode);
        $this->assertEquals('Product', $cleanName);
        $this->assertEquals('PROD', $cleanCode);
    }
    
    /**
     * Test price validation
     */
    public function testPriceValidation()
    {
        $reflection = new \ReflectionClass($this->controller);
        $validatePriceMethod = $reflection->getMethod('validatePrice');
        $validatePriceMethod->setAccessible(true);
        
        // Test valid prices
        $this->assertTrue($validatePriceMethod->invoke($this->controller, '10.00'));
        $this->assertTrue($validatePriceMethod->invoke($this->controller, '0'));
        $this->assertTrue($validatePriceMethod->invoke($this->controller, '999999.99'));
        
        // Test invalid prices
        $this->assertFalse($validatePriceMethod->invoke($this->controller, '-10'));
        $this->assertFalse($validatePriceMethod->invoke($this->controller, 'abc'));
        $this->assertFalse($validatePriceMethod->invoke($this->controller, '10.999')); // Too many decimals
        $this->assertFalse($validatePriceMethod->invoke($this->controller, '10000000')); // Exceeds max
    }
    
    /**
     * Test duplicate product name detection
     */
    public function testDuplicateNameDetection()
    {
        // Mock database query for existing product
        $stmt = $this->createMock(\PDOStatement::class);
        $stmt->method('execute')->willReturn(true);
        $stmt->method('fetchColumn')->willReturn(1); // Product exists
        
        $this->db->method('prepare')->willReturn($stmt);
        
        // Mock Flight response
        $response = [];
        Flight::map('json', function($data) use (&$response) {
            $response = $data;
        });
        
        // Test checkName endpoint
        $_GET['name'] = 'Existing Product';
        $this->controller->checkName();
        
        // Assert product exists response
        $this->assertTrue($response['exists']);
    }
    
    /**
     * Test successful product creation
     */
    public function testSuccessfulProductCreation()
    {
        // Mock category check
        $categoryStmt = $this->createMock(\PDOStatement::class);
        $categoryStmt->method('execute')->willReturn(true);
        $categoryStmt->method('fetch')->willReturn(['id' => 1]);
        
        // Mock duplicate check (no duplicate)
        $duplicateStmt = $this->createMock(\PDOStatement::class);
        $duplicateStmt->method('execute')->willReturn(true);
        $duplicateStmt->method('fetchColumn')->willReturn(0);
        
        // Mock product creation
        $this->db->method('lastInsertId')->willReturn('123');
        $this->db->method('beginTransaction')->willReturn(true);
        $this->db->method('commit')->willReturn(true);
        
        // Configure prepare method to return different stmts based on query
        $this->db->method('prepare')->willReturnCallback(function($query) use ($categoryStmt, $duplicateStmt) {
            if (strpos($query, 'is_misc_category') !== false) {
                return $categoryStmt;
            } elseif (strpos($query, 'COUNT(*)') !== false) {
                return $duplicateStmt;
            }
            // Return generic stmt for other queries
            $stmt = $this->createMock(\PDOStatement::class);
            $stmt->method('execute')->willReturn(true);
            return $stmt;
        });
        
        // Mock CatalogItem find method
        $mockProduct = $this->createMock(CatalogItem::class);
        $mockProduct->id = 123;
        $mockProduct->name = 'Test Product';
        $mockProduct->code = 'TEST001';
        $mockProduct->unit_price = 10.00;
        $mockProduct->vat_rate = 17;
        
        // Mock static method (requires runkit or similar in real tests)
        // For this example, we'll skip the actual static method mocking
        
        // Set request data
        $_POST = [
            'csrf_token' => 'test_token',
            'name' => 'Test Product',
            'unit_price' => '10.00',
            'vat_rate' => '17',
            'description' => 'Test description'
        ];
        
        // Mock Flight response
        $response = [];
        Flight::map('json', function($data) use (&$response) {
            $response = $data;
        });
        
        // Note: In a real test, you'd need to properly mock the static CatalogItem::find method
        // This is a simplified version showing the test structure
    }
    
    /**
     * Test code generation
     */
    public function testCodeGeneration()
    {
        // Mock sequence query
        $stmt = $this->createMock(\PDOStatement::class);
        $stmt->method('execute')->willReturn(true);
        $stmt->method('fetchColumn')->willReturn(5); // Next sequence = 5
        
        $this->db->method('prepare')->willReturn($stmt);
        
        // Mock Flight response
        $response = [];
        Flight::map('json', function($data) use (&$response) {
            $response = $data;
        });
        
        // Call generateCode
        $this->controller->generateCode();
        
        // Assert response
        $this->assertTrue($response['success']);
        $this->assertEquals('DIV-0005', $response['code']);
    }
    
    /**
     * Test SQL injection prevention
     */
    public function testSqlInjectionPrevention()
    {
        $maliciousInput = "'; DROP TABLE catalog_items; --";
        
        // Use reflection to test sanitization
        $reflection = new \ReflectionClass($this->controller);
        $sanitizeMethod = $reflection->getMethod('sanitizeProductName');
        $sanitizeMethod->setAccessible(true);
        
        $sanitized = $sanitizeMethod->invoke($this->controller, $maliciousInput);
        
        // Assert SQL injection attempt is neutralized
        $this->assertStringNotContainsString('DROP TABLE', $sanitized);
        $this->assertStringNotContainsString(';', $sanitized);
        $this->assertStringNotContainsString('--', $sanitized);
    }
    
    /**
     * Test permission check
     */
    public function testPermissionCheck()
    {
        // Mock user without permission
        unset($_SESSION['user_id']);
        
        $_POST = [
            'csrf_token' => 'test_token',
            'name' => 'Test Product',
            'unit_price' => '10.00'
        ];
        
        // Mock Flight response
        $response = [];
        Flight::map('json', function($data, $code = 200) use (&$response) {
            $response = ['data' => $data, 'code' => $code];
        });
        
        // Call quickCreate
        $this->controller->quickCreate();
        
        // Assert permission denied
        $this->assertEquals(403, $response['code']);
        $this->assertFalse($response['data']['success']);
        $this->assertEquals('PERMISSION_DENIED', $response['data']['code']);
    }
}