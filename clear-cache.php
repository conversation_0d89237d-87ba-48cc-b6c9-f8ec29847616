<?php
/**
 * Clear Cache Script
 * Clears all application caches including Twig templates and data cache
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

echo "=== Fit360 AdminDesk Cache Clear ===\n\n";

// Clear Twig cache
$twigCachePath = __DIR__ . '/storage/cache/twig';
if (is_dir($twigCachePath)) {
    echo "Clearing Twig template cache...\n";
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($twigCachePath, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    
    $count = 0;
    foreach ($files as $fileinfo) {
        $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
        if ($todo($fileinfo->getRealPath())) {
            $count++;
        }
    }
    echo "  Removed $count cache files/directories\n";
} else {
    echo "  Twig cache directory not found\n";
}

// Clear data cache
$dataCachePath = __DIR__ . '/storage/cache/data';
if (is_dir($dataCachePath)) {
    echo "\nClearing data cache...\n";
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($dataCachePath, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    
    $count = 0;
    foreach ($files as $fileinfo) {
        $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
        if ($todo($fileinfo->getRealPath())) {
            $count++;
        }
    }
    echo "  Removed $count cache files/directories\n";
} else {
    echo "  Data cache directory not found\n";
}

// Clear PHP opcache if available
if (function_exists('opcache_reset')) {
    echo "\nClearing PHP opcache...\n";
    if (opcache_reset()) {
        echo "  Opcache cleared successfully\n";
    } else {
        echo "  Failed to clear opcache\n";
    }
} else {
    echo "\nOpcache not available\n";
}

// Clear session data (optional)
$sessionPath = session_save_path();
if ($sessionPath && is_writable($sessionPath)) {
    echo "\nSession save path: $sessionPath\n";
    echo "Note: Session files not cleared (active sessions preserved)\n";
}

echo "\n=== Cache clear completed ===\n";
echo "\nPlease refresh your browser to see changes.\n";