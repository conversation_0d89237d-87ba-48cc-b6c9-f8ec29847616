<?php
/**
 * Clear cache script
 */

// Load composer autoloader
require __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Initialize Flight
require __DIR__ . '/app/config/bootstrap.php';

try {
    $cache = Flight::cache();
    
    // Clear VAT rates cache
    echo "Clearing VAT rates cache...\n";
    $cache->delete('vat_rates_active');
    
    // Clear invoice types cache
    echo "Clearing invoice types cache...\n";
    $cache->delete('invoice_types_active');
    
    // Clear document types cache
    echo "Clearing document types cache...\n";
    $cache->delete('document_types_active');
    
    // Clear all cache if possible
    if (method_exists($cache, 'clear')) {
        echo "Clearing all cache...\n";
        $cache->clear();
    }
    
    echo "\n✅ Cache cleared successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error clearing cache: " . $e->getMessage() . "\n";
}