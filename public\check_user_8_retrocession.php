<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $userId = 8; // User ID from the URL you provided
    
    // Get user info
    $stmt = $pdo->prepare("
        SELECT u.*, GROUP_CONCAT(g.name) as user_groups
        FROM users u
        LEFT JOIN user_group_assignments uga ON u.id = uga.user_id
        LEFT JOIN user_groups g ON uga.group_id = g.id
        WHERE u.id = ?
        GROUP BY u.id
    ");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<h2>User Information</h2>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><td><strong>ID:</strong></td><td>" . $user['id'] . "</td></tr>";
    echo "<tr><td><strong>Name:</strong></td><td>" . $user['first_name'] . " " . $user['last_name'] . "</td></tr>";
    echo "<tr><td><strong>Email:</strong></td><td>" . $user['email'] . "</td></tr>";
    echo "<tr><td><strong>Groups:</strong></td><td>" . ($user['user_groups'] ?? 'None') . "</td></tr>";
    echo "</table>";
    
    // Get all retrocession settings
    $stmt = $pdo->prepare("
        SELECT urs.*
        FROM user_retrocession_settings urs
        WHERE urs.user_id = ?
        ORDER BY urs.valid_from DESC, urs.is_active DESC
    ");
    $stmt->execute([$userId]);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Retrocession Settings</h2>";
    
    if (empty($settings)) {
        echo "<p>No retrocession settings found for this user.</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr>";
        echo "<th>ID</th>";
        echo "<th>Status</th>";
        echo "<th>Notes</th>";
        echo "<th>Valid From</th>";
        echo "<th>Valid To</th>";
        echo "<th>CNS</th>";
        echo "<th>Patient</th>";
        echo "<th>Secretary</th>";
        echo "<th>Actions</th>";
        echo "</tr>";
        
        foreach ($settings as $setting) {
            $status = $setting['is_active'] ? '<span style="color: green;">ACTIVE</span>' : '<span style="color: gray;">INACTIVE</span>';
            $validFrom = date('d/m/Y', strtotime($setting['valid_from']));
            $validTo = $setting['valid_to'] ? date('d/m/Y', strtotime($setting['valid_to'])) : 'No end date';
            
            // Check for overlaps
            $hasOverlap = false;
            if ($setting['is_active']) {
                $overlapStmt = $pdo->prepare("
                    SELECT COUNT(*) as overlap_count
                    FROM user_retrocession_settings
                    WHERE user_id = ?
                    AND id != ?
                    AND is_active = 1
                    AND (
                        (valid_from <= ? AND (valid_to IS NULL OR valid_to >= ?))
                        OR (valid_from <= ? AND (valid_to IS NULL OR valid_to >= ?))
                        OR (? <= valid_from AND (? IS NULL OR ? >= valid_to))
                    )
                ");
                
                $overlapStmt->execute([
                    $userId,
                    $setting['id'],
                    $setting['valid_from'], $setting['valid_from'],
                    $setting['valid_to'], $setting['valid_to'],
                    $setting['valid_from'], $setting['valid_to'], $setting['valid_to']
                ]);
                
                $overlaps = $overlapStmt->fetch(PDO::FETCH_ASSOC);
                $hasOverlap = $overlaps['overlap_count'] > 0;
            }
            
            echo "<tr" . ($hasOverlap ? " style='background-color: #ffcccc;'" : "") . ">";
            echo "<td>" . $setting['id'] . "</td>";
            echo "<td>" . $status . "</td>";
            echo "<td>" . ($setting['notes'] ?? '-') . "</td>";
            echo "<td>" . $validFrom . "</td>";
            echo "<td>" . $validTo . "</td>";
            echo "<td>" . $setting['cns_value'] . ($setting['cns_type'] == 'percentage' ? '%' : '€') . "</td>";
            echo "<td>" . $setting['patient_value'] . ($setting['patient_type'] == 'percentage' ? '%' : '€') . "</td>";
            echo "<td>" . $setting['secretary_value'] . ($setting['secretary_type'] == 'percentage' ? '%' : '€') . "</td>";
            echo "<td>";
            
            if ($hasOverlap) {
                echo "⚠️ <strong>OVERLAP!</strong><br>";
            }
            
            if ($setting['is_active']) {
                echo "<a href='fix_user_8_retrocession.php?action=end&date=" . date('Y-m-d', strtotime('-1 day')) . "'>End Yesterday</a> | ";
                echo "<a href='fix_user_8_retrocession.php?action=disable&id=" . $setting['id'] . "'>Disable</a>";
            }
            
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        if ($hasOverlap) {
            echo "<p style='color: red;'><strong>⚠️ WARNING:</strong> There are overlapping date ranges in the active settings. This is why you're getting the error when trying to add a new course.</p>";
        }
    }
    
    // Get user courses
    $stmt = $pdo->prepare("
        SELECT * FROM user_courses 
        WHERE user_id = ? 
        ORDER BY display_order, id
    ");
    $stmt->execute([$userId]);
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Current Courses</h2>";
    
    if (empty($courses)) {
        echo "<p>No courses found for this user.</p>";
    } else {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Course Name</th><th>Hourly Rate</th><th>VAT Rate</th><th>Order</th></tr>";
        
        foreach ($courses as $course) {
            echo "<tr>";
            echo "<td>" . $course['id'] . "</td>";
            echo "<td>" . $course['course_name'] . "</td>";
            echo "<td>" . $course['hourly_rate'] . " €</td>";
            echo "<td>" . $course['vat_rate'] . " %</td>";
            echo "<td>" . $course['display_order'] . "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
    echo "<h2>Solutions</h2>";
    echo "<ol>";
    echo "<li><strong>Option 1:</strong> End the current retrocession setting before adding the new course</li>";
    echo "<li><strong>Option 2:</strong> Disable overlapping retrocession settings</li>";
    echo "<li><strong>Option 3:</strong> Choose different validity dates that don't overlap</li>";
    echo "</ol>";
    
    echo "<p><a href='/fit/public/users/8/edit'>Back to Edit User</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}