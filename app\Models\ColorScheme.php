<?php

namespace App\Models;

use App\Core\Model;
use PDO;

class ColorScheme extends Model
{
    protected $table = 'color_schemes';
    
    protected $fillable = [
        'name',
        'code',
        'description',
        'colors',
        'is_system',
        'is_active',
        'is_default',
        'preview_image',
        'created_by'
    ];
    
    protected $casts = [
        'name' => 'json',
        'description' => 'json',
        'colors' => 'json',
        'is_system' => 'boolean',
        'is_active' => 'boolean',
        'is_default' => 'boolean'
    ];
    
    /**
     * Get the active color scheme
     */
    public static function getActive()
    {
        try {
            $db = self::db();
            
            // First try to get active scheme from config
            $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'active_color_scheme' AND category = 'appearance' LIMIT 1");
            $stmt->execute();
            $activeCode = $stmt->fetchColumn();
            
            if ($activeCode) {
                // Get the active scheme
                $stmt = $db->prepare("SELECT * FROM color_schemes WHERE code = :code AND is_active = 1 LIMIT 1");
                $stmt->execute(['code' => $activeCode]);
                $data = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($data) {
                    $instance = new static();
                    $instance->fill($data);
                    return $instance;
                }
            }
            
            // Fall back to default
            $stmt = $db->prepare("SELECT * FROM color_schemes WHERE is_default = 1 AND is_active = 1 LIMIT 1");
            $stmt->execute();
            $data = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($data) {
                $instance = new static();
                $instance->fill($data);
                return $instance;
            }
        } catch (\Exception $e) {
            // Log error but don't break the application
            error_log('ColorScheme::getActive error: ' . $e->getMessage());
        }
        
        return null;
    }
    
    /**
     * Get color value by key
     */
    public function getColor($key)
    {
        $colors = $this->__get('colors');
        return isset($colors[$key]) ? $colors[$key] : null;
    }
    
    /**
     * Get all colors as CSS variables
     */
    public function getCssVariables()
    {
        $colors = $this->__get('colors');
        if (!$colors) {
            return '';
        }
        
        $css = '';
        
        // Map internal color names to CSS variable names
        $colorMap = [
            'primary' => '--bs-primary',
            'primary_rgb' => '--bs-primary-rgb',
            'secondary' => '--bs-secondary',
            'secondary_rgb' => '--bs-secondary-rgb',
            'success' => '--bs-success',
            'success_rgb' => '--bs-success-rgb',
            'info' => '--bs-info',
            'info_rgb' => '--bs-info-rgb',
            'warning' => '--bs-warning',
            'warning_rgb' => '--bs-warning-rgb',
            'danger' => '--bs-danger',
            'danger_rgb' => '--bs-danger-rgb',
            'light' => '--bs-light',
            'light_rgb' => '--bs-light-rgb',
            'dark' => '--bs-dark',
            'dark_rgb' => '--bs-dark-rgb',
            'sidebar_bg' => '--sidebar-bg',
            'sidebar_text' => '--sidebar-text',
            'sidebar_hover' => '--sidebar-hover',
            'sidebar_active' => '--sidebar-active',
            'navbar_bg' => '--navbar-bg',
            'navbar_text' => '--navbar-text',
            'card_bg' => '--bs-card-bg',
            'body_bg' => '--bs-body-bg',
            'text_primary' => '--bs-body-color',
            'text_secondary' => '--bs-secondary-color',
            'border_color' => '--bs-border-color',
            'shadow_color' => '--shadow-color'
        ];
        
        // First, add all color values
        foreach ($colorMap as $key => $cssVar) {
            if (isset($colors[$key])) {
                $css .= "    {$cssVar}: {$colors[$key]};\n";
                
                // Auto-generate RGB values for main colors if not provided
                if (!strpos($key, '_rgb') && !strpos($key, 'shadow') && in_array($key, ['primary', 'secondary', 'success', 'info', 'warning', 'danger', 'light', 'dark'])) {
                    $rgbKey = $key . '_rgb';
                    if (!isset($colors[$rgbKey]) && isset($colorMap[$rgbKey])) {
                        // Generate RGB from hex
                        $rgb = self::hexToRgb($colors[$key]);
                        $css .= "    {$colorMap[$rgbKey]}: {$rgb};\n";
                    }
                }
            }
        }
        
        return $css;
    }
    
    /**
     * Get localized name
     */
    public function getName($locale = null)
    {
        if (!$locale) {
            $locale = $_SESSION['lang'] ?? 'en';
        }
        
        $names = $this->__get('name');
        return isset($names[$locale]) ? $names[$locale] : ($names['en'] ?? $this->__get('code'));
    }
    
    /**
     * Get localized description
     */
    public function getDescription($locale = null)
    {
        if (!$locale) {
            $locale = $_SESSION['lang'] ?? 'en';
        }
        
        $descriptions = $this->__get('description');
        return isset($descriptions[$locale]) ? $descriptions[$locale] : ($descriptions['en'] ?? '');
    }
    
    /**
     * Convert hex to RGB
     */
    public static function hexToRgb($hex)
    {
        $hex = ltrim($hex, '#');
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        return "$r, $g, $b";
    }
}