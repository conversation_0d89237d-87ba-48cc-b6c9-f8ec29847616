# Bulk Loyer (Rent) Invoice Generation Feature

## Overview
The Bulk Loyer feature allows administrators to generate monthly rental invoices for multiple users at once. This feature is accessible at `/invoices/bulk-loyer`.

## Features
- Generate invoices for all users with financial obligations in one click
- Preview users and amounts before generation
- Filter by month/year and user type (medical staff vs support staff)
- Visual indicators for already generated invoices
- Automatic calculation of VAT amounts
- Progress tracking during bulk generation

## Technical Implementation

### Database Structure
- User financial obligations stored directly in `users` table columns:
  - `rent_amount` - Monthly rent amount
  - `charges_amount` - Additional charges
  - `secretary_tvac_17` - Secretary services including 17% VAT
  - `secretary_htva` - Secretary services without VAT
  - `tva_17` - VAT amount (17%)

### Invoice Generation Logic
1. **Invoice Type**: Uses LOC (Location) type for all rental invoices
2. **Invoice Number Format**: FAC-LOC-YYYY-NNNN (e.g., FAC-LOC-2025-0201)
3. **Line Items**: Created in `invoice_lines` table with appropriate `line_type`
4. **VAT Handling**: 
   - If `secretary_tvac_17` exists, use it (includes VAT)
   - Otherwise, use `secretary_htva` + `tva_17` separately
   - Never use both to avoid double-counting

### Key Files
- **Controller**: `/app/controllers/InvoiceController.php` (bulkLoyerView, bulkGenerateLoyer methods)
- **Service**: `/app/services/MonthlyInvoiceGenerator.php` (generateRentalInvoiceForUser method)
- **View**: `/app/views/invoices/bulk-loyer-modern.twig`
- **Route**: `/app/modules/invoices/routes.php`

### Important Notes
1. **Table Usage**: Invoice line items MUST be created in `invoice_lines` table, NOT `invoice_items`
2. **Generated Columns**: The `vat_amount` column is auto-calculated - never insert values directly
3. **Invoice Creation**: Use `createInvoice()` method, not `create()`
4. **Type System**: Both `invoice_type_id` (new) and `type_id` (old) must be set for proper invoice numbering

## Usage
1. Navigate to Invoices → Bulk Loyer Generation
2. Select month and year
3. Optionally filter by user type
4. Review the list of users and amounts
5. Select users to invoice (or use Select All)
6. Click "Generate Selected"
7. Monitor progress and review results

## Permissions
- Requires invoice creation permissions
- Manager/Admin roles have full access
- Other roles may have restricted access based on group permissions