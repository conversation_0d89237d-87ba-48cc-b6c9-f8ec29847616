<?php

namespace App\Models;

use App\Core\Model;

class EmailTemplate extends Model
{
    protected $table = 'email_templates';
    
    protected $fillable = [
        'name',
        'code',
        'subject',
        'body',
        'variables',
        'is_active'
    ];
    
    protected $casts = [
        'variables' => 'array',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get template by code
     */
    public static function getByCode($code)
    {
        return self::where('code', $code)->where('is_active', true)->first();
    }
    
    /**
     * Parse template with variables
     */
    public function parse($variables = [])
    {
        $subject = $this->subject;
        $body = $this->body;
        
        foreach ($variables as $key => $value) {
            $subject = str_replace('{' . $key . '}', $value, $subject);
            $body = str_replace('{' . $key . '}', $value, $body);
        }
        
        return [
            'subject' => $subject,
            'body' => $body
        ];
    }
    
    /**
     * Get available variables for this template
     */
    public function getAvailableVariables()
    {
        return $this->variables ?: [];
    }
}