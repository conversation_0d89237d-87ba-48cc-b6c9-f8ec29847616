<!DOCTYPE html>
<html lang="{{ session.user_language|default('en') }}" data-bs-theme="light">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}{{ title|default('Dashboard') }}{% endblock %} - {{ app_name|default('Fit360 AdminDesk') }}</title>
    <meta name="author" content="{{ app_name|default('Fit360 AdminDesk') }}">
    <meta name="description" content="{{ app_name|default('Fit360 AdminDesk') }} - Modern Health Center Billing System">
    <link rel="icon" type="image/x-icon" href="{{ base_url }}/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <!-- DataTables -->
    <link href="https://cdn.datatables.net/1.13.8/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css" rel="stylesheet">
    
    <!-- Select2 -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet">
    
    <!-- Tempus Dominus (DateTime Picker) -->
    <link href="https://cdn.jsdelivr.net/npm/@eonasdan/tempus-dominus@6.9.4/dist/css/tempus-dominus.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ base_url }}/assets/css/modern-theme.css">
    
    {% block styles %}{% endblock %}
    
    <!-- Debug Mode - Log all template variables -->
    <script>
        console.log('=== TEMPLATE DEBUG MODE ===');
        console.log('Page loaded at:', new Date().toISOString());
        
        // Log all template variables (safely)
        const templateVars = {
            title: {{ title|default('null')|json_encode|raw }},
            app_name: {{ app_name|default('null')|json_encode|raw }},
            base_url: {{ base_url|default('null')|json_encode|raw }},
            current_route: {{ current_route|default('null')|json_encode|raw }},
            current_template: {{ current_template|default('null')|json_encode|raw }},
            template_name: {{ template_name|default('null')|json_encode|raw }},
            
            // Session data (carefully handled)
            session_user_id: {{ session.user_id|default('null')|json_encode|raw }},
            session_user_name: {{ session.user_name|default('null')|json_encode|raw }},
            session_user_email: {{ session.user_email|default('null')|json_encode|raw }},
            session_user_language: {{ session.user_language|default('null')|json_encode|raw }},
            session_template: {{ session.template|default('null')|json_encode|raw }},
            
            // Check if arrays exist
            has_session: {{ session is defined ? 'true' : 'false' }},
            has_flash: {{ flash is defined ? 'true' : 'false' }},
            has_config: {{ config is defined ? 'true' : 'false' }},
            has_stats: {{ stats is defined ? 'true' : 'false' }},
            
            // Flash messages
            flash_success: {{ flash.success|default('null')|json_encode|raw }},
            flash_error: {{ flash.error|default('null')|json_encode|raw }},
            flash_warning: {{ flash.warning|default('null')|json_encode|raw }},
            flash_info: {{ flash.info|default('null')|json_encode|raw }},
            
            // Config
            config_currency: {{ config.currency_symbol|default('null')|json_encode|raw }},
            config_date_format: {{ config.date_format|default('null')|json_encode|raw }},
            config_time_format: {{ config.time_format|default('null')|json_encode|raw }}
        };
        
        console.log('Template Variables:', templateVars);
        
        // Check for potential issues
        console.log('=== Checking for potential issues ===');
        for (const [key, value] of Object.entries(templateVars)) {
            if (value === null || value === undefined) {
                console.warn(`⚠️ Variable '${key}' is null or undefined`);
            }
            if (Array.isArray(value)) {
                console.warn(`⚠️ Variable '${key}' is an array:`, value);
            }
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                console.warn(`⚠️ Variable '${key}' is an object:`, value);
            }
        }
        
        // Log any JavaScript errors
        window.addEventListener('error', function(e) {
            console.error('❌ JavaScript Error:', {
                message: e.message,
                filename: e.filename,
                line: e.lineno,
                column: e.colno,
                error: e.error
            });
        });
        
        console.log('=== END TEMPLATE DEBUG ===');
    </script>
</head>
<body class="app-body">
    <!-- Loading Overlay -->
    <div id="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- App Container -->
    <div class="app">
        <!-- Top Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light app-header">
            <div class="container-fluid">
                <!-- Sidebar Toggle -->
                <button class="btn btn-link sidebar-toggle d-lg-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#mobile-sidebar">
                    <i class="bi bi-list fs-4"></i>
                </button>
                
                <!-- Brand -->
                <a class="navbar-brand d-lg-none ms-2" href="{{ base_url }}/">
                    {{ app_name|default('Fit360 AdminDesk') }}
                </a>
                
                <!-- Search Form -->
                <div class="navbar-search d-none d-lg-block">
                    <form action="{{ base_url }}/search" method="get">
                        <div class="input-group">
                            <input type="search" name="q" class="form-control" placeholder="{{ __('common.search') }}..." autocomplete="off">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Right Navigation -->
                <ul class="navbar-nav ms-auto">
                    <!-- Theme Toggle -->
                    <li class="nav-item">
                        <button class="btn btn-link text-dark p-2 theme-toggle" id="theme-toggle" title="{{ __('common.toggle_theme') }}">
                            <i class="bi bi-moon-stars fs-5" id="theme-icon"></i>
                        </button>
                    </li>
                    
                    <!-- Notifications -->
                    <li class="nav-item dropdown">
                        <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-bell fs-5"></i>
                            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                3
                                <span class="visually-hidden">unread notifications</span>
                            </span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-lg">
                            <div class="dropdown-header">
                                <h6 class="mb-0">{{ __('common.notifications') }}</h6>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div class="dropdown-body">
                                <!-- Notification items would go here -->
                                <div class="list-group list-group-flush">
                                    <a href="#" class="list-group-item list-group-item-action">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <i class="bi bi-info-circle text-info fs-4"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">New invoice created</h6>
                                                <p class="mb-1 text-muted small">Invoice #2024-001 has been created</p>
                                                <small class="text-muted">5 minutes ago</small>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <div class="dropdown-footer text-center">
                                <a href="{{ base_url }}/notifications" class="text-primary text-decoration-none">
                                    {{ __('common.view_all_notifications') }} <i class="bi bi-arrow-right"></i>
                                </a>
                            </div>
                        </div>
                    </li>
                    
                    <!-- User Menu -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" data-bs-toggle="dropdown">
                            {% if session.user_avatar is defined and session.user_avatar %}
                                <img src="{{ base_url }}/uploads/avatars/{{ session.user_avatar }}" class="user-image" alt="User">
                            {% else %}
                                <div class="user-avatar">
                                    <i class="bi bi-person-circle fs-4"></i>
                                </div>
                            {% endif %}
                            <span class="d-none d-md-inline ms-2">{{ session.user_name|default('User') }}</span>
                        </a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <div class="dropdown-header">
                                <div class="d-flex align-items-center">
                                    {% if session.user_avatar is defined and session.user_avatar %}
                                        <img src="{{ base_url }}/uploads/avatars/{{ session.user_avatar }}" class="rounded-circle me-3" style="width: 48px; height: 48px;" alt="User">
                                    {% else %}
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                                            <i class="bi bi-person fs-5"></i>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <h6 class="mb-0">{{ session.user_name|default('User') }}</h6>
                                        <small class="text-muted">{{ session.user_email|default('') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item" href="{{ base_url }}/profile">
                                <i class="bi bi-person me-2"></i> {{ __('users.my_profile') }}
                            </a>
                            <a class="dropdown-item" href="{{ base_url }}/settings">
                                <i class="bi bi-gear me-2"></i> {{ __('common.settings') }}
                            </a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-danger" href="{{ base_url }}/logout">
                                <i class="bi bi-box-arrow-right me-2"></i> {{ __('auth.logout') }}
                            </a>
                        </div>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- Sidebar -->
        <aside class="app-sidebar">
            <div class="sidebar-content">
                <!-- Brand -->
                <a href="{{ base_url }}/" class="brand-link">
                    <i class="bi bi-heart-pulse-fill brand-icon"></i>
                    <span class="brand-text">{{ app_name|default('Fit360 AdminDesk') }}</span>
                </a>
                
                <!-- Navigation -->
                <nav class="sidebar-nav">
                    <ul class="nav flex-column">
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ base_url }}/" class="nav-link {{ current_route == 'home' ? 'active' : '' }}">
                                <i class="nav-icon bi bi-speedometer2"></i>
                                <p>{{ __('common.dashboard') }}</p>
                            </a>
                        </li>
                        
                        <!-- Patients -->
                        {% if shouldShowMenu('patients') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/patients" class="nav-link {{ current_route starts with 'patients' ? 'active' : '' }} {{ getMenuClass('patients') }}">
                                <i class="nav-icon bi bi-people"></i>
                                <p>{{ __('patients.title') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Clients -->
                        {% if shouldShowMenu('clients') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/clients" class="nav-link {{ current_route starts with 'clients' ? 'active' : '' }} {{ getMenuClass('clients') }}">
                                <i class="nav-icon bi bi-building"></i>
                                <p>{{ __('clients.title') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Invoices -->
                        {% if shouldShowMenu('invoices') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/invoices" class="nav-link {{ current_route starts with 'invoices' ? 'active' : '' }} {{ getMenuClass('invoices') }}">
                                <i class="nav-icon bi bi-file-earmark-text"></i>
                                <p>{{ __('invoices.title') }}</p>
                                <span class="badge bg-danger ms-auto">5</span>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Reports -->
                        {% if shouldShowMenu('reports') %}
                        <li class="nav-item">
                            <a href="{{ base_url }}/reports" class="nav-link {{ current_route starts with 'reports' ? 'active' : '' }}">
                                <i class="nav-icon bi bi-graph-up"></i>
                                <p>{{ __('reports.title') }}</p>
                            </a>
                        </li>
                        {% endif %}
                        
                        <!-- Configuration (with submenu) -->
                        {% if shouldShowMenu('config') %}
                        <li class="nav-item has-treeview {{ current_route starts with 'config' ? 'menu-open' : '' }}">
                            <a href="#" class="nav-link {{ current_route starts with 'config' ? 'active' : '' }}" data-bs-toggle="collapse" data-bs-target="#config-menu">
                                <i class="nav-icon bi bi-gear"></i>
                                <p>
                                    {{ __('config.title') }}
                                    <i class="bi bi-chevron-down ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav collapse {{ current_route starts with 'config' ? 'show' : '' }}" id="config-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/config" class="nav-link {{ current_route == 'config' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('config.general') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/config/company" class="nav-link {{ current_route == 'config.company' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('config.company') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/config/invoice-types" class="nav-link {{ current_route == 'config.invoice-types' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('config.invoice_types') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/config/vat-rates" class="nav-link {{ current_route == 'config.vat-rates' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('config.vat_rates') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Users -->
                        {% if session.user is defined and session.user.is_admin == true %}
                        <li class="nav-item has-treeview {{ current_route starts with 'users' ? 'menu-open' : '' }}">
                            <a href="#" class="nav-link {{ current_route starts with 'users' ? 'active' : '' }}" data-bs-toggle="collapse" data-bs-target="#users-menu">
                                <i class="nav-icon bi bi-people-fill"></i>
                                <p>
                                    {{ __('users.title') }}
                                    <i class="bi bi-chevron-down ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav collapse {{ current_route starts with 'users' ? 'show' : '' }}" id="users-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/users" class="nav-link {{ current_route == 'users' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('users.all_users') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/users/groups" class="nav-link {{ current_route == 'users.groups' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('users.groups') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        
                        <!-- Translations -->
                        <li class="nav-item has-treeview {{ current_route starts with 'translations' ? 'menu-open' : '' }}">
                            <a href="#" class="nav-link {{ current_route starts with 'translations' ? 'active' : '' }}" data-bs-toggle="collapse" data-bs-target="#translations-menu">
                                <i class="nav-icon bi bi-translate"></i>
                                <p>
                                    {{ __('translations.title') }}
                                    <i class="bi bi-chevron-down ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav collapse {{ current_route starts with 'translations' ? 'show' : '' }}" id="translations-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/translations" class="nav-link {{ current_route == 'translations' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('translations.editor') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/translations/diagnostic" class="nav-link {{ current_route == 'translations.diagnostic' ? 'active' : '' }}">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('translations.diagnostic') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                        
                        <!-- Billing & Finance (with submenu) -->
                        {% if shouldShowMenu('billing') %}
                        <li class="nav-item has-treeview {{ current_route starts with 'billing' ? 'menu-open' : '' }}">
                            <a href="#" class="nav-link {{ current_route starts with 'billing' ? 'active' : '' }}" data-bs-toggle="collapse" data-bs-target="#billing-menu">
                                <i class="nav-icon bi bi-calculator"></i>
                                <p>
                                    {{ __('billing.title') }}
                                    <i class="bi bi-chevron-down ms-auto"></i>
                                </p>
                            </a>
                            <ul class="nav collapse {{ current_route starts with 'billing' ? 'show' : '' }}" id="billing-menu">
                                <li class="nav-item">
                                    <a href="{{ base_url }}/billing/wizard" class="nav-link">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('billing.wizard') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/retrocession" class="nav-link">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('billing.retrocession') }}</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ base_url }}/cns-import" class="nav-link">
                                        <i class="bi bi-circle nav-icon"></i>
                                        <p>{{ __('billing.cns_import') }}</p>
                                    </a>
                                </li>
                            </ul>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                
                <!-- Sidebar Footer -->
                <div class="sidebar-footer">
                    <div class="d-flex justify-content-between align-items-center p-3">
                        <small class="text-muted">v2.0.0</small>
                        <div>
                            <a href="{{ base_url }}/help" class="text-muted me-2" title="{{ __('common.help') }}">
                                <i class="bi bi-question-circle"></i>
                            </a>
                            <a href="{{ base_url }}/settings" class="text-muted" title="{{ __('common.settings') }}">
                                <i class="bi bi-gear"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="app-main">
            <div class="content-wrapper">
                {% block content %}{% endblock %}
            </div>
        </main>

        <!-- Footer -->
        <footer class="app-footer text-center py-3 mt-auto">
            <div class="container-fluid">
                <span class="text-muted">&copy; {{ 'now'|date('Y') }} {{ app_name|default('Fit360 AdminDesk') }}. {{ __('common.all_rights_reserved') }}</span>
                <span class="float-end">Version 2.0.0</span>
            </div>
        </footer>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <!-- Off-canvas Sidebar for Mobile -->
    <div class="offcanvas offcanvas-start" tabindex="-1" id="mobile-sidebar">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title">{{ app_name|default('Fit360 AdminDesk') }}</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas"></button>
        </div>
        <div class="offcanvas-body p-0">
            <!-- Mobile menu content will be cloned here -->
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    
    <!-- Custom Scripts -->
    <script>
        // Configure toastr
        toastr.options = {
            "closeButton": true,
            "debug": false,
            "newestOnTop": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "preventDuplicates": false,
            "onclick": null,
            "showDuration": "300",
            "hideDuration": "1000",
            "timeOut": "5000",
            "extendedTimeOut": "1000",
            "showEasing": "swing",
            "hideEasing": "linear",
            "showMethod": "fadeIn",
            "hideMethod": "fadeOut"
        };

        // Remove loading overlay when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 DOMContentLoaded event fired');
            
            setTimeout(function() {
                var overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.classList.add('fade-out');
                    setTimeout(function() {
                        overlay.style.display = 'none';
                        console.log('✅ Loading overlay removed');
                    }, 300);
                }
            }, 100);
        });
        
        // Theme Toggle
        const themeToggle = document.getElementById('theme-toggle');
        const themeIcon = document.getElementById('theme-icon');
        const htmlElement = document.documentElement;
        
        if (themeToggle && themeIcon) {
            const savedTheme = localStorage.getItem('theme') || 'light';
            htmlElement.setAttribute('data-bs-theme', savedTheme);
            updateThemeIcon(savedTheme);
            
            themeToggle.addEventListener('click', function() {
                const currentTheme = htmlElement.getAttribute('data-bs-theme');
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                
                htmlElement.setAttribute('data-bs-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateThemeIcon(newTheme);
                
                console.log('🎨 Theme changed to:', newTheme);
            });
        }
        
        function updateThemeIcon(theme) {
            if (theme === 'dark') {
                themeIcon.classList.remove('bi-moon-stars');
                themeIcon.classList.add('bi-sun-fill');
            } else {
                themeIcon.classList.remove('bi-sun-fill');
                themeIcon.classList.add('bi-moon-stars');
            }
        }
        
        // Clone desktop sidebar to mobile
        document.addEventListener('DOMContentLoaded', function() {
            const desktopNav = document.querySelector('.sidebar-nav');
            const mobileBody = document.querySelector('.offcanvas-body');
            
            if (desktopNav && mobileBody) {
                const clonedNav = desktopNav.cloneNode(true);
                mobileBody.appendChild(clonedNav);
                console.log('📱 Mobile sidebar initialized');
            }
        });
        
        // Show toast function
        function showToast(message, type = 'success') {
            console.log(`🔔 Toast shown: [${type}] ${message}`);
            
            const toastContainer = document.getElementById('toast-container');
            if (!toastContainer) return;
            
            const toastId = 'toast-' + Date.now();
            const toastHtml = `
                <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'danger' ? 'danger' : type === 'warning' ? 'warning' : type === 'info' ? 'info' : 'success'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `;
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            const toastElement = document.getElementById(toastId);
            const toast = new bootstrap.Toast(toastElement, { delay: 5000 });
            toast.show();
            
            toastElement.addEventListener('hidden.bs.toast', function () {
                toastElement.remove();
            });
        }
        
        // Show flash messages
        console.log('🔍 Checking for flash messages...');
        {% if flash.success is defined and flash.success %}
            console.log('✅ Flash success:', {{ flash.success|json_encode|raw }});
            showToast({{ flash.success|json_encode|raw }}, 'success');
        {% endif %}
        {% if flash.error is defined and flash.error %}
            console.log('❌ Flash error:', {{ flash.error|json_encode|raw }});
            showToast({{ flash.error|json_encode|raw }}, 'danger');
        {% endif %}
        {% if flash.warning is defined and flash.warning %}
            console.log('⚠️ Flash warning:', {{ flash.warning|json_encode|raw }});
            showToast({{ flash.warning|json_encode|raw }}, 'warning');
        {% endif %}
        {% if flash.info is defined and flash.info %}
            console.log('ℹ️ Flash info:', {{ flash.info|json_encode|raw }});
            showToast({{ flash.info|json_encode|raw }}, 'info');
        {% endif %}
        
        console.log('✅ Base template scripts loaded successfully');
    </script>
    
    {% block scripts %}{% endblock %}
</body>
</html>