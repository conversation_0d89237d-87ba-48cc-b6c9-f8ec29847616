{% extends "base-modern.twig" %}

{% block title %}
    {% if profile %}
        {{ __('retrocession.edit_rate_profile') }}
    {% else %}
        {{ __('retrocession.create_rate_profile') }}
    {% endif %}
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ base_url }}">{{ __('common.home') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ base_url }}/retrocession">{{ __('retrocession.title') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ base_url }}/retrocession/rate-profiles">{{ __('retrocession.rate_profiles') }}</a></li>
                    <li class="breadcrumb-item active">
                        {% if profile %}
                            {{ __('common.edit') }}
                        {% else %}
                            {{ __('common.create') }}
                        {% endif %}
                    </li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800 mt-2">
                {% if profile %}
                    {{ __('retrocession.edit_rate_profile') }}
                {% else %}
                    {{ __('retrocession.create_rate_profile') }}
                {% endif %}
            </h1>
        </div>
        <div>
            <a href="{{ base_url }}/retrocession/rate-profiles" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
        </div>
    </div>

    <!-- Profile Form -->
    <form id="profileForm" method="post" action="{% if profile %}{{ base_url }}/retrocession/rate-profiles/{{ profile.id }}/update{% else %}{{ base_url }}/retrocession/rate-profiles/store{% endif %}">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        {% if profile %}
            <input type="hidden" name="_method" value="PUT">
        {% endif %}
        
        <div class="row">
            <!-- Basic Information -->
            <div class="col-lg-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ __('retrocession.profile_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('common.name') }} *</label>
                                    <input type="text" name="name" class="form-control" 
                                           value="{{ profile.name|default('') }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('common.code') }} *</label>
                                    <input type="text" name="code" class="form-control" 
                                           value="{{ profile.code|default('') }}" 
                                           pattern="[A-Z0-9_]+" maxlength="20" required>
                                    <small class="text-muted">{{ __('retrocession.code_hint') }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('retrocession.profile_type') }} *</label>
                                    <select name="profile_type" id="profile_type" class="form-select" required>
                                        {% for key, label in profileTypes %}
                                            <option value="{{ key }}" {% if profile.profile_type == key %}selected{% endif %}>
                                                {{ label }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('common.status') }}</label>
                                    <select name="is_active" class="form-select">
                                        <option value="1" {% if profile.is_active|default(true) %}selected{% endif %}>
                                            {{ __('common.active') }}
                                        </option>
                                        <option value="0" {% if profile and not profile.is_active %}selected{% endif %}>
                                            {{ __('common.inactive') }}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{{ __('common.description') }}</label>
                            <textarea name="description" class="form-control" rows="3">{{ profile.description|default('') }}</textarea>
                        </div>
                        
                        <div class="form-check">
                            <input type="checkbox" name="is_default" class="form-check-input" id="is_default" 
                                   value="1" {% if profile.is_default %}checked{% endif %}>
                            <label class="form-check-label" for="is_default">
                                {{ __('retrocession.set_as_default_profile') }}
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Rate Configuration -->
                <div class="card shadow-sm" id="rateConfiguration">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">{{ __('retrocession.rate_configuration') }}</h5>
                    </div>
                    <div class="card-body">
                        <!-- Retrocession Rates -->
                        <div id="retrocessionRates" {% if profile and profile.profile_type != 'retrocession' and profile.profile_type != 'mixed' %}style="display:none;"{% endif %}>
                            <h6 class="mb-3">{{ __('retrocession.retrocession_rates') }}</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('retrocession.cns_rate') }} (%)</label>
                                        <input type="number" name="rates[cns_percent]" class="form-control" 
                                               step="0.01" min="0" max="100" 
                                               value="{{ profile.rates.cns_percent|default(20) }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('retrocession.patient_rate') }} (%)</label>
                                        <input type="number" name="rates[patient_percent]" class="form-control" 
                                               step="0.01" min="0" max="100" 
                                               value="{{ profile.rates.patient_percent|default(20) }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('retrocession.secretariat_rate') }} (%)</label>
                                        <input type="number" name="rates[secretariat_percent]" class="form-control" 
                                               step="0.01" min="0" max="100" 
                                               value="{{ profile.rates.secretariat_percent|default(10) }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hourly Rate -->
                        <div id="hourlyRate" {% if profile and profile.profile_type != 'hourly' and profile.profile_type != 'mixed' %}style="display:none;"{% endif %}>
                            <h6 class="mb-3">{{ __('retrocession.hourly_rate') }}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('retrocession.hourly_rate_amount') }} (€)</label>
                                        <input type="number" name="rates[hourly_rate]" class="form-control" 
                                               step="0.01" min="0" 
                                               value="{{ profile.rates.hourly_rate|default(0) }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Rental Configuration -->
                        <div id="rentalConfig" {% if profile and profile.profile_type != 'rental' and profile.profile_type != 'mixed' %}style="display:none;"{% endif %}>
                            <h6 class="mb-3">{{ __('retrocession.rental_configuration') }}</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('retrocession.rent_amount') }} (€)</label>
                                        <input type="number" name="rates[rent_amount]" class="form-control" 
                                               step="0.01" min="0" 
                                               value="{{ profile.rates.rent_amount|default(0) }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('retrocession.charges_amount') }} (€)</label>
                                        <input type="number" name="rates[charges_amount]" class="form-control" 
                                               step="0.01" min="0" 
                                               value="{{ profile.rates.charges_amount|default(0) }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Valid From Date -->
                        <div class="mb-3">
                            <label class="form-label">{{ __('retrocession.rates_valid_from') }} *</label>
                            <input type="date" name="valid_from" class="form-control" 
                                   value="{{ profile.rates.valid_from|default("now"|date('Y-m-d')) }}" required>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Side Panel -->
            <div class="col-lg-4">
                {% if profile %}
                <!-- Profile Statistics -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">{{ __('retrocession.profile_statistics') }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-8">{{ __('retrocession.active_practitioners') }}:</dt>
                            <dd class="col-4">{{ profile.practitioner_count|default(0) }}</dd>
                            
                            <dt class="col-8">{{ __('retrocession.last_updated') }}:</dt>
                            <dd class="col-4">{{ profile.updated_at|date('d/m/Y') }}</dd>
                        </dl>
                    </div>
                </div>
                {% endif %}
                
                <!-- Help Information -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">{{ __('common.help') }}</h6>
                    </div>
                    <div class="card-body">
                        <p class="small mb-2"><strong>{{ __('retrocession.profile_types') }}:</strong></p>
                        <ul class="small">
                            <li><strong>{{ __('retrocession.type_retrocession') }}:</strong> {{ __('retrocession.type_retrocession_desc') }}</li>
                            <li><strong>{{ __('retrocession.type_hourly') }}:</strong> {{ __('retrocession.type_hourly_desc') }}</li>
                            <li><strong>{{ __('retrocession.type_rental') }}:</strong> {{ __('retrocession.type_rental_desc') }}</li>
                            <li><strong>{{ __('retrocession.type_mixed') }}:</strong> {{ __('retrocession.type_mixed_desc') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ base_url }}/retrocession/rate-profiles" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.cancel') }}
            </a>
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-save me-2"></i>{{ __('common.save') }}
            </button>
        </div>
    </form>
</div>

<script>
// Show/hide rate sections based on profile type
document.getElementById('profile_type').addEventListener('change', function() {
    const type = this.value;
    
    // Hide all sections first
    document.getElementById('retrocessionRates').style.display = 'none';
    document.getElementById('hourlyRate').style.display = 'none';
    document.getElementById('rentalConfig').style.display = 'none';
    
    // Show relevant sections
    if (type === 'retrocession' || type === 'mixed') {
        document.getElementById('retrocessionRates').style.display = 'block';
    }
    if (type === 'hourly' || type === 'mixed') {
        document.getElementById('hourlyRate').style.display = 'block';
    }
    if (type === 'rental' || type === 'mixed') {
        document.getElementById('rentalConfig').style.display = 'block';
    }
});

// Auto-uppercase code field
document.querySelector('input[name="code"]').addEventListener('input', function() {
    this.value = this.value.toUpperCase().replace(/[^A-Z0-9_]/g, '');
});

// Form submission
document.getElementById('profileForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    
    // Disable button
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}...';
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.href = '{{ base_url }}/retrocession/rate-profiles';
            }, 1000);
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message || '{{ __("common.error_occurred") }}');
            }
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('{{ __("common.error_occurred") }}');
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});
</script>
{% endblock %}