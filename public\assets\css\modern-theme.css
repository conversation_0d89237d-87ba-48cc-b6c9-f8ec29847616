/* Modern Theme for Health Center Billing System - Bootstrap 5.3 */

/* ============================================
   1. CSS Variables & Color Scheme
   ============================================ */
:root {
    /* Primary Colors */
    --bs-primary: #6366f1;
    --bs-primary-rgb: 99, 102, 241;
    --bs-secondary: #64748b;
    --bs-secondary-rgb: 100, 116, 139;
    --bs-success: #10b981;
    --bs-success-rgb: 16, 185, 129;
    --bs-info: #06b6d4;
    --bs-info-rgb: 6, 182, 212;
    --bs-warning: #f59e0b;
    --bs-warning-rgb: 245, 158, 11;
    --bs-danger: #ef4444;
    --bs-danger-rgb: 239, 68, 68;
    --bs-light: #f8fafc;
    --bs-light-rgb: 248, 250, 252;
    --bs-dark: #1e293b;
    --bs-dark-rgb: 30, 41, 59;
    
    /* Custom Colors */
    --sidebar-bg: #1e293b;
    --sidebar-text: #cbd5e1;
    --sidebar-hover: #334155;
    --sidebar-active: #6366f1;
    --navbar-height: 64px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    
    /* Typography */
    --bs-body-font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
    --bs-body-font-size: 0.875rem;
    --bs-body-line-height: 1.5;
    
    /* Spacing */
    --content-padding: 1.5rem;
    
    /* Animations */
    --transition-base: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* Dark Mode Variables */
[data-bs-theme="dark"] {
    --bs-body-bg: #0f172a;
    --bs-body-bg-rgb: 15, 23, 42;
    --bs-body-color: #e2e8f0;
    --bs-body-color-rgb: 226, 232, 240;
    --sidebar-bg: #0f172a;
    --sidebar-text: #e2e8f0;
    --sidebar-hover: #1e293b;
    --bs-card-bg: #1e293b;
    --bs-border-color: #334155;
}

/* ============================================
   2. Base Styles
   ============================================ */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--bs-body-font-family);
    font-size: var(--bs-body-font-size);
    line-height: var(--bs-body-line-height);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--bs-gray-100);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: 10px;
    border: 2px solid var(--bs-gray-100);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--bs-gray-500);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-track {
    background: var(--bs-gray-900);
}

[data-bs-theme="dark"] ::-webkit-scrollbar-thumb {
    background: var(--bs-gray-600);
    border-color: var(--bs-gray-900);
}

/* ============================================
   3. Layout Structure
   ============================================ */
.app-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* ============================================
   4. Navbar Styles
   ============================================ */
.app-header {
    height: var(--navbar-height);
    background: var(--bs-white);
    box-shadow: var(--shadow-sm);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1040;
    transition: var(--transition-base);
}

[data-bs-theme="dark"] .app-header {
    background: var(--bs-dark);
    border-bottom: 1px solid var(--bs-border-color);
}

.app-header .navbar {
    height: 100%;
    padding: 0 1rem;
}

/* Navbar Brand */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
    color: var(--bs-primary);
    transition: var(--transition-fast);
}

.navbar-brand:hover {
    color: var(--bs-primary);
    transform: translateX(2px);
}

/* Search Bar */
.navbar-search {
    position: relative;
    width: 100%;
    max-width: 400px;
}

.navbar-search .form-control {
    padding-left: 2.5rem;
    border-radius: 50px;
    border: 1px solid var(--bs-gray-300);
    background: var(--bs-gray-50);
    transition: var(--transition-fast);
}

.navbar-search .form-control:focus {
    background: var(--bs-white);
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
}

.navbar-search .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--bs-gray-500);
}

/* Notification Bell */
.notification-bell {
    position: relative;
    cursor: pointer;
}

.notification-bell .badge {
    position: absolute;
    top: -5px;
    right: -5px;
    padding: 0.25rem 0.4rem;
    font-size: 0.625rem;
    border-radius: 50px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(var(--bs-danger-rgb), 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--bs-danger-rgb), 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(var(--bs-danger-rgb), 0);
    }
}

/* User Menu */
.user-menu .dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 50px;
    transition: var(--transition-fast);
}

.user-menu .dropdown-toggle:hover {
    background: var(--bs-gray-100);
}

.user-menu .user-image {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

/* Dark Mode Toggle */
.theme-toggle {
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: var(--transition-fast);
}

.theme-toggle:hover {
    background: var(--bs-gray-100);
}

[data-bs-theme="dark"] .theme-toggle:hover {
    background: var(--bs-gray-800);
}

/* ============================================
   5. Sidebar Styles
   ============================================ */
.app-sidebar {
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background: var(--sidebar-bg);
    transition: var(--transition-base);
    z-index: 1030;
    overflow: hidden;
}

.app-sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

/* Sidebar Brand */
.sidebar-brand {
    padding: 1.5rem 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand .brand-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--sidebar-text);
    font-weight: 600;
    font-size: 1.125rem;
}

/* Sidebar Navigation */
.sidebar-wrapper {
    height: calc(100% - 80px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 1rem 0;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-menu .nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-menu .nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: var(--transition-fast);
    border-left: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.sidebar-menu .nav-link p {
    margin: 0;
    line-height: 1.2;
    display: flex;
    align-items: center;
}

.sidebar-menu .nav-link:hover {
    background: var(--sidebar-hover);
    color: var(--bs-white);
}

.sidebar-menu .nav-link.active {
    background: var(--sidebar-hover);
    color: var(--bs-white);
    border-left-color: var(--sidebar-active);
}

/* Sidebar Icons */
.sidebar-menu .nav-icon {
    width: 20px;
    font-size: 1.125rem;
    text-align: center;
    flex-shrink: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Nested Menu */
.sidebar-menu .nav-treeview {
    padding-left: 2rem;
    margin-top: 0.25rem;
}

.sidebar-menu .nav-treeview .nav-link {
    padding: 0.5rem 1rem;
    font-size: 0.8125rem;
}

/* Sidebar Collapsed State */
.app-sidebar.collapsed .sidebar-menu .nav-link {
    justify-content: center;
    padding: 0.75rem;
    position: relative;
}

.app-sidebar.collapsed .sidebar-menu .nav-link p,
.app-sidebar.collapsed .sidebar-menu .nav-arrow,
.app-sidebar.collapsed .sidebar-menu .nav-link .badge {
    display: none;
}

.app-sidebar.collapsed .sidebar-brand .brand-text {
    display: none;
}

/* Regular badge styling */
.sidebar-menu .nav-link .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    transition: var(--transition-fast);
}

/* ============================================
   6. Main Content Area
   ============================================ */
.app-main {
    margin-left: var(--sidebar-width);
    margin-top: var(--navbar-height);
    min-height: calc(100vh - var(--navbar-height));
    transition: var(--transition-base);
    background: var(--bs-gray-50);
}

[data-bs-theme="dark"] .app-main {
    background: var(--bs-body-bg);
}

.app-sidebar.collapsed ~ .app-main {
    margin-left: var(--sidebar-collapsed-width);
}

/* Content Header */
.app-content-header {
    background: var(--bs-white);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-sm);
}

[data-bs-theme="dark"] .app-content-header {
    background: var(--bs-dark);
}

.app-content-header h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
    color: var(--bs-gray-900);
}

[data-bs-theme="dark"] .app-content-header h1 {
    color: var(--bs-white);
}

/* Breadcrumb */
.breadcrumb {
    margin: 0;
    background: transparent;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-weight: 600;
}

/* Content Area */
.app-content {
    padding: 0 1.5rem 2rem;
}

/* ============================================
   7. Card Styles
   ============================================ */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-base);
    overflow: hidden;
}

.card:hover {
    box-shadow: var(--shadow-md);
}

.card-header {
    background: transparent;
    border-bottom: 1px solid var(--bs-gray-200);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

[data-bs-theme="dark"] .card-header {
    border-bottom-color: var(--bs-gray-700);
}

.card-body {
    padding: 1.5rem;
}

/* Card Tools */
.card-tools {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.card-tools .btn-tool {
    padding: 0.25rem 0.5rem;
    border: none;
    background: transparent;
    color: var(--bs-gray-600);
    transition: var(--transition-fast);
}

.card-tools .btn-tool:hover {
    color: var(--bs-primary);
}

/* Small Box (Stats Card) */
.small-box {
    position: relative;
    border-radius: 0.75rem;
    padding: 1.5rem;
    color: var(--bs-white);
    overflow: hidden;
    transition: var(--transition-base);
}

.small-box:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.small-box .inner h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.small-box .inner p {
    margin: 0;
    opacity: 0.9;
}

.small-box .icon {
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%);
    font-size: 4rem;
    opacity: 0.2;
}

.small-box .small-box-footer {
    display: block;
    margin: 1rem -1.5rem -1.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: inherit;
    transition: var(--transition-fast);
}

.small-box .small-box-footer:hover {
    background: rgba(0, 0, 0, 0.2);
}

/* ============================================
   8. Form Styles
   ============================================ */
.form-control,
.form-select {
    border-radius: 0.5rem;
    border: 1px solid var(--bs-gray-300);
    padding: 0.75rem 1rem;
    transition: var(--transition-fast);
}

.form-control:focus,
.form-select:focus {
    border-color: var(--bs-primary);
    box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.1);
}

/* Floating Labels */
.form-floating > .form-control,
.form-floating > .form-select {
    height: calc(3.5rem + 2px);
    line-height: 1.25;
}

.form-floating > label {
    padding: 1rem;
}

/* Input Groups */
.input-group-text {
    background: var(--bs-gray-100);
    border: 1px solid var(--bs-gray-300);
    border-radius: 0.5rem;
}

/* ============================================
   9. Button Styles
   ============================================ */
.btn {
    padding: 0.625rem 1.25rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: var(--transition-fast);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn:active {
    transform: translateY(0);
}

/* Button Variants */
.btn-primary {
    background: var(--bs-primary);
    border: none;
}

.btn-primary:hover {
    background: #5558e3;
}

/* Icon Buttons */
.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

/* ============================================
   10. Table Styles
   ============================================ */
.table {
    border-radius: 0.75rem;
    overflow: hidden;
}

.table thead th {
    background: var(--bs-gray-50);
    border-bottom: 2px solid var(--bs-gray-200);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    padding: 1rem;
}

[data-bs-theme="dark"] .table thead th {
    background: var(--bs-gray-800);
    border-bottom-color: var(--bs-gray-700);
}

.table tbody tr {
    transition: var(--transition-fast);
}

.table-hover tbody tr:hover {
    background: var(--bs-gray-50);
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
    background: var(--bs-gray-800);
}

/* Table Actions */
.table-actions {
    display: flex;
    gap: 0.5rem;
}

/* Responsive Table Improvements */
.table-responsive {
    overflow-x: visible !important;
}

.table {
    font-size: 0.875rem;
}

.table th,
.table td {
    padding: 0.75rem 0.5rem;
    white-space: nowrap;
}

/* Make specific columns more compact */
.table th:first-child,
.table td:first-child {
    width: 30px;
    padding: 0.75rem 0.25rem;
}

/* Compact table headers for invoices table */
#invoicesTable th {
    font-size: 0.75rem;
    padding: 0.5rem 0.375rem;
}

#invoicesTable td {
    padding: 0.5rem 0.375rem;
}

/* Adjust badge sizes in tables */
.table .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* Action buttons in tables */
.table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8125rem;
}

/* Responsive behavior for smaller screens */
@media (max-width: 1400px) {
    .table {
        font-size: 0.8125rem;
    }
    
    .table th,
    .table td {
        padding: 0.5rem 0.375rem;
    }
    
    .table .badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
    }
}

/* For very wide tables, allow horizontal scroll on smaller screens */
@media (max-width: 992px) {
    .table-responsive {
        overflow-x: auto !important;
        -webkit-overflow-scrolling: touch;
    }
}

/* Invoice table specific optimizations */
#invoicesTable {
    width: 100%;
}

/* Checkbox column - minimal width */
#invoicesTable th:nth-child(1),
#invoicesTable td:nth-child(1) {
    width: 1%;
    white-space: nowrap;
}

/* Invoice number - allow some flex */
#invoicesTable th:nth-child(2),
#invoicesTable td:nth-child(2) {
    min-width: 100px;
}

/* Document type and Invoice type - compact badges */
#invoicesTable th:nth-child(3),
#invoicesTable td:nth-child(3),
#invoicesTable th:nth-child(4),
#invoicesTable td:nth-child(4) {
    white-space: nowrap;
}

/* Client/Patient - take available space */
#invoicesTable th:nth-child(5),
#invoicesTable td:nth-child(5) {
    min-width: 120px;
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Dates - compact */
#invoicesTable th:nth-child(6),
#invoicesTable td:nth-child(6),
#invoicesTable th:nth-child(7),
#invoicesTable td:nth-child(7) {
    white-space: nowrap;
    min-width: 85px;
}

/* Amount - right aligned */
#invoicesTable th:nth-child(8),
#invoicesTable td:nth-child(8) {
    text-align: right;
    white-space: nowrap;
    min-width: 80px;
}

/* Status - compact */
#invoicesTable th:nth-child(9),
#invoicesTable td:nth-child(9) {
    white-space: nowrap;
}

/* Actions - minimal needed */
#invoicesTable th:nth-child(10),
#invoicesTable td:nth-child(10) {
    width: 1%;
    white-space: nowrap;
    text-align: right;
}

/* Compact dropdown menus in tables */
.table .dropdown-menu {
    font-size: 0.8125rem;
}

.table .dropdown-item {
    padding: 0.25rem 1rem;
}

/* ============================================
   11. Modal Styles
   ============================================ */
.modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: var(--shadow-xl);
}

.modal-header {
    border-bottom: 1px solid var(--bs-gray-200);
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--bs-gray-200);
    padding: 1.5rem;
}

/* ============================================
   12. Loading States
   ============================================ */
.spinner-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.25rem;
}

/* Card Loading Overlay */
.card-loading {
    position: relative;
}

.card-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: inherit;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

[data-bs-theme="dark"] .card-loading::after {
    background: rgba(15, 23, 42, 0.8);
}

.card-loading::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--bs-primary);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* ============================================
   13. Toast Notifications
   ============================================ */
.toast-container {
    position: fixed;
    top: calc(var(--navbar-height) + 1rem);
    right: 1rem;
    z-index: 1050;
}

.toast {
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--shadow-lg);
    margin-bottom: 1rem;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-header {
    background: transparent;
    border-bottom: 1px solid var(--bs-gray-200);
}

/* ============================================
   14. Badges & Pills
   ============================================ */
.badge {
    padding: 0.375rem 0.75rem;
    font-weight: 500;
    border-radius: 50px;
}

/* Status Badges */
.badge-status-draft { background-color: #64748b; }
.badge-status-sent { background-color: #06b6d4; }
.badge-status-paid { background-color: #10b981; }
.badge-status-partial { background-color: #f59e0b; }
.badge-status-overdue { background-color: #ef4444; }
.badge-status-cancelled { background-color: #1e293b; }

/* ============================================
   15. Animations & Transitions
   ============================================ */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* ============================================
   16. Utility Classes
   ============================================ */
.hover-shadow:hover {
    box-shadow: var(--shadow-lg);
}

.transition-all {
    transition: var(--transition-base);
}

.transition-fast {
    transition: var(--transition-fast);
}

/* ============================================
   17. Responsive Design
   ============================================ */
@media (max-width: 991.98px) {
    .app-sidebar {
        transform: translateX(-100%);
    }
    
    .app-sidebar.show {
        transform: translateX(0);
    }
    
    .app-main {
        margin-left: 0;
    }
    
    .app-sidebar.collapsed ~ .app-main {
        margin-left: 0;
    }
}

@media (max-width: 767.98px) {
    .app-content {
        padding: 0 1rem 1.5rem;
    }
    
    .app-content-header {
        padding: 1rem;
    }
    
    .navbar-search {
        display: none;
    }
}

/* ============================================
   18. Print Styles
   ============================================ */
@media print {
    .app-sidebar,
    .app-header,
    .app-footer,
    .btn,
    .no-print {
        display: none !important;
    }
    
    .app-main {
        margin: 0 !important;
    }
    
    .card {
        box-shadow: none !important;
        page-break-inside: avoid;
    }
}

/* ============================================
   19. Focus States (Accessibility)
   ============================================ */
:focus-visible {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

.btn:focus-visible {
    box-shadow: 0 0 0 3px rgba(var(--bs-primary-rgb), 0.25);
}

/* ============================================
   20. Custom Components
   ============================================ */
/* Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
}

.progress-step:not(:last-child)::after {
    content: "";
    position: absolute;
    top: 20px;
    left: 50%;
    width: 100%;
    height: 2px;
    background: var(--bs-gray-300);
}

.progress-step.active .step-icon {
    background: var(--bs-primary);
    color: var(--bs-white);
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--bs-gray-300);
    color: var(--bs-gray-600);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 1;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 3rem;
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--bs-gray-400);
    margin-bottom: 1rem;
}

.empty-state-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.empty-state-description {
    color: var(--bs-gray-600);
}