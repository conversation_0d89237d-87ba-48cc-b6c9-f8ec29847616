<?php
/**
 * Simple Dashboard Test - Bypass Flight Framework
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start session
session_start();

// Check if user is logged in
if (!isset($_SESSION['user'])) {
    echo "<h1>Not Logged In</h1>";
    echo "<p>You need to be logged in to view the dashboard.</p>";
    echo "<p><a href='/fit/public/login'>Go to Login</a></p>";
    exit;
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Fit360 AdminDesk</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body { background-color: #f8f9fa; }
        .card { border: none; box-shadow: 0 0 20px rgba(0,0,0,0.05); }
        .stat-card { padding: 1.5rem; }
        .stat-card h3 { margin-bottom: 0.5rem; color: #333; }
        .stat-card p { color: #6c757d; margin-bottom: 0; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/fit/public/">Fit360 AdminDesk</a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text text-white me-3">
                    Welcome, <?php echo htmlspecialchars($_SESSION['user']['name']); ?>
                </span>
                <a class="btn btn-sm btn-light" href="/fit/public/logout">Logout</a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <h1>Dashboard</h1>
        <p class="text-muted">Simple dashboard view to test if the application is working.</p>

        <div class="row mt-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <h3>0</h3>
                    <p>Total Clients</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <h3>0</h3>
                    <p>Total Patients</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <h3>0</h3>
                    <p>Total Invoices</p>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <h3>€0</h3>
                    <p>Revenue This Month</p>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Quick Actions</h5>
                        <a href="/fit/public/invoices/create" class="btn btn-primary">Create Invoice</a>
                        <a href="/fit/public/clients/create" class="btn btn-outline-primary">Add Client</a>
                        <a href="/fit/public/patients/create" class="btn btn-outline-primary">Add Patient</a>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-4">
            <h5>Debug Information:</h5>
            <pre style="background: #f5f5f5; padding: 10px;">
User: <?php echo htmlspecialchars($_SESSION['user']['name']); ?> (ID: <?php echo $_SESSION['user']['id']; ?>)
Email: <?php echo htmlspecialchars($_SESSION['user']['email']); ?>
Language: <?php echo $_SESSION['user']['language'] ?? 'Not set'; ?>
Session ID: <?php echo session_id(); ?>
            </pre>
        </div>

        <div class="mt-4">
            <p><a href="/fit/public/catch_dashboard_error.php">Run Full Error Diagnostics</a></p>
            <p><a href="/fit/public/test_twig_render.php">Test Twig Rendering</a></p>
            <p><a href="/fit/public/">Try Main Dashboard Again</a></p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>