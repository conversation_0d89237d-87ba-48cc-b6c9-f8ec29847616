<?php
/**
 * Quick check script for permission management tables
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$username = $_ENV['DB_USER'];
$password = $_ENV['DB_PASS'];
$database = $_ENV['DB_NAME'];

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    
    echo "=== Permission Management Tables Status ===\n\n";
    
    // Tables to check
    $tables = [
        'system_modules',
        'module_permissions',
        'group_permissions',
        'permission_templates',
        'permission_audit_log'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $existingTables[] = $table;
            
            // Get record count
            $countResult = $mysqli->query("SELECT COUNT(*) as count FROM $table");
            $count = $countResult ? $countResult->fetch_assoc()['count'] : 'N/A';
            
            echo "✓ $table (exists) - $count records\n";
        } else {
            $missingTables[] = $table;
            echo "× $table (missing)\n";
        }
    }
    
    echo "\nSummary:\n";
    echo "- Existing tables: " . count($existingTables) . "\n";
    echo "- Missing tables: " . count($missingTables) . "\n";
    
    if (count($missingTables) > 0) {
        echo "\nTo create missing tables, run:\n";
        echo "cd /mnt/c/wamp64/www/fit && php database/create_permission_management_system.php\n";
    } else {
        echo "\n✓ All permission management tables exist!\n";
    }
    
    $mysqli->close();
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}