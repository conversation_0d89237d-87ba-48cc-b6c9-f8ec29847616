{% extends "base-modern.twig" %}

{% block title %}{{ template.id ? __('common.edit') : __('common.create') }} {{ __('config.email_template') }}{% endblock %}

{% block head %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
<style>
    .CodeMirror {
        height: 400px;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
    }
    .variable-helper {
        cursor: pointer;
        padding: 0.25rem 0.5rem;
        margin: 0.125rem;
        background: #e9ecef;
        border-radius: 0.25rem;
        font-family: monospace;
        font-size: 0.875rem;
        transition: all 0.2s;
    }
    .variable-helper:hover {
        background: #6c757d;
        color: white;
    }
    .priority-slider {
        width: 100%;
    }
    .priority-indicator {
        font-size: 1.5rem;
        margin-left: 1rem;
    }
    .condition-builder {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
    }
    .condition-row {
        margin-bottom: 0.5rem;
    }
    .preview-pane {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        min-height: 400px;
        background: white;
        padding: 1rem;
    }
    .mobile-touch-target {
        min-height: 44px;
        display: flex;
        align-items: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4 flex-wrap">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-envelope-open-text me-2"></i>
            {{ template.id ? __('common.edit') : __('common.create') }} {{ __('config.email_template') }}
        </h1>
        <div class="d-flex gap-2 flex-wrap">
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#variablesModal">
                <i class="bi bi-code-slash me-2"></i>{{ __('config.template_variables') }}
            </button>
            <button type="button" class="btn btn-secondary" onclick="previewTemplate()">
                <i class="bi bi-eye me-2"></i>{{ __('common.preview') }}
            </button>
            <button type="submit" form="templateForm" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
            </button>
            <a href="{{ base_url }}/email-templates" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
            </a>
        </div>
    </div>

    <!-- Alert for validation -->
    <div id="validationAlert" class="alert alert-danger alert-dismissible fade show d-none" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <span id="validationMessage"></span>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <form id="templateForm" method="POST" action="{{ base_url }}/email-templates{{ template.id ? '/' ~ template.id : '' }}" class="needs-validation" novalidate>
        {% if template.id %}
        <input type="hidden" name="_method" value="PUT">
        {% endif %}
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="row">
            <!-- Left Column - Form Fields -->
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('config.basic_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <label for="name" class="form-label">{{ __('common.name') }} *</label>
                                <input type="text" class="form-control mobile-touch-target" id="name" name="name" 
                                       value="{{ template.name }}" required
                                       placeholder="{{ __('config.template_name_placeholder') }}">
                                <div class="invalid-feedback">{{ __('validation.required_field') }}</div>
                            </div>
                            <div class="col-md-4">
                                <label for="code" class="form-label">{{ __('common.code') }} *</label>
                                <input type="text" class="form-control mobile-touch-target" id="code" name="code" 
                                       value="{{ template.code }}" required
                                       pattern="[a-z0-9_-]+" 
                                       placeholder="invoice_reminder"
                                       data-bs-toggle="tooltip"
                                       title="{{ __('config.code_format_hint') }}">
                                <div class="invalid-feedback">{{ __('validation.code_format') }}</div>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <div class="col-md-4">
                                <label for="invoice_type" class="form-label">
                                    {{ __('config.invoice_type') }}
                                    <i class="bi bi-question-circle text-muted" data-bs-toggle="tooltip" 
                                       title="{{ __('config.invoice_type_hint') }}"></i>
                                </label>
                                <select class="form-select mobile-touch-target" id="invoice_type" name="invoice_type">
                                    <option value="">{{ __('common.all_types') }}</option>
                                    {% for type in invoice_types %}
                                    <option value="{{ type.code }}" {{ template.invoice_type == type.code ? 'selected' : '' }}>
                                        {{ type.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="email_type" class="form-label">
                                    {{ __('config.email_type') }} *
                                    <i class="bi bi-question-circle text-muted" data-bs-toggle="tooltip" 
                                       title="{{ __('config.email_type_hint') }}"></i>
                                </label>
                                <select class="form-select mobile-touch-target" id="email_type" name="email_type" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    <optgroup label="{{ __('config.invoice_emails') }}">
                                        <option value="invoice_new" {{ template.email_type == 'invoice_new' ? 'selected' : '' }}>
                                            {{ __('email.type_invoice_new') }}
                                        </option>
                                        <option value="invoice_reminder" {{ template.email_type == 'invoice_reminder' ? 'selected' : '' }}>
                                            {{ __('email.type_invoice_reminder') }}
                                        </option>
                                        <option value="invoice_overdue" {{ template.email_type == 'invoice_overdue' ? 'selected' : '' }}>
                                            {{ __('email.type_invoice_overdue') }}
                                        </option>
                                        <option value="invoice_partial" {{ template.email_type == 'invoice_partial' ? 'selected' : '' }}>
                                            {{ __('email.type_invoice_partial') }}
                                        </option>
                                        <option value="invoice_paid" {{ template.email_type == 'invoice_paid' ? 'selected' : '' }}>
                                            {{ __('email.type_invoice_paid') }}
                                        </option>
                                        <option value="invoice_cancelled" {{ template.email_type == 'invoice_cancelled' ? 'selected' : '' }}>
                                            {{ __('email.type_invoice_cancelled') }}
                                        </option>
                                    </optgroup>
                                    <optgroup label="{{ __('config.other_emails') }}">
                                        <option value="quote_new" {{ template.email_type == 'quote_new' ? 'selected' : '' }}>
                                            {{ __('email.type_quote_new') }}
                                        </option>
                                        <option value="appointment_reminder" {{ template.email_type == 'appointment_reminder' ? 'selected' : '' }}>
                                            {{ __('email.type_appointment_reminder') }}
                                        </option>
                                        <option value="custom" {{ template.email_type == 'custom' ? 'selected' : '' }}>
                                            {{ __('email.type_custom') }}
                                        </option>
                                    </optgroup>
                                </select>
                                <div class="invalid-feedback">{{ __('validation.required_field') }}</div>
                            </div>
                            <div class="col-md-4">
                                <label for="parent_template_id" class="form-label">
                                    {{ __('config.parent_template') }}
                                    <i class="bi bi-question-circle text-muted" data-bs-toggle="tooltip" 
                                       title="{{ __('config.parent_template_hint') }}"></i>
                                </label>
                                <select class="form-select mobile-touch-target" id="parent_template_id" name="parent_template_id">
                                    <option value="">{{ __('common.none') }}</option>
                                    {% for parent in available_templates %}
                                        {% if parent.id != template.id %}
                                        <option value="{{ parent.id }}" {{ template.parent_template_id == parent.id ? 'selected' : '' }}>
                                            {{ parent.name }}
                                        </option>
                                        {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="row g-3 mt-2">
                            <div class="col-md-8">
                                <label for="priority" class="form-label">
                                    {{ __('config.priority') }}
                                    <span class="priority-indicator" id="priorityIndicator">
                                        {% if template.priority == 1 %}⚠️{% elseif template.priority == 2 %}🔴{% elseif template.priority == 3 %}🟡{% elseif template.priority == 4 %}🟢{% else %}⚪{% endif %}
                                    </span>
                                </label>
                                <input type="range" class="form-range priority-slider" id="priority" name="priority" 
                                       min="1" max="5" value="{{ template.priority|default(5) }}"
                                       oninput="updatePriorityIndicator(this.value)">
                                <div class="d-flex justify-content-between text-muted small">
                                    <span>{{ __('priority.critical') }}</span>
                                    <span>{{ __('priority.high') }}</span>
                                    <span>{{ __('priority.medium') }}</span>
                                    <span>{{ __('priority.low') }}</span>
                                    <span>{{ __('priority.default') }}</span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">{{ __('common.status') }}</label>
                                <div class="form-check form-switch mobile-touch-target">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                           value="1" {{ template.is_active !== false ? 'checked' : '' }}>
                                    <label class="form-check-label" for="is_active">
                                        {{ __('config.template_active') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Content -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#html-tab" role="tab">
                                    <i class="bi bi-code-slash me-2"></i>{{ __('config.html_content') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#text-tab" role="tab">
                                    <i class="bi bi-file-text me-2"></i>{{ __('config.text_content') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#conditions-tab" role="tab">
                                    <i class="bi bi-diagram-3 me-2"></i>{{ __('config.conditions') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="subject" class="form-label">{{ __('config.email_subject') }} *</label>
                            <input type="text" class="form-control mobile-touch-target" id="subject" name="subject" 
                                   value="{{ template.subject }}" required
                                   placeholder="{{ __('config.subject_placeholder') }}">
                            <div class="invalid-feedback">{{ __('validation.required_field') }}</div>
                        </div>
                        
                        <div class="tab-content">
                            <!-- HTML Content Tab -->
                            <div class="tab-pane fade show active" id="html-tab" role="tabpanel">
                                <div class="mb-3">
                                    <label for="body_html" class="form-label">{{ __('config.html_body') }} *</label>
                                    <textarea class="form-control" id="body_html" name="body_html" rows="15" required>{{ template.body_html|default(template.body) }}</textarea>
                                </div>
                            </div>
                            
                            <!-- Text Content Tab -->
                            <div class="tab-pane fade" id="text-tab" role="tabpanel">
                                <div class="mb-3">
                                    <label for="body_text" class="form-label">
                                        {{ __('config.text_body') }}
                                        <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="generateTextFromHtml()">
                                            <i class="bi bi-arrow-repeat me-1"></i>{{ __('config.generate_from_html') }}
                                        </button>
                                    </label>
                                    <textarea class="form-control" id="body_text" name="body_text" rows="15">{{ template.body_text }}</textarea>
                                </div>
                            </div>
                            
                            <!-- Conditions Tab -->
                            <div class="tab-pane fade" id="conditions-tab" role="tabpanel">
                                <div class="condition-builder">
                                    <h6 class="mb-3">{{ __('config.conditional_logic') }}</h6>
                                    <p class="text-muted small">{{ __('config.conditions_hint') }}</p>
                                    
                                    <div id="conditionsContainer">
                                        <!-- Conditions will be added here dynamically -->
                                    </div>
                                    
                                    <button type="button" class="btn btn-sm btn-outline-primary mt-3" onclick="addCondition()">
                                        <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_condition') }}
                                    </button>
                                    
                                    <div class="mt-3">
                                        <label for="conditions_json" class="form-label">{{ __('config.json_editor') }}</label>
                                        <textarea class="form-control font-monospace" id="conditions_json" name="conditions" rows="8">{{ template.conditions|json_encode(constant('JSON_PRETTY_PRINT')) }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Right Column - Variable Helper & Preview -->
            <div class="col-lg-4">
                <!-- Variable Helper -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-code me-2"></i>{{ __('config.variable_helper') }}</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted small mb-3">{{ __('config.click_to_insert') }}</p>
                        
                        <h6 class="text-primary">{{ __('config.invoice_variables') }}</h6>
                        <div class="mb-3">
                            <span class="variable-helper" onclick="insertVariable('{invoice_number}')">{invoice_number}</span>
                            <span class="variable-helper" onclick="insertVariable('{invoice_date}')">{invoice_date}</span>
                            <span class="variable-helper" onclick="insertVariable('{due_date}')">{due_date}</span>
                            <span class="variable-helper" onclick="insertVariable('{total_amount}')">{total_amount}</span>
                            <span class="variable-helper" onclick="insertVariable('{balance_due}')">{balance_due}</span>
                            <span class="variable-helper" onclick="insertVariable('{subtotal}')">{subtotal}</span>
                            <span class="variable-helper" onclick="insertVariable('{vat_amount}')">{vat_amount}</span>
                        </div>
                        
                        <h6 class="text-primary">{{ __('config.client_variables') }}</h6>
                        <div class="mb-3">
                            <span class="variable-helper" onclick="insertVariable('{client_name}')">{client_name}</span>
                            <span class="variable-helper" onclick="insertVariable('{client_email}')">{client_email}</span>
                            <span class="variable-helper" onclick="insertVariable('{client_phone}')">{client_phone}</span>
                            <span class="variable-helper" onclick="insertVariable('{client_address}')">{client_address}</span>
                            <span class="variable-helper" onclick="insertVariable('{client_city}')">{client_city}</span>
                        </div>
                        
                        <h6 class="text-primary">{{ __('config.company_variables') }}</h6>
                        <div class="mb-3">
                            <span class="variable-helper" onclick="insertVariable('{company_name}')">{company_name}</span>
                            <span class="variable-helper" onclick="insertVariable('{company_email}')">{company_email}</span>
                            <span class="variable-helper" onclick="insertVariable('{company_phone}')">{company_phone}</span>
                            <span class="variable-helper" onclick="insertVariable('{company_website}')">{company_website}</span>
                        </div>
                        
                        <h6 class="text-primary">{{ __('config.special_variables') }}</h6>
                        <div>
                            <span class="variable-helper" onclick="insertVariable('{items_table}')">{items_table}</span>
                            <span class="variable-helper" onclick="insertVariable('{payment_link}')">{payment_link}</span>
                            <span class="variable-helper" onclick="insertVariable('{invoice_link}')">{invoice_link}</span>
                            <span class="variable-helper" onclick="insertVariable('{unsubscribe_link}')">{unsubscribe_link}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Live Preview -->
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-eye me-2"></i>{{ __('common.live_preview') }}</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="preview-pane">
                            <div class="mb-3">
                                <strong>{{ __('config.subject') }}:</strong>
                                <div id="previewSubject" class="text-muted">{{ template.subject|default(__('config.no_subject')) }}</div>
                            </div>
                            <hr>
                            <div id="previewBody">
                                {{ template.body_html|default(template.body)|default(__('config.no_content'))|raw }}
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button type="button" class="btn btn-sm btn-outline-primary w-100" onclick="showFullPreview()">
                            <i class="bi bi-arrows-fullscreen me-2"></i>{{ __('config.full_preview') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Variables Modal (same as index) -->
<div class="modal fade" id="variablesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.available_variables') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Same content as in index-modern.twig -->
                <div class="row">
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">{{ __('config.invoice_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{invoice_number}</code>
                                <small class="d-block text-muted">{{ __('config.var_invoice_number') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{invoice_date}</code>
                                <small class="d-block text-muted">{{ __('config.var_invoice_date') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{due_date}</code>
                                <small class="d-block text-muted">{{ __('config.var_due_date') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{total_amount}</code>
                                <small class="d-block text-muted">{{ __('config.var_total_amount') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{subtotal}</code>
                                <small class="d-block text-muted">{{ __('config.var_subtotal') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{vat_amount}</code>
                                <small class="d-block text-muted">{{ __('config.var_vat_amount') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{balance_due}</code>
                                <small class="d-block text-muted">{{ __('config.var_balance_due') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{payment_link}</code>
                                <small class="d-block text-muted">{{ __('config.var_payment_link') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{invoice_link}</code>
                                <small class="d-block text-muted">{{ __('config.var_invoice_link') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">{{ __('config.client_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{client_name}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_name') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_email}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_email') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_phone}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_phone') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_address}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_address') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_city}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_city') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_postal_code}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_postal_code') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_country}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_country') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{client_vat}</code>
                                <small class="d-block text-muted">{{ __('config.var_client_vat') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <h6 class="text-primary mb-3">{{ __('config.company_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{company_name}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_name') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_email}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_email') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_phone}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_phone') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_address}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_address') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_website}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_website') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{company_vat}</code>
                                <small class="d-block text-muted">{{ __('config.var_company_vat') }}</small>
                            </div>
                        </div>
                        
                        <h6 class="text-primary mb-3 mt-4">{{ __('config.special_variables') }}</h6>
                        <div class="list-group">
                            <div class="list-group-item">
                                <code>{items_table}</code>
                                <small class="d-block text-muted">{{ __('config.var_items_table') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{payment_history}</code>
                                <small class="d-block text-muted">{{ __('config.var_payment_history') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{current_year}</code>
                                <small class="d-block text-muted">{{ __('config.var_current_year') }}</small>
                            </div>
                            <div class="list-group-item">
                                <code>{unsubscribe_link}</code>
                                <small class="d-block text-muted">{{ __('config.var_unsubscribe_link') }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Full Preview Modal -->
<div class="modal fade" id="fullPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.email_preview') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <iframe id="previewFrame" style="width: 100%; height: 600px; border: 1px solid #dee2e6;"></iframe>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.close') }}</button>
                <a href="{{ base_url }}/email-templates/{{ template.id }}/test" class="btn btn-primary">
                    <i class="bi bi-send me-2"></i>{{ __('config.send_test_email') }}
                </a>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/xml/xml.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/javascript/javascript.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/css/css.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/htmlmixed/htmlmixed.min.js"></script>
<script>
// Initialize CodeMirror
let htmlEditor, textEditor, jsonEditor;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize HTML editor
    htmlEditor = CodeMirror.fromTextArea(document.getElementById('body_html'), {
        mode: 'htmlmixed',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true,
        autoCloseTags: true
    });
    
    // Initialize text editor
    textEditor = CodeMirror.fromTextArea(document.getElementById('body_text'), {
        mode: 'text',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true
    });
    
    // Initialize JSON editor
    jsonEditor = CodeMirror.fromTextArea(document.getElementById('conditions_json'), {
        mode: 'application/json',
        theme: 'monokai',
        lineNumbers: true,
        lineWrapping: true
    });
    
    // Update preview on content change
    htmlEditor.on('change', updatePreview);
    document.getElementById('subject').addEventListener('input', updatePreview);
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Load existing conditions
    loadConditions();
    
    // Form validation
    const form = document.getElementById('templateForm');
    form.addEventListener('submit', function(event) {
        if (!validateForm()) {
            event.preventDefault();
            event.stopPropagation();
        }
    });
});

// Update priority indicator
function updatePriorityIndicator(value) {
    const indicator = document.getElementById('priorityIndicator');
    const indicators = {
        '1': '⚠️',
        '2': '🔴',
        '3': '🟡',
        '4': '🟢',
        '5': '⚪'
    };
    indicator.textContent = indicators[value];
}

// Insert variable at cursor position
function insertVariable(variable) {
    const activeElement = document.activeElement;
    
    // Check if we're in CodeMirror
    if (htmlEditor.hasFocus()) {
        const cursor = htmlEditor.getCursor();
        htmlEditor.replaceRange(variable, cursor);
        htmlEditor.focus();
    } else if (textEditor.hasFocus()) {
        const cursor = textEditor.getCursor();
        textEditor.replaceRange(variable, cursor);
        textEditor.focus();
    } else if (activeElement.id === 'subject') {
        // Insert into subject field
        const start = activeElement.selectionStart;
        const end = activeElement.selectionEnd;
        const text = activeElement.value;
        activeElement.value = text.substring(0, start) + variable + text.substring(end);
        activeElement.selectionStart = activeElement.selectionEnd = start + variable.length;
        activeElement.focus();
        updatePreview();
    } else {
        // Default to HTML editor
        const cursor = htmlEditor.getCursor();
        htmlEditor.replaceRange(variable, cursor);
        htmlEditor.focus();
    }
}

// Update live preview
function updatePreview() {
    const subject = document.getElementById('subject').value;
    const body = htmlEditor.getValue();
    
    document.getElementById('previewSubject').textContent = subject || '{{ __("config.no_subject") }}';
    document.getElementById('previewBody').innerHTML = body || '{{ __("config.no_content") }}';
}

// Generate text from HTML
function generateTextFromHtml() {
    const html = htmlEditor.getValue();
    const temp = document.createElement('div');
    temp.innerHTML = html;
    
    // Simple HTML to text conversion
    let text = temp.textContent || temp.innerText || '';
    
    // Clean up extra whitespace
    text = text.replace(/\n{3,}/g, '\n\n').trim();
    
    textEditor.setValue(text);
}

// Show full preview
function showFullPreview() {
    const modal = new bootstrap.Modal(document.getElementById('fullPreviewModal'));
    modal.show();
    
    // Create preview HTML
    const subject = document.getElementById('subject').value;
    const body = htmlEditor.getValue();
    
    const previewHtml = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <style>
                body { 
                    font-family: Arial, sans-serif; 
                    line-height: 1.6; 
                    color: #333; 
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .subject {
                    background: #f4f4f4;
                    padding: 15px;
                    margin-bottom: 20px;
                    border-radius: 5px;
                }
                .subject strong {
                    color: #666;
                }
            </style>
        </head>
        <body>
            <div class="subject">
                <strong>Subject:</strong> ${subject}
            </div>
            ${body}
        </body>
        </html>
    `;
    
    const iframe = document.getElementById('previewFrame');
    const doc = iframe.contentDocument || iframe.contentWindow.document;
    doc.open();
    doc.write(previewHtml);
    doc.close();
}

// Condition builder
let conditionIndex = 0;

function loadConditions() {
    try {
        const conditions = JSON.parse(jsonEditor.getValue() || '[]');
        if (Array.isArray(conditions)) {
            conditions.forEach(condition => addCondition(condition));
        }
    } catch (e) {
        console.error('Error loading conditions:', e);
    }
}

function addCondition(data = {}) {
    const container = document.getElementById('conditionsContainer');
    const id = `condition_${conditionIndex++}`;
    
    const conditionHtml = `
        <div class="condition-row card p-2 mb-2" id="${id}">
            <div class="row g-2">
                <div class="col-md-3">
                    <select class="form-select form-select-sm" onchange="updateConditionsJson()">
                        <option value="">{{ __('config.select_field') }}</option>
                        <option value="invoice_type" ${data.field === 'invoice_type' ? 'selected' : ''}>{{ __('config.invoice_type') }}</option>
                        <option value="client_type" ${data.field === 'client_type' ? 'selected' : ''}>{{ __('config.client_type') }}</option>
                        <option value="amount" ${data.field === 'amount' ? 'selected' : ''}>{{ __('config.amount') }}</option>
                        <option value="days_overdue" ${data.field === 'days_overdue' ? 'selected' : ''}>{{ __('config.days_overdue') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select form-select-sm" onchange="updateConditionsJson()">
                        <option value="equals" ${data.operator === 'equals' ? 'selected' : ''}>{{ __('config.equals') }}</option>
                        <option value="not_equals" ${data.operator === 'not_equals' ? 'selected' : ''}>{{ __('config.not_equals') }}</option>
                        <option value="greater_than" ${data.operator === 'greater_than' ? 'selected' : ''}>{{ __('config.greater_than') }}</option>
                        <option value="less_than" ${data.operator === 'less_than' ? 'selected' : ''}>{{ __('config.less_than') }}</option>
                        <option value="contains" ${data.operator === 'contains' ? 'selected' : ''}>{{ __('config.contains') }}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control form-control-sm" placeholder="{{ __('config.value') }}" 
                           value="${data.value || ''}" onchange="updateConditionsJson()">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-sm btn-outline-danger w-100" onclick="removeCondition('${id}')">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', conditionHtml);
    updateConditionsJson();
}

function removeCondition(id) {
    document.getElementById(id).remove();
    updateConditionsJson();
}

function updateConditionsJson() {
    const conditions = [];
    const rows = document.querySelectorAll('.condition-row');
    
    rows.forEach(row => {
        const selects = row.querySelectorAll('select');
        const input = row.querySelector('input');
        
        if (selects[0].value && selects[1].value && input.value) {
            conditions.push({
                field: selects[0].value,
                operator: selects[1].value,
                value: input.value
            });
        }
    });
    
    jsonEditor.setValue(JSON.stringify(conditions, null, 2));
}

// Form validation
function validateForm() {
    const form = document.getElementById('templateForm');
    let isValid = true;
    
    // Check required fields
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
        }
    });
    
    // Check code format
    const codeField = document.getElementById('code');
    if (codeField.value && !/^[a-z0-9_-]+$/.test(codeField.value)) {
        codeField.classList.add('is-invalid');
        isValid = false;
    }
    
    // Validate JSON
    try {
        JSON.parse(jsonEditor.getValue());
    } catch (e) {
        document.getElementById('validationAlert').classList.remove('d-none');
        document.getElementById('validationMessage').textContent = '{{ __("config.invalid_json") }}';
        isValid = false;
    }
    
    if (!isValid) {
        form.classList.add('was-validated');
    }
    
    return isValid;
}
</script>
{% endblock %}