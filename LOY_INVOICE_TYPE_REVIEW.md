# LOY Invoice Type Review

## Overview
LOY (Loyalty/Loyer) is a special invoice type in the Fit360 AdminDesk system designed for loyalty programs, rental fees, or special billing arrangements.

## Current Configuration

### 1. TTC (Tax Included) Pricing
- **Status**: ✅ Configured
- **Location**: `/app/Helpers/InvoiceTTCHelper.php`
- LOY invoices use TTC pricing where VAT is extracted from the total amount
- Formula: `HT = TTC / (1 + VAT_RATE/100)`
- This is the same behavior as LOC invoices

### 2. Invoice Creation Behavior
- **Location**: `/app/views/invoices/create-modern.twig` (line 1832)
- LOY is grouped with LOC invoices for location/rental type behavior
- When LOY type is selected:
  - Price labels change to "Prix TTC"
  - VAT is calculated by extraction, not addition
  - May trigger location-specific features

### 3. Database Configuration
To verify LOY configuration in your database:
1. Check `invoice_types` table for LOY prefix
2. Check `invoice_number_sequences` for LOY sequence
3. Review existing LOY invoices

### 4. Key Features
- **TTC Calculation**: Prices include VAT
- **Invoice Number Format**: FAC-LOY-YYYY-NNNN
- **VAT Handling**: Extracted from total, not added
- **Use Cases**: Loyalty programs, special pricing, rental arrangements

## Testing LOY Invoice Type

1. **Create Test Invoice**:
   - Go to `/invoices/create`
   - Select LOY invoice type
   - Enter a TTC price (e.g., 100€)
   - Verify VAT is extracted correctly

2. **Verify Calculations**:
   - For 100€ TTC with 17% VAT:
   - HT should be: 85.47€
   - VAT should be: 14.53€

3. **Check Invoice Display**:
   - Price should show as "Prix TTC"
   - Total should match entered TTC amount

## Recommendations

1. **Naming Clarity**: Consider if LOY stands for "Loyalty" or "Loyer" (rent in French)
2. **Documentation**: Add description to invoice type in database
3. **Validation**: Ensure LOY invoices follow business rules for loyalty/rental billing

## Related Files
- `/app/Helpers/InvoiceTTCHelper.php` - TTC calculation logic
- `/app/views/invoices/create-modern.twig` - Form behavior
- `/app/controllers/InvoiceController.php` - Processing logic
- Database tables: `invoice_types`, `invoice_number_sequences`