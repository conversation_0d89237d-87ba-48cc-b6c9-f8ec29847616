<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.prefix { font-weight: bold; color: #007bff; }
</style>';

echo "<h1>Invoice Types and Prefixes</h1>";

// Check config_invoice_types table
echo '<div class="section">';
echo '<h2>Config Invoice Types (config_invoice_types)</h2>';
$stmt = $db->query("
    SELECT id, name, code, prefix, description, is_active 
    FROM config_invoice_types 
    ORDER BY id
");
$types = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<table>';
echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Description</th><th>Active</th></tr>';
foreach ($types as $type) {
    // Decode JSON fields
    $name = json_decode($type['name'], true);
    $desc = json_decode($type['description'], true);
    $displayName = is_array($name) ? ($name['fr'] ?? $name['en'] ?? $type['name']) : $type['name'];
    $displayDesc = is_array($desc) ? ($desc['fr'] ?? $desc['en'] ?? $type['description']) : $type['description'];
    
    echo '<tr>';
    echo '<td>' . $type['id'] . '</td>';
    echo '<td>' . htmlspecialchars($displayName) . '</td>';
    echo '<td>' . $type['code'] . '</td>';
    echo '<td class="prefix">' . ($type['prefix'] ?? 'NULL') . '</td>';
    echo '<td>' . htmlspecialchars($displayDesc) . '</td>';
    echo '<td>' . ($type['is_active'] ? 'Yes' : 'No') . '</td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';

// Check invoice_types table (if different)
echo '<div class="section">';
echo '<h2>Invoice Types Table (invoice_types) - if exists</h2>';
try {
    $stmt = $db->query("
        SELECT id, name, code, prefix, description, is_active 
        FROM invoice_types 
        ORDER BY id
    ");
    $types = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($types) > 0) {
        echo '<table>';
        echo '<tr><th>ID</th><th>Name</th><th>Code</th><th>Prefix</th><th>Description</th><th>Active</th></tr>';
        foreach ($types as $type) {
            // Decode JSON fields if needed
            $name = json_decode($type['name'], true);
            $desc = json_decode($type['description'], true);
            $displayName = is_array($name) ? ($name['fr'] ?? $name['en'] ?? $type['name']) : $type['name'];
            $displayDesc = is_array($desc) ? ($desc['fr'] ?? $desc['en'] ?? $type['description']) : $type['description'];
            
            echo '<tr>';
            echo '<td>' . $type['id'] . '</td>';
            echo '<td>' . htmlspecialchars($displayName) . '</td>';
            echo '<td>' . $type['code'] . '</td>';
            echo '<td class="prefix">' . ($type['prefix'] ?? 'NULL') . '</td>';
            echo '<td>' . htmlspecialchars($displayDesc) . '</td>';
            echo '<td>' . ($type['is_active'] ? 'Yes' : 'No') . '</td>';
            echo '</tr>';
        }
        echo '</table>';
    } else {
        echo '<p>No records found in invoice_types table.</p>';
    }
} catch (Exception $e) {
    echo '<p>invoice_types table does not exist or error: ' . $e->getMessage() . '</p>';
}
echo '</div>';

// Show example invoice numbers
echo '<div class="section">';
echo '<h2>Example Invoice Numbers with New Format</h2>';
echo '<p>New format: <strong>FAC-{TYPE_PREFIX}-{YYYY}-{NNNN}</strong></p>';
echo '<table>';
echo '<tr><th>Type</th><th>Current Prefix</th><th>Example Number</th></tr>';
foreach ($types as $type) {
    $prefix = $type['prefix'] ?? '???';
    $example = 'FAC-' . $prefix . '-2025-0186';
    echo '<tr>';
    echo '<td>' . $type['code'] . '</td>';
    echo '<td class="prefix">' . $prefix . '</td>';
    echo '<td><strong>' . $example . '</strong></td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';

// Current document number format
echo '<div class="section">';
echo '<h2>Current Document Number Configuration</h2>';
$stmt = $db->query("
    SELECT dt.code, dt.prefix as doc_prefix, dt.number_format 
    FROM document_types dt 
    WHERE dt.code = 'invoice'
");
$docType = $stmt->fetch(\PDO::FETCH_ASSOC);
if ($docType) {
    echo '<p>Document Type: ' . $docType['code'] . '</p>';
    echo '<p>Document Prefix: ' . $docType['doc_prefix'] . '</p>';
    echo '<p>Number Format: ' . $docType['number_format'] . '</p>';
} else {
    echo '<p>No document type configuration found for invoices.</p>';
}
echo '</div>';