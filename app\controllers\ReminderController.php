<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Database;
use App\Services\ReminderService;
use App\Models\EmailTemplate;
use App\Helpers\Validator;
use App\Helpers\Logger;

class ReminderController extends Controller {
    
    private $reminderService;
    
    public function __construct() {
        parent::__construct();
        $this->reminderService = new ReminderService();
    }
    
    /**
     * Display reminder settings
     */
    public function settings() {
        $this->checkPermission('view_settings');
        
        try {
            $db = Database::getInstance()->getConnection();
            
            // Get reminder settings
            $stmt = $db->prepare("
                SELECT rs.*, et.name as template_name
                FROM reminder_settings rs
                LEFT JOIN email_templates et ON rs.template_id = et.id
                ORDER BY rs.reminder_level
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Get email templates for dropdown
            $templates = EmailTemplate::getActiveTemplates('reminders');
            
            // Get reminder statistics
            $stats = $this->reminderService->getReminderStats(30);
            
            // Get exclusion list
            $stmt = $db->prepare("
                SELECT re.*, c.first_name, c.last_name, c.company, c.email
                FROM reminder_exclusions re
                JOIN clients c ON re.client_id = c.id
                WHERE re.active = 1
                ORDER BY re.created_at DESC
            ");
            $stmt->execute();
            $exclusions = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            $this->render('reminders/settings', [
                'settings' => $settings,
                'templates' => $templates,
                'stats' => $stats,
                'exclusions' => $exclusions
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Failed to load reminder settings: ' . $e->getMessage());
            $this->flash('error', 'Failed to load reminder settings');
            $this->redirect('/dashboard');
        }
    }
    
    /**
     * Update reminder settings
     */
    public function updateSettings() {
        $this->checkPermission('edit_settings');
        
        if (!$this->isPost()) {
            $this->redirect('/reminders/settings');
        }
        
        try {
            $db = Database::getInstance()->getConnection();
            $db->beginTransaction();
            
            $settings = $_POST['settings'] ?? [];
            
            foreach ($settings as $level => $setting) {
                $stmt = $db->prepare("
                    UPDATE reminder_settings
                    SET days_overdue = ?,
                        template_id = ?,
                        enabled = ?,
                        send_copy_to_admin = ?,
                        admin_email = ?,
                        updated_at = NOW()
                    WHERE reminder_level = ?
                ");
                
                $stmt->execute([
                    $setting['days_overdue'] ?? 7,
                    $setting['template_id'] ?: null,
                    isset($setting['enabled']) ? 1 : 0,
                    isset($setting['send_copy_to_admin']) ? 1 : 0,
                    $setting['admin_email'] ?? null,
                    $level
                ]);
            }
            
            $db->commit();
            
            Logger::info('Payment reminder settings updated');
            $this->flash('success', 'Reminder settings updated successfully');
            
        } catch (\Exception $e) {
            $db->rollBack();
            Logger::error('Failed to update reminder settings: ' . $e->getMessage());
            $this->flash('error', 'Failed to update reminder settings');
        }
        
        $this->redirect('/reminders/settings');
    }
    
    /**
     * Add client to exclusion list
     */
    public function addExclusion() {
        $this->checkPermission('edit_settings');
        
        if (!$this->isPost()) {
            $this->json(['success' => false, 'message' => 'Invalid request']);
        }
        
        try {
            $clientId = $_POST['client_id'] ?? 0;
            $reason = $_POST['reason'] ?? '';
            $endDate = $_POST['end_date'] ?? null;
            
            if (!$clientId) {
                throw new \Exception('Client ID is required');
            }
            
            $this->reminderService->addClientExclusion($clientId, $reason, $endDate);
            
            Logger::info("Client {$clientId} added to reminder exclusion list");
            $this->json(['success' => true, 'message' => 'Client added to exclusion list']);
            
        } catch (\Exception $e) {
            Logger::error('Failed to add exclusion: ' . $e->getMessage());
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * Remove client from exclusion list
     */
    public function removeExclusion($clientId) {
        $this->checkPermission('edit_settings');
        
        try {
            $this->reminderService->removeClientExclusion($clientId);
            
            Logger::info("Client {$clientId} removed from reminder exclusion list");
            $this->flash('success', 'Client removed from exclusion list');
            
        } catch (\Exception $e) {
            Logger::error('Failed to remove exclusion: ' . $e->getMessage());
            $this->flash('error', 'Failed to remove exclusion');
        }
        
        $this->redirect('/reminders/settings');
    }
    
    /**
     * Preview reminder email
     */
    public function preview($level) {
        $this->checkPermission('view_settings');
        
        try {
            $db = Database::getInstance()->getConnection();
            
            // Get a sample invoice for preview
            $stmt = $db->prepare("
                SELECT 
                    i.id,
                    i.invoice_number,
                    i.total_amount,
                    i.due_date,
                    c.first_name,
                    c.last_name,
                    c.email,
                    c.company
                FROM invoices i
                JOIN clients c ON i.client_id = c.id
                WHERE i.status = 'pending'
                LIMIT 1
            ");
            $stmt->execute();
            $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$invoice) {
                // Create dummy data for preview
                $invoice = [
                    'id' => 1,
                    'invoice_number' => 'INV-2024-001',
                    'total_amount' => 500.00,
                    'due_date' => date('Y-m-d', strtotime('-14 days')),
                    'first_name' => 'John',
                    'last_name' => 'Doe',
                    'email' => '<EMAIL>',
                    'company' => 'Acme Corp'
                ];
            }
            
            // Calculate days overdue
            $invoice['days_overdue'] = max(0, (time() - strtotime($invoice['due_date'])) / 86400);
            
            // Get template
            $stmt = $db->prepare("
                SELECT et.*
                FROM reminder_settings rs
                JOIN email_templates et ON rs.template_id = et.id
                WHERE rs.reminder_level = ?
            ");
            $stmt->execute([$level]);
            $template = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$template) {
                throw new \Exception('Template not found for reminder level ' . $level);
            }
            
            // Prepare preview data
            $emailData = [
                'client_name' => trim($invoice['first_name'] . ' ' . $invoice['last_name']),
                'company' => $invoice['company'],
                'invoice_number' => $invoice['invoice_number'],
                'invoice_amount' => number_format($invoice['total_amount'], 2),
                'due_date' => date('F j, Y', strtotime($invoice['due_date'])),
                'days_overdue' => round($invoice['days_overdue']),
                'reminder_level' => $level,
                'payment_link' => getenv('APP_URL') . '/invoice/pay/' . $invoice['id'],
                'invoice_link' => getenv('APP_URL') . '/invoice/view/' . $invoice['id'],
                'company_name' => getenv('COMPANY_NAME') ?: 'Your Company',
                'company_email' => getenv('COMPANY_EMAIL') ?: '<EMAIL>',
                'company_phone' => getenv('COMPANY_PHONE') ?: '555-0123'
            ];
            
            // Replace variables in template
            $subject = $template['subject'];
            $bodyHtml = $template['body_html'];
            $bodyText = $template['body_text'];
            
            foreach ($emailData as $key => $value) {
                $subject = str_replace('{{' . $key . '}}', $value, $subject);
                $bodyHtml = str_replace('{{' . $key . '}}', $value, $bodyHtml);
                $bodyText = str_replace('{{' . $key . '}}', $value, $bodyText);
            }
            
            $this->json([
                'success' => true,
                'preview' => [
                    'subject' => $subject,
                    'body_html' => $bodyHtml,
                    'body_text' => $bodyText,
                    'level' => $level
                ]
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Failed to preview reminder: ' . $e->getMessage());
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * Send test reminder
     */
    public function sendTest() {
        $this->checkPermission('edit_settings');
        
        if (!$this->isPost()) {
            $this->json(['success' => false, 'message' => 'Invalid request']);
        }
        
        try {
            $level = $_POST['level'] ?? 1;
            $email = $_POST['email'] ?? '';
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new \Exception('Invalid email address');
            }
            
            // Create test invoice data
            $testInvoice = [
                'id' => 0,
                'invoice_number' => 'TEST-' . date('Y') . '-001',
                'client_id' => 0,
                'total_amount' => 250.00,
                'due_date' => date('Y-m-d', strtotime('-' . (7 * $level) . ' days')),
                'first_name' => 'Test',
                'last_name' => 'Customer',
                'email' => $email,
                'company' => 'Test Company',
                'days_overdue' => 7 * $level
            ];
            
            $result = $this->reminderService->sendReminder($testInvoice, $level);
            
            if ($result) {
                Logger::info("Test reminder level {$level} sent to {$email}");
                $this->json(['success' => true, 'message' => 'Test reminder sent successfully']);
            } else {
                throw new \Exception('Failed to send test reminder');
            }
            
        } catch (\Exception $e) {
            Logger::error('Failed to send test reminder: ' . $e->getMessage());
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * View reminder history for an invoice
     */
    public function history($invoiceId) {
        $this->checkPermission('view_invoices');
        
        try {
            $db = Database::getInstance()->getConnection();
            
            $stmt = $db->prepare("
                SELECT rl.*, et.name as template_name
                FROM reminder_logs rl
                LEFT JOIN reminder_settings rs ON rl.reminder_number = rs.reminder_level
                LEFT JOIN email_templates et ON rs.template_id = et.id
                WHERE rl.invoice_id = ?
                ORDER BY rl.sent_date DESC
            ");
            $stmt->execute([$invoiceId]);
            $history = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            $this->json([
                'success' => true,
                'history' => $history
            ]);
            
        } catch (\Exception $e) {
            Logger::error('Failed to get reminder history: ' . $e->getMessage());
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
    
    /**
     * Manual reminder trigger
     */
    public function sendManual($invoiceId) {
        $this->checkPermission('send_reminders');
        
        try {
            $db = Database::getInstance()->getConnection();
            
            // Get invoice details
            $stmt = $db->prepare("
                SELECT 
                    i.*,
                    c.first_name,
                    c.last_name,
                    c.email,
                    c.company,
                    DATEDIFF(CURDATE(), i.due_date) as days_overdue,
                    (SELECT COUNT(*) FROM reminder_logs WHERE invoice_id = i.id) as reminders_sent
                FROM invoices i
                JOIN clients c ON i.client_id = c.id
                WHERE i.id = ?
            ");
            $stmt->execute([$invoiceId]);
            $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$invoice) {
                throw new \Exception('Invoice not found');
            }
            
            if ($invoice['status'] !== 'pending') {
                throw new \Exception('Invoice is not pending');
            }
            
            // Determine next reminder level
            $nextLevel = min($invoice['reminders_sent'] + 1, 3);
            
            // Send reminder
            $result = $this->reminderService->sendReminder($invoice, $nextLevel);
            
            if ($result) {
                Logger::info("Manual reminder sent for invoice {$invoiceId}");
                $this->flash('success', 'Payment reminder sent successfully');
            } else {
                throw new \Exception('Failed to send reminder');
            }
            
        } catch (\Exception $e) {
            Logger::error('Failed to send manual reminder: ' . $e->getMessage());
            $this->flash('error', $e->getMessage());
        }
        
        $this->redirect('/invoice/view/' . $invoiceId);
    }
}