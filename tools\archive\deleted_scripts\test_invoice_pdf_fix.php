<?php
// Test script to verify invoice PDF fix
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Load application bootstrap
require_once __DIR__ . '/../app/config/bootstrap.php';

// Test invoice ID (you can change this)
$invoiceId = $_GET['id'] ?? 241;

try {
    // Test 1: Load invoice using the model (like the fixed PDF does)
    echo "<h2>Test 1: Loading invoice using Model (Fixed approach)</h2>";
    $invoiceModel = new \App\Models\Invoice();
    $invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);
    
    if ($invoice) {
        echo "Invoice Number: " . $invoice['invoice_number'] . "<br>";
        echo "Invoice Lines Count: " . count($invoice['lines'] ?? []) . "<br>";
        echo "<h3>Invoice Lines:</h3>";
        echo "<pre>";
        foreach (($invoice['lines'] ?? []) as $idx => $line) {
            echo "Line " . ($idx + 1) . ": " . $line['description'] . " - Qty: " . $line['quantity'] . " - Price: " . $line['unit_price'] . "\n";
        }
        echo "</pre>";
    } else {
        echo "Invoice not found!<br>";
    }
    
    echo "<hr>";
    
    // Test 2: Show what the old approach would have done (duplicate query)
    echo "<h2>Test 2: Old approach with duplicate query</h2>";
    $db = Flight::db();
    $stmt = $db->prepare("
        SELECT id, invoice_id, description, quantity, unit_price, 
               vat_rate, sort_order
        FROM invoice_lines 
        WHERE invoice_id = :id
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute(['id' => $invoiceId]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Direct Query Lines Count: " . count($items) . "<br>";
    echo "<h3>Lines from Direct Query:</h3>";
    echo "<pre>";
    foreach ($items as $idx => $item) {
        echo "Line " . ($idx + 1) . ": " . $item['description'] . " - Qty: " . $item['quantity'] . " - Price: " . $item['unit_price'] . "\n";
    }
    echo "</pre>";
    
    echo "<hr>";
    echo "<h2>Summary</h2>";
    echo "<p>If both counts are the same and show the same data, the fix is working correctly!</p>";
    echo "<p><a href='invoice-pdf.php?id=$invoiceId' target='_blank'>Test PDF Generation</a></p>";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>