# Health Center Billing System - Database Schema

## Overview
This document details all database tables, their structure, and relationships for the Health Center Billing System built with Flight PHP.

## Database Design Principles
- **Normalization:** 3NF for data integrity
- **Flexibility:** JSON fields for dynamic configurations
- **Performance:** Strategic indexing and denormalization where needed
- **Audit Trail:** Comprehensive tracking of all changes
- **Soft Deletes:** Maintain data integrity with deleted_at timestamps

## Core System Tables

### 1. Configuration Management

#### config_categories
```sql
CREATE TABLE config_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT NULL,
    sort_order INT DEFAULT 0,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES config_categories(id),
    INDEX idx_parent (parent_id)
);
```

#### config_items
```sql
CREATE TABLE config_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    key_name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    data_type ENUM('string', 'integer', 'decimal', 'boolean', 'json', 'text', 'select', 'multiselect') NOT NULL,
    default_value TEXT,
    validation_rules JSON,
    options JSON, -- For select/multiselect types
    is_required BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE, -- Cannot be deleted
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES config_categories(id),
    INDEX idx_category (category_id),
    INDEX idx_key (key_name)
);
```

#### config_values
```sql
CREATE TABLE config_values (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_item_id INT NOT NULL,
    value TEXT,
    updated_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (config_item_id) REFERENCES config_items(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    UNIQUE KEY unique_config (config_item_id)
);
```

#### config_history
```sql
CREATE TABLE config_history (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_item_id INT NOT NULL,
    old_value TEXT,
    new_value TEXT,
    changed_by INT NOT NULL,
    change_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (config_item_id) REFERENCES config_items(id),
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_config_item (config_item_id),
    INDEX idx_created (created_at)
);
```

### 2. User Management

#### users
```sql
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(50),
    language_code VARCHAR(5) DEFAULT 'fr',
    timezone VARCHAR(50) DEFAULT 'Europe/Luxembourg',
    profile_photo VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    force_password_change BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP NULL,
    last_activity_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_email (email),
    INDEX idx_active (is_active),
    INDEX idx_deleted (deleted_at)
);
```

#### user_groups
```sql
CREATE TABLE user_groups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#000000',
    icon VARCHAR(50),
    is_system BOOLEAN DEFAULT FALSE, -- Cannot be deleted (e.g., Admin)
    session_timeout INT DEFAULT 3600, -- Seconds
    max_concurrent_sessions INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_deleted (deleted_at)
);
```

#### user_group_members
```sql
CREATE TABLE user_group_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    group_id INT NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    UNIQUE KEY unique_membership (user_id, group_id),
    INDEX idx_user (user_id),
    INDEX idx_group (group_id)
);
```

#### permissions
```sql
CREATE TABLE permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    module VARCHAR(100) NOT NULL,
    action VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_permission (module, action),
    INDEX idx_module (module)
);
```

#### group_permissions
```sql
CREATE TABLE group_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    group_id INT NOT NULL,
    permission_id INT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    granted_by INT,
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE KEY unique_grant (group_id, permission_id),
    INDEX idx_group (group_id)
);
```

#### user_sessions
```sql
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_token (session_token),
    INDEX idx_expires (expires_at)
);
```

#### user_preferences
```sql
CREATE TABLE user_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_preference (user_id, preference_key),
    INDEX idx_user (user_id)
);
```

### 3. Invoice Management

#### invoice_types
```sql
CREATE TABLE invoice_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    code VARCHAR(4) UNIQUE NOT NULL,
    name JSON NOT NULL, -- {"fr": "Loyer", "de": "Miete", "en": "Rent"}
    description JSON,
    color VARCHAR(7) DEFAULT '#000000',
    default_vat_rate_id INT,
    numbering_pattern VARCHAR(100) DEFAULT '{YEAR}-{CODE}-{NUMBER}',
    number_reset_period ENUM('never', 'yearly', 'monthly') DEFAULT 'yearly',
    next_number INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    access_groups JSON, -- Array of group IDs that can use this type
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (default_vat_rate_id) REFERENCES vat_rates(id),
    INDEX idx_code (code),
    INDEX idx_active (is_active),
    INDEX idx_deleted (deleted_at)
);
```

#### vat_rates
```sql
CREATE TABLE vat_rates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    rate DECIMAL(5,2) NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    valid_from DATE NOT NULL,
    valid_until DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_dates (valid_from, valid_until)
);
```

#### invoices
```sql
CREATE TABLE invoices (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_type_id INT NOT NULL,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    invoice_date DATE NOT NULL,
    due_date DATE NOT NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    vat_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    status ENUM('draft', 'sent', 'viewed', 'partial', 'paid', 'overdue', 'cancelled') DEFAULT 'draft',
    payment_terms TEXT,
    notes TEXT,
    internal_notes TEXT,
    letterhead_mode BOOLEAN DEFAULT FALSE,
    created_by INT NOT NULL,
    updated_by INT,
    sent_at TIMESTAMP NULL,
    viewed_at TIMESTAMP NULL,
    paid_at TIMESTAMP NULL,
    cancelled_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (invoice_type_id) REFERENCES invoice_types(id),
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_number (invoice_number),
    INDEX idx_client (client_id),
    INDEX idx_type (invoice_type_id),
    INDEX idx_status (status),
    INDEX idx_dates (invoice_date, due_date),
    INDEX idx_deleted (deleted_at)
);
```

#### invoice_items
```sql
CREATE TABLE invoice_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    service_id INT,
    description TEXT NOT NULL,
    quantity DECIMAL(10,2) NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    vat_rate_id INT,
    vat_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (vat_rate_id) REFERENCES vat_rates(id),
    INDEX idx_invoice (invoice_id),
    INDEX idx_service (service_id)
);
```

#### invoice_statuses
```sql
CREATE TABLE invoice_statuses (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_id INT NOT NULL,
    status VARCHAR(50) NOT NULL,
    changed_by INT NOT NULL,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_invoice (invoice_id),
    INDEX idx_created (created_at)
);
```

#### invoice_numbers
```sql
CREATE TABLE invoice_numbers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    invoice_type_id INT NOT NULL,
    year INT NOT NULL,
    month INT,
    last_number INT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_type_id) REFERENCES invoice_types(id),
    UNIQUE KEY unique_sequence (invoice_type_id, year, month),
    INDEX idx_type_year (invoice_type_id, year)
);
```

### 4. Client Management

#### clients
```sql
CREATE TABLE clients (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_type ENUM('individual', 'company') NOT NULL,
    category_id INT,
    code VARCHAR(50) UNIQUE,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    company_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    mobile VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(2) DEFAULT 'LU',
    vat_number VARCHAR(50),
    language_code VARCHAR(5) DEFAULT 'fr',
    payment_terms_days INT DEFAULT 30,
    notes TEXT,
    balance DECIMAL(10,2) DEFAULT 0, -- Denormalized for performance
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (category_id) REFERENCES client_categories(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_code (code),
    INDEX idx_email (email),
    INDEX idx_name (last_name, first_name),
    INDEX idx_company (company_name),
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_deleted (deleted_at)
);
```

#### client_categories
```sql
CREATE TABLE client_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    color VARCHAR(7) DEFAULT '#000000',
    payment_terms_days INT,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    pricing_tier VARCHAR(50),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    INDEX idx_active (is_active),
    INDEX idx_deleted (deleted_at)
);
```

#### client_contacts
```sql
CREATE TABLE client_contacts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    contact_type VARCHAR(50) NOT NULL, -- billing, technical, etc.
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    INDEX idx_client (client_id),
    INDEX idx_type (contact_type)
);
```

#### client_preferences
```sql
CREATE TABLE client_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    UNIQUE KEY unique_preference (client_id, preference_key),
    INDEX idx_client (client_id)
);
```

#### client_documents
```sql
CREATE TABLE client_documents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    client_id INT NOT NULL,
    document_type VARCHAR(50) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    mime_type VARCHAR(100),
    uploaded_by INT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id),
    INDEX idx_client (client_id),
    INDEX idx_type (document_type),
    INDEX idx_created (created_at)
);
```

### 5. Payment Management

#### payments
```sql
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(50) UNIQUE NOT NULL,
    client_id INT NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method_id INT NOT NULL,
    reference VARCHAR(255),
    notes TEXT,
    is_reconciled BOOLEAN DEFAULT FALSE,
    reconciliation_id INT,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (client_id) REFERENCES clients(id),
    FOREIGN KEY (payment_method_id) REFERENCES payment_methods(id),
    FOREIGN KEY (reconciliation_id) REFERENCES bank_reconciliations(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_client (client_id),
    INDEX idx_date (payment_date),
    INDEX idx_method (payment_method_id),
    INDEX idx_reconciled (is_reconciled),
    INDEX idx_deleted (deleted_at)
);
```

#### payment_methods
```sql
CREATE TABLE payment_methods (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_bank_method BOOLEAN DEFAULT FALSE,
    requires_reference BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);
```

#### payment_allocations
```sql
CREATE TABLE payment_allocations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_id INT NOT NULL,
    invoice_id INT NOT NULL,
    allocated_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (payment_id) REFERENCES payments(id) ON DELETE CASCADE,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    UNIQUE KEY unique_allocation (payment_id, invoice_id),
    INDEX idx_payment (payment_id),
    INDEX idx_invoice (invoice_id)
);
```

#### bank_reconciliations
```sql
CREATE TABLE bank_reconciliations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bank_import_id INT NOT NULL,
    transaction_date DATE NOT NULL,
    description TEXT,
    amount DECIMAL(10,2) NOT NULL,
    balance DECIMAL(10,2),
    match_status ENUM('pending', 'matched', 'partial', 'unmatched', 'manual') DEFAULT 'pending',
    matched_payment_id INT,
    match_confidence DECIMAL(5,2), -- Percentage
    reconciled_by INT,
    reconciled_at TIMESTAMP NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (bank_import_id) REFERENCES bank_imports(id),
    FOREIGN KEY (matched_payment_id) REFERENCES payments(id),
    FOREIGN KEY (reconciled_by) REFERENCES users(id),
    INDEX idx_import (bank_import_id),
    INDEX idx_status (match_status),
    INDEX idx_date (transaction_date)
);
```

#### bank_imports
```sql
CREATE TABLE bank_imports (
    id INT PRIMARY KEY AUTO_INCREMENT,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_type VARCHAR(50) NOT NULL, -- CSV, MT940, PDF, etc.
    bank_name VARCHAR(100),
    account_number VARCHAR(50),
    import_date DATE NOT NULL,
    transaction_count INT DEFAULT 0,
    matched_count INT DEFAULT 0,
    unmatched_count INT DEFAULT 0,
    status ENUM('pending', 'processing', 'completed', 'failed') DEFAULT 'pending',
    error_message TEXT,
    imported_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (imported_by) REFERENCES users(id),
    INDEX idx_status (status),
    INDEX idx_date (import_date),
    INDEX idx_created (created_at)
);
```

### 6. Service Management

#### service_categories
```sql
CREATE TABLE service_categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    parent_id INT,
    name JSON NOT NULL, -- {"fr": "Kinésithérapie", "de": "Physiotherapie"}
    description JSON,
    color VARCHAR(7) DEFAULT '#000000',
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (parent_id) REFERENCES service_categories(id),
    INDEX idx_parent (parent_id),
    INDEX idx_active (is_active),
    INDEX idx_deleted (deleted_at)
);
```

#### services
```sql
CREATE TABLE services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    category_id INT NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    name JSON NOT NULL,
    description JSON,
    pricing_model ENUM('fixed', 'hourly', 'package') DEFAULT 'fixed',
    default_price DECIMAL(10,2) NOT NULL,
    default_duration INT, -- Minutes
    default_vat_rate_id INT,
    requires_staff BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (category_id) REFERENCES service_categories(id),
    FOREIGN KEY (default_vat_rate_id) REFERENCES vat_rates(id),
    INDEX idx_code (code),
    INDEX idx_category (category_id),
    INDEX idx_active (is_active),
    INDEX idx_deleted (deleted_at)
);
```

#### service_pricing
```sql
CREATE TABLE service_pricing (
    id INT PRIMARY KEY AUTO_INCREMENT,
    service_id INT NOT NULL,
    client_category_id INT,
    price_type ENUM('standard', 'volume', 'contract') DEFAULT 'standard',
    min_quantity INT DEFAULT 1,
    max_quantity INT,
    unit_price DECIMAL(10,2) NOT NULL,
    valid_from DATE NOT NULL,
    valid_until DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    FOREIGN KEY (client_category_id) REFERENCES client_categories(id),
    INDEX idx_service (service_id),
    INDEX idx_category (client_category_id),
    INDEX idx_dates (valid_from, valid_until)
);
```

#### service_bundles
```sql
CREATE TABLE service_bundles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bundle_name VARCHAR(255) NOT NULL,
    description TEXT,
    bundle_price DECIMAL(10,2) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active)
);
```

#### bundle_services
```sql
CREATE TABLE bundle_services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    bundle_id INT NOT NULL,
    service_id INT NOT NULL,
    quantity INT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (bundle_id) REFERENCES service_bundles(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id),
    UNIQUE KEY unique_bundle_service (bundle_id, service_id),
    INDEX idx_bundle (bundle_id)
);
```

#### staff_services
```sql
CREATE TABLE staff_services (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    is_preferred BOOLEAN DEFAULT FALSE,
    markup_percentage DECIMAL(5,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    UNIQUE KEY unique_staff_service (user_id, service_id),
    INDEX idx_user (user_id),
    INDEX idx_service (service_id)
);
```

### 7. Communication System

#### email_templates
```sql
CREATE TABLE email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    template_key VARCHAR(100) UNIQUE NOT NULL,
    template_type VARCHAR(50) NOT NULL, -- invoice, reminder, notification, etc.
    name VARCHAR(255) NOT NULL,
    subject JSON NOT NULL, -- {"fr": "Votre facture", "de": "Ihre Rechnung"}
    body JSON NOT NULL, -- HTML content with variables
    variables JSON, -- Available variables for this template
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_key (template_key),
    INDEX idx_type (template_type),
    INDEX idx_active (is_active)
);
```

#### email_queue
```sql
CREATE TABLE email_queue (
    id INT PRIMARY KEY AUTO_INCREMENT,
    to_email VARCHAR(255) NOT NULL,
    cc_email TEXT,
    bcc_email TEXT,
    subject VARCHAR(500) NOT NULL,
    body TEXT NOT NULL,
    attachments JSON,
    priority INT DEFAULT 5, -- 1-10, 1 being highest
    template_id INT,
    related_type VARCHAR(50), -- invoice, payment, etc.
    related_id INT,
    send_after TIMESTAMP NULL,
    attempts INT DEFAULT 0,
    max_attempts INT DEFAULT 3,
    status ENUM('pending', 'sending', 'sent', 'failed', 'cancelled') DEFAULT 'pending',
    sent_at TIMESTAMP NULL,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (template_id) REFERENCES email_templates(id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_send_after (send_after),
    INDEX idx_related (related_type, related_id)
);
```

#### email_logs
```sql
CREATE TABLE email_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    queue_id INT,
    to_email VARCHAR(255) NOT NULL,
    subject VARCHAR(500) NOT NULL,
    status VARCHAR(50) NOT NULL,
    sent_by INT,
    response_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (queue_id) REFERENCES email_queue(id),
    FOREIGN KEY (sent_by) REFERENCES users(id),
    INDEX idx_to_email (to_email),
    INDEX idx_created (created_at)
);
```

#### messages
```sql
CREATE TABLE messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    parent_id INT,
    message_type ENUM('internal', 'note', 'announcement', 'chat') NOT NULL,
    context_type VARCHAR(50), -- invoice, client, etc.
    context_id INT,
    sender_id INT NOT NULL,
    recipient_id INT, -- NULL for announcements
    recipient_group_id INT, -- For group messages
    subject VARCHAR(255),
    body TEXT NOT NULL,
    priority ENUM('low', 'normal', 'high', 'urgent') DEFAULT 'normal',
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    expires_at TIMESTAMP NULL, -- For announcements
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (parent_id) REFERENCES messages(id),
    FOREIGN KEY (sender_id) REFERENCES users(id),
    FOREIGN KEY (recipient_id) REFERENCES users(id),
    FOREIGN KEY (recipient_group_id) REFERENCES user_groups(id),
    INDEX idx_parent (parent_id),
    INDEX idx_type (message_type),
    INDEX idx_context (context_type, context_id),
    INDEX idx_sender (sender_id),
    INDEX idx_recipient (recipient_id),
    INDEX idx_group (recipient_group_id),
    INDEX idx_read (is_read),
    INDEX idx_expires (expires_at),
    INDEX idx_deleted (deleted_at)
);
```

#### news_board
```sql
CREATE TABLE news_board (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    priority ENUM('normal', 'important', 'urgent') DEFAULT 'normal',
    target_groups JSON, -- Array of group IDs, NULL = all
    posted_by INT NOT NULL,
    is_pinned BOOLEAN DEFAULT FALSE,
    allow_comments BOOLEAN DEFAULT TRUE,
    publish_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP NULL,
    FOREIGN KEY (posted_by) REFERENCES users(id),
    INDEX idx_priority (priority),
    INDEX idx_pinned (is_pinned),
    INDEX idx_publish (publish_at),
    INDEX idx_expires (expires_at),
    INDEX idx_deleted (deleted_at)
);
```

#### notifications
```sql
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    data JSON, -- Additional data for the notification
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    action_url VARCHAR(500),
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_type (type),
    INDEX idx_read (is_read),
    INDEX idx_expires (expires_at),
    INDEX idx_created (created_at)
);
```

### 8. System Tables

#### activity_logs
```sql
CREATE TABLE activity_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    module VARCHAR(50) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user (user_id),
    INDEX idx_action (action),
    INDEX idx_module (module),
    INDEX idx_entity (entity_type, entity_id),
    INDEX idx_created (created_at)
) PARTITION BY RANGE (YEAR(created_at)) (
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027),
    PARTITION p2027 VALUES LESS THAN (2028)
);
```

#### scheduled_tasks
```sql
CREATE TABLE scheduled_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_name VARCHAR(100) UNIQUE NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    schedule VARCHAR(100) NOT NULL, -- Cron expression
    command VARCHAR(500),
    parameters JSON,
    is_active BOOLEAN DEFAULT TRUE,
    last_run_at TIMESTAMP NULL,
    next_run_at TIMESTAMP NULL,
    last_status VARCHAR(50),
    last_duration INT, -- Seconds
    error_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active (is_active),
    INDEX idx_next_run (next_run_at)
);
```

#### system_health
```sql
CREATE TABLE system_health (
    id INT PRIMARY KEY AUTO_INCREMENT,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(10,2) NOT NULL,
    metric_unit VARCHAR(50),
    threshold_warning DECIMAL(10,2),
    threshold_critical DECIMAL(10,2),
    status ENUM('ok', 'warning', 'critical') DEFAULT 'ok',
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_metric (metric_name),
    INDEX idx_status (status),
    INDEX idx_recorded (recorded_at)
);
```

#### backups
```sql
CREATE TABLE backups (
    id INT PRIMARY KEY AUTO_INCREMENT,
    backup_type ENUM('full', 'incremental', 'selective') NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    storage_location VARCHAR(50) NOT NULL, -- local, cloud, etc.
    includes_database BOOLEAN DEFAULT TRUE,
    includes_files BOOLEAN DEFAULT TRUE,
    status ENUM('pending', 'running', 'completed', 'failed') DEFAULT 'pending',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    error_message TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_type (backup_type),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);
```

#### migrations
```sql
CREATE TABLE migrations (
    id INT PRIMARY KEY AUTO_INCREMENT,
    version VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    execution_time INT, -- Milliseconds
    INDEX idx_version (version)
);
```

## Key Relationships Summary

### User Management
- Users ↔ Groups: Many-to-Many via user_group_members
- Groups → Permissions: Many-to-Many via group_permissions
- Users → Sessions: One-to-Many
- Users → Preferences: One-to-Many

### Invoice System
- Invoices → Clients: Many-to-One
- Invoices → Invoice Types: Many-to-One
- Invoices → Items: One-to-Many
- Invoice Items → Services: Many-to-One (optional)
- Invoice Items → VAT Rates: Many-to-One

### Payment System
- Payments → Clients: Many-to-One
- Payments ↔ Invoices: Many-to-Many via payment_allocations
- Payments → Bank Reconciliations: One-to-One (optional)
- Bank Imports → Reconciliations: One-to-Many

### Service Management
- Services → Categories: Many-to-One (hierarchical)
- Services ↔ Staff: Many-to-Many via staff_services
- Services → Pricing: One-to-Many
- Services ↔ Bundles: Many-to-Many via bundle_services

### Communication
- Email Templates → Queue: One-to-Many
- Messages: Self-referential for threading
- News Board → User Groups: Many-to-Many (target groups)
- Notifications → Users: Many-to-One

## Indexing Strategy

### Primary Indexes
- All primary keys are automatically indexed
- All foreign keys have indexes for join performance

### Business Logic Indexes
- Lookup fields (codes, email addresses, etc.)
- Date ranges for reporting
- Status fields for filtering
- Soft delete fields (deleted_at)

### Performance Indexes
- Composite indexes for common query patterns
- Covering indexes for frequently accessed data
- Partial indexes for active records only

## Data Integrity Rules

### Cascading Deletes
- User deletion cascades to sessions, preferences
- Invoice deletion cascades to invoice items
- Payment deletion cascades to allocations
- Client deletion restricted if invoices exist

### Soft Deletes
- Users, clients, invoices use soft deletes
- Maintains referential integrity
- Allows data recovery
- Preserves audit trail

### Data Validation
- Check constraints on amounts (>= 0)
- Enum constraints on status fields
- Date validation (due_date > invoice_date)
- Unique constraints on business keys

## Performance Considerations

### Partitioning
- Activity logs partitioned by year
- Improves query performance
- Easier archival process

### Denormalization
- Client balance for quick access
- Invoice totals to avoid recalculation
- Payment allocation totals

### Caching Strategy
- Configuration values cached in application
- User permissions cached per session
- Service pricing cached with TTL

## Security Features

### Audit Trail
- All changes logged in activity_logs
- User, timestamp, IP tracked
- Old/new values stored as JSON
- Partition for performance

### Data Protection
- Sensitive data encryption ready
- PII fields identified for GDPR
- Retention policies enforceable
- Anonymization capabilities

### Access Control
- Row-level security via user groups
- Column-level masking ready
- API rate limiting support
- Session management

## Migration Strategy

### Initial Setup
1. Create database and user
2. Run schema creation scripts
3. Insert default configuration
4. Create admin user
5. Load initial data

### Version Control
- Migration files numbered sequentially
- Rollback scripts included
- Migration history tracked
- Automated migration runner

This database schema provides a robust foundation for the Health Center Billing System with extensive configurability, strong data integrity, and excellent performance characteristics.