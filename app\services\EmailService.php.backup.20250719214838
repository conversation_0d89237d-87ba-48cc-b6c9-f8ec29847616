<?php

namespace App\Services;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Voucher;
use PDO;
use Exception;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

class EmailService
{
    private $db;
    private $mailer;
    
    public function __construct()
    {
        $this->db = Flight::db();
        // Initialize mailer (you would configure your actual mail service here)
        // For now, we'll use a simple implementation
    }
    
    /**
     * Send invoice email
     */
    public function sendInvoiceEmail($invoiceId, $recipientEmail = null)
    {
        try {
            // Get invoice details
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Determine recipient
            if (!$recipientEmail) {
                // Check for client email first
                if (!empty($invoiceData['client']['email'])) {
                    $recipientEmail = $invoiceData['client']['email'];
                } 
                // Then check for user email (for user invoices)
                elseif (!empty($invoiceData['user']['invoice_email'])) {
                    $recipientEmail = $invoiceData['user']['invoice_email'];
                } 
                elseif (!empty($invoiceData['user']['email'])) {
                    $recipientEmail = $invoiceData['user']['email'];
                }
            }
            
            if (!$recipientEmail) {
                // Log the issue for debugging
                error_log('EmailService: No recipient email found for invoice ' . $invoiceId);
                error_log('Invoice data: ' . json_encode([
                    'has_client' => isset($invoiceData['client']),
                    'has_user' => isset($invoiceData['user']),
                    'client_email' => $invoiceData['client']['email'] ?? 'not set',
                    'user_email' => $invoiceData['user']['email'] ?? 'not set',
                    'user_invoice_email' => $invoiceData['user']['invoice_email'] ?? 'not set'
                ]));
                throw new Exception('No recipient email address found');
            }
            
            // Select appropriate email template
            $template = $this->selectInvoiceTemplate($invoiceData);
            
            // Render email content
            $emailContent = $this->renderTemplate($template, $invoiceData);
            
            // Generate PDF
            $pdfService = new PdfService();
            $pdf = $pdfService->generateInvoicePdf($invoiceData);
            
            // Send email
            $result = $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [
                    [
                        'name' => $invoiceData['invoice_number'] . '.pdf',
                        'content' => $pdf,
                        'type' => 'application/pdf'
                    ]
                ]
            ]);
            
            // Log email
            $this->logEmail($invoiceId, $template['id'] ?? null, $recipientEmail, $result);
            
            // Add debug logging
            error_log('EmailService: Invoice email result for invoice ' . $invoiceId . ': ' . json_encode($result));
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => $params['subject'] ?? 'Invoice Email'
            ];
        }
    }
    
    /**
     * Get invoice email preview
     */
    public function getInvoiceEmailPreview($invoiceId)
    {
        $invoice = new Invoice();
        $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
        
        if (!$invoiceData) {
            return null;
        }
        
        $template = $this->selectInvoiceTemplate($invoiceData);
        return $this->renderTemplate($template, $invoiceData);
    }
    
    /**
     * Send voucher email
     */
    public function sendVoucherEmail($voucherId, $recipientEmail)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Get client details
            $client = new Client();
            $clientData = $client->getById($voucherData['client_id']);
            
            // Select template
            $template = $this->getEmailTemplate('voucher_created');
            
            // Prepare data
            $data = array_merge($voucherData, [
                'client' => $clientData,
                'company_name' => $this->getConfig('company_name'),
                'company_email' => $this->getConfig('company_email')
            ]);
            
            // Render email
            $emailContent = $this->renderTemplate($template, $data);
            
            // Send email
            $result = $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text']
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => $params['subject'] ?? 'Invoice Email'
            ];
        }
    }
    
    /**
     * Send voucher expiry reminder
     */
    public function sendVoucherExpiryReminder($voucherId, $recipientEmail)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Calculate days until expiry
            $daysUntilExpiry = floor((strtotime($voucherData['expiry_date']) - time()) / 86400);
            
            // Get template
            $template = $this->getEmailTemplate('voucher_expiry_reminder');
            
            // Prepare data
            $data = array_merge($voucherData, [
                'days_until_expiry' => $daysUntilExpiry,
                'company_name' => $this->getConfig('company_name')
            ]);
            
            // Render and send
            $emailContent = $this->renderTemplate($template, $data);
            
            return $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text']
            ]);
            
        } catch (Exception $e) {
            return [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => $params['subject'] ?? 'Invoice Email'
            ];
        }
    }
    
    /**
     * Send payment reminder
     */
    public function sendPaymentReminder($invoiceId, $reminderLevel = 1)
    {
        try {
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Calculate overdue days
            $daysOverdue = floor((time() - strtotime($invoiceData['due_date'])) / 86400);
            
            // Get appropriate reminder template
            $template = $this->getEmailTemplate('reminder_' . $reminderLevel, $invoiceData['invoice_type']);
            
            // Prepare data
            $data = array_merge($invoiceData, [
                'days_overdue' => $daysOverdue,
                'reminder_level' => $reminderLevel,
                'company_name' => $this->getConfig('company_name')
            ]);
            
            // Render email
            $emailContent = $this->renderTemplate($template, $data);
            
            // Generate PDF
            $pdfService = new PdfService();
            $pdf = $pdfService->generateInvoicePdf($invoiceData);
            
            // Send email
            $result = $this->send([
                'to' => $invoiceData['client']['email'],
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [
                    [
                        'name' => $invoiceData['invoice_number'] . '.pdf',
                        'content' => $pdf,
                        'type' => 'application/pdf'
                    ]
                ]
            ]);
            
            // Log reminder
            $this->logPaymentReminder($invoiceId, $reminderLevel, $result);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => $params['subject'] ?? 'Invoice Email'
            ];
        }
    }
    
    /**
     * Select appropriate invoice template
     */
    private function selectInvoiceTemplate($invoice)
    {
        // Log what we're looking for
        error_log('EmailService: Selecting template for invoice type: ' . ($invoice['invoice_type'] ?? 'unknown'));
        error_log('EmailService: Invoice type code: ' . ($invoice['invoice_type_code'] ?? 'no code'));
        
        $conditions = [
            'invoice_type' => $invoice['invoice_type'],
            'is_first_invoice' => $this->isFirstInvoice($invoice['client_id'] ?? $invoice['user_id'] ?? null),
            'is_retrocession' => in_array($invoice['invoice_type'], ['retrocession_30', 'retrocession_25']),
            'is_credit_note' => !empty($invoice['reference_invoice_id'])
        ];
        
        // Map DIV type to rental for template selection
        $templateType = $invoice['invoice_type'];
        if ($invoice['invoice_type_code'] === 'DIV' || $invoice['invoice_type'] === 'rental') {
            $templateType = 'rental';
        }
        
        // Get templates matching conditions
        $stmt = $this->db->prepare("
            SELECT * FROM email_templates
            WHERE email_type = 'new_invoice'
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY invoice_type DESC, priority DESC
            LIMIT 1
        ");
        
        $stmt->execute([':invoice_type' => $templateType]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            // Get any active template as fallback
            $stmt = $this->db->prepare("
                SELECT * FROM email_templates
                WHERE email_type = 'new_invoice'
                AND is_active = TRUE
                ORDER BY priority DESC
                LIMIT 1
            ");
            $stmt->execute();
            $template = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($template) {
                error_log('EmailService: Using fallback template: ' . $template['name']);
            }
        } else {
            error_log('EmailService: Found template: ' . $template['name']);
        }
        
        if (!$template) {
            error_log('EmailService: No email template found!');
        }
        
        return $template;
    }
    
    /**
     * Render email template
     */
    private function renderTemplate($template, $data)
    {
        if (!$template) {
            throw new Exception('No email template found');
        }
        
        // Prepare variables
        $variables = $this->prepareTemplateVariables($data);
        
        // Replace variables in subject and body
        $subject = $this->replaceVariables($template['subject'], $variables);
        $bodyHtml = $this->replaceVariables($template['body_html'], $variables);
        $bodyText = $this->replaceVariables($template['body_text'], $variables);
        
        return [
            'subject' => $subject,
            'body_html' => $bodyHtml,
            'body_text' => $bodyText
        ];
    }
    
    /**
     * Prepare template variables
     */
    private function prepareTemplateVariables($data)
    {
        $variables = [];
        
        // Invoice variables
        if (isset($data['invoice_number'])) {
            $variables['INVOICE_NUMBER'] = $data['invoice_number'];
            $variables['ISSUE_DATE'] = date('d/m/Y', strtotime($data['issue_date']));
            $variables['DUE_DATE'] = date('d/m/Y', strtotime($data['due_date']));
            $variables['TOTAL_AMOUNT'] = number_format($data['total'], 2, ',', ' ');
            $variables['PAYMENT_TERMS'] = $data['payment_terms'] ?? '';
            $variables['SUBJECT'] = $data['subject'] ?? '';
            $variables['PERIOD'] = $data['period'] ?? '';
        }
        
        // Client variables
        if (isset($data['client'])) {
            $variables['CLIENT_NAME'] = $data['client']['name'];
            $variables['PRACTITIONER_NAME'] = $data['client']['name']; // Alias for retrocession
        }
        
        // User variables (for user invoices)
        if (isset($data['user'])) {
            $variables['CLIENT_NAME'] = $data['user']['full_name'] ?? $data['user']['name'] ?? ($data['user']['first_name'] . ' ' . $data['user']['last_name']);
            $variables['PRACTITIONER_NAME'] = $variables['CLIENT_NAME']; // Alias
            $variables['USER_NAME'] = $variables['CLIENT_NAME'];
        }
        
        // Retrocession variables
        if (isset($data['retrocession'])) {
            $variables['CNS_AMOUNT'] = number_format($data['retrocession']['cns_amount'], 2, ',', ' ');
            $variables['PATIENT_AMOUNT'] = number_format($data['retrocession']['patient_amount'], 2, ',', ' ');
            $variables['SECRETARIAT_AMOUNT'] = number_format($data['retrocession']['secretariat_tvac'], 2, ',', ' ');
        }
        
        // Period variables
        if (isset($data['period_month']) && isset($data['period_year'])) {
            $variables['MONTH_NAME'] = $this->getMonthName($data['period_month']);
            $variables['YEAR'] = $data['period_year'];
        }
        
        // Voucher variables
        if (isset($data['voucher_number'])) {
            $variables['VOUCHER_NUMBER'] = $data['voucher_number'];
            $variables['VOUCHER_AMOUNT'] = number_format($data['amount'], 2, ',', ' ');
            $variables['EXPIRY_DATE'] = date('d/m/Y', strtotime($data['expiry_date']));
            $variables['BENEFICIARY_NAME'] = $data['beneficiary_name'] ?? '';
        }
        
        // Company variables
        $variables['COMPANY_NAME'] = $this->getConfig('company_name');
        $variables['COMPANY_EMAIL'] = $this->getConfig('company_email');
        $variables['COMPANY_PHONE'] = $this->getConfig('company_phone');
        
        return $variables;
    }
    
    /**
     * Replace variables in text
     */
    private function replaceVariables($text, $variables)
    {
        foreach ($variables as $key => $value) {
            $text = str_replace('{' . $key . '}', $value, $text);
        }
        return $text;
    }
    
    /**
     * Send email
     */
    private function send($params)
    {
        try {
            // Validate parameters
            if (empty($params['to']) || empty($params['subject'])) {
                throw new Exception('Missing required email parameters');
            }
            
            // Get mail configuration from environment
            $host = $_ENV['MAIL_HOST'] ?? 'localhost';
            $port = $_ENV['MAIL_PORT'] ?? 1025;
            $username = $_ENV['MAIL_USERNAME'] ?? '';
            $password = $_ENV['MAIL_PASSWORD'] ?? '';
            $encryption = $_ENV['MAIL_ENCRYPTION'] ?? '';
            $fromEmail = $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>';
            $fromName = $_ENV['MAIL_FROM_NAME'] ?? 'Fit360 AdminDesk';
            
            // Create PHPMailer instance
            $mail = new PHPMailer(true);
            
            try {
                // Log email configuration
                error_log('EmailService: Attempting to send email to: ' . $params['to']);
                error_log('EmailService: SMTP Config - Host: ' . $host . ', Port: ' . $port);
                error_log('EmailService: From: ' . $fromEmail . ' (' . $fromName . ')');
                
                // Server settings
                $mail->isSMTP();
                $mail->Host       = $host;
                $mail->Port       = $port;
                
                // Enable SMTP debugging
                $mail->SMTPDebug = SMTP::DEBUG_SERVER;
                $mail->Debugoutput = function($str, $level) {
                    error_log('EmailService SMTP Debug: ' . $str);
                };
                
                // For Mailhog (local development)
                if ($host === 'localhost' && $port == 1025) {
                    $mail->SMTPAuth   = false;
                    $mail->SMTPSecure = false;
                    $mail->SMTPAutoTLS = false;
                    error_log('EmailService: Using Mailhog configuration (no auth)');
                } else {
                    // For production SMTP
                    $mail->SMTPAuth   = !empty($username);
                    $mail->Username   = $username;
                    $mail->Password   = $password;
                    
                    if ($encryption === 'tls') {
                        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                    } elseif ($encryption === 'ssl') {
                        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                    }
                    error_log('EmailService: Using production SMTP configuration');
                }
                
                // Recipients
                $mail->setFrom($fromEmail, $fromName);
                
                // Handle multiple recipients
                if (strpos($params['to'], ',') !== false) {
                    $recipients = explode(',', $params['to']);
                    foreach ($recipients as $recipient) {
                        $mail->addAddress(trim($recipient));
                    }
                } else {
                    $mail->addAddress($params['to']);
                }
                
                // Reply-To
                if (!empty($params['reply_to'])) {
                    $mail->addReplyTo($params['reply_to']);
                }
                
                // Attachments
                if (!empty($params['attachments']) && is_array($params['attachments'])) {
                    foreach ($params['attachments'] as $attachment) {
                        if (!empty($attachment['content']) && !empty($attachment['name'])) {
                            // Save attachment to temp file
                            $tempFile = tempnam(sys_get_temp_dir(), 'mail_attachment_');
                            file_put_contents($tempFile, $attachment['content']);
                            
                            // Add to email
                            $mail->addAttachment($tempFile, $attachment['name'], 'base64', $attachment['type'] ?? 'application/octet-stream');
                            
                            // Register temp file for cleanup
                            register_shutdown_function(function() use ($tempFile) {
                                if (file_exists($tempFile)) {
                                    unlink($tempFile);
                                }
                            });
                        }
                    }
                }
                
                // Content
                $mail->CharSet = 'UTF-8';
                $mail->Subject = $params['subject'];
                
                if (!empty($params['body_html'])) {
                    $mail->isHTML(true);
                    $mail->Body = $params['body_html'];
                    $mail->AltBody = $params['body_text'] ?? strip_tags($params['body_html']);
                } else {
                    $mail->isHTML(false);
                    $mail->Body = $params['body_text'] ?? '';
                }
                
                // Send email
                error_log('EmailService: Attempting to send email...');
                $mail->send();
                
                error_log('EmailService: Email sent successfully to ' . $params['to']);
                
                return [
                    'success' => true, 
                    'message' => 'Email sent successfully' . ($host === 'localhost' && $port == 1025 ? ' to Mailhog' : ''),
                    'subject' => $params['subject'] ?? 'Invoice Email',
                    'body_text' => $params['body_text'] ?? '',
                    'body_html' => $params['body_html'] ?? '',
                    'attachments' => $params['attachments'] ?? []
                ];
                
            } catch (\PHPMailer\PHPMailer\Exception $e) {
                error_log('EmailService: PHPMailer Exception - ' . $e->getMessage());
                error_log('EmailService: PHPMailer ErrorInfo - ' . $mail->ErrorInfo);
                throw new Exception('PHPMailer Error: ' . $mail->ErrorInfo);
            }
            
        } catch (Exception $e) {
            return [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => $params['subject'] ?? 'Invoice Email'
            ];
        }
    }
    
    /**
     * Log email
     */
    private function logEmail($invoiceId, $templateId, $recipient, $result)
    {
        try {
            // Prepare body preview (first 500 chars of the email body)
            $bodyPreview = '';
            if (isset($result['body_text'])) {
                $bodyPreview = substr($result['body_text'], 0, 500);
            } elseif (isset($result['body_html'])) {
                $bodyPreview = substr(strip_tags($result['body_html']), 0, 500);
            }
            
            // Prepare attachments info
            $attachmentsSent = [];
            if (isset($result['attachments'])) {
                foreach ($result['attachments'] as $attachment) {
                    $attachmentsSent[] = [
                        'name' => $attachment['name'] ?? 'attachment',
                        'type' => $attachment['type'] ?? 'application/octet-stream'
                    ];
                }
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO email_logs (
                    invoice_id, template_id, recipient_type, recipient_email,
                    subject, body_preview, attachments_sent, status, 
                    sent_at, error_message, created_at
                ) VALUES (
                    :invoice_id, :template_id, :recipient_type, :recipient_email,
                    :subject, :body_preview, :attachments_sent, :status,
                    :sent_at, :error_message, NOW()
                )
            ");
            
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':template_id' => $templateId,
                ':recipient_type' => 'primary', // Default to primary recipient
                ':recipient_email' => $recipient,
                ':subject' => $result['subject'] ?? 'Invoice Email',
                ':body_preview' => $bodyPreview,
                ':attachments_sent' => !empty($attachmentsSent) ? json_encode($attachmentsSent) : null,
                ':status' => $result['success'] ? 'sent' : 'failed',
                ':sent_at' => $result['success'] ? date('Y-m-d H:i:s') : null,
                ':error_message' => $result['success'] ? null : ($result['message'] ?? 'Unknown error')
            ]);
            
            error_log('EmailService: Email log created for invoice ' . $invoiceId . ', status: ' . ($result['success'] ? 'sent' : 'failed'));
        } catch (Exception $e) {
            error_log('EmailService: Failed to log email - ' . $e->getMessage());
        }
    }
    
    /**
     * Log payment reminder
     */
    private function logPaymentReminder($invoiceId, $reminderLevel, $result)
    {
        $stmt = $this->db->prepare("
            UPDATE payment_reminders
            SET status = :status,
                sent_date = :sent_date
            WHERE invoice_id = :invoice_id
            AND reminder_level = :reminder_level
            AND status = 'scheduled'
        ");
        
        $stmt->execute([
            ':status' => $result['success'] ? 'sent' : 'failed',
            ':sent_date' => $result['success'] ? date('Y-m-d H:i:s') : null,
            ':invoice_id' => $invoiceId,
            ':reminder_level' => $reminderLevel
        ]);
    }
    
    /**
     * Check if first invoice for client/user
     */
    private function isFirstInvoice($entityId)
    {
        if (!$entityId) {
            return false;
        }
        
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count
            FROM invoices
            WHERE (client_id = :entity_id OR user_id = :entity_id)
            AND status != 'draft'
        ");
        
        $stmt->execute([':entity_id' => $entityId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['count'] == 0;
    }
    
    /**
     * Get email template
     */
    private function getEmailTemplate($code, $invoiceType = null)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM email_templates
            WHERE code = :code
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY invoice_type DESC
            LIMIT 1
        ");
        
        $stmt->execute([
            ':code' => $code,
            ':invoice_type' => $invoiceType
        ]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get config value
     */
    private function getConfig($key)
    {
        $stmt = $this->db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        return $stmt->fetch(PDO::FETCH_COLUMN) ?: '';
    }
    
    /**
     * Get month name in French
     */
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
}