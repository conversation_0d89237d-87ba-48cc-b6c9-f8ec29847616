<?php
/**
 * Test Dashboard Error
 * Run this file to see what's causing the dashboard error
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Testing Dashboard Error</h1>\n";

// Define APP_PATH
define('APP_PATH', dirname(__DIR__));
echo "APP_PATH: " . APP_PATH . "<br>\n";

// Check vendor autoload
echo "Checking vendor autoload...<br>\n";
if (!file_exists(APP_PATH . '/vendor/autoload.php')) {
    die('ERROR: Vendor autoload not found. Please run composer install.');
}
require APP_PATH . '/vendor/autoload.php';
echo "✓ Vendor autoload loaded<br>\n";

// Load .env
echo "Loading .env file...<br>\n";
if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
    echo "✓ .env file loaded<br>\n";
} else {
    echo "⚠ .env file not found<br>\n";
}

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "✓ Session started<br>\n";

// Check bootstrap
echo "Checking bootstrap file...<br>\n";
if (!file_exists(APP_PATH . '/app/config/bootstrap.php')) {
    die('ERROR: Bootstrap file not found.');
}

// Try to load bootstrap
echo "Loading bootstrap...<br>\n";
try {
    require APP_PATH . '/app/config/bootstrap.php';
    echo "✓ Bootstrap loaded successfully<br>\n";
} catch (Exception $e) {
    echo "✗ Bootstrap error: " . $e->getMessage() . "<br>\n";
    echo "File: " . $e->getFile() . "<br>\n";
    echo "Line: " . $e->getLine() . "<br>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    die();
}

// Test Flight framework
echo "Testing Flight framework...<br>\n";
try {
    // Check if Flight is loaded
    if (!class_exists('Flight')) {
        echo "✗ Flight class not found<br>\n";
    } else {
        echo "✓ Flight class loaded<br>\n";
        
        // Check database connection
        echo "Testing database connection...<br>\n";
        $db = Flight::db();
        if ($db) {
            echo "✓ Database connection successful<br>\n";
            
            // Test a simple query
            $stmt = $db->query("SELECT 1");
            if ($stmt) {
                echo "✓ Database query successful<br>\n";
            }
        }
    }
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "<br>\n";
}

// Check if DashboardController exists
echo "Checking DashboardController...<br>\n";
if (class_exists('\App\Controllers\DashboardController')) {
    echo "✓ DashboardController class exists<br>\n";
    
    try {
        $controller = new \App\Controllers\DashboardController();
        echo "✓ DashboardController instantiated<br>\n";
    } catch (Exception $e) {
        echo "✗ DashboardController error: " . $e->getMessage() . "<br>\n";
    }
} else {
    echo "✗ DashboardController class not found<br>\n";
}

echo "<br><h2>Summary:</h2>\n";
echo "If all checks passed, try accessing <a href='/fit/public/'>the main dashboard</a> again.<br>\n";
echo "If errors persist, check the error messages above.<br>\n";