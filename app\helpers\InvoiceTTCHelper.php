<?php

namespace App\Helpers;

class InvoiceTTCHelper
{
    /**
     * Calculate VAT amount from TTC price
     * 
     * @param float $ttcAmount The TTC (tax included) amount
     * @param float $vatRate The VAT rate percentage (e.g., 17 for 17%)
     * @return array Array with htAmount and vatAmount
     */
    public static function extractVatFromTTC($ttcAmount, $vatRate)
    {
        // Formula: HT = TTC / (1 + VAT_RATE/100)
        // VAT = TTC - HT
        
        $htAmount = $ttcAmount / (1 + ($vatRate / 100));
        $vatAmount = $ttcAmount - $htAmount;
        
        return [
            "htAmount" => round($htAmount, 2),
            "vatAmount" => round($vatAmount, 2),
            "ttcAmount" => round($ttcAmount, 2)
        ];
    }
    
    /**
     * Check if invoice type should use TTC calculation
     * 
     * @param string $invoiceType The invoice type or number
     * @return bool
     */
    public static function shouldUseTTC($invoiceType)
    {
        // Check if this is a LOC invoice by type or number
        return (strpos($invoiceType, "LOC") !== false || strpos($invoiceType, "LOY") !== false);
    }
}
