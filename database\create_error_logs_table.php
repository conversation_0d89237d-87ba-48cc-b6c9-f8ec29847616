#!/usr/bin/env php
<?php
/**
 * Command-line script to create error_logs table
 * Run: php database/create_error_logs_table.php
 */

// Ensure we're running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line\n");
}

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$username = $_ENV['DB_USER'];
$password = $_ENV['DB_PASS'];
$database = $_ENV['DB_NAME'];

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    
    echo "✓ Connected to database successfully.\n";
    
    // Check if error_logs table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'error_logs'");
    if ($result->num_rows > 0) {
        echo "✓ error_logs table already exists\n";
        exit(0);
    }
    
    echo "× error_logs table does NOT exist\n";
    echo "Creating error_logs table...\n";
    
    // Execute the migration directly
    $sql = "-- Create error logs table for comprehensive error tracking
CREATE TABLE IF NOT EXISTS error_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    error_id VARCHAR(50) NOT NULL UNIQUE,
    timestamp DATETIME NOT NULL,
    environment VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    code INT DEFAULT 0,
    file VARCHAR(500) NOT NULL,
    line INT NOT NULL,
    class VARCHAR(255) NOT NULL,
    trace TEXT,
    request_method VARCHAR(10),
    request_uri VARCHAR(500),
    user_agent VARCHAR(500),
    ip_address VARCHAR(45),
    user_id INT,
    username VARCHAR(100),
    execution_time DECIMAL(10,3),
    memory_usage DECIMAL(10,2),
    memory_peak DECIMAL(10,2),
    request_headers TEXT,
    request_params TEXT,
    session_data TEXT,
    resolved BOOLEAN DEFAULT FALSE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_timestamp (timestamp),
    INDEX idx_environment (environment),
    INDEX idx_user_id (user_id),
    INDEX idx_error_class (class),
    INDEX idx_resolved (resolved),
    INDEX idx_request_uri (request_uri),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    // Execute the migration
    if ($mysqli->query($sql) === TRUE) {
        echo "✓ error_logs table created successfully\n";
        
        // Mark migration as applied
        $stmt = $mysqli->prepare("INSERT IGNORE INTO migrations (migration, applied_at) VALUES (?, NOW())");
        $migrationName = '109_create_error_logs_table.sql';
        $stmt->bind_param('s', $migrationName);
        
        if ($stmt->execute()) {
            echo "✓ Migration marked as applied\n";
        } else {
            echo "Warning: Could not mark migration as applied: " . $stmt->error . "\n";
        }
        $stmt->close();
    } else {
        throw new Exception("Error creating table: " . $mysqli->error);
    }
    
    // Test error logging
    echo "\nTesting error logging...\n";
    
    // Create a test error entry
    $testErrorId = 'CLI_TEST_' . uniqid();
    $stmt = $mysqli->prepare("
        INSERT INTO error_logs (
            error_id, timestamp, environment, message, 
            code, file, line, class, trace,
            request_method, request_uri, user_agent, ip_address
        ) VALUES (
            ?, NOW(), 'cli', 'Test error log entry from CLI',
            0, ?, 1, 'CLI', 'CLI test trace',
            'CLI', '/cli/test', 'PHP CLI', '127.0.0.1'
        )
    ");
    
    $testFile = __FILE__;
    $stmt->bind_param('ss', $testErrorId, $testFile);
    
    if ($stmt->execute()) {
        echo "✓ Test error log entry created successfully (ID: $testErrorId)\n";
        
        // Verify the entry
        $result = $mysqli->query("SELECT * FROM error_logs WHERE error_id = '$testErrorId'");
        if ($result->num_rows > 0) {
            echo "✓ Test entry verified in database\n";
            
            // Clean up test entry
            $mysqli->query("DELETE FROM error_logs WHERE error_id = '$testErrorId'");
            echo "✓ Test entry cleaned up\n";
        }
    } else {
        echo "× Could not create test entry: " . $stmt->error . "\n";
    }
    $stmt->close();
    
    // Show final status
    echo "\n=== Final Status ===\n";
    $result = $mysqli->query("SELECT COUNT(*) as count FROM error_logs");
    $row = $result->fetch_assoc();
    echo "Total error logs in table: " . $row['count'] . "\n";
    
    $mysqli->close();
    echo "\n✓ All operations completed successfully!\n";
    echo "The error_logs table is now ready to receive error entries.\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}