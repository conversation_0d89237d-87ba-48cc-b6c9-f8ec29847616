<?php
// Reset sequence to 185 after deleting invoice 187

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Reset Invoice Sequence to 185</h2>";
    
    // Check if invoice 187 still exists
    $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number LIKE '%0187'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "<strong>Warning:</strong> Invoice with number ending in 0187 still exists!<br><br>";
    } else {
        echo "✓ Invoice 0187 has been deleted<br><br>";
    }
    
    // Get current sequence
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current sequence: <strong>" . ($sequence['last_number'] ?? 'Not found') . "</strong><br>";
    
    // Reset sequence to 185
    $stmt = $db->prepare("
        UPDATE document_sequences 
        SET last_number = 185 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $stmt->execute();
    echo "✓ Reset sequence to <strong>185</strong><br>";
    echo "<h3 style='color: #28a745;'>Next invoice will be: 0186</h3>";
    
    // Add 187 to deleted pool since it was used
    try {
        $stmt = $db->prepare("
            INSERT INTO deleted_invoice_numbers (
                document_type_id, invoice_number, year, sequence_number, deleted_by
            ) VALUES (1, 'FAC-2025-0187', 2025, 187, 1)
        ");
        $stmt->execute();
        echo "✓ Added FAC-2025-0187 to deleted numbers pool<br>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate') !== false) {
            echo "! Number 0187 already in deleted pool<br>";
        } else {
            echo "! Error adding to deleted pool: " . $e->getMessage() . "<br>";
        }
    }
    
    // Show next invoice preview
    echo "<h3>Next Invoice Preview</h3>";
    echo "<p>When you create your next invoice:</p>";
    echo "<ul>";
    echo "<li>If you select <strong>Loyer</strong> type: <span style='font-size: 18px; color: #007bff;'>FAC-LOY-2025-0186</span></li>";
    echo "<li>If you select <strong>Rétrocession</strong> type: <span style='font-size: 18px; color: #007bff;'>FAC-RET-2025-0186</span></li>";
    echo "<li>If you select <strong>Divers</strong> type: <span style='font-size: 18px; color: #007bff;'>FAC-DIV-2025-0186</span></li>";
    echo "<li>If no type selected: <span style='font-size: 18px; color: #007bff;'>FAC-2025-0186</span></li>";
    echo "</ul>";
    
    echo "<br><strong>Important:</strong> Make sure to select the correct invoice type when creating the invoice!<br><br>";
    
    echo '<a href="/fit/public/invoices/create" style="font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">Create New Invoice</a>';
    echo '<a href="check_invoice_sequences.php" style="font-size: 16px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;">Check Sequences</a>';
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>