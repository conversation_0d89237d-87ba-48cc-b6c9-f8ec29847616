<?php
/**
 * Migration Runner Class
 * Handles both SQL and PHP migrations
 */

namespace Database;

use PDO;
use Exception;

class MigrationRunner
{
    private PDO $pdo;
    private string $migrationsPath;
    private string $migrationsTable = 'migrations';
    private bool $isCLI;
    
    public function __construct(PDO $pdo, string $migrationsPath)
    {
        $this->pdo = $pdo;
        $this->migrationsPath = $migrationsPath;
        $this->isCLI = php_sapi_name() === 'cli';
        
        $this->createMigrationsTable();
    }
    
    /**
     * Create migrations table if it doesn't exist
     */
    private function createMigrationsTable(): void
    {
        $this->pdo->exec("
            CREATE TABLE IF NOT EXISTS `{$this->migrationsTable}` (
                `id` INT AUTO_INCREMENT PRIMARY KEY,
                `migration` VARCHAR(255) NOT NULL,
                `batch` INT NOT NULL,
                `executed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY `migration_unique` (`migration`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
        ");
    }
    
    /**
     * Get list of executed migrations
     */
    public function getExecutedMigrations(): array
    {
        $stmt = $this->pdo->query("SELECT migration FROM `{$this->migrationsTable}` ORDER BY id");
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get all migration files (both .sql and .php)
     */
    public function getAllMigrations(): array
    {
        $sqlFiles = glob($this->migrationsPath . '/*.sql');
        $phpFiles = glob($this->migrationsPath . '/*.php');
        
        $allFiles = array_merge($sqlFiles, $phpFiles);
        
        // Sort by filename to ensure order
        usort($allFiles, function($a, $b) {
            return strcmp(basename($a), basename($b));
        });
        
        return $allFiles;
    }
    
    /**
     * Get pending migrations
     */
    public function getPendingMigrations(): array
    {
        $executed = $this->getExecutedMigrations();
        $all = $this->getAllMigrations();
        
        $pending = [];
        foreach ($all as $file) {
            $filename = basename($file);
            if (!in_array($filename, $executed)) {
                $pending[] = $file;
            }
        }
        
        return $pending;
    }
    
    /**
     * Run a single migration
     */
    public function runMigration(string $file, int $batch): bool
    {
        $filename = basename($file);
        $extension = pathinfo($file, PATHINFO_EXTENSION);
        
        $this->output("Running migration: $filename", 'info');
        
        try {
            $this->pdo->beginTransaction();
            
            if ($extension === 'sql') {
                $this->runSqlMigration($file);
            } elseif ($extension === 'php') {
                $this->runPhpMigration($file);
            } else {
                throw new Exception("Unknown migration type: $extension");
            }
            
            // Record the migration
            $stmt = $this->pdo->prepare("INSERT INTO `{$this->migrationsTable}` (migration, batch) VALUES (?, ?)");
            $stmt->execute([$filename, $batch]);
            
            $this->pdo->commit();
            $this->output("✓ $filename completed successfully", 'success');
            
            return true;
            
        } catch (Exception $e) {
            $this->pdo->rollback();
            $this->output("✗ Error in $filename: " . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * Run SQL migration
     */
    private function runSqlMigration(string $file): void
    {
        $sql = file_get_contents($file);
        
        // Remove comments
        $sql = preg_replace('/^\s*--.*$/m', '', $sql);
        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
        
        // Split statements (handle procedures/functions)
        $statements = $this->splitSqlStatements($sql);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $this->pdo->exec($statement);
            }
        }
    }
    
    /**
     * Run PHP migration
     */
    private function runPhpMigration(string $file): void
    {
        // Include the migration file
        $migration = require $file;
        
        // If it returns a migration instance, run it
        if (is_object($migration) && method_exists($migration, 'up')) {
            $migration->up($this->pdo);
        } elseif (is_callable($migration)) {
            // If it returns a callable, execute it
            $migration($this->pdo);
        } else {
            throw new Exception("Invalid PHP migration format");
        }
    }
    
    /**
     * Split SQL statements intelligently
     */
    private function splitSqlStatements(string $sql): array
    {
        // Handle DELIMITER statements for procedures
        if (stripos($sql, 'DELIMITER') !== false) {
            return $this->splitWithDelimiter($sql);
        }
        
        // Simple split by semicolon
        return preg_split('/;\s*$/m', $sql);
    }
    
    /**
     * Split SQL with custom delimiter handling
     */
    private function splitWithDelimiter(string $sql): array
    {
        $statements = [];
        $lines = explode("\n", $sql);
        $delimiter = ';';
        $statement = '';
        
        foreach ($lines as $line) {
            $line = trim($line);
            
            if (stripos($line, 'DELIMITER') === 0) {
                // Change delimiter
                $delimiter = trim(str_ireplace('DELIMITER', '', $line));
                continue;
            }
            
            if (substr($line, -strlen($delimiter)) === $delimiter) {
                // End of statement
                $statement .= substr($line, 0, -strlen($delimiter));
                $statements[] = trim($statement);
                $statement = '';
            } else {
                $statement .= $line . "\n";
            }
        }
        
        if (!empty(trim($statement))) {
            $statements[] = trim($statement);
        }
        
        return $statements;
    }
    
    /**
     * Run all pending migrations
     */
    public function runPendingMigrations(): int
    {
        $pending = $this->getPendingMigrations();
        
        if (empty($pending)) {
            $this->output("All migrations are up to date!", 'success');
            return 0;
        }
        
        $this->output("Running " . count($pending) . " pending migration(s)...", 'info');
        
        // Get next batch number
        $batch = $this->pdo->query("SELECT COALESCE(MAX(batch), 0) + 1 FROM `{$this->migrationsTable}`")->fetchColumn();
        
        $successful = 0;
        foreach ($pending as $file) {
            if ($this->runMigration($file, $batch)) {
                $successful++;
            } else {
                // Stop on first error
                break;
            }
        }
        
        $this->output("Completed $successful migration(s)", 'success');
        return $successful;
    }
    
    /**
     * Rollback last batch of migrations
     */
    public function rollbackLastBatch(): int
    {
        // Get last batch
        $lastBatch = $this->pdo->query("SELECT MAX(batch) FROM `{$this->migrationsTable}`")->fetchColumn();
        
        if (!$lastBatch) {
            $this->output("No migrations to rollback", 'warning');
            return 0;
        }
        
        // Get migrations in last batch
        $stmt = $this->pdo->prepare("SELECT migration FROM `{$this->migrationsTable}` WHERE batch = ? ORDER BY id DESC");
        $stmt->execute([$lastBatch]);
        $migrations = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $this->output("Rolling back " . count($migrations) . " migration(s) from batch $lastBatch...", 'info');
        
        $successful = 0;
        foreach ($migrations as $filename) {
            $file = $this->migrationsPath . '/' . $filename;
            
            if (!file_exists($file)) {
                $this->output("Migration file not found: $filename", 'error');
                continue;
            }
            
            try {
                $this->pdo->beginTransaction();
                
                $extension = pathinfo($file, PATHINFO_EXTENSION);
                
                if ($extension === 'php') {
                    $migration = require $file;
                    
                    if (is_object($migration) && method_exists($migration, 'down')) {
                        $migration->down($this->pdo);
                    } else {
                        $this->output("No rollback method for: $filename", 'warning');
                    }
                }
                // SQL migrations don't support rollback
                
                // Remove from migrations table
                $stmt = $this->pdo->prepare("DELETE FROM `{$this->migrationsTable}` WHERE migration = ?");
                $stmt->execute([$filename]);
                
                $this->pdo->commit();
                $this->output("✓ Rolled back: $filename", 'success');
                $successful++;
                
            } catch (Exception $e) {
                $this->pdo->rollback();
                $this->output("✗ Error rolling back $filename: " . $e->getMessage(), 'error');
            }
        }
        
        return $successful;
    }
    
    /**
     * Output message
     */
    private function output(string $message, string $type = 'info'): void
    {
        if ($this->isCLI) {
            $prefix = match($type) {
                'success' => "\033[32m✓\033[0m ",
                'error' => "\033[31m✗\033[0m ",
                'warning' => "\033[33m!\033[0m ",
                default => "\033[36mi\033[0m "
            };
            echo $prefix . $message . PHP_EOL;
        } else {
            $class = match($type) {
                'success' => 'success',
                'error' => 'error',
                'warning' => 'warning',
                default => 'info'
            };
            echo "<div class='message $class'>$message</div>";
        }
    }
}