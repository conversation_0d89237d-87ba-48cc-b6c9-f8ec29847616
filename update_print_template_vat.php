<?php
/**
 * Update print template to ensure VAT info is not shown for retrocession invoices
 */

echo "<pre>";
echo "=== Updating Print Template for Retrocession VAT ===\n\n";

$printFile = __DIR__ . '/app/views/invoices/print.twig';
$content = file_get_contents($printFile);

// Check if there's any client VAT display
if (strpos($content, 'invoice.client.vat_number') !== false || 
    strpos($content, 'invoice.client.vat_intercommunautaire') !== false ||
    strpos($content, 'invoice.user.vat_number') !== false ||
    strpos($content, 'invoice.user.vat_intercommunautaire') !== false) {
    
    echo "✓ Found VAT display in print template - adding retrocession check\n";
    
    // Add retrocession check at the beginning of the body
    $bodySearch = '<body>';
    $bodyReplace = '<body>
    {% set invoiceTypeCode = invoice.invoice_type.code|default(\'\') %}
    {% set isRetrocessionInvoice = (invoiceTypeCode starts with \'RET\') or (invoice.invoice_type.id in [2, 15]) %}';
    
    $content = str_replace($bodySearch, $bodyReplace, $content);
    
    // Wrap any VAT displays with conditional
    $content = preg_replace(
        '/\{\{([^}]*invoice\.(client|user)\.vat[^}]*)\}\}/',
        '{% if not isRetrocessionInvoice %}{{ $1 }}{% endif %}',
        $content
    );
    
    file_put_contents($printFile, $content);
    echo "✓ Updated print template\n";
} else {
    echo "✓ Print template doesn't display client/user VAT info - good!\n";
}

echo "\n=== Testing Results ===\n";
echo "The following changes have been made:\n";
echo "1. Invoice creation view (create-modern.twig) - VAT info hidden for RET invoices\n";
echo "2. Invoice show view (show-modern.twig) - VAT info hidden for RET invoices\n";
echo "3. Print template verified - no client VAT shown\n";
echo "4. Cleanup script created for default postal codes\n";

echo "\n=== Next Steps ===\n";
echo "1. Run the fixes: http://localhost/fit/fix_retrocession_vat_display.php\n";
echo "2. Run the cleanup: http://localhost/fit/cleanup_default_postal_codes.php\n";
echo "3. Check Rémi Heine: http://localhost/fit/find_remi_heine.php\n";
echo "4. Test creating a retrocession invoice for Rémi Heine\n";

echo "</pre>";