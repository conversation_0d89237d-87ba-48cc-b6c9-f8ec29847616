<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CHECKING INVOICE SEQUENCES ===\n\n";
    
    // Check document types
    echo "1. Document Types:\n";
    $stmt = $pdo->query("SELECT id, code, name, prefix, counter_type, number_format FROM document_types WHERE code = 'invoice'");
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($docType) {
        echo "Invoice Document Type:\n";
        echo "- ID: {$docType['id']}\n";
        echo "- Code: {$docType['code']}\n";
        echo "- Name: {$docType['name']}\n";
        echo "- Prefix: {$docType['prefix']}\n";
        echo "- Counter Type: {$docType['counter_type']}\n";
        echo "- Number Format: {$docType['number_format']}\n";
    }
    
    echo "\n2. Document Sequences:\n";
    $stmt = $pdo->prepare("SELECT * FROM document_sequences WHERE document_type_id = ? ORDER BY year DESC, month DESC");
    $stmt->execute([$docType['id']]);
    $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo "- Year: {$seq['year']}, Month: " . ($seq['month'] ?? 'N/A') . ", Last Number: {$seq['last_number']}\n";
    }
    
    echo "\n3. Existing Invoices:\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM invoices WHERE document_type_id = ?");
    $stmt->execute([$docType['id']]);
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "Total invoices with this document type: {$count['count']}\n";
    
    // Check latest invoices
    echo "\n4. Latest Invoice Numbers:\n";
    $stmt = $pdo->prepare("SELECT invoice_number, issue_date FROM invoices WHERE document_type_id = ? ORDER BY id DESC LIMIT 5");
    $stmt->execute([$docType['id']]);
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($invoices as $inv) {
        echo "- {$inv['invoice_number']} (Date: {$inv['issue_date']})\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}