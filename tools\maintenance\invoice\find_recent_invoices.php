<?php
// Find recent invoices to see what's in the database

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Find Recent Invoices</h2>";
    
    // 1. Show ALL invoices
    echo "<h3>All Invoices (Most Recent First)</h3>";
    $stmt = $db->query("
        SELECT i.*, it.name as type_name, it.prefix as type_prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        ORDER BY i.id DESC
        LIMIT 20
    ");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invoices) > 0) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Invoice Number</th><th>Type ID</th><th>Type Name</th><th>Type Prefix</th><th>Status</th><th>Total</th><th>Created</th><th>Action</th>";
        echo "</tr>";
        foreach ($invoices as $inv) {
            $rowStyle = '';
            if (strpos($inv['invoice_number'], '0186') !== false) {
                $rowStyle = "background: #e8f5e9;"; // Green background for 0186
            } elseif (strpos($inv['invoice_number'], '0187') !== false) {
                $rowStyle = "background: #fff3cd;"; // Yellow background for 0187
            }
            
            echo "<tr style='$rowStyle'>";
            echo "<td>" . $inv['id'] . "</td>";
            echo "<td><strong>" . $inv['invoice_number'] . "</strong></td>";
            echo "<td>" . ($inv['type_id'] ?? 'NULL') . "</td>";
            echo "<td>" . ($inv['type_name'] ?? 'No type') . "</td>";
            echo "<td><strong>" . ($inv['type_prefix'] ?? 'None') . "</strong></td>";
            echo "<td>" . $inv['status'] . "</td>";
            echo "<td>" . $inv['total'] . " " . $inv['currency'] . "</td>";
            echo "<td>" . $inv['created_at'] . "</td>";
            echo "<td>";
            if ($inv['status'] == 'draft') {
                echo "<a href='fix_specific_invoice.php?id=" . $inv['id'] . "' style='color: blue;'>Fix</a>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Highlight the invoice from your screenshot
        echo "<h3>Analysis</h3>";
        foreach ($invoices as $inv) {
            if (strpos($inv['invoice_number'], '187') !== false) {
                echo "<p>Found invoice with 187: <strong>" . $inv['invoice_number'] . "</strong> (ID: " . $inv['id'] . ")</p>";
                if ($inv['invoice_number'] == 'FAC-LOY-2025-0187') {
                    echo "<p style='color: orange;'>⚠️ This invoice already has LOY prefix but should be 0186 instead!</p>";
                }
            }
            if (strpos($inv['invoice_number'], '186') !== false) {
                echo "<p>Found invoice with 186: <strong>" . $inv['invoice_number'] . "</strong> (ID: " . $inv['id'] . ")</p>";
            }
        }
        
    } else {
        echo "<p>No invoices found in the database.</p>";
    }
    
    // 2. Check current sequence
    echo "<h3>Current Sequence Status</h3>";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $seq = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($seq) {
        echo "<p>Current sequence number: <strong>" . $seq['last_number'] . "</strong></p>";
        echo "<p>Next invoice will be: <strong>" . ($seq['last_number'] + 1) . "</strong></p>";
    }
    
    // 3. Quick fix options
    echo "<h3>Quick Actions</h3>";
    if (count($invoices) > 0) {
        $latestInvoice = $invoices[0];
        if ($latestInvoice['status'] == 'draft') {
            echo "<form method='POST'>";
            echo "<p>Latest invoice: <strong>" . $latestInvoice['invoice_number'] . "</strong></p>";
            echo "<p>Change to: <input type='text' name='new_number' value='FAC-LOY-2025-0186' style='font-size: 16px; padding: 5px;'></p>";
            echo "<input type='hidden' name='invoice_id' value='" . $latestInvoice['id'] . "'>";
            echo "<button type='submit' name='action' value='update' style='font-size: 16px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;'>Update Invoice Number</button>";
            echo "</form>";
        }
    }
    
    // Handle form submission
    if (isset($_POST['action']) && $_POST['action'] == 'update') {
        $newNumber = $_POST['new_number'];
        $invoiceId = $_POST['invoice_id'];
        
        // Check if new number already exists
        $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ? AND id != ?");
        $stmt->execute([$newNumber, $invoiceId]);
        if ($stmt->fetch()) {
            echo "<p style='color: red;'>Error: Invoice number $newNumber already exists!</p>";
        } else {
            $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
            $stmt->execute([$newNumber, $invoiceId]);
            echo "<h3 style='color: green;'>✓ Updated invoice to: $newNumber</h3>";
            echo "<script>setTimeout(() => location.reload(), 1000);</script>";
        }
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>