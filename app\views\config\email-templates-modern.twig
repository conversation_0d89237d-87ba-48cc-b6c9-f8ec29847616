{% extends "base-modern.twig" %}

{% block title %}{{ __('config.email_templates') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.email_templates') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#variablesModal">
                <i class="bi bi-code-slash me-2"></i>{{ __('config.template_variables') }}
            </button>
            <button type="submit" form="emailTemplateForm" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.email_templates_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div class="row">
        <!-- Template List -->
        <div class="col-md-4">
            <div class="card shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-envelope me-2"></i>{{ __('config.available_templates') }}</h6>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for template in templates %}
                        <a href="#" class="list-group-item list-group-item-action {{ current_template.id == template.id ? 'active' : '' }}"
                           onclick="loadTemplate({{ template.id }})">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ template.name }}</h6>
                                    <small class="{{ current_template.id == template.id ? 'text-white-50' : 'text-muted' }}">
                                        {{ template.description }}
                                    </small>
                                </div>
                                {% if template.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% endif %}
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Template Editor -->
        <div class="col-md-8">
            {% if current_template %}
            <form id="emailTemplateForm" method="POST" action="{{ base_url }}/config/email-templates/{{ current_template.id }}" class="needs-validation" novalidate>
                <input type="hidden" name="_method" value="PUT">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="card shadow-sm mb-3">
                    <div class="card-header bg-white">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#content-tab" role="tab">
                                    <i class="bi bi-file-text me-2"></i>{{ __('config.content') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#preview-tab" role="tab">
                                    <i class="bi bi-eye me-2"></i>{{ __('common.preview') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#settings-tab" role="tab">
                                    <i class="bi bi-gear me-2"></i>{{ __('common.settings') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- Content Tab -->
                            <div class="tab-pane fade show active" id="content-tab" role="tabpanel">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">{{ __('config.email_subject') }} *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="{{ current_template.subject }}" required>
                                    <small class="text-muted">{{ __('config.subject_variables_hint') }}</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="body" class="form-label">{{ __('config.email_body') }} *</label>
                                    <textarea class="form-control" id="body" name="body" rows="15" required>{{ current_template.body }}</textarea>
                                    <small class="text-muted">{{ __('config.body_html_hint') }}</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="footer" class="form-label">{{ __('config.email_footer') }}</label>
                                    <textarea class="form-control" id="footer" name="footer" rows="3">{{ current_template.footer }}</textarea>
                                </div>
                            </div>
                            
                            <!-- Preview Tab -->
                            <div class="tab-pane fade" id="preview-tab" role="tabpanel">
                                <div class="border rounded p-3">
                                    <div class="mb-3">
                                        <strong>{{ __('config.subject') }}:</strong>
                                        <div id="previewSubject" class="text-muted">{{ current_template.subject }}</div>
                                    </div>
                                    <hr>
                                    <div id="previewBody">
                                        {{ current_template.body|raw }}
                                    </div>
                                    {% if current_template.footer %}
                                    <hr>
                                    <div id="previewFooter" class="text-muted small">
                                        {{ current_template.footer|raw }}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div class="mt-3">
                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="sendTestEmail()">
                                        <i class="bi bi-send me-2"></i>{{ __('config.send_test_email') }}
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Settings Tab -->
                            <div class="tab-pane fade" id="settings-tab" role="tabpanel">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="language" class="form-label">{{ __('config.template_language') }}</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="fr" {{ current_template.language == 'fr' ? 'selected' : '' }}>Français</option>
                                            <option value="en" {{ current_template.language == 'en' ? 'selected' : '' }}>English</option>
                                            <option value="de" {{ current_template.language == 'de' ? 'selected' : '' }}>Deutsch</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="template_type" class="form-label">{{ __('config.template_type') }}</label>
                                        <input type="text" class="form-control" id="template_type" value="{{ current_template.type }}" disabled>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="form-check form-switch">
                                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                                   value="1" {{ current_template.is_active ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                {{ __('config.template_active') }}
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="form-check form-switch">
                                            <input type="checkbox" class="form-check-input" id="include_logo" name="include_logo" 
                                                   value="1" {{ current_template.include_logo ? 'checked' : '' }}>
                                            <label class="form-check-label" for="include_logo">
                                                {{ __('config.include_company_logo') }}
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="col-12">
                                        <div class="form-check form-switch">
                                            <input type="checkbox" class="form-check-input" id="attach_invoice" name="attach_invoice" 
                                                   value="1" {{ current_template.attach_invoice ? 'checked' : '' }}>
                                            <label class="form-check-label" for="attach_invoice">
                                                {{ __('config.attach_invoice_pdf') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer bg-white">
                        <button type="button" class="btn btn-outline-danger" onclick="restoreDefault()">
                            <i class="bi bi-arrow-counterclockwise me-2"></i>{{ __('config.restore_default') }}
                        </button>
                    </div>
                </div>
            </form>
            {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-envelope fs-1 text-muted mb-3"></i>
                    <p class="text-muted">{{ __('config.select_template_to_edit') }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Variables Modal -->
<div class="modal fade" id="variablesModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('config.available_variables') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="accordion" id="variablesAccordion">
                    <!-- Invoice Variables -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#invoiceVars">
                                {{ __('config.invoice_variables') }}
                            </button>
                        </h2>
                        <div id="invoiceVars" class="accordion-collapse collapse show" data-bs-parent="#variablesAccordion">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <code>{invoice_number}</code> - {{ __('config.var_invoice_number') }}<br>
                                        <code>{invoice_date}</code> - {{ __('config.var_invoice_date') }}<br>
                                        <code>{due_date}</code> - {{ __('config.var_due_date') }}<br>
                                        <code>{total_amount}</code> - {{ __('config.var_total_amount') }}<br>
                                    </div>
                                    <div class="col-md-6">
                                        <code>{subtotal}</code> - {{ __('config.var_subtotal') }}<br>
                                        <code>{vat_amount}</code> - {{ __('config.var_vat_amount') }}<br>
                                        <code>{payment_link}</code> - {{ __('config.var_payment_link') }}<br>
                                        <code>{invoice_link}</code> - {{ __('config.var_invoice_link') }}<br>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Patient Variables -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#patientVars">
                                {{ __('config.patient_variables') }}
                            </button>
                        </h2>
                        <div id="patientVars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <code>{patient_name}</code> - {{ __('config.var_patient_name') }}<br>
                                        <code>{patient_email}</code> - {{ __('config.var_patient_email') }}<br>
                                        <code>{patient_phone}</code> - {{ __('config.var_patient_phone') }}<br>
                                    </div>
                                    <div class="col-md-6">
                                        <code>{patient_address}</code> - {{ __('config.var_patient_address') }}<br>
                                        <code>{patient_city}</code> - {{ __('config.var_patient_city') }}<br>
                                        <code>{patient_country}</code> - {{ __('config.var_patient_country') }}<br>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Company Variables -->
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#companyVars">
                                {{ __('config.company_variables') }}
                            </button>
                        </h2>
                        <div id="companyVars" class="accordion-collapse collapse" data-bs-parent="#variablesAccordion">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <code>{company_name}</code> - {{ __('config.var_company_name') }}<br>
                                        <code>{company_email}</code> - {{ __('config.var_company_email') }}<br>
                                        <code>{company_phone}</code> - {{ __('config.var_company_phone') }}<br>
                                    </div>
                                    <div class="col-md-6">
                                        <code>{company_address}</code> - {{ __('config.var_company_address') }}<br>
                                        <code>{company_website}</code> - {{ __('config.var_company_website') }}<br>
                                        <code>{company_vat}</code> - {{ __('config.var_company_vat') }}<br>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function loadTemplate(id) {
    window.location.href = '{{ base_url }}/config/email-templates?template=' + id;
}

function updatePreview() {
    document.getElementById('previewSubject').textContent = document.getElementById('subject').value;
    document.getElementById('previewBody').innerHTML = document.getElementById('body').value;
    const footer = document.getElementById('footer').value;
    if (footer) {
        document.getElementById('previewFooter').innerHTML = footer;
    }
}

// Update preview when content changes
document.getElementById('subject')?.addEventListener('input', updatePreview);
document.getElementById('body')?.addEventListener('input', updatePreview);
document.getElementById('footer')?.addEventListener('input', updatePreview);

function sendTestEmail() {
    const email = prompt('{{ __("config.enter_test_email") }}:');
    if (email) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/config/email-templates/{{ current_template.id }}/test';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        const emailInput = document.createElement('input');
        emailInput.type = 'hidden';
        emailInput.name = 'email';
        emailInput.value = email;
        form.appendChild(emailInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function restoreDefault() {
    if (confirm('{{ __("config.restore_default_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/config/email-templates/{{ current_template.id }}/restore';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}