{% extends "base-modern.twig" %}

{% block title %}{{ __('config.email_templates') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.email_templates') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <a href="{{ base_url }}/config/email-templates/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.create_template') }}
            </a>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">{{ __('config.total_templates') }}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-envelope fs-2 text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">{{ __('config.active_templates') }}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle fs-2 text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">{{ __('config.inactive_templates') }}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.inactive }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-x-circle fs-2 text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/config/email-templates" class="row g-3">
                <div class="col-md-3">
                    <input type="text" class="form-control" name="search" placeholder="{{ __('common.search') }}..." 
                           value="{{ filters.search|default('') }}">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="email_type">
                        <option value="">{{ __('config.all_email_types') }}</option>
                        <option value="invoice" {{ filters.email_type == 'invoice' ? 'selected' : '' }}>{{ __('config.invoice_email') }}</option>
                        <option value="reminder" {{ filters.email_type == 'reminder' ? 'selected' : '' }}>{{ __('config.payment_reminder') }}</option>
                        <option value="notification" {{ filters.email_type == 'notification' ? 'selected' : '' }}>{{ __('config.general_notification') }}</option>
                        <option value="welcome" {{ filters.email_type == 'welcome' ? 'selected' : '' }}>{{ __('config.welcome_email') }}</option>
                        <option value="confirmation" {{ filters.email_type == 'confirmation' ? 'selected' : '' }}>{{ __('config.confirmation_email') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="invoice_type">
                        <option value="">{{ __('config.all_invoice_types') }}</option>
                        <option value="standard" {{ filters.invoice_type == 'standard' ? 'selected' : '' }}>{{ __('config.standard_invoice') }}</option>
                        <option value="proforma" {{ filters.invoice_type == 'proforma' ? 'selected' : '' }}>{{ __('config.proforma_invoice') }}</option>
                        <option value="credit" {{ filters.invoice_type == 'credit' ? 'selected' : '' }}>{{ __('config.credit_note') }}</option>
                        <option value="debit" {{ filters.invoice_type == 'debit' ? 'selected' : '' }}>{{ __('config.debit_note') }}</option>
                        <option value="recurring" {{ filters.invoice_type == 'recurring' ? 'selected' : '' }}>{{ __('config.recurring_invoice') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="is_active">
                        <option value="">{{ __('config.all_status') }}</option>
                        <option value="1" {{ filters.is_active == '1' ? 'selected' : '' }}>{{ __('common.active') }}</option>
                        <option value="0" {{ filters.is_active == '0' ? 'selected' : '' }}>{{ __('common.inactive') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="sort">
                        <option value="priority" {{ filters.sort == 'priority' ? 'selected' : '' }}>{{ __('config.priority') }}</option>
                        <option value="name" {{ filters.sort == 'name' ? 'selected' : '' }}>{{ __('common.name') }}</option>
                        <option value="created_at" {{ filters.sort == 'created_at' ? 'selected' : '' }}>{{ __('common.created_date') }}</option>
                        <option value="updated_at" {{ filters.sort == 'updated_at' ? 'selected' : '' }}>{{ __('common.updated_date') }}</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Templates Table -->
    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ __('common.name') }}</th>
                            <th>{{ __('config.code') }}</th>
                            <th>{{ __('config.email_type') }}</th>
                            <th>{{ __('config.invoice_type') }}</th>
                            <th>{{ __('config.priority') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for template in emailTemplates %}
                        <tr>
                            <td>
                                <strong>{{ template.name }}</strong><br>
                                <small class="text-muted">{{ template.subject|slice(0, 50) }}{% if template.subject|length > 50 %}...{% endif %}</small>
                            </td>
                            <td><code>{{ template.code }}</code></td>
                            <td>{{ template.getEmailTypeLabel() }}</td>
                            <td>{{ template.getInvoiceTypeLabel() }}</td>
                            <td>
                                <select class="form-select form-select-sm priority-select" data-template-id="{{ template.id }}">
                                    <option value="1" {{ template.priority == 1 ? 'selected' : '' }}>Lowest</option>
                                    <option value="2" {{ template.priority == 2 ? 'selected' : '' }}>Low</option>
                                    <option value="3" {{ template.priority == 3 ? 'selected' : '' }}>Normal</option>
                                    <option value="4" {{ template.priority == 4 ? 'selected' : '' }}>High</option>
                                    <option value="5" {{ template.priority == 5 ? 'selected' : '' }}>Highest</option>
                                </select>
                            </td>
                            <td>
                                {% if template.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ base_url }}/config/email-templates/{{ template.id }}/edit" 
                                       class="btn btn-outline-primary" title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{{ base_url }}/config/email-templates/{{ template.id }}/test" 
                                       class="btn btn-outline-info" title="{{ __('config.test_template') }}">
                                        <i class="bi bi-send"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-warning duplicate-btn" 
                                            data-template-id="{{ template.id }}" title="{{ __('common.duplicate') }}">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger delete-btn" 
                                            data-template-id="{{ template.id }}" title="{{ __('common.delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center py-5">
                                <i class="bi bi-envelope fs-1 text-muted mb-3 d-block"></i>
                                <p class="text-muted">{{ __('config.no_templates_found') }}</p>
                                <a href="{{ base_url }}/config/email-templates/create" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>{{ __('config.create_first_template') }}
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
// Priority change handler
document.querySelectorAll('.priority-select').forEach(select => {
    select.addEventListener('change', function() {
        const templateId = this.dataset.templateId;
        const priority = this.value;
        
        fetch('{{ base_url }}/config/email-templates/update-priority', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `template_id=${templateId}&priority=${priority}&csrf_token={{ csrf_token }}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
            } else {
                toastr.error(data.message);
                // Revert the change
                location.reload();
            }
        })
        .catch(error => {
            toastr.error('Error updating priority');
            location.reload();
        });
    });
});

// Duplicate template handler
document.querySelectorAll('.duplicate-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const templateId = this.dataset.templateId;
        
        if (confirm('{{ __("config.duplicate_template_confirm") }}')) {
            fetch(`{{ base_url }}/config/email-templates/${templateId}/duplicate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `csrf_token={{ csrf_token }}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toastr.success(data.message);
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        location.reload();
                    }
                } else {
                    toastr.error(data.message);
                }
            })
            .catch(error => {
                toastr.error('Error duplicating template');
            });
        }
    });
});

// Delete template handler
document.querySelectorAll('.delete-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const templateId = this.dataset.templateId;
        
        if (confirm('{{ __("config.delete_template_confirm") }}')) {
            fetch(`{{ base_url }}/config/email-templates/${templateId}`, {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `csrf_token={{ csrf_token }}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    toastr.success(data.message);
                    location.reload();
                } else {
                    toastr.error(data.message);
                }
            })
            .catch(error => {
                toastr.error('Error deleting template');
            });
        }
    });
});
</script>
{% endblock %}