{% extends "base-modern.twig" %}
{% import '_macros/table-helper-v2.twig' as tableHelper %}

{% block title %}{{ __('invoices.invoices') }}{% endblock %}

{% block styles %}
<style>
    /* Enhanced styling for invoices page */
    .stats-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .stats-card .card-body {
        padding: 1.5rem;
    }
    
    .stats-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0.5rem;
        font-size: 1.5rem;
    }
    
    .stats-icon.primary { background-color: rgba(13, 110, 253, 0.1); color: #0d6efd; }
    .stats-icon.success { background-color: rgba(25, 135, 84, 0.1); color: #198754; }
    .stats-icon.warning { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
    .stats-icon.danger { background-color: rgba(220, 53, 69, 0.1); color: #dc3545; }
    
    .invoice-number-link {
        font-weight: 500;
        color: #0d6efd;
        transition: color 0.2s;
    }
    
    .invoice-number-link:hover {
        color: #0a58ca;
        text-decoration: underline;
    }
    
    .badge {
        font-weight: 500;
        padding: 0.35em 0.65em;
        font-size: 0.8125rem;
    }
    
    .table > :not(caption) > * > * {
        padding: 0.75rem 1rem;
        vertical-align: middle;
    }
    
    .dropdown-item {
        padding: 0.5rem 1rem;
    }
    
    .dropdown-item i {
        width: 1.25rem;
    }
    
    /* Clean table design */
    .table-responsive {
        border-radius: 0.5rem;
        overflow: hidden;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    
    .table {
        margin-bottom: 0;
    }
    
    .table thead th {
        background-color: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }
    
    /* Action buttons styling */
    .btn-action-group {
        gap: 0.5rem;
    }
    
    /* Empty state styling */
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
    }
    
    .empty-state i {
        font-size: 4rem;
        color: #dee2e6;
        margin-bottom: 1rem;
    }
    
    .empty-state p {
        color: #6c757d;
        font-size: 1.125rem;
    }
    
    /* Ensure dropdown menu is visible */
    .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Ensure bulk actions dropdown is on top */
    #bulkActionsBtn + .dropdown-menu,
    .dropdown-menu {
        z-index: 1050 !important;
        position: absolute !important;
    }
    
    /* Fix for dropdown inside card */
    .card {
        overflow: visible !important;
    }
    
    .card-body {
        overflow: visible !important;
    }
    
    .table-responsive {
        overflow-x: auto;
        overflow-y: visible !important;
    }
    
    /* Fix dropdown positioning to prevent cutoff */
    .dropdown-menu {
        position: absolute !important;
        transform: none !important;
    }
    
    /* Make dropdowns flip up if near bottom of viewport */
    .dropup .dropdown-menu,
    .dropdown-menu.dropup {
        bottom: 100% !important;
        top: auto !important;
        margin-bottom: 0.5rem;
    }
    
    /* Ensure dropdown parent is positioned */
    .dropdown {
        position: relative !important;
    }
    
    /* Force dropdown menu to be visible when shown */
    .dropdown-menu.show,
    #bulkActionsBtn + .dropdown-menu.show {
        display: block !important;
        opacity: 1 !important;
        visibility: visible !important;
        position: absolute !important;
        z-index: 9999 !important;
    }
    
    /* Ensure dropdown button works */
    #bulkActionsBtn {
        cursor: pointer;
    }
    
    #bulkActionsBtn:not(:disabled):hover {
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                <div>
                    <h1 class="h3 mb-2 text-gray-800">{{ __('invoices.invoices') }}</h1>
                    <p class="text-muted mb-0">{{ __('invoices.manage_invoices_description')|default('Manage and track all your invoices') }}</p>
                </div>
                <div class="btn-action-group d-flex flex-wrap mt-3 mt-md-0">
                    <a href="{{ base_url }}/invoices/archive" class="btn btn-outline-secondary">
                        <i class="bi bi-archive me-2"></i>{{ __('invoices.view_archive') }}
                    </a>
                    <a href="{{ base_url }}/billing-wizard" class="btn btn-outline-primary">
                        <i class="bi bi-magic me-2"></i>{{ __('invoices.billing_wizard') }}
                    </a>
                    <a href="{{ base_url }}/invoices/generate-monthly" class="btn btn-warning">
                        <i class="bi bi-calendar-month me-2"></i>{{ __('invoices.generate_monthly') }}
                    </a>
                    <a href="{{ base_url }}/invoices/create" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                    </a>
                    {% if isAdmin %}
                    <button type="button" class="btn btn-danger" onclick="openDeleteAllModal()">
                        <i class="bi bi-trash-fill me-2"></i>{{ __('invoices.delete_all_invoices') }}
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-lg-6">
            <div class="card stats-card border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon primary">
                            <i class="bi bi-file-text"></i>
                        </div>
                        <div class="ms-3">
                            <p class="text-muted small mb-1">{{ __('invoices.total_invoices') }}</p>
                            <h4 class="mb-0">{{ statistics.total_invoices|default(0)|number_format }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6">
            <div class="card stats-card border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon success">
                            <i class="bi bi-cash-coin"></i>
                        </div>
                        <div class="ms-3">
                            <p class="text-muted small mb-1">{{ __('invoices.total_revenue') }}</p>
                            <h4 class="mb-0">{{ currency }}{{ statistics.total_revenue|default(0)|number_format(2, ',', ' ') }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6">
            <div class="card stats-card border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon warning">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="ms-3">
                            <p class="text-muted small mb-1">{{ __('invoices.unpaid_invoices') }}</p>
                            <h4 class="mb-0">{{ statistics.unpaid_invoices|default(0)|number_format }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6">
            <div class="card stats-card border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon danger">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="ms-3">
                            <p class="text-muted small mb-1">{{ __('invoices.outstanding_amount') }}</p>
                            <h4 class="mb-0">{{ currency }}{{ statistics.outstanding_amount|default(0)|number_format(2, ',', ' ') }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            {% set tableContent %}
                {{ tableHelper.tableHeader([
                    { label: __('invoices.invoice_number'), sortable: true },
                    { label: __('invoices.document_type'), sortable: true },
                    { label: __('invoices.invoice_type'), sortable: true },
                    { label: __('clients.client') ~ '/' ~ __('patients.patient'), sortable: true },
                    { label: __('invoices.issue_date'), sortable: true },
                    { label: __('invoices.due_date'), sortable: true },
                    { label: __('invoices.amount'), sortable: true, class: 'text-end' },
                    { label: __('common.status'), sortable: true },
                    { label: __('common.actions'), width: 100, sortable: false, reorderable: false, isAction: true }
                ], true, true) }}
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="{{ invoice.id }}">
                        </td>
                        <td>
                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="invoice-number-link text-decoration-none">
                                {{ invoice.invoice_number }}
                            </a>
                        </td>
                        <td>
                            {% if invoice.doc_type_color %}
                                <span class="badge" style="background-color: {{ invoice.doc_type_color }};">
                                    {% if invoice.doc_type_icon %}
                                        <i class="{{ invoice.doc_type_icon }} me-1"></i>
                                    {% endif %}
                                    {{ invoice.doc_type_display_name }}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">{{ __('common.unknown') }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.type_color %}
                                <span class="badge" style="background-color: {{ invoice.type_color }};">
                                    {{ invoice.type_name }}
                                </span>
                            {% else %}
                                <span class="text-muted">{{ invoice.type_name|default('N/A') }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if invoice.patient_id %}
                                    <i class="bi bi-person-circle text-primary me-2"></i>
                                    <span>{{ invoice.patient_name }}</span>
                                {% else %}
                                    <i class="bi bi-building text-info me-2"></i>
                                    <span>{{ invoice.client_name }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td data-sort="{{ invoice.issue_date|date('Y-m-d') }}">
                            {{ invoice.issue_date|date('d/m/Y') }}
                        </td>
                        <td data-sort="{{ invoice.due_date ? invoice.due_date|date('Y-m-d') : '9999-12-31' }}">
                            {% if invoice.due_date %}
                                <span class="{{ invoice.is_overdue ? 'text-danger fw-medium' : '' }}">
                                    {{ invoice.due_date|date('d/m/Y') }}
                                    {% if invoice.is_overdue %}
                                        <i class="bi bi-exclamation-circle ms-1" 
                                           data-bs-toggle="tooltip" 
                                           title="{{ __('invoices.overdue') }}"></i>
                                    {% endif %}
                                </span>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-end" data-sort="{{ invoice.total }}">
                            <span class="fw-medium">{{ currency }}{{ invoice.total|number_format(2, ',', ' ') }}</span>
                        </td>
                        <td>
                            {% set statusConfig = {
                                'draft': { class: 'secondary', icon: 'bi-pencil', label: __('invoices.status.draft') },
                                'sent': { class: 'info', icon: 'bi-send', label: __('invoices.status.sent') },
                                'paid': { class: 'success', icon: 'bi-check-circle', label: __('invoices.status.paid') },
                                'partial': { class: 'warning', icon: 'bi-circle-half', label: __('invoices.status.partial') },
                                'overdue': { class: 'danger', icon: 'bi-exclamation-circle', label: __('invoices.status.overdue') },
                                'cancelled': { class: 'dark', icon: 'bi-x-circle', label: __('invoices.status.cancelled') }
                            } %}
                            {% set status = statusConfig[invoice.status]|default({ class: 'secondary', label: invoice.status }) %}
                            <span class="badge bg-{{ status.class }}">
                                {% if status.icon is defined %}
                                    <i class="{{ status.icon }} me-1"></i>
                                {% endif %}
                                {{ status.label }}
                            </span>
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light border-0" type="button" data-bs-toggle="dropdown" 
                                        data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}">
                                            <i class="bi bi-eye text-primary"></i>{{ __('common.view') }}
                                        </a>
                                    </li>
                                    {% if invoice.status == 'draft' %}
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/edit">
                                            <i class="bi bi-pencil text-warning"></i>{{ __('common.edit') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="markInvoiceAsSent({{ invoice.id }})">
                                            <i class="bi bi-send text-success"></i>{{ __('invoices.mark_as_sent')|default('Marquer comme envoyée') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank">
                                            <i class="bi bi-printer text-secondary"></i>{{ __('common.print') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/download">
                                            <i class="bi bi-download text-info"></i>{{ __('invoices.download_pdf') }}
                                        </a>
                                    </li>
                                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="sendInvoice({{ invoice.id }})">
                                            <i class="bi bi-envelope text-primary"></i>{{ __('invoices.send_invoice') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if invoice.status == 'sent' or invoice.status == 'partial' or invoice.status == 'overdue' %}
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="recordPayment({{ invoice.id }})">
                                            <i class="bi bi-cash text-success"></i>{{ __('invoices.record_payment') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/credit-note">
                                            <i class="bi bi-receipt text-secondary"></i>{{ __('invoices.create_credit_note') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if invoice.status == 'paid' or invoice.status == 'cancelled' %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="archiveInvoice({{ invoice.id }})">
                                            <i class="bi bi-archive text-secondary"></i>{{ __('invoices.archive') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if invoice.status == 'draft' %}
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="deleteInvoice({{ invoice.id }})">
                                            <i class="bi bi-trash"></i>{{ __('common.delete') }}
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="10" class="empty-state">
                            <i class="bi bi-file-earmark-x"></i>
                            <p>{{ __('invoices.no_invoices_found') }}</p>
                            <a href="{{ base_url }}/invoices/create" class="btn btn-primary btn-sm mt-2">
                                <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_first_invoice')|default('Create your first invoice') }}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            {% endset %}

            {{ tableHelper.tableWithFilters({
                baseUrl: base_url,
                tableId: 'invoicesTable',
                formAction: base_url ~ '/invoices',
                storageKey: 'invoices_filters_v3',
                tableContent: tableContent,
                searchColumns: [1, 3, 4], 
                searchPlaceholder: __('invoices.search_placeholder')|default('Search by invoice number, client, or patient...'),
                sortable: true,
                reorderable: false,
                columnOrder: columnOrder,
                defaultSort: { column: 5, direction: 'desc' },
                showColumnToggle: false,
                filters: [
                    {
                        id: 'status',
                        name: 'status',
                        label: __('common.status'),
                        type: 'select',
                        width: 2,
                        value: filters.status|default(''),
                        placeholder: __('common.all'),
                        options: {
                            'draft': __('invoices.status.draft'),
                            'sent': __('invoices.status.sent'),
                            'paid': __('invoices.status.paid'),
                            'partial': __('invoices.status.partial'),
                            'overdue': __('invoices.status.overdue'),
                            'cancelled': __('invoices.status.cancelled')
                        }
                    },
                    {
                        id: 'document_type',
                        name: 'document_type',
                        label: __('invoices.document_type'),
                        type: 'select',
                        width: 2,
                        value: filters.document_type|default(''),
                        placeholder: __('common.all'),
                        options: documentTypes|default({})
                    },
                    {
                        id: 'type',
                        name: 'type',
                        label: __('invoices.invoice_type'),
                        type: 'select',
                        width: 2,
                        value: filters.type|default(''),
                        placeholder: __('common.all'),
                        options: invoiceTypes|default({})
                    },
                    {
                        id: 'date_from',
                        name: 'date_from',
                        label: __('common.from'),
                        type: 'date',
                        width: 2,
                        value: filters.date_from|default('')
                    },
                    {
                        id: 'date_to',
                        name: 'date_to',
                        label: __('common.to'),
                        type: 'date',
                        width: 2,
                        value: filters.date_to|default('')
                    }
                ],
                filterConfigs: [
                    { id: 'status', autoSubmit: true },
                    { id: 'document_type', autoSubmit: true },
                    { id: 'type', autoSubmit: true },
                    { id: 'date_from', autoSubmit: true },
                    { id: 'date_to', autoSubmit: true }
                ],
                showImport: true,
                importUrl: base_url ~ '/invoices/import',
                showExport: true,
                exportUrl: base_url ~ '/invoices/export',
                exportFormats: ['csv', 'excel', 'pdf'],
                showBulkActions: true,
                bulkActions: [
                    {
                        action: 'send',
                        label: __('invoices.send_selected'),
                        icon: 'bi bi-envelope',
                        url: base_url ~ '/invoices/bulk-send'
                    },
                    {
                        action: 'export',
                        label: __('invoices.export_selected'),
                        icon: 'bi bi-download',
                        url: base_url ~ '/invoices/bulk-export'
                    },
                    {
                        action: 'archive',
                        label: __('invoices.archive_selected'),
                        icon: 'bi bi-archive',
                        url: base_url ~ '/invoices/bulk-archive',
                        divider: true
                    },
                    {
                        action: 'mark_paid',
                        label: __('invoices.mark_as_paid'),
                        icon: 'bi bi-check-circle',
                        url: base_url ~ '/invoices/bulk-mark-paid'
                    },
                    {
                        action: 'delete',
                        label: __('invoices.delete_selected'),
                        icon: 'bi bi-trash',
                        class: 'text-danger',
                        url: base_url ~ '/invoices/bulk-delete',
                        confirm: true,
                        confirmMessage: __('invoices.bulk_delete_confirm')
                    }
                ],
                bulkActionUrl: base_url ~ '/invoices/bulk-action',
                pagination: {
                    current_page: current_page|default(1),
                    total_pages: total_pages|default(1),
                    base_url: base_url ~ '/invoices'
                },
                resetUrl: base_url ~ '/invoices?reset_filters=1'
            }) }}
        </div>
    </div>
</div>

<!-- Invoice Action Scripts -->
<script>
// Initialize tooltips and fix dropdown positioning
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    
    // Auto-position dropdowns to prevent cutoff
    document.addEventListener('show.bs.dropdown', function(event) {
        const dropdown = event.target;
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (menu) {
            // Get position info
            const dropdownRect = dropdown.getBoundingClientRect();
            const menuHeight = menu.offsetHeight || 200; // Estimate if not visible yet
            const viewportHeight = window.innerHeight;
            
            // Check if dropdown would be cut off at bottom
            if (dropdownRect.bottom + menuHeight > viewportHeight - 20) {
                // Add dropup class to flip menu upward
                dropdown.classList.add('dropup');
            } else {
                // Remove dropup class if not needed
                dropdown.classList.remove('dropup');
            }
        }
    });
});

function sendInvoice(id) {
    Swal.fire({
        title: '{{ __("invoices.send_invoice") }}',
        text: '{{ __("invoices.send_confirm") }}',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#0d6efd',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.send") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '{{ base_url }}/invoices/' + id + '/send';
        }
    });
}

function recordPayment(id) {
    // Redirect to invoice view page with payment modal trigger
    window.location.href = '{{ base_url }}/invoices/' + id + '#payment';
}

function deleteInvoice(id) {
    Swal.fire({
        title: '{{ __("common.are_you_sure") }}',
        text: '{{ __("invoices.delete_warning") }}',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.delete") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/' + id + '/delete';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function archiveInvoice(id) {
    Swal.fire({
        title: '{{ __("invoices.archive_invoice") }}',
        text: '{{ __("invoices.archive_confirm") }}',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6c757d',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("invoices.archive") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/' + id + '/archive';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}

function markInvoiceAsSent(id) {
    Swal.fire({
        title: '{{ __("invoices.mark_as_sent_confirm_title")|default("Marquer comme envoyée ?") }}',
        text: '{{ __("invoices.mark_as_sent_confirm_text")|default("Cette facture sera marquée comme envoyée et ne pourra plus être modifiée.") }}',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#28a745',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("invoices.yes_mark_sent")|default("Oui, marquer comme envoyée") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/' + id + '/send';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}

{% if isAdmin %}
// Delete all invoices functionality for admins
function openDeleteAllModal() {
    // First get invoice count
    fetch('{{ base_url }}/invoices/count-all', {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        // Get the response text first to debug
        return response.text().then(text => {
            console.log('Response text:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse JSON:', e);
                console.error('Response was:', text);
                // If we can't parse, show modal with zeros
                return {
                    total: 0,
                    draft: 0,
                    sent: 0,
                    paid: 0,
                    cancelled: 0,
                    error: 'Failed to load counts'
                };
            }
        });
    })
    .then(data => {
        const modal = new bootstrap.Modal(document.getElementById('deleteAllModal'));
        document.getElementById('totalInvoiceCount').textContent = data.total || 0;
        document.getElementById('draftCount').textContent = data.draft || 0;
        document.getElementById('sentCount').textContent = data.sent || 0;
        document.getElementById('paidCount').textContent = data.paid || 0;
        document.getElementById('cancelledCount').textContent = data.cancelled || 0;
        
        if (data.error) {
            toastr.warning('{{ __("invoices.could_not_load_counts") }}');
        }
        
        modal.show();
    })
    .catch(error => {
        console.error('Error fetching invoice counts:', error);
        // Still show the modal even if we can't get counts
        const modal = new bootstrap.Modal(document.getElementById('deleteAllModal'));
        document.getElementById('totalInvoiceCount').textContent = '?';
        document.getElementById('draftCount').textContent = '?';
        document.getElementById('sentCount').textContent = '?';
        document.getElementById('paidCount').textContent = '?';
        document.getElementById('cancelledCount').textContent = '?';
        modal.show();
        toastr.warning('{{ __("invoices.could_not_load_counts") }}');
    });
}

function confirmDeleteAll() {
    const confirmText = document.getElementById('deleteConfirmText').value;
    const confirmCheckbox = document.getElementById('deleteConfirmCheckbox').checked;
    const deleteType = document.querySelector('input[name="deleteType"]:checked').value;
    
    if (confirmText !== 'DELETE ALL') {
        toastr.error('{{ __("invoices.type_delete_all_to_confirm") }}');
        return;
    }
    
    if (!confirmCheckbox) {
        toastr.error('{{ __("invoices.check_understand_checkbox") }}');
        return;
    }
    
    // Close modal and show progress
    bootstrap.Modal.getInstance(document.getElementById('deleteAllModal')).hide();
    
    Swal.fire({
        title: '{{ __("invoices.deleting_invoices") }}',
        html: '{{ __("common.please_wait") }}',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });
    
    // Submit delete request
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token }}');
    formData.append('delete_type', deleteType);
    formData.append('confirmation', confirmText);
    
    fetch('{{ base_url }}/invoices/delete-all', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Delete response status:', response.status);
        console.log('Delete response headers:', response.headers);
        
        // Get response as text first
        return response.text().then(text => {
            console.log('Delete response text:', text);
            try {
                return JSON.parse(text);
            } catch (e) {
                console.error('Failed to parse delete response:', e);
                console.error('Response was:', text);
                Swal.close();
                
                // Check if it's an HTML error page
                if (text.includes('<html') || text.includes('<!DOCTYPE')) {
                    throw new Error('Server returned an HTML error page instead of JSON');
                } else {
                    throw new Error('Invalid JSON response: ' + text.substring(0, 200));
                }
            }
        });
    })
    .then(data => {
        Swal.close();
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: '{{ __("common.success") }}',
                text: data.message,
                confirmButtonText: '{{ __("common.ok") }}'
            }).then(() => {
                window.location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: '{{ __("common.error") }}',
                text: data.message || '{{ __("common.error_occurred") }}'
            });
        }
    })
    .catch(error => {
        Swal.close();
        console.error('Delete error:', error);
        Swal.fire({
            icon: 'error',
            title: '{{ __("common.error") }}',
            text: error.message || '{{ __("common.error_occurred") }}'
        });
    });
}
{% endif %}
</script>

{% if isAdmin %}
<!-- Delete All Modal -->
<div class="modal fade" id="deleteAllModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    {{ __('invoices.delete_all_invoices') }}
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h5 class="alert-heading">
                        <i class="bi bi-exclamation-octagon-fill me-2"></i>
                        {{ __('invoices.warning_irreversible') }}
                    </h5>
                    <p class="mb-0">{{ __('invoices.delete_all_warning_text') }}</p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>{{ __('invoices.invoice_summary') }}</h6>
                        <ul class="list-unstyled">
                            <li><strong>{{ __('invoices.total_invoices') }}:</strong> <span id="totalInvoiceCount">0</span></li>
                            <li><strong>{{ __('invoices.draft_invoices') }}:</strong> <span id="draftCount">0</span></li>
                            <li><strong>{{ __('invoices.sent_invoices') }}:</strong> <span id="sentCount">0</span></li>
                            <li><strong>{{ __('invoices.paid_invoices') }}:</strong> <span id="paidCount">0</span></li>
                            <li><strong>{{ __('invoices.cancelled_invoices') }}:</strong> <span id="cancelledCount">0</span></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>{{ __('invoices.delete_options') }}</h6>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="radio" name="deleteType" id="deleteAll" value="all" checked>
                            <label class="form-check-label" for="deleteAll">
                                {{ __('invoices.delete_all_invoices_option') }}
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="deleteType" id="deleteDraftOnly" value="draft">
                            <label class="form-check-label" for="deleteDraftOnly">
                                {{ __('invoices.delete_draft_only_option') }}
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="deleteConfirmText" class="form-label fw-bold">
                        {{ __('invoices.type_delete_all_label') }}
                    </label>
                    <input type="text" class="form-control form-control-lg" id="deleteConfirmText" 
                           placeholder="DELETE ALL" autocomplete="off">
                    <div class="form-text">{{ __('invoices.type_exactly_as_shown') }}</div>
                </div>
                
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="deleteConfirmCheckbox">
                    <label class="form-check-label" for="deleteConfirmCheckbox">
                        {{ __('invoices.understand_cannot_be_undone') }}
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('common.cancel') }}
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmDeleteAll()">
                    <i class="bi bi-trash-fill me-2"></i>
                    {{ __('invoices.proceed_with_deletion') }}
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block table_helper %}
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Table-specific functions for invoice actions
// The TableHelper is automatically initialized by table-helper-init.js

// You can listen for the initialization event if needed
document.addEventListener('tableHelperReady', function(e) {
    console.log('Invoice table ready:', e.detail.tableHelper);
    
    // The global dropdown fix in base-modern.twig will handle all dropdowns automatically
});
</script>
{% endblock %}