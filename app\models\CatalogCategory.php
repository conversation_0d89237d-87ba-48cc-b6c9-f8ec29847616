<?php
namespace App\Models;

use App\Core\Model;

class CatalogCategory extends Model
{
    protected $table = 'catalog_categories';
    
    protected $fillable = [
        'code',
        'name',
        'parent_id',
        'slug',
        'description',
        'icon',
        'color',
        'sort_order',
        'is_active',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'name' => 'json',
        'parent_id' => 'integer',
        'sort_order' => 'integer',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get parent category
     */
    public function parent()
    {
        return $this->belongsTo(CatalogCategory::class, 'parent_id');
    }
    
    /**
     * Get child categories
     */
    public function children()
    {
        return $this->hasMany(CatalogCategory::class, 'parent_id')
                    ->orderBy('sort_order');
    }
    
    /**
     * Get items in this category
     */
    public function items()
    {
        return $this->hasMany(CatalogItem::class, 'category_id');
    }
    
    /**
     * Get active items in this category
     */
    public function activeItems()
    {
        return $this->items()->active();
    }
    
    /**
     * Scope for active categories
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    /**
     * Scope for root categories (no parent)
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }
    
    /**
     * Get display name (handles JSON names)
     */
    public function getDisplayName($locale = null)
    {
        if (!$locale) {
            $locale = $_SESSION['user_language'] ?? 'fr';
        }
        
        // If name is an array (JSON was decoded)
        if (is_array($this->name)) {
            return $this->name[$locale] ?? $this->name['fr'] ?? $this->name['en'] ?? reset($this->name);
        }
        
        // If name is a JSON string
        if (is_string($this->name) && strpos($this->name, '{') === 0) {
            $names = json_decode($this->name, true);
            if ($names) {
                return $names[$locale] ?? $names['fr'] ?? $names['en'] ?? reset($names);
            }
        }
        
        // Otherwise return as is
        return $this->name;
    }
    
    /**
     * Get full path of category
     */
    public function getPath()
    {
        $path = [$this];
        $current = $this;

        while ($current->parent_id) {
            $parent = static::find($current->parent_id);
            if ($parent) {
                array_unshift($path, $parent);
                $current = $parent;
            } else {
                break;
            }
        }

        return $path;
    }
    
    /**
     * Get all descendants
     */
    public function getAllDescendants()
    {
        $descendants = [];
        $children = static::where('parent_id', $this->id)->get();

        foreach ($children as $child) {
            $descendants[] = $child;
            $childDescendants = $child->getAllDescendants();
            $descendants = array_merge($descendants, $childDescendants);
        }

        return $descendants;
    }
    
    /**
     * Get all items including from child categories
     */
    public function getAllItems()
    {
        $descendants = $this->getAllDescendants();
        $categoryIds = [$this->id];

        foreach ($descendants as $descendant) {
            $categoryIds[] = $descendant->id;
        }

        return CatalogItem::whereIn('category_id', $categoryIds);
    }
    
    /**
     * Check if has items
     */
    public function hasItems()
    {
        return $this->items()->count() > 0;
    }
    
    /**
     * Check if has active items
     */
    public function hasActiveItems()
    {
        return $this->activeItems()->count() > 0;
    }
    
    /**
     * Get item count
     */
    public function getItemCount($includeChildren = false)
    {
        if ($includeChildren) {
            return $this->getAllItems()->count();
        }
        
        return $this->items()->count();
    }
    
    /**
     * Get categories for dropdown
     */
    public static function getForDropdown($includeInactive = false)
    {
        $query = static::orderBy('sort_order');
        
        if (!$includeInactive) {
            $query->active();
        }
        
        $categories = $query->get();
        $options = [];
        
        // Build hierarchical options
        foreach ($categories->where('parent_id', null) as $root) {
            $options[$root->id] = $root->name;
            static::addChildrenToDropdown($options, $categories, $root->id, 1);
        }
        
        return $options;
    }
    
    /**
     * Helper for building dropdown options
     */
    private static function addChildrenToDropdown(&$options, $categories, $parentId, $level)
    {
        $children = $categories->where('parent_id', $parentId);
        
        foreach ($children as $child) {
            $prefix = str_repeat('-- ', $level);
            $options[$child->id] = $prefix . $child->name;
            static::addChildrenToDropdown($options, $categories, $child->id, $level + 1);
        }
    }
    
    /**
     * Reorder categories
     */
    public static function reorder(array $order)
    {
        foreach ($order as $index => $id) {
            static::where('id', $id)->update(['sort_order' => $index]);
        }
    }
    
    /**
     * Override fill to handle parent_id
     */
    public function fill($data)
    {
        // Convert parent_id = 0 to null
        if (isset($data['parent_id']) && ($data['parent_id'] === 0 || $data['parent_id'] === '0')) {
            $data['parent_id'] = null;
        }
        
        return parent::fill($data);
    }
    
    /**
     * Override update to handle parent_id
     */
    public function update($data)
    {
        // If parent_id is 0, set it to null
        if (isset($data['parent_id']) && $data['parent_id'] == 0) {
            $data['parent_id'] = null;
        }
        
        // Remove parent_id if not explicitly set to avoid issues
        if (!array_key_exists('parent_id', $data) && isset($this->attributes['parent_id']) && $this->attributes['parent_id'] == 0) {
            $this->attributes['parent_id'] = null;
        }
        
        return parent::update($data);
    }
    
    /**
     * Get parent category (alias for parent relationship)
     */
    public function getParent()
    {
        if ($this->parent_id) {
            return $this->parent()->first();
        }
        return null;
    }
    
    /**
     * Get children categories (alias for children relationship)
     */
    public function getChildren()
    {
        return $this->children()->get();
    }
    
    /**
     * Get breadcrumb path
     */
    public function getBreadcrumb()
    {
        $breadcrumb = [];
        $current = $this;

        while ($current) {
            array_unshift($breadcrumb, $current->getLocalizedName());
            if ($current->parent_id) {
                $current = static::find($current->parent_id);
            } else {
                $current = null;
            }
        }

        return implode(' > ', $breadcrumb);
    }
    
    /**
     * Generate unique code
     */
    public static function generateCode($prefix = 'CAT')
    {
        $counter = 1;
        $code = $prefix . str_pad($counter, 3, '0', STR_PAD_LEFT);
        
        while (static::where('code', $code)->exists()) {
            $counter++;
            $code = $prefix . str_pad($counter, 3, '0', STR_PAD_LEFT);
        }
        
        return $code;
    }
    
    /**
     * Get root categories
     */
    public static function getRootCategories()
    {
        return static::whereNull('parent_id')->orderBy('sort_order')->get();
    }
    
    /**
     * Get localized name
     */
    public function getLocalizedName($locale = null)
    {
        if (!$locale) {
            $locale = $_SESSION['lang'] ?? 'en';
        }
        
        if (is_string($this->name)) {
            return $this->name;
        }
        
        $names = is_array($this->name) ? $this->name : json_decode($this->name, true);
        return $names[$locale] ?? $names['en'] ?? $this->name;
    }
    
    /**
     * Get items (alias for items relationship)
     */
    public function getItems()
    {
        return $this->items()->get();
    }
}