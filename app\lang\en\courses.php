<?php

return [
    'courses' => 'Courses',
    'courses_management' => 'Courses Management',
    'add_course' => 'Add Course',
    'add_new_course' => 'Add New Course',
    'edit_course' => 'Edit Course',
    'name' => 'Course Name',
    'course_name' => 'Course Name',
    'hourly_rate_ttc' => 'Hourly Rate (TTC)',
    'vat_rate' => 'VAT Rate',
    'is_active' => 'Active',
    'no_courses_yet' => 'No courses have been added yet.',
    'delete_confirm' => 'Are you sure you want to delete this course?',
    'name_required' => 'Course name is required',
    'rate_required' => 'Hourly rate is required',
    'created_successfully' => 'Course created successfully',
    'updated_successfully' => 'Course updated successfully',
    'deleted_successfully' => 'Course deleted successfully',
    'invalid_order' => 'Invalid course order',
    'order_update_failed' => 'Failed to update course order',
    'order_updated' => 'Course order updated successfully',
    
    // Monthly course counts
    'monthly_course_counts' => 'Monthly Course Counts',
    'generate_invoice' => 'Generate Invoice',
    'generate_invoice_confirm' => 'Do you want to generate the course invoice for',
    'no_courses_for_period' => 'No courses recorded for this period',
    'please_add_courses_first' => 'Please add courses first to be able to record monthly counts',
    'confirm_delete_course' => 'Are you sure you want to delete this course?',
    'course_deleted' => 'Course deleted successfully',
    'course_updated' => 'Course updated successfully',
    'course_added' => 'Course added successfully',
    'not_a_coach' => 'This user is not a coach',
];