<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="demo-token">
    <title>Email Template Manager Demo</title>
    
    <!-- CSS -->
    <link rel="stylesheet" href="css/email-template-manager.css">
    
    <style>
        /* Demo page styles */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f2f5;
        }
        
        .page-header {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 24px;
            color: #212529;
        }
        
        .header-actions {
            display: flex;
            gap: 10px;
        }
        
        /* Icons (using simple text for demo) */
        .icon-trash::before { content: '🗑'; }
        .icon-send::before { content: '✉️'; }
        .icon-spinner::before { content: '⌛'; }
        .icon-check-circle::before { content: '✅'; }
        .icon-times-circle::before { content: '❌'; }
        .icon-exclamation-triangle::before { content: '⚠️'; }
        .icon-info-circle::before { content: 'ℹ️'; }
        .icon-paperclip::before { content: '📎'; }
        .icon-email-empty::before { content: '📧'; }
        .icon-info::before { content: 'ℹ️'; }
    </style>
</head>
<body>
    <!-- Page Header -->
    <div class="page-header">
        <h1>Email Template Manager</h1>
        <div class="header-actions">
            <button type="button" class="btn btn-info" onclick="EmailTemplateManager.loadTemplates()">
                Refresh Templates
            </button>
            <button type="button" class="btn btn-primary" id="saveTemplateBtn">
                Save Template
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="email-template-manager">
        <!-- Template List Sidebar -->
        <div class="template-list">
            <div class="template-list-header">
                <h3>Templates</h3>
            </div>
            <div class="template-list-body" id="templateList">
                <!-- Templates will be loaded here -->
                <div class="template-item selected" data-template-id="1" data-priority="10">
                    <div class="template-header">
                        <h4>Invoice Reminder</h4>
                        <span class="priority-badge priority-10">Priority: 10</span>
                    </div>
                    <p class="template-description">Sent when invoice is overdue</p>
                    <span class="condition-indicator">Has conditions</span>
                </div>
                <div class="template-item" data-template-id="2" data-priority="5">
                    <div class="template-header">
                        <h4>New Invoice</h4>
                        <span class="priority-badge priority-5">Priority: 5</span>
                    </div>
                    <p class="template-description">Sent when new invoice is created</p>
                </div>
                <div class="template-item" data-template-id="3" data-priority="3">
                    <div class="template-header">
                        <h4>Payment Confirmation</h4>
                        <span class="priority-badge priority-3">Priority: 3</span>
                    </div>
                    <p class="template-description">Sent when payment is received</p>
                </div>
            </div>
        </div>

        <!-- Editor Panel -->
        <div class="editor-panel">
            <div class="editor-tabs">
                <button class="editor-tab active" data-tab-target="content">Content</button>
                <button class="editor-tab" data-tab-target="conditions">Conditions</button>
                <button class="editor-tab" data-tab-target="preview">Preview</button>
                <button class="editor-tab" data-tab-target="test">Test</button>
            </div>

            <!-- Content Tab -->
            <div class="tab-content active" id="content-tab">
                <div class="form-group">
                    <label class="form-label">Subject *</label>
                    <input type="text" class="form-control" id="templateSubject" 
                           value="Invoice {invoice_number} - Payment Reminder">
                </div>

                <div class="form-group">
                    <label class="form-label">Body *</label>
                    <div class="editor-wrapper">
                        <textarea class="form-control" id="templateBody" rows="15">Dear {patient_name},

This is a friendly reminder that your invoice {invoice_number} dated {invoice_date} for {total_amount} is now overdue.

Please click the link below to pay online:
{payment_link}

Or view your invoice here:
{invoice_link}

If you have already paid, please disregard this message.

Best regards,
{company_name}</textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label class="form-label">Footer</label>
                    <textarea class="form-control" id="templateFooter" rows="3">This email was sent from {company_name}. If you have questions, please contact us at {company_email} or {company_phone}.</textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">Settings</label>
                    <div>
                        <label>
                            <input type="checkbox" id="templateActive" checked> Template is active
                        </label>
                    </div>
                    <div style="margin-top: 10px;">
                        <label class="form-label">Priority</label>
                        <input type="number" class="form-control" id="templatePriority" 
                               value="10" min="1" max="10" style="width: 100px;">
                    </div>
                </div>
            </div>

            <!-- Conditions Tab -->
            <div class="tab-content" id="conditions-tab">
                <h3>Conditional Logic</h3>
                <p>Define conditions to use different templates based on invoice data.</p>
                
                <div id="conditionBuilder">
                    <!-- Conditions will be rendered here -->
                </div>
                
                <button type="button" class="btn btn-secondary" id="addConditionBtn">
                    Add Condition
                </button>
            </div>

            <!-- Preview Tab -->
            <div class="tab-content" id="preview-tab">
                <div class="email-preview">
                    <div class="preview-header">
                        <h4>Email Preview</h4>
                        <div class="preview-meta">
                            <span><strong>To:</strong> <EMAIL></span>
                            <span><strong>From:</strong> <EMAIL></span>
                        </div>
                    </div>
                    
                    <div class="preview-subject">
                        <label>Subject:</label>
                        <div class="subject-content" id="previewSubject">
                            Invoice INV-2025-001 - Payment Reminder
                        </div>
                    </div>
                    
                    <div class="preview-body">
                        <label>Body:</label>
                        <div class="body-content" id="previewBody">
                            <!-- Preview content will be rendered here -->
                        </div>
                    </div>
                    
                    <div class="preview-footer">
                        <label>Footer:</label>
                        <div class="footer-content" id="previewFooter">
                            <!-- Footer preview will be rendered here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Tab -->
            <div class="tab-content" id="test-tab">
                <h3>Test Email Template</h3>
                
                <div class="form-group">
                    <label class="form-label">Select Test Invoice</label>
                    <select class="form-control" id="testInvoiceSelect">
                        <option value="">Select an invoice for testing...</option>
                        <option value="1">INV-2025-001 - John Doe - €150.00</option>
                        <option value="2">INV-2025-002 - Jane Smith - €200.00</option>
                        <option value="3">INV-2025-003 - Bob Johnson - €175.50</option>
                    </select>
                </div>
                
                <div id="invoiceDetails">
                    <!-- Invoice details will be shown here -->
                </div>
                
                <button type="button" class="btn btn-primary" id="updatePreviewBtn">
                    Generate Preview
                </button>
                
                <div id="testPreviewContainer" style="margin-top: 20px;">
                    <!-- Test preview will be shown here -->
                </div>
                
                <div style="margin-top: 20px;">
                    <button type="button" class="btn btn-success" id="sendTestEmailBtn">
                        <i class="icon-send"></i> Send Test Email
                    </button>
                </div>
                
                <div style="margin-top: 20px;">
                    <h4>Test Email Log</h4>
                    <div id="testEmailLog" style="max-height: 200px; overflow-y: auto; border: 1px solid #dee2e6; border-radius: 4px;">
                        <!-- Log entries will appear here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Variable Picker Sidebar -->
        <div class="sidebar-right">
            <div class="variable-picker" id="variablePicker">
                <h3>Available Variables</h3>
                
                <div class="variable-category">
                    <h5 class="category-title">Invoice Variables</h5>
                    <div class="variable-list">
                        <span class="variable-tag" data-variable="{invoice_number}" title="Invoice number">
                            {invoice_number}
                        </span>
                        <span class="variable-tag" data-variable="{invoice_date}" title="Invoice date">
                            {invoice_date}
                        </span>
                        <span class="variable-tag" data-variable="{due_date}" title="Due date">
                            {due_date}
                        </span>
                        <span class="variable-tag" data-variable="{total_amount}" title="Total amount">
                            {total_amount}
                        </span>
                        <span class="variable-tag" data-variable="{payment_link}" title="Payment link">
                            {payment_link}
                        </span>
                        <span class="variable-tag" data-variable="{invoice_link}" title="Invoice link">
                            {invoice_link}
                        </span>
                    </div>
                </div>
                
                <div class="variable-category">
                    <h5 class="category-title">Patient Variables</h5>
                    <div class="variable-list">
                        <span class="variable-tag" data-variable="{patient_name}" title="Patient name">
                            {patient_name}
                        </span>
                        <span class="variable-tag" data-variable="{patient_email}" title="Patient email">
                            {patient_email}
                        </span>
                        <span class="variable-tag" data-variable="{patient_phone}" title="Patient phone">
                            {patient_phone}
                        </span>
                        <span class="variable-tag" data-variable="{patient_address}" title="Patient address">
                            {patient_address}
                        </span>
                    </div>
                </div>
                
                <div class="variable-category">
                    <h5 class="category-title">Company Variables</h5>
                    <div class="variable-list">
                        <span class="variable-tag" data-variable="{company_name}" title="Company name">
                            {company_name}
                        </span>
                        <span class="variable-tag" data-variable="{company_email}" title="Company email">
                            {company_email}
                        </span>
                        <span class="variable-tag" data-variable="{company_phone}" title="Company phone">
                            {company_phone}
                        </span>
                        <span class="variable-tag" data-variable="{company_website}" title="Company website">
                            {company_website}
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/email-template-manager.js"></script>
    <script src="js/email-template-test.js"></script>
    
    <script>
        // Demo: Update preview on load
        setTimeout(() => {
            EmailTemplateManager.updatePreview();
        }, 100);
    </script>
</body>
</html>