<?php
/**
 * Migration: Add User Invoicing Capability
 * 
 * This migration allows creating invoices for system users
 * including administrators and staff members
 */

return new class {
    
    public function up(PDO $pdo): void
    {
        // Add user_id column to invoices table
        $pdo->exec("
            ALTER TABLE `invoices` 
            ADD COLUMN `user_id` int(11) NULL AFTER `client_id`,
            ADD KEY `idx_invoice_user` (`user_id`),
            ADD CONSTRAINT `fk_invoice_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
        ");
        
        // Add billing information to users table
        $pdo->exec("
            ALTER TABLE `users` 
            ADD COLUMN `billing_address` text NULL AFTER `language`,
            ADD COLUMN `billing_city` varchar(100) NULL AFTER `billing_address`,
            ADD COLUMN `billing_postal_code` varchar(20) NULL AFTER `billing_city`,
            ADD COLUMN `billing_country` varchar(100) DEFAULT 'Luxembourg' AFTER `billing_postal_code`,
            ADD COLUMN `vat_number` varchar(50) NULL AFTER `billing_country`,
            ADD COLUMN `billing_email` varchar(100) NULL AFTER `vat_number`,
            ADD COLUMN `preferred_payment_method_id` int(11) NULL AFTER `billing_email`,
            ADD COLUMN `can_be_invoiced` tinyint(1) DEFAULT 1 COMMENT 'Whether this user can receive invoices',
            ADD KEY `idx_user_payment_method` (`preferred_payment_method_id`),
            ADD CONSTRAINT `fk_user_payment_method` FOREIGN KEY (`preferred_payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE SET NULL
        ");
        
        // Create user invoice preferences table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS `user_invoice_preferences` (
                `user_id` int(11) NOT NULL,
                `invoice_language` varchar(2) DEFAULT 'fr',
                `send_invoice_copy` tinyint(1) DEFAULT 1 COMMENT 'Send copy of invoices by email',
                `invoice_notes` text NULL COMMENT 'Default notes for user invoices',
                `discount_percentage` decimal(5,2) DEFAULT 0.00 COMMENT 'Default discount for this user',
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`user_id`),
                CONSTRAINT `fk_user_invoice_pref` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Add permissions for user invoicing
        $stmt = $pdo->prepare("
            INSERT INTO `permissions` (`category`, `name`, `code`, `description`, `sort_order`) 
            VALUES
            ('invoices', 'Create User Invoices', 'invoices.create_user', 'Create invoices for system users', 49),
            ('invoices', 'View Own Invoices', 'invoices.view_own', 'View invoices addressed to me', 50)
        ");
        $stmt->execute();
        
        // Grant permissions to admin group
        $pdo->exec("
            INSERT INTO `group_permissions` (`group_id`, `permission_id`)
            SELECT 1, `id` FROM `permissions` WHERE `code` IN ('invoices.create_user', 'invoices.view_own')
        ");
        
        // Add default invoice preferences for existing admin users
        $pdo->exec("
            INSERT INTO `user_invoice_preferences` (`user_id`, `invoice_language`, `send_invoice_copy`)
            SELECT u.`id`, COALESCE(u.`language`, 'fr'), 1
            FROM `users` u
            WHERE u.`role` = 'admin'
            AND NOT EXISTS (
                SELECT 1 FROM `user_invoice_preferences` uip WHERE uip.`user_id` = u.`id`
            )
        ");
        
        // Create view for unified invoice recipients
        $pdo->exec("
            CREATE OR REPLACE VIEW `invoice_recipients` AS
            SELECT 
                'client' as recipient_type,
                c.id as recipient_id,
                c.client_number as number,
                CASE 
                    WHEN c.client_type = 'company' THEN c.company_name
                    ELSE CONCAT(c.first_name, ' ', c.last_name)
                END as name,
                c.email,
                c.address,
                c.city,
                c.postal_code,
                c.country,
                c.vat_number,
                c.is_active
            FROM clients c
            WHERE c.is_active = 1
            
            UNION ALL
            
            SELECT 
                'user' as recipient_type,
                u.id as recipient_id,
                CONCAT('USR-', LPAD(u.id, 5, '0')) as number,
                CONCAT(u.first_name, ' ', u.last_name) as name,
                COALESCE(u.billing_email, u.email) as email,
                u.billing_address as address,
                u.billing_city as city,
                u.billing_postal_code as postal_code,
                u.billing_country as country,
                u.vat_number,
                u.is_active
            FROM users u
            WHERE u.can_be_invoiced = 1 AND u.is_active = 1
        ");
        
        // Add sample billing data for admin user (optional)
        $pdo->exec("
            UPDATE users 
            SET 
                billing_address = '123 Admin Street',
                billing_city = 'Luxembourg',
                billing_postal_code = 'L-1234',
                billing_country = 'Luxembourg',
                billing_email = email
            WHERE role = 'admin' 
            AND billing_address IS NULL
            LIMIT 1
        ");
    }
    
    public function down(PDO $pdo): void
    {
        // Drop view
        $pdo->exec("DROP VIEW IF EXISTS `invoice_recipients`");
        
        // Remove permissions
        $pdo->exec("DELETE FROM `permissions` WHERE `code` IN ('invoices.create_user', 'invoices.view_own')");
        
        // Drop user invoice preferences table
        $pdo->exec("DROP TABLE IF EXISTS `user_invoice_preferences`");
        
        // Remove columns from users table
        $pdo->exec("ALTER TABLE `users` DROP FOREIGN KEY `fk_user_payment_method`");
        $pdo->exec("
            ALTER TABLE `users` 
            DROP COLUMN `billing_address`,
            DROP COLUMN `billing_city`,
            DROP COLUMN `billing_postal_code`,
            DROP COLUMN `billing_country`,
            DROP COLUMN `vat_number`,
            DROP COLUMN `billing_email`,
            DROP COLUMN `preferred_payment_method_id`,
            DROP COLUMN `can_be_invoiced`
        ");
        
        // Remove user_id from invoices
        $pdo->exec("ALTER TABLE `invoices` DROP FOREIGN KEY `fk_invoice_user`");
        $pdo->exec("ALTER TABLE `invoices` DROP COLUMN `user_id`");
    }
};