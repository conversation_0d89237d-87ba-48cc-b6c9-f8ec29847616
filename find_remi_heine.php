<?php
/**
 * Find Rémi Heine in database and check VAT information
 */

// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found!");
}

$envContent = file_get_contents($envFile);
$lines = explode("\n", $envContent);
$env = [];

foreach ($lines as $line) {
    $line = trim($line);
    if (empty($line) || strpos($line, '#') === 0) continue;
    
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $env[$key] = $value;
    }
}

// Database configuration from .env
$dbHost = $env['DB_HOST'] ?? '127.0.0.1';
$dbPort = $env['DB_PORT'] ?? '3306';
$dbName = $env['DB_DATABASE'] ?? 'fitapp';
$dbUser = $env['DB_USERNAME'] ?? 'root';
$dbPass = $env['DB_PASSWORD'] ?? '';

echo "<pre>";
echo "=== Finding Rémi Heine in Database ===\n\n";

try {
    // Connect to database using .env credentials
    $dsn = "mysql:host=$dbHost;port=$dbPort;dbname=$dbName;charset=utf8mb4";
    $db = new PDO($dsn, $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Search in users table
    echo "1. Searching in USERS table:\n";
    $stmt = $db->prepare("
        SELECT 
            u.id, u.username, u.email,
            u.first_name, u.last_name,
            CONCAT(u.first_name, ' ', u.last_name) as full_name,
            u.vat_number, u.vat_intercommunautaire,
            u.is_intracommunity, u.is_practitioner,
            u.address, u.postal_code, u.city, u.country,
            u.billing_address, u.billing_postal_code, u.billing_city, u.billing_country,
            ugm.group_id,
            ug.name as group_name
        FROM users u
        LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
        LEFT JOIN user_groups ug ON ugm.group_id = ug.id
        WHERE (u.first_name = 'Rémi' AND u.last_name = 'Heine')
           OR (u.first_name = 'Remi' AND u.last_name = 'Heine')
           OR CONCAT(u.first_name, ' ', u.last_name) LIKE '%Rémi Heine%'
           OR CONCAT(u.first_name, ' ', u.last_name) LIKE '%Remi Heine%'
    ");
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($users)) {
        foreach ($users as $user) {
            echo "\n✓ Found in users table:\n";
            echo "  ID: " . $user['id'] . "\n";
            echo "  Username: " . $user['username'] . "\n";
            echo "  Name: " . $user['full_name'] . "\n";
            echo "  Email: " . $user['email'] . "\n";
            echo "  Is Practitioner: " . ($user['is_practitioner'] ? 'Yes' : 'No') . "\n";
            echo "  Group: " . $user['group_name'] . " (ID: " . $user['group_id'] . ")\n";
            echo "\n  VAT Information:\n";
            echo "  VAT Number: " . ($user['vat_number'] ?: '(empty)') . "\n";
            echo "  VAT Intercommunautaire: " . ($user['vat_intercommunautaire'] ?: '(empty)') . "\n";
            echo "  Is Intracommunity: " . ($user['is_intracommunity'] ? 'Yes' : 'No') . "\n";
            echo "\n  Address Information:\n";
            echo "  Address: " . ($user['address'] ?: '(empty)') . "\n";
            echo "  Postal Code: " . ($user['postal_code'] ?: '(empty)') . "\n";
            echo "  City: " . ($user['city'] ?: '(empty)') . "\n";
            echo "  Country: " . ($user['country'] ?: '(empty)') . "\n";
            echo "\n  Billing Address:\n";
            echo "  Address: " . ($user['billing_address'] ?: '(empty)') . "\n";
            echo "  Postal Code: " . ($user['billing_postal_code'] ?: '(empty)') . "\n";
            echo "  City: " . ($user['billing_city'] ?: '(empty)') . "\n";
            echo "  Country: " . ($user['billing_country'] ?: '(empty)') . "\n";
        }
    } else {
        echo "✗ Not found in users table\n";
    }
    
    // Search in clients table
    echo "\n\n2. Searching in CLIENTS table:\n";
    $stmt = $db->prepare("
        SELECT 
            c.id, c.client_number, c.client_type,
            c.first_name, c.last_name, c.company_name,
            COALESCE(c.company_name, CONCAT(c.first_name, ' ', c.last_name)) as display_name,
            c.vat_number, c.tax_exempt,
            c.address, c.postal_code, c.city, c.country,
            c.is_practitioner, c.is_active
        FROM clients c
        WHERE (c.first_name = 'Rémi' AND c.last_name = 'Heine')
           OR (c.first_name = 'Remi' AND c.last_name = 'Heine')
           OR c.company_name LIKE '%Rémi Heine%'
           OR c.company_name LIKE '%Remi Heine%'
    ");
    $stmt->execute();
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($clients)) {
        foreach ($clients as $client) {
            echo "\n✓ Found in clients table:\n";
            echo "  ID: " . $client['id'] . "\n";
            echo "  Client Number: " . $client['client_number'] . "\n";
            echo "  Name: " . $client['display_name'] . "\n";
            echo "  Type: " . $client['client_type'] . "\n";
            echo "  Is Practitioner: " . ($client['is_practitioner'] ? 'Yes' : 'No') . "\n";
            echo "  Is Active: " . ($client['is_active'] ? 'Yes' : 'No') . "\n";
            echo "\n  VAT Information:\n";
            echo "  VAT Number: " . ($client['vat_number'] ?: '(empty)') . "\n";
            echo "  Tax Exempt: " . ($client['tax_exempt'] ? 'Yes' : 'No') . "\n";
            echo "\n  Address:\n";
            echo "  Address: " . ($client['address'] ?: '(empty)') . "\n";
            echo "  Postal Code: " . ($client['postal_code'] ?: '(empty)') . "\n";
            echo "  City: " . ($client['city'] ?: '(empty)') . "\n";
            echo "  Country: " . ($client['country'] ?: '(empty)') . "\n";
        }
    } else {
        echo "✗ Not found in clients table\n";
    }
    
    // Check retrocession data entries
    echo "\n\n3. Checking RETROCESSION data:\n";
    if (!empty($users)) {
        foreach ($users as $user) {
            $stmt = $db->prepare("
                SELECT 
                    rde.id, rde.period_month, rde.period_year,
                    rde.status, rde.exclude_patient_line,
                    rde.invoice_id, i.invoice_number
                FROM retrocession_data_entry rde
                LEFT JOIN invoices i ON rde.invoice_id = i.id
                WHERE rde.practitioner_id = :user_id
                ORDER BY rde.period_year DESC, rde.period_month DESC
                LIMIT 5
            ");
            $stmt->execute(['user_id' => $user['id']]);
            $entries = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (!empty($entries)) {
                echo "\n✓ Found retrocession entries for user ID " . $user['id'] . ":\n";
                foreach ($entries as $entry) {
                    echo sprintf("  - %02d/%d: Status=%s, Exclude Patient=%s, Invoice=%s\n",
                        $entry['period_month'],
                        $entry['period_year'],
                        $entry['status'],
                        $entry['exclude_patient_line'] ? 'Yes' : 'No',
                        $entry['invoice_number'] ?: 'None'
                    );
                }
            }
        }
    }
    
    echo "\n=== Summary ===\n";
    echo "The issue is that when creating a retrocession invoice for Rémi Heine,\n";
    echo "the system displays VAT intracommunautaire information even though\n";
    echo "he doesn't have a VAT number and it's not needed on retrocession PDFs.\n";
    echo "\nNext steps:\n";
    echo "1. Check if VAT fields are empty or contain default values\n";
    echo "2. Modify the invoice display logic to hide VAT info for retrocession invoices\n";
    echo "3. Update the PDF template to exclude VAT info for practitioners without VAT numbers\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
echo "</pre>";