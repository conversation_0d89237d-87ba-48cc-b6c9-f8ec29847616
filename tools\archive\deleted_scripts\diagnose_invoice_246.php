<?php
// Diagnose invoice 246 duplicate lines issue

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Diagnose Invoice 246 - Duplicate Lines Issue</h2>";
    
    $invoiceId = 246;
    
    // 1. Check invoice details
    echo "<h3>1. Invoice Details</h3>";
    $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
    $stmt->execute([$invoiceId]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice 246 not found!</p>";
        exit;
    }
    
    echo "<p>Invoice Number: <strong>" . $invoice['invoice_number'] . "</strong></p>";
    echo "<p>Status: " . $invoice['status'] . "</p>";
    echo "<p>Total: " . $invoice['total'] . " " . $invoice['currency'] . "</p>";
    
    // 2. Check invoice lines in database
    echo "<h3>2. Invoice Lines in Database</h3>";
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order, id
    ");
    $stmt->execute([$invoiceId]);
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total lines found: <strong>" . count($lines) . "</strong></p>";
    
    echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th><th>Sort Order</th>";
    echo "</tr>";
    
    $descriptions = [];
    foreach ($lines as $line) {
        echo "<tr>";
        echo "<td>" . $line['id'] . "</td>";
        echo "<td>" . $line['description'] . "</td>";
        echo "<td>" . $line['quantity'] . "</td>";
        echo "<td>" . $line['unit_price'] . "</td>";
        echo "<td>" . $line['vat_rate'] . "%</td>";
        echo "<td>" . $line['line_total'] . "</td>";
        echo "<td>" . ($line['sort_order'] ?? 'NULL') . "</td>";
        echo "</tr>";
        
        // Track descriptions
        if (!isset($descriptions[$line['description']])) {
            $descriptions[$line['description']] = 0;
        }
        $descriptions[$line['description']]++;
    }
    echo "</table>";
    
    // 3. Check for duplicate descriptions
    echo "<h3>3. Description Analysis</h3>";
    $hasDuplicates = false;
    foreach ($descriptions as $desc => $count) {
        if ($count > 1) {
            echo "<p style='color: red;'>⚠️ '<strong>$desc</strong>' appears <strong>$count times</strong></p>";
            $hasDuplicates = true;
        } else {
            echo "<p style='color: green;'>✓ '$desc' appears once</p>";
        }
    }
    
    if (!$hasDuplicates) {
        echo "<p style='color: green;'>✓ No duplicate descriptions found in database</p>";
    }
    
    // 4. Check cache
    echo "<h3>4. Cache Check</h3>";
    $cacheKey = 'invoice_details_' . $invoiceId;
    $cacheDir = __DIR__ . '/../storage/cache/invoices/';
    $cacheFile = $cacheDir . md5($cacheKey) . '.cache';
    
    if (file_exists($cacheFile)) {
        echo "<p style='color: orange;'>⚠️ Cache file exists: " . basename($cacheFile) . "</p>";
        $cacheData = unserialize(file_get_contents($cacheFile));
        if (isset($cacheData['lines'])) {
            echo "<p>Cached lines count: <strong>" . count($cacheData['lines']) . "</strong></p>";
            
            // Show cached lines
            echo "<h4>Lines in Cache:</h4>";
            echo "<ul>";
            foreach ($cacheData['lines'] as $line) {
                echo "<li>" . $line['description'] . " (ID: " . $line['id'] . ")</li>";
            }
            echo "</ul>";
        }
    } else {
        echo "<p>No cache file found</p>";
    }
    
    // 5. Simulate PDF query
    echo "<h3>5. Simulating PDF Query</h3>";
    echo "<p>The PDF uses getInvoiceWithDetails() which runs this query:</p>";
    echo "<pre style='background: #f4f4f4; padding: 10px;'>";
    echo "SELECT * FROM invoice_lines\n";
    echo "WHERE invoice_id = $invoiceId\n";
    echo "ORDER BY sort_order, id";
    echo "</pre>";
    
    // 6. Actions
    if ($hasDuplicates || file_exists($cacheFile)) {
        echo "<h3>6. Recommended Actions</h3>";
        echo "<form method='POST'>";
        
        if ($hasDuplicates) {
            echo "<p><label><input type='checkbox' name='remove_duplicates' value='1' checked> Remove duplicate lines (keep first occurrence)</label></p>";
        }
        
        if (file_exists($cacheFile)) {
            echo "<p><label><input type='checkbox' name='clear_cache' value='1' checked> Clear invoice cache</label></p>";
        }
        
        echo "<button type='submit' name='action' value='fix' style='font-size: 16px; padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;'>Fix Issues</button>";
        echo "</form>";
    } else {
        echo "<h3 style='color: green;'>✓ No issues found in database or cache</h3>";
        echo "<p>The PDF might be using a different query or there might be an issue in the PDF generation code itself.</p>";
    }
    
    // Handle fix action
    if (isset($_POST['action']) && $_POST['action'] == 'fix') {
        echo "<h3>Fixing Issues...</h3>";
        
        if (isset($_POST['remove_duplicates']) && $hasDuplicates) {
            // Remove duplicates
            $seen = [];
            foreach ($lines as $line) {
                $key = $line['description'] . '_' . $line['unit_price'];
                if (isset($seen[$key])) {
                    // Delete duplicate
                    $stmt = $db->prepare("DELETE FROM invoice_lines WHERE id = ?");
                    $stmt->execute([$line['id']]);
                    echo "<p>✓ Removed duplicate line ID " . $line['id'] . "</p>";
                } else {
                    $seen[$key] = true;
                }
            }
        }
        
        if (isset($_POST['clear_cache'])) {
            if (file_exists($cacheFile)) {
                unlink($cacheFile);
                echo "<p>✓ Cache cleared</p>";
            }
        }
        
        echo "<p style='color: green;'><strong>Done! Refresh to see updated results.</strong></p>";
        echo "<script>setTimeout(() => location.reload(), 2000);</script>";
    }
    
    // Test links
    echo "<h3>Test Links</h3>";
    echo "<p><a href='/fit/public/invoices/246' target='_blank'>View Invoice 246</a></p>";
    echo "<p><a href='/fit/public/invoice-pdf.php?id=246' target='_blank'>View PDF</a></p>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>