/* ========================================
   COMPREHENSIVE MOBILE OPTIMIZATIONS
   For Fit360 AdminDesk
   ======================================== */

/* ========================================
   1. CORE MOBILE RESET & FOUNDATIONS
   ======================================== */

/* Prevent iOS zoom on form focus */
@media (max-width: 767px) {
    input[type="text"],
    input[type="email"],
    input[type="number"],
    input[type="password"],
    input[type="tel"],
    input[type="url"],
    input[type="date"],
    input[type="datetime-local"],
    input[type="month"],
    input[type="time"],
    input[type="week"],
    select,
    textarea {
        font-size: 16px !important;
    }
}

/* Improved touch targets globally */
@media (hover: none) and (pointer: coarse) {
    a, button, input, select, textarea, 
    [role="button"], [tabindex]:not([tabindex="-1"]) {
        min-height: 44px;
        min-width: 44px;
    }
    
    /* Smaller targets in dense areas */
    .table a, .table button,
    .dropdown-item, .nav-link {
        min-height: 36px;
    }
}

/* ========================================
   2. RESPONSIVE TYPOGRAPHY
   ======================================== */

/* Fluid typography scaling */
@media (max-width: 767px) {
    :root {
        --fs-base: clamp(14px, 4vw, 16px);
        --fs-small: clamp(12px, 3.5vw, 14px);
        --fs-large: clamp(16px, 4.5vw, 18px);
        --fs-h1: clamp(24px, 6vw, 32px);
        --fs-h2: clamp(20px, 5vw, 28px);
        --fs-h3: clamp(18px, 4.5vw, 24px);
        --fs-h4: clamp(16px, 4vw, 20px);
        --fs-h5: clamp(14px, 3.5vw, 18px);
        --fs-h6: clamp(12px, 3vw, 16px);
    }
    
    body { font-size: var(--fs-base); }
    small, .small { font-size: var(--fs-small); }
    .lead { font-size: var(--fs-large); }
    h1, .h1 { font-size: var(--fs-h1); }
    h2, .h2 { font-size: var(--fs-h2); }
    h3, .h3 { font-size: var(--fs-h3); }
    h4, .h4 { font-size: var(--fs-h4); }
    h5, .h5 { font-size: var(--fs-h5); }
    h6, .h6 { font-size: var(--fs-h6); }
}

/* ========================================
   3. MOBILE NAVIGATION PATTERNS
   ======================================== */

/* Bottom navigation bar */
.mobile-bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--bs-white);
    border-top: 1px solid var(--bs-border-color);
    display: none;
    z-index: 1030;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 767px) {
    .mobile-bottom-nav {
        display: flex;
        justify-content: space-around;
        padding: 0.5rem 0;
    }
    
    .mobile-bottom-nav .nav-item {
        flex: 1;
        text-align: center;
    }
    
    .mobile-bottom-nav .nav-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0.5rem;
        font-size: 0.75rem;
        color: var(--bs-gray-600);
        transition: color 0.2s;
    }
    
    .mobile-bottom-nav .nav-link.active {
        color: var(--bs-primary);
    }
    
    .mobile-bottom-nav .nav-link i {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }
    
    /* Adjust main content for bottom nav */
    .main-content {
        padding-bottom: 4.5rem;
    }
}

/* Hamburger menu enhancement */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1100;
    width: 44px;
    height: 44px;
    padding: 0;
    background: var(--bs-primary);
    border: none;
    border-radius: 0.5rem;
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

@media (max-width: 991px) {
    .mobile-menu-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .mobile-menu-toggle span {
        display: block;
        width: 22px;
        height: 2px;
        background: white;
        position: relative;
        transition: all 0.3s;
    }
    
    .mobile-menu-toggle span::before,
    .mobile-menu-toggle span::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: white;
        transition: all 0.3s;
    }
    
    .mobile-menu-toggle span::before {
        top: -6px;
    }
    
    .mobile-menu-toggle span::after {
        bottom: -6px;
    }
    
    .mobile-menu-toggle.active span {
        background: transparent;
    }
    
    .mobile-menu-toggle.active span::before {
        top: 0;
        transform: rotate(45deg);
    }
    
    .mobile-menu-toggle.active span::after {
        bottom: 0;
        transform: rotate(-45deg);
    }
}

/* ========================================
   4. RESPONSIVE TABLES
   ======================================== */

/* Card-based table layout for mobile */
@media (max-width: 767px) {
    .table-mobile-cards {
        display: block;
    }
    
    .table-mobile-cards thead {
        display: none;
    }
    
    .table-mobile-cards tbody {
        display: block;
    }
    
    .table-mobile-cards tr {
        display: block;
        margin-bottom: 1rem;
        border: 1px solid var(--bs-border-color);
        border-radius: 0.5rem;
        background: var(--bs-white);
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        overflow: hidden;
    }
    
    .table-mobile-cards td {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 1rem;
        border: none;
        border-bottom: 1px solid var(--bs-gray-200);
    }
    
    .table-mobile-cards td:last-child {
        border-bottom: none;
    }
    
    .table-mobile-cards td::before {
        content: attr(data-label);
        font-weight: 600;
        color: var(--bs-gray-700);
        flex-shrink: 0;
        margin-right: 1rem;
    }
    
    .table-mobile-cards td[data-priority="low"] {
        display: none;
    }
    
    /* Actions row */
    .table-mobile-cards .actions-cell {
        background: var(--bs-gray-100);
        justify-content: center;
        padding: 0.5rem;
    }
    
    .table-mobile-cards .actions-cell::before {
        display: none;
    }
}

/* Horizontal scroll for data-heavy tables */
.table-mobile-scroll {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin: 0 -1rem;
    padding: 0 1rem;
}

@media (max-width: 767px) {
    .table-mobile-scroll table {
        min-width: 600px;
    }
    
    .table-mobile-scroll::-webkit-scrollbar {
        height: 6px;
    }
    
    .table-mobile-scroll::-webkit-scrollbar-track {
        background: var(--bs-gray-200);
    }
    
    .table-mobile-scroll::-webkit-scrollbar-thumb {
        background: var(--bs-gray-500);
        border-radius: 3px;
    }
}

/* ========================================
   5. MOBILE FORMS
   ======================================== */

@media (max-width: 767px) {
    /* Stack all form groups */
    .form-row {
        display: block !important;
    }
    
    .form-row > [class*="col"] {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 1rem;
    }
    
    /* Enhanced form controls */
    .form-control,
    .form-select {
        padding: 0.75rem 1rem;
        font-size: 16px;
        border-radius: 0.5rem;
    }
    
    /* Floating labels optimization */
    .form-floating > label {
        padding: 1rem 1rem;
    }
    
    .form-floating > .form-control,
    .form-floating > .form-select {
        height: calc(3.5rem + 2px);
    }
    
    /* Radio and checkbox groups */
    .form-check {
        padding: 0.75rem 0;
    }
    
    .form-check-input {
        width: 1.5rem;
        height: 1.5rem;
        margin-top: 0;
        margin-right: 0.75rem;
        border-width: 2px;
    }
    
    .form-check-label {
        padding-left: 0.5rem;
        line-height: 1.5rem;
    }
    
    /* Input groups */
    .input-group {
        flex-direction: column;
    }
    
    .input-group > * {
        width: 100%;
        border-radius: 0.5rem !important;
        margin-bottom: 0.5rem;
    }
    
    .input-group > *:last-child {
        margin-bottom: 0;
    }
}

/* ========================================
   6. MOBILE MODALS & OVERLAYS
   ======================================== */

@media (max-width: 767px) {
    /* Full-screen modals on mobile */
    .modal-dialog {
        margin: 0;
        width: 100%;
        max-width: 100%;
        height: 100%;
    }
    
    .modal-content {
        height: 100%;
        border: none;
        border-radius: 0;
    }
    
    .modal-header {
        position: sticky;
        top: 0;
        background: var(--bs-white);
        z-index: 10;
        padding: 1rem;
        border-bottom: 1px solid var(--bs-border-color);
    }
    
    .modal-body {
        padding: 1rem;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .modal-footer {
        position: sticky;
        bottom: 0;
        background: var(--bs-white);
        padding: 1rem;
        border-top: 1px solid var(--bs-border-color);
    }
    
    /* Sheet-style modals */
    .modal-sheet .modal-dialog {
        position: fixed;
        bottom: 0;
        margin: 0;
        height: auto;
        max-height: 90vh;
    }
    
    .modal-sheet .modal-content {
        border-radius: 1rem 1rem 0 0;
        height: auto;
    }
}

/* Mobile-optimized tooltips and popovers */
@media (hover: none) and (pointer: coarse) {
    .tooltip {
        font-size: 14px;
        padding: 0.5rem 0.75rem;
    }
    
    .popover {
        max-width: 90vw;
    }
    
    .popover-body {
        padding: 1rem;
        font-size: 14px;
    }
}

/* ========================================
   7. MOBILE PERFORMANCE OPTIMIZATIONS
   ======================================== */

/* Reduce animations on mobile */
@media (max-width: 767px) and (prefers-reduced-motion: no-preference) {
    *,
    *::before,
    *::after {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }
    
    /* Disable parallax and complex animations */
    .parallax,
    .animate-on-scroll {
        transform: none !important;
        animation: none !important;
    }
}

/* Optimize images for mobile */
@media (max-width: 767px) {
    img {
        max-width: 100%;
        height: auto;
    }
    
    /* Lazy loading indicators */
    img[loading="lazy"] {
        background: var(--bs-gray-100);
        min-height: 100px;
    }
    
    /* Responsive images in cards */
    .card-img-top {
        height: 200px;
        object-fit: cover;
    }
}

/* ========================================
   8. MOBILE UTILITIES
   ======================================== */

/* Touch-specific utilities */
@media (hover: none) and (pointer: coarse) {
    .hover\:hidden {
        display: none !important;
    }
    
    .touch\:block {
        display: block !important;
    }
    
    .touch\:large {
        transform: scale(1.1);
    }
}

/* Mobile-only utilities */
@media (max-width: 767px) {
    .mobile-only { display: block !important; }
    .mobile-hidden { display: none !important; }
    .mobile-fullwidth { width: 100% !important; }
    .mobile-center { text-align: center !important; }
    .mobile-stack { flex-direction: column !important; }
    .mobile-reverse { flex-direction: column-reverse !important; }
    .mobile-spacing { margin: 1rem 0 !important; }
    .mobile-compact { padding: 0.5rem !important; }
    .mobile-rounded { border-radius: 0.5rem !important; }
}

/* Desktop-only utilities */
@media (min-width: 768px) {
    .desktop-only { display: block !important; }
    .desktop-hidden { display: none !important; }
}

/* ========================================
   9. SWIPE GESTURES & TOUCH INTERACTIONS
   ======================================== */

/* Swipeable lists */
.swipeable-list-item {
    position: relative;
    overflow: hidden;
    touch-action: pan-y;
}

.swipeable-list-item .swipe-actions {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    transform: translateX(100%);
    transition: transform 0.3s;
}

.swipeable-list-item.swiped .swipe-actions {
    transform: translateX(0);
}

/* Pull to refresh */
.pull-to-refresh-container {
    position: relative;
    overflow: hidden;
}

.pull-to-refresh-indicator {
    position: absolute;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: var(--bs-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: top 0.3s;
    z-index: 1000;
}

.pull-to-refresh-indicator.show {
    top: 20px;
}

.pull-to-refresh-indicator.refreshing {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: translateX(-50%) rotate(0deg); }
    to { transform: translateX(-50%) rotate(360deg); }
}

/* ========================================
   10. OFFLINE MODE INDICATORS
   ======================================== */

.offline-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: var(--bs-warning);
    color: var(--bs-dark);
    text-align: center;
    padding: 0.5rem;
    font-size: 0.875rem;
    transform: translateY(-100%);
    transition: transform 0.3s;
    z-index: 2000;
}

.offline-indicator.show {
    transform: translateY(0);
}

/* ========================================
   11. MOBILE LOADING STATES
   ======================================== */

/* Skeleton screens for mobile */
.skeleton {
    background: linear-gradient(90deg, 
        var(--bs-gray-200) 25%, 
        var(--bs-gray-300) 50%, 
        var(--bs-gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 0.25rem;
}

.skeleton-button {
    height: 2.5rem;
    width: 100px;
    border-radius: 0.375rem;
}

/* Mobile spinner */
.mobile-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
}

.mobile-spinner::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    border: 3px solid var(--bs-gray-300);
    border-top-color: var(--bs-primary);
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

/* ========================================
   12. LANDSCAPE MODE ADJUSTMENTS
   ======================================== */

@media (max-width: 767px) and (orientation: landscape) {
    /* Reduce vertical spacing */
    .card {
        margin-bottom: 0.5rem;
    }
    
    .card-body {
        padding: 0.75rem;
    }
    
    /* Compact navigation */
    .navbar {
        padding: 0.25rem 1rem;
    }
    
    /* Hide less important elements */
    .landscape-hidden {
        display: none !important;
    }
    
    /* Adjust modal height */
    .modal-dialog {
        max-height: 90vh;
    }
    
    .modal-body {
        max-height: 60vh;
    }
}