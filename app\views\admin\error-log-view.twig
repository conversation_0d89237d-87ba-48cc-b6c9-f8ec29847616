{% extends "base-modern.twig" %}

{% block title %}{{ __('admin.error_details') }} - {{ error.error_id }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-1">{{ __('admin.error_details') }}</h1>
            <code class="text-muted">{{ error.error_id }}</code>
        </div>
        <div>
            <a href="/admin/errors" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left me-1"></i>{{ __('common.back') }}
            </a>
            {% if not error.resolved %}
            <button type="button" class="btn btn-success ms-2" data-bs-toggle="modal" data-bs-target="#resolveModal">
                <i class="bi bi-check-circle me-1"></i>{{ __('admin.mark_resolved') }}
            </button>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <!-- Error Information -->
            <div class="card mb-4">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0">{{ error.class|split('\\')|last }}: {{ error.message }}</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>{{ __('admin.file') }}:</strong><br>
                            <code class="small">{{ error.file }}</code>
                        </div>
                        <div class="col-md-6">
                            <strong>{{ __('admin.line') }}:</strong> {{ error.line }}
                        </div>
                    </div>
                    
                    <h6 class="mt-4 mb-3">{{ __('admin.stack_trace') }}</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0" style="max-height: 400px; overflow-y: auto;">{{ error.trace }}</pre>
                    </div>
                </div>
            </div>

            <!-- Request Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('admin.request_information') }}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th width="30%">{{ __('admin.method') }}</th>
                            <td><span class="badge bg-info">{{ error.request_method }}</span></td>
                        </tr>
                        <tr>
                            <th>{{ __('admin.uri') }}</th>
                            <td><code>{{ error.request_uri }}</code></td>
                        </tr>
                        <tr>
                            <th>{{ __('admin.ip_address') }}</th>
                            <td>{{ error.ip_address }}</td>
                        </tr>
                        <tr>
                            <th>{{ __('admin.user_agent') }}</th>
                            <td class="small">{{ error.user_agent }}</td>
                        </tr>
                    </table>

                    {% if error.request_params %}
                    <h6 class="mt-4 mb-3">{{ __('admin.request_parameters') }}</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0">{{ error.request_params|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                    </div>
                    {% endif %}

                    {% if error.request_headers %}
                    <h6 class="mt-4 mb-3">{{ __('admin.request_headers') }}</h6>
                    <div class="bg-light p-3 rounded">
                        <pre class="mb-0" style="max-height: 300px; overflow-y: auto;">{{ error.request_headers|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Error Metadata -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('admin.error_metadata') }}</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <th>{{ __('admin.timestamp') }}</th>
                            <td>{{ error.timestamp|date('Y-m-d H:i:s') }}</td>
                        </tr>
                        <tr>
                            <th>{{ __('admin.environment') }}</th>
                            <td>
                                <span class="badge bg-{% if error.environment == 'production' %}danger{% else %}warning{% endif %}">
                                    {{ error.environment }}
                                </span>
                            </td>
                        </tr>
                        <tr>
                            <th>{{ __('admin.status') }}</th>
                            <td>
                                {% if error.resolved %}
                                    <span class="badge bg-success">{{ __('admin.resolved') }}</span>
                                {% else %}
                                    <span class="badge bg-danger">{{ __('admin.unresolved') }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <th>{{ __('admin.error_code') }}</th>
                            <td>{{ error.code|default('0') }}</td>
                        </tr>
                    </table>
                </div>
            </div>

            <!-- User Information -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('admin.user_information') }}</h5>
                </div>
                <div class="card-body">
                    {% if error.user_id %}
                        <p><strong>{{ __('common.user') }}:</strong> {{ error.user_name }} (ID: {{ error.user_id }})</p>
                    {% else %}
                        <p class="text-muted">{{ __('admin.guest_user') }}</p>
                    {% endif %}
                    
                    {% if error.session_data %}
                        <h6 class="mt-3 mb-2">{{ __('admin.session_data') }}</h6>
                        <div class="bg-light p-2 rounded">
                            <pre class="mb-0 small" style="max-height: 200px; overflow-y: auto;">{{ error.session_data|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Performance Metrics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('admin.performance_metrics') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h4 class="mb-1">{{ error.execution_time }}s</h4>
                            <small class="text-muted">{{ __('admin.execution_time') }}</small>
                        </div>
                        <div class="col-6">
                            <h4 class="mb-1">{{ error.memory_peak }}MB</h4>
                            <small class="text-muted">{{ __('admin.memory_peak') }}</small>
                        </div>
                    </div>
                    <div class="row text-center mt-3">
                        <div class="col-12">
                            <h4 class="mb-1">{{ error.memory_usage }}MB</h4>
                            <small class="text-muted">{{ __('admin.memory_usage') }}</small>
                        </div>
                    </div>
                </div>
            </div>

            {% if error.notes %}
            <!-- Resolution Notes -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('admin.resolution_notes') }}</h5>
                </div>
                <div class="card-body">
                    <p class="mb-0">{{ error.notes|nl2br }}</p>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Resolve Modal -->
<div class="modal fade" id="resolveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="/admin/errors/{{ error.id }}/resolve">
                {{ csrf_field() }}
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('admin.mark_error_resolved') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ __('admin.resolution_notes') }}</label>
                        <textarea name="notes" class="form-control" rows="4" 
                                  placeholder="{{ __('admin.resolution_notes_placeholder') }}"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-check-circle me-1"></i>{{ __('admin.mark_resolved') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}