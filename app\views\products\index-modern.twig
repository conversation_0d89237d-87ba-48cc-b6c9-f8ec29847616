{% extends "base-modern.twig" %}

{% block title %}{{ __('products.title') | default('Products') }}{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item active">{{ __('products.title') | default('Products') }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow-sm">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ __('products.list') | default('Product List') }}</h5>
                <div>
                    <a href="{{ base_url }}/products/categories" class="btn btn-secondary btn-sm">
                        <i class="fas fa-folder"></i> {{ __('products.categories') | default('Categories') }}
                    </a>
                    <a href="{{ base_url }}/products/create" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> {{ __('products.create') | default('Add Product') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filters -->
                <form method="get" action="{{ base_url }}/products" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="{{ __('common.search') | default('Search...') }}" 
                                   value="{{ filters.search }}">
                        </div>
                        <div class="col-md-2">
                            <select name="category" class="form-select">
                                <option value="">{{ __('products.all_categories') | default('All Categories') }}</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if filters.category == category.id %}selected{% endif %}>
                                        {{ category.getDisplayName() }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="type" class="form-select">
                                <option value="">{{ __('products.all_types') | default('All Types') }}</option>
                                <option value="product" {% if filters.type == 'product' %}selected{% endif %}>
                                    {{ __('products.type_product') | default('Product') }}
                                </option>
                                <option value="service" {% if filters.type == 'service' %}selected{% endif %}>
                                    {{ __('products.type_service') | default('Service') }}
                                </option>
                                <option value="package" {% if filters.type == 'package' %}selected{% endif %}>
                                    {{ __('products.type_package') | default('Package') }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <select name="stock_status" class="form-select">
                                <option value="">{{ __('products.all_stock') | default('All Stock Status') }}</option>
                                <option value="in" {% if filters.stock_status == 'in' %}selected{% endif %}>
                                    {{ __('products.in_stock') | default('In Stock') }}
                                </option>
                                <option value="low" {% if filters.stock_status == 'low' %}selected{% endif %}>
                                    {{ __('products.low_stock') | default('Low Stock') }}
                                </option>
                                <option value="out" {% if filters.stock_status == 'out' %}selected{% endif %}>
                                    {{ __('products.out_of_stock') | default('Out of Stock') }}
                                </option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> {{ __('common.filter') | default('Filter') }}
                            </button>
                            <a href="/products" class="btn btn-secondary">
                                <i class="fas fa-times"></i> {{ __('common.reset') | default('Reset') }}
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Bulk Actions -->
                <div class="d-flex justify-content-between align-items-center mb-3" id="bulkActions" style="display: none;">
                    <div>
                        <span class="text-muted"><span id="selectedCount">0</span> {{ __('common.selected') | default('selected') }}</span>
                    </div>
                    <div>
                        <button type="button" class="btn btn-sm btn-danger" onclick="bulkDelete()">
                            <i class="fas fa-trash me-1"></i>{{ __('common.delete_selected') | default('Delete Selected') }}
                        </button>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="exportSelected()">
                            <i class="fas fa-download me-1"></i>{{ __('common.export') | default('Export') }}
                        </button>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="40">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="selectAll">
                                    </div>
                                </th>
                                <th>{{ __('products.code') | default('Code') }}</th>
                                <th>{{ __('products.name') | default('Name') }}</th>
                                <th>{{ __('products.category') | default('Category') }}</th>
                                <th>{{ __('products.type') | default('Type') }}</th>
                                <th class="text-end">{{ __('products.price') | default('Price') }}</th>
                                <th class="text-center">{{ __('products.stock') | default('Stock') }}</th>
                                <th class="text-center">{{ __('common.status') | default('Status') }}</th>
                                <th class="text-end">{{ __('common.actions') | default('Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products %}
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input product-checkbox" type="checkbox" value="{{ product.id }}">
                                    </div>
                                </td>
                                <td>
                                    <code>{{ product.code }}</code>
                                </td>
                                <td>
                                    <a href="{{ base_url }}/products/{{ product.id }}" class="text-decoration-none">
                                        {{ product.name }}
                                    </a>
                                    {% if product.quick_sale_button %}
                                        <span class="badge bg-info ms-1" title="Quick Sale">
                                            <i class="fas fa-bolt"></i>
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-muted">
                                        <i class="fas {{ product.category.icon | default('fa-folder') }}"></i>
                                        {{ product.category.name }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ product.item_type }}</span>
                                </td>
                                <td class="text-end">
                                    {{ product.unit_price | number_format(2, ',', ' ') }} €
                                    <small class="text-muted d-block">
                                        +{{ product.vatRate.rate }} % TVA
                                    </small>
                                </td>
                                <td class="text-center">
                                    {% if product.is_stockable %}
                                        {% set stockStatus = product.getStockStatus() %}
                                        <span class="badge bg-{{ product.getStockStatusColor() }}">
                                            {{ product.current_stock }}
                                        </span>
                                        {% if stockStatus == 'low_stock' %}
                                            <i class="fas fa-exclamation-triangle text-warning ms-1" 
                                               title="Low stock alert: {{ product.low_stock_alert }}"></i>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    {% if product.is_active %}
                                        <span class="badge bg-success">{{ __('common.active') | default('Active') }}</span>
                                    {% else %}
                                        <span class="badge bg-danger">{{ __('common.inactive') | default('Inactive') }}</span>
                                    {% endif %}
                                </td>
                                <td class="text-end">
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ base_url }}/products/{{ product.id }}" class="btn btn-outline-primary" 
                                           title="{{ __('common.view') | default('View') }}">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ base_url }}/products/{{ product.id }}/edit" class="btn btn-outline-secondary"
                                           title="{{ __('common.edit') | default('Edit') }}">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if product.is_stockable %}
                                        <button type="button" class="btn btn-outline-info"
                                                onclick="adjustStock({{ product.id }}, '{{ product.name | escape('js') }}')"
                                                title="{{ __('products.adjust_stock') | default('Adjust Stock') }}">
                                            <i class="fas fa-boxes"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    {{ __('products.no_products') | default('No products found') }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if pagination.has_pages %}
                <div class="d-flex justify-content-between align-items-center mt-4">
                    <div>
                        {{ __('common.showing') | default('Showing') }} 
                        {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} - 
                        {{ min(pagination.current_page * pagination.per_page, pagination.total) }} 
                        {{ __('common.of') | default('of') }} {{ pagination.total }}
                    </div>
                    <nav>
                        <ul class="pagination mb-0">
                            {% if pagination.current_page > 1 %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.current_page - 1 }}&search={{ filters.search }}&category={{ filters.category }}&type={{ filters.type }}&stock_status={{ filters.stock_status }}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for i in 1..pagination.last_page %}
                                {% if i == 1 or i == pagination.last_page or (i >= pagination.current_page - 2 and i <= pagination.current_page + 2) %}
                                    <li class="page-item {% if i == pagination.current_page %}active{% endif %}">
                                        <a class="page-link" href="?page={{ i }}&search={{ filters.search }}&category={{ filters.category }}&type={{ filters.type }}&stock_status={{ filters.stock_status }}">{{ i }}</a>
                                    </li>
                                {% elseif i == pagination.current_page - 3 or i == pagination.current_page + 3 %}
                                    <li class="page-item disabled"><span class="page-link">...</span></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if pagination.current_page < pagination.last_page %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ pagination.current_page + 1 }}&search={{ filters.search }}&category={{ filters.category }}&type={{ filters.type }}&stock_status={{ filters.stock_status }}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="stockForm" method="POST">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('products.adjust_stock') | default('Adjust Stock') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ __('products.product') | default('Product') }}</label>
                        <input type="text" class="form-control" id="productName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('products.adjustment_type') | default('Adjustment Type') }}</label>
                        <select name="type" class="form-select" required>
                            <option value="adjustment">{{ __('products.adjustment') | default('Adjustment') }}</option>
                            <option value="purchase">{{ __('products.purchase') | default('Purchase') }}</option>
                            <option value="return">{{ __('products.return') | default('Return') }}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('products.quantity') | default('Quantity') }}</label>
                        <input type="number" name="quantity" class="form-control" required>
                        <small class="text-muted">
                            {{ __('products.quantity_help') | default('Use positive numbers to add stock, negative to remove') }}
                        </small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('common.notes') | default('Notes') }}</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') | default('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        {{ __('common.save') | default('Save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function adjustStock(productId, productName) {
    document.getElementById('productName').value = productName;
    document.getElementById('stockForm').action = '{{ base_url }}/products/' + productId + '/adjust-stock';
    new bootstrap.Modal(document.getElementById('stockModal')).show();
}

document.getElementById('stockForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    fetch(this.action, {
        method: 'POST',
        body: new FormData(this),
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || 'Error adjusting stock');
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
});

// Select all functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.product-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');
    
    // Select all functionality
    selectAll.addEventListener('change', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });
    
    // Individual checkbox change
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
    
    // Update bulk actions visibility and count
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.product-checkbox:checked');
        const count = checkedBoxes.length;
        
        if (count > 0) {
            bulkActions.style.display = 'flex';
            selectedCount.textContent = count;
        } else {
            bulkActions.style.display = 'none';
        }
        
        // Update select all checkbox state
        selectAll.checked = count === checkboxes.length && count > 0;
        selectAll.indeterminate = count > 0 && count < checkboxes.length;
    }
    
    // Get selected product IDs
    function getSelectedIds() {
        const ids = [];
        document.querySelectorAll('.product-checkbox:checked').forEach(checkbox => {
            ids.push(checkbox.value);
        });
        return ids;
    }
    
    // Bulk delete
    window.bulkDelete = function() {
        const ids = getSelectedIds();
        if (ids.length === 0) return;
        
        if (!confirm('{{ __("products.delete_selected_confirm") | default("Are you sure you want to delete the selected products?") }}')) {
            return;
        }
        
        // Send delete request
        fetch('{{ base_url }}/api/products/bulk-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ ids: ids })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || '{{ __("common.error_occurred") | default("An error occurred") }}');
            }
        });
    };
    
    // Export selected
    window.exportSelected = function() {
        const ids = getSelectedIds();
        if (ids.length === 0) return;
        
        // Create form and submit
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/products/export';
        form.target = '_blank';
        
        // Add IDs
        ids.forEach(id => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'ids[]';
            input.value = id;
            form.appendChild(input);
        });
        
        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '{{ csrf_token }}';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);
    };
});
</script>
{% endblock %}