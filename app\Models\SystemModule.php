<?php

namespace App\Models;

use PDO;
use Flight;

class SystemModule
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Get all system modules with their parent relationships
     */
    public function getAllModules($includeInactive = false)
    {
        $sql = "SELECT * FROM system_modules";
        if (!$includeInactive) {
            $sql .= " WHERE is_active = 1";
        }
        $sql .= " ORDER BY sort_order, name";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get modules in hierarchical structure
     */
    public function getModuleHierarchy($includeInactive = false)
    {
        $modules = $this->getAllModules($includeInactive);
        return $this->buildHierarchy($modules);
    }
    
    /**
     * Build hierarchical structure from flat module list
     */
    private function buildHierarchy($modules, $parentId = null)
    {
        $hierarchy = [];
        
        foreach ($modules as $module) {
            if ($module['parent_id'] == $parentId) {
                $module['children'] = $this->buildHierarchy($modules, $module['id']);
                $hierarchy[] = $module;
            }
        }
        
        return $hierarchy;
    }
    
    /**
     * Get module by code
     */
    public function getByCode($code)
    {
        $stmt = $this->db->prepare("SELECT * FROM system_modules WHERE code = ?");
        $stmt->execute([$code]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get module by ID
     */
    public function getById($id)
    {
        $stmt = $this->db->prepare("SELECT * FROM system_modules WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get module permissions
     */
    public function getModulePermissions($moduleId)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM module_permissions 
            WHERE module_id = ? AND is_active = 1
            ORDER BY permission_code
        ");
        $stmt->execute([$moduleId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create a new module
     */
    public function create($data)
    {
        $stmt = $this->db->prepare("
            INSERT INTO system_modules (code, name, description, parent_id, icon, route_prefix, sort_order, is_active)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        return $stmt->execute([
            $data['code'],
            $data['name'],
            $data['description'] ?? null,
            $data['parent_id'] ?? null,
            $data['icon'] ?? 'fa-folder',
            $data['route_prefix'] ?? null,
            $data['sort_order'] ?? 0,
            $data['is_active'] ?? true
        ]);
    }
    
    /**
     * Update a module
     */
    public function update($id, $data)
    {
        $fields = [];
        $values = [];
        
        foreach (['name', 'description', 'parent_id', 'icon', 'route_prefix', 'sort_order', 'is_active'] as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $values[] = $data[$field];
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $values[] = $id;
        $sql = "UPDATE system_modules SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        
        return $stmt->execute($values);
    }
    
    /**
     * Check if user has permission for a module
     * This method works with the OLD schema where group_permissions has permission_id
     */
    public function userHasPermission($userId, $moduleCode, $permissionCode)
    {
        // For OLD schema compatibility:
        // We need to check permissions table directly since we don't have module_id in group_permissions
        // The permission code format in OLD schema is: category.action (e.g., 'invoices.view')
        // So we combine module code with permission action to match the OLD permission.code format
        
        $fullPermissionCode = $moduleCode . '.' . $permissionCode;
        
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as has_permission
            FROM group_permissions gp
            JOIN user_group_members ugm ON gp.group_id = ugm.group_id
            JOIN permissions p ON gp.permission_id = p.id
            WHERE ugm.user_id = ?
                AND p.code = ?
        ");
        
        $stmt->execute([$userId, $fullPermissionCode]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['has_permission'] > 0;
    }
}