<?php
/**
 * Force clear ALL caches and verify the fix
 */

// Prevent any caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: 0");

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <meta http-equiv='Cache-Control' content='no-cache, no-store, must-revalidate'>
    <meta http-equiv='Pragma' content='no-cache'>
    <meta http-equiv='Expires' content='0'>
    <title>Force Cache Clear</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .button { background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; text-decoration: none; display: inline-block; margin: 10px 5px; }
        .button:hover { background: #218838; }
        .button.secondary { background: #6c757d; }
        .button.secondary:hover { background: #5a6268; }
    </style>
</head>
<body>
<div class='container'>
    <h1>🔧 Force Cache Clear & Fix Verification</h1>";

// Step 1: Clear OPcache
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "<div class='success'>✅ OPcache cleared</div>";
} else {
    echo "<div class='info'>ℹ️ OPcache not available</div>";
}

// Step 2: Clear APCu cache if available
if (function_exists('apcu_clear_cache')) {
    apcu_clear_cache();
    echo "<div class='success'>✅ APCu cache cleared</div>";
}

// Step 3: Clear Twig cache
$twigCacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($twigCacheDir)) {
    $count = 0;
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($twigCacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
            $count++;
        }
    }
    echo "<div class='success'>✅ Twig cache cleared ($count files removed)</div>";
}

// Step 4: Clear any other cache directories
$cacheDirectories = [
    dirname(__DIR__) . '/storage/cache/views',
    dirname(__DIR__) . '/storage/cache/sessions',
    dirname(__DIR__) . '/storage/cache/files'
];

foreach ($cacheDirectories as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "<div class='success'>✅ Cleared: " . basename($dir) . "</div>";
    }
}

// Step 5: Touch the template file to update its modification time
$templateFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
if (file_exists($templateFile)) {
    touch($templateFile);
    echo "<div class='success'>✅ Template file modification time updated</div>";
}

// Step 6: Verify the fix
echo "<h2>📋 Verification Results:</h2>";
echo "<div class='info'>";

$content = file_get_contents($templateFile);

// Check for any remaining Twig expressions in template literals
$hasIssues = false;
$issueCount = 0;

// Pattern 1: Template literals with Twig
if (preg_match_all('/`[^`]*\{\{[^}]*\}\}[^`]*`/m', $content, $matches)) {
    $hasIssues = true;
    echo "<div class='error'>❌ Found " . count($matches[0]) . " template literals with Twig expressions</div>";
    foreach ($matches[0] as $match) {
        echo "<pre>" . htmlspecialchars($match) . "</pre>";
    }
    $issueCount += count($matches[0]);
}

// Pattern 2: Check specific lines that were problematic
$lines = explode("\n", $content);
$problemLines = [3598, 3607, 3638, 4015];
foreach ($problemLines as $lineNum) {
    if (isset($lines[$lineNum - 1])) {
        $line = $lines[$lineNum - 1];
        if (preg_match('/\{\{.*\}\}/', $line) && strpos($line, '`') !== false) {
            $hasIssues = true;
            echo "<div class='error'>❌ Line $lineNum still has Twig in template literal</div>";
            echo "<pre>" . htmlspecialchars($line) . "</pre>";
            $issueCount++;
        }
    }
}

if (!$hasIssues) {
    echo "<div class='success' style='font-size: 18px; font-weight: bold;'>✅ All syntax errors have been fixed!</div>";
    echo "<p>The template file is clean and contains no Twig expressions in template literals.</p>";
} else {
    echo "<div class='error'>⚠️ Found $issueCount issues that need fixing</div>";
}

echo "</div>";

// Step 7: Show modified time
$modTime = filemtime($templateFile);
echo "<div class='info'>
    <strong>Template file last modified:</strong> " . date('Y-m-d H:i:s', $modTime) . " 
    <small>(" . round((time() - $modTime) / 60) . " minutes ago)</small>
</div>";

// Generate unique URLs with timestamps
$timestamp = time() . rand(1000, 9999);
$invoiceUrl = "/fit/public/invoices/create?_t=" . $timestamp . "&nocache=1";
$debugUrl = "/fit/public/debug_syntax_error.php?_t=" . $timestamp;

echo "
<h2>🚀 Next Steps:</h2>
<div class='info'>
    <h3>Browser Cache Must Be Cleared!</h3>
    <p>The server-side fixes are complete, but your browser is still showing the cached version.</p>
    
    <h4>Method 1: Force Refresh (Recommended)</h4>
    <ol>
        <li>Click the 'Open Invoice Page' button below</li>
        <li>Once the page loads, immediately press:
            <ul>
                <li><strong>Windows/Linux:</strong> Ctrl + Shift + R or Ctrl + F5</li>
                <li><strong>Mac:</strong> Cmd + Shift + R</li>
            </ul>
        </li>
    </ol>
    
    <h4>Method 2: Developer Tools</h4>
    <ol>
        <li>Open Developer Tools (F12)</li>
        <li>Right-click the refresh button</li>
        <li>Select 'Empty Cache and Hard Reload'</li>
    </ol>
    
    <h4>Method 3: Clear Browser Data</h4>
    <ol>
        <li>Press Ctrl/Cmd + Shift + Delete</li>
        <li>Select 'Cached images and files'</li>
        <li>Time range: 'All time'</li>
        <li>Click 'Clear data'</li>
    </ol>
</div>

<div style='text-align: center; margin: 30px 0;'>
    <a href='$invoiceUrl' class='button' target='_blank'>Open Invoice Page</a>
    <a href='$debugUrl' class='button secondary' target='_blank'>Run Debug Check</a>
</div>

<div class='info'>
    <strong>💡 Pro Tip:</strong> If you still see the error after force refresh, try opening the page in an incognito/private window or a different browser.
</div>
";

// Add JavaScript to help with cache busting
echo "
<script>
// Try to clear browser cache programmatically
if ('caches' in window) {
    caches.keys().then(function(names) {
        for (let name of names) {
            caches.delete(name);
        }
        console.log('Browser caches cleared');
    });
}

// Add timestamp to all links
document.addEventListener('DOMContentLoaded', function() {
    const links = document.querySelectorAll('a[href*=\"/fit/public/\"]');
    links.forEach(link => {
        const url = new URL(link.href, window.location);
        url.searchParams.set('_cb', Date.now());
        link.href = url.toString();
    });
});
</script>
";

echo "</div></body></html>";
?>