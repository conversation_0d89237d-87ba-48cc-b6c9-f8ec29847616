<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firefox Add-on & Plugin Detector</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { padding: 12px 24px; margin: 8px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .success { border-left: 5px solid #28a745; background: #f8fff9; }
        .info { border-left: 5px solid #17a2b8; background: #f0f8ff; }
        .warning { border-left: 5px solid #ffc107; background: #fffdf5; }
        .error { border-left: 5px solid #dc3545; background: #fff5f5; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; font-size: 14px; border: 1px solid #e9ecef; white-space: pre-wrap; }
        .indicator { margin: 10px 0; padding: 10px; border-radius: 5px; background: #e9ecef; }
        .high-confidence { border-left: 5px solid #dc3545; }
        .medium-confidence { border-left: 5px solid #ffc107; }
        .low-confidence { border-left: 5px solid #17a2b8; }
        .loading { text-align: center; padding: 20px; }
        .hidden { display: none; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .summary-box { text-align: center; padding: 20px; border-radius: 10px; margin: 10px 0; }
        .risk-low { background: #d4edda; border: 2px solid #28a745; }
        .risk-medium { background: #fff3cd; border: 2px solid #ffc107; }
        .risk-high { background: #f8d7da; border: 2px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Firefox Add-on & Plugin Detector</h1>
            <p>Detect browser extensions that may interfere with JavaScript execution</p>
        </div>

        <div class="card">
            <h2>🎯 Detection Purpose</h2>
            <p>This tool helps identify Firefox add-ons, security software, and network modifications that might be causing the coach dropdown JavaScript errors.</p>
            
            <div class="grid">
                <div class="info">
                    <h3>What We Detect</h3>
                    <ul>
                        <li>Ad blockers (uBlock, AdBlock Plus, etc.)</li>
                        <li>Privacy extensions (Ghostery, Privacy Badger)</li>
                        <li>Security software (Antivirus, Firewall)</li>
                        <li>Network modifications (Proxy, VPN)</li>
                        <li>JavaScript blockers (NoScript, etc.)</li>
                    </ul>
                </div>
                <div class="warning">
                    <h3>Known Interfering Extensions</h3>
                    <ul>
                        <li><strong>uBlock Origin</strong> - May block JavaScript execution</li>
                        <li><strong>NoScript</strong> - Blocks JavaScript by default</li>
                        <li><strong>Ghostery</strong> - May interfere with DOM manipulation</li>
                        <li><strong>Privacy Badger</strong> - Blocks tracking scripts</li>
                        <li><strong>AdBlock Plus</strong> - May block dynamic content</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🔍 Run Detection</h2>
            <button class="btn btn-primary" onclick="runDetection()">🔍 Detect Add-ons & Plugins</button>
            <button class="btn btn-success" onclick="runQuickTest()">⚡ Quick JavaScript Test</button>
            <button class="btn btn-warning" onclick="clearResults()">🧹 Clear Results</button>
        </div>

        <div id="loading" class="loading hidden">
            <h3>🔄 Analyzing Browser Environment...</h3>
            <p>Checking for add-ons, plugins, and security software...</p>
        </div>

        <div id="results" class="hidden">
            <div class="card">
                <h2>📊 Detection Results</h2>
                <div id="summary"></div>
                <div id="browserInfo"></div>
                <div id="indicators"></div>
                <div id="recommendations"></div>
            </div>
        </div>

        <div class="card info">
            <h2>🛠️ Manual Tests</h2>
            <p>Run these tests to identify JavaScript blocking:</p>
            
            <div class="grid">
                <div>
                    <h3>Test 1: Basic JavaScript</h3>
                    <button class="btn btn-primary" onclick="testBasicJS()">Test Basic JS</button>
                    <div id="basicJSResult"></div>
                </div>
                <div>
                    <h3>Test 2: DOM Manipulation</h3>
                    <button class="btn btn-primary" onclick="testDOMManipulation()">Test DOM</button>
                    <div id="domResult"></div>
                </div>
                <div>
                    <h3>Test 3: AJAX Requests</h3>
                    <button class="btn btn-primary" onclick="testAJAX()">Test AJAX</button>
                    <div id="ajaxResult"></div>
                </div>
                <div>
                    <h3>Test 4: Console Access</h3>
                    <button class="btn btn-primary" onclick="testConsole()">Test Console</button>
                    <div id="consoleResult"></div>
                </div>
            </div>
        </div>

        <div class="card warning">
            <h2>⚠️ If Add-ons Are Detected</h2>
            <div class="grid">
                <div>
                    <h3>Firefox Settings</h3>
                    <ol>
                        <li>Open Firefox menu → Add-ons and Themes</li>
                        <li>Find the problematic extension</li>
                        <li>Click "..." → "Manage"</li>
                        <li>Toggle "Run in Private Windows" OFF</li>
                        <li>Or disable completely for testing</li>
                    </ol>
                </div>
                <div>
                    <h3>Quick Solutions</h3>
                    <ul>
                        <li><strong>Private Mode:</strong> Ctrl+Shift+P</li>
                        <li><strong>Safe Mode:</strong> Help → Troubleshoot Mode</li>
                        <li><strong>Disable Extensions:</strong> about:addons</li>
                        <li><strong>Fresh Profile:</strong> Create new Firefox profile</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function runDetection() {
            document.getElementById('loading').classList.remove('hidden');
            document.getElementById('results').classList.add('hidden');
            
            fetch('/fit/public/detect-firefox-addons.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('loading').classList.add('hidden');
                    document.getElementById('results').classList.remove('hidden');
                    displayResults(data);
                })
                .catch(error => {
                    document.getElementById('loading').classList.add('hidden');
                    console.error('Detection failed:', error);
                    alert('Detection failed. Check console for details.');
                });
        }
        
        function displayResults(data) {
            // Summary
            const summaryDiv = document.getElementById('summary');
            const riskClass = data.summary.risk_level === 'High' ? 'risk-high' : 
                             data.summary.risk_level === 'Medium' ? 'risk-medium' : 'risk-low';
            summaryDiv.innerHTML = `
                <div class="summary-box ${riskClass}">
                    <h3>Risk Level: ${data.summary.risk_level}</h3>
                    <p><strong>Browser:</strong> ${data.summary.browser}</p>
                    <p><strong>Total Indicators:</strong> ${data.summary.total_indicators}</p>
                    <p><strong>Likely Interference:</strong> ${data.summary.likely_interference ? 'Yes' : 'No'}</p>
                </div>
            `;
            
            // Browser Info
            const browserDiv = document.getElementById('browserInfo');
            browserDiv.innerHTML = `
                <h3>Browser Information</h3>
                <div class="code">Browser: ${data.browser_info.name} ${data.browser_info.version}
User Agent: ${data.user_agent}</div>
            `;
            
            // Indicators
            const indicatorsDiv = document.getElementById('indicators');
            let indicatorsHTML = '<h3>Detected Indicators</h3>';
            
            const allIndicators = [
                ...data.addon_indicators,
                ...data.security_software,
                ...data.network_modifications
            ];
            
            if (allIndicators.length === 0) {
                indicatorsHTML += '<p class="success">✅ No interfering add-ons or software detected!</p>';
            } else {
                allIndicators.forEach(indicator => {
                    const confidenceClass = indicator.confidence === 'High' ? 'high-confidence' : 
                                           indicator.confidence === 'Medium' ? 'medium-confidence' : 'low-confidence';
                    indicatorsHTML += `
                        <div class="indicator ${confidenceClass}">
                            <strong>${indicator.type}</strong> (${indicator.confidence} confidence)<br>
                            ${indicator.indicator}: ${indicator.value || indicator.name || 'Detected'}
                        </div>
                    `;
                });
            }
            
            indicatorsDiv.innerHTML = indicatorsHTML;
            
            // Recommendations
            const recommendationsDiv = document.getElementById('recommendations');
            let recommendationsHTML = '<h3>Recommendations</h3>';
            
            data.recommendations.forEach(rec => {
                recommendationsHTML += `
                    <div class="warning">
                        <strong>${rec.type}:</strong> ${rec.message}<br>
                        <strong>Action:</strong> ${rec.action}
                    </div>
                `;
            });
            
            recommendationsDiv.innerHTML = recommendationsHTML;
        }
        
        function runQuickTest() {
            const results = {
                basicJS: testBasicJS(),
                domManipulation: testDOMManipulation(),
                ajax: testAJAX(),
                console: testConsole()
            };
            
            console.log('Quick test results:', results);
            alert('Quick test completed. Check console for results.');
        }
        
        function testBasicJS() {
            try {
                const result = 2 + 2;
                const message = result === 4 ? '✅ Basic JavaScript works' : '❌ Basic JavaScript failed';
                document.getElementById('basicJSResult').innerHTML = `<div class="success">${message}</div>`;
                return true;
            } catch (error) {
                document.getElementById('basicJSResult').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return false;
            }
        }
        
        function testDOMManipulation() {
            try {
                const testDiv = document.createElement('div');
                testDiv.innerHTML = 'Test element';
                document.body.appendChild(testDiv);
                document.body.removeChild(testDiv);
                document.getElementById('domResult').innerHTML = '<div class="success">✅ DOM manipulation works</div>';
                return true;
            } catch (error) {
                document.getElementById('domResult').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return false;
            }
        }
        
        function testAJAX() {
            try {
                const xhr = new XMLHttpRequest();
                xhr.open('GET', '/fit/public/detect-firefox-addons.php', true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            document.getElementById('ajaxResult').innerHTML = '<div class="success">✅ AJAX requests work</div>';
                        } else {
                            document.getElementById('ajaxResult').innerHTML = `<div class="error">❌ AJAX failed: ${xhr.status}</div>`;
                        }
                    }
                };
                xhr.send();
                return true;
            } catch (error) {
                document.getElementById('ajaxResult').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return false;
            }
        }
        
        function testConsole() {
            try {
                console.log('Console test message');
                console.error('Console error test');
                console.warn('Console warning test');
                document.getElementById('consoleResult').innerHTML = '<div class="success">✅ Console access works</div>';
                return true;
            } catch (error) {
                document.getElementById('consoleResult').innerHTML = `<div class="error">❌ Error: ${error.message}</div>`;
                return false;
            }
        }
        
        function clearResults() {
            document.getElementById('results').classList.add('hidden');
            document.getElementById('basicJSResult').innerHTML = '';
            document.getElementById('domResult').innerHTML = '';
            document.getElementById('ajaxResult').innerHTML = '';
            document.getElementById('consoleResult').innerHTML = '';
        }
    </script>
</body>
</html>