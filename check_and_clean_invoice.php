<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceNumber = 'FAC-RET25-2025-0198';
    
    echo "=== Checking Invoice Status ===\n\n";
    
    // Check if invoice exists
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
    $stmt->execute([$invoiceNumber]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "❌ Invoice '$invoiceNumber' STILL EXISTS in database!\n";
        echo "- ID: " . $invoice['id'] . "\n";
        echo "- Status: " . $invoice['status'] . "\n";
        echo "- Total: €" . number_format($invoice['total'], 2) . "\n";
        echo "\nForce deleting now...\n\n";
        
        $invoiceId = $invoice['id'];
        
        // Force delete everything
        $pdo->beginTransaction();
        
        try {
            // Delete payment allocations
            $stmt = $pdo->prepare("DELETE FROM payment_allocations WHERE invoice_id = ?");
            $stmt->execute([$invoiceId]);
            echo "- Cleaned payment allocations\n";
            
            // Delete invoice items
            $stmt = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
            $stmt->execute([$invoiceId]);
            echo "- Cleaned invoice items\n";
            
            // Delete invoice lines
            $stmt = $pdo->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
            $stmt->execute([$invoiceId]);
            echo "- Cleaned invoice lines\n";
            
            // Delete from any other related tables
            $relatedTables = [
                'invoice_payments' => 'invoice_id',
                'invoice_documents' => 'invoice_id',
                'invoice_history' => 'invoice_id'
            ];
            
            foreach ($relatedTables as $table => $column) {
                try {
                    $stmt = $pdo->prepare("DELETE FROM $table WHERE $column = ?");
                    $stmt->execute([$invoiceId]);
                    if ($stmt->rowCount() > 0) {
                        echo "- Cleaned $table\n";
                    }
                } catch (Exception $e) {
                    // Table might not exist
                }
            }
            
            // Finally delete the invoice
            $stmt = $pdo->prepare("DELETE FROM invoices WHERE id = ?");
            $stmt->execute([$invoiceId]);
            echo "- Deleted invoice record\n";
            
            $pdo->commit();
            echo "\n✅ Invoice has been completely removed!\n";
            
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "\n❌ Error during deletion: " . $e->getMessage() . "\n";
        }
        
    } else {
        echo "✅ Invoice '$invoiceNumber' is NOT in the database.\n";
        echo "\nIf you still see it in the list, try:\n";
        echo "1. Clear your browser cache (Ctrl+F5)\n";
        echo "2. Log out and log back in\n";
        echo "3. Check if there's a caching system that needs to be cleared\n";
    }
    
    // Check for cache files
    echo "\n=== Checking for cache files ===\n";
    
    $cacheDirectories = [
        __DIR__ . '/storage/cache',
        __DIR__ . '/storage/cache/invoices',
        __DIR__ . '/storage/cache/twig',
        __DIR__ . '/cache',
        __DIR__ . '/tmp/cache'
    ];
    
    foreach ($cacheDirectories as $dir) {
        if (is_dir($dir)) {
            echo "Found cache directory: $dir\n";
            
            // Look for invoice-related cache files
            $files = glob($dir . '/*invoice*');
            if (!empty($files)) {
                echo "  - Found " . count($files) . " invoice-related cache files\n";
                
                // Optional: delete them
                foreach ($files as $file) {
                    if (is_file($file) && unlink($file)) {
                        echo "  - Deleted: " . basename($file) . "\n";
                    }
                }
            }
        }
    }
    
    // Clear any session-based cache
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    if (isset($_SESSION['invoice_cache'])) {
        unset($_SESSION['invoice_cache']);
        echo "\n✅ Cleared session invoice cache\n";
    }
    
    // Check if there's a specific invoice cache table
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE '%cache%'");
        $cacheTables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!empty($cacheTables)) {
            echo "\n=== Cache tables found ===\n";
            foreach ($cacheTables as $table) {
                echo "- $table\n";
                
                // Try to clear invoice-related entries
                try {
                    $stmt = $pdo->prepare("DELETE FROM $table WHERE cache_key LIKE '%invoice%' OR cache_key LIKE '%$invoiceNumber%'");
                    $stmt->execute();
                    if ($stmt->rowCount() > 0) {
                        echo "  Cleared " . $stmt->rowCount() . " cache entries\n";
                    }
                } catch (Exception $e) {
                    // Might not have the expected structure
                }
            }
        }
    } catch (Exception $e) {
        // No cache tables
    }
    
    echo "\n=== Final Check ===\n";
    
    // Double-check the invoice is gone
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM invoices WHERE invoice_number = ?");
    $stmt->execute([$invoiceNumber]);
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        echo "✅ Invoice '$invoiceNumber' is definitely NOT in the database.\n";
        echo "\n📌 Next steps:\n";
        echo "1. Go to: http://localhost/fit/public/invoices\n";
        echo "2. Press Ctrl+F5 to hard refresh the page\n";
        echo "3. If still visible, log out and log back in\n";
    } else {
        echo "❌ Invoice still exists! There might be a database trigger preventing deletion.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}