# PermissionService Documentation

## Overview
The PermissionService provides a centralized, cached permission checking system for the application with super admin bypass functionality.

## Features
- Singleton pattern for consistent instance usage
- Redis caching with file cache fallback
- Super admin bypass (user ID 1)
- Multiple permission checking methods
- Cache management
- Helper functions for easy access

## Usage

### Basic Permission Checking

```php
// Using helper functions (recommended)
if (hasPermission('users.create')) {
    // User can create users
}

// Check multiple permissions
if (hasAnyPermission(['users.create', 'users.update'])) {
    // User has at least one permission
}

if (hasAllPermissions(['users.create', 'users.update', 'users.delete'])) {
    // User has all permissions
}
```

### In Controllers

```php
class UserController extends Controller
{
    public function create()
    {
        // Method 1: Using controller method
        if (!$this->checkPermission('users.create')) {
            $this->flash('error', 'Permission denied');
            return $this->redirect('/dashboard');
        }
        
        // Method 2: Using exception-based check
        $this->requirePermission('users.create');
        
        // Method 3: Check module action
        if (!$this->canPerformAction('users', 'create')) {
            // Handle permission denied
        }
    }
}
```

### Route-Level Permission Checking

```php
// Basic permission check
Flight::route('/users/create', 
    PermissionMiddleware::require('users.create'), 
    [$userController, 'create']
);

// Check any permission
Flight::route('/users/manage', 
    PermissionMiddleware::requireAny(['users.create', 'users.update']), 
    [$userController, 'manage']
);

// API endpoints
Flight::route('POST /api/users', 
    PermissionMiddleware::requireApi('users.create'), 
    [$apiController, 'createUser']
);

// Super admin only
Flight::route('/system/config', 
    PermissionMiddleware::superAdminOnly(), 
    [$configController, 'system']
);
```

### Helper Functions

```php
// Check permissions
hasPermission('users.create');
hasAnyPermission(['users.create', 'users.update']);
hasAllPermissions(['users.create', 'users.update']);

// Get user permissions
$permissions = getUserPermissions();
$permissionsByModule = getUserPermissionsByModule();

// Check super admin
if (isSuperAdmin()) {
    // Has all permissions
}

// Require permissions (throws exception)
requirePermission('users.delete');
requireAnyPermission(['users.delete', 'users.archive']);
requireAllPermissions(['users.delete', 'users.purge']);

// Clear cache
clearPermissionCache(); // Clear all
clearPermissionCache(123); // Clear for user 123

// Check multiple permissions at once
$permissions = checkPermissions(['users.create', 'users.update', 'users.delete']);
// Returns: ['users.create' => true, 'users.update' => false, 'users.delete' => true]
```

### In Twig Templates

```twig
{# Check single permission #}
{% if hasPermission('users.create') %}
    <a href="/users/create" class="btn btn-primary">Create User</a>
{% endif %}

{# Check any permission #}
{% if hasAnyPermission(['users.create', 'users.update']) %}
    <div class="admin-panel">...</div>
{% endif %}

{# Check super admin #}
{% if isSuperAdmin() %}
    <div class="super-admin-tools">...</div>
{% endif %}
```

## Permission Format

Permissions follow the format: `module.action`

Examples:
- `users.create`
- `users.update`
- `users.delete`
- `users.view`
- `invoices.create`
- `invoices.approve`
- `settings.manage`

## Caching

The service automatically caches permissions for 1 hour:
- Uses Redis if available
- Falls back to file-based cache
- Cache is automatically cleared when permissions are updated
- Manual cache clearing available via helper function

## Super Admin

User ID 1 is always considered a super admin and:
- Bypasses all permission checks
- Has access to all features
- Cannot be restricted

## Integration Points

1. **BaseController** - All controllers inherit permission methods
2. **Middleware** - Route-level permission enforcement
3. **Helper Functions** - Global functions available everywhere
4. **Twig Templates** - Permission checks in views
5. **API Endpoints** - JSON responses for unauthorized access

## Best Practices

1. Use descriptive permission codes: `module.action`
2. Cache permissions appropriately
3. Clear cache when permissions change
4. Use middleware for route-level checks
5. Use helper functions for consistency
6. Always handle permission denied gracefully
7. Log permission denials for security auditing