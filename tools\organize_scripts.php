<?php
/**
 * <PERSON><PERSON><PERSON> to organize and move maintenance scripts
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Define script categories and their destinations
$organizations = [
    'maintenance/invoice' => [
        'check_invoice_sequences.php',
        'check_invoice_structure.php',
        'check_invoice_lines.php',
        'check_duplicate_invoice_lines.php',
        'fix_duplicate_invoice_lines.php',
        'clear_invoice_cache.php',
        'find_recent_invoices.php',
        'check_sequence_and_invoices.php',
        'setup_invoice_sequence.php',
        'check_invoice_types_prefixes.php',
        'update_invoice_type_prefixes.php',
    ],
    
    'maintenance/diagnostics' => [
        'check_column_types.php',
        'check_config_table.php',
        'check_invoice_table_structure.php',
        'check_document_types.php',
        'check_payment_terms_data.php',
        'debug_payment_terms.php',
    ],
    
    'maintenance/user-management' => [
        'add_users_to_coach_group.php',
        'add_coaches_to_group.php',
        'create_coach_users.php',
        'capitalize_usernames.php',
        'check_user_addresses.php',
        'add_address_to_all_users.php',
    ],
];

// Scripts to delete (one-time fixes)
$to_delete = [
    'fix_to_0186.php',
    'fix_invoice_number_186.php',
    'fix_invoice_number_0186.php',
    'fix_sequence_to_186.php',
    'reset_sequence_185.php',
    'fix_invoice_187_duplicates.php',
    'fix_invoice_187_and_sequence.php',
    'force_reset_sequence.php',
    'reset_sequence_to_185.php',
    'fix_invoice_sequence_186.php',
    'force_sequence_reset.php',
    'fix_invoice_246_lines.php',
    'fix_invoice_loy_prefix.php',
    'reset_invoice_sequence.php',
    'update_invoice_sequence.php',
    
    // Debug scripts for specific invoices
    'debug_invoice_187.php',
    'debug_invoice_241.php',
    'debug_invoice_246_pdf.php',
    'diagnose_invoice_187.php',
    'diagnose_invoice_246.php',
    
    // Test scripts
    'test_invoice_create.php',
    'test_invoice_creation.php',
    'test_invoice_post.php',
    'test_create_invoice.php',
    'test_invoice_187_pdf.php',
    'test_invoice_pdf_fix.php',
    'test_new_invoice_numbering.php',
    'test_next_invoice_number.php',
    'test_pdf.php',
    'test_pdf_simple.php',
    'test_pdf_data.php',
    'test_tcpdf_simple.php',
    'test_integrated_pdf.php',
    'test_invoice_web.php',
    'test-pdf-246.php',
    
    // Migration scripts (already run)
    'run_migration_072.php',
    'run_migration_072_simple.php',
    'run_migration_env.php',
    'run_migration_fixed.php',
    'run_migration_manual.php',
    'run_prefix_migration.php',
    
    // Other test/debug files
    'debug_invoice_form.php',
    'debug_invoice_pdf.php',
    'debug_invoice_save.html',
    'debug_pdf_columns.php',
    'debug_pdf_output.php',
    'invoice-creation-fix-summary.php',
    'invoice-pdf-debug.php',
    'invoice-pdf-debug-table.php',
    'footer-test.php',
    'payment_terms_test.html',
    'test-client-dropdown.html',
    'test-column-rename.html',
    'test-dynamic-columns.html',
    'test-invoice-items-columns.html',
    'test-render-columns.html',
    'test-render-columns-debug.html',
    'simple_check.php',
    
    // Specific fixes already applied
    'fix_payment_terms_data.php',
    'fix_payment_terms_days.php',
    'fix_payment_terms_translation.php',
    'fix_draft_invoice_visibility.php',
    'fix_document_names.php',
    'fix_invoice_totals.php',
    'clear_payment_terms_cache.php',
    'update_invoice_format.php',
    'check_invoice_submission.php',
    'check_invoice_format.php',
    'check_invoice_items_table.php',
    'check_invoice_lines_structure.php',
    'check_payment_terms_translation.php',
    'check_pdf_columns.php',
    'check_user_address_fields.php',
    'check_sequence_issue.php',
    'check_all_invoices.php',
    'check_draft_invoices.php',
    'check_duplicate_lines.php',
    'check_invoice.php',
    'check_invoice_187.php',
    'check_invoice_238_details.php',
    'check_invoice_db.php',
    'check_invoices.php',
    'test_and_fix_translations.php',
    'test_translation.php',
    'add_invoice_items.php',
];

// Scripts to keep in public (core functionality)
$keep_in_public = [
    'index.php',
    'install.php',
    'invoice-pdf.php',
    '.htaccess',
];

echo "Organizing Scripts...\n\n";

// Count files
$publicDir = __DIR__ . '/../public/';
$moveCount = 0;
$deleteCount = 0;
$keepCount = 0;

// First, show what will be done
echo "=== SCRIPTS TO MOVE ===\n";
foreach ($organizations as $dest => $files) {
    echo "\nTo /tools/$dest/:\n";
    foreach ($files as $file) {
        if (file_exists($publicDir . $file)) {
            echo "  - $file\n";
            $moveCount++;
        }
    }
}

echo "\n=== SCRIPTS TO DELETE ===\n";
foreach ($to_delete as $file) {
    if (file_exists($publicDir . $file)) {
        echo "  - $file\n";
        $deleteCount++;
    }
}

echo "\n=== SCRIPTS TO KEEP IN PUBLIC ===\n";
foreach ($keep_in_public as $file) {
    if (file_exists($publicDir . $file)) {
        echo "  - $file\n";
        $keepCount++;
    }
}

// Also backup files
$backupFiles = glob($publicDir . '*.backup-*');
if (!empty($backupFiles)) {
    echo "\n=== BACKUP FILES TO DELETE ===\n";
    foreach ($backupFiles as $file) {
        echo "  - " . basename($file) . "\n";
        $deleteCount++;
    }
}

// Log files
$logFiles = glob($publicDir . '*.log');
if (!empty($logFiles)) {
    echo "\n=== LOG FILES TO DELETE ===\n";
    foreach ($logFiles as $file) {
        echo "  - " . basename($file) . "\n";
        $deleteCount++;
    }
}

echo "\n\nSummary:\n";
echo "- Scripts to move: $moveCount\n";
echo "- Scripts to delete: $deleteCount\n";
echo "- Scripts to keep: $keepCount\n";

echo "\nThis is a DRY RUN. To execute, run with --execute parameter.\n";