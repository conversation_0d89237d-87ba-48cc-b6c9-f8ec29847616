<?php

namespace Database\Seeders;

use PDO;
use Exception;

class PermissionSeeder
{
    private PDO $db;
    
    public function __construct(PDO $db)
    {
        $this->db = $db;
    }
    
    /**
     * Run the permission seeder
     */
    public function run(): void
    {
        echo "Seeding permissions...\n\n";
        
        try {
            $this->db->beginTransaction();
            
            // First, ensure the tables exist
            $this->createTablesIfNotExist();
            
            // Create system modules
            $this->createSystemModules();
            
            // Create default groups if they don't exist
            $this->createDefaultGroups();
            
            // Assign permissions to groups
            $this->assignPermissionsToGroups();
            
            // Create permission templates
            $this->createPermissionTemplates();
            
            $this->db->commit();
            echo "\n✅ Permissions seeded successfully!\n";
            
        } catch (Exception $e) {
            $this->db->rollBack();
            echo "\n❌ Error seeding permissions: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * Ensure required tables exist
     */
    private function createTablesIfNotExist(): void
    {
        echo "Checking database tables...\n";
        
        // Check if system_modules table exists
        $stmt = $this->db->query("SHOW TABLES LIKE 'system_modules'");
        if ($stmt->rowCount() == 0) {
            throw new Exception("Table 'system_modules' does not exist. Please run the permission management migration first.");
        }
        
        // Check other required tables
        $requiredTables = ['module_permissions', 'group_permissions', 'permission_templates'];
        foreach ($requiredTables as $table) {
            $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() == 0) {
                throw new Exception("Table '$table' does not exist. Please run the permission management migration first.");
            }
        }
        
        echo "✓ All required tables exist\n\n";
    }
    
    /**
     * Create system modules with their permissions
     */
    private function createSystemModules(): void
    {
        echo "Creating system modules...\n";
        
        $modules = [
            [
                'code' => 'users',
                'name' => 'User Management',
                'description' => 'Manage system users and groups',
                'icon' => 'fa-users',
                'route_prefix' => '/users',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Users', 'description' => 'Can view user list and details'],
                    ['code' => 'create', 'name' => 'Create Users', 'description' => 'Can create new users'],
                    ['code' => 'edit', 'name' => 'Edit Users', 'description' => 'Can edit user information'],
                    ['code' => 'delete', 'name' => 'Delete Users', 'description' => 'Can delete users'],
                    ['code' => 'reset_password', 'name' => 'Reset Passwords', 'description' => 'Can reset user passwords'],
                    ['code' => 'manage_groups', 'name' => 'Manage Groups', 'description' => 'Can manage user groups']
                ]
            ],
            [
                'code' => 'invoices',
                'name' => 'Invoices',
                'description' => 'Invoice management system',
                'icon' => 'fa-file-invoice',
                'route_prefix' => '/invoices',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Invoices', 'description' => 'Can view invoices'],
                    ['code' => 'create', 'name' => 'Create Invoices', 'description' => 'Can create new invoices'],
                    ['code' => 'edit', 'name' => 'Edit Invoices', 'description' => 'Can edit invoices'],
                    ['code' => 'delete', 'name' => 'Delete Invoices', 'description' => 'Can delete invoices'],
                    ['code' => 'send', 'name' => 'Send Invoices', 'description' => 'Can send invoices via email'],
                    ['code' => 'export', 'name' => 'Export Invoices', 'description' => 'Can export invoices'],
                    ['code' => 'void', 'name' => 'Void Invoices', 'description' => 'Can void invoices']
                ]
            ],
            [
                'code' => 'clients',
                'name' => 'Clients',
                'description' => 'Client management',
                'icon' => 'fa-users',
                'route_prefix' => '/clients',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Clients', 'description' => 'Can view client list'],
                    ['code' => 'create', 'name' => 'Create Clients', 'description' => 'Can create new clients'],
                    ['code' => 'edit', 'name' => 'Edit Clients', 'description' => 'Can edit client information'],
                    ['code' => 'delete', 'name' => 'Delete Clients', 'description' => 'Can delete clients'],
                    ['code' => 'export', 'name' => 'Export Clients', 'description' => 'Can export client data']
                ]
            ],
            [
                'code' => 'products',
                'name' => 'Products & Services',
                'description' => 'Product and service management',
                'icon' => 'fa-box',
                'route_prefix' => '/products',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Products', 'description' => 'Can view products'],
                    ['code' => 'create', 'name' => 'Create Products', 'description' => 'Can create new products'],
                    ['code' => 'edit', 'name' => 'Edit Products', 'description' => 'Can edit products'],
                    ['code' => 'delete', 'name' => 'Delete Products', 'description' => 'Can delete products'],
                    ['code' => 'manage_stock', 'name' => 'Manage Stock', 'description' => 'Can manage product stock'],
                    ['code' => 'import', 'name' => 'Import Products', 'description' => 'Can import products']
                ]
            ],
            [
                'code' => 'reports',
                'name' => 'Reports',
                'description' => 'System reports and analytics',
                'icon' => 'fa-chart-bar',
                'route_prefix' => '/reports',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Reports', 'description' => 'Can view reports'],
                    ['code' => 'view_financial', 'name' => 'View Financial Reports', 'description' => 'Can view financial reports'],
                    ['code' => 'view_inventory', 'name' => 'View Inventory Reports', 'description' => 'Can view inventory reports'],
                    ['code' => 'export', 'name' => 'Export Reports', 'description' => 'Can export reports']
                ]
            ],
            [
                'code' => 'config',
                'name' => 'Configuration',
                'description' => 'System configuration',
                'icon' => 'fa-cog',
                'route_prefix' => '/config',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Configuration', 'description' => 'Can view system configuration'],
                    ['code' => 'edit', 'name' => 'Edit Configuration', 'description' => 'Can edit system configuration'],
                    ['code' => 'manage_company', 'name' => 'Manage Company', 'description' => 'Can manage company settings'],
                    ['code' => 'manage_system', 'name' => 'Manage System', 'description' => 'Can manage system settings'],
                    ['code' => 'manage_templates', 'name' => 'Manage Templates', 'description' => 'Can manage email templates']
                ]
            ],
            [
                'code' => 'permissions',
                'name' => 'Permissions',
                'description' => 'Permission management',
                'icon' => 'fa-shield-alt',
                'route_prefix' => '/admin/permissions',
                'permissions' => [
                    ['code' => 'view', 'name' => 'View Permissions', 'description' => 'Can view permissions'],
                    ['code' => 'manage', 'name' => 'Manage Permissions', 'description' => 'Can manage permissions']
                ]
            ]
        ];
        
        // Insert modules and their permissions
        $moduleStmt = $this->db->prepare("
            INSERT INTO system_modules (code, name, description, icon, route_prefix, sort_order, is_active)
            VALUES (:code, :name, :description, :icon, :route_prefix, :sort_order, 1)
            ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                description = VALUES(description),
                icon = VALUES(icon),
                route_prefix = VALUES(route_prefix)
        ");
        
        $permStmt = $this->db->prepare("
            INSERT INTO module_permissions (module_id, permission_code, permission_name, description, is_active)
            VALUES (:module_id, :permission_code, :permission_name, :description, 1)
            ON DUPLICATE KEY UPDATE
                permission_name = VALUES(permission_name),
                description = VALUES(description)
        ");
        
        $sortOrder = 1;
        foreach ($modules as $module) {
            // Insert module
            $moduleStmt->execute([
                'code' => $module['code'],
                'name' => $module['name'],
                'description' => $module['description'],
                'icon' => $module['icon'],
                'route_prefix' => $module['route_prefix'],
                'sort_order' => $sortOrder++
            ]);
            
            echo "✓ Created/Updated module: {$module['name']}\n";
            
            // Get module ID
            $moduleId = $this->db->lastInsertId();
            if ($moduleId == 0) {
                // Module already existed, fetch its ID
                $stmt = $this->db->prepare("SELECT id FROM system_modules WHERE code = :code");
                $stmt->execute(['code' => $module['code']]);
                $moduleId = $stmt->fetchColumn();
            }
            
            // Insert permissions for this module
            foreach ($module['permissions'] as $perm) {
                $permStmt->execute([
                    'module_id' => $moduleId,
                    'permission_code' => $perm['code'],
                    'permission_name' => $perm['name'],
                    'description' => $perm['description']
                ]);
            }
        }
    }
    
    /**
     * Create default user groups
     */
    private function createDefaultGroups(): void
    {
        echo "\nCreating default user groups...\n";
        
        $groups = [
            [
                'name' => 'Administrators',
                'description' => 'Full system access',
                'color' => '#dc3545',
                'icon' => 'fas fa-crown',
                'is_system' => 1
            ],
            [
                'name' => 'Managers',
                'description' => 'Access to most features except system configuration',
                'color' => '#007bff',
                'icon' => 'fas fa-user-tie',
                'is_system' => 1
            ],
            [
                'name' => 'Staff',
                'description' => 'Basic access to operational features',
                'color' => '#28a745',
                'icon' => 'fas fa-users',
                'is_system' => 1
            ],
            [
                'name' => 'Practitioners',
                'description' => 'Access to patient management and their own invoicing',
                'color' => '#17a2b8',
                'icon' => 'fas fa-user-md',
                'is_system' => 0
            ],
        ];
        
        // Check if user_groups table has all required columns
        $stmt = $this->db->query("SHOW COLUMNS FROM user_groups");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }
        
        // Build insert query based on available columns
        $fields = ['name', 'description'];
        $values = [':name', ':description'];
        
        if (in_array('color', $columns)) {
            $fields[] = 'color';
            $values[] = ':color';
        }
        if (in_array('icon', $columns)) {
            $fields[] = 'icon';
            $values[] = ':icon';
        }
        if (in_array('is_system', $columns)) {
            $fields[] = 'is_system';
            $values[] = ':is_system';
        }
        if (in_array('is_active', $columns)) {
            $fields[] = 'is_active';
            $values[] = '1';
        }
        if (in_array('created_at', $columns)) {
            $fields[] = 'created_at';
            $values[] = 'NOW()';
        }
        
        $sql = "INSERT INTO user_groups (" . implode(', ', $fields) . ") 
                VALUES (" . implode(', ', $values) . ")
                ON DUPLICATE KEY UPDATE description = VALUES(description)";
        
        if (in_array('color', $columns)) {
            $sql .= ", color = VALUES(color)";
        }
        if (in_array('icon', $columns)) {
            $sql .= ", icon = VALUES(icon)";
        }
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($groups as $group) {
            $params = [
                'name' => $group['name'],
                'description' => $group['description']
            ];
            
            if (in_array('color', $columns)) {
                $params['color'] = $group['color'];
            }
            if (in_array('icon', $columns)) {
                $params['icon'] = $group['icon'];
            }
            if (in_array('is_system', $columns)) {
                $params['is_system'] = $group['is_system'];
            }
            
            $stmt->execute($params);
            echo "✓ Created/Updated group: {$group['name']}\n";
        }
    }
    
    /**
     * Assign permissions to default groups
     */
    private function assignPermissionsToGroups(): void
    {
        echo "\nAssigning permissions to groups...\n";
        
        // Get group IDs
        $groups = [];
        $stmt = $this->db->query("SELECT id, name FROM user_groups WHERE name IN ('Administrators', 'Managers', 'Staff', 'Practitioners')");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $groups[$row['name']] = $row['id'];
        }
        
        // Define permission assignments
        $assignments = [
            'Administrators' => 'ALL', // All permissions
            'Managers' => [
                'users.view', 'users.create', 'users.edit', 'users.reset_password',
                'invoices.*',
                'clients.*',
                'products.*',
                'reports.*'
            ],
            'Staff' => [
                'users.view',
                'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.send',
                'clients.view', 'clients.create', 'clients.edit',
                'products.view',
                'reports.view'
            ],
            'Practitioners' => [
                'clients.view',
                'invoices.view', 'invoices.create',
                'products.view'
            ]
        ];
        
        // Clear existing group permissions
        if (!empty($groups)) {
            $stmt = $this->db->prepare("DELETE FROM group_permissions WHERE group_id IN (" . implode(',', array_values($groups)) . ")");
            $stmt->execute();
        }
        
        // Insert new permissions
        $insertStmt = $this->db->prepare("
            INSERT INTO group_permissions (group_id, module_id, permission_code, is_granted, granted_by)
            VALUES (:group_id, :module_id, :permission_code, 1, 1)
        ");
        
        foreach ($assignments as $groupName => $permissions) {
            if (!isset($groups[$groupName])) continue;
            
            $groupId = $groups[$groupName];
            
            if ($permissions === 'ALL') {
                // Assign all permissions
                $stmt = $this->db->query("
                    SELECT sm.id as module_id, mp.permission_code 
                    FROM system_modules sm
                    JOIN module_permissions mp ON mp.module_id = sm.id
                    WHERE sm.is_active = 1 AND mp.is_active = 1
                ");
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    $insertStmt->execute([
                        'group_id' => $groupId,
                        'module_id' => $row['module_id'],
                        'permission_code' => $row['permission_code']
                    ]);
                }
                echo "✓ Assigned all permissions to: {$groupName}\n";
            } else {
                // Assign specific permissions
                foreach ($permissions as $permissionPattern) {
                    if (strpos($permissionPattern, '*') !== false) {
                        // Handle wildcards (e.g., invoices.*)
                        list($moduleCode, $permCode) = explode('.', $permissionPattern);
                        
                        if ($permCode === '*') {
                            // All permissions for this module
                            $stmt = $this->db->prepare("
                                SELECT sm.id as module_id, mp.permission_code 
                                FROM system_modules sm
                                JOIN module_permissions mp ON mp.module_id = sm.id
                                WHERE sm.code = :module_code AND sm.is_active = 1 AND mp.is_active = 1
                            ");
                            $stmt->execute(['module_code' => $moduleCode]);
                        }
                    } else {
                        // Exact match (e.g., users.view)
                        list($moduleCode, $permCode) = explode('.', $permissionPattern);
                        $stmt = $this->db->prepare("
                            SELECT sm.id as module_id, mp.permission_code 
                            FROM system_modules sm
                            JOIN module_permissions mp ON mp.module_id = sm.id
                            WHERE sm.code = :module_code 
                                AND mp.permission_code = :perm_code 
                                AND sm.is_active = 1 
                                AND mp.is_active = 1
                        ");
                        $stmt->execute([
                            'module_code' => $moduleCode,
                            'perm_code' => $permCode
                        ]);
                    }
                    
                    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                        $insertStmt->execute([
                            'group_id' => $groupId,
                            'module_id' => $row['module_id'],
                            'permission_code' => $row['permission_code']
                        ]);
                    }
                }
                echo "✓ Assigned permissions to: {$groupName}\n";
            }
        }
    }
    
    /**
     * Create permission templates
     */
    private function createPermissionTemplates(): void
    {
        echo "\nCreating permission templates...\n";
        
        $templates = [
            [
                'name' => 'Administrator Template',
                'description' => 'Full access to all system features',
                'code' => 'admin',
                'is_system' => 1,
                'permissions' => 'ALL'
            ],
            [
                'name' => 'Manager Template',
                'description' => 'Access to operational features without system configuration',
                'code' => 'manager',
                'is_system' => 1,
                'permissions' => [
                    'users.view', 'users.create', 'users.edit',
                    'invoices.*', 'clients.*', 'products.*', 'reports.*'
                ]
            ],
            [
                'name' => 'Staff Template',
                'description' => 'Basic operational access',
                'code' => 'staff',
                'is_system' => 1,
                'permissions' => [
                    'users.view',
                    'invoices.view', 'invoices.create', 'invoices.edit',
                    'clients.view', 'clients.create', 'clients.edit',
                    'products.view', 'reports.view'
                ]
            ],
            [
                'name' => 'Read-Only Template',
                'description' => 'View-only access to all features',
                'code' => 'readonly',
                'is_system' => 1,
                'permissions' => [
                    'users.view', 'invoices.view', 'clients.view',
                    'products.view', 'reports.view', 'config.view'
                ]
            ]
        ];
        
        $templateStmt = $this->db->prepare("
            INSERT INTO permission_templates (name, description, code, is_system, permissions_json, created_by)
            VALUES (:name, :description, :code, :is_system, :permissions_json, 1)
            ON DUPLICATE KEY UPDATE
                description = VALUES(description),
                permissions_json = VALUES(permissions_json)
        ");
        
        foreach ($templates as $template) {
            // Convert permissions to JSON format
            $permissionsJson = [];
            
            if ($template['permissions'] === 'ALL') {
                // Get all permissions
                $stmt = $this->db->query("
                    SELECT sm.code as module_code, mp.permission_code 
                    FROM system_modules sm
                    JOIN module_permissions mp ON mp.module_id = sm.id
                    WHERE sm.is_active = 1 AND mp.is_active = 1
                ");
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    if (!isset($permissionsJson[$row['module_code']])) {
                        $permissionsJson[$row['module_code']] = [];
                    }
                    $permissionsJson[$row['module_code']][] = $row['permission_code'];
                }
            } else {
                // Parse specific permissions
                foreach ($template['permissions'] as $perm) {
                    if (strpos($perm, '.') !== false) {
                        list($module, $permission) = explode('.', $perm);
                        if (!isset($permissionsJson[$module])) {
                            $permissionsJson[$module] = [];
                        }
                        if ($permission === '*') {
                            // Get all permissions for this module
                            $stmt = $this->db->prepare("
                                SELECT mp.permission_code 
                                FROM system_modules sm
                                JOIN module_permissions mp ON mp.module_id = sm.id
                                WHERE sm.code = :module_code AND sm.is_active = 1 AND mp.is_active = 1
                            ");
                            $stmt->execute(['module_code' => $module]);
                            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                                $permissionsJson[$module][] = $row['permission_code'];
                            }
                        } else {
                            $permissionsJson[$module][] = $permission;
                        }
                    }
                }
            }
            
            $templateStmt->execute([
                'name' => $template['name'],
                'description' => $template['description'],
                'code' => $template['code'],
                'is_system' => $template['is_system'],
                'permissions_json' => json_encode($permissionsJson)
            ]);
            
            echo "✓ Created/Updated template: {$template['name']}\n";
        }
    }
}

// If run directly from command line
if (php_sapi_name() === 'cli' && basename($argv[0]) === basename(__FILE__)) {
    try {
        // Load database configuration
        require_once __DIR__ . '/../../vendor/autoload.php';
        require_once __DIR__ . '/../../app/config/bootstrap.php';
        
        $seeder = new PermissionSeeder(Flight::db());
        $seeder->run();
    } catch (Exception $e) {
        echo "\n❌ Fatal error: " . $e->getMessage() . "\n";
        exit(1);
    }
}