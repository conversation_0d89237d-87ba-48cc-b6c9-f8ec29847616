<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\EmailTemplate;
use App\Models\InvoiceType;
use App\Models\Setting;
use App\Services\CacheService;
use App\Services\EmailService;
use App\Services\EnhancedEmailService;

class EmailAutomationController extends Controller
{
    private $cache;
    private $emailService;
    
    public function __construct()
    {
        $this->cache = new CacheService();
        $this->emailService = new EnhancedEmailService();
    }
    
    /**
     * Display email automation settings page
     */
    public function index()
    {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/login');
            return;
        }
        
        // Check permission
        $permissionService = \App\Services\PermissionService::getInstance();
        if (!$permissionService->hasPermission('config_view')) {
            $this->flash('error', 'You do not have permission to access this page.');
            $this->redirect('/');
            return;
        }
        
        // Get invoice types
        $invoiceTypes = InvoiceType::getActive();
        
        // Get email templates grouped by type
        $db = \Flight::db();
        $stmt = $db->prepare("
            SELECT id, name, code, email_type, invoice_type, subject
            FROM email_templates 
            WHERE is_active = 1
            ORDER BY email_type, name
        ");
        $stmt->execute();
        $templates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Group templates by type
        $groupedTemplates = [];
        foreach ($templates as $template) {
            $type = $template['email_type'] ?: 'general';
            if (!isset($groupedTemplates[$type])) {
                $groupedTemplates[$type] = [];
            }
            $groupedTemplates[$type][] = $template;
        }
        
        // Get current automation settings
        $settings = $this->getAutomationSettings();
        
        $this->render('admin/email-automation-settings', [
            'invoiceTypes' => $invoiceTypes,
            'emailTemplates' => $groupedTemplates,
            'settings' => $settings,
            'delayOptions' => [
                'immediate' => __('email.automation.immediate'),
                '1h' => __('email.automation.1_hour'),
                '24h' => __('email.automation.24_hours'),
                'custom' => __('email.automation.custom')
            ],
            'reminderDays' => [7, 14, 30, 60, 90]
        ]);
    }
    
    /**
     * Save email automation settings
     */
    public function save()
    {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 401);
            return;
        }
        
        // Check permission
        $permissionService = \App\Services\PermissionService::getInstance();
        if (!$permissionService->hasPermission('config_edit')) {
            $this->json(['success' => false, 'message' => 'Permission denied'], 403);
            return;
        }
        
        try {
            $data = \Flight::request()->data->getData();
            
            // Validate data
            if (!isset($data['settings'])) {
                throw new \Exception(__('errors.invalid_data'));
            }
            
            $settings = $data['settings'];
            
            // Save master enable/disable
            Setting::set('email_automation_enabled', $settings['enabled'] ?? false);
            
            // Save invoice type settings
            if (isset($settings['invoice_types'])) {
                foreach ($settings['invoice_types'] as $typeId => $typeSettings) {
                    $key = "email_automation_invoice_type_{$typeId}";
                    Setting::set($key, json_encode($typeSettings));
                }
            }
            
            // Save payment reminder settings
            if (isset($settings['payment_reminders'])) {
                Setting::set('email_automation_payment_reminders', json_encode($settings['payment_reminders']));
            }
            
            // Clear cache
            $this->cache->clear('email_automation_*');
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('messages.settings_saved')
            ]);
            
        } catch (\Exception $e) {
            error_log("Error saving email automation settings: " . $e->getMessage());
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Test email template with sample data
     */
    public function test()
    {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            $this->json(['success' => false, 'message' => 'Unauthorized'], 401);
            return;
        }
        
        // Check permission
        $permissionService = \App\Services\PermissionService::getInstance();
        if (!$permissionService->hasPermission('config_edit')) {
            $this->json(['success' => false, 'message' => 'Permission denied'], 403);
            return;
        }
        
        try {
            $data = \Flight::request()->data->getData();
            
            if (!isset($data['template_id']) || !isset($data['email'])) {
                throw new \Exception(__('errors.missing_required_fields'));
            }
            
            // Get template
            $db = \Flight::db();
            $stmt = $db->prepare("SELECT * FROM email_templates WHERE id = ? AND is_active = 1");
            $stmt->execute([$data['template_id']]);
            $template = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$template) {
                throw new \Exception(__('errors.template_not_found'));
            }
            
            // Generate sample data based on template type
            $sampleData = $this->generateSampleData($template['email_type']);
            
            // Send test email
            $result = $this->emailService->sendWithTemplate(
                $template['code'],
                $data['email'],
                $sampleData,
                [
                    'is_test' => true,
                    'test_prefix' => '[TEST] '
                ]
            );
            
            if ($result['success']) {
                $this->jsonResponse([
                    'success' => true,
                    'message' => __('messages.test_email_sent')
                ]);
            } else {
                throw new \Exception($result['error'] ?? __('errors.email_send_failed'));
            }
            
        } catch (\Exception $e) {
            error_log("Error sending test email: " . $e->getMessage());
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Preview email template
     */
    public function preview($templateId)
    {
        // Check authentication
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('/login');
            return;
        }
        
        // Check permission
        $permissionService = \App\Services\PermissionService::getInstance();
        if (!$permissionService->hasPermission('config_view')) {
            $this->flash('error', 'You do not have permission to access this page.');
            $this->redirect('/');
            return;
        }
        
        try {
            // Get template
            $db = \Flight::db();
            $stmt = $db->prepare("SELECT * FROM email_templates WHERE id = ? AND is_active = 1");
            $stmt->execute([$templateId]);
            $template = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$template) {
                throw new \Exception(__('errors.template_not_found'));
            }
            
            // Generate sample data
            $sampleData = $this->generateSampleData($template['email_type']);
            
            // Process template with sample data
            $subject = $this->emailService->processTemplate($template['subject'], $sampleData);
            $bodyHtml = $this->emailService->processTemplate($template['body_html'], $sampleData);
            $bodyText = $this->emailService->processTemplate($template['body_text'], $sampleData);
            
            $this->jsonResponse([
                'success' => true,
                'preview' => [
                    'subject' => $subject,
                    'body_html' => $bodyHtml,
                    'body_text' => $bodyText,
                    'variables' => $sampleData
                ]
            ]);
            
        } catch (\Exception $e) {
            error_log("Error previewing template: " . $e->getMessage());
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get current automation settings
     */
    private function getAutomationSettings()
    {
        $settings = [
            'enabled' => Setting::get('email_automation_enabled', false),
            'invoice_types' => [],
            'payment_reminders' => json_decode(Setting::get('email_automation_payment_reminders', '{}'), true)
        ];
        
        // Get invoice type specific settings
        $invoiceTypes = InvoiceType::getActive();
        foreach ($invoiceTypes as $type) {
            $key = "email_automation_invoice_type_{$type['id']}";
            $typeSettings = json_decode(Setting::get($key, '{}'), true);
            
            // Set defaults if not configured
            if (empty($typeSettings)) {
                $typeSettings = [
                    'enabled' => false,
                    'delay' => 'immediate',
                    'custom_delay' => 0,
                    'template_id' => null
                ];
            }
            
            $settings['invoice_types'][$type['id']] = $typeSettings;
        }
        
        // Set default payment reminder settings if not configured
        if (empty($settings['payment_reminders'])) {
            $settings['payment_reminders'] = [
                'enabled' => false,
                'reminders' => [
                    ['days' => 7, 'template_id' => null],
                    ['days' => 14, 'template_id' => null],
                    ['days' => 30, 'template_id' => null]
                ],
                'max_reminders' => 3
            ];
        }
        
        return $settings;
    }
    
    /**
     * Generate sample data for template preview/test
     */
    private function generateSampleData($emailType)
    {
        $baseData = [
            'company_name' => 'Sample Company Ltd.',
            'company_email' => '<EMAIL>',
            'company_phone' => '+33 1 23 45 67 89',
            'company_address' => '123 Main Street, Paris, France',
            'current_date' => date('d/m/Y'),
            'current_year' => date('Y')
        ];
        
        switch ($emailType) {
            case 'invoice':
                return array_merge($baseData, [
                    'invoice_number' => 'INV-2025-001',
                    'invoice_date' => date('d/m/Y'),
                    'invoice_due_date' => date('d/m/Y', strtotime('+30 days')),
                    'invoice_amount' => '1,234.56 €',
                    'invoice_status' => 'pending',
                    'client_name' => 'John Doe',
                    'client_email' => '<EMAIL>',
                    'client_company' => 'Doe Industries',
                    'invoice_items' => 'Service A, Service B',
                    'payment_link' => 'https://example.com/pay/INV-2025-001'
                ]);
                
            case 'payment_reminder':
                return array_merge($baseData, [
                    'invoice_number' => 'INV-2025-001',
                    'invoice_date' => date('d/m/Y', strtotime('-14 days')),
                    'invoice_due_date' => date('d/m/Y', strtotime('-7 days')),
                    'invoice_amount' => '1,234.56 €',
                    'days_overdue' => '7',
                    'client_name' => 'John Doe',
                    'client_email' => '<EMAIL>',
                    'reminder_number' => '1',
                    'payment_link' => 'https://example.com/pay/INV-2025-001'
                ]);
                
            case 'quote':
                return array_merge($baseData, [
                    'quote_number' => 'QUO-2025-001',
                    'quote_date' => date('d/m/Y'),
                    'quote_valid_until' => date('d/m/Y', strtotime('+30 days')),
                    'quote_amount' => '2,345.67 €',
                    'client_name' => 'Jane Smith',
                    'client_email' => '<EMAIL>',
                    'client_company' => 'Smith Corp',
                    'quote_items' => 'Product X, Product Y'
                ]);
                
            default:
                return array_merge($baseData, [
                    'recipient_name' => 'Valued Customer',
                    'recipient_email' => '<EMAIL>'
                ]);
        }
    }
}