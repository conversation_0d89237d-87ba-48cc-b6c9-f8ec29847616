<?php

namespace App\Models;

use Flight;

class Translation
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Get all translations for a specific language and group
     */
    public function getByLanguageAndGroup($language, $group = null)
    {
        if ($group) {
            $query = "SELECT * FROM translations WHERE language = ? AND `group` = ? ORDER BY `key`";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language, $group]);
        } else {
            $query = "SELECT * FROM translations WHERE language = ? ORDER BY `group`, `key`";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language]);
        }
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all available groups
     */
    public function getGroups($language = null)
    {
        if ($language) {
            $query = "SELECT DISTINCT `group` FROM translations WHERE language = ? ORDER BY `group`";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language]);
        } else {
            $query = "SELECT DISTINCT `group` FROM translations ORDER BY `group`";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
        }
        
        return $stmt->fetchAll(\PDO::FETCH_COLUMN);
    }
    
    /**
     * Get all available languages
     */
    public function getLanguages()
    {
        $query = "SELECT DISTINCT language FROM translations ORDER BY language";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(\PDO::FETCH_COLUMN);
    }
    
    /**
     * Update or create a single translation
     */
    public function updateTranslation($language, $group, $key, $value)
    {
        $query = "INSERT INTO translations (language, `group`, `key`, value) 
                  VALUES (?, ?, ?, ?) 
                  ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = NOW()";
        
        $stmt = $this->db->prepare($query);
        return $stmt->execute([$language, $group, $key, $value]);
    }
    
    /**
     * Delete a translation
     */
    public function deleteTranslation($language, $group, $key)
    {
        $query = "DELETE FROM translations WHERE language = ? AND `group` = ? AND `key` = ?";
        $stmt = $this->db->prepare($query);
        return $stmt->execute([$language, $group, $key]);
    }
    
    /**
     * Import translations in bulk
     */
    public function importTranslations($language, $data, $type = 'merge', $userId = null)
    {
        $this->db->beginTransaction();
        
        try {
            $keysImported = 0;
            $keysUpdated = 0;
            $keysAdded = 0;
            
            // If replace type, delete existing translations for this language
            if ($type === 'replace') {
                $query = "DELETE FROM translations WHERE language = ?";
                $stmt = $this->db->prepare($query);
                $stmt->execute([$language]);
            }
            
            // Process each group
            foreach ($data as $group => $translations) {
                foreach ($translations as $key => $value) {
                    // Check if translation exists
                    $checkQuery = "SELECT id FROM translations WHERE language = ? AND `group` = ? AND `key` = ?";
                    $checkStmt = $this->db->prepare($checkQuery);
                    $checkStmt->execute([$language, $group, $key]);
                    $exists = $checkStmt->fetch();
                    
                    if ($type === 'add_new' && $exists) {
                        continue; // Skip existing translations
                    }
                    
                    // Update or insert translation
                    $this->updateTranslation($language, $group, $key, $value);
                    
                    $keysImported++;
                    if ($exists) {
                        $keysUpdated++;
                    } else {
                        $keysAdded++;
                    }
                }
            }
            
            // Log the import
            if ($userId) {
                $logQuery = "INSERT INTO translation_imports 
                            (user_id, language, filename, import_type, keys_imported, keys_updated, keys_added) 
                            VALUES (?, ?, ?, ?, ?, ?, ?)";
                $logStmt = $this->db->prepare($logQuery);
                $logStmt->execute([$userId, $language, 'bulk_import', $type, $keysImported, $keysUpdated, $keysAdded]);
            }
            
            $this->db->commit();
            
            return [
                'success' => true,
                'keys_imported' => $keysImported,
                'keys_updated' => $keysUpdated,
                'keys_added' => $keysAdded
            ];
            
        } catch (\Exception $e) {
            $this->db->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Export translations to array format
     */
    public function exportTranslations($language, $group = null)
    {
        $translations = $this->getByLanguageAndGroup($language, $group);
        
        $result = [];
        foreach ($translations as $translation) {
            if (!isset($result[$translation['group']])) {
                $result[$translation['group']] = [];
            }
            $result[$translation['group']][$translation['key']] = $translation['value'];
        }
        
        return $result;
    }
    
    /**
     * Search translations
     */
    public function searchTranslations($language, $searchTerm, $group = null)
    {
        $search = '%' . $searchTerm . '%';
        
        if ($group) {
            $query = "SELECT * FROM translations 
                     WHERE language = ? AND `group` = ? 
                     AND (`key` LIKE ? OR value LIKE ?)
                     ORDER BY `key`";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language, $group, $search, $search]);
        } else {
            $query = "SELECT * FROM translations 
                     WHERE language = ? 
                     AND (`key` LIKE ? OR value LIKE ?)
                     ORDER BY `group`, `key`";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language, $search, $search]);
        }
        
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get a single translation value
     */
    public function getTranslation($language, $group, $key)
    {
        $query = "SELECT value FROM translations WHERE language = ? AND `group` = ? AND `key` = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$language, $group, $key]);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        return $result ? $result['value'] : null;
    }
    
    /**
     * Save or update a translation
     */
    public function saveTranslation($language, $group, $key, $value)
    {
        // Check if translation exists
        $query = "SELECT id FROM translations WHERE language = ? AND `group` = ? AND `key` = ?";
        $stmt = $this->db->prepare($query);
        $stmt->execute([$language, $group, $key]);
        $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($existing) {
            // Update existing translation
            $query = "UPDATE translations SET value = ?, updated_at = NOW() WHERE id = ?";
            $stmt = $this->db->prepare($query);
            return $stmt->execute([$value, $existing['id']]);
        } else {
            // Insert new translation
            $query = "INSERT INTO translations (language, `group`, `key`, value, created_at, updated_at) VALUES (?, ?, ?, ?, NOW(), NOW())";
            $stmt = $this->db->prepare($query);
            return $stmt->execute([$language, $group, $key, $value]);
        }
    }
    
    /**
     * Get translation statistics
     */
    public function getStatistics($language = null)
    {
        $stats = [];
        
        if ($language) {
            // Get total count for specific language
            $query = "SELECT COUNT(*) as total FROM translations WHERE language = ?";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language]);
            $stats['total'] = $stmt->fetch(\PDO::FETCH_ASSOC)['total'];
            
            // Get count by group
            $query = "SELECT `group`, COUNT(*) as count FROM translations WHERE language = ? GROUP BY `group`";
            $stmt = $this->db->prepare($query);
            $stmt->execute([$language]);
            $stats['by_group'] = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } else {
            // Get total count
            $query = "SELECT COUNT(*) as total FROM translations";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['total'] = $stmt->fetch(\PDO::FETCH_ASSOC)['total'];
            
            // Get count by language
            $query = "SELECT language, COUNT(*) as count FROM translations GROUP BY language";
            $stmt = $this->db->prepare($query);
            $stmt->execute();
            $stats['by_language'] = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        }
        
        return $stats;
    }
    
    /**
     * Sync translations from language files to database
     */
    public function syncFromFiles($languageDir, $language = null)
    {
        $results = [];
        
        // Get all language directories
        $languages = $language ? [$language] : array_diff(scandir($languageDir), ['.', '..']);
        
        foreach ($languages as $lang) {
            if (!is_dir($languageDir . '/' . $lang)) {
                continue;
            }
            
            $langResults = [
                'language' => $lang,
                'files_processed' => 0,
                'keys_imported' => 0,
                'keys_added' => 0,
                'keys_updated' => 0
            ];
            
            // Get all PHP files in language directory
            $files = glob($languageDir . '/' . $lang . '/*.php');
            
            foreach ($files as $file) {
                $group = basename($file, '.php');
                $translations = include $file;
                
                if (is_array($translations)) {
                    foreach ($translations as $key => $value) {
                        // Check if translation exists
                        $existing = $this->getTranslation($lang, $group, $key);
                        $this->updateTranslation($lang, $group, $key, $value);
                        $langResults['keys_imported']++;
                        if ($existing === null) {
                            $langResults['keys_added']++;
                        } else {
                            $langResults['keys_updated']++;
                        }
                    }
                    $langResults['files_processed']++;
                }
            }
            
            $results[$lang] = $langResults;
        }
        
        return $results;
    }
}