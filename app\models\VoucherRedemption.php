<?php

namespace App\Models;

use App\Core\Model;
use Exception;

class VoucherRedemption extends Model
{
    protected $table = 'voucher_redemptions';
    
    protected $fillable = [
        'voucher_id',
        'redemption_date',
        'amount_used',
        'invoice_id',
        'redeemed_by'
    ];
    
    protected $casts = [
        'redemption_date' => 'datetime',
        'amount_used' => 'decimal'
    ];
    
    /**
     * Get the voucher
     */
    public function voucher()
    {
        return $this->belongsTo(Voucher::class, 'voucher_id');
    }
    
    /**
     * Get the invoice
     */
    public function invoice()
    {
        return $this->belongsTo(SalesInvoice::class, 'invoice_id');
    }
    
    /**
     * Get the user who redeemed
     */
    public function redeemedBy()
    {
        return $this->belongsTo(User::class, 'redeemed_by');
    }
    
    /**
     * Create a redemption
     */
    public static function redeem($voucherCode, $amount, $invoiceId, $userId)
    {
        // Find the voucher
        $voucher = Voucher::where('voucher_code', $voucherCode)->first();
        
        if (!$voucher) {
            throw new Exception('Voucher not found');
        }
        
        // Check if voucher is valid
        if ($voucher->status === 'expired') {
            throw new Exception('Voucher has expired');
        }
        
        if ($voucher->status === 'used') {
            throw new Exception('Voucher has already been fully used');
        }
        
        if ($voucher->status === 'cancelled') {
            throw new Exception('Voucher has been cancelled');
        }
        
        // Check expiry date
        if ($voucher->expiry_date < date('Y-m-d')) {
            $voucher->status = 'expired';
            $voucher->save();
            throw new Exception('Voucher has expired');
        }
        
        // Check remaining value
        if ($amount > $voucher->remaining_value) {
            throw new Exception('Amount exceeds voucher remaining value');
        }
        
        // Create redemption record
        $redemption = static::create([
            'voucher_id' => $voucher->id,
            'redemption_date' => date('Y-m-d H:i:s'),
            'amount_used' => $amount,
            'invoice_id' => $invoiceId,
            'redeemed_by' => $userId
        ]);
        
        // Update voucher remaining value and status
        $voucher->remaining_value -= $amount;
        
        if ($voucher->remaining_value == 0) {
            $voucher->status = 'used';
        } elseif ($voucher->remaining_value < $voucher->original_value) {
            $voucher->status = 'partial';
        }
        
        $voucher->save();
        
        return $redemption;
    }
    
    /**
     * Get formatted redemption info
     */
    public function getFormattedInfo()
    {
        return sprintf(
            'Redeemed %.2f € on %s',
            $this->amount_used,
            $this->redemption_date->format('d/m/Y H:i')
        );
    }
    
    /**
     * Get redemption summary for a period
     */
    public static function getRedemptionSummary($startDate = null, $endDate = null)
    {
        $query = static::query();
        
        if ($startDate && $endDate) {
            $query->whereBetween('redemption_date', [$startDate, $endDate]);
        }
        
        return $query->selectRaw('
                COUNT(*) as redemption_count,
                SUM(amount_used) as total_amount,
                COUNT(DISTINCT voucher_id) as unique_vouchers,
                COUNT(DISTINCT invoice_id) as unique_invoices
            ')
            ->first();
    }
    
    /**
     * Get top redeemed vouchers
     */
    public static function getTopRedeemedVouchers($limit = 10, $startDate = null, $endDate = null)
    {
        $query = static::query();
        
        if ($startDate && $endDate) {
            $query->whereBetween('redemption_date', [$startDate, $endDate]);
        }
        
        return $query->selectRaw('
                voucher_id,
                COUNT(*) as redemption_count,
                SUM(amount_used) as total_redeemed
            ')
            ->groupBy('voucher_id')
            ->orderBy('total_redeemed', 'desc')
            ->limit($limit)
            ->get();
    }
}