<!DOCTYPE html>
<html>
<head>
    <title>Test All Invoice Types</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 1200px; margin: 0 auto; }
        .success { color: green; font-weight: bold; }
        .warning { color: orange; font-weight: bold; }
        .error { color: red; font-weight: bold; }
        .info { color: blue; }
        .test-section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        button { padding: 8px 15px; margin: 5px; cursor: pointer; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #e9e9e9; }
    </style>
</head>
<body>
    <h1>🧪 Complete Invoice Type Test</h1>
    
    <div class="test-section">
        <h2>Current Status:</h2>
        <ul>
            <li class="success">✅ Practitioners loading correctly (7 Kiné members)</li>
            <li class="warning">⚠️ Coaches array is empty (0 coaches)</li>
            <li class="error">❌ Column config API error</li>
            <li class="error">❌ Template API error</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>Quick Diagnostics:</h2>
        <button onclick="window.open('/fit/public/check_coaches_group.php', '_blank')">
            🔍 Check Coach Group (Group 2)
        </button>
        <button onclick="window.open('/fit/public/check_group_config.php', '_blank')">
            ⚙️ Check All Group Config
        </button>
        <button onclick="testColumnConfigAPI()">
            🔧 Test Column Config API
        </button>
    </div>
    
    <div class="test-section">
        <h2>Test Invoice Creation:</h2>
        <table>
            <tr>
                <th>Type</th>
                <th>Expected Dropdown</th>
                <th>Test Link</th>
                <th>Expected Console Output</th>
            </tr>
            <tr>
                <td>Standard</td>
                <td>All users (18)</td>
                <td><button onclick="window.open('/fit/public/invoices/create', '_blank')">Test</button></td>
                <td>Loading all users, count: 18</td>
            </tr>
            <tr>
                <td>Location</td>
                <td class="warning">Coaches (currently 0)</td>
                <td><button onclick="window.open('/fit/public/invoices/create?type=location', '_blank')">Test</button></td>
                <td>Loading coaches for location invoice, count: 0</td>
            </tr>
            <tr>
                <td>Retrocession 30%</td>
                <td class="success">Practitioners (7)</td>
                <td><button onclick="window.open('/fit/public/invoices/create?type=retrocession_30', '_blank')">Test</button></td>
                <td>Loading practitioners for retrocession invoice, count: 7</td>
            </tr>
            <tr>
                <td>Retrocession 25%</td>
                <td class="success">Practitioners (7)</td>
                <td><button onclick="window.open('/fit/public/invoices/create?type=retrocession_25', '_blank')">Test</button></td>
                <td>Loading practitioners for retrocession invoice, count: 7</td>
            </tr>
        </table>
    </div>
    
    <div class="test-section">
        <h2>Manual Console Tests:</h2>
        <p>Open browser console (F12) on any invoice page and run:</p>
        <pre>
// Check loaded data
console.log('Coaches:', coachesData);
console.log('Practitioners:', practitionersData);
console.log('All users:', usersData);

// Test dropdown reload
loadBillableOptions();

// Check current invoice type
const invoiceTypeSelect = document.getElementById('invoice_type_id');
console.log('Current invoice type:', invoiceTypeSelect.value);
console.log('Type code:', invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex].getAttribute('data-prefix'));
        </pre>
    </div>
    
    <div class="test-section">
        <h2>Solutions:</h2>
        <ol>
            <li><strong>Empty Coaches:</strong> Need to either:
                <ul>
                    <li>Add users to group 2 (Coach group)</li>
                    <li>Or identify which group contains the coaches</li>
                </ul>
            </li>
            <li><strong>Column Config Error:</strong> Fix the strpos() null parameter in the API</li>
            <li><strong>Template Error:</strong> Fix the 500 error in invoice-templates API</li>
        </ol>
    </div>
    
    <script>
    function testColumnConfigAPI() {
        const url = '/fit/public/api/column-config/invoice_items?documentTypeId=1&invoiceTypeId=12';
        fetch(url)
            .then(response => response.json())
            .then(data => {
                console.log('Column config API response:', data);
                alert('Check console for API response. Success: ' + (data.success ? 'Yes' : 'No'));
            })
            .catch(error => {
                console.error('API error:', error);
                alert('API Error: ' + error.message);
            });
    }
    </script>
</body>
</html>