<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== DOCUMENT TYPES IN DATABASE ===\n\n";
    
    // Check all document types
    $stmt = $pdo->query("SELECT id, code, name, prefix, counter_type FROM document_types ORDER BY id");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($types as $type) {
        echo "ID: {$type['id']}\n";
        echo "Code: {$type['code']}\n";
        echo "Prefix: {$type['prefix']}\n";
        echo "Counter Type: {$type['counter_type']}\n";
        
        // Decode name
        $nameData = json_decode($type['name'], true);
        if (is_array($nameData)) {
            echo "Names:\n";
            foreach ($nameData as $lang => $name) {
                echo "  - $lang: $name\n";
            }
        } else {
            echo "Name (raw): {$type['name']}\n";
        }
        echo "---\n\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}