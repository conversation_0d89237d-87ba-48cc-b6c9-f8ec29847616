<?php
// Run migration 072 - Add deleted invoice numbers pool (simplified version)

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

try {
    echo "<h2>Running Migration 072: Add deleted invoice numbers pool</h2>";
    
    // Check if table already exists
    $stmt = $db->query("SHOW TABLES LIKE 'deleted_invoice_numbers'");
    if ($stmt->fetch()) {
        echo "! Table deleted_invoice_numbers already exists<br>";
    } else {
        // Create deleted_invoice_numbers table
        $sql = "CREATE TABLE deleted_invoice_numbers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_type_id INT NOT NULL,
            invoice_type_id INT NULL COMMENT 'Optional: specific invoice type',
            invoice_number VARCHAR(100) NOT NULL,
            year INT NULL COMMENT 'For yearly sequences',
            month INT NULL COMMENT 'For monthly sequences',
            sequence_number INT NOT NULL COMMENT 'The actual sequence number that was used',
            deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            deleted_by INT NULL,
            reused_at TIMESTAMP NULL COMMENT 'When this number was reused',
            reused_by INT NULL COMMENT 'Who reused this number',
            UNIQUE KEY unique_number (invoice_number),
            INDEX idx_available (document_type_id, year, month, reused_at),
            INDEX idx_sequence (document_type_id, invoice_type_id, year, month, sequence_number),
            FOREIGN KEY (document_type_id) REFERENCES document_types(id) ON DELETE CASCADE,
            FOREIGN KEY (invoice_type_id) REFERENCES config_invoice_types(id) ON DELETE SET NULL,
            FOREIGN KEY (deleted_by) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (reused_by) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "✓ Created deleted_invoice_numbers table<br>";
    }
    
    // Add configuration option
    $stmt = $db->prepare("
        INSERT INTO config (config_key, config_value, created_at, updated_at) 
        VALUES ('reuse_deleted_invoice_numbers', 'true', NOW(), NOW())
        ON DUPLICATE KEY UPDATE config_value = 'true', updated_at = NOW()
    ");
    $stmt->execute();
    echo "✓ Added/Updated configuration option reuse_deleted_invoice_numbers = true<br>";
    
    // Add comment to document_sequences table
    try {
        $sql = "ALTER TABLE document_sequences 
                COMMENT = 'Tracks the highest used sequence number. When reuse_deleted_invoice_numbers is enabled, deleted numbers are tracked separately.'";
        $db->exec($sql);
        echo "✓ Updated document_sequences table comment<br>";
    } catch (Exception $e) {
        echo "! Could not update table comment (not critical)<br>";
    }
    
    // Add index to invoices table
    try {
        // Check if index already exists
        $stmt = $db->query("SHOW INDEX FROM invoices WHERE Key_name = 'idx_invoice_number'");
        if ($stmt->fetch()) {
            echo "! Index idx_invoice_number already exists on invoices table<br>";
        } else {
            $sql = "ALTER TABLE invoices ADD INDEX idx_invoice_number (invoice_number)";
            $db->exec($sql);
            echo "✓ Added index on invoices.invoice_number<br>";
        }
    } catch (Exception $e) {
        echo "! Could not add index (may already exist)<br>";
    }
    
    // Try to record migration if migrations table exists
    try {
        $stmt = $db->query("SHOW TABLES LIKE 'migrations'");
        if ($stmt->fetch()) {
            // Check if migration already recorded
            $stmt = $db->prepare("SELECT * FROM migrations WHERE name = 'add_deleted_invoice_numbers_pool' OR version = 72");
            $stmt->execute();
            if (!$stmt->fetch()) {
                $stmt = $db->prepare("
                    INSERT INTO migrations (version, name, executed_at) 
                    VALUES (72, 'add_deleted_invoice_numbers_pool', NOW())
                ");
                $stmt->execute();
                echo "✓ Recorded migration in migrations table<br>";
            } else {
                echo "! Migration already recorded<br>";
            }
        }
    } catch (Exception $e) {
        // Migrations table might not exist or have different structure
        echo "! Could not record in migrations table (not critical)<br>";
    }
    
    echo "<br><strong>Migration completed successfully!</strong><br>";
    echo "<br>The system will now reuse deleted invoice numbers when creating new invoices.";
    echo "<br><br><a href='fix_invoice_number_186.php'>Click here to fix invoice number</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
    echo "<br><br><strong>Stack trace:</strong><br>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>