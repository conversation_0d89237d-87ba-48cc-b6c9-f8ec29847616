<?php

namespace App\Helpers;

/**
 * FormatHelper - Centralized formatting for currency and percentages
 */
class FormatHelper
{
    /**
     * Format currency with space before € symbol
     * 
     * @param float $amount The amount to format
     * @param int $decimals Number of decimal places (default: 2)
     * @param string $decimalSeparator Decimal separator (default: ,)
     * @param string $thousandsSeparator Thousands separator (default: .)
     * @return string Formatted currency string with space before €
     */
    public static function formatCurrency($amount, $decimals = 2, $decimalSeparator = ',', $thousandsSeparator = '.')
    {
        if ($amount === null || $amount === '') {
            $amount = 0;
        }
        
        return number_format((float)$amount, $decimals, $decimalSeparator, $thousandsSeparator) . ' €';
    }
    
    /**
     * Format percentage with space before % symbol
     * 
     * @param float $value The percentage value
     * @param int $decimals Number of decimal places (default: 0)
     * @param string $decimalSeparator Decimal separator (default: ,)
     * @param string $thousandsSeparator Thousands separator (default: .)
     * @return string Formatted percentage string with space before %
     */
    public static function formatPercentage($value, $decimals = 0, $decimalSeparator = ',', $thousandsSeparator = '.')
    {
        if ($value === null || $value === '') {
            $value = 0;
        }
        
        return number_format((float)$value, $decimals, $decimalSeparator, $thousandsSeparator) . ' %';
    }
    
    /**
     * Format currency without symbol (just the number)
     * 
     * @param float $amount The amount to format
     * @param int $decimals Number of decimal places (default: 2)
     * @param string $decimalSeparator Decimal separator (default: ,)
     * @param string $thousandsSeparator Thousands separator (default: .)
     * @return string Formatted number string
     */
    public static function formatNumber($amount, $decimals = 2, $decimalSeparator = ',', $thousandsSeparator = '.')
    {
        if ($amount === null || $amount === '') {
            $amount = 0;
        }
        
        return number_format((float)$amount, $decimals, $decimalSeparator, $thousandsSeparator);
    }
}