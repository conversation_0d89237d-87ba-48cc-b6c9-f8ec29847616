{% extends "base-modern.twig" %}

{% block title %}{{ __('translations.editor') }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ __('translations.translation_management') }}</h3>
                <div class="card-tools">
                    <a href="{{ base_url }}/translations/multilingual" class="btn btn-info btn-sm">
                        <i class="bi bi-translate"></i> {{ __('translations.multilingual_editor') }}
                    </a>
                    <a href="{{ base_url }}/translations/diagnostic" class="btn btn-warning btn-sm">
                        <i class="bi bi-bug"></i> {{ __('translations.diagnostic') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Form -->
                <form method="get" action="{{ base_url }}/translations" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <select name="language" class="form-select" onchange="this.form.submit()">
                                {% for lang in languages %}
                                    <option value="{{ lang }}" {% if lang == currentLanguage %}selected{% endif %}>
                                        {{ lang|upper }} - {{ __('languages.' ~ lang) }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="group" class="form-select" onchange="this.form.submit()">
                                <option value="">{{ __('translations.all_groups') }}</option>
                                {% for grp in groups %}
                                    <option value="{{ grp }}" {% if grp == currentGroup %}selected{% endif %}>
                                        {{ grp }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="{{ __('translations.search_translations') }}" 
                                   value="{{ search }}">
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group w-100" role="group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> {{ __('common.search') }}
                                </button>
                                <a href="{{ base_url }}/translations" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> {{ __('common.clear') }}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <div class="form-check">
                                <input type="checkbox" name="show_missing" value="1" class="form-check-input" 
                                       id="showMissing" {% if showMissing %}checked{% endif %} 
                                       onchange="this.form.submit()">
                                <label class="form-check-label" for="showMissing">
                                    {{ __('translations.missing_translations') }}
                                </label>
                            </div>
                        </div>
                    </div>
                </form>

                <!-- Statistics -->
                {% if stats %}
                <div class="alert alert-info mb-4">
                    <h5 class="mb-2">{{ __('translations.statistics') }}</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>{{ __('translations.total_translations') }}:</strong> {{ stats.total }}
                        </div>
                        <div class="col-md-3">
                            <strong>{{ __('translations.translated') }}:</strong> {{ stats.total }}
                        </div>
                        {% if stats.by_group %}
                        <div class="col-md-6">
                            <strong>{{ __('common.by_group') }}:</strong>
                            {% for group in stats.by_group %}
                                <span class="badge bg-secondary me-1">{{ group.group }}: {{ group.count }}</span>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                <!-- Translations Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="translationsTable">
                        <thead>
                            <tr>
                                <th style="width: 150px;">{{ __('translations.group') }}</th>
                                <th style="width: 250px;">{{ __('translations.key') }}</th>
                                <th>{{ __('translations.value') }}</th>
                                <th style="width: 120px;">{{ __('common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for translation in translations %}
                            <tr id="translation-{{ translation.id }}">
                                <td>
                                    <span class="badge bg-primary">{{ translation.group }}</span>
                                </td>
                                <td>
                                    <code>{{ translation.key }}</code>
                                </td>
                                <td>
                                    <div class="translation-value" data-id="{{ translation.id }}">
                                        <span class="value-display">{{ translation.value|default('<em class="text-muted">' ~ __('translations.not_translated') ~ '</em>')|raw }}</span>
                                        <div class="value-edit d-none">
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-sm" 
                                                       value="{{ translation.value }}" 
                                                       data-original="{{ translation.value }}">
                                                <button class="btn btn-sm btn-success save-btn" type="button">
                                                    <i class="bi bi-check"></i>
                                                </button>
                                                <button class="btn btn-sm btn-secondary cancel-btn" type="button">
                                                    <i class="bi bi-x"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-primary edit-translation" 
                                                data-id="{{ translation.id }}" 
                                                title="{{ __('common.edit') }}">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-danger delete-translation" 
                                                data-id="{{ translation.id }}" 
                                                data-group="{{ translation.group }}"
                                                data-key="{{ translation.key }}"
                                                title="{{ __('common.delete') }}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-translate fs-1 d-block mb-2"></i>
                                        {{ __('translations.no_translations_found') }}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Add Translation Button -->
                <div class="mt-3">
                    <button class="btn btn-success" id="addTranslationBtn">
                        <i class="bi bi-plus-circle"></i> {{ __('translations.add_translation') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Translation Modal -->
<div class="modal fade" id="addTranslationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="addTranslationForm">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('translations.add_translation') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ __('translations.language') }}</label>
                        <select name="language" class="form-select" required>
                            {% for lang in languages %}
                                <option value="{{ lang }}" {% if lang == currentLanguage %}selected{% endif %}>
                                    {{ lang|upper }} - {{ __('languages.' ~ lang) }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('translations.group') }}</label>
                        <input type="text" name="group" class="form-control" required 
                               list="groupsList" placeholder="e.g., common, users, invoices">
                        <datalist id="groupsList">
                            {% for grp in groups %}
                                <option value="{{ grp }}">
                            {% endfor %}
                        </datalist>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('translations.key') }}</label>
                        <input type="text" name="key" class="form-control" required 
                               placeholder="e.g., welcome_message">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">{{ __('translations.value') }}</label>
                        <textarea name="value" class="form-control" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> {{ __('common.save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Edit translation inline
    $('.edit-translation').on('click', function() {
        var id = $(this).data('id');
        var container = $('.translation-value[data-id="' + id + '"]');
        var display = container.find('.value-display');
        var edit = container.find('.value-edit');
        
        display.addClass('d-none');
        edit.removeClass('d-none');
        edit.find('input').focus().select();
    });
    
    // Cancel edit
    $('.cancel-btn').on('click', function() {
        var container = $(this).closest('.translation-value');
        var display = container.find('.value-display');
        var edit = container.find('.value-edit');
        var input = edit.find('input');
        
        input.val(input.data('original'));
        edit.addClass('d-none');
        display.removeClass('d-none');
    });
    
    // Save translation
    $('.save-btn').on('click', function() {
        var btn = $(this);
        var container = btn.closest('.translation-value');
        var id = container.data('id');
        var input = container.find('input');
        var value = input.val();
        
        btn.prop('disabled', true);
        
        $.ajax({
            url: '{{ base_url }}/translations/' + id,
            method: 'PUT',
            data: {
                value: value,
                _method: 'PUT',
                csrf_token: '{{ csrf_token }}'
            },
            success: function(response) {
                if (response.success) {
                    var display = container.find('.value-display');
                    var edit = container.find('.value-edit');
                    
                    display.html(value || '<em class="text-muted">{{ __("translations.not_translated") }}</em>');
                    input.data('original', value);
                    
                    edit.addClass('d-none');
                    display.removeClass('d-none');
                    
                    toastr.success('{{ __("translations.translation_saved") }}');
                } else {
                    toastr.error(response.message || '{{ __("common.error_occurred") }}');
                }
            },
            error: function() {
                toastr.error('{{ __("common.error_occurred") }}');
            },
            complete: function() {
                btn.prop('disabled', false);
            }
        });
    });
    
    // Save on Enter key
    $('.value-edit input').on('keypress', function(e) {
        if (e.which === 13) {
            $(this).siblings('.btn-group').find('.save-btn').click();
        }
    });
    
    // Delete translation
    $('.delete-translation').on('click', function() {
        var btn = $(this);
        var id = btn.data('id');
        var group = btn.data('group');
        var key = btn.data('key');
        
        Swal.fire({
            title: '{{ __("common.are_you_sure") }}',
            text: group + '.' + key,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: '{{ __("common.yes_delete") }}',
            cancelButtonText: '{{ __("common.cancel") }}'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '{{ base_url }}/translations/' + id,
                    method: 'DELETE',
                    data: {
                        _method: 'DELETE',
                        csrf_token: '{{ csrf_token }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#translation-' + id).fadeOut(function() {
                                $(this).remove();
                            });
                            toastr.success('{{ __("translations.translation_deleted") }}');
                        } else {
                            toastr.error(response.message || '{{ __("common.error_occurred") }}');
                        }
                    },
                    error: function() {
                        toastr.error('{{ __("common.error_occurred") }}');
                    }
                });
            }
        });
    });
    
    // Add translation modal
    $('#addTranslationBtn').on('click', function() {
        $('#addTranslationModal').modal('show');
    });
    
    // Add translation form
    $('#addTranslationForm').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var data = form.serialize() + '&csrf_token={{ csrf_token }}';
        
        $.ajax({
            url: '{{ base_url }}/translations',
            method: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    $('#addTranslationModal').modal('hide');
                    form[0].reset();
                    toastr.success('{{ __("translations.translation_saved") }}');
                    
                    // Reload page to show new translation
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    toastr.error(response.message || '{{ __("common.error_occurred") }}');
                }
            },
            error: function() {
                toastr.error('{{ __("common.error_occurred") }}');
            }
        });
    });
});
</script>
{% endblock %}