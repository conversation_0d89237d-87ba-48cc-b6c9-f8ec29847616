# Development Configuration

## CSRF Protection

CSRF (Cross-Site Request Forgery) protection can be disabled during development by setting the `CSRF_PROTECTION` environment variable in your `.env` file:

```bash
# Enable CSRF protection (default)
CSRF_PROTECTION=true

# Disable CSRF protection (development only!)
CSRF_PROTECTION=false
```

**⚠️ WARNING:** Never disable CSRF protection in production!

### How it works

When CSRF protection is enabled:
- All POST, PUT, PATCH, and DELETE requests must include a valid CSRF token
- The token can be included as:
  - Form field: `<input type="hidden" name="csrf_token" value="{{ csrf_token }}">`
  - HTTP header: `X-CSRF-Token: [token]`
  - Query parameter: `?csrf_token=[token]`

When CSRF protection is disabled:
- All CSRF validation is skipped
- Forms still generate tokens but they are not validated
- Useful for API testing or development

### Testing with CSRF disabled

1. Edit `.env` file:
   ```bash
   CSRF_PROTECTION=false
   ```

2. Test your forms without CSRF tokens:
   ```bash
   curl -X POST http://localhost/fit/public/clients/1 \
     -d "_method=PUT" \
     -d "email=<EMAIL>"
   ```

3. **Remember to re-enable before deploying:**
   ```bash
   CSRF_PROTECTION=true
   ```

## Other Development Settings

### Debug Mode
```bash
APP_DEBUG=true  # Show detailed error messages
APP_DEBUG=false # Hide error details (production)
```

### Database
```bash
DB_HOST=127.0.0.1
DB_DATABASE=fitapp
DB_USERNAME=root
DB_PASSWORD=test1234
```

### Session Lifetime
```bash
SESSION_LIFETIME=120  # Minutes
```