<?php
/**
 * Package Management Routes
 */

use App\Controllers\PackageController;

// Package routes
Flight::group('/packages', function() {
    
    // List packages
    Flight::route('GET /', [new PackageController(), 'index']);
    
    // Create package
    Flight::route('GET /create', [new PackageController(), 'create']);
    Flight::route('POST /create', [new PackageController(), 'store']);
    
    // Package details
    Flight::route('GET /@id:[0-9]+', [new PackageController(), 'show']);
    
    // Edit package
    Flight::route('GET /@id:[0-9]+/edit', [new PackageController(), 'edit']);
    Flight::route('POST /@id:[0-9]+/edit', [new PackageController(), 'update']);
    
    // Delete package
    Flight::route('DELETE /@id:[0-9]+', [new PackageController(), 'destroy']);
    Flight::route('POST /@id:[0-9]+/delete', [new PackageController(), 'destroy']);
    
    // Duplicate package
    Flight::route('POST /@id:[0-9]+/duplicate', [new PackageController(), 'duplicate']);
    
    // Purchase package
    Flight::route('GET /@id:[0-9]+/purchase', [new PackageController(), 'purchase']);
    Flight::route('POST /@id:[0-9]+/purchase', [new PackageController(), 'processPurchase']);
    
    // List all purchases
    Flight::route('GET /purchases', [new PackageController(), 'purchases']);
    
    // Purchase details
    Flight::route('GET /purchases/@id:[0-9]+', [new PackageController(), 'showPurchase']);
    
    // Use session from package
    Flight::route('POST /purchases/@id:[0-9]+/use-session', [new PackageController(), 'useSession']);
    
    // Get client's packages
    Flight::route('GET /client/@clientId:[0-9]+', [new PackageController(), 'clientPackages']);
    
    // Expiring packages
    Flight::route('GET /expiring', [new PackageController(), 'expiring']);
    
});