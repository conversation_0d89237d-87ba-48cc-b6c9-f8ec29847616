#!/usr/bin/env php
<?php
/**
 * Command-line script to create permission management system tables
 * Run: php database/create_permission_management_system.php
 */

// Ensure we're running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from the command line\n");
}

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$username = $_ENV['DB_USER'];
$password = $_ENV['DB_PASS'];
$database = $_ENV['DB_NAME'];

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    
    echo "✓ Connected to database successfully.\n";
    echo "\n=== Checking Permission Management System Tables ===\n";
    
    // Tables to check
    $tables = [
        'system_modules' => 'System modules registry',
        'module_permissions' => 'Module-specific permissions',
        'group_permissions' => 'Group permission assignments',
        'permission_templates' => 'Permission templates for quick setup',
        'permission_audit_log' => 'Permission change audit trail'
    ];
    
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table => $description) {
        $result = $mysqli->query("SHOW TABLES LIKE '$table'");
        if ($result->num_rows > 0) {
            $existingTables[] = $table;
            echo "✓ $table - $description (exists)\n";
        } else {
            $missingTables[] = $table;
            echo "× $table - $description (missing)\n";
        }
    }
    
    if (empty($missingTables)) {
        echo "\n✓ All permission management tables already exist.\n";
        
        // Show current statistics
        echo "\n=== Current Statistics ===\n";
        
        $stats = [
            'system_modules' => "SELECT COUNT(*) as count FROM system_modules",
            'module_permissions' => "SELECT COUNT(*) as count FROM module_permissions",
            'user_groups' => "SELECT COUNT(*) as count FROM user_groups",
            'group_permissions' => "SELECT COUNT(*) as count FROM group_permissions",
            'permission_templates' => "SELECT COUNT(*) as count FROM permission_templates"
        ];
        
        foreach ($stats as $table => $query) {
            $result = $mysqli->query($query);
            if ($result) {
                $row = $result->fetch_assoc();
                echo "  - $table: " . $row['count'] . " records\n";
            }
        }
        
        exit(0);
    }
    
    echo "\nCreating missing tables...\n";
    
    // Read the migration file
    $migrationFile = __DIR__ . '/migrations/200_create_permission_management_system.sql';
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', preg_split('/;(?=(?:[^\'"]|[\'"][^\'"]*[\'"])*$)/', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    // Execute each statement
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        // Add semicolon back
        $statement .= ';';
        
        // Extract table name for logging
        $tableName = null;
        if (preg_match('/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i', $statement, $matches)) {
            $tableName = $matches[1];
        } elseif (preg_match('/INSERT\s+INTO\s+`?(\w+)`?/i', $statement, $matches)) {
            $tableName = $matches[1] . ' (data)';
        } elseif (preg_match('/CREATE\s+INDEX\s+`?(\w+)`?/i', $statement, $matches)) {
            $tableName = 'INDEX ' . $matches[1];
        }
        
        // Execute statement
        if ($mysqli->query($statement) === TRUE) {
            if ($tableName) {
                echo "✓ Created/Updated: $tableName\n";
            }
            $successCount++;
        } else {
            // Check if it's a duplicate key error (which is OK for INSERT IGNORE)
            if ($mysqli->errno == 1062 || strpos($statement, 'INSERT IGNORE') !== false) {
                if ($tableName) {
                    echo "→ Skipped (already exists): $tableName\n";
                }
                $successCount++;
            } else {
                echo "× Error: " . $mysqli->error . "\n";
                if ($tableName) {
                    echo "  Failed on: $tableName\n";
                }
                $errorCount++;
            }
        }
    }
    
    echo "\n=== Migration Summary ===\n";
    echo "Successful operations: $successCount\n";
    echo "Failed operations: $errorCount\n";
    
    if ($errorCount > 0) {
        echo "\n⚠ Some operations failed. Please check the errors above.\n";
    }
    
    // Mark migration as applied
    $stmt = $mysqli->prepare("INSERT IGNORE INTO migrations (migration, applied_at) VALUES (?, NOW())");
    $migrationName = '200_create_permission_management_system.sql';
    $stmt->bind_param('s', $migrationName);
    
    if ($stmt->execute()) {
        echo "\n✓ Migration marked as applied\n";
    } else {
        echo "\nWarning: Could not mark migration as applied: " . $stmt->error . "\n";
    }
    $stmt->close();
    
    // Test the permission system
    echo "\n=== Testing Permission System ===\n";
    
    // Check if we have core modules
    $result = $mysqli->query("SELECT code, name FROM system_modules WHERE parent_id IS NULL ORDER BY sort_order");
    if ($result && $result->num_rows > 0) {
        echo "\nCore Modules:\n";
        while ($row = $result->fetch_assoc()) {
            echo "  - {$row['code']}: {$row['name']}\n";
        }
    }
    
    // Check permission templates
    $result = $mysqli->query("SELECT name, description, is_system FROM permission_templates ORDER BY id");
    if ($result && $result->num_rows > 0) {
        echo "\nPermission Templates:\n";
        while ($row = $result->fetch_assoc()) {
            $system = $row['is_system'] ? ' [SYSTEM]' : '';
            echo "  - {$row['name']}: {$row['description']}$system\n";
        }
    }
    
    // Check if Admin group has permissions
    $result = $mysqli->query("
        SELECT COUNT(*) as permission_count 
        FROM group_permissions gp
        JOIN user_groups ug ON gp.group_id = ug.id
        WHERE ug.name = 'Admin' AND gp.is_granted = 1
    ");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "\nAdmin Group Permissions: {$row['permission_count']} granted\n";
    }
    
    echo "\n=== Final Status ===\n";
    
    // Show final statistics
    foreach ($tables as $table => $description) {
        $result = $mysqli->query("SELECT COUNT(*) as count FROM $table");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "  - $table: {$row['count']} records\n";
        }
    }
    
    $mysqli->close();
    echo "\n✓ Permission management system setup completed successfully!\n";
    echo "You can now configure permissions through the admin interface.\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}