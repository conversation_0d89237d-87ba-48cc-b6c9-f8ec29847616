# Invoice Number Fix Guide

## The Problem
- You want invoice number FAC-2025-0186
- But the system shows FAC-2025-0187
- The sequence was reset to 185, but invoice 0187 already exists

## Quick Fix Options

### Option 1: Use the Fix Script (Recommended)
1. Navigate to: `http://localhost/fit/public/fix_invoice_number_0186.php`
2. The script will show you the current situation
3. Click the appropriate button to fix the issue

### Option 2: Manual Edit
Since we made invoice numbers editable for draft invoices:
1. Go to: `http://localhost/fit/public/invoices/241/edit`
2. Change the invoice number from FAC-2025-0187 to FAC-2025-0186
3. Save the invoice

### Option 3: Check and Diagnose
Run these diagnostic scripts:
- `http://localhost/fit/public/check_sequence_and_invoices.php` - Shows all invoices and sequences
- `http://localhost/fit/public/debug_invoice_241.php` - Shows details of invoice 241

## Understanding the Sequence System

The invoice numbering works like this:
1. **Sequence Table**: Stores the last used number (e.g., 185)
2. **New Invoice**: Gets sequence + 1 (e.g., 186)
3. **Existing Invoices**: Keep their numbers until manually changed

When you reset the sequence to 185:
- Existing invoices (like 0187) keep their numbers
- Only NEW invoices will get 0186, 0187, etc.

## Preventing Future Issues

1. **Always check** existing invoice numbers before resetting sequences
2. **For draft invoices**, you can now edit the invoice number directly
3. **For sent/paid invoices**, the number is locked and cannot be changed

## Next Steps

After fixing the invoice number:
1. The PDF will now show the lines correctly (with counters if descriptions are identical)
2. The edit form will warn you about duplicate line descriptions
3. Future invoices will follow the correct sequence