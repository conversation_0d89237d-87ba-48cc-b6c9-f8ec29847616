<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - {{ app_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        .stat-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,.08);
            transition: transform 0.3s;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-card .card-body {
            padding: 1.5rem;
        }
        .stat-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0;
        }
        .stat-card p {
            color: #6c757d;
            margin-bottom: 0;
        }
        .stat-card i {
            font-size: 3rem;
            opacity: 0.3;
            position: absolute;
            right: 1rem;
            top: 1rem;
        }
        .quick-actions .list-group-item {
            border: none;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            transition: all 0.3s;
        }
        .quick-actions .list-group-item:hover {
            background-color: #e9ecef;
            transform: translateX(5px);
        }
        .section-card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,.08);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ base_url }}/">
                <i class="bi bi-heart-pulse"></i> {{ app_name }}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="{{ base_url }}/">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ base_url }}/invoices">
                            <i class="bi bi-receipt"></i> Invoices
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ base_url }}/patients">
                            <i class="bi bi-people"></i> Patients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ base_url }}/clients">
                            <i class="bi bi-building"></i> Clients
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <span class="navbar-text text-white me-3">
                        <i class="bi bi-person-circle"></i> Welcome, {{ session.user_name }}!
                    </span>
                    <a class="nav-link text-white" href="{{ base_url }}/logout">
                        <i class="bi bi-box-arrow-right"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h2 mb-0">Dashboard</h1>
                <p class="text-muted">Welcome back, {{ session.user_name }}! Here's an overview of your system.</p>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row g-4 mb-4">
            <div class="col-md-3">
                <div class="card stat-card text-white bg-primary">
                    <div class="card-body position-relative">
                        <i class="bi bi-people-fill"></i>
                        <h3>{{ stats.total_clients|default(0) }}</h3>
                        <p>Total Clients</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-white bg-success">
                    <div class="card-body position-relative">
                        <i class="bi bi-check-circle-fill"></i>
                        <h3>{{ stats.active_clients|default(0) }}</h3>
                        <p>Active Clients</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-white bg-warning">
                    <div class="card-body position-relative">
                        <i class="bi bi-file-text-fill"></i>
                        <h3>{{ stats.total_invoices|default(0) }}</h3>
                        <p>Total Invoices</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card text-white bg-danger">
                    <div class="card-body position-relative">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <h3>{{ stats.unpaid_invoices|default(0) }}</h3>
                        <p>Unpaid Invoices</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Overview -->
        <div class="row g-4 mb-4">
            <div class="col-md-6">
                <div class="card stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="text-muted mb-1">Total Revenue</h5>
                                <h2 class="text-primary mb-0">{{ currency }}{{ stats.total_revenue|default(0)|number_format(2) }}</h2>
                            </div>
                            <i class="bi bi-cash-stack text-primary" style="font-size: 2.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card stat-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h5 class="text-muted mb-1">Outstanding Amount</h5>
                                <h2 class="text-danger mb-0">{{ currency }}{{ stats.outstanding_amount|default(0)|number_format(2) }}</h2>
                            </div>
                            <i class="bi bi-clock-history text-danger" style="font-size: 2.5rem;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions and Navigation -->
        <div class="row g-4">
            <div class="col-md-6">
                <div class="card section-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-lightning-fill"></i> Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group quick-actions">
                            <a href="{{ base_url }}/invoices/create" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-plus-circle text-primary"></i> Create New Invoice</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/patients/create" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-person-plus text-success"></i> Add New Patient</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/clients/create" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-building-add text-info"></i> Add New Client</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/retrocession" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-calculator text-warning"></i> Calculate Retrocession</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card section-card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-gear-fill"></i> System Management</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group quick-actions">
                            <a href="{{ base_url }}/invoices" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-file-earmark-text text-primary"></i> Manage Invoices</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/patients" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-people text-success"></i> Manage Patients</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/clients" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-building text-info"></i> Manage Clients</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/config" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-sliders text-secondary"></i> System Configuration</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                            <a href="{{ base_url }}/users" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                <span><i class="bi bi-person-gear text-dark"></i> User Management</span>
                                <i class="bi bi-arrow-right text-muted"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Info -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card section-card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <i class="bi bi-check-circle text-success" style="font-size: 2rem;"></i>
                                <p class="mb-0 mt-2">System Status: <strong class="text-success">Active</strong></p>
                            </div>
                            <div class="col-md-3">
                                <i class="bi bi-shield-check text-primary" style="font-size: 2rem;"></i>
                                <p class="mb-0 mt-2">Version: <strong>3.0</strong></p>
                            </div>
                            <div class="col-md-3">
                                <i class="bi bi-calendar3 text-info" style="font-size: 2rem;"></i>
                                <p class="mb-0 mt-2">Date: <strong>{{ "now"|date("d/m/Y") }}</strong></p>
                            </div>
                            <div class="col-md-3">
                                <i class="bi bi-clock text-warning" style="font-size: 2rem;"></i>
                                <p class="mb-0 mt-2">Time: <strong>{{ "now"|date("H:i") }}</strong></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>