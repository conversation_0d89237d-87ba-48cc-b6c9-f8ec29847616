<?php

namespace App\Services;

use App\Models\EmailTemplate;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Config;
use App\Core\Model;

class TemplateSelector
{
    /**
     * Selection criteria priority levels
     */
    const PRIORITY_CRITICAL = 1;
    const PRIORITY_HIGH = 2;
    const PRIORITY_MEDIUM = 3;
    const PRIORITY_LOW = 4;
    const PRIORITY_DEFAULT = 5;
    
    /**
     * Template type constants
     */
    const TYPE_INVOICE_NEW = 'invoice_new';
    const TYPE_INVOICE_REMINDER = 'invoice_reminder';
    const TYPE_INVOICE_OVERDUE = 'invoice_overdue';
    const TYPE_INVOICE_PARTIAL = 'invoice_partial';
    const TYPE_INVOICE_PAID = 'invoice_paid';
    const TYPE_INVOICE_CANCELLED = 'invoice_cancelled';
    const TYPE_APPOINTMENT_REMINDER = 'appointment_reminder';
    const TYPE_WELCOME = 'welcome';
    const TYPE_CUSTOM = 'custom';
    
    /**
     * Template selection rules cache
     */
    private static $rulesCache = [];
    
    /**
     * Selected template inheritance chain
     */
    private $templateChain = [];
    
    /**
     * Selection context data
     */
    private $context = [];
    
    /**
     * Selection metadata for debugging
     */
    private $selectionMetadata = [];
    
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->loadSelectionRules();
    }
    
    /**
     * Select the most appropriate email template based on context
     *
     * @param string $baseType Base template type
     * @param array $context Context data for selection
     * @return EmailTemplate|null
     */
    public function selectTemplate($baseType, array $context = [])
    {
        $this->context = $context;
        $this->selectionMetadata = [
            'base_type' => $baseType,
            'timestamp' => date('Y-m-d H:i:s'),
            'context' => $context
        ];
        
        // Extract key context elements
        $invoice = $context['invoice'] ?? null;
        $client = $context['client'] ?? null;
        $language = $context['language'] ?? $this->detectLanguage($client);
        $paymentStatus = $context['payment_status'] ?? $this->getPaymentStatus($invoice);
        $invoiceType = $invoice ? $invoice->type : null;
        
        // Build selection criteria
        $criteria = [
            'base_type' => $baseType,
            'invoice_type' => $invoiceType,
            'client_type' => $this->getClientType($client),
            'language' => $language,
            'payment_status' => $paymentStatus,
            'is_overdue' => $this->isOverdue($invoice),
            'days_overdue' => $this->getDaysOverdue($invoice),
            'is_practitioner' => $this->isPractitioner($client),
            'has_insurance' => $this->hasInsurance($client),
            'custom_conditions' => $context['custom_conditions'] ?? []
        ];
        
        $this->selectionMetadata['criteria'] = $criteria;
        
        // Apply selection rules in priority order
        $candidates = $this->findCandidateTemplates($criteria);
        
        if (empty($candidates)) {
            // Fall back to default template for base type
            $defaultTemplate = $this->getDefaultTemplate($baseType, $language);
            $this->selectionMetadata['fallback'] = true;
            $this->selectionMetadata['selected'] = $defaultTemplate ? $defaultTemplate->code : null;
            return $defaultTemplate;
        }
        
        // Score and rank candidates
        $scoredCandidates = $this->scoreCandidates($candidates, $criteria);
        
        // Select best match
        $selectedTemplate = $this->selectBestMatch($scoredCandidates);
        
        // Build template inheritance chain if needed
        if ($selectedTemplate && $selectedTemplate->parent_template_id) {
            $this->buildTemplateChain($selectedTemplate);
        }
        
        $this->selectionMetadata['selected'] = $selectedTemplate ? $selectedTemplate->code : null;
        $this->selectionMetadata['score'] = $scoredCandidates[0]['score'] ?? 0;
        
        return $selectedTemplate;
    }
    
    /**
     * Get template with inheritance support
     *
     * @param string $code Template code
     * @param array $context Optional context for dynamic selection
     * @return EmailTemplate|null
     */
    public function getTemplateWithInheritance($code, array $context = [])
    {
        $template = EmailTemplate::getByCode($code);
        
        if (!$template) {
            return null;
        }
        
        // Check if template has variants based on context
        if ($context) {
            $variant = $this->selectVariant($template, $context);
            if ($variant) {
                $template = $variant;
            }
        }
        
        // Build inheritance chain
        if ($template->parent_template_id) {
            $this->buildTemplateChain($template);
        }
        
        return $template;
    }
    
    /**
     * Parse template with inheritance and context
     *
     * @param EmailTemplate $template
     * @param array $variables
     * @return array
     */
    public function parseWithInheritance(EmailTemplate $template, array $variables = [])
    {
        $subject = $template->subject;
        $body = $template->body;
        
        // Apply inheritance chain
        foreach ($this->templateChain as $parentTemplate) {
            // Inherit missing parts from parent
            if (empty($subject) && !empty($parentTemplate->subject)) {
                $subject = $parentTemplate->subject;
            }
            
            // Apply parent template sections to body
            $body = $this->mergeTemplateBody($body, $parentTemplate->body);
        }
        
        // Parse variables
        foreach ($variables as $key => $value) {
            $subject = str_replace('{' . $key . '}', $value, $subject);
            $body = str_replace('{' . $key . '}', $value, $body);
        }
        
        // Apply conditional sections
        $body = $this->parseConditionalSections($body, $variables);
        
        return [
            'subject' => $subject,
            'body' => $body,
            'metadata' => $this->selectionMetadata
        ];
    }
    
    /**
     * Load selection rules from database or configuration
     */
    private function loadSelectionRules()
    {
        if (!empty(self::$rulesCache)) {
            return;
        }
        
        // Load from database if exists
        // Note: template_selection_rules table doesn't exist yet, so we'll use hardcoded rules
        // TODO: Create template_selection_rules table and model when needed
        /*
        $db = \App\Services\Database::getInstance()->getConnection();
        $stmt = $db->prepare("SELECT * FROM template_selection_rules WHERE is_active = 1 ORDER BY priority");
        $stmt->execute();
        $rules = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        foreach ($rules as $rule) {
            self::$rulesCache[] = [
                'conditions' => json_decode($rule['conditions'], true),
                'template_code' => $rule['template_code'],
                'priority' => $rule['priority']
            ];
        }
        */
        
        // Default rules if no database rules
        if (empty(self::$rulesCache)) {
            self::$rulesCache = $this->getDefaultRules();
        }
    }
    
    /**
     * Get default selection rules
     */
    private function getDefaultRules()
    {
        return [
            // Overdue invoice rules
            [
                'conditions' => [
                    'base_type' => self::TYPE_INVOICE_REMINDER,
                    'days_overdue' => ['>', 30]
                ],
                'template_code' => 'invoice_overdue_severe',
                'priority' => self::PRIORITY_CRITICAL
            ],
            [
                'conditions' => [
                    'base_type' => self::TYPE_INVOICE_REMINDER,
                    'days_overdue' => ['>', 14]
                ],
                'template_code' => 'invoice_overdue_moderate',
                'priority' => self::PRIORITY_HIGH
            ],
            
            // Invoice type specific rules
            [
                'conditions' => [
                    'invoice_type' => 'FAC-RET30',
                    'is_practitioner' => true
                ],
                'template_code' => 'retrocession_invoice',
                'priority' => self::PRIORITY_HIGH
            ],
            [
                'conditions' => [
                    'invoice_type' => 'FAC-LOC',
                    'base_type' => self::TYPE_INVOICE_NEW
                ],
                'template_code' => 'rental_invoice',
                'priority' => self::PRIORITY_HIGH
            ],
            
            // Client type rules
            [
                'conditions' => [
                    'client_type' => 'company',
                    'base_type' => self::TYPE_INVOICE_NEW
                ],
                'template_code' => 'invoice_company',
                'priority' => self::PRIORITY_MEDIUM
            ],
            [
                'conditions' => [
                    'has_insurance' => true,
                    'base_type' => self::TYPE_INVOICE_NEW
                ],
                'template_code' => 'invoice_with_insurance',
                'priority' => self::PRIORITY_MEDIUM
            ],
            
            // Language specific defaults
            [
                'conditions' => [
                    'language' => 'de'
                ],
                'template_code' => 'default_de',
                'priority' => self::PRIORITY_LOW
            ],
            [
                'conditions' => [
                    'language' => 'en'
                ],
                'template_code' => 'default_en',
                'priority' => self::PRIORITY_LOW
            ]
        ];
    }
    
    /**
     * Find candidate templates based on criteria
     */
    private function findCandidateTemplates(array $criteria)
    {
        $candidates = [];
        
        foreach (self::$rulesCache as $rule) {
            if ($this->matchesConditions($criteria, $rule['conditions'])) {
                $template = EmailTemplate::getByCode($rule['template_code']);
                if ($template) {
                    $candidates[] = [
                        'template' => $template,
                        'priority' => $rule['priority'],
                        'matched_conditions' => $rule['conditions']
                    ];
                }
            }
        }
        
        // Also check for direct template matches
        $directMatches = $this->findDirectMatches($criteria);
        $candidates = array_merge($candidates, $directMatches);
        
        return $candidates;
    }
    
    /**
     * Find direct template matches based on naming convention
     */
    private function findDirectMatches(array $criteria)
    {
        $matches = [];
        
        // Build potential template codes based on criteria
        $codes = [];
        
        // Type + invoice type + language
        if ($criteria['invoice_type'] && $criteria['language']) {
            $codes[] = strtolower($criteria['base_type'] . '_' . $criteria['invoice_type'] . '_' . $criteria['language']);
        }
        
        // Type + invoice type
        if ($criteria['invoice_type']) {
            $codes[] = strtolower($criteria['base_type'] . '_' . $criteria['invoice_type']);
        }
        
        // Type + client type + language
        if ($criteria['client_type'] && $criteria['language']) {
            $codes[] = strtolower($criteria['base_type'] . '_' . $criteria['client_type'] . '_' . $criteria['language']);
        }
        
        // Type + language
        if ($criteria['language']) {
            $codes[] = strtolower($criteria['base_type'] . '_' . $criteria['language']);
        }
        
        // Check each potential code
        foreach ($codes as $code) {
            $template = EmailTemplate::getByCode($code);
            if ($template) {
                $matches[] = [
                    'template' => $template,
                    'priority' => self::PRIORITY_MEDIUM,
                    'matched_conditions' => ['direct_match' => $code]
                ];
            }
        }
        
        return $matches;
    }
    
    /**
     * Check if criteria matches conditions
     */
    private function matchesConditions(array $criteria, array $conditions)
    {
        foreach ($conditions as $key => $condition) {
            if (!isset($criteria[$key])) {
                return false;
            }
            
            $value = $criteria[$key];
            
            // Handle different condition types
            if (is_array($condition) && count($condition) == 2) {
                // Comparison operator
                $operator = $condition[0];
                $compareValue = $condition[1];
                
                switch ($operator) {
                    case '>':
                        if (!($value > $compareValue)) return false;
                        break;
                    case '>=':
                        if (!($value >= $compareValue)) return false;
                        break;
                    case '<':
                        if (!($value < $compareValue)) return false;
                        break;
                    case '<=':
                        if (!($value <= $compareValue)) return false;
                        break;
                    case '!=':
                        if (!($value != $compareValue)) return false;
                        break;
                    case 'in':
                        if (!in_array($value, $compareValue)) return false;
                        break;
                }
            } else {
                // Direct comparison
                if ($value != $condition) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Score candidates based on match quality
     */
    private function scoreCandidates(array $candidates, array $criteria)
    {
        $scored = [];
        
        foreach ($candidates as $candidate) {
            $score = 0;
            
            // Base score from priority
            $score += (6 - $candidate['priority']) * 20;
            
            // Bonus for exact matches
            foreach ($candidate['matched_conditions'] as $key => $value) {
                if (isset($criteria[$key]) && $criteria[$key] === $value) {
                    $score += 10;
                }
            }
            
            // Bonus for template recency
            $template = $candidate['template'];
            $daysSinceUpdate = (time() - strtotime($template->updated_at)) / 86400;
            if ($daysSinceUpdate < 30) {
                $score += 5;
            }
            
            // Bonus for active templates with high usage
            if (isset($template->usage_count) && $template->usage_count > 100) {
                $score += 3;
            }
            
            $candidate['score'] = $score;
            $scored[] = $candidate;
        }
        
        // Sort by score descending
        usort($scored, function($a, $b) {
            return $b['score'] - $a['score'];
        });
        
        return $scored;
    }
    
    /**
     * Select best match from scored candidates
     */
    private function selectBestMatch(array $scoredCandidates)
    {
        if (empty($scoredCandidates)) {
            return null;
        }
        
        return $scoredCandidates[0]['template'];
    }
    
    /**
     * Build template inheritance chain
     */
    private function buildTemplateChain(EmailTemplate $template)
    {
        $this->templateChain = [];
        $currentTemplate = $template;
        $depth = 0;
        
        while ($currentTemplate->parent_template_id && $depth < 5) {
            $parent = EmailTemplate::find($currentTemplate->parent_template_id);
            if ($parent) {
                $this->templateChain[] = $parent;
                $currentTemplate = $parent;
            } else {
                break;
            }
            $depth++;
        }
    }
    
    /**
     * Merge template body with parent template
     */
    private function mergeTemplateBody($childBody, $parentBody)
    {
        // Look for inheritance markers
        if (strpos($childBody, '{{parent}}') !== false) {
            return str_replace('{{parent}}', $parentBody, $childBody);
        }
        
        // Look for section inheritance
        preg_match_all('/{{section:(\w+)}}(.*?){{\/section}}/s', $parentBody, $parentSections);
        
        foreach ($parentSections[1] as $index => $sectionName) {
            $sectionContent = $parentSections[2][$index];
            
            // Check if child overrides this section
            if (!preg_match('/{{section:' . $sectionName . '}}/', $childBody)) {
                // Append parent section to child
                $childBody .= "\n{{section:$sectionName}}" . $sectionContent . "{{/section}}";
            }
        }
        
        return $childBody;
    }
    
    /**
     * Parse conditional sections in template
     */
    private function parseConditionalSections($body, array $variables)
    {
        // Parse if conditions
        $body = preg_replace_callback(
            '/{{if\s+(\w+)}}(.*?){{\/if}}/s',
            function($matches) use ($variables) {
                $condition = $matches[1];
                $content = $matches[2];
                
                if (!empty($variables[$condition])) {
                    return $content;
                }
                return '';
            },
            $body
        );
        
        // Parse unless conditions
        $body = preg_replace_callback(
            '/{{unless\s+(\w+)}}(.*?){{\/unless}}/s',
            function($matches) use ($variables) {
                $condition = $matches[1];
                $content = $matches[2];
                
                if (empty($variables[$condition])) {
                    return $content;
                }
                return '';
            },
            $body
        );
        
        // Parse each loops
        $body = preg_replace_callback(
            '/{{each\s+(\w+)}}(.*?){{\/each}}/s',
            function($matches) use ($variables) {
                $collection = $matches[1];
                $template = $matches[2];
                
                if (!isset($variables[$collection]) || !is_array($variables[$collection])) {
                    return '';
                }
                
                $output = '';
                foreach ($variables[$collection] as $item) {
                    $itemOutput = $template;
                    foreach ($item as $key => $value) {
                        $itemOutput = str_replace('{' . $key . '}', $value, $itemOutput);
                    }
                    $output .= $itemOutput;
                }
                
                return $output;
            },
            $body
        );
        
        return $body;
    }
    
    /**
     * Get default template for base type and language
     */
    private function getDefaultTemplate($baseType, $language = 'fr')
    {
        // Try language-specific default
        $code = $baseType . '_' . $language;
        $template = EmailTemplate::getByCode($code);
        
        if ($template) {
            return $template;
        }
        
        // Try base type default
        return EmailTemplate::getByCode($baseType);
    }
    
    /**
     * Detect language from client or system settings
     */
    private function detectLanguage($client = null)
    {
        if ($client && isset($client->language)) {
            return $client->language;
        }
        
        // Get from system config
        $config = Config::getByKey('default_language');
        return $config ? $config->value : 'fr';
    }
    
    /**
     * Get payment status from invoice
     */
    private function getPaymentStatus($invoice = null)
    {
        if (!$invoice) {
            return 'unknown';
        }
        
        if ($invoice->paid_amount >= $invoice->total_amount) {
            return 'paid';
        } elseif ($invoice->paid_amount > 0) {
            return 'partial';
        } elseif ($this->isOverdue($invoice)) {
            return 'overdue';
        }
        
        return 'pending';
    }
    
    /**
     * Check if invoice is overdue
     */
    private function isOverdue($invoice = null)
    {
        if (!$invoice || !$invoice->due_date) {
            return false;
        }
        
        return strtotime($invoice->due_date) < time();
    }
    
    /**
     * Get days overdue
     */
    private function getDaysOverdue($invoice = null)
    {
        if (!$this->isOverdue($invoice)) {
            return 0;
        }
        
        $daysOverdue = (time() - strtotime($invoice->due_date)) / 86400;
        return (int) $daysOverdue;
    }
    
    /**
     * Get client type
     */
    private function getClientType($client = null)
    {
        if (!$client) {
            return 'unknown';
        }
        
        if (!empty($client->company_name)) {
            return 'company';
        } elseif ($this->isPractitioner($client)) {
            return 'practitioner';
        }
        
        return 'individual';
    }
    
    /**
     * Check if client is a practitioner
     */
    private function isPractitioner($client = null)
    {
        if (!$client) {
            return false;
        }
        
        // Check user relationship
        if (isset($client->user_id)) {
            $user = Model::query('users')->find($client->user_id);
            return $user && !empty($user->is_practitioner);
        }
        
        // Check client type field
        return isset($client->client_type) && $client->client_type === 'practitioner';
    }
    
    /**
     * Check if client has insurance
     */
    private function hasInsurance($client = null)
    {
        if (!$client) {
            return false;
        }
        
        return !empty($client->insurance_number) || !empty($client->insurance_company);
    }
    
    /**
     * Select template variant based on context
     */
    private function selectVariant(EmailTemplate $template, array $context)
    {
        // Look for variants of this template
        $variants = EmailTemplate::where('parent_template_id', $template->id)
            ->where('is_active', true)
            ->get();
        
        if ($variants->isEmpty()) {
            return null;
        }
        
        // Score variants based on context match
        $bestVariant = null;
        $bestScore = 0;
        
        foreach ($variants as $variant) {
            $score = $this->scoreVariant($variant, $context);
            if ($score > $bestScore) {
                $bestScore = $score;
                $bestVariant = $variant;
            }
        }
        
        return $bestVariant;
    }
    
    /**
     * Score a template variant
     */
    private function scoreVariant(EmailTemplate $variant, array $context)
    {
        $score = 0;
        
        // Check variant metadata
        $metadata = json_decode($variant->metadata, true) ?? [];
        
        foreach ($metadata as $key => $value) {
            if (isset($context[$key]) && $context[$key] == $value) {
                $score += 10;
            }
        }
        
        return $score;
    }
    
    /**
     * Get selection metadata for debugging
     */
    public function getSelectionMetadata()
    {
        return $this->selectionMetadata;
    }
    
    /**
     * Clear rules cache
     */
    public static function clearCache()
    {
        self::$rulesCache = [];
    }
}