# Diagnostic and Utility Scripts

This directory contains diagnostic and utility scripts for the Fit360 AdminDesk system.

## Invoice Generation Diagnostic Scripts

### test-user-invoice-generation.php
Comprehensive test script for all three invoice generation types (RET, LOY, LOC).

**Usage:** Navigate to `http://localhost/fit/public/test-user-invoice-generation.php`

**Features:**
- Tests retrocession invoice generation
- Tests loyer invoice generation  
- Tests location/course invoice generation
- Shows detailed error messages and success indicators
- Includes links to view generated invoices

### check-invoice-type-mapping.php
Diagnoses invoice type configuration issues.

**Usage:** Navigate to `http://localhost/fit/public/check-invoice-type-mapping.php`

**Shows:**
- All invoice types in the database
- Config mappings for invoice types
- Which codes the UnifiedInvoiceGenerator expects
- Existing invoices for the test period

### fix-all-invoice-patterns.php
Standardizes all invoice numbering patterns to the consistent format.

**Usage:** Navigate to `http://localhost/fit/public/fix-all-invoice-patterns.php`

**Fixes:**
- Updates all patterns to FAC-{PREFIX}-{YEAR}-{NUMBER:4}
- Corrects prefix values (e.g., FAC-LOY → LOY)
- Updates config mappings

### delete-test-invoices.php
Safely deletes test invoices for re-testing.

**Usage:** Navigate to `http://localhost/fit/public/delete-test-invoices.php`

**Features:**
- Shows all test invoices before deletion
- Requires confirmation
- Cleans up all related data (lines, items, user_generated_invoices)

### fix-invoice-type-mappings.php
Fixes configuration mappings for invoice types.

**Usage:** Navigate to `http://localhost/fit/public/fix-invoice-type-mappings.php`

**Fixes:**
- Updates RET mapping to correct code
- Shows all invoices for debugging
- Option to delete test invoices

### final-invoice-types-cleanup.php
Resolves duplicate invoice types with shorter codes.

**Usage:** Navigate to `http://localhost/fit/public/final-invoice-types-cleanup.php`

**Changes:**
- Updates to shorter codes: LOY, LOC, RET25, RET30
- Handles MySQL 4-character index limitation
- Updates config mappings

## Legacy Scripts

### update-loy-code.php
Updates invoice type codes for Loyer invoices.

### check-loyer-color.php
Checks and fixes color consistency for Loyer invoice types.

### fix-loyer-color.php
Standardizes Loyer invoice type colors.

## Usage Guidelines

1. **Always backup your database** before running any fix scripts
2. **Run diagnostic scripts first** to understand the current state
3. **Use fix scripts carefully** - they make permanent database changes
4. **Test thoroughly** after running fix scripts

## Common Issues and Solutions

### "Duplicate entry for key" errors
Run `final-invoice-types-cleanup.php` to resolve code conflicts.

### "Invalid invoice type" errors
Run `check-invoice-type-mapping.php` to diagnose, then `fix-invoice-type-mappings.php` to fix.

### "Invoice already exists" errors
Use `delete-test-invoices.php` to clean up test data.

### Invoice patterns inconsistent
Run `fix-all-invoice-patterns.php` to standardize.