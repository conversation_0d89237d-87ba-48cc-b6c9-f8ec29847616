# Phase 1 Testing Checklist - Health Center Billing System

## Week 1: Project Setup & Architecture Tests

### 1.1 Project Structure Tests

```bash
#!/bin/bash
# Test Script: verify-project-structure.sh

echo "=== Project Structure Verification ==="

# Test 1.1.1: Directory Structure
echo -n "Testing directory structure... "
DIRS=(
    "app/config" "app/controllers" "app/models" "app/services" 
    "app/middleware" "app/helpers" "app/views"
    "public/assets/css" "public/assets/js" "public/assets/img"
    "storage/logs" "storage/cache" "storage/sessions"
    "database/migrations" "database/seeds"
    "tests/unit" "tests/integration"
    "docs" "scripts" "lang"
)

MISSING_DIRS=0
for dir in "${DIRS[@]}"; do
    if [ ! -d "/mnt/c/wamp64/www/fit/$dir" ]; then
        echo "Missing: $dir"
        MISSING_DIRS=$((MISSING_DIRS + 1))
    fi
done

if [ $MISSING_DIRS -eq 0 ]; then
    echo "PASS ✓"
else
    echo "FAIL ✗ - $MISSING_DIRS directories missing"
fi

# Test 1.1.2: Git Repository
echo -n "Testing Git initialization... "
if [ -d "/mnt/c/wamp64/www/fit/.git" ]; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 1.1.5: Composer Validation
echo -n "Testing composer.json validity... "
cd /mnt/c/wamp64/www/fit
if composer validate --quiet; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 1.1.6: Flight PHP Installation
echo -n "Testing Flight PHP installation... "
if composer show mikecao/flight 2>/dev/null | grep -q "^versions"; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 1.1.10: Basic Route Test
echo -n "Testing basic route... "
RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost{{ base_url }}/)
if [ "$RESPONSE" = "200" ]; then
    echo "PASS ✓"
else
    echo "FAIL ✗ - HTTP $RESPONSE"
fi
```

**Expected Output:**
```
=== Project Structure Verification ===
Testing directory structure... PASS ✓
Testing Git initialization... PASS ✓
Testing composer.json validity... PASS ✓
Testing Flight PHP installation... PASS ✓
Testing basic route... PASS ✓
```

### 1.2 Database Connection Tests

```bash
#!/bin/bash
# Test Script: test-database.sh

echo "=== Database Connection Tests ==="

# Test 1.2.2: Database Exists
echo -n "Testing database existence... "
if mysql -u root -e "USE billing_system" 2>/dev/null; then
    echo "PASS ✓"
else
    echo "FAIL ✗ - Database not found"
fi

# Test 1.2.7: Connection Test
echo -n "Testing PHP database connection... "
php -r '
try {
    $pdo = new PDO("mysql:host=localhost;dbname=billing_system", "root", "");
    echo "PASS ✓\n";
} catch (Exception $e) {
    echo "FAIL ✗ - " . $e->getMessage() . "\n";
}
'

# Test 1.2.8: Migration System
echo -n "Testing migration system... "
if [ -f "/mnt/c/wamp64/www/fit/scripts/migrate.php" ]; then
    php /mnt/c/wamp64/www/fit/scripts/migrate.php --status > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "PASS ✓"
    else
        echo "FAIL ✗ - Migration script error"
    fi
else
    echo "FAIL ✗ - Migration script not found"
fi
```

### 1.3 Routing & Middleware Tests

```php
<?php
// Test Script: test-routing.php

echo "=== Routing System Tests ===\n";

require_once __DIR__ . '/../vendor/autoload.php';

// Test 1.3.1: Routes Configuration
echo "Testing routes configuration... ";
if (file_exists(__DIR__ . '/../app/config/routes.php')) {
    require_once __DIR__ . '/../app/config/routes.php';
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}

// Test 1.3.3: Base Controller
echo "Testing BaseController existence... ";
if (class_exists('App\Controllers\BaseController')) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}

// Test 1.3.7: CORS Headers
echo "Testing CORS middleware... ";
$ch = curl_init('http://localhost{{ base_url }}/test');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'OPTIONS');
$response = curl_exec($ch);
$headers = curl_getinfo($ch);
curl_close($ch);

if (strpos($response, 'Access-Control-Allow-Origin') !== false) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}
```

### 1.4 Error Handling & Logging Tests

```php
<?php
// Test Script: test-error-handling.php

echo "=== Error Handling Tests ===\n";

// Test 1.4.3: Logging Service
echo "Testing logging service... ";
$testLog = __DIR__ . '/../storage/logs/test-' . date('Y-m-d') . '.log';
$logger = new \App\Services\Logger();
$logger->info('Test log entry');

if (file_exists($testLog) && strpos(file_get_contents($testLog), 'Test log entry') !== false) {
    echo "PASS ✓\n";
    unlink($testLog);
} else {
    echo "FAIL ✗\n";
}

// Test 1.4.5: Debug Mode
echo "Testing debug mode toggle... ";
$_ENV['APP_DEBUG'] = 'true';
if (getenv('APP_DEBUG') === 'true') {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}
```

### 1.5 Development Tools Tests

```bash
#!/bin/bash
# Test Script: test-dev-tools.sh

echo "=== Development Tools Tests ==="

# Test 1.5.1: PHPUnit Installation
echo -n "Testing PHPUnit installation... "
if vendor/bin/phpunit --version > /dev/null 2>&1; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 1.5.4: Run Unit Tests
echo -n "Testing PHPUnit execution... "
if vendor/bin/phpunit tests/unit/ExampleTest.php --quiet; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 1.5.5: PHP CodeSniffer
echo -n "Testing PHP CodeSniffer... "
if vendor/bin/phpcs --version > /dev/null 2>&1; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 1.5.7: Composer Scripts
echo -n "Testing composer scripts... "
if composer run-script test > /dev/null 2>&1; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi
```

## Week 2: Configuration Engine Tests

### 2.1 Configuration Schema Tests

```sql
-- Test Script: test-config-schema.sql
-- Run with: mysql -u root billing_system < test-config-schema.sql

-- Test 2.1.1: Config Categories Table
SELECT 'Testing config_categories table...' as Test;
SELECT COUNT(*) as 'Table Exists' FROM information_schema.tables 
WHERE table_schema = 'billing_system' AND table_name = 'config_categories';

-- Test 2.1.2: Config Items Table
SELECT 'Testing config_items table...' as Test;
DESCRIBE config_items;

-- Test 2.1.3: Config Values Table
SELECT 'Testing config_values table...' as Test;
DESCRIBE config_values;

-- Test 2.1.4: Config History Table
SELECT 'Testing config_history table...' as Test;
SELECT COUNT(*) as 'History Table' FROM information_schema.tables 
WHERE table_schema = 'billing_system' AND table_name = 'config_history';

-- Test 2.1.7: Initial Configuration Data
SELECT 'Testing initial configuration...' as Test;
SELECT COUNT(*) as 'Config Items Count' FROM config_items;
```

### 2.2 Configuration Service Tests

```php
<?php
// Test Script: test-config-service.php

use PHPUnit\Framework\TestCase;

class ConfigurationServiceTest extends TestCase
{
    private $configService;
    
    protected function setUp(): void
    {
        $this->configService = new \App\Services\ConfigurationService();
    }
    
    // Test 2.2.2: CRUD Operations
    public function testCreateConfiguration()
    {
        $result = $this->configService->create([
            'key' => 'test.item',
            'value' => 'test value',
            'type' => 'string'
        ]);
        
        $this->assertNotNull($result);
        $this->assertEquals('test value', $result->value);
    }
    
    // Test 2.2.3: Type Handling
    public function testTypeConversion()
    {
        $tests = [
            ['value' => '123', 'type' => 'integer', 'expected' => 123],
            ['value' => 'true', 'type' => 'boolean', 'expected' => true],
            ['value' => '12.34', 'type' => 'float', 'expected' => 12.34],
            ['value' => '["a","b"]', 'type' => 'array', 'expected' => ['a', 'b']]
        ];
        
        foreach ($tests as $test) {
            $result = $this->configService->castValue($test['value'], $test['type']);
            $this->assertEquals($test['expected'], $result);
        }
    }
    
    // Test 2.2.4: Cache Performance
    public function testCachePerformance()
    {
        $start = microtime(true);
        $this->configService->get('app.name');
        $firstCall = microtime(true) - $start;
        
        $start = microtime(true);
        $this->configService->get('app.name');
        $secondCall = microtime(true) - $start;
        
        $this->assertLessThan($firstCall, $secondCall);
    }
    
    // Test 2.2.8: Helper Function
    public function testConfigHelper()
    {
        $value = config('app.name');
        $this->assertNotNull($value);
    }
}

// Run the tests
echo "=== Configuration Service Tests ===\n";
$tester = new ConfigurationServiceTest();
$tester->setUp();

echo "Testing CRUD operations... ";
try {
    $tester->testCreateConfiguration();
    echo "PASS ✓\n";
} catch (Exception $e) {
    echo "FAIL ✗ - " . $e->getMessage() . "\n";
}

echo "Testing type conversion... ";
try {
    $tester->testTypeConversion();
    echo "PASS ✓\n";
} catch (Exception $e) {
    echo "FAIL ✗\n";
}
```

### 2.3 Configuration UI Tests

```javascript
// Test Script: test-config-ui.js
// Run with: node test-config-ui.js (requires puppeteer)

const puppeteer = require('puppeteer');

(async () => {
    console.log('=== Configuration UI Tests ===');
    
    const browser = await puppeteer.launch();
    const page = await browser.newPage();
    
    // Test 2.3.2: Configuration List View
    console.log('Testing configuration list view... ');
    try {
        await page.goto('http://localhost{{ base_url }}/admin/config');
        await page.waitForSelector('.config-list', { timeout: 5000 });
        console.log('PASS ✓');
    } catch (e) {
        console.log('FAIL ✗');
    }
    
    // Test 2.3.3: Search Functionality
    console.log('Testing search/filter... ');
    try {
        await page.type('#config-search', 'app');
        await page.waitForFunction(
            () => document.querySelectorAll('.config-item:not(.hidden)').length > 0
        );
        console.log('PASS ✓');
    } catch (e) {
        console.log('FAIL ✗');
    }
    
    // Test 2.3.5: AJAX Save
    console.log('Testing AJAX save... ');
    try {
        await page.click('.config-edit-btn');
        await page.type('.config-value-input', 'New Value');
        await page.click('.save-config-btn');
        await page.waitForSelector('.success-notification');
        console.log('PASS ✓');
    } catch (e) {
        console.log('FAIL ✗');
    }
    
    await browser.close();
})();
```

## Week 3: Authentication Tests

### 3.1 Authentication Core Tests

```php
<?php
// Test Script: test-auth-core.php

echo "=== Authentication Core Tests ===\n";

// Test 3.1.2: JWT Token Generation
echo "Testing JWT token generation... ";
$jwtService = new \App\Services\JWTService();
$token = $jwtService->generateToken(['user_id' => 1]);
if (strlen($token) > 50 && substr_count($token, '.') === 2) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}

// Test 3.1.2: JWT Token Validation
echo "Testing JWT token validation... ";
$payload = $jwtService->validateToken($token);
if ($payload && $payload->user_id === 1) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}

// Test 3.1.5: Password Hashing
echo "Testing password hashing... ";
$authService = new \App\Services\AuthService();
$hash = $authService->hashPassword('testpassword');
if (password_verify('testpassword', $hash)) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}

// Test 3.1.7: Rate Limiting
echo "Testing rate limiting... ";
$limiter = new \App\Services\RateLimiter();
$ip = '127.0.0.1';
for ($i = 0; $i < 6; $i++) {
    $allowed = $limiter->checkLoginAttempt($ip);
}
if (!$allowed) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}
```

### 3.2 User Management Tests

```bash
#!/bin/bash
# Test Script: test-user-management.sh

echo "=== User Management Tests ==="

# Test 3.2.1: Users Table
echo -n "Testing users table creation... "
RESULT=$(mysql -u root billing_system -e "SHOW TABLES LIKE 'users'" 2>/dev/null | grep users)
if [ -n "$RESULT" ]; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 3.2.3: User CRUD
echo -n "Testing user creation... "
php -r '
require_once "vendor/autoload.php";
$user = new \App\Models\User();
$user->username = "testuser";
$user->email = "<EMAIL>";
$user->password = password_hash("test123", PASSWORD_DEFAULT);
if ($user->save()) {
    echo "PASS ✓\n";
    $user->delete();
} else {
    echo "FAIL ✗\n";
}
'

# Test 3.2.5: User-Group Assignment
echo -n "Testing user-group relations... "
php -r '
require_once "vendor/autoload.php";
$userService = new \App\Services\UserService();
$result = $userService->assignToGroup(1, 1);
if ($result) {
    echo "PASS ✓\n";
} else {
    echo "FAIL ✗\n";
}
'
```

### 3.3 Permission System Tests

```php
<?php
// Test Script: test-permissions.php

class PermissionTest
{
    private $permissionService;
    
    public function __construct()
    {
        $this->permissionService = new \App\Services\PermissionService();
    }
    
    public function runTests()
    {
        echo "=== Permission System Tests ===\n";
        
        // Test 3.3.3: Permission Checking
        echo "Testing permission checking... ";
        $user = ['id' => 1, 'groups' => [1, 2]];
        if ($this->permissionService->userHasPermission($user, 'admin.access')) {
            echo "PASS ✓\n";
        } else {
            echo "FAIL ✗\n";
        }
        
        // Test 3.3.4: Role Inheritance
        echo "Testing role inheritance... ";
        $this->permissionService->setGroupParent(2, 1); // Group 2 inherits from Group 1
        $permissions = $this->permissionService->getGroupPermissions(2);
        if (count($permissions) > 0) {
            echo "PASS ✓\n";
        } else {
            echo "FAIL ✗\n";
        }
        
        // Test 3.3.6: Dynamic Permissions
        echo "Testing dynamic permission creation... ";
        $result = $this->permissionService->createPermission('test.dynamic', 'Test Dynamic Permission');
        if ($result) {
            echo "PASS ✓\n";
        } else {
            echo "FAIL ✗\n";
        }
        
        // Test 3.3.8: Permission Helpers
        echo "Testing permission helper... ";
        if (function_exists('can') && can('admin.access')) {
            echo "PASS ✓\n";
        } else {
            echo "FAIL ✗\n";
        }
    }
}

$tester = new PermissionTest();
$tester->runTests();
```

### 3.4 Login UI Tests

```bash
#!/bin/bash
# Test Script: test-login-ui.sh

echo "=== Login UI Tests ==="

# Test 3.4.1: Login Page Accessibility
echo -n "Testing login page accessibility... "
STATUS=$(curl -s -o /dev/null -w "%{http_code}" http://localhost{{ base_url }}/login)
if [ "$STATUS" = "200" ]; then
    echo "PASS ✓"
else
    echo "FAIL ✗ - HTTP $STATUS"
fi

# Test 3.4.2: Login Form Submission
echo -n "Testing login form submission... "
RESPONSE=$(curl -s -X POST http://localhost{{ base_url }}/login \
    -d "username=admin&password=admin123" \
    -w "%{http_code}")
if [[ "$RESPONSE" =~ 302|200 ]]; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 3.4.4: Logout Functionality
echo -n "Testing logout functionality... "
RESPONSE=$(curl -s -X POST http://localhost{{ base_url }}/logout \
    -w "%{http_code}")
if [[ "$RESPONSE" =~ 302 ]]; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi

# Test 3.4.7: Session Timeout
echo -n "Testing session timeout configuration... "
if grep -q "SESSION_LIFETIME" /mnt/c/wamp64/www/fit/.env; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
fi
```

## Master Test Runner

```bash
#!/bin/bash
# Master Test Script: run-all-phase1-tests.sh

echo "========================================="
echo "   PHASE 1 COMPLETE TEST SUITE"
echo "========================================="
echo ""

# Set test directory
TEST_DIR="/mnt/c/wamp64/www/fit/tests"

# Run all test scripts
TESTS=(
    "verify-project-structure.sh"
    "test-database.sh"
    "test-routing.php"
    "test-error-handling.php"
    "test-dev-tools.sh"
    "test-config-service.php"
    "test-auth-core.php"
    "test-user-management.sh"
    "test-permissions.php"
    "test-login-ui.sh"
)

TOTAL_TESTS=0
PASSED_TESTS=0

for test in "${TESTS[@]}"; do
    echo "Running $test..."
    echo "----------------------------------------"
    
    if [ -f "$TEST_DIR/$test" ]; then
        if [[ "$test" == *.sh ]]; then
            bash "$TEST_DIR/$test"
        elif [[ "$test" == *.php ]]; then
            php "$TEST_DIR/$test"
        fi
        
        # Simple pass/fail detection (customize based on actual output)
        if [ $? -eq 0 ]; then
            PASSED_TESTS=$((PASSED_TESTS + 1))
        fi
    else
        echo "Test file not found: $test"
    fi
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    echo ""
done

echo "========================================="
echo "TEST SUMMARY"
echo "========================================="
echo "Total Tests: $TOTAL_TESTS"
echo "Passed: $PASSED_TESTS"
echo "Failed: $((TOTAL_TESTS - PASSED_TESTS))"
echo "Success Rate: $(( (PASSED_TESTS * 100) / TOTAL_TESTS ))%"
echo "========================================="

# Exit with error if any tests failed
if [ $PASSED_TESTS -ne $TOTAL_TESTS ]; then
    exit 1
fi
```

## Automated Test Creation Script

```bash
#!/bin/bash
# Script: create-test-for-task.sh
# Usage: ./create-test-for-task.sh <task_id>

TASK_ID=$1
TEST_FILE="test-task-${TASK_ID}.sh"

cat > "$TEST_FILE" << 'EOF'
#!/bin/bash
# Auto-generated test for Task ${TASK_ID}

echo "=== Testing Task ${TASK_ID} ==="

# Add your test logic here
TEST_PASSED=true

# Example test structure
echo -n "Testing component existence... "
if [ -f "/path/to/component" ]; then
    echo "PASS ✓"
else
    echo "FAIL ✗"
    TEST_PASSED=false
fi

# Exit with appropriate code
if [ "$TEST_PASSED" = true ]; then
    exit 0
else
    exit 1
fi
EOF

chmod +x "$TEST_FILE"
echo "Created test file: $TEST_FILE"
```

## Pass/Fail Criteria Summary

### Critical Tests (Must Pass)
1. **Database Connection** - System cannot function without database
2. **Flight PHP Installation** - Core framework must be present
3. **Basic Routing** - Application must respond to requests
4. **Authentication** - Security is paramount
5. **Configuration Loading** - System depends on configuration

### Important Tests (Should Pass)
1. **Directory Structure** - Affects development workflow
2. **Composer Validation** - Package management must work
3. **Logging System** - Needed for debugging
4. **Permission System** - Access control required
5. **UI Accessibility** - Users must access the system

### Nice-to-Have Tests (Can Fail Initially)
1. **Code Style** - Can be fixed later
2. **Performance Benchmarks** - Can be optimized
3. **Advanced Features** - Can be implemented incrementally
4. **Browser Automation** - Manual testing acceptable initially