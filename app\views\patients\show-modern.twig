{% extends "base-modern.twig" %}

{% block title %}{{ patient.first_name }} {{ patient.last_name }} - {{ __('patients.details') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ patient.first_name }} {{ patient.last_name }}</h1>
            <p class="text-muted mb-0">{{ __('patients.patient_number') }}: {{ patient.patient_number }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/patients/{{ patient.id }}/edit" class="btn btn-primary">
                <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
            </a>
            <a href="{{ base_url }}/patients" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
            </a>
        </div>
    </div>

    <!-- Patient Status Alert -->
    {% if not patient.is_active %}
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i>
        {{ __('patients.inactive_patient_notice') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
    {% endif %}

    <div class="row">
        <!-- Main Information -->
        <div class="col-lg-8">
            <!-- Basic Information Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-person-fill me-2"></i>{{ __('common.basic_information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">{{ __('patients.title') }}:</dt>
                                <dd class="col-sm-8">{{ patient.title|default('-') }}</dd>
                                
                                <dt class="col-sm-4">{{ __('common.first_name') }}:</dt>
                                <dd class="col-sm-8">{{ patient.first_name }}</dd>
                                
                                <dt class="col-sm-4">{{ __('common.last_name') }}:</dt>
                                <dd class="col-sm-8">{{ patient.last_name }}</dd>
                                
                                <dt class="col-sm-4">{{ __('patients.gender') }}:</dt>
                                <dd class="col-sm-8">
                                    {% if patient.gender == 'M' %}
                                        <i class="bi bi-gender-male text-primary"></i> {{ __('common.male') }}
                                    {% else %}
                                        <i class="bi bi-gender-female text-danger"></i> {{ __('common.female') }}
                                    {% endif %}
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">{{ __('common.birth_date') }}:</dt>
                                <dd class="col-sm-8">
                                    {{ patient.birth_date|date('d/m/Y') }}
                                    <span class="text-muted">({{ patient.age }} {{ __('common.years') }})</span>
                                </dd>
                                
                                <dt class="col-sm-4">{{ __('patients.social_security_number') }}:</dt>
                                <dd class="col-sm-8">{{ patient.social_security_number|default('-') }}</dd>
                                
                                <dt class="col-sm-4">{{ __('common.blood_type') }}:</dt>
                                <dd class="col-sm-8">
                                    {% if patient.blood_type %}
                                        <span class="badge bg-danger">{{ patient.blood_type }}</span>
                                    {% else %}
                                        <span class="text-muted">{{ __('common.unknown') }}</span>
                                    {% endif %}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-telephone-fill me-2"></i>{{ __('patients.contact_information') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">{{ __('common.phone') }}:</dt>
                                <dd class="col-sm-8">
                                    {% if patient.phone %}
                                        <a href="tel:{{ patient.phone }}">{{ patient.phone }}</a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">{{ __('patients.mobile') }}:</dt>
                                <dd class="col-sm-8">
                                    {% if patient.mobile %}
                                        <a href="tel:{{ patient.mobile }}">{{ patient.mobile }}</a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </dd>
                                
                                <dt class="col-sm-4">{{ __('common.email') }}:</dt>
                                <dd class="col-sm-8">
                                    {% if patient.email %}
                                        <a href="mailto:{{ patient.email }}">{{ patient.email }}</a>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">{{ __('common.address') }}:</dt>
                                <dd class="col-sm-8">
                                    {% if patient.address %}
                                        {{ patient.address }}<br>
                                        {{ patient.postal_code }} {{ patient.city }}<br>
                                        {{ patient.country }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Medical Information Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-heart-pulse-fill me-2"></i>{{ __('patients.medical_information') }}</h5>
                </div>
                <div class="card-body">
                    {% if patient.allergies %}
                    <div class="alert alert-danger" role="alert">
                        <h6 class="alert-heading"><i class="bi bi-exclamation-triangle-fill me-2"></i>{{ __('common.allergies') }}</h6>
                        <p class="mb-0">{{ patient.allergies }}</p>
                    </div>
                    {% endif %}
                    
                    <h6 class="mb-3">{{ __('patients.medical_history') }}</h6>
                    <p>{{ patient.medical_history|default(__('common.no_information')) }}</p>
                    
                    <h6 class="mb-3">{{ __('common.notes') }}</h6>
                    <p>{{ patient.notes|default(__('common.no_notes')) }}</p>
                </div>
            </div>

            <!-- Recent Invoices -->
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-receipt me-2"></i>{{ __('patients.recent_invoices') }}</h5>
                </div>
                <div class="card-body">
                    {% if recent_invoices %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ __('invoices.invoice_number') }}</th>
                                        <th>{{ __('invoices.issue_date') }}</th>
                                        <th>{{ __('invoices.amount') }}</th>
                                        <th>{{ __('common.status') }}</th>
                                        <th>{{ __('common.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for invoice in recent_invoices %}
                                    <tr>
                                        <td>{{ invoice.invoice_number }}</td>
                                        <td>{{ invoice.issue_date|date('d/m/Y') }}</td>
                                        <td>{{ invoice.total_amount|number_format(2, ',', ' ') }} €</td>
                                        <td>
                                            {% if invoice.status == 'paid' %}
                                                <span class="badge bg-success">{{ __('invoices.status.paid') }}</span>
                                            {% elseif invoice.status == 'overdue' %}
                                                <span class="badge bg-danger">{{ __('invoices.status.overdue') }}</span>
                                            {% else %}
                                                <span class="badge bg-warning">{{ __('invoices.status.unpaid') }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ base_url }}/invoices?patient_id={{ patient.id }}" class="btn btn-sm btn-outline-primary">
                                {{ __('patients.view_all_invoices') }} <i class="bi bi-arrow-right"></i>
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center mb-0">{{ __('patients.no_invoices_yet') }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Side Information -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-lightning-fill me-2"></i>{{ __('common.quick_actions') }}</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ base_url }}/invoices/create?patient_id={{ patient.id }}" class="btn btn-success">
                            <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
                        </a>
                        <a href="{{ base_url }}/appointments/create?patient_id={{ patient.id }}" class="btn btn-info">
                            <i class="bi bi-calendar-plus me-2"></i>{{ __('appointments.schedule') }}
                        </a>
                        <a href="{{ base_url }}/patients/{{ patient.id }}/history" class="btn btn-outline-primary">
                            <i class="bi bi-clock-history me-2"></i>{{ __('patients.view_history') }}
                        </a>
                        <button type="button" class="btn btn-outline-secondary" onclick="window.print()">
                            <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Visit Statistics -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>{{ __('patients.visit_statistics') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-7">{{ __('patients.total_visits') }}:</dt>
                        <dd class="col-5 text-end">{{ statistics.total_visits|default(0) }}</dd>
                        
                        <dt class="col-7">{{ __('patients.last_visit') }}:</dt>
                        <dd class="col-5 text-end">
                            {% if statistics.last_visit %}
                                {{ statistics.last_visit|date('d/m/Y') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </dd>
                        
                        <dt class="col-7">{{ __('patients.next_appointment') }}:</dt>
                        <dd class="col-5 text-end">
                            {% if statistics.next_appointment %}
                                {{ statistics.next_appointment|date('d/m/Y') }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>

            <!-- System Information -->
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('common.system_info') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0 small">
                        <dt class="col-6">{{ __('common.created_at') }}:</dt>
                        <dd class="col-6 text-end">{{ patient.created_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-6">{{ __('common.updated_at') }}:</dt>
                        <dd class="col-6 text-end">{{ patient.updated_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-6">{{ __('common.created_by') }}:</dt>
                        <dd class="col-6 text-end">{{ patient.created_by_name|default('System') }}</dd>
                    </dl>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}