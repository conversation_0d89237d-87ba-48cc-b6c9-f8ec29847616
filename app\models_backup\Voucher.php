<?php

namespace App\Models;

use Flight;
use PDO;
use Exception;

class Voucher extends \App\Core\Model
{
    protected $table = 'vouchers';
    
    // Voucher types
    const TYPE_AMOUNT = 'amount';
    const TYPE_SERVICE = 'service';
    const TYPE_PERCENTAGE = 'percentage';
    
    // Voucher statuses
    const STATUS_ACTIVE = 'active';
    const STATUS_PARTIALLY_USED = 'partially_used';
    const STATUS_FULLY_USED = 'fully_used';
    const STATUS_EXPIRED = 'expired';
    const STATUS_CANCELLED = 'cancelled';
    
    /**
     * Generate voucher number
     */
    public function generateVoucherNumber()
    {
        $db = Flight::db();
        
        // Get format from config
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'voucher_number_format'");
        $stmt->execute();
        $format = $stmt->fetch(PDO::FETCH_COLUMN) ?: 'VCH-{YEAR}-{NUMBER:4}';
        
        // Get next number
        $year = date('Y');
        $stmt = $db->prepare("
            SELECT COUNT(*) + 1 as next_number 
            FROM vouchers 
            WHERE YEAR(issue_date) = ?
        ");
        $stmt->execute([$year]);
        $nextNumber = $stmt->fetch(PDO::FETCH_COLUMN);
        
        // Format voucher number
        $number = str_replace('{YEAR}', $year, $format);
        $number = preg_replace_callback('/\{NUMBER:(\d+)\}/', function($matches) use ($nextNumber) {
            return str_pad($nextNumber, $matches[1], '0', STR_PAD_LEFT);
        }, $number);
        $number = str_replace('{NUMBER}', $nextNumber, $number);
        
        return $number;
    }
    
    /**
     * Create voucher
     */
    public function createVoucher($data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Generate voucher number
            if (empty($data['voucher_number'])) {
                $data['voucher_number'] = $this->generateVoucherNumber();
            }
            
            // Set default expiry date if not provided
            if (empty($data['expiry_date'])) {
                $validityDays = $this->getConfig('voucher_validity_days', 365);
                $data['expiry_date'] = date('Y-m-d', strtotime("+$validityDays days"));
            }
            
            // Insert voucher
            $stmt = $db->prepare("
                INSERT INTO vouchers (
                    voucher_number, voucher_type, client_id,
                    beneficiary_name, beneficiary_email,
                    issue_date, expiry_date, amount, remaining_amount,
                    percentage_value, service_id, status,
                    purchase_invoice_id, notes, created_by
                ) VALUES (
                    :voucher_number, :voucher_type, :client_id,
                    :beneficiary_name, :beneficiary_email,
                    :issue_date, :expiry_date, :amount, :remaining_amount,
                    :percentage_value, :service_id, :status,
                    :purchase_invoice_id, :notes, :created_by
                )
            ");
            
            $stmt->execute([
                ':voucher_number' => $data['voucher_number'],
                ':voucher_type' => $data['voucher_type'],
                ':client_id' => $data['client_id'],
                ':beneficiary_name' => $data['beneficiary_name'] ?? null,
                ':beneficiary_email' => $data['beneficiary_email'] ?? null,
                ':issue_date' => $data['issue_date'] ?? date('Y-m-d'),
                ':expiry_date' => $data['expiry_date'],
                ':amount' => $data['amount'],
                ':remaining_amount' => $data['amount'],
                ':percentage_value' => $data['percentage_value'] ?? null,
                ':service_id' => $data['service_id'] ?? null,
                ':status' => self::STATUS_ACTIVE,
                ':purchase_invoice_id' => $data['purchase_invoice_id'] ?? null,
                ':notes' => $data['notes'] ?? null,
                ':created_by' => $data['created_by'] ?? $_SESSION['user_id'] ?? 1
            ]);
            
            $voucherId = $db->lastInsertId();
            
            // Send voucher email if enabled
            if ($this->getConfig('voucher_email_on_purchase', 'true') === 'true') {
                $this->sendVoucherEmail($voucherId, 'created');
            }
            
            $db->commit();
            
            return $this->getById($voucherId);
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Use voucher
     */
    public function useVoucher($voucherNumber, $amount, $invoiceId)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get voucher
            $voucher = $this->getByVoucherNumber($voucherNumber);
            
            if (!$voucher) {
                throw new Exception('Voucher not found');
            }
            
            // Validate voucher
            $this->validateVoucher($voucher);
            
            // Check sufficient balance
            if ($amount > $voucher['remaining_amount']) {
                throw new Exception('Insufficient voucher balance');
            }
            
            // Record usage
            $stmt = $db->prepare("
                INSERT INTO voucher_usage (
                    voucher_id, invoice_id, amount_used,
                    usage_date, remaining_after, processed_by
                ) VALUES (
                    :voucher_id, :invoice_id, :amount_used,
                    NOW(), :remaining_after, :processed_by
                )
            ");
            
            $remainingAfter = $voucher['remaining_amount'] - $amount;
            
            $stmt->execute([
                ':voucher_id' => $voucher['id'],
                ':invoice_id' => $invoiceId,
                ':amount_used' => $amount,
                ':remaining_after' => $remainingAfter,
                ':processed_by' => $_SESSION['user_id'] ?? 1
            ]);
            
            // Update voucher
            $newStatus = $remainingAfter == 0 ? self::STATUS_FULLY_USED : self::STATUS_PARTIALLY_USED;
            
            $stmt = $db->prepare("
                UPDATE vouchers 
                SET remaining_amount = :remaining_amount,
                    status = :status,
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $stmt->execute([
                ':remaining_amount' => $remainingAfter,
                ':status' => $newStatus,
                ':id' => $voucher['id']
            ]);
            
            $db->commit();
            
            return [
                'success' => true,
                'amount_used' => $amount,
                'remaining_balance' => $remainingAfter
            ];
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Validate voucher
     */
    public function validateVoucher($voucher)
    {
        // Check if active
        if (!in_array($voucher['status'], [self::STATUS_ACTIVE, self::STATUS_PARTIALLY_USED])) {
            throw new Exception('Voucher is not active');
        }
        
        // Check expiry
        if (!empty($voucher['expiry_date']) && strtotime($voucher['expiry_date']) < time()) {
            // Update status
            $this->updateStatus($voucher['id'], self::STATUS_EXPIRED);
            throw new Exception('Voucher has expired');
        }
        
        // Check balance
        if ($voucher['remaining_amount'] <= 0) {
            throw new Exception('Voucher has no remaining balance');
        }
        
        return true;
    }
    
    /**
     * Get voucher by number
     */
    public function getByVoucherNumber($voucherNumber)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT v.*, c.name as client_name
            FROM vouchers v
            LEFT JOIN clients c ON v.client_id = c.id
            WHERE v.voucher_number = ?
        ");
        
        $stmt->execute([$voucherNumber]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get voucher usage history
     */
    public function getUsageHistory($voucherId)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT vu.*, i.invoice_number, c.name as client_name
            FROM voucher_usage vu
            JOIN invoices i ON vu.invoice_id = i.id
            JOIN clients c ON i.client_id = c.id
            WHERE vu.voucher_id = ?
            ORDER BY vu.usage_date DESC
        ");
        
        $stmt->execute([$voucherId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check and update expired vouchers
     */
    public function updateExpiredVouchers()
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE vouchers 
            SET status = :expired_status,
                updated_at = NOW()
            WHERE status IN (:active_status, :partial_status)
            AND expiry_date < CURDATE()
        ");
        
        $stmt->execute([
            ':expired_status' => self::STATUS_EXPIRED,
            ':active_status' => self::STATUS_ACTIVE,
            ':partial_status' => self::STATUS_PARTIALLY_USED
        ]);
        
        return $stmt->rowCount();
    }
    
    /**
     * Get expiring vouchers
     */
    public function getExpiringVouchers($days = 30)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT v.*, c.name as client_name, c.email as client_email
            FROM vouchers v
            JOIN clients c ON v.client_id = c.id
            WHERE v.status IN (:active_status, :partial_status)
            AND v.expiry_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL :days DAY)
            ORDER BY v.expiry_date
        ");
        
        $stmt->execute([
            ':active_status' => self::STATUS_ACTIVE,
            ':partial_status' => self::STATUS_PARTIALLY_USED,
            ':days' => $days
        ]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Cancel voucher
     */
    public function cancelVoucher($voucherId, $reason = null)
    {
        $db = Flight::db();
        
        $voucher = $this->getById($voucherId);
        
        if (!$voucher) {
            throw new Exception('Voucher not found');
        }
        
        if ($voucher['status'] === self::STATUS_FULLY_USED) {
            throw new Exception('Cannot cancel fully used voucher');
        }
        
        $stmt = $db->prepare("
            UPDATE vouchers 
            SET status = :status,
                notes = CONCAT(IFNULL(notes, ''), :cancellation_note),
                updated_at = NOW()
            WHERE id = :id
        ");
        
        $cancellationNote = "\n[Cancelled on " . date('Y-m-d H:i:s') . "]";
        if ($reason) {
            $cancellationNote .= " Reason: " . $reason;
        }
        
        $stmt->execute([
            ':status' => self::STATUS_CANCELLED,
            ':cancellation_note' => $cancellationNote,
            ':id' => $voucherId
        ]);
        
        return true;
    }
    
    /**
     * Get voucher statistics
     */
    public function getStatistics($filters = [])
    {
        $db = Flight::db();
        
        $where = ['1=1'];
        $params = [];
        
        if (!empty($filters['date_from'])) {
            $where[] = 'v.issue_date >= :date_from';
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = 'v.issue_date <= :date_to';
            $params[':date_to'] = $filters['date_to'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $stmt = $db->prepare("
            SELECT 
                COUNT(*) as total_vouchers,
                SUM(v.amount) as total_value,
                SUM(v.amount - v.remaining_amount) as total_used,
                SUM(v.remaining_amount) as total_remaining,
                SUM(CASE WHEN v.status = 'active' THEN 1 ELSE 0 END) as active_count,
                SUM(CASE WHEN v.status = 'partially_used' THEN 1 ELSE 0 END) as partial_count,
                SUM(CASE WHEN v.status = 'fully_used' THEN 1 ELSE 0 END) as used_count,
                SUM(CASE WHEN v.status = 'expired' THEN 1 ELSE 0 END) as expired_count,
                SUM(CASE WHEN v.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_count
            FROM vouchers v
            WHERE $whereClause
        ");
        
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Send voucher email
     */
    private function sendVoucherEmail($voucherId, $type = 'created')
    {
        // This would integrate with your email service
        // For now, just a placeholder
        return true;
    }
    
    /**
     * Get config value
     */
    private function getConfig($key, $default = null)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        $value = $stmt->fetch(PDO::FETCH_COLUMN);
        
        return $value !== false ? $value : $default;
    }
    
    /**
     * Update voucher status
     */
    private function updateStatus($voucherId, $status)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE vouchers 
            SET status = :status, updated_at = NOW() 
            WHERE id = :id
        ");
        
        return $stmt->execute([':status' => $status, ':id' => $voucherId]);
    }
}