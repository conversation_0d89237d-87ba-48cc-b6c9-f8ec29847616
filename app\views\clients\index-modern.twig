{% extends "base-modern.twig" %}

{% block title %}{{ __('clients.title') }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('common.home') }}</a></li>
<li class="breadcrumb-item active">{{ __('clients.title') }}</li>
{% endblock %}

{% block content %}
<!-- Page Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0">{{ __('clients.client_list') }}</h4>
                <p class="text-muted mb-0">{{ __('clients.manage_clients_desc') }}</p>
            </div>
            <a href="{{ base_url }}/clients/create" class="btn btn-primary">
                <i class="bi bi-plus-lg me-2"></i>{{ __('clients.add_client') }}
            </a>
        </div>
    </div>
</div>

<!-- Filters Card -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="{{ base_url }}/clients" id="filterForm">
            <div class="row g-3 align-items-end">
                <!-- Search -->
                <div class="col-md-4">
                    <label for="search" class="form-label">{{ __('common.search') }}</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" id="search" name="search" 
                               placeholder="{{ __('clients.search_placeholder') }}" 
                               value="{{ filters.search|default('') }}">
                    </div>
                </div>
                
                <!-- Client Type -->
                <div class="col-md-3">
                    <label for="type" class="form-label">{{ __('clients.client_type') }}</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="individual" {{ filters.type == 'individual' ? 'selected' : '' }}>
                            {{ __('clients.individual') }}
                        </option>
                        <option value="company" {{ filters.type == 'company' ? 'selected' : '' }}>
                            {{ __('clients.company') }}
                        </option>
                    </select>
                </div>
                
                <!-- Status -->
                <div class="col-md-3">
                    <label for="status" class="form-label">{{ __('common.status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="1" {{ filters.status == '1' ? 'selected' : '' }}>
                            {{ __('common.active') }}
                        </option>
                        <option value="0" {{ filters.status == '0' ? 'selected' : '' }}>
                            {{ __('common.inactive') }}
                        </option>
                    </select>
                </div>
                
                <!-- Actions -->
                <div class="col-md-2">
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary flex-grow-1">
                            <i class="bi bi-funnel me-1"></i>{{ __('common.filter') }}
                        </button>
                        <a href="{{ base_url }}/clients" class="btn btn-outline-secondary" title="{{ __('common.reset') }}">
                            <i class="bi bi-arrow-clockwise"></i>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Stats Cards -->
<div class="row g-3 mb-4">
    <div class="col-md-3 col-sm-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">{{ __('clients.total_clients') }}</h6>
                        <h3 class="mb-0">{{ pagination.total|default(0)|number_format(0, '.', ',') }}</h3>
                    </div>
                    <div class="text-primary">
                        <i class="bi bi-people-fill fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">{{ __('clients.active_clients') }}</h6>
                        <h3 class="mb-0">{{ statistics.total_active|default(0)|number_format(0, '.', ',') }}</h3>
                    </div>
                    <div class="text-success">
                        <i class="bi bi-check-circle-fill fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">{{ __('clients.companies') }}</h6>
                        <h3 class="mb-0">{{ statistics.total_companies|default(0)|number_format(0, '.', ',') }}</h3>
                    </div>
                    <div class="text-info">
                        <i class="bi bi-building fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="text-muted mb-1">{{ __('clients.individuals') }}</h6>
                        <h3 class="mb-0">{{ statistics.total_individuals|default(0)|number_format(0, '.', ',') }}</h3>
                    </div>
                    <div class="text-warning">
                        <i class="bi bi-person-fill fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clients Table -->
<div class="card">
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="clientsTable">
                <thead>
                    <tr>
                        <th class="px-4">
                            <input type="checkbox" class="form-check-input" id="selectAll">
                        </th>
                        <th>{{ __('clients.client') }}</th>
                        <th>{{ __('clients.type') }}</th>
                        <th>{{ __('clients.contact') }}</th>
                        <th>{{ __('clients.location') }}</th>
                        <th>{{ __('common.status') }}</th>
                        <th>{{ __('common.created_at') }}</th>
                        <th class="text-end pe-4">{{ __('common.actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr class="align-middle">
                        <td class="px-4">
                            <input type="checkbox" class="form-check-input row-checkbox" value="{{ client.id }}">
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-3">
                                    {{ client.first_name|first|upper }}{{ client.last_name|first|upper }}
                                </div>
                                <div>
                                    <h6 class="mb-0">
                                        {% if client.client_type == 'company' and client.company_name %}
                                            {{ client.company_name }}
                                        {% else %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        {% endif %}
                                    </h6>
                                    {% if client.client_type == 'company' and client.company_name %}
                                        <small class="text-muted">{{ client.first_name }} {{ client.last_name }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>
                            {% if client.client_type == 'company' %}
                                <span class="badge bg-info-subtle text-info">
                                    <i class="bi bi-building me-1"></i>{{ __('clients.company') }}
                                </span>
                            {% else %}
                                <span class="badge bg-warning-subtle text-warning">
                                    <i class="bi bi-person me-1"></i>{{ __('clients.individual') }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            {% if client.email %}
                                <div><i class="bi bi-envelope text-muted me-1"></i>{{ client.email }}</div>
                            {% endif %}
                            {% if client.phone %}
                                <div><i class="bi bi-telephone text-muted me-1"></i>{{ client.phone }}</div>
                            {% endif %}
                        </td>
                        <td>
                            {% if client.city or client.country %}
                                <i class="bi bi-geo-alt text-muted me-1"></i>
                                {{ client.city }}{% if client.city and client.country %}, {% endif %}{{ client.country }}
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if client.is_active == 1 %}
                                <span class="badge bg-success-subtle text-success status-badge" 
                                      data-client-id="{{ client.id }}" 
                                      data-status="{{ client.is_active }}"
                                      style="cursor: pointer;"
                                      title="{{ __('common.click_to_change_status') }}">
                                    <i class="bi bi-check-circle me-1"></i>{{ __('common.active') }}
                                </span>
                            {% else %}
                                <span class="badge bg-danger-subtle text-danger status-badge" 
                                      data-client-id="{{ client.id }}" 
                                      data-status="{{ client.is_active }}"
                                      style="cursor: pointer;"
                                      title="{{ __('common.click_to_change_status') }}">
                                    <i class="bi bi-x-circle me-1"></i>{{ __('common.inactive') }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <small class="text-muted">{{ client.created_at|date('M j, Y') }}</small>
                        </td>
                        <td class="text-end pe-4">
                            <div class="btn-group btn-group-sm">
                                <a href="{{ base_url }}/clients/{{ client.id }}" class="btn btn-outline-primary" 
                                   data-bs-toggle="tooltip"
                                   title="{{ __('common.view') }}">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{{ base_url }}/clients/{{ client.id }}/edit" class="btn btn-outline-secondary" 
                                   data-bs-toggle="tooltip"
                                   title="{{ __('common.edit') }}">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete({{ client.id }}, '{{ client.first_name }} {{ client.last_name }}')"
                                        data-bs-toggle="tooltip"
                                        title="{{ __('common.delete') }}">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center py-5">
                            <div class="empty-state">
                                <div class="empty-state-icon">
                                    <i class="bi bi-people"></i>
                                </div>
                                <h5 class="empty-state-title">{{ __('clients.no_clients_found') }}</h5>
                                <p class="empty-state-description">{{ __('clients.no_clients_description') }}</p>
                                <a href="{{ base_url }}/clients/create" class="btn btn-primary mt-3">
                                    <i class="bi bi-plus-lg me-2"></i>{{ __('clients.add_first_client') }}
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if clients|length > 0 %}
        <!-- Bulk Actions -->
        <div class="border-top px-4 py-3" id="bulkActions" style="display: none;">
            <div class="d-flex align-items-center justify-content-between">
                <div>
                    <span class="text-muted me-2">
                        <span id="selectedCount">0</span> {{ __('common.items_selected') }}
                    </span>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="bulkDelete()">
                        <i class="bi bi-trash me-1"></i>{{ __('common.delete_selected') }}
                    </button>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if pagination.total_pages > 1 %}
<div class="d-flex justify-content-between align-items-center mt-4">
    <div class="text-muted">
        {{ __('common.showing') }} {{ pagination.from }} - {{ pagination.to }} {{ __('common.of') }} {{ pagination.total }} {{ __('common.results') }}
    </div>
    <nav>
        <ul class="pagination mb-0">
            {% if pagination.current_page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ base_url }}/clients?page={{ pagination.current_page - 1 }}{{ filters_query }}">
                        <i class="bi bi-chevron-left"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for page in 1..pagination.total_pages %}
                {% if page == pagination.current_page %}
                    <li class="page-item active">
                        <span class="page-link">{{ page }}</span>
                    </li>
                {% elseif page == 1 or page == pagination.total_pages or (page >= pagination.current_page - 2 and page <= pagination.current_page + 2) %}
                    <li class="page-item">
                        <a class="page-link" href="{{ base_url }}/clients?page={{ page }}{{ filters_query }}">{{ page }}</a>
                    </li>
                {% elseif page == pagination.current_page - 3 or page == pagination.current_page + 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.current_page < pagination.total_pages %}
                <li class="page-item">
                    <a class="page-link" href="{{ base_url }}/clients?page={{ pagination.current_page + 1 }}{{ filters_query }}">
                        <i class="bi bi-chevron-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
</div>
{% endif %}
{% endblock %}

{% block styles %}
<style>
/* Avatar Circle */
.avatar-circle {
    width: 40px;
    height: 40px;
    background: var(--bs-primary);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

/* Table Row Hover */
.table-hover tbody tr {
    transition: var(--transition-fast);
}

.table-hover tbody tr:hover {
    transform: translateX(2px);
}

/* Checkbox Animation */
.form-check-input {
    cursor: pointer;
    transition: var(--transition-fast);
}

.form-check-input:checked {
    animation: checkPulse 0.3s ease-out;
}

@keyframes checkPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}
</style>
{% endblock %}

{% block scripts %}
<script>
// Initialize Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Select All Checkbox
const selectAllCheckbox = document.getElementById('selectAll');
const rowCheckboxes = document.querySelectorAll('.row-checkbox');
const bulkActionsDiv = document.getElementById('bulkActions');
const selectedCountSpan = document.getElementById('selectedCount');

selectAllCheckbox?.addEventListener('change', function() {
    rowCheckboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
    updateBulkActions();
});

rowCheckboxes.forEach(checkbox => {
    checkbox.addEventListener('change', updateBulkActions);
});

function updateBulkActions() {
    const checkedCount = document.querySelectorAll('.row-checkbox:checked').length;
    
    if (checkedCount > 0) {
        bulkActionsDiv.style.display = 'block';
        selectedCountSpan.textContent = checkedCount;
    } else {
        bulkActionsDiv.style.display = 'none';
    }
    
    // Update select all checkbox state
    selectAllCheckbox.checked = checkedCount === rowCheckboxes.length && checkedCount > 0;
    selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < rowCheckboxes.length;
}

// Delete Confirmation
function confirmDelete(id, name) {
    Swal.fire({
        title: '{{ __("common.are_you_sure") }}',
        text: `{{ __("clients.delete_confirmation") }}: ${name}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.yes_delete") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `{{ base_url }}/clients/${id}/delete`;
            
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '{{ csrf_name }}';
            csrfInput.value = '{{ csrf_value }}';
            form.appendChild(csrfInput);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}

// Bulk Delete
function bulkDelete() {
    const selectedIds = Array.from(document.querySelectorAll('.row-checkbox:checked'))
        .map(checkbox => checkbox.value);
    
    if (selectedIds.length === 0) return;
    
    Swal.fire({
        title: '{{ __("common.are_you_sure") }}',
        text: `{{ __("clients.bulk_delete_confirmation") }} ${selectedIds.length} {{ __("clients.clients") }}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("common.yes_delete") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            // Implement bulk delete
            console.log('Bulk delete:', selectedIds);
        }
    });
}

// Auto-submit filter form on select change
document.querySelectorAll('#filterForm select').forEach(select => {
    select.addEventListener('change', function() {
        document.getElementById('filterForm').submit();
    });
});

// Table row click (view details)
document.querySelectorAll('#clientsTable tbody tr').forEach(row => {
    row.addEventListener('click', function(e) {
        // Don't navigate if clicking on checkbox, link, or button
        if (e.target.matches('input, a, button, i')) return;
        
        const clientId = this.querySelector('.row-checkbox')?.value;
        if (clientId) {
            window.location.href = `{{ base_url }}/clients/${clientId}`;
        }
    });
    
    // Add cursor pointer on hover
    row.style.cursor = 'pointer';
});

// Status Toggle
document.querySelectorAll('.status-badge').forEach(badge => {
    badge.addEventListener('click', function(e) {
        e.stopPropagation(); // Prevent row click
        
        const clientId = this.getAttribute('data-client-id');
        const currentStatus = this.getAttribute('data-status');
        const newStatus = currentStatus == '1' ? '0' : '1';
        const badge = this;
        
        // Show loading state
        badge.style.opacity = '0.5';
        badge.style.pointerEvents = 'none';
        
        // Make AJAX request
        fetch(`{{ base_url }}/clients/${clientId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-Token': '{{ csrf_token }}'
            },
            body: JSON.stringify({ status: newStatus })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update badge appearance
                if (newStatus == '1') {
                    badge.className = 'badge bg-success-subtle text-success status-badge';
                    badge.innerHTML = '<i class="bi bi-check-circle me-1"></i>{{ __("common.active") }}';
                } else {
                    badge.className = 'badge bg-danger-subtle text-danger status-badge';
                    badge.innerHTML = '<i class="bi bi-x-circle me-1"></i>{{ __("common.inactive") }}';
                }
                
                // Update data attribute
                badge.setAttribute('data-status', newStatus);
                
                // Show success message
                toastr.success(data.message || '{{ __("common.status_updated") }}');
                
                // Update statistics if needed
                if (typeof updateStatistics === 'function') {
                    updateStatistics();
                }
            } else {
                toastr.error(data.message || '{{ __("common.error_occurred") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ __("common.error_occurred") }}');
        })
        .finally(() => {
            // Restore badge state
            badge.style.opacity = '1';
            badge.style.pointerEvents = 'auto';
        });
    });
});
</script>
{% endblock %}