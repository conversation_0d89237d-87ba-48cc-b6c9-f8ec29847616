<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== UPDATING INVOICE NUMBER FORMAT ===\n\n";
    
    // Update invoice document type to use 4-digit format
    $stmt = $pdo->prepare("
        UPDATE document_types 
        SET number_format = '{PREFIX}-{YYYY}-{NNNN}',
            updated_at = NOW()
        WHERE code = 'invoice'
    ");
    $stmt->execute();
    
    echo "✓ Updated invoice number format to use 4 digits\n";
    echo "  New format: {PREFIX}-{YYYY}-{NNNN}\n";
    echo "  Example: FAC-2025-0186\n\n";
    
    // Also update the sequence to 185 so next will be 186
    $stmt = $pdo->prepare("SELECT id FROM document_types WHERE code = 'invoice'");
    $stmt->execute();
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($docType) {
        $stmt = $pdo->prepare("
            UPDATE document_sequences 
            SET last_number = 185, updated_at = NOW() 
            WHERE document_type_id = ? 
            AND year = 2025 
            AND month IS NULL
        ");
        $stmt->execute([$docType['id']]);
        
        echo "✓ Updated sequence to last_number = 185\n";
        echo "  Next invoice will be: FAC-2025-0186\n\n";
    }
    
    echo "Done! The next invoice number will be FAC-2025-0186 (with 4 digits)";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}