<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== FIXING INVOICE TOTALS ===\n\n";

$db = Flight::db();

// Get invoice 238
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = 238");
$stmt->execute();
$invoice = $stmt->fetch(\PDO::FETCH_ASSOC);

if ($invoice) {
    echo "Invoice: {$invoice['invoice_number']}\n";
    echo "Current totals:\n";
    echo "- Subtotal: {$invoice['subtotal']}\n";
    echo "- VAT: {$invoice['vat_amount']}\n";
    echo "- Total: {$invoice['total']}\n\n";
    
    // Get lines and recalculate
    $stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = 238");
    $stmt->execute();
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    $subtotal = 0;
    $vat = 0;
    
    echo "Lines:\n";
    foreach ($lines as $line) {
        $lineTotal = $line['quantity'] * $line['unit_price'];
        $lineVat = $lineTotal * ($line['vat_rate'] / 100);
        $subtotal += $lineTotal;
        $vat += $lineVat;
        
        echo "- {$line['description']}: {$line['quantity']} x {$line['unit_price']} = $lineTotal (VAT: $lineVat)\n";
    }
    
    $total = $subtotal + $vat;
    
    echo "\nCalculated totals:\n";
    echo "- Subtotal: $subtotal\n";
    echo "- VAT: $vat\n";
    echo "- Total: $total\n";
    
    if ($invoice['subtotal'] != $subtotal || $invoice['vat_amount'] != $vat || $invoice['total'] != $total) {
        echo "\nTotals don't match! Updating...\n";
        $stmt = $db->prepare("
            UPDATE invoices 
            SET subtotal = ?, vat_amount = ?, total = ?
            WHERE id = 238
        ");
        $stmt->execute([$subtotal, $vat, $total]);
        echo "Updated!\n";
    } else {
        echo "\nTotals match correctly.\n";
    }
}