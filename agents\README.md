# Fit360 AdminDesk Claude Agents

This directory contains specialized Claude agent prompts for the Fit360 AdminDesk healthcare billing system. These agents are designed to provide expert assistance for different aspects of the project.

## Available Agents

### Core Development Agents

1. **[Backend Engineer Agent](backend-engineer-agent.md)**
   - PHP/Flight framework development
   - API design and implementation
   - Database architecture
   - Business logic and services

2. **[Frontend Engineer Agent](frontend-engineer-agent.md)**
   - Multi-theme UI development
   - JavaScript functionality
   - Responsive design
   - User experience optimization

3. **[Mobile UI/UX Agent](mobile-ui-agent.md)**
   - Touch-optimized interfaces
   - Mobile-specific features
   - Gesture implementations
   - Cross-device compatibility

### Domain-Specific Agents

4. **[Billing & Invoice Management Agent](billing-invoice-agent.md)**
   - Invoice generation and management
   - VAT calculations
   - Payment processing
   - PDF generation and email delivery

5. **[Retrocession Management Agent](retrocession-agent.md)**
   - Practitioner billing calculations
   - Monthly data management
   - Bulk invoice generation
   - Custom retrocession settings

6. **[Database Migration Agent](database-migration-agent.md)**
   - Schema updates
   - Data migration strategies
   - Performance optimization
   - Foreign key management

### Support & Quality Agents

7. **[Developer Debugger Agent](debugger-agent.md)**
   - Issue diagnosis and resolution
   - Performance troubleshooting
   - Error analysis
   - Quick fixes and patches

8. **[Testing & QA Agent](testing-qa-agent.md)**
   - PHPUnit test creation
   - Integration testing
   - Quality assurance
   - Test coverage improvement

9. **[Localization Agent](localization-agent.md)**
   - Multi-language support (FR/EN/DE)
   - Translation management
   - Cultural adaptation
   - Email and PDF localization

### Research & Optimization

10. **[Research & Optimization Agent](research-optimization-agent.md)**
    - Real-time documentation research with Context7
    - Performance optimization strategies
    - Security best practices and updates
    - Framework and library research
    - Compliance and standards monitoring

### Meta Agent

11. **[Agent Selector/Router](agent-selector-router.md)**
    - Analyzes requests and routes to appropriate agents
    - Handles complex multi-agent workflows
    - Provides handoff instructions

## Usage

### Direct Agent Usage

Copy the content of any agent file and use it as a system prompt for Claude to get specialized assistance:

```
[Paste agent prompt here]

User: I need help implementing a new invoice type for the system
```

### Using the Agent Selector

For optimal routing, use the Agent Selector first:

```
[Paste agent-selector-router.md content]

User: I'm getting an error when generating bulk retrocession invoices
```

The selector will identify the best agent(s) for your task.

### Multi-Agent Workflows

For complex features, you might need multiple agents:

1. Start with Agent Selector to plan the workflow
2. Use Backend Engineer for API development
3. Switch to Frontend Engineer for UI
4. Finish with Testing Agent for quality assurance

## Project Context

All agents are configured with deep knowledge of:

- **Tech Stack**: PHP 8.2, Flight PHP 3.x, Twig 3.0, MySQL/MariaDB
- **Architecture**: MVC pattern, multi-theme support, modular routing
- **Business Domain**: Healthcare billing, retrocession calculations, Luxembourg tax rules
- **Key Features**: Multi-language, mobile-responsive, CSRF protection
- **Development Practices**: Composer, PHPUnit, migration-based schema
- **Research Tools**: Context7 MCP integration for real-time documentation

## Creating Custom Agents

To create a new specialized agent:

1. Identify the specific domain or skill set
2. Include relevant technical context
3. Define core responsibilities
4. Add common tasks and patterns
5. Include troubleshooting guidance
6. Reference key files and locations

## Best Practices

- Use specific agents for focused tasks
- Start with the debugger for unknown issues
- Combine agents for full-stack features
- Keep agents updated with project changes
- Include project-specific patterns and conventions

## Maintenance

These agents should be updated when:
- Major architectural changes occur
- New features or modules are added
- Common issues or patterns emerge
- Technology stack updates