<?php
/**
 * Test PDF Generation
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

// Test direct TCPDF
try {
    // Check if TCPDF is available
    if (!class_exists('TCPDF')) {
        die("TCPDF class not found!");
    }
    
    // Try to include TCPDF config if needed
    $tcpdfConfig = __DIR__ . '/../vendor/tecnickcom/tcpdf/config/tcpdf_config.php';
    if (file_exists($tcpdfConfig)) {
        require_once $tcpdfConfig;
    }
    
    // Create new PDF
    $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
    
    // Set document info
    $pdf->SetCreator('Test');
    $pdf->SetAuthor('Test');
    $pdf->SetTitle('Test PDF');
    
    // Add a page
    $pdf->AddPage();
    
    // Set content
    $pdf->SetFont('helvetica', '', 12);
    $pdf->Cell(0, 10, 'Test PDF Generation', 0, 1, 'C');
    $pdf->Ln(10);
    $pdf->Cell(0, 10, 'If you can see this, TCPDF is working!', 0, 1, 'L');
    
    // Output
    $pdf->Output('test.pdf', 'I');
    
} catch (Exception $e) {
    echo "<h1>Error generating PDF:</h1>";
    echo "<pre>" . $e->getMessage() . "</pre>";
    echo "<h2>Stack trace:</h2>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>