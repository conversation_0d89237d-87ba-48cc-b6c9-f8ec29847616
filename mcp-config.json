{"context_sources": [{"type": "file", "path": "./CLAUDE.md", "name": "project_instructions", "description": "Claude Code instructions and project overview"}, {"type": "directory", "path": "./app", "name": "flight_php_app", "description": "Flight PHP application code - controllers, models, views"}, {"type": "file", "path": "./.env", "name": "environment", "description": "Environment configuration"}, {"type": "directory", "path": "./tasks", "name": "project_tasks", "description": "Project task breakdowns and documentation"}], "max_context_size": 50000}