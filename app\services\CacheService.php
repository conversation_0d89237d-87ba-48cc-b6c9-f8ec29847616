<?php

namespace App\Services;

use Flight;

class CacheService
{
    private $cacheDir;
    private $defaultTTL = 3600; // 1 hour default
    private $enabled;
    private $memoryCache = []; // In-memory cache for request lifecycle
    private $stats = [
        'hits' => 0,
        'misses' => 0,
        'writes' => 0
    ];
    
    public function __construct()
    {
        $this->cacheDir = __DIR__ . '/../../storage/cache/data/';
        $this->enabled = !($_ENV['APP_DEBUG'] ?? false);
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Get cached data or execute callback
     */
    public function remember($key, $callback, $ttl = null)
    {
        if (!$this->enabled) {
            return $callback();
        }
        
        $cached = $this->get($key);
        if ($cached !== null) {
            return $cached;
        }
        
        $data = $callback();
        $this->set($key, $data, $ttl);
        
        return $data;
    }
    
    /**
     * Get cached value
     */
    public function get($key)
    {
        if (!$this->enabled) {
            return null;
        }
        
        // Check memory cache first
        if (isset($this->memoryCache[$key])) {
            $this->stats['hits']++;
            return $this->memoryCache[$key]['value'];
        }
        
        $filename = $this->getCacheFilename($key);
        
        if (!file_exists($filename)) {
            $this->stats['misses']++;
            return null;
        }
        
        $data = @unserialize(file_get_contents($filename));
        
        if ($data === false) {
            $this->stats['misses']++;
            return null;
        }
        
        // Check expiration
        if ($data['expires'] < time()) {
            @unlink($filename);
            unset($this->memoryCache[$key]);
            $this->stats['misses']++;
            return null;
        }
        
        // Store in memory cache for faster subsequent access
        $this->memoryCache[$key] = $data;
        $this->stats['hits']++;
        
        return $data['value'];
    }
    
    /**
     * Set cache value
     */
    public function set($key, $value, $ttl = null)
    {
        if (!$this->enabled) {
            return false;
        }
        
        $ttl = $ttl ?? $this->defaultTTL;
        $filename = $this->getCacheFilename($key);
        
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        
        // Store in memory cache
        $this->memoryCache[$key] = $data;
        $this->stats['writes']++;
        
        return file_put_contents($filename, serialize($data), LOCK_EX) !== false;
    }
    
    /**
     * Delete cached value
     */
    public function forget($key)
    {
        unset($this->memoryCache[$key]);
        
        $filename = $this->getCacheFilename($key);
        
        if (file_exists($filename)) {
            return @unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Delete all cached values with a given prefix
     */
    public function forgetByPrefix($prefix)
    {
        $pattern = $this->cacheDir . md5($prefix) . '*';
        $files = glob($pattern);
        
        foreach ($files as $file) {
            @unlink($file);
        }
        
        return true;
    }
    
    /**
     * Delete cached values matching a pattern
     * @param string $pattern Pattern to match (e.g., 'lang.fr.*')
     */
    public function deletePattern($pattern)
    {
        // Convert pattern to regex
        $regex = str_replace(['*', '.'], ['.*', '\.'], $pattern);
        $regex = '/^' . $regex . '$/';
        
        // Clear from memory cache
        foreach ($this->memoryCache as $key => $value) {
            if (preg_match($regex, $key)) {
                unset($this->memoryCache[$key]);
            }
        }
        
        // Clear from file cache
        $files = glob($this->cacheDir . '*.cache');
        foreach ($files as $file) {
            // We need to check the actual key, not the hashed filename
            // For now, we'll use a simpler approach: clear by prefix
            $prefix = explode('.', $pattern)[0] . '.' . explode('.', $pattern)[1] . '.';
            $this->forgetByPrefix($prefix);
            break;
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     */
    public function flush()
    {
        $files = glob($this->cacheDir . '*');
        
        foreach ($files as $file) {
            if (is_file($file)) {
                @unlink($file);
            }
        }
        
        return true;
    }
    
    /**
     * Generate cache tags for invalidation
     */
    public function tags($tags)
    {
        // For simplicity, we'll use prefixes for now
        // In a more advanced implementation, this could use Redis or similar
        return new TaggedCache($this, $tags);
    }
    
    /**
     * Get cache filename for a key
     */
    private function getCacheFilename($key)
    {
        return $this->cacheDir . md5($key) . '.cache';
    }
    
    /**
     * Enable/disable caching
     */
    public function enable($enabled = true)
    {
        $this->enabled = $enabled;
    }
    
    /**
     * Get cache statistics
     */
    public function getStats()
    {
        $files = glob($this->cacheDir . '*.cache');
        $totalSize = 0;
        $validEntries = 0;
        $expiredEntries = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            $data = @unserialize(file_get_contents($file));
            
            if ($data !== false && isset($data['expires'])) {
                if ($data['expires'] >= time()) {
                    $validEntries++;
                } else {
                    $expiredEntries++;
                }
            }
        }
        
        return array_merge($this->stats, [
            'memory_entries' => count($this->memoryCache),
            'file_entries' => count($files),
            'valid_entries' => $validEntries,
            'expired_entries' => $expiredEntries,
            'total_size' => $totalSize,
            'cache_dir' => $this->cacheDir,
            'hit_rate' => $this->stats['hits'] + $this->stats['misses'] > 0 
                ? round($this->stats['hits'] / ($this->stats['hits'] + $this->stats['misses']) * 100, 2) 
                : 0
        ]);
    }
}

/**
 * Tagged cache implementation for invalidation by tags
 */
class TaggedCache
{
    private $cache;
    private $tags;
    
    public function __construct(CacheService $cache, array $tags)
    {
        $this->cache = $cache;
        $this->tags = $tags;
    }
    
    public function remember($key, $callback, $ttl = null)
    {
        // Add tags to the key
        $taggedKey = $this->getTaggedKey($key);
        return $this->cache->remember($taggedKey, $callback, $ttl);
    }
    
    public function get($key)
    {
        $taggedKey = $this->getTaggedKey($key);
        return $this->cache->get($taggedKey);
    }
    
    public function set($key, $value, $ttl = null)
    {
        $taggedKey = $this->getTaggedKey($key);
        return $this->cache->set($taggedKey, $value, $ttl);
    }
    
    public function flush()
    {
        foreach ($this->tags as $tag) {
            $this->cache->forgetByPrefix($tag . ':');
        }
    }
    
    private function getTaggedKey($key)
    {
        return implode(':', $this->tags) . ':' . $key;
    }
}