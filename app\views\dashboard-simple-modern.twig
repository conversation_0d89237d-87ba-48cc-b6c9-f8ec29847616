{% extends "base-modern.twig" %}

{% block title %}{{ __('common.dashboard') }}{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <h2 class="card-title mb-3">
                    {{ __('common.welcome') }}, {{ session.user_name|default('User') }}!
                </h2>
                <p class="text-muted mb-0">
                    {{ __('common.dashboard_subtitle')|default('Here\'s an overview of your health center\'s current status.') }}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards Row -->
<div class="row g-3 mb-4">
    <!-- Total Patients Card -->
    <div class="col-xl-3 col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-user-injured text-primary fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">{{ __('patients.total_patients') }}</h6>
                        <h3 class="mb-0">{{ stats.total_patients|default(0)|number_format }}</h3>
                        <small class="text-success">
                            <i class="bi bi-arrow-up"></i> +12%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Active Clients Card -->
    <div class="col-xl-3 col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-building text-success fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">{{ __('clients.active_clients') }}</h6>
                        <h3 class="mb-0">{{ stats.total_clients|default(0)|number_format }}</h3>
                        <small class="text-muted">
                            <i class="bi bi-dash"></i> 0%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Invoices Card -->
    <div class="col-xl-3 col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-clock-history text-warning fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">{{ __('invoices.pending_invoices') }}</h6>
                        <h3 class="mb-0">{{ stats.pending_invoices|default(0)|number_format }}</h3>
                        <small class="text-danger">
                            <i class="bi bi-arrow-down"></i> -8%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Revenue Card -->
    <div class="col-xl-3 col-lg-6">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="bi bi-currency-euro text-info fs-4"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h6 class="text-muted mb-1">{{ __('common.monthly_revenue') }}</h6>
                        <h3 class="mb-0">{{ currency }}{{ stats.monthly_revenue|default(0)|number_format(2) }}</h3>
                        <small class="text-success">
                            <i class="bi bi-arrow-up"></i> +15%
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Activity Row -->
<div class="row g-3">
    <!-- Revenue Chart -->
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0 d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">{{ __('common.revenue_overview') }}</h5>
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary active">{{ __('common.week') }}</button>
                    <button type="button" class="btn btn-outline-secondary">{{ __('common.month') }}</button>
                    <button type="button" class="btn btn-outline-secondary">{{ __('common.year') }}</button>
                </div>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="300"></canvas>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-transparent border-0">
                <h5 class="card-title mb-0">{{ __('common.recent_activity') }}</h5>
            </div>
            <div class="card-body">
                <div class="activity-feed">
                    <!-- Activity Item -->
                    <div class="activity-item d-flex mb-3">
                        <div class="activity-icon bg-primary bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-file-earmark-plus text-primary"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-0">
                                <strong>{{ __('invoices.new_invoice_created') }}</strong>
                            </p>
                            <small class="text-muted">{{ __('common.minutes_ago', {'minutes': 5}) }}</small>
                        </div>
                    </div>

                    <!-- Activity Item -->
                    <div class="activity-item d-flex mb-3">
                        <div class="activity-icon bg-success bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-person-plus text-success"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-0">
                                <strong>{{ __('patients.new_patient_added') }}</strong>
                            </p>
                            <small class="text-muted">{{ __('common.hours_ago', {'hours': 2}) }}</small>
                        </div>
                    </div>

                    <!-- Activity Item -->
                    <div class="activity-item d-flex mb-3">
                        <div class="activity-icon bg-info bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-cash-coin text-info"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-0">
                                <strong>{{ __('invoices.payment_received') }}</strong>
                            </p>
                            <small class="text-muted">{{ __('common.hours_ago', {'hours': 4}) }}</small>
                        </div>
                    </div>

                    <!-- Activity Item -->
                    <div class="activity-item d-flex">
                        <div class="activity-icon bg-warning bg-opacity-10 rounded-circle p-2 me-3">
                            <i class="bi bi-building-add text-warning"></i>
                        </div>
                        <div class="flex-grow-1">
                            <p class="mb-0">
                                <strong>{{ __('clients.new_client_added') }}</strong>
                            </p>
                            <small class="text-muted">{{ __('common.yesterday') }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-transparent border-0">
                <h5 class="card-title mb-0">{{ __('common.quick_actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/invoices/create" class="btn btn-primary w-100 py-3">
                            <i class="bi bi-file-earmark-plus me-2"></i>
                            {{ __('invoices.create_invoice') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/patients/create" class="btn btn-success w-100 py-3">
                            <i class="fas fa-user-plus me-2"></i>
                            {{ __('patients.add_patient') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/clients/create" class="btn btn-info w-100 py-3">
                            <i class="bi bi-building-add me-2"></i>
                            {{ __('clients.add_client') }}
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <a href="{{ base_url }}/invoices" class="btn btn-secondary w-100 py-3">
                            <i class="bi bi-list-check me-2"></i>
                            {{ __('invoices.view_all_invoices') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Revenue Chart
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: '{{ __("common.revenue") }}',
            data: [1200, 1900, 3000, 2500, 2700, 3500, 3200],
            borderColor: 'rgb(13, 110, 253)',
            backgroundColor: 'rgba(13, 110, 253, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                    label: function(context) {
                        return '{{ currency }}' + context.parsed.y.toFixed(2);
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return '{{ currency }}' + value;
                    }
                }
            }
        }
    }
});

// Add animation to cards
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.animation = `fadeInUp 0.5s ease-out ${index * 0.1}s forwards`;
    });
});
</script>

<style>
@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.activity-feed .activity-item:last-child {
    margin-bottom: 0;
}

.activity-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.btn-group-sm .btn {
    font-size: 0.875rem;
    padding: 0.25rem 0.75rem;
}

.card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1) !important;
}
</style>
{% endblock %}