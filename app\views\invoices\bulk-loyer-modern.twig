{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.bulk_loyer_generation') | default('Génération de loyer en masse') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.bulk_loyer_generation') | default('Génération de loyer en masse') }}</h1>
            <p class="text-muted mb-0">{{ __('invoices.generate_loyer_invoices_for_period') | default('Générer les factures de loyer pour') }} {{ monthName }} {{ year }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/invoices" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('invoices.total_users_with_obligations') | default('Total utilisateurs avec obligations') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ stats.total_users }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('invoices.ready_to_generate') | default('Prêt à générer') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ stats.ready_to_generate }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('invoices.already_generated') | default('Déjà généré') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ stats.already_generated }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-file-earmark-check text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('invoices.total_loyer_amount') | default('Montant total loyer') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">€ {{ stats.total_amount|number_format(2, ',', ' ') }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-euro text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Month/Year Selection -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ __('invoices.select_period') | default('Sélectionner la période') }}</h6>
        </div>
        <div class="card-body">
            <form id="periodForm" class="row g-3">
                <div class="col-md-3">
                    <label for="month" class="form-label">{{ __('invoices.month') | default('Mois') }}</label>
                    <select class="form-select" id="month" name="month">
                        {% for value, name in months %}
                            <option value="{{ value }}" {% if value == month %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="year" class="form-label">{{ __('invoices.year') | default('Année') }}</label>
                    <select class="form-select" id="year" name="year">
                        {% for y in years %}
                            <option value="{{ y }}" {% if y == year %}selected{% endif %}>{{ y }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="userType" class="form-label">{{ __('invoices.user_type') | default('Type d\'utilisateur') }}</label>
                    <select class="form-select" id="userType" name="userType">
                        <option value="">{{ __('common.all') | default('Tous') }}</option>
                        <option value="practitioner" {% if userType == 'practitioner' %}selected{% endif %}>{{ __('invoices.medical_staff') | default('Personnel médical') }}</option>
                        <option value="support" {% if userType == 'support' %}selected{% endif %}>{{ __('invoices.managers_support') | default('Managers & Support') }}</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">
                        <i class="bi bi-arrow-clockwise me-2"></i>{{ __('invoices.update_list') | default('Actualiser la liste') }}
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Users List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 fw-bold text-primary">{{ __('invoices.users_with_obligations') | default('Utilisateurs avec obligations financières') }}</h6>
                </div>
                <div class="col-auto">
                    {% if stats.ready_to_generate > 0 %}
                        <button type="button" class="btn btn-success" id="generateAllBtn">
                            <i class="bi bi-play-circle me-2"></i>{{ __('invoices.generate_all_selected') | default('Générer toutes les sélectionnées') }}
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="usersTable">
                    <thead>
                        <tr>
                            <th>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th>{{ __('invoices.user') | default('Utilisateur') }}</th>
                            <th>{{ __('invoices.type') | default('Type') }}</th>
                            <th>{{ __('invoices.rent') | default('Loyer') }}</th>
                            <th>{{ __('invoices.charges') | default('Charges') }}</th>
                            <th>{{ __('invoices.other_charges') | default('Autres charges') }}</th>
                            <th>{{ __('invoices.total_obligations') | default('Total obligations') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('invoices.invoice') | default('Facture') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr data-user-id="{{ user.user_id }}" class="{% if not user.can_generate %}table-secondary{% endif %}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input user-checkbox" 
                                           type="checkbox" 
                                           value="{{ user.user_id }}"
                                           {% if not user.can_generate %}disabled{% endif %}>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <div class="fw-bold">{{ user.name }}</div>
                                    <small class="text-muted">{{ user.email }}</small>
                                </div>
                            </td>
                            <td>
                                {% if user.is_practitioner %}
                                    <span class="badge bg-primary">{{ __('invoices.medical_staff') | default('Personnel médical') }}</span>
                                {% else %}
                                    <span class="badge bg-info">{{ __('invoices.support_staff') | default('Personnel support') }}</span>
                                {% endif %}
                            </td>
                            <td>€ {{ user.rental_amount|number_format(2, ',', ' ') }}</td>
                            <td>€ {{ user.charges_amount|number_format(2, ',', ' ') }}</td>
                            <td>
                                {% if user.secretary_tvac_17 > 0 %}
                                    <span title="Secrétariat TVAC 17%">€ {{ user.secretary_tvac_17|number_format(2, ',', ' ') }}</span>
                                {% endif %}
                                {% if user.secretary_htva > 0 %}
                                    {% if user.secretary_tvac_17 > 0 %}<br>{% endif %}
                                    <span title="Secrétariat HTVA" class="text-muted">+ € {{ user.secretary_htva|number_format(2, ',', ' ') }}</span>
                                {% endif %}
                                {% if user.tva_17 > 0 %}
                                    {% if user.secretary_tvac_17 > 0 or user.secretary_htva > 0 %}<br>{% endif %}
                                    <span title="Autres TVA 17%" class="text-muted">+ € {{ user.tva_17|number_format(2, ',', ' ') }}</span>
                                {% endif %}
                                {% if user.secretary_tvac_17 == 0 and user.secretary_htva == 0 and user.tva_17 == 0 %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="fw-bold text-primary">€ {{ user.total_amount|number_format(2, ',', ' ') }}</td>
                            <td>
                                {% if user.invoice_id %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>{{ __('invoices.generated') | default('Générée') }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-warning">
                                        <i class="bi bi-clock me-1"></i>{{ __('invoices.pending') | default('En attente') }}
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.invoice_number %}
                                    <a href="{{ base_url }}/invoices/{{ user.invoice_id }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-eye me-1"></i>{{ user.invoice_number }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center py-4 text-muted">
                                {{ __('invoices.no_users_with_obligations') | default('Aucun utilisateur avec des obligations financières pour cette période') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.generating_loyer_invoices') | default('Génération des factures de loyer') }}</h5>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <p class="text-center mb-2" id="progressText">{{ __('invoices.processing') | default('Traitement en cours...') }}</p>
                <div class="progress" style="height: 25px;">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" id="progressBar" style="width: 0%">
                        0%
                    </div>
                </div>
                <div class="mt-3" id="progressDetails"></div>
            </div>
        </div>
    </div>
</div>

<!-- Results Modal -->
<div class="modal fade" id="resultsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('invoices.generation_results') | default('Résultats de la génération') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="resultsContent">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.close') }}</button>
                <button type="button" class="btn btn-primary" onclick="location.reload()">
                    <i class="bi bi-arrow-clockwise me-2"></i>{{ __('common.refresh') }}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Period form submission
    $('#periodForm').on('submit', function(e) {
        e.preventDefault();
        const month = $('#month').val();
        const year = $('#year').val();
        const userType = $('#userType').val();
        let url = '{{ base_url }}/invoices/bulk-loyer?month=' + month + '&year=' + year;
        if (userType) {
            url += '&userType=' + userType;
        }
        window.location.href = url;
    });
    
    // Select all checkbox
    $('#selectAll').on('change', function() {
        $('.user-checkbox:not(:disabled)').prop('checked', $(this).prop('checked'));
        updateGenerateButton();
    });
    
    // Individual checkbox change
    $('.user-checkbox').on('change', function() {
        updateGenerateButton();
    });
    
    // Update generate button state
    function updateGenerateButton() {
        const checkedCount = $('.user-checkbox:checked').length;
        $('#generateAllBtn').prop('disabled', checkedCount === 0);
        
        if (checkedCount > 0) {
            $('#generateAllBtn').html('<i class="bi bi-play-circle me-2"></i>' + 
                '{{ __("invoices.generate_selected") | default("Générer") }}' + ' (' + checkedCount + ')');
        } else {
            $('#generateAllBtn').html('<i class="bi bi-play-circle me-2"></i>' + 
                '{{ __("invoices.generate_all_selected") | default("Générer toutes les sélectionnées") }}');
        }
    }
    
    // Generate button click
    $('#generateAllBtn').on('click', function() {
        const selectedIds = [];
        $('.user-checkbox:checked').each(function() {
            selectedIds.push($(this).val());
        });
        
        if (selectedIds.length === 0) {
            alert('{{ __("invoices.please_select_users") | default("Veuillez sélectionner au moins un utilisateur") }}');
            return;
        }
        
        // Show progress modal
        const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
        progressModal.show();
        
        // Start generation
        generateInvoices(selectedIds);
    });
    
    // Generate invoices function
    function generateInvoices(userIds) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token }}');
        userIds.forEach(id => formData.append('user_ids[]', id));
        formData.append('month', '{{ month }}');
        formData.append('year', '{{ year }}');
        
        // Update progress
        let processed = 0;
        const total = userIds.length;
        updateProgress(0, total);
        
        fetch('{{ base_url }}/invoices/bulk-loyer/generate', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers.get('content-type'));
            
            // First get the response as text to debug
            return response.text().then(text => {
                console.log('Raw response:', text);
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}\nResponse: ${text}`);
                }
                
                // Try to parse as JSON
                try {
                    const data = JSON.parse(text);
                    return data;
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                    console.error('Response was:', text);
                    throw new Error('Response is not valid JSON. Check console for details.');
                }
            });
        })
        .then(data => {
            console.log('Parsed data:', data);
            
            // Hide progress modal
            bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
            
            if (data.success) {
                showResults(data.results);
            } else {
                alert(data.message || '{{ __("common.error_occurred") }}');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();
            
            alert('{{ __("common.error_occurred") }}: ' + error.message);
        });
    }
    
    // Update progress display
    function updateProgress(current, total) {
        const percentage = total > 0 ? Math.round((current / total) * 100) : 0;
        $('#progressBar').css('width', percentage + '%').text(percentage + '%');
        $('#progressText').text('{{ __("invoices.processing") | default("Traitement en cours") }}... ' + current + '/' + total);
    }
    
    // Show results
    function showResults(results) {
        let html = '<div class="row g-3 mb-4">';
        
        // Success card
        html += '<div class="col-md-6">';
        html += '<div class="card border-start border-4 border-success">';
        html += '<div class="card-body">';
        html += '<h6 class="text-success">{{ __("invoices.loyer_invoices_generated") | default("Factures de loyer générées") }}</h6>';
        html += '<h3 class="mb-0">' + results.success + '</h3>';
        html += '</div></div></div>';
        
        // Failed card
        html += '<div class="col-md-6">';
        html += '<div class="card border-start border-4 border-danger">';
        html += '<div class="card-body">';
        html += '<h6 class="text-danger">{{ __("invoices.generation_failed") | default("Échecs") }}</h6>';
        html += '<h3 class="mb-0">' + results.failed + '</h3>';
        html += '</div></div></div>';
        
        html += '</div>';
        
        // Success list
        if (results.invoices && results.invoices.length > 0) {
            html += '<h6 class="fw-bold mb-3">{{ __("invoices.generated_invoices") | default("Factures générées avec succès") }}</h6>';
            html += '<div class="list-group mb-4">';
            results.invoices.forEach(function(invoice) {
                html += '<a href="{{ base_url }}/invoices/' + invoice.invoice_id + '" class="list-group-item list-group-item-action">';
                html += '<div class="d-flex justify-content-between align-items-center">';
                html += '<div>';
                html += '<strong>' + invoice.user_name + '</strong>';
                html += '<div class="text-muted small">' + invoice.invoice_number + ' - €' + invoice.amount + '</div>';
                html += '</div>';
                html += '<i class="bi bi-arrow-right"></i>';
                html += '</div></a>';
            });
            html += '</div>';
        }
        
        // Error list
        if (results.errors && results.errors.length > 0) {
            html += '<h6 class="fw-bold mb-3 text-danger">{{ __("invoices.errors") | default("Erreurs") }}</h6>';
            html += '<div class="list-group">';
            results.errors.forEach(function(error) {
                html += '<div class="list-group-item list-group-item-danger">';
                html += '<strong>' + error.user_name + '</strong>';
                html += '<div class="text-danger small">' + error.error + '</div>';
                html += '</div>';
            });
            html += '</div>';
        }
        
        $('#resultsContent').html(html);
        
        // Show results modal
        const resultsModal = new bootstrap.Modal(document.getElementById('resultsModal'));
        resultsModal.show();
    }
});
</script>
{% endblock %}