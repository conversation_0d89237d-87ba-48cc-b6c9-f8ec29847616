<?php

namespace App\Controllers;

use Flight;
use App\Core\Controller;
use App\Models\Translation;

class TranslationController extends Controller
{
    private $translation;
    
    public function __construct()
    {
        $this->translation = new Translation();
    }
    
    /**
     * Show translation editor interface
     */
    public function index()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->query->language ?? 'fr';
        $group = Flight::request()->query->group ?? null;
        $search = Flight::request()->query->search ?? null;
        $showMissing = Flight::request()->query->show_missing ?? null;
        
        // Get available languages and groups
        $languages = $this->translation->getLanguages();
        if (empty($languages)) {
            $languages = ['fr', 'en']; // Default languages
        }
        
        $groups = $this->translation->getGroups($language);
        
        // Get translations
        try {
            if ($search) {
                $translations = $this->translation->searchTranslations($language, $search, $group);
            } else {
                $translations = $this->translation->getByLanguageAndGroup($language, $group);
            }
            
            // If show_missing is set, filter to show only missing translations
            if ($showMissing && $group) {
                // Get the reference language (opposite of current)
                $refLanguage = $language === 'fr' ? 'en' : 'fr';
                
                // Get all keys from reference language for this group
                $refTranslations = $this->translation->getByLanguageAndGroup($refLanguage, $group);
                $refKeys = array_column($refTranslations, 'key');
                
                // Get current language keys
                $currentKeys = array_column($translations, 'key');
                
                // Find missing keys
                $missingKeys = array_diff($refKeys, $currentKeys);
                
                // Add missing entries with empty values
                $missingTranslations = [];
                foreach ($missingKeys as $key) {
                    // Find the reference translation
                    $refTrans = array_filter($refTranslations, function($t) use ($key) {
                        return $t['key'] === $key;
                    });
                    $refTrans = reset($refTrans);
                    
                    $missingTranslations[] = [
                        'id' => null,
                        'language' => $language,
                        'group' => $group,
                        'key' => $key,
                        'value' => '',
                        'reference_value' => $refTrans ? $refTrans['value'] : '',
                        'is_missing' => true
                    ];
                }
                
                // Merge with existing translations or show only missing
                if ($showMissing === '1') {
                    $translations = $missingTranslations;
                } else {
                    $translations = array_merge($translations, $missingTranslations);
                }
            }
        } catch (\Exception $e) {
            // Log error and use empty array
            error_log('Translation fetch error: ' . $e->getMessage());
            $translations = [];
        }
        
        // Get statistics
        $stats = $this->translation->getStatistics($language);
        
        // Group translations by group if no specific group selected
        $groupedTranslations = [];
        foreach ($translations as $translation) {
            if (!isset($groupedTranslations[$translation['group']])) {
                $groupedTranslations[$translation['group']] = [];
            }
            $groupedTranslations[$translation['group']][] = $translation;
        }
        
        // Use appropriate template based on user preference
        $template = $this->getTemplate();
        $viewFile = 'translations/merged-editor-modern';
        
        // Build all translations data for the new view format
        $allTranslations = [];
        $processedKeys = [];
        
        // Get all unique group.key combinations
        foreach ($languages as $lang) {
            try {
                if ($search) {
                    $langTranslations = $this->translation->searchTranslations($lang, $search, $group);
                } else {
                    $langTranslations = $this->translation->getByLanguageAndGroup($lang, $group);
                }
                
                foreach ($langTranslations as $trans) {
                    $key = $trans['group'] . '.' . $trans['key'];
                    if (!isset($processedKeys[$key])) {
                        $processedKeys[$key] = [
                            'group' => $trans['group'],
                            'key' => $trans['key'],
                            'values' => []
                        ];
                    }
                    $processedKeys[$key]['values'][$lang] = $trans['value'];
                }
            } catch (\Exception $e) {
                error_log('Translation fetch error: ' . $e->getMessage());
            }
        }
        
        // Fill missing language values
        foreach ($processedKeys as &$translation) {
            foreach ($languages as $lang) {
                if (!isset($translation['values'][$lang])) {
                    $translation['values'][$lang] = null;
                }
            }
        }
        
        // Filter by missing if requested
        if ($showMissing) {
            $processedKeys = array_filter($processedKeys, function($trans) use ($language) {
                return empty($trans['values'][$language]);
            });
        }
        
        // Convert to indexed array for template
        $allTranslations = array_values($processedKeys);
        
        $this->render($viewFile, [
            'title' => __('config.translation_editor'),
            'languages' => $languages,
            'currentLanguage' => $language,
            'groups' => $groups,
            'currentGroup' => $group,
            'search' => $search,
            'showMissing' => $showMissing,
            'translations' => $translations,
            'allTranslations' => $allTranslations,
            'groupedTranslations' => $groupedTranslations,
            'stats' => $stats
        ]);
    }
    
    /**
     * Update single translation via AJAX
     */
    public function update()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->data->language;
        $group = Flight::request()->data->group;
        $key = Flight::request()->data->key;
        $value = Flight::request()->data->value;
        
        if (!$language || !$group || !$key) {
            Flight::json(['success' => false, 'message' => __('common.invalid_request')], 400);
            return;
        }
        
        $result = $this->translation->updateTranslation($language, $group, $key, $value);
        
        if ($result) {
            // Clear language cache
            $this->clearLanguageCache($language);
            
            Flight::json([
                'success' => true,
                'message' => __('config.translation_updated')
            ]);
        } else {
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Delete translation via AJAX
     */
    public function delete()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->data->language;
        $group = Flight::request()->data->group;
        $key = Flight::request()->data->key;
        
        if (!$language || !$group || !$key) {
            Flight::json(['success' => false, 'message' => __('common.invalid_request')], 400);
            return;
        }
        
        $result = $this->translation->deleteTranslation($language, $group, $key);
        
        if ($result) {
            // Clear language cache
            $this->clearLanguageCache($language);
            
            Flight::json([
                'success' => true,
                'message' => __('config.translation_deleted')
            ]);
        } else {
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Handle file upload and import
     */
    public function import()
    {
        // TODO: Add permission check when authentication system is implemented
        
        if (!isset($_FILES['translation_file'])) {
            setMessage('error', __('config.no_file_uploaded'));
            Flight::redirect('/translations');
            return;
        }
        
        $file = $_FILES['translation_file'];
        $language = Flight::request()->data->language ?? 'fr';
        $importType = Flight::request()->data->import_type ?? 'merge';
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            setMessage('error', __('config.file_upload_error'));
            Flight::redirect('/translations');
            return;
        }
        
        $allowedTypes = ['application/json', 'text/json', 'text/plain'];
        if (!in_array($file['type'], $allowedTypes)) {
            setMessage('error', __('config.invalid_file_type'));
            Flight::redirect('/translations');
            return;
        }
        
        // Read and parse file
        $content = file_get_contents($file['tmp_name']);
        $data = json_decode($content, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            setMessage('error', __('config.invalid_json_format'));
            Flight::redirect('/translations');
            return;
        }
        
        // Import translations
        $userId = Flight::get('user')['id'] ?? null;
        $result = $this->translation->importTranslations($language, $data, $importType, $userId);
        
        if ($result['success']) {
            // Clear language cache
            $this->clearLanguageCache($language);
            
            $message = sprintf(
                __('config.import_success'),
                $result['keys_imported'],
                $result['keys_added'],
                $result['keys_updated']
            );
            setMessage('success', $message);
        } else {
            setMessage('error', __('config.import_error') . ': ' . $result['error']);
        }
        
        Flight::redirect('/translations?language=' . $language);
    }
    
    /**
     * Export translations to file
     */
    public function export()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->query->language ?? 'fr';
        $group = Flight::request()->query->group ?? null;
        $format = Flight::request()->query->format ?? 'json';
        
        $translations = $this->translation->exportTranslations($language, $group);
        
        if ($format === 'php') {
            // Export as PHP array
            $filename = $group ? "{$language}_{$group}.php" : "{$language}_all.php";
            $content = "<?php\n\nreturn " . var_export($translations, true) . ";\n";
            $contentType = 'application/x-php';
        } else {
            // Export as JSON
            $filename = $group ? "{$language}_{$group}.json" : "{$language}_all.json";
            $content = json_encode($translations, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            $contentType = 'application/json';
        }
        
        // Send file
        header('Content-Type: ' . $contentType);
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($content));
        echo $content;
        exit;
    }
    
    /**
     * Sync translations from files to database
     */
    public function sync()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->data->language ?? null;
        $languageDir = __DIR__ . '/../lang';
        
        $results = $this->translation->syncFromFiles($languageDir, $language);
        
        $totalKeys = 0;
        $totalFiles = 0;
        foreach ($results as $result) {
            $totalKeys += $result['keys_imported'];
            $totalFiles += $result['files_processed'];
        }
        
        setMessage('success', sprintf(
            __('config.sync_success'),
            $totalKeys,
            $totalFiles
        ));
        
        Flight::redirect('/translations');
    }
    
    /**
     * Add new translation
     */
    public function add()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->data->language;
        $group = Flight::request()->data->group;
        $key = Flight::request()->data->key;
        $value = Flight::request()->data->value;
        
        if (!$language || !$group || !$key) {
            Flight::json(['success' => false, 'message' => __('common.invalid_request')], 400);
            return;
        }
        
        // Check if translation already exists
        $existing = $this->translation->getByLanguageAndGroup($language, $group);
        foreach ($existing as $translation) {
            if ($translation['key'] === $key) {
                Flight::json([
                    'success' => false,
                    'message' => __('config.translation_key_exists')
                ], 400);
                return;
            }
        }
        
        $result = $this->translation->updateTranslation($language, $group, $key, $value);
        
        if ($result) {
            // Clear language cache
            $this->clearLanguageCache($language);
            
            Flight::json([
                'success' => true,
                'message' => __('config.translation_added')
            ]);
        } else {
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Show multilingual editor interface
     */
    public function multilingual()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $selectedLanguages = Flight::request()->query->languages ?? null;
        $group = Flight::request()->query->group ?? null;
        $search = Flight::request()->query->search ?? null;
        $page = Flight::request()->query->page ?? 1;
        $perPage = 50;
        
        // Get available languages and groups
        $allLanguages = $this->translation->getLanguages();
        if (empty($allLanguages)) {
            $allLanguages = ['fr', 'en']; // Default languages
        }
        
        // Parse selected languages
        if ($selectedLanguages) {
            $displayLanguages = explode(',', $selectedLanguages);
            $displayLanguages = array_intersect($displayLanguages, $allLanguages);
        } else {
            // Default to first 3 languages
            $displayLanguages = array_slice($allLanguages, 0, 3);
        }
        
        // Ensure at least 2 languages are selected
        if (count($displayLanguages) < 2) {
            $displayLanguages = array_slice($allLanguages, 0, 2);
        }
        
        $groups = $this->translation->getGroups($allLanguages[0]);
        
        // Get all unique keys for the selected group/search
        $allKeys = [];
        $keyGroups = [];
        
        foreach ($allLanguages as $lang) {
            try {
                if ($search) {
                    $translations = $this->translation->searchTranslations($lang, $search, $group);
                } else {
                    $translations = $this->translation->getByLanguageAndGroup($lang, $group);
                }
                
                foreach ($translations as $translation) {
                    $keyIdentifier = $translation['group'] . '.' . $translation['key'];
                    if (!in_array($keyIdentifier, $allKeys)) {
                        $allKeys[] = $keyIdentifier;
                        $keyGroups[$keyIdentifier] = [
                            'group' => $translation['group'],
                            'key' => $translation['key']
                        ];
                    }
                }
            } catch (\Exception $e) {
                error_log('Translation fetch error: ' . $e->getMessage());
            }
        }
        
        // Sort keys
        sort($allKeys);
        
        // Paginate
        $totalKeys = count($allKeys);
        $totalPages = ceil($totalKeys / $perPage);
        $page = max(1, min($page, $totalPages));
        $offset = ($page - 1) * $perPage;
        $pageKeys = array_slice($allKeys, $offset, $perPage);
        
        // Build grouped translations for display
        $groupedTranslations = [];
        foreach ($pageKeys as $keyIdentifier) {
            $keyInfo = $keyGroups[$keyIdentifier];
            $translationGroup = [
                'group' => $keyInfo['group'],
                'key' => $keyInfo['key'],
                'translations' => [],
                'missing' => []
            ];
            
            // Get translation for each language
            foreach ($displayLanguages as $lang) {
                $value = $this->translation->getTranslation($lang, $keyInfo['group'], $keyInfo['key']);
                $translationGroup['translations'][$lang] = $value;
                if (!$value) {
                    $translationGroup['missing'][] = $lang;
                }
            }
            
            $groupedTranslations[$keyIdentifier] = $translationGroup;
        }
        
        // Build query string for pagination
        $queryParams = [];
        if ($selectedLanguages) $queryParams['languages'] = $selectedLanguages;
        if ($group) $queryParams['group'] = $group;
        if ($search) $queryParams['search'] = $search;
        $queryString = http_build_query($queryParams);
        
        // Use appropriate template based on user preference
        $template = $this->getTemplate();
        $viewFile = 'translations/editor-multilang-modern';
        
        $this->render($viewFile, [
            'title' => __('config.translation_editor'),
            'allLanguages' => $allLanguages,
            'displayLanguages' => $displayLanguages,
            'groups' => $groups,
            'currentGroup' => $group,
            'search' => $search,
            'groupedTranslations' => $groupedTranslations,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'queryString' => $queryString
        ]);
    }
    
    /**
     * Save a single translation via AJAX
     */
    public function save()
    {
        $lang = Flight::request()->data->lang;
        $group = Flight::request()->data->group;
        $key = Flight::request()->data->key;
        $value = Flight::request()->data->value;
        
        if (!$lang || !$group || !$key) {
            Flight::json([
                'success' => false,
                'message' => __('common.required_fields_missing')
            ], 400);
            return;
        }
        
        try {
            $result = $this->translation->saveTranslation($lang, $group, $key, $value);
            
            Flight::json([
                'success' => true,
                'message' => __('translations.translation_saved')
            ]);
        } catch (\Exception $e) {
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Get translations for a specific key across all languages (AJAX)
     */
    public function getMultilang()
    {
        $group = Flight::request()->query->group;
        $key = Flight::request()->query->key;
        
        if (!$group || !$key) {
            Flight::json([
                'success' => false,
                'message' => __('common.required_fields_missing')
            ], 400);
            return;
        }
        
        $languages = $this->translation->getLanguages();
        $translations = [];
        
        foreach ($languages as $lang) {
            $value = $this->translation->getTranslation($lang, $group, $key);
            $translations[$lang] = $value ?: '';
        }
        
        Flight::json([
            'success' => true,
            'translations' => $translations,
            'languages' => $languages
        ]);
    }
    
    /**
     * Delete a translation key (AJAX)
     */
    public function deleteKey()
    {
        $lang = Flight::request()->data->lang;
        $group = Flight::request()->data->group;
        $key = Flight::request()->data->key;
        
        if (!$lang || !$group || !$key) {
            Flight::json([
                'success' => false,
                'message' => __('common.required_fields_missing')
            ], 400);
            return;
        }
        
        try {
            $result = $this->translation->deleteTranslation($lang, $group, $key);
            
            Flight::json([
                'success' => true,
                'message' => __('translations.translation_deleted')
            ]);
        } catch (\Exception $e) {
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Show translation diagnostic interface
     */
    public function diagnostic()
    {
        // TODO: Add permission check when authentication system is implemented
        
        // Debug: Log that we reached this method
        error_log('TranslationController::diagnostic() called');
        
        try {
            $db = Flight::db();
            error_log('Database connection obtained');
            
            // Get counts by group and language
            $stmt = $db->query("
                SELECT 
                    `group`, 
                    language,
                    COUNT(*) as count
                FROM translations
                GROUP BY `group`, language
                ORDER BY `group`, language
            ");
            $groupCounts = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Build a matrix
            $matrix = [];
            $languages = [];
            foreach ($groupCounts as $row) {
                $matrix[$row['group']][$row['language']] = $row['count'];
                if (!in_array($row['language'], $languages)) {
                    $languages[] = $row['language'];
                }
            }
            sort($languages);
            
            // Check for duplicate keys
            $stmt = $db->query("
                SELECT 
                    language, 
                    `group`, 
                    `key`, 
                    COUNT(*) as duplicates
                FROM translations
                GROUP BY language, `group`, `key`
                HAVING COUNT(*) > 1
            ");
            $duplicates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Find missing translations per language
            $missingTranslations = [];
            foreach ($matrix as $group => $langs) {
                foreach ($languages as $lang) {
                    if (!isset($langs[$lang]) || $langs[$lang] == 0) {
                        if (!isset($missingTranslations[$lang])) {
                            $missingTranslations[$lang] = [];
                        }
                        $missingTranslations[$lang][] = $group;
                    }
                }
            }
            
            // Find keys that exist in one language but not others
            $inconsistentKeys = [];
            foreach ($matrix as $group => $langs) {
                $counts = array_values($langs);
                if (count(array_unique($counts)) > 1) {
                    $inconsistentKeys[$group] = $langs;
                }
            }
            
            // Get template preference
            $template = $this->getTemplate();
            $viewFile = 'translations/diagnostic-modern';
            
            // Debug data
            error_log('Matrix count: ' . count($matrix));
            error_log('Languages: ' . implode(', ', $languages));
            error_log('Duplicates count: ' . count($duplicates));
            
            $this->render($viewFile, [
                'title' => __('config.translation_diagnostic'),
                'matrix' => $matrix,
                'languages' => $languages,
                'duplicates' => $duplicates,
                'missingTranslations' => $missingTranslations,
                'inconsistentKeys' => $inconsistentKeys
            ]);
            
            error_log('Render completed successfully');
            
        } catch (\Exception $e) {
            error_log('Translation diagnostic error: ' . $e->getMessage());
            error_log('Error file: ' . $e->getFile());
            error_log('Error line: ' . $e->getLine());
            error_log('Error trace: ' . $e->getTraceAsString());
            
            // Try to identify specific error types
            if (strpos($e->getMessage(), 'Unable to find template') !== false) {
                error_log('Template not found error');
            }
            if (strpos($e->getMessage(), 'undefined') !== false) {
                error_log('Undefined variable/function error');
            }
            
            setMessage('error', 'Diagnostic error: ' . $e->getMessage());
            redirect_to('/translations');
        }
    }
    
    /**
     * Fix missing translations
     */
    public function fixMissing()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->data->language ?? null;
        $group = Flight::request()->data->group ?? null;
        $action = Flight::request()->data->action ?? 'copy';
        
        if (!$language || !$group) {
            Flight::json(['success' => false, 'message' => __('common.invalid_request')], 400);
            return;
        }
        
        try {
            $db = Flight::db();
            $db->beginTransaction();
            
            // Get reference language (usually English)
            $refLang = $language === 'en' ? 'fr' : 'en';
            
            // Get all keys from reference language for this group
            $stmt = $db->prepare("
                SELECT `key`, value 
                FROM translations 
                WHERE language = ? AND `group` = ?
            ");
            $stmt->execute([$refLang, $group]);
            $referenceKeys = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Check which keys are missing in target language
            $missingCount = 0;
            foreach ($referenceKeys as $ref) {
                $checkStmt = $db->prepare("
                    SELECT id FROM translations 
                    WHERE language = ? AND `group` = ? AND `key` = ?
                ");
                $checkStmt->execute([$language, $group, $ref['key']]);
                
                if (!$checkStmt->fetch()) {
                    // Key is missing, add it
                    $value = $action === 'copy' ? $ref['value'] : '[' . $ref['value'] . ']';
                    
                    $insertStmt = $db->prepare("
                        INSERT INTO translations (language, `group`, `key`, value) 
                        VALUES (?, ?, ?, ?)
                    ");
                    $insertStmt->execute([$language, $group, $ref['key'], $value]);
                    $missingCount++;
                }
            }
            
            $db->commit();
            
            // Clear language cache
            $this->clearLanguageCache($language);
            
            Flight::json([
                'success' => true,
                'message' => sprintf(__('config.missing_translations_fixed'), $missingCount, $language, $group)
            ]);
            
        } catch (\Exception $e) {
            $db->rollBack();
            error_log('Fix missing translations error: ' . $e->getMessage());
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Sync translations to language files
     */
    public function syncToFiles()
    {
        // TODO: Add permission check when authentication system is implemented
        
        $language = Flight::request()->data->language ?? null;
        
        try {
            $langDir = dirname(__DIR__) . '/lang';
            
            if ($language) {
                $languages = [$language];
            } else {
                // Sync all languages
                $languages = $this->translation->getLanguages();
            }
            
            $totalSynced = 0;
            
            foreach ($languages as $lang) {
                $langPath = $langDir . '/' . $lang;
                
                // Create language directory if it doesn't exist
                if (!is_dir($langPath)) {
                    mkdir($langPath, 0755, true);
                }
                
                // Get all translations for this language
                $translations = $this->translation->exportTranslations($lang);
                
                foreach ($translations as $group => $keys) {
                    $filePath = $langPath . '/' . $group . '.php';
                    
                    // Generate PHP array content
                    $content = "<?php\n\nreturn " . var_export($keys, true) . ";\n";
                    
                    // Write to file
                    file_put_contents($filePath, $content);
                    $totalSynced++;
                }
            }
            
            Flight::json([
                'success' => true,
                'message' => sprintf(__('config.sync_to_files_success'), $totalSynced)
            ]);
            
        } catch (\Exception $e) {
            error_log('Sync to files error: ' . $e->getMessage());
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Clear language cache
     */
    private function clearLanguageCache($language)
    {
        // This would be implemented if you have a caching system
        // For now, it's a placeholder for future cache clearing
    }
}