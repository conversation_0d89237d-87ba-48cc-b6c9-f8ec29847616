<?php

namespace App\Models;

use App\Core\Model;

class ConfigPaymentTerm extends Model
{
    protected $table = 'config_payment_terms';
    
    protected $fillable = [
        'code',
        'name',
        'description',
        'days',
        'discount_days',
        'discount_percentage',
        'is_default',
        'is_active',
        'created_by',
        'updated_by'
    ];
    
    protected $casts = [
        'days' => 'integer',
        'discount_days' => 'integer',
        'discount_percentage' => 'decimal:2',
        'is_default' => 'boolean',
        'is_active' => 'boolean'
    ];
    
    /**
     * Get localized name
     */
    public function getLocalizedName($locale = null)
    {
        if (!$locale) {
            $locale = $_SESSION['language'] ?? 'en';
        }
        
        $names = json_decode($this->name, true);
        
        if (is_array($names)) {
            return $names[$locale] ?? $names['en'] ?? reset($names);
        }
        
        return $this->name;
    }
    
    /**
     * Get localized description
     */
    public function getLocalizedDescription($locale = null)
    {
        if (!$this->description) {
            return '';
        }
        
        if (!$locale) {
            $locale = $_SESSION['language'] ?? 'en';
        }
        
        $descriptions = json_decode($this->description, true);
        
        if (is_array($descriptions)) {
            return $descriptions[$locale] ?? $descriptions['en'] ?? reset($descriptions);
        }
        
        return $this->description;
    }
    
    /**
     * Calculate due date based on issue date
     */
    public function calculateDueDate($issueDate)
    {
        $date = new \DateTime($issueDate);
        $date->add(new \DateInterval('P' . $this->days . 'D'));
        return $date->format('Y-m-d');
    }
    
    /**
     * Get display name (alias for getLocalizedName)
     */
    public function getDisplayName($locale = null)
    {
        return $this->getLocalizedName($locale);
    }
}