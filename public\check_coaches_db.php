<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/database.php';

use App\Models\User;
use App\Models\UserGroupMember;

header('Content-Type: text/plain; charset=utf-8');

try {
    $pdo = new PDO(
        "mysql:host={$config['database']['host']};dbname={$config['database']['name']};charset=utf8mb4",
        $config['database']['username'],
        $config['database']['password']
    );
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Checking Coaches in Database ===\n\n";
    
    // 1. Check if coach group exists
    $stmt = $pdo->prepare("SELECT * FROM user_groups WHERE id = 24");
    $stmt->execute();
    $coachGroup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($coachGroup) {
        echo "✓ Coach group (ID: 24) exists: {$coachGroup['name']}\n\n";
    } else {
        echo "✗ Coach group (ID: 24) NOT FOUND!\n\n";
    }
    
    // 2. Check users in coach group
    echo "=== Users in Coach Group ===\n";
    $query = "
        SELECT u.*, ugm.group_id 
        FROM users u
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 24
        ORDER BY u.name
    ";
    
    $stmt = $pdo->query($query);
    $coaches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($coaches) . " users in coach group:\n\n";
    
    foreach ($coaches as $coach) {
        echo "ID: {$coach['id']}, Name: {$coach['name']}\n";
        echo "  - Email: {$coach['email']}\n";
        echo "  - Active: " . ($coach['is_active'] ? 'Yes' : 'No') . "\n";
        echo "  - Can be invoiced: " . ($coach['can_be_invoiced'] ? 'Yes' : 'No') . "\n";
        echo "  - Has courses: " . ($coach['has_courses'] ? 'Yes' : 'No') . "\n";
        echo "\n";
    }
    
    // 3. Check billable coaches specifically
    echo "=== Billable Coaches (can_be_invoiced = 1 AND is_active = 1) ===\n";
    $query = "
        SELECT u.* 
        FROM users u
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = 24
        AND u.can_be_invoiced = 1
        AND u.is_active = 1
        ORDER BY u.name
    ";
    
    $stmt = $pdo->query($query);
    $billableCoaches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($billableCoaches) . " billable coaches:\n\n";
    
    foreach ($billableCoaches as $coach) {
        echo "ID: {$coach['id']}, Name: {$coach['name']} (Email: {$coach['email']})\n";
    }
    
    // 4. Check the exact query used in UserController
    echo "\n=== Testing UserController Query ===\n";
    $query = "
        SELECT u.* 
        FROM users u
        INNER JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = :group_id
        AND u.can_be_invoiced = 1
        AND u.is_active = 1
        ORDER BY u.name
    ";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute(['group_id' => 24]);
    $controllerResult = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "UserController query would return " . count($controllerResult) . " coaches\n";
    
    // 5. Check if there are ANY active users that can be invoiced
    echo "\n=== All Active Billable Users (any group) ===\n";
    $stmt = $pdo->query("SELECT COUNT(*) FROM users WHERE is_active = 1 AND can_be_invoiced = 1");
    $totalBillable = $stmt->fetchColumn();
    echo "Total active billable users: $totalBillable\n";
    
    // 6. Check user_group_members table structure
    echo "\n=== User Group Members Table Structure ===\n";
    $stmt = $pdo->query("DESCRIBE user_group_members");
    $structure = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($structure as $col) {
        echo "{$col['Field']} - {$col['Type']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}