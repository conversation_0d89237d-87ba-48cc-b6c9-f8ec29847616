<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\CatalogItem;
use App\Models\CatalogCategory;
use App\Models\ConfigVatRate;
use App\Models\StockMovement;
use Flight;

class ProductController extends Controller
{
    /**
     * List all products
     */
    public function index()
    {
        $page = (int) (Flight::request()->query->page ?? 1);
        $perPage = (int) (Flight::request()->query->per_page ?? 25);
        $search = Flight::request()->query->search ?? '';
        $category = Flight::request()->query->category ?? '';
        $type = Flight::request()->query->type ?? '';
        $stockStatus = Flight::request()->query->stock_status ?? '';
        
        // Build query
        $db = CatalogItem::db();
        $where = [];
        $params = [];
        
        // Base query
        $sql = "SELECT ci.*, cc.name as category_name, cc.icon as category_icon, 
                cvr.rate as vat_rate, cvr.name as vat_name
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE 1=1";
        
        // Apply filters
        if ($search) {
            $sql .= " AND (ci.code LIKE :search OR ci.name LIKE :search2 OR ci.description LIKE :search3)";
            $params['search'] = "%{$search}%";
            $params['search2'] = "%{$search}%";
            $params['search3'] = "%{$search}%";
        }
        
        if ($category) {
            $sql .= " AND ci.category_id = :category";
            $params['category'] = $category;
        }
        
        if ($type) {
            $sql .= " AND ci.item_type = :type";
            $params['type'] = $type;
        }
        
        if ($stockStatus) {
            switch ($stockStatus) {
                case 'low':
                    $sql .= " AND ci.is_stockable = 1 AND ci.current_stock <= ci.low_stock_alert AND ci.current_stock > 0";
                    break;
                case 'out':
                    $sql .= " AND ci.is_stockable = 1 AND ci.current_stock <= 0";
                    break;
                case 'in':
                    $sql .= " AND ci.is_stockable = 1 AND ci.current_stock > ci.low_stock_alert";
                    break;
            }
        }
        
        // Count total records
        $countSql = "SELECT COUNT(*) as total FROM ($sql) as count_query";
        $countStmt = $db->prepare($countSql);
        $countStmt->execute($params);
        $total = $countStmt->fetch(\PDO::FETCH_ASSOC)['total'];
        
        // Add pagination
        $sql .= " ORDER BY ci.name ASC";
        $offset = ($page - 1) * $perPage;
        $sql .= " LIMIT :limit OFFSET :offset";
        $params['limit'] = $perPage;
        $params['offset'] = $offset;
        
        // Execute query
        $stmt = $db->prepare($sql);
        foreach ($params as $key => $value) {
            if ($key === 'limit' || $key === 'offset') {
                $stmt->bindValue($key, $value, \PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }
        $stmt->execute();
        
        // Build results
        $products = [];
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $product = new CatalogItem();
            $product->fill($row);
            
            // Parse category name if it's JSON
            $categoryName = $row['category_name'];
            if ($categoryName && strpos($categoryName, '{') === 0) {
                $names = json_decode($categoryName, true);
                if ($names) {
                    // Use user's language preference, fallback to French, then English, then any available
                    $userLang = $_SESSION['user_language'] ?? 'fr';
                    $categoryName = $names[$userLang] ?? $names['fr'] ?? $names['en'] ?? reset($names);
                }
            }
            
            // Add loaded relationships
            $product->category = (object) [
                'id' => $row['category_id'],
                'name' => $categoryName,
                'icon' => $row['category_icon']
            ];
            $product->vatRate = (object) [
                'id' => $row['vat_rate_id'],
                'rate' => $row['vat_rate'],
                'name' => $row['vat_name']
            ];
            
            $products[] = $product;
        }
        
        // Pagination data
        $totalPages = ceil($total / $perPage);
        $pagination = [
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => $totalPages,
            'has_pages' => $totalPages > 1
        ];
        
        // Get categories for filter
        $categorySql = "SELECT * FROM catalog_categories WHERE is_active = 1 ORDER BY sort_order ASC";
        $categoryStmt = $db->query($categorySql);
        $categories = [];
        while ($row = $categoryStmt->fetch(\PDO::FETCH_ASSOC)) {
            $cat = new CatalogCategory();
            $cat->fill($row);
            // Add display name for template
            $cat->display_name = $cat->getDisplayName();
            $categories[] = $cat;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/index-' . $template;
        
        $this->render($viewName, [
            'products' => $products,
            'pagination' => $pagination,
            'categories' => $categories,
            'filters' => [
                'search' => $search,
                'category' => $category,
                'type' => $type,
                'stock_status' => $stockStatus
            ]
        ]);
    }
    
    /**
     * Show create form
     */
    public function create()
    {
        $categories = CatalogCategory::all();
        $vatRates = ConfigVatRate::where('is_active', '=', 1)->get();
        
        // Process categories to add display names
        foreach ($categories as $category) {
            $category->display_name = $category->getDisplayName();
        }
        
        // Process VAT rates to add display names
        foreach ($vatRates as $rate) {
            if (is_string($rate->name) && strpos($rate->name, '{') === 0) {
                $names = json_decode($rate->name, true);
                $rate->display_name = $names[$_SESSION['user_language'] ?? 'fr'] ?? $names['fr'] ?? $rate->name;
            } else {
                $rate->display_name = $rate->name;
            }
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/create-' . $template;
        
        $this->render($viewName, [
            'categories' => $categories,
            'vatRates' => $vatRates,
            'defaultCode' => CatalogItem::generateCode()
        ]);
    }
    
    /**
     * Store new product
     */
    public function store()
    {
        // Include validation helper
        require_once __DIR__ . '/../helpers/validation.php';
        
        // Get data from POST since it's sent as FormData
        $data = $_POST;
        
        // Map field names to match validation rules
        $validationData = [
            'name' => $data['name'] ?? '',
            'category_id' => $data['category_id'] ?? '',
            'price' => $data['unit_price'] ?? '',
            'cost' => $data['purchase_price'] ?? '',
            'stock' => $data['current_stock'] ?? 0,
            'sku' => $data['code'] ?? '',
            'barcode' => $data['barcode'] ?? ''
        ];
        
        // Validate using the validation helper
        $validation = validateForm($validationData, getProductValidationRules());
        
        if (!$validation['valid']) {
            // Convert validation errors to the expected format
            $errors = [];
            foreach ($validation['errors'] as $error) {
                // Map back to original field names
                $fieldMap = [
                    'price' => 'unit_price',
                    'cost' => 'purchase_price',
                    'stock' => 'current_stock',
                    'sku' => 'code'
                ];
                $field = $fieldMap[$error['field']] ?? $error['field'];
                $errors[$field] = $error['message'];
            }
            
            Flight::json(['success' => false, 'errors' => $errors], 422);
            return;
        }
        
        try {
            // Create product
            $product = CatalogItem::create([
                'code' => $data['code'],
                'name' => $data['name'],
                'category_id' => $data['category_id'],
                'item_type' => $data['item_type'],
                'description' => $data['description'] ?? null,
                'unit_price' => $data['unit_price'],
                'vat_rate_id' => $data['vat_rate_id'],
                'is_stockable' => isset($data['is_stockable']) ? 1 : 0,
                'current_stock' => $data['current_stock'] ?? 0,
                'low_stock_alert' => $data['low_stock_alert'] ?? 5,
                'quick_sale_button' => isset($data['quick_sale_button']) ? 1 : 0,
                'button_color' => $data['button_color'] ?? '#007bff',
                'button_order' => $data['button_order'] ?? 0,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            // If initial stock provided, create stock movement
            if ($product->is_stockable && $product->current_stock > 0) {
                StockMovement::create([
                    'item_id' => $product->id,
                    'movement_type' => 'adjustment',
                    'quantity' => $product->current_stock,
                    'notes' => 'Initial stock',
                    'created_by' => $_SESSION['user_id'] ?? null
                ]);
            }
            
            // Remove flash message since it might not be available
            Flight::json(['success' => true, 'redirect' => $this->url('/products/' . $product->id)]);
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error creating product: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Show product details
     */
    public function show($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::halt(404, 'Product not found');
            return;
        }
        $product->loadCategory();
        $product->loadVatRate();
        
        // Get recent stock movements
        $db = StockMovement::db();
        $stmt = $db->prepare("
            SELECT sm.*, CONCAT(u.first_name, ' ', u.last_name) as user_name
            FROM stock_movements sm
            LEFT JOIN users u ON sm.created_by = u.id
            WHERE sm.item_id = :item_id
            ORDER BY sm.created_at DESC
            LIMIT 10
        ");
        $stmt->execute(['item_id' => $id]);
        $stockMovements = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Get sales history
        try {
            $stmt = $db->prepare("
                SELECT sil.*, si.invoice_number, si.issue_date, c.name as client_name
                FROM sales_invoice_lines sil
                JOIN sales_invoices si ON sil.invoice_id = si.id
                JOIN clients c ON si.client_id = c.id
                WHERE sil.item_id = :item_id
                ORDER BY si.issue_date DESC
                LIMIT 10
            ");
            $stmt->execute(['item_id' => $id]);
            $salesHistory = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            // If there's an error (e.g., no sales yet), just set empty array
            $salesHistory = [];
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/show-' . $template;
        
        $this->render($viewName, [
            'product' => $product,
            'stockMovements' => $stockMovements,
            'salesHistory' => $salesHistory
        ]);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::halt(404, 'Product not found');
            return;
        }
        $categories = CatalogCategory::all();
        $vatRates = ConfigVatRate::where('is_active', '=', 1)->get();
        
        // Process categories to add display names
        foreach ($categories as $category) {
            $category->display_name = $category->getDisplayName();
        }
        
        // Process VAT rates to add display names
        foreach ($vatRates as $rate) {
            if (is_string($rate->name) && strpos($rate->name, '{') === 0) {
                $names = json_decode($rate->name, true);
                $rate->display_name = $names[$_SESSION['user_language'] ?? 'fr'] ?? $names['fr'] ?? $rate->name;
            } else {
                $rate->display_name = $rate->name;
            }
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'products/edit-' . $template;
        
        $this->render($viewName, [
            'product' => $product,
            'categories' => $categories,
            'vatRates' => $vatRates
        ]);
    }
    
    /**
     * Update product
     */
    public function update($id)
    {
        // Force JSON response header
        header('Content-Type: application/json');
        
        $product = CatalogItem::find($id);
        if (!$product) {
            echo json_encode(['success' => false, 'message' => 'Product not found']);
            exit;
        }
        
        // Get data from POST since it's sent as FormData
        $data = $_POST;
        
        // Validate
        $errors = $this->validateProduct($data, $id);
        if (!empty($errors)) {
            echo json_encode(['success' => false, 'errors' => $errors]);
            exit;
        }
        
        try {
            // Update product
            $product->update([
                'code' => $data['code'],
                'name' => $data['name'],
                'category_id' => $data['category_id'],
                'item_type' => $data['item_type'],
                'description' => $data['description'] ?? null,
                'unit_price' => $data['unit_price'],
                'vat_rate_id' => $data['vat_rate_id'],
                'is_stockable' => isset($data['is_stockable']) ? 1 : 0,
                'low_stock_alert' => $data['low_stock_alert'] ?? 5,
                'quick_sale_button' => isset($data['quick_sale_button']) ? 1 : 0,
                'button_color' => $data['button_color'] ?? '#007bff',
                'button_order' => $data['button_order'] ?? 0,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            // Remove flash message since it might not be available
            echo json_encode(['success' => true, 'redirect' => $this->url('/products/' . $product->id)]);
            exit;
            
        } catch (\Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Error updating product: ' . $e->getMessage()]);
            exit;
        }
    }
    
    /**
     * Adjust stock
     */
    public function adjustStock($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::json(['success' => false, 'message' => 'Product not found'], 404);
            return;
        }
        
        if (!$product->is_stockable) {
            Flight::json(['success' => false, 'message' => 'This product is not stockable'], 400);
            return;
        }
        
        // Get data from POST since it's sent as FormData
        $data = $_POST;
        
        // Validate
        if (!isset($data['quantity']) || !is_numeric($data['quantity'])) {
            Flight::json(['success' => false, 'message' => 'Invalid quantity'], 422);
            return;
        }
        
        try {
            $movement = $product->adjustStock(
                $data['quantity'],
                $data['type'] ?? 'adjustment',
                null,
                $_SESSION['user_id'] ?? null
            );
            
            if (isset($data['notes'])) {
                $movement->update(['notes' => $data['notes']]);
            }
            
            Flight::json([
                'success' => true,
                'current_stock' => $product->current_stock,
                'message' => 'Stock adjusted successfully'
            ]);
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error adjusting stock: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Delete product
     */
    public function destroy($id)
    {
        $product = CatalogItem::find($id);
        if (!$product) {
            Flight::json(['success' => false, 'message' => 'Product not found'], 404);
            return;
        }
        
        // Check if product has been used
        $db = CatalogItem::db();
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM sales_invoice_lines WHERE item_id = :id");
        $stmt->execute(['id' => $id]);
        $hasHistory = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
        
        if ($hasHistory) {
            // Soft delete by deactivating
            $product->update(['is_active' => 0]);
            $message = 'Product deactivated (has sales history)';
        } else {
            // Hard delete
            $product->delete();
            $message = 'Product deleted successfully';
        }
        
        // Remove flash message since it might not be available
        Flight::json(['success' => true, 'redirect' => $this->url('/products')]);
    }
    
    /**
     * Quick sale items API
     */
    public function quickSaleItems()
    {
        $items = CatalogItem::getQuickSaleItems();
        
        $result = [];
        foreach ($items as $item) {
            $item->loadVatRate();
            $result[] = [
                'id' => $item->id,
                'code' => $item->code,
                'name' => $item->name,
                'price' => $item->unit_price,
                'price_with_vat' => $item->getPriceWithVat(),
                'vat_rate' => $item->vatRate ? $item->vatRate->rate : 0,
                'color' => $item->button_color,
                'in_stock' => $item->isInStock(),
                'current_stock' => $item->current_stock
            ];
        }
        
        Flight::json(['success' => true, 'items' => $result]);
    }
    
    /**
     * Search products API
     */
    public function search()
    {
        $term = Flight::request()->query->term ?? '';
        
        if (strlen($term) < 2) {
            Flight::json(['success' => true, 'items' => []]);
            return;
        }
        
        $db = CatalogItem::db();
        $sql = "SELECT ci.*, cc.name as category_name, cvr.rate as vat_rate
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE ci.is_active = 1
                AND (ci.code LIKE :term OR ci.name LIKE :term2 OR ci.description LIKE :term3)
                LIMIT 20";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            'term' => "%{$term}%",
            'term2' => "%{$term}%",
            'term3' => "%{$term}%"
        ]);
        
        $items = [];
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $item = new CatalogItem();
            $item->fill($row);
            
            // Parse category name if it's JSON
            $categoryName = $row['category_name'];
            if ($categoryName && strpos($categoryName, '{') === 0) {
                $names = json_decode($categoryName, true);
                if ($names) {
                    $categoryName = $names[$_SESSION['user_language'] ?? 'fr'] ?? reset($names);
                }
            }
            
            // Check if category is "Divers" (miscellaneous)
            $categoryIsMisc = false;
            if ($categoryName) {
                $categoryIsMisc = (stripos($categoryName, 'divers') !== false);
            }
            
            $items[] = [
                'id' => $item->id,
                'code' => $item->code,
                'name' => $item->name,
                'category' => $categoryName,
                'category_id' => $item->category_id,
                'category_is_misc' => $categoryIsMisc,
                'price' => $item->unit_price,
                'unit_price' => $item->unit_price,
                'price_formatted' => $item->formatPrice(),
                'vat_rate' => $row['vat_rate'],
                'in_stock' => $item->isInStock(),
                'stock_status' => $item->getStockStatusLabel()
            ];
        }
        
        Flight::json(['success' => true, 'items' => $items]);
    }
    
    /**
     * Validate product data
     */
    private function validateProduct($data, $id = null)
    {
        $errors = [];
        
        // Required fields
        if (empty($data['code'])) {
            $errors['code'] = 'Product code is required';
        } else {
            // Check uniqueness
            $db = CatalogItem::db();
            $sql = "SELECT COUNT(*) as count FROM catalog_items WHERE code = :code";
            $params = ['code' => $data['code']];
            
            if ($id) {
                $sql .= " AND id != :id";
                $params['id'] = $id;
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            if ($stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0) {
                $errors['code'] = 'Product code already exists';
            }
        }
        
        if (empty($data['name'])) {
            $errors['name'] = 'Product name is required';
        }
        
        if (empty($data['category_id'])) {
            $errors['category_id'] = 'Category is required';
        }
        
        if (empty($data['item_type'])) {
            $errors['item_type'] = 'Item type is required';
        }
        
        if (!isset($data['unit_price']) || !is_numeric($data['unit_price']) || $data['unit_price'] < 0) {
            $errors['unit_price'] = 'Valid unit price is required';
        }
        
        if (empty($data['vat_rate_id'])) {
            $errors['vat_rate_id'] = 'VAT rate is required';
        }
        
        return $errors;
    }
    
    /**
     * Quick create product from invoice line
     */
    public function quickCreate()
    {
        // Start output buffering to prevent any accidental output
        ob_start();
        
        try {
            // Force JSON response header
            header('Content-Type: application/json; charset=UTF-8');
            
            // 1. CSRF Protection
            if (!$this->validateCsrfToken()) {
                ob_clean();
                Flight::json([
                    'success' => false,
                    'message' => __('common.csrf_error'),
                    'code' => 'CSRF_INVALID'
                ], 403);
                return;
            }
            
            // 2. Permission Check
            if (!$this->checkPermission('invoice.create')) {
                ob_clean();
                Flight::json([
                    'success' => false,
                    'message' => __('common.permission_denied'),
                    'code' => 'PERMISSION_DENIED'
                ], 403);
                return;
            }
            
            // 3. Rate Limiting Check
            if (!$this->checkRateLimit('product_quick_create', 10, 300)) {
                ob_clean();
                Flight::json([
                    'success' => false,
                    'message' => __('common.rate_limit_exceeded'),
                    'code' => 'RATE_LIMIT_EXCEEDED'
                ], 429);
                return;
            }
            
            // Get and sanitize input data
            $rawData = Flight::request()->data->getData();
            
            // 4. Comprehensive Input Sanitization
            $cleanData = [
                'code' => $this->sanitizeProductCode($rawData['code'] ?? ''),
                'name' => $this->sanitizeProductName($rawData['name'] ?? ''),
                'category_id' => $this->sanitizeId($rawData['category_id'] ?? 0),
                'item_type' => $this->sanitizeItemType($rawData['item_type'] ?? ''),
                'unit_price' => $this->sanitizePrice($rawData['unit_price'] ?? 0),
                'vat_rate_id' => $this->sanitizeId($rawData['vat_rate_id'] ?? 1),
                'description' => $this->sanitizeDescription($rawData['description'] ?? ''),
                'is_stockable' => $this->sanitizeBoolean($rawData['is_stockable'] ?? false),
                'current_stock' => $this->sanitizeStock($rawData['current_stock'] ?? 0),
                'low_stock_alert' => $this->sanitizeStock($rawData['low_stock_alert'] ?? 5),
                'is_active' => 1,
                'creation_source' => 'inline',
                'created_by' => $_SESSION['user_id'] ?? null
            ];
            
            // 5. Enhanced Validation
            $errors = $this->validateProductDataEnhanced($cleanData, true);
            
            if (!empty($errors)) {
                ob_clean();
                Flight::json([
                    'success' => false,
                    'errors' => $errors,
                    'message' => __('validation.fix_errors'),
                    'code' => 'VALIDATION_ERROR'
                ], 422);
                return;
            }
            
            // 6. Duplicate Detection
            $duplicateCheck = $this->checkDuplicateProduct($cleanData['name'], $cleanData['category_id']);
            if ($duplicateCheck['exists']) {
                ob_clean();
                Flight::json([
                    'success' => false,
                    'message' => __('products.duplicate_detected'),
                    'existing_product' => $duplicateCheck['product'],
                    'code' => 'DUPLICATE_PRODUCT'
                ], 409);
                return;
            }
            
            $db = CatalogItem::db();
            $db->beginTransaction();
            
            try {
                // 7. Auto-generate code if needed
                if (empty($cleanData['code'])) {
                    $cleanData['code'] = $this->generateProductCode(
                        $cleanData['category_id'], 
                        $cleanData['item_type']
                    );
                }
                
                // Create product with all fields
                $product = CatalogItem::create($cleanData);
                
                // Create initial stock movement if applicable
                if ($product->is_stockable && $product->current_stock > 0) {
                    StockMovement::create([
                        'item_id' => $product->id,
                        'movement_type' => 'initial',
                        'quantity' => $product->current_stock,
                        'notes' => 'Initial stock from quick creation',
                        'created_by' => $_SESSION['user_id'] ?? null
                    ]);
                }
                
                // Load relationships for complete response
                $product->loadCategory();
                $product->loadVatRate();
                
                // 8. Prepare enriched response
                $responseData = $this->prepareProductResponse($product);
                
                $db->commit();
                
                // 9. Activity Logging
                $activityData = [
                    'code' => $product->code,
                    'name' => $product->name,
                    'unit_price' => $product->unit_price,
                    'category_id' => $product->category_id,
                    'creation_source' => 'inline'
                ];
                
                // Log activity - use simple insert if ActivityLog doesn't exist
                try {
                    $stmt = $db->prepare("
                        INSERT INTO activity_logs 
                        (entity_type, entity_id, action, description, changes, user_id, created_at) 
                        VALUES (:entity_type, :entity_id, :action, :description, :changes, :user_id, NOW())
                    ");
                    $stmt->execute([
                        'entity_type' => 'catalog_items',
                        'entity_id' => $product->id,
                        'action' => 'created',
                        'description' => sprintf('Product "%s" created via invoice quick-add', $product->name),
                        'changes' => json_encode($activityData),
                        'user_id' => $_SESSION['user_id'] ?? null
                    ]);
                } catch (\PDOException $e) {
                    // Activity logging failure should not stop the operation
                    error_log("Activity logging failed: " . $e->getMessage());
                }
                
                // Clear output buffer and send response
                ob_clean();
                Flight::json([
                    'success' => true,
                    'message' => __('products.created_successfully'),
                    'product' => $responseData,
                    'code' => 'PRODUCT_CREATED'
                ]);
                
            } catch (\Exception $e) {
                $db->rollBack();
                
                // Log error with context
                error_log(sprintf(
                    "Product quick create error: %s | User: %d | Data: %s",
                    $e->getMessage(),
                    $_SESSION['user_id'] ?? 0,
                    json_encode($cleanData)
                ));
                
                ob_clean();
                $appDebug = defined('APP_DEBUG') && APP_DEBUG;
                Flight::json([
                    'success' => false,
                    'message' => __('common.error_occurred'),
                    'error' => $appDebug ? $e->getMessage() : null,
                    'code' => 'CREATION_ERROR'
                ], 500);
            }
            
        } catch (\Exception $e) {
            ob_clean();
            error_log("Product quick create critical error: " . $e->getMessage());
            
            Flight::json([
                'success' => false,
                'message' => __('common.error_occurred'),
                'code' => 'CRITICAL_ERROR'
            ], 500);
        }
    }
    
    /**
     * Validate product data for quick create
     */
    protected function validateProductData($data, $isQuickCreate = false)
    {
        $errors = [];
        
        // Code validation
        if (!$isQuickCreate && empty($data['code'])) {
            $errors['code'] = __('validation.required');
        } elseif (!empty($data['code'])) {
            // Check if code already exists
            $db = CatalogItem::db();
            $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
            $stmt->execute(['code' => $data['code']]);
            $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
            if ($existing) {
                $errors['code'] = __('products.code_exists');
            }
        }
        
        // Name validation
        if (empty($data['name'])) {
            $errors['name'] = __('validation.required');
        } elseif (strlen($data['name']) > 255) {
            $errors['name'] = __('validation.max_length', ['max' => 255]);
        }
        
        // Category validation
        if (empty($data['category_id'])) {
            $errors['category_id'] = __('validation.required');
        } else {
            $category = CatalogCategory::find($data['category_id']);
            if (!$category) {
                $errors['category_id'] = __('validation.invalid');
            }
        }
        
        // Price validation
        if (!isset($data['unit_price']) || $data['unit_price'] < 0) {
            $errors['unit_price'] = __('validation.min', ['min' => 0]);
        }
        
        // VAT rate validation
        if (!empty($data['vat_rate_id'])) {
            $vatRate = ConfigVatRate::find($data['vat_rate_id']);
            if (!$vatRate) {
                $errors['vat_rate_id'] = __('validation.invalid');
            }
        }
        
        // Stock validation
        if ($data['is_stockable'] && $data['current_stock'] < 0) {
            $errors['current_stock'] = __('validation.min', ['min' => 0]);
        }
        
        return $errors;
    }
    
    /**
     * Check if product name exists (for duplicate detection)
     */
    public function checkName()
    {
        try {
            header('Content-Type: application/json; charset=UTF-8');
            
            $name = $this->sanitizeProductName(Flight::request()->query->name ?? '');
            $categoryId = $this->sanitizeId(Flight::request()->query->category_id ?? 0);
            $excludeId = $this->sanitizeId(Flight::request()->query->exclude_id ?? 0);
            
            if (strlen($name) < 2) {
                Flight::json(['exists' => false, 'similar' => []]);
                return;
            }
            
            $db = CatalogItem::db();
            
            // Check exact match
            $sql = "SELECT ci.id, ci.code, ci.name, ci.unit_price, ci.vat_rate_id, ci.category_id,
                    cc.name as category_name, cvr.rate as vat_rate
                    FROM catalog_items ci
                    LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                    LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                    WHERE LOWER(TRIM(ci.name)) = LOWER(TRIM(:name))
                    AND ci.is_active = 1";
            
            $params = ['name' => $name];
            
            if ($excludeId > 0) {
                $sql .= " AND ci.id != :exclude_id";
                $params['exclude_id'] = $excludeId;
            }
            
            $sql .= " LIMIT 1";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($existing) {
                Flight::json([
                    'exists' => true,
                    'exact_match' => true,
                    'product' => [
                        'id' => $existing['id'],
                        'code' => $existing['code'],
                        'name' => $existing['name'],
                        'unit_price' => floatval($existing['unit_price']),
                        'vat_rate_id' => $existing['vat_rate_id'],
                        'vat_rate' => floatval($existing['vat_rate']),
                        'category_id' => $existing['category_id'],
                        'category_name' => $existing['category_name']
                    ],
                    'message' => __('products.name_already_exists')
                ]);
                return;
            }
            
            // Find similar products
            $sql = "SELECT ci.id, ci.code, ci.name, ci.unit_price, ci.category_id,
                    cc.name as category_name,
                    CASE 
                        WHEN ci.name LIKE CONCAT('%', :name2, '%') THEN 1
                        WHEN :name3 LIKE CONCAT('%', ci.name, '%') THEN 2
                        ELSE 3
                    END as match_score
                    FROM catalog_items ci
                    LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                    WHERE ci.is_active = 1
                    AND (
                        ci.name LIKE CONCAT('%', :name4, '%')
                        OR :name5 LIKE CONCAT('%', ci.name, '%')
                    )";
            
            if ($categoryId > 0) {
                $sql .= " AND ci.category_id = :category_id";
                $params['category_id'] = $categoryId;
            }
            
            $sql .= " ORDER BY match_score ASC, ci.name ASC LIMIT 5";
            
            // Add all name parameters
            for ($i = 2; $i <= 5; $i++) {
                $params['name' . $i] = $name;
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            $similar = [];
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                // Parse category name if JSON
                $categoryName = $row['category_name'];
                if ($categoryName && strpos($categoryName, '{') === 0) {
                    $names = json_decode($categoryName, true);
                    if ($names) {
                        $categoryName = $names[$_SESSION['user_language'] ?? 'fr'] ?? reset($names);
                    }
                }
                
                $similar[] = [
                    'id' => $row['id'],
                    'code' => $row['code'],
                    'name' => $row['name'],
                    'unit_price' => floatval($row['unit_price']),
                    'category_name' => $categoryName,
                    'match_score' => $row['match_score']
                ];
            }
            
            Flight::json([
                'exists' => false,
                'similar' => $similar,
                'suggestions_available' => count($similar) > 0
            ]);
            
        } catch (\Exception $e) {
            error_log("Check product name error: " . $e->getMessage());
            Flight::json([
                'exists' => false,
                'error' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Generate unique product code API endpoint
     */
    public function generateCode()
    {
        try {
            header('Content-Type: application/json; charset=UTF-8');
            
            $categoryId = $this->sanitizeId(Flight::request()->query->category_id ?? 0);
            $itemType = $this->sanitizeItemType(Flight::request()->query->item_type ?? 'service');
            
            // Use the enhanced method that uses sequences
            $code = $this->generateProductCode($categoryId, $itemType);
            
            Flight::json([
                'success' => true,
                'code' => $code,
                'category_id' => $categoryId
            ]);
            
        } catch (\Exception $e) {
            error_log("Generate product code API error: " . $e->getMessage());
            Flight::json([
                'success' => false,
                'error' => __('common.error_occurred'),
                'message' => defined('APP_DEBUG') && APP_DEBUG ? $e->getMessage() : null
            ], 500);
        }
    }
    
    /**
     * Get VAT rates for dropdown
     */
    public function getVatRates()
    {
        try {
            header('Content-Type: application/json');
            
            $db = ConfigVatRate::db();
            $stmt = $db->prepare("SELECT id, name, rate FROM config_vat_rates WHERE is_active = 1 ORDER BY rate ASC");
            $stmt->execute();
            $rates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Format rates for dropdown
            $formattedRates = [];
            foreach ($rates as $rate) {
                $formattedRates[] = [
                    'id' => intval($rate['id']),
                    'name' => $rate['name'],
                    'rate' => floatval($rate['rate']),
                    'display' => $rate['name'] . ' (' . number_format($rate['rate'], 1) . '%)'
                ];
            }
            
            Flight::json([
                'success' => true,
                'rates' => $formattedRates
            ]);
            
        } catch (\Exception $e) {
            error_log("Get VAT rates error: " . $e->getMessage());
            Flight::json([
                'success' => false,
                'error' => __('common.error_occurred')
            ], 500);
        }
    }
    
    /**
     * Check if product code exists and suggest alternatives
     */
    public function checkCode()
    {
        try {
            // Force JSON response header
            header('Content-Type: application/json');
            
            $code = Flight::request()->query->code ?? '';
            
            if (empty($code)) {
                Flight::json(['exists' => false, 'suggestions' => []]);
                return;
            }
            
            // Check if code exists
            $db = CatalogItem::db();
            $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
            $stmt->execute(['code' => $code]);
            $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$existing) {
                Flight::json(['exists' => false, 'suggestions' => []]);
                return;
            }
            
            // Generate alternative suggestions
            $baseCode = preg_replace('/\d+$/', '', $code); // Remove trailing numbers
            $suggestions = [];
            
            // Try different suffixes
            for ($i = 1; $i <= 5; $i++) {
                $newCode = $baseCode . str_pad((int)substr($code, -3) + $i, 3, '0', STR_PAD_LEFT);
                $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
                $stmt->execute(['code' => $newCode]);
                if (!$stmt->fetch(\PDO::FETCH_ASSOC)) {
                    $suggestions[] = $newCode;
                    if (count($suggestions) >= 3) break;
                }
            }
            
            // If still need suggestions, try with current timestamp
            if (count($suggestions) < 3) {
                $timestamp = time();
                $suggestions[] = $baseCode . substr($timestamp, -3);
                $suggestions[] = $baseCode . substr($timestamp, -4, 3);
            }
            
            Flight::json([
                'exists' => true,
                'suggestions' => array_unique($suggestions)
            ]);
        } catch (\Exception $e) {
            // Always return JSON on error
            Flight::json([
                'error' => true,
                'message' => 'Error checking code',
                'exists' => false,
                'suggestions' => []
            ]);
        }
    }
    
    /**
     * Bulk delete products
     */
    public function bulkDelete()
    {
        try {
            // Get JSON data
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($data['ids']) || !is_array($data['ids']) || empty($data['ids'])) {
                Flight::json(['success' => false, 'message' => 'No products selected'], 400);
                return;
            }
            
            $db = CatalogItem::db();
            $db->beginTransaction();
            
            try {
                $deletedCount = 0;
                $deactivatedCount = 0;
                
                foreach ($data['ids'] as $id) {
                    $product = CatalogItem::find($id);
                    if (!$product) continue;
                    
                    // Check if product has been used in invoices
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM invoice_lines WHERE item_id = :id");
                    $stmt->execute(['id' => $id]);
                    $hasHistory = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
                    
                    if ($hasHistory) {
                        // Soft delete by deactivating
                        $product->update(['is_active' => 0]);
                        $deactivatedCount++;
                    } else {
                        // Hard delete
                        $product->delete();
                        $deletedCount++;
                    }
                }
                
                $db->commit();
                
                $message = '';
                if ($deletedCount > 0) {
                    $message .= "$deletedCount products deleted. ";
                }
                if ($deactivatedCount > 0) {
                    $message .= "$deactivatedCount products deactivated (have sales history).";
                }
                
                Flight::json(['success' => true, 'message' => $message]);
                
            } catch (\Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error deleting products: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Export products
     */
    public function export()
    {
        try {
            $ids = Flight::request()->data->ids ?? [];
            
            if (empty($ids)) {
                Flight::halt(400, 'No products selected');
                return;
            }
            
            // Get products
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $db = CatalogItem::db();
            $sql = "
                SELECT ci.*, cc.name as category_name, cvr.rate as vat_rate
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE ci.id IN ($placeholders)
            ";
            $stmt = $db->prepare($sql);
            $stmt->execute($ids);
            
            // Set headers for CSV download with UTF-8 encoding
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename="products_export_' . date('Y-m-d_His') . '.csv"');
            
            // Open output stream
            $output = fopen('php://output', 'w');
            
            // Write UTF-8 BOM to ensure Excel recognizes UTF-8 encoding
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Write CSV headers
            fputcsv($output, [
                'Code',
                'Name',
                'Category',
                'Type',
                'Description',
                'Unit Price',
                'VAT Rate',
                'Stock',
                'Status'
            ]);
            
            // Write data
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                // Parse category name if JSON
                $categoryName = $row['category_name'];
                if ($categoryName && strpos($categoryName, '{') === 0) {
                    $names = json_decode($categoryName, true);
                    if ($names) {
                        $categoryName = $names[$_SESSION['user_language'] ?? 'fr'] ?? $names['fr'] ?? $names['en'] ?? reset($names);
                    }
                }
                
                fputcsv($output, [
                    $row['code'],
                    $row['name'],
                    $categoryName,
                    $row['item_type'],
                    $row['description'],
                    $row['unit_price'],
                    $row['vat_rate'] . '%',
                    $row['is_stockable'] ? $row['current_stock'] : 'N/A',
                    $row['is_active'] ? 'Active' : 'Inactive'
                ]);
            }
            
            fclose($output);
            exit;
            
        } catch (\Exception $e) {
            Flight::halt(500, 'Error exporting products: ' . $e->getMessage());
        }
    }
    
    /**
     * Enhanced validation for product data
     */
    protected function validateProductDataEnhanced($data, $isQuickCreate = false)
    {
        $errors = [];
        
        // Code validation
        if (!$isQuickCreate && empty($data['code'])) {
            $errors['code'] = __('validation.required');
        } elseif (!empty($data['code'])) {
            if (!preg_match('/^[A-Z0-9\-_]+$/', $data['code'])) {
                $errors['code'] = __('products.code_invalid_format');
            }
            
            // Check uniqueness
            $db = CatalogItem::db();
            $stmt = $db->prepare("SELECT id FROM catalog_items WHERE code = :code LIMIT 1");
            $stmt->execute(['code' => $data['code']]);
            if ($stmt->fetch(\PDO::FETCH_ASSOC)) {
                $errors['code'] = __('products.code_exists');
            }
        }
        
        // Name validation
        if (empty($data['name'])) {
            $errors['name'] = __('validation.required');
        } else {
            if (strlen($data['name']) < 2) {
                $errors['name'] = __('validation.min_length', ['min' => 2]);
            }
            if (strlen($data['name']) > 255) {
                $errors['name'] = __('validation.max_length', ['max' => 255]);
            }
            // Check for suspicious patterns
            if (preg_match('/[;\'"<>]/', $data['name'])) {
                $errors['name'] = __('validation.invalid_characters');
            }
        }
        
        // Category validation
        if (empty($data['category_id'])) {
            $errors['category_id'] = __('validation.required');
        } else {
            $category = CatalogCategory::find($data['category_id']);
            if (!$category || !$category->is_active) {
                $errors['category_id'] = __('validation.invalid_category');
            }
        }
        
        // Price validation
        if (!isset($data['unit_price']) || $data['unit_price'] < 0) {
            $errors['unit_price'] = __('validation.min', ['min' => 0]);
        } elseif ($data['unit_price'] > 999999.99) {
            $errors['unit_price'] = __('validation.max', ['max' => 999999.99]);
        }
        
        // VAT rate validation
        if (!empty($data['vat_rate_id'])) {
            $vatRate = ConfigVatRate::find($data['vat_rate_id']);
            if (!$vatRate || !$vatRate->is_active) {
                $errors['vat_rate_id'] = __('validation.invalid_vat_rate');
            }
        }
        
        // Stock validation
        if ($data['is_stockable']) {
            if ($data['current_stock'] < 0) {
                $errors['current_stock'] = __('validation.min', ['min' => 0]);
            }
            if ($data['current_stock'] > 999999) {
                $errors['current_stock'] = __('validation.max', ['max' => 999999]);
            }
        }
        
        return $errors;
    }
    
    /**
     * Sanitize product code
     */
    protected function sanitizeProductCode($code)
    {
        $code = preg_replace('/[^A-Za-z0-9\-_]/', '', trim($code));
        $code = strtoupper($code);
        return substr($code, 0, 50);
    }
    
    /**
     * Sanitize product name
     */
    protected function sanitizeProductName($name)
    {
        $name = strip_tags(trim($name));
        $name = preg_replace('/\s+/', ' ', $name);
        return substr($name, 0, 255);
    }
    
    /**
     * Sanitize description
     */
    protected function sanitizeDescription($description)
    {
        $allowedTags = '<p><br><strong><em><ul><ol><li>';
        $description = strip_tags(trim($description), $allowedTags);
        $description = preg_replace('#<script(.*?)>(.*?)</script>#is', '', $description);
        $description = preg_replace('/javascript:/i', '', $description);
        return substr($description, 0, 1000);
    }
    
    /**
     * Sanitize numeric ID
     */
    protected function sanitizeId($id)
    {
        return max(0, intval($id));
    }
    
    /**
     * Sanitize item type
     */
    protected function sanitizeItemType($type)
    {
        $allowedTypes = ['product', 'service'];
        return in_array($type, $allowedTypes) ? $type : 'service';
    }
    
    /**
     * Sanitize price
     */
    protected function sanitizePrice($price)
    {
        $price = preg_replace('/[^0-9.]/', '', $price);
        $price = floatval($price);
        return max(0, min($price, 999999.99));
    }
    
    /**
     * Sanitize stock quantity
     */
    protected function sanitizeStock($stock)
    {
        return max(0, min(intval($stock), 999999));
    }
    
    /**
     * Sanitize boolean value
     */
    protected function sanitizeBoolean($value)
    {
        return filter_var($value, FILTER_VALIDATE_BOOLEAN) ? 1 : 0;
    }
    
    /**
     * Check for duplicate products
     */
    protected function checkDuplicateProduct($name, $categoryId = null)
    {
        try {
            $db = CatalogItem::db();
            
            $sql = "SELECT ci.*, cc.name as category_name, cvr.rate as vat_rate 
                    FROM catalog_items ci
                    LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                    LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                    WHERE LOWER(TRIM(ci.name)) = LOWER(TRIM(:name))";
            
            $params = ['name' => $name];
            
            if ($categoryId) {
                $sql .= " AND ci.category_id = :category_id";
                $params['category_id'] = $categoryId;
            }
            
            $sql .= " LIMIT 1";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $existing = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($existing) {
                return [
                    'exists' => true,
                    'product' => [
                        'id' => $existing['id'],
                        'code' => $existing['code'],
                        'name' => $existing['name'],
                        'category_name' => $existing['category_name'],
                        'unit_price' => floatval($existing['unit_price']),
                        'vat_rate' => floatval($existing['vat_rate']),
                        'is_active' => $existing['is_active']
                    ]
                ];
            }
            
            return ['exists' => false];
            
        } catch (\Exception $e) {
            error_log("Duplicate check error: " . $e->getMessage());
            return ['exists' => false, 'error' => true];
        }
    }
    
    /**
     * Generate unique product code using sequences
     */
    protected function generateProductCode($categoryId = null, $itemType = null)
    {
        $db = CatalogItem::db();
        
        try {
            // Get category prefix if available
            $prefix = 'PROD';
            if ($categoryId) {
                $category = CatalogCategory::find($categoryId);
                if ($category) {
                    // Use category code or first 4 letters of name
                    if (!empty($category->code)) {
                        $prefix = strtoupper(substr($category->code, 0, 4));
                    } else {
                        $name = is_array($category->name) ? reset($category->name) : $category->name;
                        $prefix = strtoupper(substr(preg_replace('/[^A-Za-z]/', '', $name), 0, 4));
                    }
                }
            }
            
            // Try to use catalog_sequences table
            $stmt = $db->prepare("
                SELECT last_number FROM catalog_sequences 
                WHERE category_id = :category_id 
                FOR UPDATE
            ");
            $stmt->execute(['category_id' => $categoryId ?: 0]);
            $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($sequence) {
                $nextNumber = $sequence['last_number'] + 1;
                
                $updateStmt = $db->prepare("
                    UPDATE catalog_sequences 
                    SET last_number = :number, 
                        updated_at = NOW() 
                    WHERE category_id = :category_id
                ");
                $updateStmt->execute([
                    'number' => $nextNumber,
                    'category_id' => $categoryId ?: 0
                ]);
                
                return $prefix . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
            }
            
        } catch (\PDOException $e) {
            // Table might not exist, fall back to traditional method
        }
        
        // Fallback: Traditional method
        return CatalogItem::generateCode($prefix);
    }
    
    /**
     * Check rate limit for actions
     */
    protected function checkRateLimit($action, $maxAttempts = 10, $windowSeconds = 60)
    {
        $userId = $_SESSION['user_id'] ?? 0;
        $ipAddress = $this->getClientIp();
        
        try {
            $db = Flight::db();
            
            // Create rate_limits table if it doesn't exist
            $db->exec("
                CREATE TABLE IF NOT EXISTS `rate_limits` (
                    `id` INT AUTO_INCREMENT PRIMARY KEY,
                    `action` VARCHAR(50) NOT NULL,
                    `user_id` INT DEFAULT NULL,
                    `ip_address` VARCHAR(45) NOT NULL,
                    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX `idx_action_time` (`action`, `created_at`),
                    INDEX `idx_user_ip` (`user_id`, `ip_address`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
            ");
            
            // Clean old entries
            $stmt = $db->prepare("
                DELETE FROM rate_limits 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL :window SECOND)
            ");
            $stmt->execute(['window' => $windowSeconds]);
            
            // Count recent attempts
            $stmt = $db->prepare("
                SELECT COUNT(*) as attempts 
                FROM rate_limits 
                WHERE action = :action 
                AND (user_id = :user_id OR ip_address = :ip) 
                AND created_at > DATE_SUB(NOW(), INTERVAL :window SECOND)
            ");
            $stmt->execute([
                'action' => $action,
                'user_id' => $userId,
                'ip' => $ipAddress,
                'window' => $windowSeconds
            ]);
            
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $attempts = $result['attempts'] ?? 0;
            
            if ($attempts >= $maxAttempts) {
                return false;
            }
            
            // Log this attempt
            $stmt = $db->prepare("
                INSERT INTO rate_limits (action, user_id, ip_address, created_at) 
                VALUES (:action, :user_id, :ip, NOW())
            ");
            $stmt->execute([
                'action' => $action,
                'user_id' => $userId,
                'ip' => $ipAddress
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            // If rate limiting fails, allow the action but log the error
            error_log("Rate limit check failed: " . $e->getMessage());
            return true;
        }
    }
    
    /**
     * Get client IP address
     */
    protected function getClientIp()
    {
        $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP)) {
                    return $ip;
                }
            }
        }
        return '0.0.0.0';
    }
    
    /**
     * Prepare enriched product response for invoice integration
     */
    protected function prepareProductResponse($product)
    {
        $category = $product->category ?? $product->getCategory();
        $vatRate = $product->vatRate ?? $product->getVatRate();
        
        return [
            'id' => $product->id,
            'code' => $product->code,
            'name' => $product->name,
            'description' => $product->description,
            'unit_price' => floatval($product->unit_price),
            'price_formatted' => $product->formatPrice(),
            'price_with_vat' => $product->getPriceWithVat(),
            'price_with_vat_formatted' => $product->formatPrice(true),
            
            // VAT information
            'vat_rate_id' => $product->vat_rate_id,
            'vat_rate' => $vatRate ? floatval($vatRate->rate) : 0,
            'vat_rate_name' => $vatRate ? $vatRate->name : '',
            'vat_amount' => $product->getVatAmount(),
            
            // Category information
            'category_id' => $product->category_id,
            'category_name' => $category ? $category->getDisplayName() : '',
            'category_code' => $category ? $category->code : '',
            'category_is_misc' => $category && property_exists($category, 'is_misc_category') && $category->is_misc_category ? true : false,
            
            // Product attributes
            'item_type' => $product->item_type,
            'is_stockable' => (bool)$product->is_stockable,
            'is_active' => (bool)$product->is_active,
            
            // Stock information
            'current_stock' => $product->current_stock,
            'low_stock_alert' => $product->low_stock_alert,
            'in_stock' => $product->isInStock(),
            'stock_status' => $product->getStockStatus(),
            'stock_status_label' => $product->getStockStatusLabel(),
            'stock_status_color' => $product->getStockStatusColor(),
            
            // Metadata
            'created_at' => $product->created_at,
            'creation_source' => $product->creation_source ?? 'inline'
        ];
    }
    
    /**
     * Export products to Excel, CSV or PDF
     */
    public function export()
    {
        // Check permissions
        if (!$this->checkPermission('view_catalog')) {
            $this->accessDenied();
            return;
        }
        
        $format = Flight::request()->query->format ?: 'excel';
        $filters = [];
        
        // Get filters from request
        $search = Flight::request()->query->search ?? '';
        $category = Flight::request()->query->category ?? '';
        $type = Flight::request()->query->type ?? '';
        $stockStatus = Flight::request()->query->stock_status ?? '';
        
        // Build query
        $db = CatalogItem::db();
        $sql = "SELECT ci.*, cc.name as category_name, cc.icon as category_icon, 
                cvr.rate as vat_rate, cvr.name as vat_name
                FROM catalog_items ci
                LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                LEFT JOIN config_vat_rates cvr ON ci.vat_rate_id = cvr.id
                WHERE 1=1";
        
        $params = [];
        
        // Apply filters
        if ($search) {
            $sql .= " AND (ci.code LIKE :search OR ci.name LIKE :search2 OR ci.description LIKE :search3)";
            $params['search'] = "%{$search}%";
            $params['search2'] = "%{$search}%";
            $params['search3'] = "%{$search}%";
            $filters['search'] = $search;
        }
        
        if ($category) {
            $sql .= " AND ci.category_id = :category";
            $params['category'] = $category;
            $filters['category'] = $category;
        }
        
        if ($type) {
            $sql .= " AND ci.type = :type";
            $params['type'] = $type;
            $filters['type'] = $type;
        }
        
        if ($stockStatus) {
            switch ($stockStatus) {
                case 'in_stock':
                    $sql .= " AND ci.current_stock > ci.low_stock_alert";
                    break;
                case 'low_stock':
                    $sql .= " AND ci.current_stock > 0 AND ci.current_stock <= ci.low_stock_alert";
                    break;
                case 'out_of_stock':
                    $sql .= " AND ci.current_stock <= 0";
                    break;
            }
            $filters['stock_status'] = $stockStatus;
        }
        
        $sql .= " ORDER BY ci.code, ci.name";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        $products = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Prepare data for export
        $exportData = [];
        foreach ($products as $product) {
            // Determine stock status
            $stockStatus = __('products.in_stock');
            $stockStatusClass = 'in_stock';
            if ($product['current_stock'] <= 0) {
                $stockStatus = __('products.out_of_stock');
                $stockStatusClass = 'out_of_stock';
            } elseif ($product['current_stock'] <= $product['low_stock_alert']) {
                $stockStatus = __('products.low_stock');
                $stockStatusClass = 'low_stock';
            }
            
            $exportData[] = [
                'code' => $product['code'],
                'name' => $product['name'],
                'category' => $product['category_name'] ?? '',
                'type' => $product['type'] ? __('products.type.' . $product['type']) : '',
                'description' => $product['description'] ?? '',
                'unit' => $product['unit'] ?? '',
                'sale_price' => $product['sale_price'],
                'vat_rate' => $product['vat_name'] ?? '',
                'price_incl_vat' => $product['sale_price'] * (1 + ($product['vat_rate'] ?? 0) / 100),
                'current_stock' => $product['current_stock'],
                'low_stock_alert' => $product['low_stock_alert'],
                'stock_status' => $stockStatus,
                'is_active' => $product['is_active'] ? __('common.yes') : __('common.no'),
                'created_at' => $product['created_at']
            ];
        }
        
        // Define columns with formatting
        $columns = [
            'code' => ['label' => __('products.code'), 'type' => 'text'],
            'name' => ['label' => __('products.name'), 'type' => 'text'],
            'category' => ['label' => __('products.category'), 'type' => 'text'],
            'type' => ['label' => __('products.type'), 'type' => 'text'],
            'description' => ['label' => __('products.description'), 'type' => 'text'],
            'unit' => ['label' => __('products.unit'), 'type' => 'text'],
            'sale_price' => ['label' => __('products.sale_price'), 'type' => 'currency'],
            'vat_rate' => ['label' => __('products.vat_rate'), 'type' => 'text'],
            'price_incl_vat' => ['label' => __('products.price_incl_vat'), 'type' => 'currency'],
            'current_stock' => ['label' => __('products.current_stock'), 'type' => 'number'],
            'low_stock_alert' => ['label' => __('products.low_stock_alert'), 'type' => 'number'],
            'stock_status' => ['label' => __('products.stock_status'), 'type' => 'text'],
            'is_active' => ['label' => __('products.is_active'), 'type' => 'text'],
            'created_at' => ['label' => __('products.created_at'), 'type' => 'datetime']
        ];
        
        // Prepare export options
        $options = [
            'title' => __('products.export_title'),
            'sheet_name' => __('products.sheet_name')
        ];
        
        // Add filters to options
        if (!empty($filters)) {
            $filterLabels = [];
            if (isset($filters['search'])) {
                $filterLabels[__('common.search')] = $filters['search'];
            }
            if (isset($filters['category'])) {
                $category = CatalogCategory::getById($filters['category']);
                if ($category) {
                    $filterLabels[__('products.category')] = $category['name'];
                }
            }
            if (isset($filters['type'])) {
                $filterLabels[__('products.type')] = __('products.type.' . $filters['type']);
            }
            if (isset($filters['stock_status'])) {
                $filterLabels[__('products.stock_status')] = __('products.' . $filters['stock_status']);
            }
            $options['filters'] = $filterLabels;
        }
        
        // Add summary statistics
        $totalValue = 0;
        $totalStock = 0;
        foreach ($exportData as $item) {
            $totalValue += $item['sale_price'] * $item['current_stock'];
            $totalStock += $item['current_stock'];
        }
        
        $options['totals'] = [
            'current_stock' => $totalStock
        ];
        
        // Create export service and export
        $exportService = new \App\Services\ExportService();
        
        // Log the export
        $exportService->logExport($format, 'products', count($exportData), $filters);
        
        // Generate filename
        $filename = 'products_' . date('Y-m-d_H-i-s');
        
        switch ($format) {
            case 'csv':
                $exportService->exportToCSV($exportData, $columns, $filename, ['delimiter' => ';']);
                break;
            case 'pdf':
                $exportService->exportToPDF($exportData, $columns, $filename, $options);
                break;
            case 'excel':
            default:
                $exportService->exportToExcel($exportData, $columns, $filename, $options);
                break;
        }
    }
}