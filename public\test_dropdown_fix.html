<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dropdown Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/fit/public/css/dropdown-positioning-fix.css">
</head>
<body>
    <div class="container mt-5">
        <h1>Dropdown Positioning Test</h1>
        
        <div class="alert alert-info">
            <h5>Testing Dropdown Fix</h5>
            <p>The dropdown menu on the right side should not be cut off. It should appear fully visible within the viewport.</p>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>Test Template</td>
                                <td>This is a test template with a long description</td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td class="text-end">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="#">Edit</a></li>
                                            <li><a class="dropdown-item" href="#">Duplicate</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#">Delete</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5>✅ Fix Applied</h5>
            <p>The dropdown positioning fix has been applied. The menu should now appear correctly positioned and not be cut off.</p>
            <ul>
                <li>CSS file created: <code>/css/dropdown-positioning-fix.css</code></li>
                <li>JavaScript positioning handler added</li>
                <li>Bootstrap dropdown attributes optimized</li>
            </ul>
        </div>
        
        <a href="/fit/public/config/invoice-templates" class="btn btn-primary">Back to Invoice Templates</a>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
    // Same positioning fix as in the main template
    document.addEventListener('shown.bs.dropdown', function(event) {
        const dropdown = event.target;
        const menu = dropdown.querySelector('.dropdown-menu');
        if (menu) {
            const rect = menu.getBoundingClientRect();
            const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
            
            if (rect.right > viewportWidth) {
                menu.style.right = '0px';
                menu.style.left = 'auto';
                menu.style.transform = 'translateX(0)';
            }
            
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            if (rect.bottom > viewportHeight) {
                menu.style.bottom = '100%';
                menu.style.top = 'auto';
            }
        }
    });
    </script>
</body>
</html>