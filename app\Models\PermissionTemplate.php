<?php

namespace App\Models;

use PDO;
use Flight;

class PermissionTemplate
{
    private $db;
    
    public function __construct()
    {
        $this->db = Flight::db();
    }
    
    /**
     * Get all permission templates
     */
    public function getAll()
    {
        $stmt = $this->db->prepare("
            SELECT pt.*, u.username as created_by_username
            FROM permission_templates pt
            LEFT JOIN users u ON pt.created_by = u.id
            ORDER BY pt.is_system DESC, pt.name
        ");
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get template by ID
     */
    public function getById($id)
    {
        $stmt = $this->db->prepare("
            SELECT pt.*, u.username as created_by_username
            FROM permission_templates pt
            LEFT JOIN users u ON pt.created_by = u.id
            WHERE pt.id = ?
        ");
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create a new template
     */
    public function create($data)
    {
        $stmt = $this->db->prepare("
            INSERT INTO permission_templates (name, description, template_data, is_system, created_by, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        return $stmt->execute([
            $data['name'],
            $data['description'] ?? null,
            json_encode($data['template_data']),
            $data['is_system'] ?? false,
            $data['created_by']
        ]);
    }
    
    /**
     * Update a template
     */
    public function update($id, $data)
    {
        // Check if template is system template
        $stmt = $this->db->prepare("SELECT is_system FROM permission_templates WHERE id = ?");
        $stmt->execute([$id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template && $template['is_system']) {
            throw new \Exception("System templates cannot be modified");
        }
        
        $fields = [];
        $values = [];
        
        foreach (['name', 'description', 'template_data'] as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                if ($field === 'template_data') {
                    $values[] = json_encode($data[$field]);
                } else {
                    $values[] = $data[$field];
                }
            }
        }
        
        if (empty($fields)) {
            return false;
        }
        
        $fields[] = "updated_at = NOW()";
        $values[] = $id;
        
        $sql = "UPDATE permission_templates SET " . implode(', ', $fields) . " WHERE id = ?";
        $stmt = $this->db->prepare($sql);
        
        return $stmt->execute($values);
    }
    
    /**
     * Delete a template
     */
    public function delete($id)
    {
        // Check if template is system template
        $stmt = $this->db->prepare("SELECT is_system FROM permission_templates WHERE id = ?");
        $stmt->execute([$id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template && $template['is_system']) {
            throw new \Exception("System templates cannot be deleted");
        }
        
        $stmt = $this->db->prepare("DELETE FROM permission_templates WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Build template data from current group permissions
     * OLD schema compatibility - uses permissions table
     */
    public function buildTemplateFromGroup($groupId)
    {
        $stmt = $this->db->prepare("
            SELECT 
                p.code as permission_code,
                p.category as module_code
            FROM group_permissions gp
            JOIN permissions p ON gp.permission_id = p.id
            WHERE gp.group_id = ?
            ORDER BY p.category, p.code
        ");
        $stmt->execute([$groupId]);
        $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $templateData = ['modules' => []];
        
        foreach ($permissions as $perm) {
            // Extract the action part from the permission code (e.g., 'invoices.view' -> 'view')
            $parts = explode('.', $perm['permission_code']);
            $action = isset($parts[1]) ? $parts[1] : $perm['permission_code'];
            
            if (!isset($templateData['modules'][$perm['module_code']])) {
                $templateData['modules'][$perm['module_code']] = [];
            }
            $templateData['modules'][$perm['module_code']][] = $action;
        }
        
        return $templateData;
    }
    
    /**
     * Get template preview (list of permissions that would be granted)
     */
    public function getTemplatePreview($templateId)
    {
        $template = $this->getById($templateId);
        if (!$template) {
            return [];
        }
        
        $templateData = json_decode($template['template_data'], true);
        $permissions = [];
        
        if (isset($templateData['all_permissions']) && $templateData['all_permissions']) {
            // Get all permissions - OLD schema compatibility
            $stmt = $this->db->prepare("
                SELECT 
                    p.category as module_name,
                    p.category as module_code,
                    p.code as permission_code,
                    p.name as permission_name
                FROM permissions p
                ORDER BY p.category, p.code
            ");
            $stmt->execute();
            $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } elseif (isset($templateData['modules'])) {
            // Get specific permissions - OLD schema compatibility
            foreach ($templateData['modules'] as $moduleCode => $permActions) {
                // Build full permission codes from module.action format
                $fullCodes = [];
                foreach ($permActions as $action) {
                    $fullCodes[] = $moduleCode . '.' . $action;
                }
                
                $placeholders = str_repeat('?,', count($fullCodes) - 1) . '?';
                $stmt = $this->db->prepare("
                    SELECT 
                        p.category as module_name,
                        p.category as module_code,
                        p.code as permission_code,
                        p.name as permission_name
                    FROM permissions p
                    WHERE p.code IN ($placeholders)
                ");
                $stmt->execute($fullCodes);
                $modulePerms = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $permissions = array_merge($permissions, $modulePerms);
            }
        }
        
        return $permissions;
    }
}