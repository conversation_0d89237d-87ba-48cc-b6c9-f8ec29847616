<?php

use App\Controllers\PatientController;

// Patient search (AJAX) - must come before other patient routes
Flight::route('GET /patients/search', [PatientController::class, 'search']);

// Patient routes
Flight::route('GET /patients', [PatientController::class, 'index']);
Flight::route('GET /patients/create', [PatientController::class, 'create']);
Flight::route('POST /patients', [PatientController::class, 'store']);
Flight::route('GET /patients/@id', [PatientController::class, 'show']);
Flight::route('GET /patients/@id/', [PatientController::class, 'show']); // Handle trailing slash
Flight::route('GET /patients/@id/edit', [PatientController::class, 'edit']);
Flight::route('PUT /patients/@id', [PatientController::class, 'update']);
Flight::route('DELETE /patients/@id', [PatientController::class, 'destroy']);

// Patient notes
Flight::route('POST /patients/@id/notes', [PatientController::class, 'addNote']);