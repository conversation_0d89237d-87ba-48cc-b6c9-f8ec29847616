<?php

namespace App\Database\Seeds;

use Flight;

class RateProfilesSeeder
{
    public function run()
    {
        $db = Flight::db();
        
        // Check if rate profiles already exist
        $stmt = $db->query("SELECT COUNT(*) as count FROM rate_profiles");
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "Rate profiles already exist, skipping...\n";
            return;
        }
        
        $rateProfiles = [
            [
                'name' => 'Standard Retrocession',
                'code' => 'STD_RETRO',
                'profile_type' => 'retrocession',
                'description' => 'Standard retrocession profile with default rates',
                'is_active' => 1,
                'is_default' => 1
            ],
            [
                'name' => 'Hourly Rate',
                'code' => 'HOURLY',
                'profile_type' => 'hourly',
                'description' => 'Hourly billing profile',
                'is_active' => 1,
                'is_default' => 0
            ],
            [
                'name' => 'Office Rental',
                'code' => 'RENTAL',
                'profile_type' => 'rental',
                'description' => 'Office rental with fixed charges',
                'is_active' => 1,
                'is_default' => 0
            ],
            [
                'name' => 'Mixed Profile',
                'code' => 'MIXED',
                'profile_type' => 'mixed',
                'description' => 'Combined retrocession and hourly rates',
                'is_active' => 1,
                'is_default' => 0
            ]
        ];
        
        // Insert rate profiles
        $stmt = $db->prepare("
            INSERT INTO rate_profiles (name, code, profile_type, description, is_active, is_default, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        foreach ($rateProfiles as $profile) {
            $stmt->execute([
                $profile['name'],
                $profile['code'],
                $profile['profile_type'],
                $profile['description'],
                $profile['is_active'],
                $profile['is_default']
            ]);
            
            $profileId = $db->lastInsertId();
            echo "Created rate profile: " . $profile['name'] . " (ID: $profileId)\n";
            
            // Add default rates for each profile
            if ($profile['profile_type'] === 'retrocession' || $profile['profile_type'] === 'mixed') {
                $this->addRetrocessionRates($db, $profileId);
            }
            
            if ($profile['profile_type'] === 'hourly' || $profile['profile_type'] === 'mixed') {
                $this->addHourlyRate($db, $profileId);
            }
            
            if ($profile['profile_type'] === 'rental') {
                $this->addRentalRates($db, $profileId);
            }
        }
        
        echo "Rate profiles seeded successfully!\n";
    }
    
    private function addRetrocessionRates($db, $profileId)
    {
        $rates = [
            ['rate_type' => 'cns_percent', 'base_value' => 20],
            ['rate_type' => 'patient_percent', 'base_value' => 20],
            ['rate_type' => 'secretariat_percent', 'base_value' => 10]
        ];
        
        $stmt = $db->prepare("
            INSERT INTO rate_profile_rates (profile_id, rate_type, base_value, valid_from, is_current, created_at)
            VALUES (?, ?, ?, CURDATE(), 1, NOW())
        ");
        
        foreach ($rates as $rate) {
            $stmt->execute([$profileId, $rate['rate_type'], $rate['base_value']]);
        }
    }
    
    private function addHourlyRate($db, $profileId)
    {
        $stmt = $db->prepare("
            INSERT INTO rate_profile_rates (profile_id, rate_type, base_value, valid_from, is_current, created_at)
            VALUES (?, 'hourly_rate', 75, CURDATE(), 1, NOW())
        ");
        $stmt->execute([$profileId]);
    }
    
    private function addRentalRates($db, $profileId)
    {
        $rates = [
            ['rate_type' => 'rent_amount', 'base_value' => 1500],
            ['rate_type' => 'charges_amount', 'base_value' => 300]
        ];
        
        $stmt = $db->prepare("
            INSERT INTO rate_profile_rates (profile_id, rate_type, base_value, valid_from, is_current, created_at)
            VALUES (?, ?, ?, CURDATE(), 1, NOW())
        ");
        
        foreach ($rates as $rate) {
            $stmt->execute([$profileId, $rate['rate_type'], $rate['base_value']]);
        }
    }
}