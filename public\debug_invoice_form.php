<?php
/**
 * Debug Invoice Form Submission
 * This file helps diagnose issues with the invoice creation form
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

// Check if this is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    header('Content-Type: application/json');
    
    $response = [
        'post_data' => $_POST,
        'files' => $_FILES,
        'session' => [
            'user_id' => $_SESSION['user_id'] ?? null,
            'csrf_token' => $_SESSION['csrf_token'] ?? null
        ],
        'headers' => getallheaders(),
        'content_type' => $_SERVER['CONTENT_TYPE'] ?? null
    ];
    
    // Check CSRF token
    $submittedToken = $_POST['csrf_token'] ?? null;
    $sessionToken = $_SESSION['csrf_token'] ?? null;
    
    $response['csrf_check'] = [
        'submitted' => $submittedToken,
        'session' => $sessionToken,
        'valid' => $submittedToken === $sessionToken
    ];
    
    // Validate required fields
    $requiredFields = [
        'document_type_id' => 'Document Type',
        'invoice_number' => 'Invoice Number',
        'issue_date' => 'Issue Date',
        'billable_type' => 'Bill To Type',
        'billable_id' => 'Client/User',
        'items' => 'Invoice Items'
    ];
    
    $missingFields = [];
    foreach ($requiredFields as $field => $label) {
        if (empty($_POST[$field])) {
            $missingFields[] = $label;
        }
    }
    
    $response['validation'] = [
        'missing_fields' => $missingFields,
        'is_valid' => empty($missingFields)
    ];
    
    // Check items
    if (isset($_POST['items']) && is_array($_POST['items'])) {
        $response['items_count'] = count($_POST['items']);
        $response['items_data'] = $_POST['items'];
    } else {
        $response['items_count'] = 0;
        $response['items_data'] = [];
    }
    
    echo json_encode($response, JSON_PRETTY_PRINT);
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Form Debug</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h1>Invoice Form Debug Tool</h1>
    
    <div class="alert alert-info">
        <h4>Instructions:</h4>
        <ol>
            <li>Open the invoice creation page in another tab</li>
            <li>Fill out the form completely</li>
            <li>Before clicking Save, open your browser's Developer Console (F12)</li>
            <li>In the Console, paste and run this code:</li>
        </ol>
        <pre><code>// Debug code for invoice form
(function() {
    const form = document.getElementById('invoiceForm');
    if (!form) {
        console.error('Invoice form not found!');
        return;
    }
    
    // Clone the form
    const debugForm = form.cloneNode(true);
    
    // Change the action to our debug endpoint
    debugForm.action = '/fit/public/debug_invoice_form.php';
    debugForm.target = '_blank';
    
    // Add to body temporarily
    document.body.appendChild(debugForm);
    
    // Submit to debug endpoint
    debugForm.submit();
    
    // Remove the cloned form
    setTimeout(() => debugForm.remove(), 100);
    
    console.log('Debug form submitted - check the new tab for results');
})();</code></pre>
    </div>
    
    <div class="alert alert-warning">
        <h4>Alternative Manual Test:</h4>
        <p>You can also modify the form action temporarily:</p>
        <pre><code>document.getElementById('invoiceForm').action = '/fit/public/debug_invoice_form.php';
document.getElementById('invoiceForm').target = '_blank';</code></pre>
        <p>Then click the Save button normally.</p>
    </div>
    
    <div class="mt-4">
        <h3>Common Issues:</h3>
        <ul>
            <li><strong>Form validation blocking submission:</strong> Check for required fields marked with *</li>
            <li><strong>No invoice items:</strong> Must add at least one line item</li>
            <li><strong>JavaScript errors:</strong> Check browser console for red errors</li>
            <li><strong>CSRF token mismatch:</strong> Refresh the page and try again</li>
        </ul>
    </div>
</div>
</body>
</html>