# Email Variable Substitution Guide

## Overview

The Fit360 email system provides a powerful variable substitution engine that supports simple variables, nested objects, conditionals, loops, and default values. This guide explains how to use these features in email templates.

## Variable Syntax

### 1. Simple Variables

```handlebars
{{variable_name}}
{VARIABLE_NAME}     // Legacy format
[VARIABLE_NAME]     // Alternative format
```

Example:
```handlebars
Dear {{CLIENT_NAME}},
Your invoice {{INVOICE_NUMBER}} is ready.
```

### 2. Nested Variables

Access properties of objects using dot notation:

```handlebars
{{object.property}}
{{invoice.number}}
{{client.email}}
{{company.name}}
```

Example:
```handlebars
Invoice: {{invoice.number}}
Client: {{client.name}} ({{client.email}})
Total: {{invoice.total}}
```

### 3. Default Values

Provide fallback values for missing variables:

```handlebars
{{variable|default value}}
```

Example:
```handlebars
Hello {{name|Guest}},
Your balance is {{balance|0.00}} EUR.
```

### 4. Conditionals

Show content based on conditions:

```handlebars
{{#if condition}}
  Content when true
{{/if}}

{{#if condition}}
  Content when true
{{else}}
  Content when false
{{/if}}

{{#if !condition}}
  Content when condition is false
{{/if}}
```

Example:
```handlebars
{{#if invoice.is_paid}}
  Thank you for your payment!
{{else}}
  Payment is due by {{invoice.due_date}}.
{{/if}}

{{#if !client.has_discount}}
  Standard pricing applies.
{{/if}}
```

### 5. Loops

Iterate over arrays:

```handlebars
{{#each array}}
  {{this}}           // Current item (for simple arrays)
  {{property}}       // Property of current object
  {{@index}}         // Current index (0-based)
{{/each}}
```

Example:
```handlebars
Invoice Lines:
{{#each lines}}
{{@index}}. {{description}}
   Quantity: {{quantity}} × {{unit_price}} = {{total}}
{{/each}}

Tags: {{#each tags}}{{this}}{{#if @index}}, {{/if}}{{/each}}
```

## Available Variables

### Invoice Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{{invoice.number}}` | Invoice number | INV-2025-001 |
| `{{invoice.issue_date}}` | Issue date (formatted) | 01/01/2025 |
| `{{invoice.due_date}}` | Due date (formatted) | 31/01/2025 |
| `{{invoice.total}}` | Total amount (formatted) | 1 170,00 |
| `{{invoice.subtotal}}` | Subtotal (formatted) | 1 000,00 |
| `{{invoice.vat_amount}}` | VAT amount (formatted) | 170,00 |
| `{{invoice.vat_rate}}` | VAT rate | 17 |
| `{{invoice.payment_terms}}` | Payment terms | Net 30 |
| `{{invoice.subject}}` | Invoice subject | Professional Services |
| `{{invoice.period}}` | Billing period | Janvier 2025 |
| `{{invoice.type}}` | Invoice type | rental, retrocession_30, etc. |
| `{{invoice.status}}` | Invoice status | pending, paid, overdue |
| `{{invoice.is_credit_note}}` | Is credit note | true/false |
| `{{invoice.reference_invoice_number}}` | Reference for credit notes | INV-2025-000 |

### Client Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{{client.name}}` | Client name | ABC Company Ltd |
| `{{client.email}}` | Client email | <EMAIL> |
| `{{client.phone}}` | Client phone | +*********** |
| `{{client.address}}` | Street address | 123 Main Street |
| `{{client.city}}` | City | Luxembourg |
| `{{client.postal_code}}` | Postal code | L-1234 |
| `{{client.country}}` | Country | Luxembourg |
| `{{client.vat_number}}` | VAT number | LU12345678 |
| `{{client.is_practitioner}}` | Is practitioner | true/false |

### User Variables (for user invoices)

| Variable | Description | Example |
|----------|-------------|---------|
| `{{user.name}}` | Full name | John Doe |
| `{{user.first_name}}` | First name | John |
| `{{user.last_name}}` | Last name | Doe |
| `{{user.email}}` | Email | <EMAIL> |
| `{{user.invoice_email}}` | Invoice email | <EMAIL> |
| `{{user.phone}}` | Phone | +*********** |
| `{{user.role}}` | User role | admin, practitioner |

### Company Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{{company.name}}` | Company name | Fit360 |
| `{{company.email}}` | Company email | <EMAIL> |
| `{{company.phone}}` | Company phone | +*********** |
| `{{company.address}}` | Company address | 123 Business St |
| `{{company.vat_number}}` | Company VAT | ********** |
| `{{company.website}}` | Website | www.fit360.lu |
| `{{company.bank_account}}` | Bank account | LU12 3456 7890 1234 |
| `{{company.iban}}` | IBAN | LU12 3456 7890 1234 |
| `{{company.bic}}` | BIC/SWIFT | ABCDLULL |

### Line Items (use with loops)

| Variable | Description | Example |
|----------|-------------|---------|
| `{{description}}` | Line description | Office Rental |
| `{{quantity}}` | Quantity | 1 |
| `{{unit_price}}` | Unit price (formatted) | 1 000,00 |
| `{{total}}` | Line total (formatted) | 1 000,00 |
| `{{vat_rate}}` | VAT rate | 17 |

### Retrocession Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{{retrocession.cns_amount}}` | CNS amount | 500,00 |
| `{{retrocession.patient_amount}}` | Patient amount | 200,00 |
| `{{retrocession.secretariat_amount}}` | Secretariat amount | 150,00 |
| `{{retrocession.total_consultations}}` | Total consultations | 25 |
| `{{retrocession.rate}}` | Retrocession rate | 30 |

### System Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `{{current_date}}` | Current date | 28/01/2025 |
| `{{current_year}}` | Current year | 2025 |
| `{{current_month}}` | Current month | 01 |
| `{{current_day}}` | Current day | 28 |

### Legacy Variables (backward compatibility)

| Variable | Description |
|----------|-------------|
| `{CLIENT_NAME}` | Client or user name |
| `{INVOICE_NUMBER}` | Invoice number |
| `{TOTAL_AMOUNT}` | Total amount |
| `{DUE_DATE}` | Due date |
| `{MONTH_NAME}` | Month name (French) |
| `{YEAR}` | Year |

## Complete Template Examples

### Standard Invoice Email

```handlebars
Objet: Facture {{invoice.number}} - {{client.name}}

Bonjour {{client.name|Madame/Monsieur}},

Nous avons le plaisir de vous adresser la facture {{invoice.number}} d'un montant de {{invoice.total}} EUR.

{{#if invoice.is_credit_note}}
Cette note de crédit fait référence à la facture {{invoice.reference_invoice_number}}.
{{else}}
Détail de la facture:
{{#each lines}}
- {{description}}: {{quantity}} × {{unit_price}} = {{total}} EUR
{{/each}}

Sous-total HT: {{invoice.subtotal}} EUR
TVA ({{invoice.vat_rate}}%): {{invoice.vat_amount}} EUR
Total TTC: {{invoice.total}} EUR
{{/if}}

{{#if !invoice.is_paid}}
Date d'échéance: {{invoice.due_date}}
Conditions de paiement: {{invoice.payment_terms|Net 30}}

Vous pouvez effectuer le paiement par virement bancaire:
IBAN: {{company.iban}}
BIC: {{company.bic}}
{{else}}
Nous vous remercions pour votre paiement.
{{/if}}

Cordialement,
{{company.name}}
{{company.email}}
{{company.phone}}
```

### Retrocession Invoice Email

```handlebars
Objet: Rétrocession {{invoice.period}} - {{invoice.number}}

Cher(e) {{client.name}},

Voici votre décompte de rétrocession pour {{invoice.period}}:

Nombre de consultations: {{retrocession.total_consultations}}
Montant CNS: {{retrocession.cns_amount}} EUR
Montant patients: {{retrocession.patient_amount}} EUR
Montant secrétariat: {{retrocession.secretariat_amount}} EUR

Total à recevoir: {{invoice.total}} EUR

{{#if lines}}
Détail des prestations:
{{#each lines}}
- {{description}}
{{/each}}
{{/if}}

Cordialement,
{{company.name}}
```

### Payment Reminder

```handlebars
Objet: Rappel - Facture {{invoice.number}} impayée

{{client.name|Madame/Monsieur}},

{{#if days_overdue}}
Votre facture {{invoice.number}} est en retard de {{days_overdue}} jours.
{{else}}
Nous vous rappelons que la facture {{invoice.number}} arrive à échéance le {{invoice.due_date}}.
{{/if}}

Montant dû: {{invoice.total}} EUR

{{#if !reminder_level}}
Ceci est un rappel amical. 
{{else}}
  {{#if reminder_level|1}}
  Ceci est votre premier rappel.
  {{/if}}
  {{#if reminder_level|2}}
  Ceci est votre deuxième rappel. Des frais de retard peuvent s'appliquer.
  {{/if}}
  {{#if reminder_level|3}}
  Ceci est votre dernier rappel avant action en recouvrement.
  {{/if}}
{{/if}}

Merci de régulariser votre situation dans les plus brefs délais.

{{company.name}}
```

## Best Practices

1. **Use nested variables** for better organization:
   - Good: `{{invoice.number}}`
   - Avoid: `{{INVOICE_NUMBER}}`

2. **Provide default values** for optional fields:
   - `{{client.phone|Non renseigné}}`
   - `{{invoice.notes|Aucune note}}`

3. **Use conditionals** for dynamic content:
   - Show payment instructions only for unpaid invoices
   - Display credit note references when applicable

4. **Format currency** consistently:
   - Variables already include formatting (e.g., "1 170,00")
   - Add currency symbol in template: `{{invoice.total}} EUR`

5. **Test templates** with different scenarios:
   - Paid vs unpaid invoices
   - With and without VAT
   - Credit notes
   - Different invoice types

## Troubleshooting

### Variable not replaced
- Check variable name spelling
- Verify the variable exists in the data
- Use default values: `{{variable|default}}`

### Conditional not working
- Ensure boolean values are actual booleans, not strings
- Use negation for false checks: `{{#if !is_paid}}`

### Loop not iterating
- Verify the variable is an array
- Check array is not empty
- Use correct syntax for accessing properties

### Special characters
- Variables are automatically escaped for safety
- Currency symbols should be in the template, not the data