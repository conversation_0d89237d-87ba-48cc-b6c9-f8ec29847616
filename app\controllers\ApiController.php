<?php

namespace App\Controllers;

use Flight;
use App\Core\Controller;

class ApiController extends Controller
{
    /**
     * Get VAT rates for AJAX requests
     */
    public function getVatRates()
    {
        header('Content-Type: application/json');
        
        try {
            $db = Flight::db();
            $stmt = $db->query("
                SELECT id, code, name, rate, is_default, is_active 
                FROM config_vat_rates 
                WHERE is_active = 1 
                ORDER BY is_default DESC, rate ASC
            ");
            
            $rates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Format rates for frontend
            $formattedRates = [];
            $currentLang = $_SESSION['language'] ?? 'en';
            
            foreach ($rates as $rate) {
                // Decode JSON name and get localized version
                $names = json_decode($rate['name'], true);
                $displayName = $rate['name']; // fallback to raw value
                
                if (is_array($names)) {
                    // Try to get name in current language, fallback to English, then any available
                    if (isset($names[$currentLang])) {
                        $displayName = $names[$currentLang];
                    } elseif (isset($names['en'])) {
                        $displayName = $names['en'];
                    } elseif (!empty($names)) {
                        $displayName = reset($names); // Get first available name
                    }
                } elseif (is_string($names)) {
                    // If it's already a string, use it as is
                    $displayName = $names;
                }
                
                $formattedRates[] = [
                    'id' => $rate['id'],
                    'name' => $displayName,
                    'rate' => floatval($rate['rate']),
                    'is_default' => (bool)$rate['is_default']
                ];
            }
            
            echo json_encode([
                'success' => true,
                'rates' => $formattedRates
            ]);
        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error loading VAT rates',
                'rates' => []
            ]);
        }
    }
    
    /**
     * Search billable entities (clients, users, patients)
     */
    public function searchBillable()
    {
        header('Content-Type: application/json');
        
        $type = $_GET['type'] ?? '';
        $search = $_GET['search'] ?? '';
        
        try {
            $db = Flight::db();
            $results = [];
            
            switch ($type) {
                case 'client':
                    $sql = "SELECT id, name, client_number AS code, email, phone 
                            FROM clients 
                            WHERE is_active = 1 ";
                    
                    if ($search) {
                        $sql .= " AND (name LIKE :search OR client_number LIKE :search OR email LIKE :search)";
                    }
                    
                    $sql .= " ORDER BY name ASC LIMIT 50";
                    
                    $stmt = $db->prepare($sql);
                    if ($search) {
                        $stmt->execute(['search' => '%' . $search . '%']);
                    } else {
                        $stmt->execute();
                    }
                    
                    $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                    break;
                    
                case 'user':
                    $sql = "SELECT id, CONCAT(first_name, ' ', last_name) AS name, username AS code, email 
                            FROM users 
                            WHERE is_active = 1 ";
                    
                    if ($search) {
                        $sql .= " AND (first_name LIKE :search OR last_name LIKE :search OR username LIKE :search)";
                    }
                    
                    $sql .= " ORDER BY first_name, last_name ASC LIMIT 50";
                    
                    $stmt = $db->prepare($sql);
                    if ($search) {
                        $stmt->execute(['search' => '%' . $search . '%']);
                    } else {
                        $stmt->execute();
                    }
                    
                    $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                    break;
                    
                case 'patient':
                    // Check if patients table exists
                    $stmt = $db->query("SHOW TABLES LIKE 'patients'");
                    if ($stmt->rowCount() > 0) {
                        $sql = "SELECT id, CONCAT(first_name, ' ', last_name) AS name, patient_code AS code, email 
                                FROM patients 
                                WHERE is_active = 1 ";
                        
                        if ($search) {
                            $sql .= " AND (first_name LIKE :search OR last_name LIKE :search OR patient_code LIKE :search)";
                        }
                        
                        $sql .= " ORDER BY first_name, last_name ASC LIMIT 50";
                        
                        $stmt = $db->prepare($sql);
                        if ($search) {
                            $stmt->execute(['search' => '%' . $search . '%']);
                        } else {
                            $stmt->execute();
                        }
                        
                        $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
                    }
                    break;
            }
            
            echo json_encode([
                'success' => true,
                'results' => $results
            ]);
        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error loading data: ' . $e->getMessage(),
                'results' => []
            ]);
        }
    }
    
    /**
     * Search catalog items
     */
    public function searchCatalog()
    {
        header('Content-Type: application/json');
        
        $term = $_GET['term'] ?? '';
        
        try {
            $db = Flight::db();
            
            // Check if catalog_items table exists
            $stmt = $db->query("SHOW TABLES LIKE 'catalog_items'");
            if ($stmt->rowCount() === 0) {
                echo json_encode([
                    'success' => false,
                    'message' => 'Catalog not available',
                    'items' => []
                ]);
                return;
            }
            
            $sql = "SELECT 
                        ci.id, ci.code, ci.name, ci.description, 
                        ci.unit_price, ci.vat_rate_id,
                        ci.is_stockable, ci.current_stock,
                        cc.name as category_name,
                        vr.rate as vat_rate
                    FROM catalog_items ci
                    LEFT JOIN catalog_categories cc ON ci.category_id = cc.id
                    LEFT JOIN config_vat_rates vr ON ci.vat_rate_id = vr.id
                    WHERE ci.is_active = 1 ";
            
            if ($term) {
                $sql .= " AND (ci.code LIKE :term OR ci.name LIKE :term OR ci.description LIKE :term)";
            }
            
            $sql .= " ORDER BY ci.name ASC LIMIT 20";
            
            $stmt = $db->prepare($sql);
            if ($term) {
                $stmt->execute(['term' => '%' . $term . '%']);
            } else {
                $stmt->execute();
            }
            
            $items = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'items' => $items
            ]);
        } catch (\Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Error searching catalog: ' . $e->getMessage(),
                'items' => []
            ]);
        }
    }
}