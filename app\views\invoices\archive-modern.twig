{% extends "base-modern.twig" %}
{% import '_macros/table-helper-v2.twig' as tableHelper %}

{% block title %}{{ __('invoices.archive') }}{% endblock %}

{% block styles %}
<style>
    /* Archive page styling */
    .archive-tabs {
        margin-bottom: 2rem;
    }
    
    .nav-tabs .nav-link {
        border: none;
        border-bottom: 3px solid transparent;
        color: #6c757d;
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        transition: all 0.3s;
    }
    
    .nav-tabs .nav-link:hover {
        border-bottom-color: #e9ecef;
        color: #495057;
    }
    
    .nav-tabs .nav-link.active {
        color: #0d6efd;
        border-bottom-color: #0d6efd;
        background: transparent;
    }
    
    .nav-tabs .nav-link .badge {
        margin-left: 0.5rem;
        background-color: #e9ecef;
        color: #495057;
        font-weight: 600;
    }
    
    .nav-tabs .nav-link.active .badge {
        background-color: #0d6efd;
        color: white;
    }
    
    .archive-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
        padding: 1.5rem 0;
        margin-bottom: 2rem;
    }
    
    .archive-stats {
        display: flex;
        gap: 2rem;
        margin-top: 1rem;
    }
    
    .archive-stat {
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }
    
    .archive-stat-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        font-size: 1.25rem;
    }
    
    .archive-indicator {
        display: inline-flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: #6c757d;
        background-color: #f8f9fa;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block content %}
<div class="archive-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="h3 mb-0">{{ __('invoices.archive') }}</h1>
                <p class="text-muted mb-0 mt-1">{{ __('invoices.archive_description')|default('View and manage archived invoices by document type') }}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="{{ base_url }}/invoices" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left me-2"></i>{{ __('invoices.back_to_invoices') }}
                </a>
            </div>
        </div>
        
        <div class="archive-stats">
            {% for stat in archiveStats %}
            {% if stat.count > 0 %}
            <div class="archive-stat">
                <div class="archive-stat-icon" style="background-color: {{ stat.color|default('#e9ecef') }}20; color: {{ stat.color|default('#6c757d') }};">
                    <i class="{{ stat.icon|default('bi bi-file-text') }}"></i>
                </div>
                <div>
                    <div class="fw-bold">{{ stat.count|number_format }}</div>
                    <div class="small text-muted">{{ stat.document_type_display_name }}</div>
                </div>
            </div>
            {% endif %}
            {% endfor %}
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Document Type Tabs -->
    <ul class="nav nav-tabs archive-tabs" role="tablist">
        <li class="nav-item">
            <a class="nav-link {% if not filters.document_type %}active{% endif %}" 
               href="{{ base_url }}/invoices/archive"
               role="tab">
                {{ __('common.all') }}
                <span class="badge">{{ archiveStats|reduce((total, stat) => total + stat.count, 0)|number_format }}</span>
            </a>
        </li>
        {% for stat in archiveStats %}
        <li class="nav-item">
            <a class="nav-link {% if filters.document_type == stat.document_type_id %}active{% endif %}" 
               href="{{ base_url }}/invoices/archive?document_type={{ stat.document_type_id }}"
               role="tab">
                <i class="{{ stat.icon|default('bi bi-file-text') }} me-1"></i>
                {{ stat.document_type_display_name }}
                <span class="badge">{{ stat.count|number_format }}</span>
            </a>
        </li>
        {% endfor %}
    </ul>

    <!-- Archived Invoices Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-body p-0">
            {% set tableContent %}
                {{ tableHelper.tableHeader([
                    { label: __('invoices.invoice_number'), sortable: true },
                    { label: __('invoices.document_type'), sortable: true },
                    { label: __('invoices.invoice_type'), sortable: true },
                    { label: __('clients.client') ~ '/' ~ __('patients.patient'), sortable: true },
                    { label: __('invoices.issue_date'), sortable: true },
                    { label: __('invoices.archived_date'), sortable: true },
                    { label: __('invoices.amount'), sortable: true, class: 'text-end' },
                    { label: __('common.status'), sortable: true },
                    { label: __('common.actions'), width: 100, sortable: false, reorderable: false, isAction: true }
                ], true) }}
                <tbody>
                    {% for invoice in invoices %}
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input row-checkbox" value="{{ invoice.id }}">
                        </td>
                        <td>
                            <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="text-decoration-none">
                                {{ invoice.invoice_number }}
                            </a>
                            <div class="archive-indicator mt-1">
                                <i class="bi bi-archive-fill"></i>
                                {{ __('invoices.archived') }}
                            </div>
                        </td>
                        <td>
                            {% if invoice.doc_type_color %}
                                <span class="badge" style="background-color: {{ invoice.doc_type_color }};">
                                    {% if invoice.doc_type_icon %}
                                        <i class="{{ invoice.doc_type_icon }} me-1"></i>
                                    {% endif %}
                                    {{ invoice.doc_type_display_name }}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">{{ __('common.unknown') }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if invoice.type_color %}
                                <span class="badge" style="background-color: {{ invoice.type_color }};">
                                    {{ invoice.type_name }}
                                </span>
                            {% else %}
                                <span class="text-muted">{{ invoice.type_name|default('N/A') }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                {% if invoice.patient_id %}
                                    <i class="bi bi-person-circle text-primary me-2"></i>
                                    <span>{{ invoice.patient_name }}</span>
                                {% else %}
                                    <i class="bi bi-building text-info me-2"></i>
                                    <span>{{ invoice.client_name }}</span>
                                {% endif %}
                            </div>
                        </td>
                        <td data-sort="{{ invoice.issue_date|date('Y-m-d') }}">
                            {{ invoice.issue_date|date('d/m/Y') }}
                        </td>
                        <td data-sort="{{ invoice.archived_at|date('Y-m-d H:i:s') }}">
                            {{ invoice.archived_at|date('d/m/Y H:i') }}
                        </td>
                        <td class="text-end" data-sort="{{ invoice.total }}">
                            <span class="fw-medium">{{ currency }}{{ invoice.total|number_format(2, ',', ' ') }}</span>
                        </td>
                        <td>
                            {% set statusConfig = {
                                'draft': { class: 'secondary', icon: 'bi-pencil', label: __('invoices.status.draft') },
                                'sent': { class: 'info', icon: 'bi-send', label: __('invoices.status.sent') },
                                'paid': { class: 'success', icon: 'bi-check-circle', label: __('invoices.status.paid') },
                                'partial': { class: 'warning', icon: 'bi-circle-half', label: __('invoices.status.partial') },
                                'overdue': { class: 'danger', icon: 'bi-exclamation-circle', label: __('invoices.status.overdue') },
                                'cancelled': { class: 'dark', icon: 'bi-x-circle', label: __('invoices.status.cancelled') }
                            } %}
                            {% set status = statusConfig[invoice.status]|default({ class: 'secondary', label: invoice.status }) %}
                            <span class="badge bg-{{ status.class }}">
                                {% if status.icon is defined %}
                                    <i class="{{ status.icon }} me-1"></i>
                                {% endif %}
                                {{ status.label }}
                            </span>
                        </td>
                        <td>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-light border-0" type="button" data-bs-toggle="dropdown" 
                                        data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}">
                                            <i class="bi bi-eye text-primary"></i>{{ __('common.view') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/print" target="_blank">
                                            <i class="bi bi-printer text-secondary"></i>{{ __('common.print') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{{ base_url }}/invoices/{{ invoice.id }}/download">
                                            <i class="bi bi-download text-info"></i>{{ __('invoices.download_pdf') }}
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="restoreInvoice({{ invoice.id }})">
                                            <i class="bi bi-arrow-counterclockwise text-success"></i>{{ __('invoices.restore') }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="10" class="empty-state">
                            <i class="bi bi-archive"></i>
                            <p>{{ __('invoices.no_archived_invoices')|default('No archived invoices found') }}</p>
                            <a href="{{ base_url }}/invoices" class="btn btn-primary btn-sm mt-2">
                                <i class="bi bi-arrow-left me-2"></i>{{ __('invoices.back_to_invoices') }}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            {% endset %}

            {{ tableHelper.tableWithFilters({
                baseUrl: base_url,
                tableId: 'archiveTable',
                formAction: base_url ~ '/invoices/archive',
                storageKey: 'invoices_archive_filters',
                tableContent: tableContent,
                searchColumns: [1, 3, 4],
                searchPlaceholder: __('invoices.search_placeholder')|default('Search by invoice number, client, or patient...'),
                sortable: true,
                reorderable: false,
                defaultSort: { column: 6, direction: 'desc' },
                showColumnToggle: false,
                filters: [
                    {
                        id: 'document_type',
                        name: 'document_type',
                        label: __('invoices.document_type'),
                        type: 'select',
                        width: 3,
                        value: filters.document_type|default(''),
                        placeholder: __('common.all'),
                        options: documentTypes|default({})
                    },
                    {
                        id: 'status',
                        name: 'status',
                        label: __('common.status'),
                        type: 'select',
                        width: 3,
                        value: filters.status|default(''),
                        placeholder: __('common.all'),
                        options: statuses
                    },
                    {
                        id: 'date_from',
                        name: 'date_from',
                        label: __('invoices.archived_from'),
                        type: 'date',
                        width: 3,
                        value: filters.date_from|default('')
                    },
                    {
                        id: 'date_to',
                        name: 'date_to',
                        label: __('invoices.archived_to'),
                        type: 'date',
                        width: 3,
                        value: filters.date_to|default('')
                    }
                ],
                filterConfigs: [
                    { id: 'document_type', autoSubmit: true },
                    { id: 'status', autoSubmit: true },
                    { id: 'date_from', autoSubmit: true },
                    { id: 'date_to', autoSubmit: true }
                ],
                showImport: false,
                showExport: true,
                exportUrl: base_url ~ '/invoices/export?archived=1',
                exportFormats: ['csv', 'excel', 'pdf'],
                showBulkActions: true,
                bulkActions: [
                    {
                        action: 'restore',
                        label: __('invoices.restore_selected'),
                        icon: 'bi bi-arrow-counterclockwise',
                        url: base_url ~ '/invoices/bulk-restore',
                        class: 'text-success'
                    },
                    {
                        action: 'export',
                        label: __('invoices.export_selected'),
                        icon: 'bi bi-download',
                        url: base_url ~ '/invoices/bulk-export?archived=1'
                    }
                ],
                bulkActionUrl: base_url ~ '/invoices/bulk-action',
                pagination: {
                    current_page: current_page|default(1),
                    total_pages: total_pages|default(1),
                    base_url: base_url ~ '/invoices/archive'
                },
                resetUrl: base_url ~ '/invoices/archive?reset_filters=1'
            }) }}
        </div>
    </div>
</div>

<!-- Archive Scripts -->
<script>
function restoreInvoice(id) {
    Swal.fire({
        title: '{{ __("invoices.restore_invoice") }}',
        text: '{{ __("invoices.restore_confirm") }}',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#198754',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '{{ __("invoices.restore") }}',
        cancelButtonText: '{{ __("common.cancel") }}'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '{{ base_url }}/invoices/' + id + '/restore';
            
            const csrf = document.createElement('input');
            csrf.type = 'hidden';
            csrf.name = 'csrf_token';
            csrf.value = '{{ csrf_token }}';
            form.appendChild(csrf);
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>

<style>
/* Fix for dropdown positioning in tables */
.table-responsive .dropdown {
    position: static;
}

.table-responsive .dropdown-menu {
    position: absolute !important;
    transform: translate3d(0px, 0px, 0px) !important;
    will-change: transform !important;
}
</style>
{% endblock %}

{% block table_helper %}
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
{% endblock %}