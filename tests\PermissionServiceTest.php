<?php

use PHPUnit\Framework\TestCase;
use App\Services\PermissionService;

class PermissionServiceTest extends TestCase
{
    private $permissionService;
    private $originalSession;
    
    protected function setUp(): void
    {
        parent::setUp();
        
        // Backup original session
        $this->originalSession = $_SESSION ?? [];
        
        // Initialize service
        $this->permissionService = PermissionService::getInstance();
        
        // Clear any existing cache
        $this->permissionService->clearPermissionCache();
    }
    
    protected function tearDown(): void
    {
        // Restore original session
        $_SESSION = $this->originalSession;
        
        // Clear cache
        $this->permissionService->clearPermissionCache();
        
        parent::tearDown();
    }
    
    public function testSuperAdminBypass()
    {
        // Set super admin user
        $_SESSION['user_id'] = 1;
        
        // Super admin should have any permission
        $this->assertTrue($this->permissionService->hasPermission('any.permission'));
        $this->assertTrue($this->permissionService->hasPermission('does.not.exist'));
        $this->assertTrue($this->permissionService->hasAnyPermission(['foo', 'bar']));
        $this->assertTrue($this->permissionService->hasAllPermissions(['foo', 'bar', 'baz']));
    }
    
    public function testNoUserReturnsfalse()
    {
        // No user in session
        unset($_SESSION['user_id']);
        
        $this->assertFalse($this->permissionService->hasPermission('users.create'));
        $this->assertFalse($this->permissionService->hasAnyPermission(['users.create']));
        $this->assertFalse($this->permissionService->hasAllPermissions(['users.create']));
    }
    
    public function testIsSuperAdmin()
    {
        // Test with super admin
        $_SESSION['user_id'] = 1;
        $this->assertTrue($this->permissionService->isSuperAdmin());
        
        // Test with regular user
        $_SESSION['user_id'] = 2;
        $this->assertFalse($this->permissionService->isSuperAdmin());
        
        // Test with specific user ID
        $this->assertTrue($this->permissionService->isSuperAdmin(1));
        $this->assertFalse($this->permissionService->isSuperAdmin(2));
    }
    
    public function testGetUserPermissionsForSuperAdmin()
    {
        $_SESSION['user_id'] = 1;
        
        $permissions = $this->permissionService->getUserPermissions();
        
        // Super admin should get all available permissions
        $this->assertIsArray($permissions);
        $this->assertNotEmpty($permissions);
    }
    
    public function testCacheClear()
    {
        $_SESSION['user_id'] = 2;
        
        // This should not throw an exception
        $this->permissionService->clearPermissionCache();
        $this->permissionService->clearPermissionCache(2);
        $this->permissionService->clearPermissionCache(null);
        
        $this->assertTrue(true); // If we get here, no exception was thrown
    }
    
    public function testHelperFunctions()
    {
        $_SESSION['user_id'] = 1;
        
        // Test helper functions exist and work with super admin
        $this->assertTrue(hasPermission('test.permission'));
        $this->assertTrue(hasAnyPermission(['test1', 'test2']));
        $this->assertTrue(hasAllPermissions(['test1', 'test2', 'test3']));
        $this->assertTrue(isSuperAdmin());
        $this->assertTrue(canPerformAction('users', 'create'));
        
        $permissions = getUserPermissions();
        $this->assertIsArray($permissions);
        
        $checkedPermissions = checkPermissions(['users.create', 'users.delete']);
        $this->assertIsArray($checkedPermissions);
        $this->assertTrue($checkedPermissions['users.create']);
        $this->assertTrue($checkedPermissions['users.delete']);
    }
    
    public function testRequirePermissionThrowsException()
    {
        $_SESSION['user_id'] = 2; // Regular user
        
        $this->expectException(Exception::class);
        $this->expectExceptionCode(403);
        
        requirePermission('admin.super.secret');
    }
    
    public function testPermissionServiceSingleton()
    {
        $instance1 = PermissionService::getInstance();
        $instance2 = PermissionService::getInstance();
        
        $this->assertSame($instance1, $instance2);
    }
}

// Simple integration test
class PermissionIntegrationTest extends TestCase
{
    public function testMiddlewareIntegration()
    {
        $_SESSION['user_id'] = 1;
        
        // Test middleware closures can be created
        $middleware = \App\Middleware\PermissionMiddleware::require('users.create');
        $this->assertIsCallable($middleware);
        
        $middlewareAny = \App\Middleware\PermissionMiddleware::requireAny(['users.create', 'users.update']);
        $this->assertIsCallable($middlewareAny);
        
        $middlewareAll = \App\Middleware\PermissionMiddleware::requireAll(['users.create', 'users.update']);
        $this->assertIsCallable($middlewareAll);
        
        $middlewareApi = \App\Middleware\PermissionMiddleware::requireApi('users.create');
        $this->assertIsCallable($middlewareApi);
        
        $middlewareSuperAdmin = \App\Middleware\PermissionMiddleware::superAdminOnly();
        $this->assertIsCallable($middlewareSuperAdmin);
    }
}