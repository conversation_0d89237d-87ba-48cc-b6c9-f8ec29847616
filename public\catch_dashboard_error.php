<?php
/**
 * Catch Dashboard Error - Direct Dashboard Load with Full Error Catching
 */

// Enable ALL error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/dashboard_error.log');

// Set custom error handler
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    echo "<div style='background: #fee; padding: 10px; margin: 5px; border: 1px solid #c00;'>";
    echo "<strong>Error [$errno]:</strong> $errstr<br>";
    echo "<small>File: $errfile:$errline</small>";
    echo "</div>";
    return true;
});

// Set exception handler
set_exception_handler(function($exception) {
    echo "<div style='background: #fee; padding: 10px; margin: 5px; border: 1px solid #c00;'>";
    echo "<strong>Exception:</strong> " . $exception->getMessage() . "<br>";
    echo "<small>File: " . $exception->getFile() . ":" . $exception->getLine() . "</small><br>";
    echo "<pre>" . $exception->getTraceAsString() . "</pre>";
    echo "</div>";
});

echo "<h1>Dashboard Error Catcher</h1>";

try {
    // Step 1: Load the application
    echo "<p>Loading application...</p>";
    
    // Define APP_PATH
    define('APP_PATH', dirname(__DIR__));
    
    // Load vendor autoload
    require APP_PATH . '/vendor/autoload.php';
    
    // Load .env
    if (file_exists(APP_PATH . '/.env')) {
        $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
        $dotenv->load();
    }
    
    // Start session
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Load bootstrap
    echo "<p>Loading bootstrap...</p>";
    require APP_PATH . '/app/config/bootstrap.php';
    
    // Now try to manually execute the dashboard route
    echo "<p>Executing dashboard route...</p>";
    
    // Get the dashboard controller
    $controller = new \App\Controllers\DashboardController();
    
    // Capture output
    ob_start();
    
    try {
        // Call the index method
        $controller->index();
        $output = ob_get_clean();
        
        echo "<div style='background: #efe; padding: 10px; margin: 5px; border: 1px solid #0c0;'>";
        echo "<strong>Dashboard Output:</strong><br>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; background: white;'>";
        echo $output;
        echo "</div>";
        echo "</div>";
        
    } catch (Exception $e) {
        ob_end_clean();
        throw $e;
    }
    
} catch (Exception $e) {
    echo "<div style='background: #fee; padding: 10px; margin: 5px; border: 1px solid #c00;'>";
    echo "<h2>Caught Exception!</h2>";
    echo "<strong>Message:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>File:</strong> " . htmlspecialchars($e->getFile()) . "<br>";
    echo "<strong>Line:</strong> " . $e->getLine() . "<br>";
    echo "<strong>Type:</strong> " . get_class($e) . "<br>";
    echo "<h3>Stack Trace:</h3>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
    
    // Try to get more details about the error
    if (method_exists($e, 'getPrevious') && $e->getPrevious()) {
        echo "<h3>Previous Exception:</h3>";
        $prev = $e->getPrevious();
        echo "<strong>Message:</strong> " . htmlspecialchars($prev->getMessage()) . "<br>";
        echo "<strong>File:</strong> " . htmlspecialchars($prev->getFile()) . "<br>";
        echo "<strong>Line:</strong> " . $prev->getLine() . "<br>";
    }
    echo "</div>";
}

// Check for any logged errors
$log_file = __DIR__ . '/dashboard_error.log';
if (file_exists($log_file) && filesize($log_file) > 0) {
    echo "<h2>Error Log:</h2>";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>";
    echo htmlspecialchars(file_get_contents($log_file));
    echo "</pre>";
}