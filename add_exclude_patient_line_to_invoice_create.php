<?php
/**
 * Add exclude patient line option to invoice create form for retrocession invoices
 */

// Read the create-modern.twig file
$viewFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($viewFile);

// Find where to insert the exclude patient line option
// We'll add it after the invoice type selection, only visible for retrocession invoices

// First, let's add the HTML for the checkbox after the invoice subject field
$searchFor = '</div>
                    </div>
                </div>
            </div>
        </div>';

$insertAfter = '</div>
                    </div>
                    
                    <!-- Exclude Patient Line (for retrocession invoices only) -->
                    <div class="col-md-12" id="exclude_patient_line_container" style="display: none;">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="exclude_patient_line" 
                                   name="exclude_patient_line" value="1">
                            <label class="form-check-label" for="exclude_patient_line">
                                {{ __("retrocession.exclude_patient_line") }}
                                <small class="text-muted d-block">{{ __("retrocession.exclude_patient_line_help") }}</small>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>';

// Replace the content
if (strpos($content, 'id="exclude_patient_line"') === false) {
    $content = str_replace($searchFor, $insertAfter, $content);
    
    // Now add JavaScript to show/hide the checkbox based on invoice type
    $jsToAdd = "
    // Show/hide exclude patient line option based on invoice type
    function toggleExcludePatientLine() {
        const typeSelect = document.getElementById('document_type_id');
        const container = document.getElementById('exclude_patient_line_container');
        
        if (!typeSelect || !container) return;
        
        const selectedOption = typeSelect.options[typeSelect.selectedIndex];
        const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        const typeId = typeSelect.value;
        
        // Show for RET invoices (RET, RET25, RET30)
        const isRetrocession = typePrefix && typePrefix.startsWith('RET');
        container.style.display = isRetrocession ? 'block' : 'none';
        
        // Clear checkbox if hiding
        if (!isRetrocession) {
            document.getElementById('exclude_patient_line').checked = false;
        }
    }
    
    // Add event listener for document type change
    document.getElementById('document_type_id').addEventListener('change', toggleExcludePatientLine);
    
    // Check on page load
    toggleExcludePatientLine();
    
    // Modify the initializeRetrocessionInvoice function to handle exclude patient line
    const originalInitRetro = initializeRetrocessionInvoice;
    initializeRetrocessionInvoice = function(isRet25) {
        originalInitRetro(isRet25);
        
        // Check if patient line should be excluded
        const excludePatient = document.getElementById('exclude_patient_line').checked;
        
        if (excludePatient) {
            // Find and remove the patient line
            const rows = document.querySelectorAll('#invoice-items tbody tr');
            rows.forEach(row => {
                const descInput = row.querySelector('input[name*=\"[description]\"]');
                if (descInput && descInput.value && descInput.value.toUpperCase().includes('PATIENT')) {
                    row.remove();
                }
            });
            
            // Recalculate totals
            calculateRetrocessionTotals();
        }
    };
    
    // Also handle checkbox change
    document.getElementById('exclude_patient_line').addEventListener('change', function() {
        const typeSelect = document.getElementById('document_type_id');
        const selectedOption = typeSelect.options[typeSelect.selectedIndex];
        const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        
        if (typePrefix && typePrefix.startsWith('RET')) {
            if (this.checked) {
                // Remove patient line
                const rows = document.querySelectorAll('#invoice-items tbody tr');
                rows.forEach(row => {
                    const descInput = row.querySelector('input[name*=\"[description]\"]');
                    if (descInput && descInput.value && descInput.value.toUpperCase().includes('PATIENT')) {
                        row.remove();
                    }
                });
            } else {
                // Re-add patient line if it doesn't exist
                const rows = document.querySelectorAll('#invoice-items tbody tr');
                let hasPatientLine = false;
                rows.forEach(row => {
                    const descInput = row.querySelector('input[name*=\"[description]\"]');
                    if (descInput && descInput.value && descInput.value.toUpperCase().includes('PATIENT')) {
                        hasPatientLine = true;
                    }
                });
                
                if (!hasPatientLine) {
                    // Re-initialize to add all lines
                    const isRet25 = typePrefix === 'RET25';
                    initializeRetrocessionInvoice(isRet25);
                }
            }
            
            calculateRetrocessionTotals();
        }
    });";
    
    // Find where to insert the JavaScript (before the closing script tag)
    $scriptInsertPoint = "// Check if this is a retrocession invoice and ensure practitioners are loaded";
    $content = str_replace($scriptInsertPoint, $jsToAdd . "\n\n" . $scriptInsertPoint, $content);
    
    // Save the file
    file_put_contents($viewFile, $content);
    
    echo "✓ Added exclude patient line option to invoice create form\n";
    echo "  - Checkbox added after invoice subject field\n";
    echo "  - JavaScript added to show/hide based on invoice type\n";
    echo "  - Logic added to remove/add patient line when checkbox is toggled\n";
} else {
    echo "⚠ Exclude patient line option already exists in the form\n";
}

// Also update the InvoiceController to handle the exclude_patient_line field
$controllerFile = __DIR__ . '/app/controllers/InvoiceController.php';
$controllerContent = file_get_contents($controllerFile);

// Find the store method where invoice data is prepared
$searchPattern = "'payment_method' => \$data\['payment_method'\] ?? null,";
$insertAfter = "'payment_method' => \$data['payment_method'] ?? null,
            'retro_exclude_patient_line' => !empty(\$data['exclude_patient_line']) ? 1 : 0,";

if (strpos($controllerContent, 'retro_exclude_patient_line') === false) {
    $controllerContent = str_replace($searchPattern, $insertAfter, $controllerContent);
    file_put_contents($controllerFile, $controllerContent);
    echo "\n✓ Updated InvoiceController to handle exclude_patient_line field\n";
} else {
    echo "\n⚠ InvoiceController already handles exclude_patient_line field\n";
}

echo "\n✅ Done! Now when creating a retrocession invoice:\n";
echo "1. Select a RET type invoice (RET, RET25, RET30)\n";
echo "2. A checkbox 'Exclude patient line' will appear\n";
echo "3. Check it to exclude the patient line from the invoice\n";
echo "4. The patient line will be removed and totals recalculated\n";