<?php
/**
 * Check Users Table Structure
 */

// Load environment
define('APP_PATH', dirname(__DIR__));
require APP_PATH . '/vendor/autoload.php';

if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Connect to database
try {
    $db = new PDO(
        "mysql:host=" . ($_ENV['DB_HOST'] ?? '127.0.0.1') . ";dbname=" . ($_ENV['DB_DATABASE'] ?? 'fitapp'),
        $_ENV['DB_USERNAME'] ?? 'root',
        $_ENV['DB_PASSWORD'] ?? ''
    );
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Users Table Structure</h1>";
    echo "<pre>";
    
    // Get table structure
    $stmt = $db->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Columns in users table:\n";
    echo str_pad("Field", 20) . str_pad("Type", 30) . str_pad("Null", 10) . str_pad("Key", 10) . "\n";
    echo str_repeat("-", 70) . "\n";
    
    $hasNameColumn = false;
    $hasFirstNameColumn = false;
    $hasLastNameColumn = false;
    
    foreach ($columns as $col) {
        echo str_pad($col['Field'], 20);
        echo str_pad($col['Type'], 30);
        echo str_pad($col['Null'], 10);
        echo str_pad($col['Key'], 10);
        echo "\n";
        
        if ($col['Field'] === 'name') $hasNameColumn = true;
        if ($col['Field'] === 'first_name') $hasFirstNameColumn = true;
        if ($col['Field'] === 'last_name') $hasLastNameColumn = true;
    }
    
    echo "\n\nAnalysis:\n";
    if (!$hasNameColumn && ($hasFirstNameColumn || $hasLastNameColumn)) {
        echo "⚠ The table uses first_name/last_name instead of a single 'name' column.\n";
        echo "This is causing the error when code tries to access the 'name' field.\n";
        
        // Check a sample user
        echo "\nSample user data:\n";
        $stmt = $db->query("SELECT id, first_name, last_name, email FROM users LIMIT 1");
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($user) {
            print_r($user);
        }
        
        echo "\n\nFix Options:\n";
        echo "1. Add a 'name' column that combines first_name and last_name\n";
        echo "2. Update the code to use first_name/last_name instead of name\n";
        
        echo "\n<form method='post'>\n";
        echo "<button type='submit' name='add_name_column' value='1'>Add 'name' Column (Recommended)</button>\n";
        echo "</form>\n";
    } elseif ($hasNameColumn) {
        echo "✓ The 'name' column exists.\n";
    }
    
    echo "</pre>";
    
    // Handle adding name column
    if (isset($_POST['add_name_column'])) {
        try {
            echo "<h2>Adding 'name' column...</h2>";
            echo "<pre>";
            
            // Add the name column
            $db->exec("ALTER TABLE users ADD COLUMN name VARCHAR(255) AFTER id");
            echo "✓ Added 'name' column\n";
            
            // Populate it with first_name + last_name
            $db->exec("UPDATE users SET name = CONCAT(IFNULL(first_name, ''), ' ', IFNULL(last_name, ''))");
            echo "✓ Populated 'name' column with combined first_name and last_name\n";
            
            // Trim any extra spaces
            $db->exec("UPDATE users SET name = TRIM(name)");
            echo "✓ Cleaned up name values\n";
            
            echo "\n<strong>Success! The 'name' column has been added.</strong>\n";
            echo "<a href='/fit/public/'>Try the dashboard now</a>\n";
            echo "</pre>";
        } catch (Exception $e) {
            echo "<pre style='color: red;'>Error: " . $e->getMessage() . "</pre>";
        }
    }
    
} catch (Exception $e) {
    echo "<h1>Database Error</h1>";
    echo "<pre style='color: red;'>" . $e->getMessage() . "</pre>";
}