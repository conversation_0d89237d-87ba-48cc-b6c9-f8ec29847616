{% extends "base-modern.twig" %}

{% block title %}{{ __('billing.wizard_configure_options') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Wizard Progress -->
    <div class="mb-4">
        <div class="d-flex justify-content-center">
            <div class="wizard-progress">
                <div class="wizard-step completed">
                    <div class="step-number"><i class="bi bi-check"></i></div>
                    <div class="step-label">{{ __('billing.select_invoices') }}</div>
                </div>
                <div class="wizard-line completed"></div>
                <div class="wizard-step active">
                    <div class="step-number">2</div>
                    <div class="step-label">{{ __('billing.configure_options') }}</div>
                </div>
                <div class="wizard-line"></div>
                <div class="wizard-step">
                    <div class="step-number">3</div>
                    <div class="step-label">{{ __('billing.review_generate') }}</div>
                </div>
            </div>
        </div>
    </div>

    <!-- <PERSON> -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('billing.configure_invoice_options') }}</h1>
        <a href="{{ base_url }}/billing-wizard" class="btn btn-secondary">
            <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
        </a>
    </div>

    <form method="POST" action="{{ base_url }}/billing-wizard/step-3" id="optionsForm">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" name="session_id" value="{{ session_id }}">
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Invoice Details -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('billing.invoice_details') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="invoice_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                                <input type="date" class="form-control" id="invoice_date" name="invoice_date" 
                                       value="{{ 'now'|date('Y-m-d') }}" required>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="due_days" class="form-label">{{ __('billing.payment_terms') }}</label>
                                <select class="form-select" id="due_days" name="due_days">
                                    <option value="0">{{ __('billing.due_immediately') }}</option>
                                    <option value="15">{{ __('billing.due_15_days') }}</option>
                                    <option value="30" selected>{{ __('billing.due_30_days') }}</option>
                                    <option value="45">{{ __('billing.due_45_days') }}</option>
                                    <option value="60">{{ __('billing.due_60_days') }}</option>
                                    <option value="custom">{{ __('billing.custom_due_date') }}</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6 d-none" id="customDueDateDiv">
                                <label for="custom_due_date" class="form-label">{{ __('billing.custom_due_date') }}</label>
                                <input type="date" class="form-control" id="custom_due_date" name="custom_due_date">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="invoice_prefix" class="form-label">{{ __('billing.invoice_prefix') }}</label>
                                <input type="text" class="form-control" id="invoice_prefix" name="invoice_prefix" 
                                       value="{{ default_prefix }}" placeholder="INV-">
                                <small class="text-muted">{{ __('billing.prefix_hint') }}</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="starting_number" class="form-label">{{ __('billing.starting_number') }}</label>
                                <input type="number" class="form-control" id="starting_number" name="starting_number" 
                                       value="{{ next_number }}" min="1">
                                <small class="text-muted">{{ __('billing.current_next') }}: {{ next_number }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Email Options -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="bi bi-envelope me-2"></i>{{ __('billing.email_options') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-12">
                                <div class="form-check form-switch mb-3">
                                    <input type="checkbox" class="form-check-input" id="send_emails" name="send_emails" 
                                           value="1" checked onchange="toggleEmailOptions()">
                                    <label class="form-check-label" for="send_emails">
                                        {{ __('billing.send_invoices_by_email') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div id="emailOptionsDiv">
                                <div class="col-12">
                                    <label for="email_template" class="form-label">{{ __('billing.email_template') }}</label>
                                    <select class="form-select" id="email_template" name="email_template">
                                        {% for template in email_templates %}
                                            <option value="{{ template.id }}" {{ template.is_default ? 'selected' : '' }}>
                                                {{ template.name }}
                                            </option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div class="col-12 mt-3">
                                    <label for="email_subject" class="form-label">{{ __('billing.email_subject') }}</label>
                                    <input type="text" class="form-control" id="email_subject" name="email_subject" 
                                           value="{{ default_subject }}">
                                    <small class="text-muted">{{ __('billing.subject_variables') }}: {invoice_number}, {patient_name}, {month}, {year}</small>
                                </div>
                                
                                <div class="col-12 mt-3">
                                    <label for="additional_message" class="form-label">{{ __('billing.additional_message') }}</label>
                                    <textarea class="form-control" id="additional_message" name="additional_message" rows="3"></textarea>
                                    <small class="text-muted">{{ __('billing.message_hint') }}</small>
                                </div>
                                
                                <div class="col-12 mt-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="attach_pdf" name="attach_pdf" 
                                               value="1" checked>
                                        <label class="form-check-label" for="attach_pdf">
                                            {{ __('billing.attach_pdf_invoice') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="col-12 mt-2">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="cc_admin" name="cc_admin" value="1">
                                        <label class="form-check-label" for="cc_admin">
                                            {{ __('billing.send_copy_to_admin') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Options -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-warning text-white">
                        <h6 class="mb-0"><i class="bi bi-gear me-2"></i>{{ __('billing.advanced_options') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="group_by" class="form-label">{{ __('billing.group_services_by') }}</label>
                                <select class="form-select" id="group_by" name="group_by">
                                    <option value="none">{{ __('billing.no_grouping') }}</option>
                                    <option value="date">{{ __('billing.group_by_date') }}</option>
                                    <option value="type">{{ __('billing.group_by_type') }}</option>
                                    <option value="provider">{{ __('billing.group_by_provider') }}</option>
                                </select>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="sort_by" class="form-label">{{ __('billing.sort_services_by') }}</label>
                                <select class="form-select" id="sort_by" name="sort_by">
                                    <option value="date_asc">{{ __('billing.date_ascending') }}</option>
                                    <option value="date_desc">{{ __('billing.date_descending') }}</option>
                                    <option value="service">{{ __('billing.service_name') }}</option>
                                    <option value="amount">{{ __('billing.amount') }}</option>
                                </select>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="include_details" name="include_details" 
                                           value="1" checked>
                                    <label class="form-check-label" for="include_details">
                                        {{ __('billing.include_service_details') }}
                                    </label>
                                </div>
                                
                                <div class="form-check mb-2">
                                    <input type="checkbox" class="form-check-input" id="apply_discounts" name="apply_discounts" 
                                           value="1" checked>
                                    <label class="form-check-label" for="apply_discounts">
                                        {{ __('billing.apply_patient_discounts') }}
                                    </label>
                                </div>
                                
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="generate_summary" name="generate_summary" 
                                           value="1">
                                    <label class="form-check-label" for="generate_summary">
                                        {{ __('billing.generate_summary_report') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Session Summary -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-list-check me-2"></i>{{ __('billing.session_summary') }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-7">{{ __('billing.invoice_type') }}:</dt>
                            <dd class="col-5 text-end">
                                {% if session.type == 'recurring' %}
                                    <span class="badge bg-primary">{{ __('billing.recurring') }}</span>
                                {% elseif session.type == 'retrocession' %}
                                    <span class="badge bg-success">{{ __('billing.retrocession') }}</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ __('billing.pending') }}</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-7">{{ __('billing.selected_invoices') }}:</dt>
                            <dd class="col-5 text-end">{{ session.invoice_count }}</dd>
                            
                            <dt class="col-7">{{ __('billing.total_amount') }}:</dt>
                            <dd class="col-5 text-end">{{ currency }}{{ session.total_amount|number_format(2, ',', ' ') }}</dd>
                            
                            <dt class="col-7">{{ __('billing.billing_period') }}:</dt>
                            <dd class="col-5 text-end">
                                {% if session.period_month %}
                                    {{ session.period_month }}/{{ session.period_year }}
                                {% else %}
                                    -
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow-sm sticky-top" style="top: 20px;">
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                {{ __('billing.continue_to_review') }} <i class="bi bi-arrow-right ms-2"></i>
                            </button>
                            <a href="{{ base_url }}/billing-wizard/step-1?session={{ session_id }}" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i> {{ __('common.back') }}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<style>
.wizard-progress {
    display: flex;
    align-items: center;
    margin-bottom: 2rem;
}

.wizard-step {
    text-align: center;
    position: relative;
}

.wizard-step .step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 0.5rem;
}

.wizard-step.active .step-number {
    background-color: #0d6efd;
    color: white;
}

.wizard-step.completed .step-number {
    background-color: #198754;
    color: white;
}

.wizard-step .step-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.wizard-step.active .step-label,
.wizard-step.completed .step-label {
    color: #0d6efd;
    font-weight: 500;
}

.wizard-line {
    flex: 1;
    height: 2px;
    background-color: #e9ecef;
    margin: 0 1rem;
    margin-bottom: 2rem;
}

.wizard-line.completed {
    background-color: #198754;
}
</style>

<script>
// Toggle custom due date field
document.getElementById('due_days').addEventListener('change', function() {
    const customDiv = document.getElementById('customDueDateDiv');
    if (this.value === 'custom') {
        customDiv.classList.remove('d-none');
    } else {
        customDiv.classList.add('d-none');
    }
});

// Toggle email options
function toggleEmailOptions() {
    const sendEmails = document.getElementById('send_emails').checked;
    const emailDiv = document.getElementById('emailOptionsDiv');
    
    if (sendEmails) {
        emailDiv.style.display = 'block';
    } else {
        emailDiv.style.display = 'none';
    }
}

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}