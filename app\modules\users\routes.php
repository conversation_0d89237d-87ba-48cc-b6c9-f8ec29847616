<?php

use App\Controllers\UserController;
use App\Controllers\UserGroupController;
use App\Core\Request;
use App\Core\Response;

// User Groups Management
Flight::route('GET /users/groups', function() {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->index($request, $response);
});

Flight::route('GET /users/groups/create', function() {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->create($request, $response);
});

Flight::route('POST /users/groups', function() {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->store($request, $response);
});

Flight::route('GET /users/groups/@id', function($id) {
    // Redirect to group members page as the main view
    Flight::redirect(Flight::get('flight.base_url') . '/users/groups/' . $id . '/members');
});

Flight::route('GET /users/groups/@id/edit', function($id) {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->edit($request, $response, $id);
});

Flight::route('POST /users/groups/@id', function($id) {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->update($request, $response, $id);
});

Flight::route('DELETE /users/groups/@id', function($id) {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->delete($request, $response, $id);
});

// Group Members Management
Flight::route('GET /users/groups/@id/members', function($id) {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->members($request, $response, $id);
});

Flight::route('POST /users/groups/@id/members', function($id) {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->addMember($request, $response, $id);
});

Flight::route('DELETE /users/groups/@groupId/members/@userId', function($groupId, $userId) {
    $controller = new UserGroupController();
    $request = new Request();
    $response = new Response();
    $controller->removeMember($request, $response, $groupId, $userId);
});

// User Management Routes
Flight::route('GET /users', function() {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->index($request, $response);
});

Flight::route('GET /users/create', function() {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->create($request, $response);
});

Flight::route('POST /users', function() {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->store($request, $response);
});

Flight::route('GET /users/@id', function($id) {
    // Redirect to profile page
    Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/profile');
});

Flight::route('GET /users/@id/edit', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->edit($request, $response, $id);
});

Flight::route('POST /users/@id/toggle-active', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->toggleActive($request, $response, $id);
});

Flight::route('POST /users/@id', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->update($request, $response, $id);
});

Flight::route('DELETE /users/@id', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->delete($request, $response, $id);
});

Flight::route('GET /users/@id/profile', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->profile($request, $response, $id);
});

Flight::route('GET /profile', function() {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->profile($request, $response);
});

// Update profile preferences
Flight::route('POST /profile/preferences', function() {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->updatePreferences($request, $response);
});

// Retrocession History
Flight::route('GET /users/@id/retrocession-history', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->retrocessionHistory($request, $response, $id);
});

// User Courses (AJAX endpoint)
Flight::route('GET /users/@id/courses', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->getCourses($request, $response, $id);
});

// Financial Obligations
Flight::route('GET /users/@id/financial-obligations', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->financialObligations($request, $response, $id);
});

Flight::route('POST /users/@id/financial-obligations', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->updateFinancialObligations($request, $response, $id);
});

// Password Reset (for admins)
Flight::route('POST /users/@id/reset-password', function($id) {
    $controller = new UserController();
    $request = new Request();
    $response = new Response();
    $controller->resetPassword($request, $response, $id);
});