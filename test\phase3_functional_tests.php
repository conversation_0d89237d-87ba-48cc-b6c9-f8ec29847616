<?php
/**
 * Phase 3 Functional Testing Script
 * Tests all invoice payment features through the application
 */

require_once __DIR__ . '/../vendor/autoload.php';

class Phase3FunctionalTests
{
    private $baseUrl;
    private $db;
    private $testsPassed = 0;
    private $testsFailed = 0;
    private $currentTest = '';
    
    public function __construct($baseUrl = 'http://localhost/fit/public')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
        $this->connectDatabase();
    }
    
    private function connectDatabase()
    {
        try {
            $this->db = new PDO(
                'mysql:host=127.0.0.1;dbname=fit_appv3;charset=utf8mb4',
                'root',
                '',
                [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
            );
            $this->success("Database connection established");
        } catch (Exception $e) {
            $this->error("Database connection failed: " . $e->getMessage());
            exit(1);
        }
    }
    
    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "\n===== PHASE 3 FUNCTIONAL TESTS =====\n\n";
        
        // Test 1: Payment Methods
        $this->testPaymentMethods();
        
        // Test 2: Email Templates
        $this->testEmailTemplates();
        
        // Test 3: Field Manager
        $this->testFieldManager();
        
        // Test 4: Retrocession Management
        $this->testRetrocessionManagement();
        
        // Test 5: Invoice Creation with Payment Features
        $this->testInvoiceCreation();
        
        // Test 6: Payment Recording
        $this->testPaymentRecording();
        
        // Test 7: Document Types
        $this->testDocumentTypes();
        
        // Test 8: Credit Note Creation
        $this->testCreditNoteCreation();
        
        // Summary
        $this->printSummary();
    }
    
    /**
     * Test 1: Payment Methods Configuration
     */
    private function testPaymentMethods()
    {
        $this->startTest("Payment Methods Configuration");
        
        try {
            // Check if payment_methods table exists
            $stmt = $this->db->query("SHOW TABLES LIKE 'payment_methods'");
            if ($stmt->rowCount() == 0) {
                $this->warning("payment_methods table not found - creating...");
                $this->createPaymentMethodsTable();
            }
            
            // Check if payment methods are configured
            $stmt = $this->db->query("SELECT * FROM payment_methods WHERE is_active = 1");
            $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($methods) == 0) {
                $this->warning("No active payment methods found - inserting defaults...");
                $this->insertDefaultPaymentMethods();
                
                // Re-check
                $stmt = $this->db->query("SELECT * FROM payment_methods WHERE is_active = 1");
                $methods = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            $this->info("Found " . count($methods) . " active payment methods:");
            foreach ($methods as $method) {
                $this->info("  - " . $method['name'] . " (" . $method['code'] . ")");
            }
            
            // Test API endpoint
            $this->testEndpoint('/config/payment-methods', 'Payment Methods Page');
            
            $this->success("Payment methods configured successfully");
            
        } catch (Exception $e) {
            $this->error("Payment methods test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 2: Email Templates
     */
    private function testEmailTemplates()
    {
        $this->startTest("Email Templates Configuration");
        
        try {
            // Check if email_templates table exists
            $stmt = $this->db->query("SHOW TABLES LIKE 'email_templates'");
            if ($stmt->rowCount() == 0) {
                $this->warning("email_templates table not found");
                return;
            }
            
            // Check invoice-related templates
            $requiredTemplates = ['invoice_created', 'invoice_sent', 'payment_received'];
            $stmt = $this->db->prepare("SELECT code FROM email_templates WHERE code IN ('" . implode("','", $requiredTemplates) . "')");
            $stmt->execute();
            $existingTemplates = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $missingTemplates = array_diff($requiredTemplates, $existingTemplates);
            if (count($missingTemplates) > 0) {
                $this->warning("Missing email templates: " . implode(', ', $missingTemplates));
            } else {
                $this->success("All required email templates found");
            }
            
            // Test endpoint
            $this->testEndpoint('/config/email-templates', 'Email Templates Page');
            
        } catch (Exception $e) {
            $this->error("Email templates test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 3: Field Manager
     */
    private function testFieldManager()
    {
        $this->startTest("Field Manager Configuration");
        
        try {
            // Check if field tables exist
            $tables = ['field_visibility', 'custom_fields'];
            foreach ($tables as $table) {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $this->info("Table $table exists");
                } else {
                    $this->warning("Table $table not found");
                }
            }
            
            // Test endpoint
            $this->testEndpoint('/config/fields', 'Field Manager Page');
            
        } catch (Exception $e) {
            $this->error("Field manager test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 4: Retrocession Management
     */
    private function testRetrocessionManagement()
    {
        $this->startTest("Retrocession Management");
        
        try {
            // Check required tables
            $tables = ['rate_profiles', 'client_rate_assignments', 'retrocession_entries'];
            $allExist = true;
            
            foreach ($tables as $table) {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() == 0) {
                    $this->warning("Table $table not found");
                    $allExist = false;
                }
            }
            
            if ($allExist) {
                // Check rate profiles
                $stmt = $this->db->query("SELECT COUNT(*) as count FROM rate_profiles WHERE is_active = 1");
                $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
                $this->info("Found $count active rate profiles");
                
                // Test endpoints
                $this->testEndpoint('/retrocession', 'Retrocession Index');
                $this->testEndpoint('/retrocession/rate-profiles', 'Rate Profiles Page');
            }
            
        } catch (Exception $e) {
            $this->error("Retrocession test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 5: Invoice Creation
     */
    private function testInvoiceCreation()
    {
        $this->startTest("Invoice Creation with Payment Features");
        
        try {
            // Check invoices table structure
            $stmt = $this->db->query("SHOW COLUMNS FROM invoices");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $requiredColumns = ['id', 'invoice_number', 'status', 'total', 'payment_method_id'];
            $missingColumns = array_diff($requiredColumns, $columns);
            
            if (count($missingColumns) > 0) {
                $this->warning("Missing columns in invoices table: " . implode(', ', $missingColumns));
            }
            
            // Test endpoints
            $this->testEndpoint('/invoices', 'Invoices Index');
            $this->testEndpoint('/invoices/create', 'Invoice Creation Form');
            
            $this->success("Invoice structure verified");
            
        } catch (Exception $e) {
            $this->error("Invoice creation test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 6: Payment Recording
     */
    private function testPaymentRecording()
    {
        $this->startTest("Payment Recording");
        
        try {
            // Check payments table
            $paymentTables = ['invoice_payments', 'payments'];
            $foundTable = null;
            
            foreach ($paymentTables as $table) {
                $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
                if ($stmt->rowCount() > 0) {
                    $foundTable = $table;
                    break;
                }
            }
            
            if ($foundTable) {
                $this->success("Found payments table: $foundTable");
                
                // Check structure
                $stmt = $this->db->query("SHOW COLUMNS FROM $foundTable");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $this->info("Payment table columns: " . implode(', ', array_slice($columns, 0, 5)) . "...");
            } else {
                $this->warning("No payments table found");
            }
            
        } catch (Exception $e) {
            $this->error("Payment recording test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 7: Document Types
     */
    private function testDocumentTypes()
    {
        $this->startTest("Document Types System");
        
        try {
            // Check if document_types table exists
            $stmt = $this->db->query("SHOW TABLES LIKE 'document_types'");
            if ($stmt->rowCount() == 0) {
                $this->info("Document types not yet implemented (new feature)");
                return;
            }
            
            // Check document types
            $stmt = $this->db->query("SELECT code, prefix FROM document_types WHERE is_active = 1");
            $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->info("Found " . count($types) . " document types:");
            foreach ($types as $type) {
                $this->info("  - " . $type['code'] . " (prefix: " . $type['prefix'] . ")");
            }
            
            // Test endpoint
            $this->testEndpoint('/config/document-types', 'Document Types Page');
            
        } catch (Exception $e) {
            $this->error("Document types test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Test 8: Credit Note Creation
     */
    private function testCreditNoteCreation()
    {
        $this->startTest("Credit Note Creation");
        
        try {
            // Find a paid invoice for testing
            $stmt = $this->db->query("SELECT id, invoice_number FROM invoices WHERE status = 'paid' LIMIT 1");
            $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($invoice) {
                $this->info("Found paid invoice: " . $invoice['invoice_number']);
                $this->testEndpoint('/invoices/' . $invoice['id'] . '/credit-note', 'Credit Note Form');
            } else {
                $this->info("No paid invoices found for credit note testing");
            }
            
        } catch (Exception $e) {
            $this->error("Credit note test failed: " . $e->getMessage());
        }
    }
    
    /**
     * Helper Methods
     */
    private function startTest($testName)
    {
        $this->currentTest = $testName;
        echo "\n--- Testing: $testName ---\n";
    }
    
    private function testEndpoint($path, $description)
    {
        $url = $this->baseUrl . $path;
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode == 200) {
            $this->info("✓ $description accessible at $path");
        } else {
            $this->warning("✗ $description returned HTTP $httpCode at $path");
        }
    }
    
    private function success($message)
    {
        echo "[SUCCESS] $message\n";
        $this->testsPassed++;
    }
    
    private function info($message)
    {
        echo "[INFO] $message\n";
    }
    
    private function warning($message)
    {
        echo "[WARNING] $message\n";
    }
    
    private function error($message)
    {
        echo "[ERROR] $message\n";
        $this->testsFailed++;
    }
    
    private function printSummary()
    {
        echo "\n===== TEST SUMMARY =====\n";
        echo "Tests Passed: " . $this->testsPassed . "\n";
        echo "Tests Failed: " . $this->testsFailed . "\n";
        
        if ($this->testsFailed == 0) {
            echo "\nAll tests passed! Phase 3 features are working correctly.\n";
        } else {
            echo "\nSome tests failed. Please check the errors above.\n";
        }
        
        echo "\n===== NEXT STEPS =====\n";
        echo "1. Access the application at: " . $this->baseUrl . "\n";
        echo "2. Configure payment methods at: " . $this->baseUrl . "/config/payment-methods\n";
        echo "3. Create a test invoice at: " . $this->baseUrl . "/invoices/create\n";
        echo "4. Test payment recording on the invoice\n";
        echo "5. Test email sending functionality\n";
        echo "6. Create a credit note from a paid invoice\n";
    }
    
    /**
     * Create payment methods table if missing
     */
    private function createPaymentMethodsTable()
    {
        $sql = "CREATE TABLE IF NOT EXISTS `payment_methods` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `code` varchar(50) NOT NULL UNIQUE,
            `name` varchar(100) NOT NULL,
            `description` text,
            `is_active` tinyint(1) DEFAULT 1,
            `is_default` tinyint(1) DEFAULT 0,
            `requires_reference` tinyint(1) DEFAULT 0,
            `icon` varchar(50) DEFAULT 'bi bi-credit-card',
            `color` varchar(7) DEFAULT '#6c757d',
            `sort_order` int(11) DEFAULT 0,
            `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";
        
        $this->db->exec($sql);
    }
    
    /**
     * Insert default payment methods
     */
    private function insertDefaultPaymentMethods()
    {
        $methods = [
            ['code' => 'cash', 'name' => 'Espèces', 'icon' => 'bi bi-cash', 'color' => '#28a745'],
            ['code' => 'bank_transfer', 'name' => 'Virement bancaire', 'icon' => 'bi bi-bank', 'color' => '#17a2b8', 'is_default' => 1],
            ['code' => 'credit_card', 'name' => 'Carte de crédit', 'icon' => 'bi bi-credit-card', 'color' => '#007bff'],
            ['code' => 'check', 'name' => 'Chèque', 'icon' => 'bi bi-envelope', 'color' => '#6c757d']
        ];
        
        foreach ($methods as $index => $method) {
            $stmt = $this->db->prepare("INSERT INTO payment_methods (code, name, icon, color, is_default, sort_order) VALUES (:code, :name, :icon, :color, :is_default, :sort_order)");
            $stmt->execute([
                ':code' => $method['code'],
                ':name' => $method['name'],
                ':icon' => $method['icon'],
                ':color' => $method['color'],
                ':is_default' => $method['is_default'] ?? 0,
                ':sort_order' => $index
            ]);
        }
    }
}

// Run the tests
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost/fit/public';
    $tester = new Phase3FunctionalTests($baseUrl);
    $tester->runAllTests();
} else {
    echo "<pre>";
    $tester = new Phase3FunctionalTests();
    $tester->runAllTests();
    echo "</pre>";
}