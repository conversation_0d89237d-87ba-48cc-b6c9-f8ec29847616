<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== FIXING DRAFT INVOICE VISIBILITY ===\n\n";

try {
    $db = Flight::db();
    
    // Check the most recent invoices
    echo "1. Recent invoices (last 5):\n";
    $stmt = $db->query("
        SELECT id, invoice_number, status, is_archived, created_at,
               CASE 
                   WHEN client_id IS NOT NULL THEN 'Client'
                   WHEN user_id IS NOT NULL THEN 'User'
                   ELSE 'Unknown'
               END as billable_type
        FROM invoices 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $invoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($invoices as $inv) {
        echo sprintf("ID: %d | Number: %s | Status: '%s' | Archived: %s | Type: %s | Created: %s\n",
            $inv['id'],
            $inv['invoice_number'],
            $inv['status'],
            $inv['is_archived'] ? 'YES' : 'NO',
            $inv['billable_type'],
            $inv['created_at']
        );
    }
    
    // Check for draft invoices that might be hidden
    echo "\n2. All draft invoices:\n";
    $stmt = $db->query("
        SELECT id, invoice_number, status, is_archived 
        FROM invoices 
        WHERE LOWER(status) = 'draft' OR status IS NULL
    ");
    $drafts = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($drafts) > 0) {
        foreach ($drafts as $draft) {
            echo "- Invoice #{$draft['id']} ({$draft['invoice_number']}): Status='{$draft['status']}', Archived=" . ($draft['is_archived'] ? 'YES' : 'NO') . "\n";
            
            // Fix if archived
            if ($draft['is_archived']) {
                $updateStmt = $db->prepare("UPDATE invoices SET is_archived = 0 WHERE id = ?");
                $updateStmt->execute([$draft['id']]);
                echo "  ✓ Fixed: Set is_archived to FALSE\n";
            }
            
            // Fix if status is NULL
            if ($draft['status'] === null) {
                $updateStmt = $db->prepare("UPDATE invoices SET status = 'draft' WHERE id = ?");
                $updateStmt->execute([$draft['id']]);
                echo "  ✓ Fixed: Set status to 'draft'\n";
            }
        }
    } else {
        echo "No draft invoices found.\n";
    }
    
    // Check what the actual filter query would return
    echo "\n3. Testing the filter query (status='draft'):\n";
    $where = ['1=1'];
    $params = [];
    
    // Same conditions as in getInvoices
    $where[] = 'COALESCE(i.is_archived, FALSE) = FALSE';
    $where[] = 'i.status = :status';
    $params[':status'] = 'draft';
    
    $whereClause = implode(' AND ', $where);
    
    $stmt = $db->prepare("
        SELECT COUNT(*) as total
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN users u ON i.user_id = u.id
        WHERE $whereClause
    ");
    $stmt->execute($params);
    $result = $stmt->fetch();
    echo "Query would return: " . $result['total'] . " draft invoices\n";
    
    // Show the exact SQL being used
    echo "\n4. SQL query being used:\n";
    echo "WHERE " . str_replace(':status', "'draft'", $whereClause) . "\n";
    
    // Check for case sensitivity issues
    echo "\n5. Checking for case sensitivity issues:\n";
    $stmt = $db->query("
        SELECT DISTINCT status, COUNT(*) as count 
        FROM invoices 
        GROUP BY status 
        ORDER BY count DESC
    ");
    $statuses = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($statuses as $status) {
        echo "- Status: '{$status['status']}' (count: {$status['count']})\n";
        if (strtolower($status['status']) === 'draft' && $status['status'] !== 'draft') {
            echo "  ⚠️  Found case mismatch! Fixing...\n";
            $updateStmt = $db->prepare("UPDATE invoices SET status = 'draft' WHERE status = ?");
            $updateStmt->execute([$status['status']]);
            echo "  ✓ Fixed " . $updateStmt->rowCount() . " invoices\n";
        }
    }
    
    echo "\nDone! Please refresh the invoices page with draft filter.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}