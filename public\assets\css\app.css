/* Health Center Billing System - Custom Styles */

/* Brand colors */
:root {
    --primary-color: #007bff;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Invoice status badges */
.badge-draft { background-color: #6c757d; }
.badge-sent { background-color: #17a2b8; }
.badge-paid { background-color: #28a745; }
.badge-partial { background-color: #ffc107; }
.badge-overdue { background-color: #dc3545; }
.badge-cancelled { background-color: #343a40; }

/* Table improvements */
.table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

/* Card loading state */
.card-loading {
    position: relative;
    min-height: 200px;
}

.card-loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.8);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .invoice-print {
        page-break-inside: avoid;
    }
}

/* AdminLTE 4 Compatibility */
.app-sidebar {
    overflow-y: auto;
}

.btn-block {
    display: block;
    width: 100%;
}

/* Fix for DataTables with Bootstrap 5 */
.dataTables_wrapper .dataTables_paginate .paginate_button {
    padding: 0.25rem 0.5rem;
    margin: 0 0.1rem;
}

/* Small box adjustments for AdminLTE 4 */
.small-box .small-box-icon {
    font-size: 70px;
    position: absolute;
    top: 15px;
    right: 15px;
    opacity: 0.3;
}

.small-box .inner {
    position: relative;
    z-index: 1;
}

/* Card tools alignment fix */
.card-tools {
    float: right;
    margin-right: -0.625rem;
}

/* Compatibility for both mr-2 (Bootstrap 4) and me-2 (Bootstrap 5) */
.mr-2, .me-2 {
    margin-right: 0.5rem !important;
}

/* User menu adjustments */
.user-menu .user-image {
    width: 30px;
    height: 30px;
    margin-right: 0.5rem;
}