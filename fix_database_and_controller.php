<?php
/**
 * Fix database and controller for exclude patient line feature
 */

// Load .env file
$envFile = __DIR__ . '/.env';
if (!file_exists($envFile)) {
    die("Error: .env file not found!");
}

$envContent = file_get_contents($envFile);
$lines = explode("\n", $envContent);
$env = [];

foreach ($lines as $line) {
    $line = trim($line);
    if (empty($line) || strpos($line, '#') === 0) continue;
    
    if (strpos($line, '=') !== false) {
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value, '"\'');
        $env[$key] = $value;
    }
}

// Database configuration from .env
$dbHost = $env['DB_HOST'] ?? '127.0.0.1';
$dbPort = $env['DB_PORT'] ?? '3306';
$dbName = $env['DB_DATABASE'] ?? 'fitapp';
$dbUser = $env['DB_USERNAME'] ?? 'root';
$dbPass = $env['DB_PASSWORD'] ?? '';

try {
    // Connect to database using .env credentials
    $dsn = "mysql:host=$dbHost;port=$dbPort;dbname=$dbName;charset=utf8mb4";
    $db = new PDO($dsn, $dbUser, $dbPass);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<pre>";
    echo "✓ Connected to database '$dbName' successfully using .env configuration.\n\n";
    
    // Step 1: Add the column if it doesn't exist
    echo "=== Step 1: Database Column ===\n";
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('exclude_patient_line', $columns)) {
        echo "Adding 'exclude_patient_line' column...\n";
        
        $db->exec("ALTER TABLE retrocession_data_entry 
                   ADD COLUMN exclude_patient_line TINYINT(1) DEFAULT 0 AFTER status");
        
        echo "✓ Column added successfully!\n";
        
        // Update existing entries
        $db->exec("UPDATE retrocession_data_entry SET exclude_patient_line = 0 WHERE exclude_patient_line IS NULL");
        echo "✓ Existing entries updated.\n";
    } else {
        echo "✓ Column already exists.\n";
    }
    
    // Step 2: Fix the RetrocessionController
    echo "\n=== Step 2: Controller Fix ===\n";
    $controllerFile = __DIR__ . '/app/controllers/RetrocessionController.php';
    $content = file_get_contents($controllerFile);
    
    // Check if the column is commented out
    if (strpos($content, '-- rde.exclude_patient_line') !== false) {
        // Uncomment it
        $content = str_replace(
            '-- rde.exclude_patient_line, -- Column will be added after migration',
            'rde.exclude_patient_line,',
            $content
        );
        file_put_contents($controllerFile, $content);
        echo "✓ Uncommented exclude_patient_line in controller.\n";
    } else {
        echo "✓ Controller already includes exclude_patient_line.\n";
    }
    
    // Step 3: Verify everything works
    echo "\n=== Step 3: Verification ===\n";
    
    // Test the query that was failing
    $testQuery = "
        SELECT 
            c.id, 
            COALESCE(c.company_name, CONCAT(c.first_name, ' ', c.last_name)) as name,
            rde.exclude_patient_line
        FROM clients c
        LEFT JOIN retrocession_data_entry rde ON c.id = rde.practitioner_id 
        WHERE c.is_practitioner = TRUE 
        LIMIT 1
    ";
    
    $stmt = $db->query($testQuery);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result !== false) {
        echo "✓ Query test successful!\n";
    }
    
    // Show database connection info
    echo "\n=== Database Connection Info ===\n";
    echo "Host: $dbHost\n";
    echo "Port: $dbPort\n";
    echo "Database: $dbName\n";
    echo "User: $dbUser\n";
    
    echo "\n✅ All fixes applied successfully!\n";
    echo "\nThe retrocession page should now load without errors.\n";
    echo "You can access it at: <a href='/fit/public/retrocession/'>http://localhost/fit/public/retrocession/</a>\n";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<pre>";
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "\nDatabase connection details from .env:\n";
    echo "Host: $dbHost\n";
    echo "Port: $dbPort\n";
    echo "Database: $dbName\n";
    echo "User: $dbUser\n";
    echo "\nPlease ensure:\n";
    echo "1. MySQL is running\n";
    echo "2. Database '$dbName' exists\n";
    echo "3. User '$dbUser' has proper permissions\n";
    echo "</pre>";
}