<?php

namespace App\Services;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Voucher;
use PDO;
use Exception;

class EmailService
{
    private $db;
    private $mailer;
    
    public function __construct()
    {
        $this->db = Flight::db();
        // Initialize mailer (you would configure your actual mail service here)
        // For now, we'll use a simple implementation
    }
    
    /**
     * Send invoice email
     */
    public function sendInvoiceEmail($invoiceId, $recipientEmail = null)
    {
        try {
            // Get invoice details
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Determine recipient
            if (!$recipientEmail) {
                $recipientEmail = $invoiceData['client']['email'];
            }
            
            if (!$recipientEmail) {
                throw new Exception('No recipient email address');
            }
            
            // Select appropriate email template
            $template = $this->selectInvoiceTemplate($invoiceData);
            
            // Render email content
            $emailContent = $this->renderTemplate($template, $invoiceData);
            
            // Generate PDF
            $pdfService = new PdfService();
            $pdf = $pdfService->generateInvoicePdf($invoiceData);
            
            // Send email
            $result = $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [
                    [
                        'name' => $invoiceData['invoice_number'] . '.pdf',
                        'content' => $pdf,
                        'type' => 'application/pdf'
                    ]
                ]
            ]);
            
            // Log email
            $this->logEmail($invoiceId, $template['id'], $recipientEmail, $result);
            
            return $result;
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Get invoice email preview
     */
    public function getInvoiceEmailPreview($invoiceId)
    {
        $invoice = new Invoice();
        $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
        
        if (!$invoiceData) {
            return null;
        }
        
        $template = $this->selectInvoiceTemplate($invoiceData);
        return $this->renderTemplate($template, $invoiceData);
    }
    
    /**
     * Send voucher email
     */
    public function sendVoucherEmail($voucherId, $recipientEmail)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Get client details
            $client = new Client();
            $clientData = $client->getById($voucherData['client_id']);
            
            // Select template
            $template = $this->getEmailTemplate('voucher_created');
            
            // Prepare data
            $data = array_merge($voucherData, [
                'client' => $clientData,
                'company_name' => $this->getConfig('company_name'),
                'company_email' => $this->getConfig('company_email')
            ]);
            
            // Render email
            $emailContent = $this->renderTemplate($template, $data);
            
            // Send email
            $result = $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text']
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Send voucher expiry reminder
     */
    public function sendVoucherExpiryReminder($voucherId, $recipientEmail)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Calculate days until expiry
            $daysUntilExpiry = floor((strtotime($voucherData['expiry_date']) - time()) / 86400);
            
            // Get template
            $template = $this->getEmailTemplate('voucher_expiry_reminder');
            
            // Prepare data
            $data = array_merge($voucherData, [
                'days_until_expiry' => $daysUntilExpiry,
                'company_name' => $this->getConfig('company_name')
            ]);
            
            // Render and send
            $emailContent = $this->renderTemplate($template, $data);
            
            return $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text']
            ]);
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Send payment reminder
     */
    public function sendPaymentReminder($invoiceId, $reminderLevel = 1)
    {
        try {
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Calculate overdue days
            $daysOverdue = floor((time() - strtotime($invoiceData['due_date'])) / 86400);
            
            // Get appropriate reminder template
            $template = $this->getEmailTemplate('reminder_' . $reminderLevel, $invoiceData['invoice_type']);
            
            // Prepare data
            $data = array_merge($invoiceData, [
                'days_overdue' => $daysOverdue,
                'reminder_level' => $reminderLevel,
                'company_name' => $this->getConfig('company_name')
            ]);
            
            // Render email
            $emailContent = $this->renderTemplate($template, $data);
            
            // Generate PDF
            $pdfService = new PdfService();
            $pdf = $pdfService->generateInvoicePdf($invoiceData);
            
            // Send email
            $result = $this->send([
                'to' => $invoiceData['client']['email'],
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [
                    [
                        'name' => $invoiceData['invoice_number'] . '.pdf',
                        'content' => $pdf,
                        'type' => 'application/pdf'
                    ]
                ]
            ]);
            
            // Log reminder
            $this->logPaymentReminder($invoiceId, $reminderLevel, $result);
            
            return $result;
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Select appropriate invoice template
     */
    private function selectInvoiceTemplate($invoice)
    {
        $conditions = [
            'invoice_type' => $invoice['invoice_type'],
            'is_first_invoice' => $this->isFirstInvoice($invoice['client_id']),
            'is_retrocession' => in_array($invoice['invoice_type'], ['retrocession_30', 'retrocession_25']),
            'is_credit_note' => !empty($invoice['reference_invoice_id'])
        ];
        
        // Get templates matching conditions
        $stmt = $this->db->prepare("
            SELECT * FROM email_templates
            WHERE email_type = 'new_invoice'
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY priority DESC
            LIMIT 1
        ");
        
        $stmt->execute([':invoice_type' => $invoice['invoice_type']]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            // Get default template
            $stmt = $this->db->prepare("
                SELECT * FROM email_templates
                WHERE email_type = 'new_invoice'
                AND is_active = TRUE
                AND invoice_type IS NULL
                ORDER BY priority DESC
                LIMIT 1
            ");
            $stmt->execute();
            $template = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return $template;
    }
    
    /**
     * Render email template
     */
    private function renderTemplate($template, $data)
    {
        if (!$template) {
            throw new Exception('No email template found');
        }
        
        // Prepare variables
        $variables = $this->prepareTemplateVariables($data);
        
        // Replace variables in subject and body
        $subject = $this->replaceVariables($template['subject'], $variables);
        $bodyHtml = $this->replaceVariables($template['body_html'], $variables);
        $bodyText = $this->replaceVariables($template['body_text'], $variables);
        
        return [
            'subject' => $subject,
            'body_html' => $bodyHtml,
            'body_text' => $bodyText
        ];
    }
    
    /**
     * Prepare template variables
     */
    private function prepareTemplateVariables($data)
    {
        $variables = [];
        
        // Invoice variables
        if (isset($data['invoice_number'])) {
            $variables['INVOICE_NUMBER'] = $data['invoice_number'];
            $variables['ISSUE_DATE'] = date('d/m/Y', strtotime($data['issue_date']));
            $variables['DUE_DATE'] = date('d/m/Y', strtotime($data['due_date']));
            $variables['TOTAL_AMOUNT'] = number_format($data['total'], 2, ',', ' ');
            $variables['PAYMENT_TERMS'] = $data['payment_terms'];
        }
        
        // Client variables
        if (isset($data['client'])) {
            $variables['CLIENT_NAME'] = $data['client']['name'];
            $variables['PRACTITIONER_NAME'] = $data['client']['name']; // Alias for retrocession
        }
        
        // Retrocession variables
        if (isset($data['retrocession'])) {
            $variables['CNS_AMOUNT'] = number_format($data['retrocession']['cns_amount'], 2, ',', ' ');
            $variables['PATIENT_AMOUNT'] = number_format($data['retrocession']['patient_amount'], 2, ',', ' ');
            $variables['SECRETARIAT_AMOUNT'] = number_format($data['retrocession']['secretariat_tvac'], 2, ',', ' ');
        }
        
        // Period variables
        if (isset($data['period_month']) && isset($data['period_year'])) {
            $variables['MONTH_NAME'] = $this->getMonthName($data['period_month']);
            $variables['YEAR'] = $data['period_year'];
        }
        
        // Voucher variables
        if (isset($data['voucher_number'])) {
            $variables['VOUCHER_NUMBER'] = $data['voucher_number'];
            $variables['VOUCHER_AMOUNT'] = number_format($data['amount'], 2, ',', ' ');
            $variables['EXPIRY_DATE'] = date('d/m/Y', strtotime($data['expiry_date']));
            $variables['BENEFICIARY_NAME'] = $data['beneficiary_name'] ?? '';
        }
        
        // Company variables
        $variables['COMPANY_NAME'] = $this->getConfig('company_name');
        $variables['COMPANY_EMAIL'] = $this->getConfig('company_email');
        $variables['COMPANY_PHONE'] = $this->getConfig('company_phone');
        
        return $variables;
    }
    
    /**
     * Replace variables in text
     */
    private function replaceVariables($text, $variables)
    {
        foreach ($variables as $key => $value) {
            $text = str_replace('{' . $key . '}', $value, $text);
        }
        return $text;
    }
    
    /**
     * Send email
     */
    private function send($params)
    {
        // In production, you would use a real mail service (PHPMailer, SwiftMailer, etc.)
        // For now, we'll simulate sending
        
        try {
            // Validate parameters
            if (empty($params['to']) || empty($params['subject'])) {
                throw new Exception('Missing required email parameters');
            }
            
            // Here you would actually send the email
            // mail($params['to'], $params['subject'], $params['body_text'], $headers);
            
            // For development, log to file
            $logEntry = sprintf(
                "[%s] Email to: %s, Subject: %s\n",
                date('Y-m-d H:i:s'),
                $params['to'],
                $params['subject']
            );
            
            // In production, this would be actual email sending
            // For now, we'll return success
            return ['success' => true, 'message' => 'Email sent successfully'];
            
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * Log email
     */
    private function logEmail($invoiceId, $templateId, $recipient, $result)
    {
        $stmt = $this->db->prepare("
            INSERT INTO email_logs (
                invoice_id, template_id, recipient_email,
                subject, status, sent_at, error_message
            ) VALUES (
                :invoice_id, :template_id, :recipient_email,
                :subject, :status, :sent_at, :error_message
            )
        ");
        
        $stmt->execute([
            ':invoice_id' => $invoiceId,
            ':template_id' => $templateId,
            ':recipient_email' => $recipient,
            ':subject' => $result['subject'] ?? '',
            ':status' => $result['success'] ? 'sent' : 'failed',
            ':sent_at' => $result['success'] ? date('Y-m-d H:i:s') : null,
            ':error_message' => $result['success'] ? null : $result['message']
        ]);
    }
    
    /**
     * Log payment reminder
     */
    private function logPaymentReminder($invoiceId, $reminderLevel, $result)
    {
        $stmt = $this->db->prepare("
            UPDATE payment_reminders
            SET status = :status,
                sent_date = :sent_date
            WHERE invoice_id = :invoice_id
            AND reminder_level = :reminder_level
            AND status = 'scheduled'
        ");
        
        $stmt->execute([
            ':status' => $result['success'] ? 'sent' : 'failed',
            ':sent_date' => $result['success'] ? date('Y-m-d H:i:s') : null,
            ':invoice_id' => $invoiceId,
            ':reminder_level' => $reminderLevel
        ]);
    }
    
    /**
     * Check if first invoice for client
     */
    private function isFirstInvoice($clientId)
    {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count
            FROM invoices
            WHERE client_id = :client_id
            AND status != 'draft'
        ");
        
        $stmt->execute([':client_id' => $clientId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['count'] == 0;
    }
    
    /**
     * Get email template
     */
    private function getEmailTemplate($code, $invoiceType = null)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM email_templates
            WHERE code = :code
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY invoice_type DESC
            LIMIT 1
        ");
        
        $stmt->execute([
            ':code' => $code,
            ':invoice_type' => $invoiceType
        ]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get config value
     */
    private function getConfig($key)
    {
        $stmt = $this->db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        return $stmt->fetch(PDO::FETCH_COLUMN) ?: '';
    }
    
    /**
     * Get month name in French
     */
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
}