<?php

namespace App\Services;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Voucher;
use PDO;
use Exception;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;

class EmailService
{
    private $db;
    private $mailer;
    
    public function __construct()
    {
        $this->db = Flight::db();
        // Initialize mailer (you would configure your actual mail service here)
        // For now, we'll use a simple implementation
    }
    
    /**
     * Send invoice email
     */
    public function sendInvoiceEmail($invoiceId, $recipientEmail = null)
    {
        try {
            // Get invoice details
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Determine recipient
            if (!$recipientEmail) {
                // Check for client email first
                if (!empty($invoiceData['client']['email'])) {
                    $recipientEmail = $invoiceData['client']['email'];
                } 
                // Then check for user email (for user invoices)
                elseif (!empty($invoiceData['user']['invoice_email'])) {
                    $recipientEmail = $invoiceData['user']['invoice_email'];
                } 
                elseif (!empty($invoiceData['user']['email'])) {
                    $recipientEmail = $invoiceData['user']['email'];
                }
            }
            
            if (!$recipientEmail) {
                // Log the issue for debugging
                error_log('EmailService: No recipient email found for invoice ' . $invoiceId);
                error_log('Invoice data: ' . json_encode([
                    'has_client' => isset($invoiceData['client']),
                    'has_user' => isset($invoiceData['user']),
                    'client_email' => $invoiceData['client']['email'] ?? 'not set',
                    'user_email' => $invoiceData['user']['email'] ?? 'not set',
                    'user_invoice_email' => $invoiceData['user']['invoice_email'] ?? 'not set'
                ]));
                throw new Exception('No recipient email address found');
            }
            
            // Select appropriate email template
            $template = $this->selectInvoiceTemplate($invoiceData);
            
            // Render email content
            $emailContent = $this->renderTemplate($template, $invoiceData);
            
            // Generate PDF using the same method as invoice download
            // This ensures consistency between downloaded and emailed PDFs
            $pdf = $this->generateInvoicePdfFromDownloadScript($invoiceId);
            
            // Send email
            $result = $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [
                    [
                        'name' => $invoiceData['invoice_number'] . '.pdf',
                        'content' => $pdf,
                        'type' => 'application/pdf'
                    ]
                ]
            ]);
            
            // Log email
            $this->logEmail($invoiceId, $template['id'] ?? null, $recipientEmail, $result);
            
            // Add debug logging
            error_log('EmailService: Invoice email result for invoice ' . $invoiceId . ': ' . json_encode($result));
            
            return $result;
            
        } catch (Exception $e) {
            // Log the error
            error_log('EmailService: sendInvoiceEmail error - ' . $e->getMessage());
            
            // Still try to log the failed email attempt
            $errorResult = [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => 'Invoice Email',
                'body_text' => '',
                'body_html' => '',
                'attachments' => []
            ];
            
            // Try to log the failed attempt
            $this->logEmail($invoiceId, null, $recipientEmail ?? 'unknown', $errorResult);
            
            return $errorResult;
        }
    }
    
    /**
     * Get invoice email preview
     */
    public function getInvoiceEmailPreview($invoiceId)
    {
        $invoice = new Invoice();
        $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
        
        if (!$invoiceData) {
            return null;
        }
        
        $template = $this->selectInvoiceTemplate($invoiceData);
        return $this->renderTemplate($template, $invoiceData);
    }
    
    /**
     * Send voucher email
     */
    public function sendVoucherEmail($voucherId, $recipientEmail)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Get client details
            $client = new Client();
            $clientData = $client->getById($voucherData['client_id']);
            
            // Select template
            $template = $this->getEmailTemplate('voucher_created');
            
            // Prepare data
            $data = array_merge($voucherData, [
                'client' => $clientData,
                'company_name' => $this->getConfig('company_name'),
                'company_email' => $this->getConfig('company_email')
            ]);
            
            // Render email
            $emailContent = $this->renderTemplate($template, $data);
            
            // Send email
            $result = $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text']
            ]);
            
            return $result;
            
        } catch (Exception $e) {
            // Log the error
            error_log('EmailService: sendInvoiceEmail error - ' . $e->getMessage());
            
            // Still try to log the failed email attempt
            $errorResult = [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => 'Invoice Email',
                'body_text' => '',
                'body_html' => '',
                'attachments' => []
            ];
            
            // Try to log the failed attempt
            $this->logEmail($invoiceId, null, $recipientEmail ?? 'unknown', $errorResult);
            
            return $errorResult;
        }
    }
    
    /**
     * Send voucher expiry reminder
     */
    public function sendVoucherExpiryReminder($voucherId, $recipientEmail)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Calculate days until expiry
            $daysUntilExpiry = floor((strtotime($voucherData['expiry_date']) - time()) / 86400);
            
            // Get template
            $template = $this->getEmailTemplate('voucher_expiry_reminder');
            
            // Prepare data
            $data = array_merge($voucherData, [
                'days_until_expiry' => $daysUntilExpiry,
                'company_name' => $this->getConfig('company_name')
            ]);
            
            // Render and send
            $emailContent = $this->renderTemplate($template, $data);
            
            return $this->send([
                'to' => $recipientEmail,
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text']
            ]);
            
        } catch (Exception $e) {
            // Log the error
            error_log('EmailService: sendInvoiceEmail error - ' . $e->getMessage());
            
            // Still try to log the failed email attempt
            $errorResult = [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => 'Invoice Email',
                'body_text' => '',
                'body_html' => '',
                'attachments' => []
            ];
            
            // Try to log the failed attempt
            $this->logEmail($invoiceId, null, $recipientEmail ?? 'unknown', $errorResult);
            
            return $errorResult;
        }
    }
    
    /**
     * Send payment reminder
     */
    public function sendPaymentReminder($invoiceId, $reminderLevel = 1)
    {
        try {
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found');
            }
            
            // Calculate overdue days
            $daysOverdue = floor((time() - strtotime($invoiceData['due_date'])) / 86400);
            
            // Get appropriate reminder template
            $template = $this->getEmailTemplate('reminder_' . $reminderLevel, $invoiceData['invoice_type']);
            
            // Prepare data
            $data = array_merge($invoiceData, [
                'days_overdue' => $daysOverdue,
                'reminder_level' => $reminderLevel,
                'company_name' => $this->getConfig('company_name')
            ]);
            
            // Render email
            $emailContent = $this->renderTemplate($template, $data);
            
            // Generate PDF using the same method as invoice download
            // This ensures consistency between downloaded and emailed PDFs
            $pdf = $this->generateInvoicePdfFromDownloadScript($invoiceId);
            
            // Send email
            $result = $this->send([
                'to' => $invoiceData['client']['email'],
                'subject' => $emailContent['subject'],
                'body_html' => $emailContent['body_html'],
                'body_text' => $emailContent['body_text'],
                'attachments' => [
                    [
                        'name' => $invoiceData['invoice_number'] . '.pdf',
                        'content' => $pdf,
                        'type' => 'application/pdf'
                    ]
                ]
            ]);
            
            // Log reminder
            $this->logPaymentReminder($invoiceId, $reminderLevel, $result);
            
            return $result;
            
        } catch (Exception $e) {
            // Log the error
            error_log('EmailService: sendInvoiceEmail error - ' . $e->getMessage());
            
            // Still try to log the failed email attempt
            $errorResult = [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => 'Invoice Email',
                'body_text' => '',
                'body_html' => '',
                'attachments' => []
            ];
            
            // Try to log the failed attempt
            $this->logEmail($invoiceId, null, $recipientEmail ?? 'unknown', $errorResult);
            
            return $errorResult;
        }
    }
    
    /**
     * Select appropriate invoice template
     */
    private function selectInvoiceTemplate($invoice)
    {
        // Log what we're looking for
        error_log('EmailService: Selecting template for invoice type: ' . ($invoice['invoice_type'] ?? 'unknown'));
        error_log('EmailService: Invoice type code: ' . ($invoice['invoice_type_code'] ?? 'no code'));
        
        $conditions = [
            'invoice_type' => $invoice['invoice_type'],
            'is_first_invoice' => $this->isFirstInvoice($invoice['client_id'] ?? $invoice['user_id'] ?? null),
            'is_retrocession' => in_array($invoice['invoice_type'], ['retrocession_30', 'retrocession_25']),
            'is_credit_note' => !empty($invoice['reference_invoice_id'])
        ];
        
        // Map DIV type to rental for template selection
        $templateType = $invoice['invoice_type'];
        if (($invoice['invoice_type_code'] ?? '') === 'DIV' || $invoice['invoice_type'] === 'rental') {
            $templateType = 'rental';
        }
        
        // Get templates matching conditions
        $stmt = $this->db->prepare("
            SELECT * FROM email_templates
            WHERE email_type = 'new_invoice'
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY invoice_type DESC, priority DESC
            LIMIT 1
        ");
        
        $stmt->execute([':invoice_type' => $templateType]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            // Get any active template as fallback
            $stmt = $this->db->prepare("
                SELECT * FROM email_templates
                WHERE email_type = 'new_invoice'
                AND is_active = TRUE
                ORDER BY priority DESC
                LIMIT 1
            ");
            $stmt->execute();
            $template = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($template) {
                error_log('EmailService: Using fallback template: ' . $template['name']);
            }
        } else {
            error_log('EmailService: Found template: ' . $template['name']);
        }
        
        if (!$template) {
            error_log('EmailService: No email template found!');
        }
        
        return $template;
    }
    
    /**
     * Render email template
     */
    private function renderTemplate($template, $data)
    {
        if (!$template) {
            throw new Exception('No email template found');
        }
        
        // Prepare variables
        $variables = $this->prepareTemplateVariables($data);
        
        // Replace variables in subject and body
        $subject = $this->replaceVariables($template['subject'], $variables);
        $bodyHtml = $this->replaceVariables($template['body_html'], $variables);
        $bodyText = $this->replaceVariables($template['body_text'], $variables);
        
        return [
            'subject' => $subject,
            'body_html' => $bodyHtml,
            'body_text' => $bodyText
        ];
    }
    
    /**
     * Prepare template variables
     */
    private function prepareTemplateVariables($data)
    {
        $variables = [];
        
        // Add raw data objects for advanced templating
        $variables['invoice'] = [];
        $variables['client'] = [];
        $variables['user'] = [];
        $variables['retrocession'] = [];
        $variables['lines'] = [];
        
        // Invoice variables
        if (isset($data['invoice_number'])) {
            // Legacy format support
            $variables['INVOICE_NUMBER'] = $data['invoice_number'];
            $variables['ISSUE_DATE'] = date('d/m/Y', strtotime($data['issue_date']));
            $variables['DUE_DATE'] = date('d/m/Y', strtotime($data['due_date']));
            $variables['TOTAL_AMOUNT'] = number_format($data['total'], 2, ',', ' ');
            $variables['PAYMENT_TERMS'] = $data['payment_terms'] ?? '';
            $variables['SUBJECT'] = $data['subject'] ?? '';
            $variables['PERIOD'] = $data['period'] ?? '';
            
            // New nested format support
            $variables['invoice'] = [
                'number' => $data['invoice_number'],
                'issue_date' => date('d/m/Y', strtotime($data['issue_date'])),
                'issue_date_iso' => $data['issue_date'],
                'due_date' => date('d/m/Y', strtotime($data['due_date'])),
                'due_date_iso' => $data['due_date'],
                'total' => number_format($data['total'], 2, ',', ' '),
                'total_raw' => $data['total'],
                'subtotal' => number_format($data['subtotal'] ?? 0, 2, ',', ' '),
                'subtotal_raw' => $data['subtotal'] ?? 0,
                'vat_amount' => number_format($data['vat_amount'] ?? 0, 2, ',', ' '),
                'vat_amount_raw' => $data['vat_amount'] ?? 0,
                'vat_rate' => $data['vat_rate'] ?? 0,
                'payment_terms' => $data['payment_terms'] ?? '',
                'subject' => $data['subject'] ?? '',
                'period' => $data['period'] ?? '',
                'type' => $data['invoice_type'] ?? '',
                'status' => $data['status'] ?? '',
                'is_credit_note' => !empty($data['reference_invoice_id']),
                'reference_invoice_number' => $data['reference_invoice_number'] ?? ''
            ];
        }
        
        // Client variables
        if (isset($data['client'])) {
            // Legacy format
            $variables['CLIENT_NAME'] = $data['client']['name'];
            $variables['PRACTITIONER_NAME'] = $data['client']['name']; // Alias for retrocession
            
            // New nested format
            $variables['client'] = [
                'name' => $data['client']['name'],
                'email' => $data['client']['email'] ?? '',
                'phone' => $data['client']['phone'] ?? '',
                'address' => $data['client']['address'] ?? '',
                'city' => $data['client']['city'] ?? '',
                'postal_code' => $data['client']['postal_code'] ?? '',
                'country' => $data['client']['country'] ?? '',
                'vat_number' => $data['client']['vat_number'] ?? '',
                'type' => $data['client']['type'] ?? '',
                'is_practitioner' => in_array($data['invoice_type'] ?? '', ['retrocession_30', 'retrocession_25'])
            ];
        }
        
        // User variables (for user invoices)
        if (isset($data['user'])) {
            $fullName = $data['user']['full_name'] ?? $data['user']['name'] ?? 
                       ($data['user']['first_name'] . ' ' . $data['user']['last_name']);
            
            // Legacy format
            $variables['CLIENT_NAME'] = $fullName;
            $variables['PRACTITIONER_NAME'] = $fullName; // Alias
            $variables['USER_NAME'] = $fullName;
            
            // New nested format
            $variables['user'] = [
                'name' => $fullName,
                'first_name' => $data['user']['first_name'] ?? '',
                'last_name' => $data['user']['last_name'] ?? '',
                'email' => $data['user']['email'] ?? '',
                'invoice_email' => $data['user']['invoice_email'] ?? '',
                'phone' => $data['user']['phone'] ?? '',
                'role' => $data['user']['role'] ?? ''
            ];
        }
        
        // Retrocession variables
        if (isset($data['retrocession'])) {
            // Legacy format
            $variables['CNS_AMOUNT'] = number_format($data['retrocession']['cns_amount'], 2, ',', ' ');
            $variables['PATIENT_AMOUNT'] = number_format($data['retrocession']['patient_amount'], 2, ',', ' ');
            $variables['SECRETARIAT_AMOUNT'] = number_format($data['retrocession']['secretariat_tvac'], 2, ',', ' ');
            
            // New nested format
            $variables['retrocession'] = [
                'cns_amount' => number_format($data['retrocession']['cns_amount'], 2, ',', ' '),
                'cns_amount_raw' => $data['retrocession']['cns_amount'],
                'patient_amount' => number_format($data['retrocession']['patient_amount'], 2, ',', ' '),
                'patient_amount_raw' => $data['retrocession']['patient_amount'],
                'secretariat_amount' => number_format($data['retrocession']['secretariat_tvac'], 2, ',', ' '),
                'secretariat_amount_raw' => $data['retrocession']['secretariat_tvac'],
                'secretariat_htva' => number_format($data['retrocession']['secretariat_htva'] ?? 0, 2, ',', ' '),
                'secretariat_htva_raw' => $data['retrocession']['secretariat_htva'] ?? 0,
                'secretariat_vat' => number_format($data['retrocession']['secretariat_vat'] ?? 0, 2, ',', ' '),
                'secretariat_vat_raw' => $data['retrocession']['secretariat_vat'] ?? 0,
                'total_consultations' => $data['retrocession']['total_consultations'] ?? 0,
                'rate' => $data['retrocession']['rate'] ?? 0
            ];
        }
        
        // Rental invoice variables
        if ($data['invoice_type'] === 'rental' || ($data['type_details']['code'] ?? '') === 'DIV') {
            // Try to extract rent and charges from invoice lines
            $rentAmount = 0;
            $chargesAmount = 0;
            
            if (isset($data['lines']) && is_array($data['lines'])) {
                foreach ($data['lines'] as $line) {
                    $description = strtolower($line['description'] ?? '');
                    if (strpos($description, 'loyer') !== false) {
                        $rentAmount += $line['unit_price'] ?? 0;
                    } elseif (strpos($description, 'charge') !== false) {
                        $chargesAmount += $line['unit_price'] ?? 0;
                    }
                }
            }
            
            // If no specific lines found, use subtotal
            if ($rentAmount == 0 && $chargesAmount == 0) {
                $rentAmount = $data['subtotal'] ?? 0;
            }
            
            $variables['RENT_AMOUNT'] = number_format($rentAmount, 2, ',', ' ');
            $variables['CHARGES_AMOUNT'] = number_format($chargesAmount, 2, ',', ' ');
            $variables['VAT_AMOUNT'] = number_format($data['vat_amount'] ?? 0, 2, ',', ' ');
        }
        
        // Period variables
        if (isset($data['period_month']) && isset($data['period_year'])) {
            $variables['MONTH_NAME'] = $this->getMonthName($data['period_month']);
            $variables['YEAR'] = $data['period_year'];
        } elseif (isset($data['period']) && preg_match('/(\w+)\s+(\d{4})/i', $data['period'], $matches)) {
            // Parse period like "JUILLET 2025"
            $variables['MONTH_NAME'] = $matches[1];
            $variables['YEAR'] = $matches[2];
        }
        
        // Voucher variables
        if (isset($data['voucher_number'])) {
            $variables['VOUCHER_NUMBER'] = $data['voucher_number'];
            $variables['VOUCHER_AMOUNT'] = number_format($data['amount'], 2, ',', ' ');
            $variables['EXPIRY_DATE'] = date('d/m/Y', strtotime($data['expiry_date']));
            $variables['BENEFICIARY_NAME'] = $data['beneficiary_name'] ?? '';
        }
        
        // Invoice lines for loops
        if (isset($data['lines']) && is_array($data['lines'])) {
            $variables['lines'] = [];
            foreach ($data['lines'] as $line) {
                $variables['lines'][] = [
                    'description' => $line['description'] ?? '',
                    'quantity' => $line['quantity'] ?? 1,
                    'unit_price' => number_format($line['unit_price'] ?? 0, 2, ',', ' '),
                    'unit_price_raw' => $line['unit_price'] ?? 0,
                    'total' => number_format(($line['quantity'] ?? 1) * ($line['unit_price'] ?? 0), 2, ',', ' '),
                    'total_raw' => ($line['quantity'] ?? 1) * ($line['unit_price'] ?? 0),
                    'vat_rate' => $line['vat_rate'] ?? 0
                ];
            }
        }
        
        // Company variables
        $variables['COMPANY_NAME'] = $this->getConfig('company_name');
        $variables['COMPANY_EMAIL'] = $this->getConfig('company_email');
        $variables['COMPANY_PHONE'] = $this->getConfig('company_phone');
        $variables['COMPANY_ADDRESS'] = $this->getConfig('company_address');
        $variables['COMPANY_VAT'] = $this->getConfig('company_vat_number');
        
        // New nested format
        $variables['company'] = [
            'name' => $this->getConfig('company_name'),
            'email' => $this->getConfig('company_email'),
            'phone' => $this->getConfig('company_phone'),
            'address' => $this->getConfig('company_address'),
            'vat_number' => $this->getConfig('company_vat_number'),
            'website' => $this->getConfig('company_website'),
            'bank_account' => $this->getConfig('company_bank_account'),
            'bank_name' => $this->getConfig('company_bank_name'),
            'iban' => $this->getConfig('company_iban'),
            'bic' => $this->getConfig('company_bic')
        ];
        
        // System variables
        $variables['current_date'] = date('d/m/Y');
        $variables['current_year'] = date('Y');
        $variables['current_month'] = date('m');
        $variables['current_day'] = date('d');
        
        return $variables;
    }
    
    /**
     * Replace variables in text with advanced support
     * Supports multiple formats: {{var}}, {var}, [var]
     * Supports nested variables: {{client.name}}, {{invoice.number}}
     * Supports conditionals: {{#if condition}}...{{/if}}
     * Supports loops: {{#each items}}...{{/each}}
     * Supports defaults: {{var|default}}
     */
    private function replaceVariables($text, $variables)
    {
        // First, handle loops ({{#each items}}...{{/each}})
        $text = $this->processLoops($text, $variables);
        
        // Then, handle conditionals ({{#if condition}}...{{/if}})
        $text = $this->processConditionals($text, $variables);
        
        // Finally, replace regular variables
        $text = $this->replaceSimpleVariables($text, $variables);
        
        return $text;
    }
    
    /**
     * Process loops in template
     */
    private function processLoops($text, $variables)
    {
        // Pattern for {{#each array}}...{{/each}}
        $pattern = '/\{\{#each\s+([\w\.]+)\}\}(.+?)\{\{\/each\}\}/s';
        
        return preg_replace_callback($pattern, function($matches) use ($variables) {
            $arrayPath = $matches[1];
            $loopContent = $matches[2];
            $result = '';
            
            // Get array from variables using dot notation
            $array = $this->getNestedValue($variables, $arrayPath);
            
            if (is_array($array)) {
                foreach ($array as $index => $item) {
                    $itemContent = $loopContent;
                    
                    // Replace {{@index}} with current index
                    $itemContent = str_replace('{{@index}}', $index, $itemContent);
                    
                    // Replace {{@key}} with current key (for associative arrays)
                    $itemContent = str_replace('{{@key}}', $index, $itemContent);
                    
                    // Replace {{this}} with current item (if scalar)
                    if (is_scalar($item)) {
                        $itemContent = str_replace('{{this}}', $item, $itemContent);
                    }
                    
                    // Replace item properties
                    if (is_array($item)) {
                        foreach ($item as $key => $value) {
                            if (is_scalar($value)) {
                                // Support both {{key}} and {{this.key}} inside loops
                                $itemContent = str_replace('{{' . $key . '}}', $value, $itemContent);
                                $itemContent = str_replace('{{this.' . $key . '}}', $value, $itemContent);
                            }
                        }
                    }
                    
                    $result .= $itemContent;
                }
            }
            
            return $result;
        }, $text);
    }
    
    /**
     * Process conditionals in template
     */
    private function processConditionals($text, $variables)
    {
        // Pattern for {{#if condition}}...{{/if}} with optional {{else}}
        $pattern = '/\{\{#if\s+([\w\.!]+)\}\}(.*?)(?:\{\{else\}\}(.*?))?\{\{\/if\}\}/s';
        
        return preg_replace_callback($pattern, function($matches) use ($variables) {
            $condition = $matches[1];
            $ifContent = $matches[2];
            $elseContent = $matches[3] ?? '';
            
            // Check if condition starts with ! (negation)
            $negate = false;
            if (strpos($condition, '!') === 0) {
                $negate = true;
                $condition = substr($condition, 1);
            }
            
            // Evaluate condition
            $value = $this->getNestedValue($variables, $condition);
            $isTrue = $this->evaluateCondition($value);
            
            if ($negate) {
                $isTrue = !$isTrue;
            }
            
            return $isTrue ? $ifContent : $elseContent;
        }, $text);
    }
    
    /**
     * Replace simple variables including nested ones
     */
    private function replaceSimpleVariables($text, $variables)
    {
        // Flatten nested variables for easier replacement
        $flatVariables = $this->flattenVariables($variables);
        
        // Replace {{var}}, {{var|default}}, and {{nested.var}} format
        $text = preg_replace_callback('/\{\{([\w\.]+)(\|([^}]+))?\}\}/', function($matches) use ($flatVariables) {
            $varPath = $matches[1];
            $defaultValue = $matches[3] ?? '';
            
            // Check both the original path and uppercase version
            $value = $flatVariables[$varPath] ?? $flatVariables[strtoupper($varPath)] ?? null;
            
            return $value !== null ? $value : $defaultValue;
        }, $text);
        
        // Replace {var} format (legacy support)
        foreach ($variables as $key => $value) {
            if (is_scalar($value)) {
                $text = str_replace('{' . $key . '}', $value, $text);
            }
        }
        
        // Replace [var] format
        $text = preg_replace_callback('/\[([\w\.]+)\]/', function($matches) use ($flatVariables) {
            $varPath = $matches[1];
            $value = $flatVariables[$varPath] ?? $flatVariables[strtoupper($varPath)] ?? null;
            return $value !== null ? $value : '[' . $varPath . ']';
        }, $text);
        
        return $text;
    }
    
    /**
     * Get nested value from array using dot notation
     */
    private function getNestedValue($array, $path)
    {
        $keys = explode('.', $path);
        $value = $array;
        
        foreach ($keys as $key) {
            if (is_array($value) && isset($value[$key])) {
                $value = $value[$key];
            } else {
                return null;
            }
        }
        
        return $value;
    }
    
    /**
     * Flatten nested variables for easy access
     */
    private function flattenVariables($variables, $prefix = '')
    {
        $flat = [];
        
        foreach ($variables as $key => $value) {
            $fullKey = $prefix ? $prefix . '.' . $key : $key;
            
            if (is_array($value) && $this->isAssociativeArray($value)) {
                // Recursively flatten nested arrays
                $flat = array_merge($flat, $this->flattenVariables($value, $fullKey));
            } else {
                // Store the value
                $flat[$fullKey] = is_scalar($value) ? $value : json_encode($value);
            }
        }
        
        return $flat;
    }
    
    /**
     * Check if array is associative
     */
    private function isAssociativeArray($arr)
    {
        if (!is_array($arr) || empty($arr)) {
            return false;
        }
        return array_keys($arr) !== range(0, count($arr) - 1);
    }
    
    /**
     * Evaluate condition for truthiness
     */
    private function evaluateCondition($value)
    {
        if (is_null($value)) {
            return false;
        }
        if (is_bool($value)) {
            return $value;
        }
        if (is_numeric($value)) {
            return $value != 0;
        }
        if (is_string($value)) {
            return !empty($value) && strtolower($value) !== 'false';
        }
        if (is_array($value)) {
            return !empty($value);
        }
        return true;
    }
    
    /**
     * Send email with enhanced features
     */
    private function send($params)
    {
        try {
            // Check if queue is enabled
            $queueEnabled = $_ENV['MAIL_QUEUE_ENABLED'] ?? false;
            
            if ($queueEnabled && !($params['immediate'] ?? false)) {
                // Add to queue instead of sending immediately
                $queueService = new EmailQueueService();
                $queueId = $queueService->queueEmail($params);
                
                return [
                    'success' => true,
                    'message' => 'Email queued successfully',
                    'queue_id' => $queueId,
                    'subject' => $params['subject'],
                    'body_text' => $params['body_text'] ?? '',
                    'body_html' => $params['body_html'] ?? '',
                    'attachments' => $params['attachments'] ?? []
                ];
            }
            
            // Validate parameters
            if (empty($params['to']) || empty($params['subject'])) {
                throw new Exception('Missing required email parameters');
            }
            
            // Get mail configuration from environment
            $host = $_ENV['MAIL_HOST'] ?? 'localhost';
            $port = $_ENV['MAIL_PORT'] ?? 1025;
            $username = $_ENV['MAIL_USERNAME'] ?? '';
            $password = $_ENV['MAIL_PASSWORD'] ?? '';
            $encryption = $_ENV['MAIL_ENCRYPTION'] ?? '';
            $fromEmail = $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>';
            $fromName = $_ENV['MAIL_FROM_NAME'] ?? 'Fit360 AdminDesk';
            
            // Create PHPMailer instance
            $mail = new PHPMailer(true);
            
            try {
                // Log email configuration
                error_log('EmailService: Attempting to send email to: ' . $params['to']);
                error_log('EmailService: SMTP Config - Host: ' . $host . ', Port: ' . $port);
                error_log('EmailService: From: ' . $fromEmail . ' (' . $fromName . ')');
                
                // Server settings
                $mail->isSMTP();
                $mail->Host       = $host;
                $mail->Port       = $port;
                
                // Enable SMTP debugging
                $mail->SMTPDebug = SMTP::DEBUG_SERVER;
                $mail->Debugoutput = function($str, $level) {
                    error_log('EmailService SMTP Debug: ' . $str);
                };
                
                // For Mailhog (local development)
                if ($host === 'localhost' && $port == 1025) {
                    $mail->SMTPAuth   = false;
                    $mail->SMTPSecure = false;
                    $mail->SMTPAutoTLS = false;
                    error_log('EmailService: Using Mailhog configuration (no auth)');
                } else {
                    // For production SMTP
                    $mail->SMTPAuth   = !empty($username);
                    $mail->Username   = $username;
                    $mail->Password   = $password;
                    
                    if ($encryption === 'tls') {
                        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                    } elseif ($encryption === 'ssl') {
                        $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                    }
                    error_log('EmailService: Using production SMTP configuration');
                }
                
                // Recipients
                $mail->setFrom($fromEmail, $fromName);
                
                // Handle multiple TO recipients
                if (strpos($params['to'], ',') !== false) {
                    $recipients = explode(',', $params['to']);
                    foreach ($recipients as $recipient) {
                        $mail->addAddress(trim($recipient));
                    }
                } else {
                    $mail->addAddress($params['to']);
                }
                
                // CC recipients
                if (!empty($params['cc'])) {
                    $ccList = is_array($params['cc']) ? $params['cc'] : explode(',', $params['cc']);
                    foreach ($ccList as $cc) {
                        $mail->addCC(trim($cc));
                    }
                }
                
                // BCC recipients
                if (!empty($params['bcc'])) {
                    $bccList = is_array($params['bcc']) ? $params['bcc'] : explode(',', $params['bcc']);
                    foreach ($bccList as $bcc) {
                        $mail->addBCC(trim($bcc));
                    }
                }
                
                // Reply-To
                if (!empty($params['reply_to'])) {
                    $mail->addReplyTo($params['reply_to']);
                }
                
                // Attachments
                if (!empty($params['attachments']) && is_array($params['attachments'])) {
                    foreach ($params['attachments'] as $attachment) {
                        if (!empty($attachment['content']) && !empty($attachment['name'])) {
                            // Save attachment to temp file
                            $tempFile = tempnam(sys_get_temp_dir(), 'mail_attachment_');
                            file_put_contents($tempFile, $attachment['content']);
                            
                            // Add to email
                            $mail->addAttachment($tempFile, $attachment['name'], 'base64', $attachment['type'] ?? 'application/octet-stream');
                            
                            // Register temp file for cleanup
                            register_shutdown_function(function() use ($tempFile) {
                                if (file_exists($tempFile)) {
                                    unlink($tempFile);
                                }
                            });
                        }
                    }
                }
                
                // Content
                $mail->CharSet = 'UTF-8';
                $mail->Subject = $params['subject'];
                
                if (!empty($params['body_html'])) {
                    $mail->isHTML(true);
                    $mail->Body = $params['body_html'];
                    $mail->AltBody = $params['body_text'] ?? strip_tags($params['body_html']);
                } else {
                    $mail->isHTML(false);
                    $mail->Body = $params['body_text'] ?? '';
                }
                
                // Send email
                error_log('EmailService: Attempting to send email...');
                $mail->send();
                
                error_log('EmailService: Email sent successfully to ' . $params['to']);
                
                return [
                    'success' => true, 
                    'message' => 'Email sent successfully' . ($host === 'localhost' && $port == 1025 ? ' to Mailhog' : ''),
                    'subject' => $params['subject'] ?? 'Invoice Email',
                    'body_text' => $params['body_text'] ?? '',
                    'body_html' => $params['body_html'] ?? '',
                    'attachments' => $params['attachments'] ?? []
                ];
                
            } catch (\PHPMailer\PHPMailer\Exception $e) {
                error_log('EmailService: PHPMailer Exception - ' . $e->getMessage());
                error_log('EmailService: PHPMailer ErrorInfo - ' . $mail->ErrorInfo);
                
                // Handle specific SMTP errors
                $errorMessage = $this->handleSmtpError($mail->ErrorInfo, $e->getMessage());
                throw new Exception($errorMessage);
            }
            
        } catch (Exception $e) {
            // Log the error
            error_log('EmailService: sendInvoiceEmail error - ' . $e->getMessage());
            
            // Still try to log the failed email attempt
            $errorResult = [
                'success' => false, 
                'message' => $e->getMessage(),
                'subject' => 'Invoice Email',
                'body_text' => '',
                'body_html' => '',
                'attachments' => []
            ];
            
            // Try to log the failed attempt
            $this->logEmail($invoiceId, null, $recipientEmail ?? 'unknown', $errorResult);
            
            return $errorResult;
        }
    }
    
    /**
     * Log email
     */
    private function logEmail($invoiceId, $templateId, $recipient, $result)
    {
        try {
            // Prepare body preview (first 500 chars of the email body)
            $bodyPreview = '';
            if (isset($result['body_text'])) {
                $bodyPreview = substr($result['body_text'], 0, 500);
            } elseif (isset($result['body_html'])) {
                $bodyPreview = substr(strip_tags($result['body_html']), 0, 500);
            }
            
            // Prepare attachments info
            $attachmentsSent = [];
            if (isset($result['attachments'])) {
                foreach ($result['attachments'] as $attachment) {
                    $attachmentsSent[] = [
                        'name' => $attachment['name'] ?? 'attachment',
                        'type' => $attachment['type'] ?? 'application/octet-stream'
                    ];
                }
            }
            
            $stmt = $this->db->prepare("
                INSERT INTO email_logs (
                    invoice_id, template_id, recipient_type, recipient_email,
                    subject, body_preview, attachments_sent, status, 
                    sent_at, error_message, created_at
                ) VALUES (
                    :invoice_id, :template_id, :recipient_type, :recipient_email,
                    :subject, :body_preview, :attachments_sent, :status,
                    :sent_at, :error_message, NOW()
                )
            ");
            
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':template_id' => $templateId,
                ':recipient_type' => 'primary', // Default to primary recipient
                ':recipient_email' => $recipient,
                ':subject' => $result['subject'] ?? 'Invoice Email',
                ':body_preview' => $bodyPreview,
                ':attachments_sent' => !empty($attachmentsSent) ? json_encode($attachmentsSent) : null,
                ':status' => $result['success'] ? 'sent' : 'failed',
                ':sent_at' => $result['success'] ? date('Y-m-d H:i:s') : null,
                ':error_message' => $result['success'] ? null : ($result['message'] ?? 'Unknown error')
            ]);
            
            error_log('EmailService: Email log created for invoice ' . $invoiceId . ', status: ' . ($result['success'] ? 'sent' : 'failed'));
        } catch (Exception $e) {
            error_log('EmailService: Failed to log email - ' . $e->getMessage());
        }
    }
    
    /**
     * Log payment reminder
     */
    private function logPaymentReminder($invoiceId, $reminderLevel, $result)
    {
        $stmt = $this->db->prepare("
            UPDATE payment_reminders
            SET status = :status,
                sent_date = :sent_date
            WHERE invoice_id = :invoice_id
            AND reminder_level = :reminder_level
            AND status = 'scheduled'
        ");
        
        $stmt->execute([
            ':status' => $result['success'] ? 'sent' : 'failed',
            ':sent_date' => $result['success'] ? date('Y-m-d H:i:s') : null,
            ':invoice_id' => $invoiceId,
            ':reminder_level' => $reminderLevel
        ]);
    }
    
    /**
     * Check if first invoice for client/user
     */
    private function isFirstInvoice($entityId)
    {
        if (!$entityId) {
            return false;
        }
        
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as count
            FROM invoices
            WHERE (client_id = :entity_id1 OR user_id = :entity_id2)
            AND status != 'draft'
        ");
        
        $stmt->execute([
            ':entity_id1' => $entityId,
            ':entity_id2' => $entityId
        ]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return $result['count'] == 0;
    }
    
    /**
     * Get email template
     */
    private function getEmailTemplate($code, $invoiceType = null)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM email_templates
            WHERE code = :code
            AND is_active = TRUE
            AND (invoice_type = :invoice_type OR invoice_type IS NULL)
            ORDER BY invoice_type DESC
            LIMIT 1
        ");
        
        $stmt->execute([
            ':code' => $code,
            ':invoice_type' => $invoiceType
        ]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get config value
     */
    private function getConfig($key)
    {
        $stmt = $this->db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        return $stmt->fetch(PDO::FETCH_COLUMN) ?: '';
    }
    
    /**
     * Get month name in French
     */
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
    
    /**
     * Generate invoice PDF using the same method as the download endpoint
     * This ensures consistency between downloaded and emailed PDFs
     */
    private function generateInvoicePdfFromDownloadScript($invoiceId)
    {
        try {
            error_log('EmailService: Generating PDF for invoice ID ' . $invoiceId . ' using invoice-pdf.php');
            
            // We need to execute the invoice-pdf.php in a way that returns the PDF content
            // Since it uses exit() and outputs directly, we'll use a different approach
            
            // Option 1: Use file_get_contents with URL (if allow_url_fopen is enabled)
            $baseUrl = $_ENV['APP_URL'] ?? 'http://localhost/fit/public';
            $pdfUrl = $baseUrl . '/invoice-pdf.php?id=' . $invoiceId . '&action=download';
            
            error_log('EmailService: Attempting to fetch PDF from URL: ' . $pdfUrl);
            
            // Try URL fetch first
            $context = stream_context_create([
                'http' => [
                    'timeout' => 30,
                    'header' => "User-Agent: EmailService/1.0\r\n"
                ]
            ]);
            
            $pdfContent = @file_get_contents($pdfUrl, false, $context);
            
            if ($pdfContent === false) {
                error_log('EmailService: URL fetch failed, trying alternative method');
                
                // Option 2: Execute PHP script and capture output
                $scriptPath = dirname(__DIR__, 2) . '/public/invoice-pdf.php';
                
                if (!file_exists($scriptPath)) {
                    throw new Exception('Invoice PDF script not found at: ' . $scriptPath);
                }
                
                // Use output buffering and execute in isolated scope
                $pdfContent = $this->executePdfScript($scriptPath, $invoiceId);
            }
            
            if (empty($pdfContent)) {
                throw new Exception('PDF generation returned empty content');
            }
            
            // Verify it's actually a PDF
            if (substr($pdfContent, 0, 4) !== '%PDF') {
                error_log('EmailService: Invalid PDF content, first 100 chars: ' . substr($pdfContent, 0, 100));
                throw new Exception('Generated content is not a valid PDF');
            }
            
            error_log('EmailService: PDF generated successfully, size: ' . strlen($pdfContent) . ' bytes');
            
            return $pdfContent;
            
        } catch (Exception $e) {
            error_log('EmailService: Failed to generate PDF using invoice-pdf.php: ' . $e->getMessage());
            
            // Fallback to PdfService if the download script fails
            error_log('EmailService: Falling back to PdfService for PDF generation');
            
            // Get invoice data for fallback
            $invoice = new Invoice();
            $invoiceData = $invoice->getInvoiceWithDetails($invoiceId);
            
            if (!$invoiceData) {
                throw new Exception('Invoice not found for PDF generation');
            }
            
            // Use the original PdfService as fallback
            $pdfService = new PdfService();
            return $pdfService->generateInvoicePdf($invoiceData);
        }
    }
    
    /**
     * Execute PDF script in isolated scope
     */
    private function executePdfScript($scriptPath, $invoiceId)
    {
        // Set up the environment for string output
        $_GET['id'] = $invoiceId;
        $_GET['action'] = 'string'; // This will make invoice-pdf.php output raw PDF
        
        // Start output buffering to capture the PDF
        ob_start();
        
        try {
            // Execute the script - it will output PDF and exit
            include $scriptPath;
        } catch (Exception $e) {
            // The script might throw exceptions
            error_log('EmailService: Exception in PDF script: ' . $e->getMessage());
        }
        
        // Get whatever was output (should be the PDF content)
        $output = ob_get_clean();
        
        return $output;
    }
    
    /**
     * Handle SMTP errors with user-friendly messages
     */
    private function handleSmtpError($smtpError, $exceptionMessage)
    {
        $errorMap = [
            'SMTP connect() failed' => 'Unable to connect to email server. Please check SMTP settings.',
            'Authentication failed' => 'Email authentication failed. Please check username and password.',
            'Connection timeout' => 'Email server connection timeout. Please try again later.',
            'Invalid address' => 'Invalid email address format.',
            'Relay access denied' => 'Email server relay access denied. Check SMTP configuration.',
            'Mailbox unavailable' => 'Recipient mailbox unavailable or full.',
            'Message rejected' => 'Email message rejected by server. Check content and attachments.',
            'Daily sending quota exceeded' => 'Daily email sending limit reached. Try again tomorrow.',
            'Rate limit' => 'Too many emails sent. Please wait before sending more.',
            'TLS' => 'Secure connection (TLS) error. Check encryption settings.',
            'SSL' => 'Secure connection (SSL) error. Check encryption settings.'
        ];
        
        // Check for known error patterns
        foreach ($errorMap as $pattern => $friendlyMessage) {
            if (stripos($smtpError, $pattern) !== false || stripos($exceptionMessage, $pattern) !== false) {
                return $friendlyMessage . ' (Technical: ' . $smtpError . ')';
            }
        }
        
        // Default error message
        return 'Email sending failed: ' . $smtpError;
    }
}