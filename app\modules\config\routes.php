<?php

use App\Controllers\ConfigController;
use App\Controllers\TranslationController;
use App\Controllers\FieldManagerController;
use App\Core\Request;
use App\Core\Response;

// Configuration Dashboard
Flight::route('GET /config', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->index($request, $response);
});

// VAT Rates Management
Flight::route('GET /config/vat-rates', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->vatRates($request, $response);
});

Flight::route('POST /config/vat-rates', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->storeVatRate($request, $response);
});

Flight::route('PUT /config/vat-rates/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateVatRate($request, $response, $id);
});

Flight::route('DELETE /config/vat-rates/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deleteVatRate($request, $response, $id);
});

Flight::route('PUT /config/vat-rates/@id/default', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->setDefaultVatRate($request, $response, $id);
});

// Bulk operations
Flight::route('POST /config/vat-rates/bulk-delete', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->bulkDeleteVatRates($request, $response);
});

Flight::route('GET /config/vat-rates/export', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->exportVatRates($request, $response);
});

Flight::route('POST /config/vat-rates/import', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->importVatRates($request, $response);
});

// Invoice Types Management
Flight::route('GET /config/invoice-types', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->invoiceTypes($request, $response);
});

Flight::route('POST /config/invoice-types', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->storeInvoiceType($request, $response);
});

Flight::route('PUT /config/invoice-types/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateInvoiceType($request, $response, $id);
});

Flight::route('DELETE /config/invoice-types/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deleteInvoiceType($request, $response, $id);
});

Flight::route('POST /config/invoice-types/reorder', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->reorderInvoiceTypes($request, $response);
});

// Email Templates Management
Flight::route('GET /config/email-templates', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->emailTemplates($request, $response);
});

Flight::route('GET /config/email-templates/create', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->createEmailTemplate($request, $response);
});

Flight::route('POST /config/email-templates', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->storeEmailTemplate($request, $response);
});

Flight::route('GET /config/email-templates/@id:[0-9]+/edit', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->editEmailTemplate($request, $response, $id);
});

Flight::route('PUT /config/email-templates/@id:[0-9]+', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateEmailTemplate($request, $response, $id);
});

Flight::route('DELETE /config/email-templates/@id:[0-9]+', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deleteEmailTemplate($request, $response, $id);
});

Flight::route('GET /config/email-templates/@id:[0-9]+/test', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->testEmailTemplate($request, $response, $id);
});

Flight::route('POST /config/email-templates/preview', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->previewEmailTemplate($request, $response);
});

Flight::route('POST /config/email-templates/send-test', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->sendTestEmail($request, $response);
});

Flight::route('POST /config/email-templates/@id:[0-9]+/duplicate', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->duplicateEmailTemplate($request, $response, $id);
});

Flight::route('POST /config/email-templates/update-priority', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateTemplatePriority($request, $response);
});

// Company Settings
Flight::route('GET /config/company', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->companySettings($request, $response);
});

Flight::route('PUT /config/company', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateCompanySettings($request, $response);
});

// System Settings
Flight::route('GET /config/system', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->systemSettings($request, $response);
});

// Accept both PUT and POST (for compatibility)
Flight::route('PUT|POST /config/system', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateSystemSettings($request, $response);
});

// Translation Management Routes
Flight::route('GET /translations', function() {
    $controller = new TranslationController();
    $controller->index();
});

Flight::route('POST /translations/update', function() {
    $controller = new TranslationController();
    $controller->update();
});

Flight::route('POST /translations/add', function() {
    $controller = new TranslationController();
    $controller->add();
});

Flight::route('POST /translations/delete', function() {
    $controller = new TranslationController();
    $controller->delete();
});

Flight::route('POST /translations/import', function() {
    $controller = new TranslationController();
    $controller->import();
});

Flight::route('GET /translations/export', function() {
    $controller = new TranslationController();
    $controller->export();
});

Flight::route('POST /translations/sync', function() {
    $controller = new TranslationController();
    $controller->sync();
});

Flight::route('GET /translations/multilingual', function() {
    // Redirect to the merged editor
    Flight::redirect('/translations');
});

Flight::route('POST /translations/save', function() {
    $controller = new TranslationController();
    $controller->save();
});

Flight::route('GET /translations/multilang', function() {
    $controller = new TranslationController();
    $controller->getMultilang();
});

Flight::route('POST /translations/delete', function() {
    $controller = new TranslationController();
    $controller->deleteKey();
});

// Translation Diagnostic and Management Routes
Flight::route('GET /translations/diagnostic', function() {
    $controller = new TranslationController();
    $controller->diagnostic();
});

Flight::route('POST /translations/fix-missing', function() {
    $controller = new TranslationController();
    $controller->fixMissing();
});

Flight::route('POST /translations/sync-to-files', function() {
    $controller = new TranslationController();
    $controller->syncToFiles();
});

// Field Management Routes
Flight::route('GET /config/fields', [FieldManagerController::class, 'index']);
Flight::route('GET /config/fields/@module', [FieldManagerController::class, 'index']);
Flight::route('POST /config/fields/visibility', [FieldManagerController::class, 'saveVisibility']);
Flight::route('POST /config/fields/create', [FieldManagerController::class, 'createField']);
Flight::route('POST /config/fields/@id:[0-9]+/update', [FieldManagerController::class, 'updateField']);
Flight::route('DELETE /config/fields/@id:[0-9]+', [FieldManagerController::class, 'deleteField']);

// Number Formats Management
Flight::route('GET /config/number-formats', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->numberFormats($request, $response);
});

Flight::route('PUT|POST /config/number-formats', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateNumberFormats($request, $response);
});

// Payment Methods Management
Flight::route('GET /config/payment-methods', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->paymentMethods($request, $response);
});

Flight::route('POST /config/payment-methods', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->createPaymentMethod($request, $response);
});

Flight::route('PUT /config/payment-methods/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updatePaymentMethod($request, $response, $id);
});

Flight::route('DELETE /config/payment-methods/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deletePaymentMethod($request, $response, $id);
});

// Payment Terms Management
Flight::route('GET /config/payment-terms', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->paymentTerms($request, $response);
});

Flight::route('POST /config/payment-terms', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->createPaymentTerm($request, $response);
});

Flight::route('PUT /config/payment-terms/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updatePaymentTerm($request, $response, $id);
});

Flight::route('DELETE /config/payment-terms/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deletePaymentTerm($request, $response, $id);
});

// Document Types Management
Flight::route('GET /config/document-types', function() {
    $controller = new ConfigController();
    $controller->documentTypes();
});

Flight::route('POST /config/document-types', function() {
    $controller = new ConfigController();
    $controller->createDocumentType();
});

Flight::route('PUT /config/document-types/@id', function($id) {
    $controller = new ConfigController();
    $controller->updateDocumentType($id);
});

Flight::route('DELETE /config/document-types/@id', function($id) {
    $controller = new ConfigController();
    $controller->deleteDocumentType($id);
});

// Table Columns Configuration
Flight::route('GET /config/table-columns', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->tableColumns($request, $response);
});

// Invoice Items Columns Configuration
Flight::route('GET /config/invoice-items-columns', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->invoiceItemsColumns($request, $response);
});

Flight::route('GET /config/table-columns/@table', function($table) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->getTableColumns($request, $response, $table);
});

Flight::route('POST /config/table-columns/save', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->saveTableColumns($request, $response);
});

// Document Type specific column configuration
Flight::route('GET /config/document-type-columns/@table', function($table) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->getDocumentTypeColumns($request, $response, $table);
});

Flight::route('POST /config/document-type-columns/save', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->saveDocumentTypeColumns($request, $response);
});

Flight::route('GET /config/document-types/list', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->getDocumentTypes($request, $response);
});

// Get column configuration for JavaScript/AJAX
Flight::route('GET /api/column-config/@table', function($table) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->getColumnConfigForApi($request, $response, $table);
});

// Color Schemes Management
Flight::route('GET /config/color-schemes', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->colorSchemes($request, $response);
});

Flight::route('POST /config/color-schemes', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->createColorScheme($request, $response);
});

Flight::route('PUT /config/color-schemes/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateColorScheme($request, $response, $id);
});

Flight::route('DELETE /config/color-schemes/@id', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deleteColorScheme($request, $response, $id);
});

Flight::route('POST /config/color-schemes/@id/activate', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->setActiveColorScheme($request, $response, $id);
});

Flight::route('POST /config/color-schemes/@id/duplicate', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->duplicateColorScheme($request, $response, $id);
});

Flight::route('GET /config/color-schemes/@id/preview', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->previewColorScheme($request, $response, $id);
});

// Invoice Templates Management
Flight::route('GET /config/invoice-templates', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->invoiceTemplates($request, $response);
});

Flight::route('GET /config/invoice-templates/create', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->createInvoiceTemplate($request, $response);
});

Flight::route('POST /config/invoice-templates', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->storeInvoiceTemplate($request, $response);
});

Flight::route('GET /config/invoice-templates/@id:[0-9]+/edit', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->editInvoiceTemplate($request, $response, $id);
});

Flight::route('PUT /config/invoice-templates/@id:[0-9]+', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateInvoiceTemplate($request, $response, $id);
});

Flight::route('DELETE /config/invoice-templates/@id:[0-9]+', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->deleteInvoiceTemplate($request, $response, $id);
});

Flight::route('POST /config/invoice-templates/@id:[0-9]+/duplicate', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->duplicateInvoiceTemplate($request, $response, $id);
});

Flight::route('GET /config/invoice-templates/@id:[0-9]+/items', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->manageTemplateItems($request, $response, $id);
});

Flight::route('POST /config/invoice-templates/@id:[0-9]+/items', function($id) {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->updateTemplateItems($request, $response, $id);
});

Flight::route('POST /config/invoice-templates/create-from-invoice', function() {
    $controller = new ConfigController();
    $request = new Request();
    $response = new Response();
    $controller->createTemplateFromInvoice($request, $response);
});