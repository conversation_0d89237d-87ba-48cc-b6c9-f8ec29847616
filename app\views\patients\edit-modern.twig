{% extends "base-modern.twig" %}

{% block title %}{{ __('patients.edit') }} - {{ patient.first_name }} {{ patient.last_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('patients.edit') }}</h1>
        <a href="{{ base_url }}/patients/{{ patient.id }}" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <form method="POST" action="{{ base_url }}/patients/{{ patient.id }}" class="needs-validation" novalidate>
                <input type="hidden" name="_method" value="PUT">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <!-- Basic Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-person-fill me-2"></i>{{ __('common.basic_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label for="title" class="form-label">{{ __('patients.title') }}</label>
                                <select class="form-select" id="title" name="title" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    <option value="Mr" {{ patient.title == 'Mr' ? 'selected' : '' }}>M.</option>
                                    <option value="Mrs" {{ patient.title == 'Mrs' ? 'selected' : '' }}>Mme</option>
                                    <option value="Ms" {{ patient.title == 'Ms' ? 'selected' : '' }}>Mlle</option>
                                    <option value="Dr" {{ patient.title == 'Dr' ? 'selected' : '' }}>Dr</option>
                                </select>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-5">
                                <label for="first_name" class="form-label">{{ __('common.first_name') }} *</label>
                                <input type="text" class="form-control" id="first_name" name="first_name" value="{{ patient.first_name }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-5">
                                <label for="last_name" class="form-label">{{ __('common.last_name') }} *</label>
                                <input type="text" class="form-control" id="last_name" name="last_name" value="{{ patient.last_name }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="birth_date" class="form-label">{{ __('common.birth_date') }} *</label>
                                <input type="date" class="form-control" id="birth_date" name="birth_date" value="{{ patient.birth_date }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="gender" class="form-label">{{ __('patients.gender') }} *</label>
                                <select class="form-select" id="gender" name="gender" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    <option value="M" {{ patient.gender == 'M' ? 'selected' : '' }}>{{ __('common.male') }}</option>
                                    <option value="F" {{ patient.gender == 'F' ? 'selected' : '' }}>{{ __('common.female') }}</option>
                                </select>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-4">
                                <label for="social_security_number" class="form-label">{{ __('patients.social_security_number') }}</label>
                                <input type="text" class="form-control" id="social_security_number" name="social_security_number" value="{{ patient.social_security_number }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-telephone-fill me-2"></i>{{ __('patients.contact_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="phone" class="form-label">{{ __('common.phone') }}</label>
                                <input type="tel" class="form-control" id="phone" name="phone" value="{{ patient.phone }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="mobile" class="form-label">{{ __('patients.mobile') }}</label>
                                <input type="tel" class="form-control" id="mobile" name="mobile" value="{{ patient.mobile }}">
                            </div>
                            
                            <div class="col-md-12">
                                <label for="email" class="form-label">{{ __('common.email') }}</label>
                                <input type="email" class="form-control" id="email" name="email" value="{{ patient.email }}">
                                <div class="invalid-feedback">
                                    {{ __('validation.email') }}
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="address" class="form-label">{{ __('common.address') }}</label>
                                <input type="text" class="form-control" id="address" name="address" value="{{ patient.address }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="postal_code" class="form-label">{{ __('common.postal_code') }}</label>
                                <input type="text" class="form-control" id="postal_code" name="postal_code" value="{{ patient.postal_code }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="city" class="form-label">{{ __('common.city') }}</label>
                                <input type="text" class="form-control" id="city" name="city" value="{{ patient.city }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="country" class="form-label">{{ __('common.country') }}</label>
                                <select class="form-select" id="country" name="country">
                                    <option value="LU" {{ patient.country == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                    <option value="FR" {{ patient.country == 'FR' ? 'selected' : '' }}>France</option>
                                    <option value="BE" {{ patient.country == 'BE' ? 'selected' : '' }}>Belgique</option>
                                    <option value="DE" {{ patient.country == 'DE' ? 'selected' : '' }}>Allemagne</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Medical Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-heart-pulse-fill me-2"></i>{{ __('patients.medical_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="blood_type" class="form-label">{{ __('common.blood_type') }}</label>
                                <select class="form-select" id="blood_type" name="blood_type">
                                    <option value="">{{ __('common.unknown') }}</option>
                                    <option value="A+" {{ patient.blood_type == 'A+' ? 'selected' : '' }}>A+</option>
                                    <option value="A-" {{ patient.blood_type == 'A-' ? 'selected' : '' }}>A-</option>
                                    <option value="B+" {{ patient.blood_type == 'B+' ? 'selected' : '' }}>B+</option>
                                    <option value="B-" {{ patient.blood_type == 'B-' ? 'selected' : '' }}>B-</option>
                                    <option value="AB+" {{ patient.blood_type == 'AB+' ? 'selected' : '' }}>AB+</option>
                                    <option value="AB-" {{ patient.blood_type == 'AB-' ? 'selected' : '' }}>AB-</option>
                                    <option value="O+" {{ patient.blood_type == 'O+' ? 'selected' : '' }}>O+</option>
                                    <option value="O-" {{ patient.blood_type == 'O-' ? 'selected' : '' }}>O-</option>
                                </select>
                            </div>
                            
                            <div class="col-md-9">
                                <label for="allergies" class="form-label">{{ __('common.allergies') }}</label>
                                <input type="text" class="form-control" id="allergies" name="allergies" value="{{ patient.allergies }}"
                                       placeholder="{{ __('patients.allergies_placeholder') }}">
                            </div>
                            
                            <div class="col-md-12">
                                <label for="medical_history" class="form-label">{{ __('patients.medical_history') }}</label>
                                <textarea class="form-control" id="medical_history" name="medical_history" rows="3">{{ patient.medical_history }}</textarea>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="notes" class="form-label">{{ __('common.notes') }}</label>
                                <textarea class="form-control" id="notes" name="notes" rows="3">{{ patient.notes }}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                    </button>
                    <div class="d-flex gap-2">
                        <a href="{{ base_url }}/patients/{{ patient.id }}" class="btn btn-secondary">
                            <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i>{{ __('common.save_changes') }}
                        </button>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- Side Information -->
        <div class="col-lg-4">
            <!-- Patient Info Card -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="bi bi-person-badge-fill me-2"></i>{{ __('patients.patient_info') }}</h5>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-5">{{ __('patients.patient_number') }}:</dt>
                        <dd class="col-sm-7">{{ patient.patient_number }}</dd>
                        
                        <dt class="col-sm-5">{{ __('common.created_at') }}:</dt>
                        <dd class="col-sm-7">{{ patient.created_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-sm-5">{{ __('common.updated_at') }}:</dt>
                        <dd class="col-sm-7">{{ patient.updated_at|date('d/m/Y H:i') }}</dd>
                        
                        <dt class="col-sm-5">{{ __('common.status') }}:</dt>
                        <dd class="col-sm-7">
                            {% if patient.is_active %}
                                <span class="badge bg-success">{{ __('common.active') }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="card shadow-sm">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>{{ __('patients.recent_activity') }}</h5>
                </div>
                <div class="card-body">
                    {% if recent_visits %}
                        <div class="timeline timeline-sm">
                            {% for visit in recent_visits %}
                                <div class="timeline-item">
                                    <div class="timeline-marker"></div>
                                    <div class="timeline-content">
                                        <p class="mb-0"><strong>{{ visit.date|date('d/m/Y') }}</strong></p>
                                        <p class="mb-0 text-muted small">{{ visit.reason }}</p>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <p class="text-muted mb-0">{{ __('patients.no_visits_yet') }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Form (hidden) -->
<form id="deleteForm" method="POST" action="{{ base_url }}/patients/{{ patient.id }}" style="display: none;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
</form>

<script>
// Bootstrap validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

function confirmDelete() {
    if (confirm('{{ __("patients.delete_confirm") }}')) {
        document.getElementById('deleteForm').submit();
    }
}
</script>
{% endblock %}