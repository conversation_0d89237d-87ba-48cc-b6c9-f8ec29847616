#!/bin/bash

# Fit360 AdminDesk - Database Backup Shell Script
# Simple wrapper for the PHP backup script

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Change to project directory
cd "$PROJECT_DIR"

# Default options
COMPRESS="--compress"
RETENTION="--retention=7"
OUTPUT_DIR="--output-dir=storage/backups"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --no-compress)
            COMPRESS="--no-compress"
            shift
            ;;
        --retention=*)
            RETENTION="$1"
            shift
            ;;
        --output-dir=*)
            OUTPUT_DIR="$1"
            shift
            ;;
        --list)
            php scripts/database-backup.php --list
            exit 0
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --no-compress       Don't compress backup files"
            echo "  --retention=N       Keep backups for N days (default: 7)"
            echo "  --output-dir=DIR    Output directory (default: storage/backups)"
            echo "  --list              List existing backups"
            echo "  --help              Show this help message"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in PATH"
    exit 1
fi

# Check if the backup script exists
if [[ ! -f "scripts/database-backup.php" ]]; then
    echo "Error: Backup script not found at scripts/database-backup.php"
    exit 1
fi

# Run the backup
echo "Starting database backup..."
php scripts/database-backup.php $COMPRESS $RETENTION $OUTPUT_DIR

if [[ $? -eq 0 ]]; then
    echo "Backup completed successfully!"
else
    echo "Backup failed!"
    exit 1
fi
