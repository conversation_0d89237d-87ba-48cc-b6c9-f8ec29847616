<?php

namespace Tests\Phase4;

use PHPUnit\Framework\TestCase;
use App\Models\CatalogItem;
use App\Models\CatalogCategory;
use App\Models\ConfigVatRate;
use App\Models\StockMovement;
use App\Helpers\MoneyHelper;
use Flight;

class CatalogItemTest extends TestCase
{
    protected static $db;
    protected $testItems = [];
    protected $testCategory;
    protected $testVatRate;
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        self::$db->exec("DELETE FROM stock_movements WHERE item_id IN (SELECT id FROM catalog_items WHERE code LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM catalog_items WHERE code LIKE 'TEST-%'");
        self::$db->exec("DELETE FROM catalog_categories WHERE code LIKE 'TEST-%'");
    }
    
    public function setUp(): void
    {
        // Create test category
        $this->testCategory = CatalogCategory::create([
            'code' => 'TEST-CAT-01',
            'name' => json_encode(['en' => 'Test Category', 'fr' => 'Catégorie Test']),
            'parent_id' => null,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        // Get or create test VAT rate
        $this->testVatRate = ConfigVatRate::where('is_default', '=', 1)->first();
        if (!$this->testVatRate) {
            $this->testVatRate = ConfigVatRate::create([
                'code' => 'TEST-VAT',
                'name' => json_encode(['en' => 'Test VAT']),
                'rate' => 17.00,
                'is_default' => 1,
                'is_active' => 1
            ]);
        }
    }
    
    public function testCreateCatalogItem()
    {
        $item = CatalogItem::create([
            'code' => 'TEST-ITEM-001',
            'name' => 'Test Product',
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'description' => 'Test product description',
            'unit_price' => 99.99,
            'vat_rate_id' => $this->testVatRate->id,
            'is_stockable' => 1,
            'current_stock' => 100,
            'low_stock_alert' => 10,
            'quick_sale_button' => 0,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->assertNotNull($item);
        $this->assertNotNull($item->id);
        $this->assertEquals('TEST-ITEM-001', $item->code);
        $this->assertEquals(99.99, $item->unit_price);
        $this->assertEquals(100, $item->current_stock);
        
        $this->testItems[] = $item->id;
        
        return $item;
    }
    
    public function testGetCategory()
    {
        // Create test item
        $item = CatalogItem::create([
            'code' => 'TEST-CAT-ITEM',
            'name' => 'Test Category Item',
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'unit_price' => 50.00,
            'vat_rate_id' => $this->testVatRate->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testItems[] = $item->id;
        
        $category = $item->getCategory();
        $this->assertNotNull($category);
        $this->assertEquals($this->testCategory->id, $category->id);
        $this->assertEquals('TEST-CAT-01', $category->code);
    }
    
    public function testGetVatRate()
    {
        // Create test item
        $item = CatalogItem::create([
            'code' => 'TEST-VAT-ITEM',
            'name' => 'Test VAT Item',
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'unit_price' => 60.00,
            'vat_rate_id' => $this->testVatRate->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testItems[] = $item->id;
        
        $vatRate = $item->getVatRate();
        $this->assertNotNull($vatRate);
        $this->assertEquals($this->testVatRate->id, $vatRate->id);
        $this->assertEquals(17.00, $vatRate->rate);
    }
    
    public function testPriceCalculations()
    {
        // Create test item
        $item = CatalogItem::create([
            'code' => 'TEST-PRICE-ITEM',
            'name' => 'Test Price Item',
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'unit_price' => 99.99,
            'vat_rate_id' => $this->testVatRate->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testItems[] = $item->id;
        
        // Test price with VAT using MoneyHelper
        $priceWithVat = $item->getPriceWithVat();
        $expectedVat = MoneyHelper::calculateTax(99.99, 17, false); // 17.00
        $expectedPrice = MoneyHelper::round(99.99 + $expectedVat); // 116.99
        $this->assertEquals($expectedPrice, $priceWithVat);
        
        // Test VAT amount
        $vatAmount = $item->getVatAmount();
        $this->assertEquals($expectedVat, $vatAmount);
        
        // Test formatted price
        $formattedPrice = $item->formatPrice();
        $this->assertStringContainsString('99.99', $formattedPrice);
        $this->assertStringContainsString('€', $formattedPrice);
        
        $formattedPriceWithVat = $item->formatPrice(true);
        $this->assertStringContainsString('116.99', $formattedPriceWithVat);
    }
    
    public function testStockManagement()
    {
        // Create test item with stock
        $item = CatalogItem::create([
            'code' => 'TEST-STOCK-MGMT',
            'name' => 'Test Stock Management',
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'unit_price' => 75.00,
            'vat_rate_id' => $this->testVatRate->id,
            'is_stockable' => 1,
            'current_stock' => 100,
            'low_stock_alert' => 10,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testItems[] = $item->id;
        
        // Test stock status
        $this->assertTrue($item->isInStock());
        $this->assertFalse($item->hasLowStock());
        $this->assertEquals('in_stock', $item->getStockStatus());
        $this->assertEquals('In Stock', $item->getStockStatusLabel());
        $this->assertEquals('success', $item->getStockStatusColor());
        
        // Test stock adjustment - decrease
        $movement = $item->adjustStock(-95, 'sale', null, 1);
        $this->assertNotNull($movement);
        $this->assertEquals(-95, $movement->quantity);
        $this->assertEquals(5, $item->current_stock);
        
        // Test low stock
        $this->assertTrue($item->hasLowStock());
        $this->assertEquals('low_stock', $item->getStockStatus());
        $this->assertEquals('warning', $item->getStockStatusColor());
        
        // Test out of stock
        $item->adjustStock(-5, 'sale');
        $this->assertEquals(0, $item->current_stock);
        $this->assertFalse($item->isInStock());
        $this->assertEquals('out_of_stock', $item->getStockStatus());
        $this->assertEquals('danger', $item->getStockStatusColor());
        
        // Test stock increase
        $item->adjustStock(50, 'purchase');
        $this->assertEquals(50, $item->current_stock);
        $this->assertTrue($item->isInStock());
    }
    
    public function testNonStockableItem()
    {
        $service = CatalogItem::create([
            'code' => 'TEST-SERVICE-001',
            'name' => 'Test Service',
            'category_id' => $this->testCategory->id,
            'item_type' => 'service',
            'unit_price' => 150.00,
            'vat_rate_id' => $this->testVatRate->id,
            'is_stockable' => 0,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testItems[] = $service->id;
        
        // Non-stockable items should always be "in stock"
        $this->assertTrue($service->isInStock());
        $this->assertFalse($service->hasLowStock());
        $this->assertEquals('not_applicable', $service->getStockStatus());
        $this->assertEquals('N/A', $service->getStockStatusLabel());
        
        // Stock adjustment should fail for non-stockable items
        $result = $service->adjustStock(10);
        $this->assertFalse($result);
    }
    
    public function testQuickSaleItems()
    {
        // Create quick sale items
        $quickItems = [];
        for ($i = 1; $i <= 3; $i++) {
            $item = CatalogItem::create([
                'code' => "TEST-QUICK-{$i}",
                'name' => "Quick Sale Item {$i}",
                'category_id' => $this->testCategory->id,
                'item_type' => 'product',
                'unit_price' => 10.00 * $i,
                'vat_rate_id' => $this->testVatRate->id,
                'quick_sale_button' => 1,
                'button_color' => '#' . dechex(rand(0x000000, 0xFFFFFF)),
                'button_order' => $i,
                'is_active' => 1,
                'created_by' => 1
            ]);
            
            $quickItems[] = $item;
            $this->testItems[] = $item->id;
        }
        
        // Test getting quick sale items
        $foundQuickItems = CatalogItem::getQuickSaleItems();
        
        // Filter to only our test items
        $testQuickItems = [];
        foreach ($foundQuickItems as $item) {
            if (strpos($item->code, 'TEST-QUICK-') === 0) {
                $testQuickItems[] = $item;
            }
        }
        
        $this->assertCount(3, $testQuickItems);
        
        // Verify order
        $previousOrder = 0;
        foreach ($testQuickItems as $item) {
            $this->assertGreaterThan($previousOrder, $item->button_order);
            $previousOrder = $item->button_order;
        }
    }
    
    public function testSearchItems()
    {
        // Create items for search
        $searchItems = [
            ['code' => 'TEST-SEARCH-001', 'name' => 'Widget Pro', 'description' => 'Professional widget'],
            ['code' => 'TEST-SEARCH-002', 'name' => 'Gadget Plus', 'description' => 'Advanced gadget'],
            ['code' => 'TEST-SEARCH-003', 'name' => 'Tool Master', 'description' => 'Master tool widget']
        ];
        
        foreach ($searchItems as $itemData) {
            $item = CatalogItem::create(array_merge($itemData, [
                'category_id' => $this->testCategory->id,
                'item_type' => 'product',
                'unit_price' => 50.00,
                'vat_rate_id' => $this->testVatRate->id,
                'is_active' => 1,
                'created_by' => 1
            ]));
            
            $this->testItems[] = $item->id;
        }
        
        // Test search by code
        $results = CatalogItem::search('SEARCH-001');
        $this->assertCount(1, $results);
        $this->assertEquals('TEST-SEARCH-001', $results[0]->code);
        
        // Test search by name
        $results = CatalogItem::search('Widget');
        $this->assertGreaterThanOrEqual(1, count($results));
        
        // Test search by description
        $results = CatalogItem::search('widget');
        $this->assertGreaterThanOrEqual(2, count($results)); // Should find both items with 'widget' in name or description
    }
    
    public function testGenerateCode()
    {
        // Test default prefix
        $code1 = CatalogItem::generateCode();
        $this->assertStringStartsWith('ITEM', $code1);
        
        // Test custom prefix
        $code2 = CatalogItem::generateCode('PROD');
        $this->assertStringStartsWith('PROD', $code2);
        
        // Create an item with the generated code
        $item = CatalogItem::create([
            'code' => $code2,
            'name' => 'Generated Code Item',
            'category_id' => $this->testCategory->id,
            'item_type' => 'product',
            'unit_price' => 25.00,
            'vat_rate_id' => $this->testVatRate->id,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testItems[] = $item->id;
        
        // Generate next code - should increment
        $code3 = CatalogItem::generateCode('PROD');
        $this->assertNotEquals($code2, $code3);
        $this->assertGreaterThan($code2, $code3);
    }
    
    public function testGetLowStockItems()
    {
        // Create items with various stock levels
        $stockItems = [
            ['code' => 'TEST-STOCK-LOW', 'current_stock' => 5, 'low_stock_alert' => 10],
            ['code' => 'TEST-STOCK-OUT', 'current_stock' => 0, 'low_stock_alert' => 5],
            ['code' => 'TEST-STOCK-GOOD', 'current_stock' => 50, 'low_stock_alert' => 10]
        ];
        
        foreach ($stockItems as $itemData) {
            $item = CatalogItem::create(array_merge($itemData, [
                'name' => $itemData['code'],
                'category_id' => $this->testCategory->id,
                'item_type' => 'product',
                'unit_price' => 30.00,
                'vat_rate_id' => $this->testVatRate->id,
                'is_stockable' => 1,
                'is_active' => 1,
                'created_by' => 1
            ]));
            
            $this->testItems[] = $item->id;
        }
        
        // Get low stock items
        $lowStockItems = CatalogItem::getLowStockItems();
        
        // Filter to only our test items
        $testLowStock = array_filter($lowStockItems, function($item) {
            return strpos($item->code, 'TEST-STOCK-') === 0;
        });
        
        $this->assertCount(2, $testLowStock); // Should find LOW and OUT items
        
        // Verify they are sorted by stock level (ascending)
        $stockLevels = array_map(function($item) { return $item->current_stock; }, $testLowStock);
        $sortedLevels = $stockLevels;
        sort($sortedLevels);
        $this->assertEquals($sortedLevels, $stockLevels);
    }
    
    public function tearDown(): void
    {
        // Clean up test items
        if (!empty($this->testItems)) {
            self::$db->exec("DELETE FROM stock_movements WHERE item_id IN (" . implode(',', $this->testItems) . ")");
            self::$db->exec("DELETE FROM catalog_items WHERE id IN (" . implode(',', $this->testItems) . ")");
        }
        
        // Clean up test category
        if ($this->testCategory) {
            self::$db->exec("DELETE FROM catalog_categories WHERE id = " . $this->testCategory->id);
        }
        
        // Clean up test VAT rate if created
        if ($this->testVatRate && $this->testVatRate->code === 'TEST-VAT') {
            self::$db->exec("DELETE FROM config_vat_rates WHERE id = " . $this->testVatRate->id);
        }
    }
    
    public static function tearDownAfterClass(): void
    {
        // Final cleanup
        self::$db->exec("DELETE FROM stock_movements WHERE item_id IN (SELECT id FROM catalog_items WHERE code LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM catalog_items WHERE code LIKE 'TEST-%'");
        self::$db->exec("DELETE FROM catalog_categories WHERE code LIKE 'TEST-%'");
    }
}