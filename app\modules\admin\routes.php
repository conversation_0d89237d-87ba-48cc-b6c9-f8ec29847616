<?php

// Admin routes
Flight::route('GET /admin', ['App\Controllers\AdminController', 'index']);
Flight::route('GET /admin/menu-config', ['App\Controllers\AdminController', 'menuConfig']);
Flight::route('POST /admin/menu-config/save', ['App\Controllers\AdminController', 'saveMenuConfig']);

// Permission management routes
Flight::route('GET /admin/permissions', ['App\Controllers\PermissionController', 'index']);
Flight::route('GET /admin/permissions/group', ['App\Controllers\PermissionController', 'getGroupPermissions']);
Flight::route('GET /admin/permissions/template', ['App\Controllers\PermissionController', 'getTemplateData']);
Flight::route('POST /admin/permissions/update', ['App\Controllers\PermissionController', 'updatePermissions']);
Flight::route('POST /admin/permissions/copy', ['App\Controllers\PermissionController', 'copyPermissions']);
Flight::route('POST /admin/permissions/template', ['App\Controllers\PermissionController', 'loadTemplate']);
Flight::route('GET /admin/permissions/audit', ['App\Controllers\PermissionController', 'auditLog']);

// Error log routes
Flight::route('GET /admin/errors', ['App\Controllers\ErrorLogController', 'index']);
Flight::route('GET /admin/errors/@id:[0-9]+', ['App\Controllers\ErrorLogController', 'view']);
Flight::route('POST /admin/errors/@id:[0-9]+/resolve', ['App\Controllers\ErrorLogController', 'resolve']);
Flight::route('POST /admin/errors/cleanup', ['App\Controllers\ErrorLogController', 'cleanup']);
Flight::route('GET /admin/errors/export', ['App\Controllers\ErrorLogController', 'export']);

// Email automation routes
Flight::route('GET /admin/email-automation', ['App\Controllers\EmailAutomationController', 'index']);
Flight::route('POST /admin/email-automation/save', ['App\Controllers\EmailAutomationController', 'save']);
Flight::route('POST /admin/email-automation/test', ['App\Controllers\EmailAutomationController', 'test']);
Flight::route('GET /admin/email-automation/preview/@id:[0-9]+', ['App\Controllers\EmailAutomationController', 'preview']);