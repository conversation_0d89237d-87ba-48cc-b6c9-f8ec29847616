<?php
// Set up invoice sequence for next invoice to be 0186

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Setting Up Invoice Sequence</h2>";
    
    // Get the invoice document type
    $stmt = $db->query("SELECT * FROM document_types WHERE code = 'invoice'");
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$docType) {
        echo "<strong>Error:</strong> Invoice document type not found!<br>";
        echo "Let me check what document types exist:<br>";
        
        $stmt = $db->query("SELECT * FROM document_types");
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>ID</th><th>Code</th><th>Name</th><th>Prefix</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . $row['code'] . "</td>";
            echo "<td>" . $row['name'] . "</td>";
            echo "<td>" . $row['prefix'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Try to find the most likely invoice type
        $stmt = $db->query("SELECT * FROM document_types WHERE code LIKE '%inv%' OR prefix = 'FAC' LIMIT 1");
        $docType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($docType) {
            echo "<br>Found possible invoice type: " . $docType['code'] . " (ID: " . $docType['id'] . ")<br>";
        } else {
            exit("Cannot proceed without invoice document type.");
        }
    }
    
    echo "Using document type: <strong>" . $docType['code'] . "</strong> (ID: " . $docType['id'] . ")<br>";
    echo "Prefix: <strong>" . $docType['prefix'] . "</strong><br>";
    echo "Counter type: <strong>" . $docType['counter_type'] . "</strong><br><br>";
    
    // Check LOY invoice type
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE prefix = 'LOY' OR name LIKE '%loyer%' OR name LIKE '%rental%'");
    $loyType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($loyType) {
        echo "Found LOY invoice type: <strong>" . $loyType['name'] . "</strong> (ID: " . $loyType['id'] . ", Prefix: " . $loyType['prefix'] . ")<br><br>";
    } else {
        echo "<strong>Warning:</strong> No LOY invoice type found. You may need to update the invoice type prefixes.<br><br>";
    }
    
    // Handle based on counter type
    $year = date('Y');
    $month = date('n');
    
    if ($docType['counter_type'] === 'yearly') {
        // Check current sequence for 2025
        $stmt = $db->prepare("
            SELECT * FROM document_sequences 
            WHERE document_type_id = ? AND year = 2025
        ");
        $stmt->execute([$docType['id']]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($sequence) {
            echo "Current sequence for 2025: <strong>" . $sequence['last_number'] . "</strong><br>";
            echo "Next invoice will be: <strong>" . ($sequence['last_number'] + 1) . "</strong><br><br>";
            
            // Update to 185 so next will be 186
            if ($sequence['last_number'] >= 186) {
                echo "⚠️ Sequence is already at or past 186. Current: " . $sequence['last_number'] . "<br>";
                echo "The next invoice will be: " . ($sequence['last_number'] + 1) . "<br>";
            } else {
                $stmt = $db->prepare("
                    UPDATE document_sequences 
                    SET last_number = 185 
                    WHERE document_type_id = ? AND year = 2025
                ");
                $stmt->execute([$docType['id']]);
                echo "✓ Updated sequence to 185. Next invoice will be <strong>0186</strong><br>";
            }
        } else {
            // Create new sequence
            $stmt = $db->prepare("
                INSERT INTO document_sequences (document_type_id, year, month, last_number) 
                VALUES (?, 2025, NULL, 185)
            ");
            $stmt->execute([$docType['id']]);
            echo "✓ Created sequence for 2025. Next invoice will be <strong>0186</strong><br>";
        }
    } elseif ($docType['counter_type'] === 'monthly') {
        // For monthly counters
        $stmt = $db->prepare("
            SELECT * FROM document_sequences 
            WHERE document_type_id = ? AND year = ? AND month = ?
        ");
        $stmt->execute([$docType['id'], $year, $month]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($sequence) {
            echo "Current sequence for $year-$month: <strong>" . $sequence['last_number'] . "</strong><br>";
            // Update as needed
        } else {
            // Create new sequence
            $stmt = $db->prepare("
                INSERT INTO document_sequences (document_type_id, year, month, last_number) 
                VALUES (?, ?, ?, 185)
            ");
            $stmt->execute([$docType['id'], $year, $month]);
            echo "✓ Created sequence for $year-$month. Next invoice will be <strong>0186</strong><br>";
        }
    } else {
        // Global counter
        $stmt = $db->prepare("
            SELECT * FROM document_sequences 
            WHERE document_type_id = ? AND year IS NULL AND month IS NULL
        ");
        $stmt->execute([$docType['id']]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($sequence) {
            echo "Current global sequence: <strong>" . $sequence['last_number'] . "</strong><br>";
        }
    }
    
    // Show what the next invoice number will look like
    echo "<h3>Next Invoice Preview</h3>";
    echo "<p>When you create a rental invoice (LOY type), the number will be:</p>";
    echo "<h2 style='color: #28a745;'>FAC-LOY-2025-0186</h2>";
    
    echo "<h3>Important Notes</h3>";
    echo "<ul>";
    echo "<li>Make sure to select the correct invoice type when creating invoices</li>";
    echo "<li>The LOY prefix will only appear for rental invoices</li>";
    echo "<li>The system will now reuse deleted invoice numbers</li>";
    echo "</ul>";
    
    echo "<br><a href='test_invoice_creation.php' style='font-size: 16px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>Test Invoice Creation</a>";
    echo "<a href='check_invoice_sequences.php' style='font-size: 16px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Back to Sequence Check</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>