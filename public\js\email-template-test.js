/**
 * Email Template Test Module
 * Handles invoice data loading, preview generation, and test email sending
 */

(function() {
    'use strict';

    window.EmailTemplateTest = {
        // Test data
        testInvoices: [],
        selectedInvoice: null,
        currentTemplate: null,
        
        // Initialize test module
        init: function() {
            this.bindEvents();
            this.loadTestInvoices();
        },

        // Bind event handlers
        bindEvents: function() {
            // Test email button
            const testBtn = document.getElementById('sendTestEmailBtn');
            if (testBtn) {
                testBtn.addEventListener('click', () => this.showTestDialog());
            }

            // Invoice selector
            const invoiceSelect = document.getElementById('testInvoiceSelect');
            if (invoiceSelect) {
                invoiceSelect.addEventListener('change', (e) => {
                    this.loadInvoiceData(e.target.value);
                });
            }

            // Preview update button
            const updatePreviewBtn = document.getElementById('updatePreviewBtn');
            if (updatePreviewBtn) {
                updatePreviewBtn.addEventListener('click', () => this.generatePreview());
            }

            // Send test button in dialog
            document.addEventListener('click', (e) => {
                if (e.target.matches('#confirmSendTest')) {
                    this.sendTestEmail();
                }
            });
        },

        // Load test invoices
        loadTestInvoices: function() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/invoices/recent?limit=20', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.testInvoices = JSON.parse(xhr.responseText);
                    this.renderInvoiceSelector();
                }
            };
            
            xhr.send();
        },

        // Render invoice selector
        renderInvoiceSelector: function() {
            const select = document.getElementById('testInvoiceSelect');
            if (!select) return;
            
            select.innerHTML = '<option value="">Select an invoice for testing...</option>' +
                this.testInvoices.map(invoice => `
                    <option value="${invoice.id}">
                        ${invoice.invoice_number} - ${invoice.patient_name} - ${invoice.total_amount}
                    </option>
                `).join('');
        },

        // Load specific invoice data
        loadInvoiceData: function(invoiceId) {
            if (!invoiceId) {
                this.selectedInvoice = null;
                this.clearPreview();
                return;
            }
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', `/api/invoices/${invoiceId}`, true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.selectedInvoice = JSON.parse(xhr.responseText);
                    this.displayInvoiceDetails();
                    this.generatePreview();
                }
            };
            
            xhr.send();
        },

        // Display invoice details
        displayInvoiceDetails: function() {
            const detailsContainer = document.getElementById('invoiceDetails');
            if (!detailsContainer || !this.selectedInvoice) return;
            
            detailsContainer.innerHTML = `
                <div class="invoice-details-card">
                    <h4>Invoice Details</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>Invoice Number:</label>
                            <span>${this.escapeHtml(this.selectedInvoice.invoice_number)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Patient:</label>
                            <span>${this.escapeHtml(this.selectedInvoice.patient_name)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Email:</label>
                            <span>${this.escapeHtml(this.selectedInvoice.patient_email || 'No email')}</span>
                        </div>
                        <div class="detail-item">
                            <label>Total Amount:</label>
                            <span>${this.escapeHtml(this.selectedInvoice.total_amount)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Due Date:</label>
                            <span>${this.formatDate(this.selectedInvoice.due_date)}</span>
                        </div>
                        <div class="detail-item">
                            <label>Status:</label>
                            <span class="status-badge status-${this.selectedInvoice.status}">
                                ${this.escapeHtml(this.selectedInvoice.status)}
                            </span>
                        </div>
                    </div>
                </div>
            `;
        },

        // Generate preview with real data
        generatePreview: function() {
            if (!this.selectedInvoice) {
                this.showNotification('Please select an invoice first', 'warning');
                return;
            }
            
            // Get current template content
            const templateId = document.querySelector('.template-item.selected')?.dataset.templateId;
            if (!templateId) {
                this.showNotification('Please select a template first', 'warning');
                return;
            }
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', `/api/email-templates/${templateId}/preview`, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-CSRF-Token', this.getCsrfToken());
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    const preview = JSON.parse(xhr.responseText);
                    this.displayPreview(preview);
                }
            };
            
            xhr.send(JSON.stringify({
                invoice_id: this.selectedInvoice.id,
                test_mode: true
            }));
        },

        // Display preview
        displayPreview: function(preview) {
            const container = document.getElementById('testPreviewContainer');
            if (!container) return;
            
            container.innerHTML = `
                <div class="email-preview">
                    <div class="preview-header">
                        <h4>Email Preview</h4>
                        <div class="preview-meta">
                            <span><strong>To:</strong> ${this.escapeHtml(preview.to)}</span>
                            <span><strong>From:</strong> ${this.escapeHtml(preview.from)}</span>
                        </div>
                    </div>
                    <div class="preview-subject">
                        <label>Subject:</label>
                        <div class="subject-content">${this.escapeHtml(preview.subject)}</div>
                    </div>
                    <div class="preview-body">
                        <label>Body:</label>
                        <div class="body-content">${preview.body_html}</div>
                    </div>
                    ${preview.footer ? `
                        <div class="preview-footer">
                            <label>Footer:</label>
                            <div class="footer-content">${preview.footer}</div>
                        </div>
                    ` : ''}
                    ${preview.attachments && preview.attachments.length > 0 ? `
                        <div class="preview-attachments">
                            <label>Attachments:</label>
                            <ul class="attachment-list">
                                ${preview.attachments.map(att => `
                                    <li><i class="icon-paperclip"></i> ${this.escapeHtml(att.name)}</li>
                                `).join('')}
                            </ul>
                        </div>
                    ` : ''}
                </div>
            `;
            
            // Add variable highlighting
            this.highlightVariables(container);
        },

        // Show test email dialog
        showTestDialog: function() {
            if (!this.selectedInvoice) {
                this.showNotification('Please select an invoice first', 'warning');
                return;
            }
            
            const dialog = document.createElement('div');
            dialog.className = 'modal-backdrop';
            dialog.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>Send Test Email</h3>
                            <button type="button" class="close-btn" onclick="this.closest('.modal-backdrop').remove()">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="form-group">
                                <label>Send test email to:</label>
                                <input type="email" id="testEmailAddress" class="form-control" 
                                       placeholder="<EMAIL>"
                                       value="${this.getDefaultTestEmail()}">
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="includeAttachments" class="form-check-input" checked>
                                <label for="includeAttachments" class="form-check-label">
                                    Include invoice PDF attachment
                                </label>
                            </div>
                            <div class="form-check">
                                <input type="checkbox" id="useRealData" class="form-check-input" checked>
                                <label for="useRealData" class="form-check-label">
                                    Use real invoice data (${this.selectedInvoice.invoice_number})
                                </label>
                            </div>
                            <div class="alert alert-info">
                                <i class="icon-info"></i>
                                This will send a real email using the selected template and invoice data.
                                The email will be marked as a test in the subject line.
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" 
                                    onclick="this.closest('.modal-backdrop').remove()">
                                Cancel
                            </button>
                            <button type="button" class="btn btn-primary" id="confirmSendTest">
                                <i class="icon-send"></i> Send Test Email
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(dialog);
            
            // Focus email input
            setTimeout(() => {
                document.getElementById('testEmailAddress').focus();
            }, 100);
        },

        // Send test email
        sendTestEmail: function() {
            const emailInput = document.getElementById('testEmailAddress');
            const email = emailInput?.value;
            
            if (!email || !this.validateEmail(email)) {
                this.showNotification('Please enter a valid email address', 'error');
                return;
            }
            
            const templateId = document.querySelector('.template-item.selected')?.dataset.templateId;
            if (!templateId) {
                this.showNotification('Please select a template first', 'warning');
                return;
            }
            
            // Show loading state
            const sendBtn = document.getElementById('confirmSendTest');
            const originalText = sendBtn.innerHTML;
            sendBtn.innerHTML = '<i class="icon-spinner"></i> Sending...';
            sendBtn.disabled = true;
            
            const xhr = new XMLHttpRequest();
            xhr.open('POST', `/api/email-templates/${templateId}/send-test`, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-CSRF-Token', this.getCsrfToken());
            
            xhr.onload = () => {
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
                
                if (xhr.status === 200) {
                    this.showNotification('Test email sent successfully!', 'success');
                    document.querySelector('.modal-backdrop')?.remove();
                    
                    // Log the test
                    this.logTestEmail(email, templateId);
                } else {
                    const error = JSON.parse(xhr.responseText);
                    this.showNotification(error.message || 'Failed to send test email', 'error');
                }
            };
            
            xhr.onerror = () => {
                sendBtn.innerHTML = originalText;
                sendBtn.disabled = false;
                this.showNotification('Network error occurred', 'error');
            };
            
            xhr.send(JSON.stringify({
                to_email: email,
                invoice_id: this.selectedInvoice.id,
                include_attachments: document.getElementById('includeAttachments')?.checked,
                use_real_data: document.getElementById('useRealData')?.checked
            }));
        },

        // Log test email
        logTestEmail: function(email, templateId) {
            const logContainer = document.getElementById('testEmailLog');
            if (!logContainer) return;
            
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry';
            logEntry.innerHTML = `
                <div class="log-time">${new Date().toLocaleString()}</div>
                <div class="log-details">
                    Sent test email to <strong>${this.escapeHtml(email)}</strong>
                    using template #${templateId}
                    with invoice ${this.selectedInvoice.invoice_number}
                </div>
            `;
            
            logContainer.insertBefore(logEntry, logContainer.firstChild);
            
            // Keep only last 10 entries
            while (logContainer.children.length > 10) {
                logContainer.removeChild(logContainer.lastChild);
            }
        },

        // Clear preview
        clearPreview: function() {
            const container = document.getElementById('testPreviewContainer');
            if (container) {
                container.innerHTML = `
                    <div class="empty-state">
                        <i class="icon-email-empty"></i>
                        <p>Select an invoice to generate preview</p>
                    </div>
                `;
            }
            
            const details = document.getElementById('invoiceDetails');
            if (details) {
                details.innerHTML = '';
            }
        },

        // Highlight variables in preview
        highlightVariables: function(container) {
            const content = container.innerHTML;
            const highlighted = content.replace(/\{([^}]+)\}/g, '<span class="variable-highlight">{$1}</span>');
            container.innerHTML = highlighted;
        },

        // Get default test email
        getDefaultTestEmail: function() {
            // Try to get user's email from page or return empty
            const userEmail = document.querySelector('meta[name="user-email"]')?.content;
            return userEmail || '';
        },

        // Validate email
        validateEmail: function(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        },

        // Format date
        formatDate: function(dateString) {
            if (!dateString) return 'N/A';
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        },

        // Show notification
        showNotification: function(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <i class="icon-${this.getIconForType(type)}"></i>
                <span>${message}</span>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        },

        // Get icon for notification type
        getIconForType: function(type) {
            const icons = {
                success: 'check-circle',
                error: 'times-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        },

        // Get CSRF token
        getCsrfToken: function() {
            const meta = document.querySelector('meta[name="csrf-token"]');
            return meta ? meta.content : '';
        },

        // Escape HTML
        escapeHtml: function(text) {
            const div = document.createElement('div');
            div.textContent = text || '';
            return div.innerHTML;
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            EmailTemplateTest.init();
        });
    } else {
        EmailTemplateTest.init();
    }
})();