{% extends "base-modern.twig" %}
{% import '_macros/table-helper-v2.twig' as tableHelper %}

{% block title %}{{ __('users.users') }}{% endblock %}

{% block styles %}
{{ parent() }}
<style>
.avatar {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.avatar-text {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('users.users') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/users/groups" class="btn btn-info">
                <i class="bi bi-people me-2"></i>{{ __('users.user_groups') }}
            </a>
            <a href="{{ base_url }}/users/create" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>{{ __('users.add_user') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('users.total_users') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('users.active_users') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.active_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-check text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('users.user_groups') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_groups|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-diagram-3 text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('users.online_now') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.online_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-circle-fill text-success fs-6"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table with Enhanced Table Helper -->
    {% set tableContent %}
        {{ tableHelper.tableHeader([
            { label: __('users.user'), sortable: true },
            { label: __('users.username'), sortable: true },
            { label: __('common.email'), sortable: true },
            { label: __('users.role'), sortable: true },
            { label: __('users.groups'), sortable: false, reorderable: true },
            { label: __('users.last_login'), sortable: true },
            { label: __('common.status'), sortable: true },
            { label: __('common.actions'), width: 100, sortable: false, reorderable: false, isAction: true }
        ], true) }}
        <tbody>
            {% for user in users %}
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input row-checkbox" value="{{ user.id }}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        {% if user.avatar %}
                            <img src="{{ user.avatar starts with base_url ? user.avatar : base_url ~ '/uploads/avatars/' ~ user.avatar }}" 
                                 class="rounded-circle me-2" style="width: 40px; height: 40px;" 
                                 alt="{{ user.name }}">
                        {% else %}
                            <div class="avatar avatar-sm me-2">
                                <span class="avatar-text rounded-circle bg-primary">
                                    {{ user.name|first|upper }}
                                </span>
                            </div>
                        {% endif %}
                        <div>
                            <div class="fw-bold">{{ user.name }}</div>
                            <small class="text-muted">ID: {{ user.id }}</small>
                        </div>
                    </div>
                </td>
                <td>{{ user.username }}</td>
                <td>
                    <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                </td>
                <td>
                    <span class="badge bg-{{ user.role == 'admin' ? 'danger' : (user.role == 'manager' ? 'warning' : 'info') }}">
                        {{ __('users.role_' ~ user.role) }}
                    </span>
                </td>
                <td>
                    {% if user.groups %}
                        {% for group in user.groups %}
                            <span class="badge" style="background-color: {{ group.color }};">
                                <i class="{{ group.icon }} me-1"></i>{{ group.name }}
                            </span>
                        {% endfor %}
                    {% else %}
                        <span class="text-muted">-</span>
                    {% endif %}
                </td>
                <td data-sort="{{ user.last_login ? user.last_login|date('Y-m-d H:i:s') : '0' }}">
                    {% if user.last_login %}
                        {{ user.last_login|date('d/m/Y H:i') }}
                    {% else %}
                        <span class="text-muted">{{ __('users.never') }}</span>
                    {% endif %}
                </td>
                <td>
                    {{ tableHelper.statusBadge(user.is_active ? 'active' : 'inactive', {
                        'active': __('common.active'),
                        'inactive': __('common.inactive')
                    }) }}
                </td>
                <td>
                    <div class="dropdown">
                        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown">
                            <i class="bi bi-three-dots-vertical"></i>
                        </button>
                        <ul class="dropdown-menu">
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/users/{{ user.id }}">
                                    <i class="bi bi-eye me-2"></i>{{ __('common.view') }}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{{ base_url }}/users/{{ user.id }}/edit">
                                    <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
                                </a>
                            </li>
                            {% if user.id != session.user_id %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="toggleUserStatus({{ user.id }}, {{ user.is_active ? 'false' : 'true' }})">
                                    {% if user.is_active %}
                                        <i class="bi bi-x-circle me-2"></i>{{ __('users.deactivate') }}
                                    {% else %}
                                        <i class="bi bi-check-circle me-2"></i>{{ __('users.activate') }}
                                    {% endif %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="resetPassword({{ user.id }})">
                                    <i class="bi bi-key me-2"></i>{{ __('users.reset_password') }}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger" href="#" onclick="deleteUser({{ user.id }})">
                                    <i class="bi bi-trash me-2"></i>{{ __('common.delete') }}
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </td>
            </tr>
            {% else %}
            {{ tableHelper.emptyState(__('users.no_users_found'), 'bi-people', 9) }}
            {% endfor %}
        </tbody>
    {% endset %}

    {{ tableHelper.tableWithFilters({
        tableId: 'usersTable',
        formAction: base_url ~ '/users',
        storageKey: 'users_filters',
        tableContent: tableContent,
        searchColumns: [1, 2, 3], 
        searchPlaceholder: __('users.search_users'),
        sortable: true,
        reorderable: true,
        defaultSort: { column: 1, direction: 'asc' },
        showColumnToggle: true,
        filters: [
            {
                id: 'role',
                name: 'role',
                label: __('users.role'),
                type: 'select',
                width: 3,
                value: filters.role|default(''),
                options: {
                    'admin': __('users.role_admin'),
                    'manager': __('users.role_manager'),
                    'user': __('users.role_user')
                }
            },
            {
                id: 'status',
                name: 'status',
                label: __('common.status'),
                type: 'select',
                width: 3,
                value: filters.status|default(''),
                options: {
                    'active': __('common.active'),
                    'inactive': __('common.inactive')
                }
            },
            {
                id: 'group_id',
                name: 'group_id',
                label: __('users.group'),
                type: 'select',
                width: 3,
                value: filters.group_id|default(''),
                options: groups|default({})
            }
        ],
        filterConfigs: [
            { id: 'role' },
            { id: 'status' },
            { id: 'group_id' }
        ],
        showImport: true,
        importUrl: base_url ~ '/users/import',
        showExport: true,
        exportUrl: base_url ~ '/users/export',
        exportFormats: ['csv', 'excel'],
        showBulkActions: true,
        bulkActions: [
            {
                action: 'activate',
                label: __('users.activate_selected'),
                icon: 'bi bi-check-circle',
                url: base_url ~ '/users/bulk-activate'
            },
            {
                action: 'deactivate',
                label: __('users.deactivate_selected'),
                icon: 'bi bi-x-circle',
                url: base_url ~ '/users/bulk-deactivate'
            },
            {
                action: 'assign_group',
                label: __('users.assign_to_group'),
                icon: 'bi bi-people',
                url: base_url ~ '/users/bulk-assign-group',
                divider: true
            },
            {
                action: 'export',
                label: __('users.export_selected'),
                icon: 'bi bi-download',
                url: base_url ~ '/users/bulk-export'
            },
            {
                action: 'delete',
                label: __('users.delete_selected'),
                icon: 'bi bi-trash',
                class: 'text-danger',
                url: base_url ~ '/users/bulk-delete',
                confirm: true,
                confirmMessage: __('users.bulk_delete_confirm')
            }
        ],
        bulkActionConfigs: [
            {
                action: 'activate',
                url: base_url ~ '/users/bulk-activate'
            },
            {
                action: 'deactivate',
                url: base_url ~ '/users/bulk-deactivate'
            },
            {
                action: 'assign_group',
                url: base_url ~ '/users/bulk-assign-group'
            },
            {
                action: 'export',
                url: base_url ~ '/users/bulk-export'
            },
            {
                action: 'delete',
                url: base_url ~ '/users/bulk-delete',
                confirm: true,
                confirmMessage: __('users.bulk_delete_confirm')
            }
        ],
        bulkActionUrl: base_url ~ '/users/bulk-action',
        pagination: {
            current_page: current_page,
            total_pages: total_pages,
            base_url: base_url ~ '/users'
        },
        resetUrl: base_url ~ '/users?reset_filters=1'
    }) }}
</div>

<script>
function toggleUserStatus(id, activate) {
    const action = activate ? '{{ __("users.activate_confirm") }}' : '{{ __("users.deactivate_confirm") }}';
    if (confirm(action)) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/users/' + id + '/toggle-status';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function resetPassword(id) {
    if (confirm('{{ __("users.reset_password_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/users/' + id + '/reset-password';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function deleteUser(id) {
    if (confirm('{{ __("users.delete_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/users/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %}

{% block table_helper %}
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
{% endblock %}