<?php
// Simple PDF test
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

// Just output the visible columns for invoice 238
$invoiceId = 238;
$db = Flight::db();

// Get invoice
$stmt = $db->prepare("SELECT document_type_id FROM invoices WHERE id = ?");
$stmt->execute([$invoiceId]);
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

$documentTypeId = $invoice['document_type_id'] ?? null;
$visibleColumns = [];

if ($documentTypeId) {
    $visibleColumns = \App\Models\DocumentTypeColumnConfig::getVisibleInvoiceColumns($documentTypeId);
}

// Default columns if no configuration
if (empty($visibleColumns)) {
    $visibleColumns = [
        'description' => ['visible' => true, 'order' => 1],
        'quantity' => ['visible' => true, 'order' => 2],
        'unit_price' => ['visible' => true, 'order' => 3],
        'vat_rate' => ['visible' => true, 'order' => 4],
        'total' => ['visible' => true, 'order' => 5]
    ];
}

header('Content-Type: text/plain');
echo "Visible columns for invoice $invoiceId (document_type_id: " . ($documentTypeId ?? 'NULL') . "):\n\n";

// Sort by order
uasort($visibleColumns, function($a, $b) {
    return ($a['order'] ?? 999) - ($b['order'] ?? 999);
});

$i = 1;
foreach ($visibleColumns as $col => $config) {
    echo "$i. $col (order: " . ($config['order'] ?? 'default') . ")\n";
    $i++;
}

// Now show a sample row
echo "\nSample row output order:\n";
$sampleData = [
    'description' => 'Loyer mensuel',
    'quantity' => '1.00',
    'unit_price' => '720,00€',
    'vat_rate' => '0.00%',
    'total' => '720,00€'
];

$output = [];
foreach ($visibleColumns as $col => $config) {
    $output[] = $sampleData[$col] ?? '?';
}
echo implode(' | ', $output);