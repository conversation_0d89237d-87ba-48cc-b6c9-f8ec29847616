<?php

namespace Tests\Phase3;

use PDO;
use Exception;

class Task_3_2_1_VATRatesTest
{
    private $db;
    private $passed = 0;
    private $failed = 0;
    
    public function setUp(): void
    {
        try {
            // Database connection
            $this->db = new PDO('mysql:host=localhost;dbname=fitapp', 'root', 'test1234');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (Exception $e) {
            die("Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    private function assertEquals($expected, $actual, $message = '')
    {
        if ($expected == $actual) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: $expected, Got: $actual)");
        }
    }
    
    private function assertTrue($condition, $message = '')
    {
        if ($condition) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: true, Got: false)");
        }
    }
    
    private function assertContains($needle, $haystack, $message = '')
    {
        if (in_array($needle, $haystack)) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ('$needle' not found in array)");
        }
    }
    
    private function assertGreaterThan($expected, $actual, $message = '')
    {
        if ($actual > $expected) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ($actual not greater than $expected)");
        }
    }
    
    /**
     * Test 1: Check VAT tables exist
     */
    public function testVATTablesExist()
    {
        // Check config_vat_rates table
        $stmt = $this->db->query("SHOW TABLES LIKE 'config_vat_rates'");
        $this->assertEquals(1, $stmt->rowCount(), "config_vat_rates table should exist");
        
        // Check vat_rates table
        $stmt = $this->db->query("SHOW TABLES LIKE 'vat_rates'");
        $this->assertEquals(1, $stmt->rowCount(), "vat_rates table should exist");
        
        // Check template_vat_configs table
        $stmt = $this->db->query("SHOW TABLES LIKE 'template_vat_configs'");
        $this->assertEquals(1, $stmt->rowCount(), "template_vat_configs table should exist");
        
        echo "✓ All VAT-related tables exist\n";
    }
    
    /**
     * Test 2: Check config_vat_rates structure with temporal validity
     */
    public function testConfigVatRatesStructure()
    {
        $stmt = $this->db->query("SHOW COLUMNS FROM config_vat_rates");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        // Check required columns including temporal validity
        $requiredColumns = ['id', 'code', 'name', 'rate', 'description', 
                          'is_default', 'is_active', 'effective_from', 'effective_to'];
        
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in config_vat_rates");
        }
        
        // Check for JSON column (name)
        $stmt = $this->db->query("SHOW COLUMNS FROM config_vat_rates WHERE Field = 'name'");
        $nameColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertTrue(stripos($nameColumn['Type'], 'json') !== false, "Name column should be JSON type");
        
        // Check indexes
        $stmt = $this->db->query("SHOW INDEX FROM config_vat_rates WHERE Key_name = 'idx_effective_dates'");
        $hasIndex = $stmt->rowCount() > 0;
        echo "✓ config_vat_rates structure is correct" . ($hasIndex ? " (with date index)" : "") . "\n";
    }
    
    /**
     * Test 3: Check Luxembourg standard VAT rates
     */
    public function testLuxembourgVATRates()
    {
        $stmt = $this->db->query("SELECT code, rate FROM config_vat_rates WHERE is_active = 1");
        $rates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $ratesByCode = [];
        foreach ($rates as $rate) {
            $ratesByCode[$rate['code']] = $rate['rate'];
        }
        
        // Check standard Luxembourg rate (17%)
        $this->assertTrue(isset($ratesByCode['TVA17']), "Luxembourg standard VAT rate (17%) should exist");
        if (isset($ratesByCode['TVA17'])) {
            $this->assertEquals(17.00, $ratesByCode['TVA17'], "Standard rate should be 17%");
        }
        
        // Check zero rate
        $hasZeroRate = false;
        foreach ($ratesByCode as $code => $rate) {
            if ($rate == 0.00) {
                $hasZeroRate = true;
                break;
            }
        }
        $this->assertTrue($hasZeroRate, "Zero VAT rate should exist");
        
        echo "✓ Luxembourg VAT rates are configured correctly\n";
        echo "  Found rates: " . implode(', ', array_map(function($k, $v) { 
            return "$k ($v%)"; 
        }, array_keys($ratesByCode), array_values($ratesByCode))) . "\n";
    }
    
    /**
     * Test 4: Test VAT rate retrieval by date
     */
    public function testVATRateByDate()
    {
        // Get a rate that has effective_from date
        $stmt = $this->db->prepare("
            SELECT * FROM config_vat_rates 
            WHERE code = 'TVA17' 
            AND is_active = 1
            AND (effective_from IS NULL OR effective_from <= ?)
            AND (effective_to IS NULL OR effective_to >= ?)
            LIMIT 1
        ");
        
        $testDate = '2025-01-15';
        $stmt->execute([$testDate, $testDate]);
        $rate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertTrue($rate !== false, "Should find VAT rate for date $testDate");
        if ($rate) {
            $this->assertEquals(17.00, $rate['rate'], "Rate should be 17% for standard VAT");
        }
        
        echo "✓ VAT rate retrieval by date works correctly\n";
    }
    
    /**
     * Test 5: Test creating a new VAT rate
     */
    public function testCreateVATRate()
    {
        // Create a test VAT rate
        $sql = "INSERT INTO config_vat_rates (code, name, rate, description, is_active, effective_from) 
                VALUES (:code, :name, :rate, :description, :is_active, :effective_from)";
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            ':code' => 'TEST_VAT',
            ':name' => json_encode(['en' => 'Test VAT', 'fr' => 'TVA Test']),
            ':rate' => 15.00,
            ':description' => 'Test VAT rate',
            ':is_active' => 1,
            ':effective_from' => date('Y-m-d')
        ]);
        
        $this->assertTrue($result, "Test VAT rate should be created");
        
        // Verify creation
        $stmt = $this->db->prepare("SELECT * FROM config_vat_rates WHERE code = ?");
        $stmt->execute(['TEST_VAT']);
        $testRate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('TEST_VAT', $testRate['code']);
        $this->assertEquals(15.00, $testRate['rate']);
        
        echo "✓ VAT rate creation works correctly\n";
        
        // Clean up
        $this->db->exec("DELETE FROM config_vat_rates WHERE code = 'TEST_VAT'");
    }
    
    /**
     * Test 6: Test temporal validity (historical rates)
     */
    public function testTemporalValidity()
    {
        // Create a future rate change
        $sql = "INSERT INTO config_vat_rates 
                (code, name, rate, description, is_active, effective_from, effective_to) 
                VALUES 
                (:code, :name, :rate, :description, :is_active, :effective_from, :effective_to)";
        
        $stmt = $this->db->prepare($sql);
        
        // Historical rate
        $stmt->execute([
            ':code' => 'HIST_TEST',
            ':name' => json_encode(['en' => 'Historical Test']),
            ':rate' => 16.00,
            ':description' => 'Historical rate',
            ':is_active' => 1,
            ':effective_from' => '2020-01-01',
            ':effective_to' => '2023-12-31'
        ]);
        
        // Current rate
        $stmt->execute([
            ':code' => 'HIST_TEST',
            ':name' => json_encode(['en' => 'Current Test']),
            ':rate' => 18.00,
            ':description' => 'Current rate',
            ':is_active' => 1,
            ':effective_from' => '2024-01-01',
            ':effective_to' => null
        ]);
        
        // Test retrieval for different dates
        $stmt = $this->db->prepare("
            SELECT rate FROM config_vat_rates 
            WHERE code = 'HIST_TEST' 
            AND effective_from <= :date 
            AND (effective_to IS NULL OR effective_to >= :date)
            ORDER BY effective_from DESC
            LIMIT 1
        ");
        
        // Test historical date
        $stmt->execute([':date' => '2022-06-15']);
        $histRate = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertEquals(16.00, $histRate['rate'], "Historical rate should be 16%");
        
        // Test current date
        $stmt->execute([':date' => '2025-01-15']);
        $currRate = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertEquals(18.00, $currRate['rate'], "Current rate should be 18%");
        
        echo "✓ Temporal validity (historical rates) works correctly\n";
        
        // Clean up
        $this->db->exec("DELETE FROM config_vat_rates WHERE code = 'HIST_TEST'");
    }
    
    /**
     * Test 7: Test template VAT configurations
     */
    public function testTemplateVATConfigs()
    {
        // Check if template_vat_configs has proper structure
        $stmt = $this->db->query("SHOW COLUMNS FROM template_vat_configs");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = ['id', 'template_id', 'vat_rate_id'];
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in template_vat_configs");
        }
        
        echo "✓ Template VAT configurations table structure is correct\n";
    }
    
    /**
     * Run all tests and provide summary
     */
    public function runAllTests()
    {
        echo "\n=== Task 3.2.1: VAT Rates Tables Tests ===\n\n";
        
        $tests = [
            'testVATTablesExist' => 'Checking VAT tables existence',
            'testConfigVatRatesStructure' => 'Checking config_vat_rates structure',
            'testLuxembourgVATRates' => 'Checking Luxembourg standard VAT rates',
            'testVATRateByDate' => 'Testing VAT rate retrieval by date',
            'testCreateVATRate' => 'Testing VAT rate creation',
            'testTemporalValidity' => 'Testing temporal validity (historical rates)',
            'testTemplateVATConfigs' => 'Testing template VAT configurations'
        ];
        
        foreach ($tests as $method => $description) {
            echo "\n$description...\n";
            try {
                $this->$method();
            } catch (Exception $e) {
                echo "❌ FAILED: " . $e->getMessage() . "\n";
                continue;
            }
        }
        
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "Test Results: {$this->passed} passed, {$this->failed} failed\n";
        
        if ($this->failed === 0) {
            echo "\n✅ ALL TESTS PASSED for Task 3.2.1\n";
            echo "\nKey features verified:\n";
            echo "- VAT tables with temporal validity support\n";
            echo "- Luxembourg standard rates (17%, 0%)\n";
            echo "- Multilingual support with JSON columns\n";
            echo "- Date-based rate retrieval\n";
            echo "- Historical rate tracking\n";
            echo "- Template VAT configurations\n";
        } else {
            echo "\n❌ SOME TESTS FAILED\n";
            echo "Please check the error messages above\n";
        }
        
        return $this->failed === 0;
    }
}

// Run tests if executed directly
if (php_sapi_name() === 'cli' && basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    $test = new Task_3_2_1_VATRatesTest();
    $test->setUp();
    $test->runAllTests();
}