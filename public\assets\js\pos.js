// POS System JavaScript
const POS = {
    cart: [],
    selectedCustomer: null,
    paymentMethods: new Set(),
    
    init() {
        this.bindEvents();
        this.loadSavedCart();
        this.updateCartDisplay();
    },
    
    bindEvents() {
        // Product search
        document.getElementById('productSearch').addEventListener('input', (e) => {
            this.searchProducts(e.target.value);
        });
        
        // Category tabs
        document.querySelectorAll('#categoryTabs .nav-link').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                this.filterByCategory(e.target.dataset.category);
                
                // Update active tab
                document.querySelectorAll('#categoryTabs .nav-link').forEach(t => t.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
        
        // Quick sale buttons
        document.querySelectorAll('.quick-sale-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                this.addItemToCart({
                    id: btn.dataset.itemId,
                    name: btn.dataset.itemName,
                    price: parseFloat(btn.dataset.itemPrice),
                    vat_rate: parseFloat(btn.dataset.itemVat),
                    current_stock: parseInt(btn.dataset.itemStock),
                    is_stockable: btn.dataset.itemStockable === '1'
                });
            });
        });
        
        // Customer search
        let customerSearchTimeout;
        document.getElementById('customerSearch').addEventListener('input', (e) => {
            clearTimeout(customerSearchTimeout);
            customerSearchTimeout = setTimeout(() => {
                this.searchCustomers(e.target.value);
            }, 300);
        });
        
        // Clear cart
        document.getElementById('clearCartBtn').addEventListener('click', () => {
            if (confirm('Clear all items from cart?')) {
                this.clearCart();
            }
        });
        
        // Remove customer
        document.getElementById('removeCustomerBtn').addEventListener('click', () => {
            this.selectedCustomer = null;
            document.getElementById('selectedCustomer').style.display = 'none';
            document.getElementById('customerSearch').value = '';
        });
        
        // Payment method selection
        document.querySelectorAll('input[name="payment_method"]').forEach(input => {
            input.addEventListener('change', () => {
                this.updatePaymentMethods();
            });
        });
        
        // Complete payment
        document.getElementById('completePaymentBtn').addEventListener('click', () => {
            this.completeSale();
        });
        
        // Edit item modal
        document.getElementById('saveItemBtn').addEventListener('click', () => {
            this.saveItemChanges();
        });
    },
    
    searchProducts(term) {
        if (term.length < 2) {
            document.getElementById('productList').style.display = 'none';
            document.getElementById('quickSaleButtons').style.display = 'flex';
            return;
        }
        
        fetch(`${window.POS_BASE_URL || ''}/pos/search-products?term=${encodeURIComponent(term)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.displaySearchResults(data.items);
                }
            });
    },
    
    displaySearchResults(items) {
        const productList = document.getElementById('productList');
        productList.innerHTML = '';
        
        if (items.length === 0) {
            productList.innerHTML = '<div class="text-center text-muted p-3">No products found</div>';
        } else {
            items.forEach(item => {
                const stockBadge = item.is_stockable && item.current_stock <= 0 
                    ? '<span class="badge bg-danger">Out of stock</span>' 
                    : '';
                
                const div = document.createElement('div');
                div.className = 'list-group-item list-group-item-action';
                div.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">${item.code} - ${item.category || 'Uncategorized'}</small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">€${item.price.toFixed(2)}</div>
                            ${stockBadge}
                        </div>
                    </div>
                `;
                
                div.addEventListener('click', () => {
                    this.addItemToCart(item);
                    document.getElementById('productSearch').value = '';
                    this.searchProducts('');
                });
                
                productList.appendChild(div);
            });
        }
        
        document.getElementById('quickSaleButtons').style.display = 'none';
        productList.style.display = 'block';
    },
    
    filterByCategory(categoryId) {
        if (categoryId === 'all') {
            document.querySelectorAll('.quick-sale-btn').forEach(btn => {
                btn.parentElement.style.display = 'block';
            });
        } else {
            // This would require category data on buttons
            // For now, just show all
            document.querySelectorAll('.quick-sale-btn').forEach(btn => {
                btn.parentElement.style.display = 'block';
            });
        }
    },
    
    addItemToCart(item) {
        // Check stock
        if (item.is_stockable && item.current_stock <= 0) {
            alert('This item is out of stock');
            return;
        }
        
        // Check if item already in cart
        const existingItem = this.cart.find(cartItem => cartItem.id === item.id);
        
        if (existingItem) {
            // Check stock for quantity increase
            if (item.is_stockable && existingItem.quantity >= item.current_stock) {
                alert(`Only ${item.current_stock} units available`);
                return;
            }
            existingItem.quantity++;
        } else {
            this.cart.push({
                id: item.id,
                name: item.name,
                price: item.price,
                vat_rate: item.vat_rate || 0,
                quantity: 1,
                discount: 0,
                is_stockable: item.is_stockable,
                current_stock: item.current_stock
            });
        }
        
        this.updateCartDisplay();
        this.saveCart();
    },
    
    updateCartDisplay() {
        const cartItems = document.getElementById('cartItems');
        const cartEmpty = document.getElementById('cartEmpty');
        
        if (this.cart.length === 0) {
            cartItems.style.display = 'none';
            cartEmpty.style.display = 'block';
            document.getElementById('completePaymentBtn').disabled = true;
        } else {
            cartItems.style.display = 'block';
            cartEmpty.style.display = 'none';
            
            cartItems.innerHTML = '';
            let subtotal = 0;
            let totalVat = 0;
            
            this.cart.forEach((item, index) => {
                const lineTotal = item.price * item.quantity * (1 - item.discount / 100);
                const vatAmount = lineTotal * (item.vat_rate / 100);
                subtotal += lineTotal;
                totalVat += vatAmount;
                
                const div = document.createElement('div');
                div.className = 'list-group-item cart-item';
                div.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="flex-grow-1" onclick="POS.editItem(${index})">
                            <h6 class="mb-1">${item.name}</h6>
                            <small class="text-muted">
                                ${item.quantity} × €${item.price.toFixed(2)}
                                ${item.discount > 0 ? `(-${item.discount}%)` : ''}
                            </small>
                        </div>
                        <div class="text-end">
                            <div class="fw-bold">€${lineTotal.toFixed(2)}</div>
                            <button class="btn btn-sm btn-link text-danger p-0" onclick="POS.removeItem(${index})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
                
                cartItems.appendChild(div);
            });
            
            // Update totals
            document.getElementById('cartSubtotal').textContent = `€${subtotal.toFixed(2)}`;
            document.getElementById('cartVat').textContent = `€${totalVat.toFixed(2)}`;
            document.getElementById('cartTotal').textContent = `€${(subtotal + totalVat).toFixed(2)}`;
            
            // Enable complete button if payment method selected
            this.updatePaymentMethods();
        }
    },
    
    editItem(index) {
        const item = this.cart[index];
        document.getElementById('editItemIndex').value = index;
        document.getElementById('editItemQuantity').value = item.quantity;
        document.getElementById('editItemPrice').value = item.price;
        document.getElementById('editItemDiscount').value = item.discount;
        
        const modal = new bootstrap.Modal(document.getElementById('editItemModal'));
        modal.show();
    },
    
    saveItemChanges() {
        const index = parseInt(document.getElementById('editItemIndex').value);
        const quantity = parseFloat(document.getElementById('editItemQuantity').value);
        const price = parseFloat(document.getElementById('editItemPrice').value);
        const discount = parseFloat(document.getElementById('editItemDiscount').value);
        
        if (quantity <= 0) {
            this.removeItem(index);
        } else {
            const item = this.cart[index];
            
            // Check stock
            if (item.is_stockable && quantity > item.current_stock) {
                alert(`Only ${item.current_stock} units available`);
                return;
            }
            
            item.quantity = quantity;
            item.price = price;
            item.discount = discount;
            
            this.updateCartDisplay();
            this.saveCart();
        }
        
        bootstrap.Modal.getInstance(document.getElementById('editItemModal')).hide();
    },
    
    removeItem(index) {
        this.cart.splice(index, 1);
        this.updateCartDisplay();
        this.saveCart();
    },
    
    clearCart() {
        this.cart = [];
        this.updateCartDisplay();
        this.saveCart();
    },
    
    searchCustomers(term) {
        if (term.length < 2) return;
        
        fetch(`${window.POS_BASE_URL || ''}/pos/search-customers?term=${encodeURIComponent(term)}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.displayCustomerResults(data.customers);
                }
            });
    },
    
    displayCustomerResults(customers) {
        // Create dropdown with results
        const input = document.getElementById('customerSearch');
        const rect = input.getBoundingClientRect();
        
        // Remove existing dropdown
        const existingDropdown = document.getElementById('customerDropdown');
        if (existingDropdown) existingDropdown.remove();
        
        if (customers.length === 0) return;
        
        const dropdown = document.createElement('div');
        dropdown.id = 'customerDropdown';
        dropdown.className = 'list-group position-absolute shadow';
        dropdown.style.cssText = `
            top: ${rect.bottom + window.scrollY}px;
            left: ${rect.left}px;
            width: ${rect.width}px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1050;
        `;
        
        customers.forEach(customer => {
            const item = document.createElement('a');
            item.href = '#';
            item.className = 'list-group-item list-group-item-action';
            item.innerHTML = `
                <div class="d-flex justify-content-between">
                    <div>
                        <strong>${customer.name}</strong><br>
                        <small class="text-muted">${customer.email || ''} ${customer.phone || ''}</small>
                    </div>
                </div>
            `;
            
            item.addEventListener('click', (e) => {
                e.preventDefault();
                this.selectCustomer(customer);
                dropdown.remove();
            });
            
            dropdown.appendChild(item);
        });
        
        document.body.appendChild(dropdown);
        
        // Close dropdown on outside click
        setTimeout(() => {
            document.addEventListener('click', function closeDropdown(e) {
                if (!dropdown.contains(e.target) && e.target !== input) {
                    dropdown.remove();
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }, 100);
    },
    
    selectCustomer(customer) {
        this.selectedCustomer = customer;
        document.getElementById('customerSearch').value = '';
        document.getElementById('customerName').textContent = customer.name;
        document.getElementById('selectedCustomer').style.display = 'block';
    },
    
    updatePaymentMethods() {
        const selectedMethods = document.querySelectorAll('input[name="payment_method"]:checked');
        const paymentAmounts = document.getElementById('paymentAmounts');
        const completeBtn = document.getElementById('completePaymentBtn');
        
        if (selectedMethods.length === 0) {
            completeBtn.disabled = true;
            paymentAmounts.style.display = 'none';
            return;
        }
        
        completeBtn.disabled = this.cart.length === 0;
        
        if (selectedMethods.length > 1) {
            // Show amount inputs for split payment
            const total = this.getCartTotal();
            paymentAmounts.innerHTML = '<label class="form-label">Split Payment Amounts:</label>';
            
            selectedMethods.forEach((method, index) => {
                const label = method.nextElementSibling.textContent.trim();
                const div = document.createElement('div');
                div.className = 'input-group mb-2';
                div.innerHTML = `
                    <span class="input-group-text">${label}</span>
                    <input type="number" class="form-control payment-amount-input" 
                           data-method-id="${method.value}"
                           min="0" step="0.01" 
                           value="${index === 0 ? total.toFixed(2) : '0.00'}">
                    <span class="input-group-text">€</span>
                `;
                paymentAmounts.appendChild(div);
            });
            
            paymentAmounts.style.display = 'block';
        } else {
            paymentAmounts.style.display = 'none';
        }
    },
    
    getCartTotal() {
        let total = 0;
        this.cart.forEach(item => {
            const lineTotal = item.price * item.quantity * (1 - item.discount / 100);
            const vatAmount = lineTotal * (item.vat_rate / 100);
            total += lineTotal + vatAmount;
        });
        return total;
    },
    
    completeSale() {
        if (this.cart.length === 0) {
            alert('Cart is empty');
            return;
        }
        
        const selectedMethods = document.querySelectorAll('input[name="payment_method"]:checked');
        if (selectedMethods.length === 0) {
            alert('Please select a payment method');
            return;
        }
        
        // Prepare sale data
        const saleData = {
            items: this.cart.map(item => ({
                item_id: item.id,
                description: item.name,
                quantity: item.quantity,
                unit_price: item.price,
                vat_rate: item.vat_rate,
                discount: item.discount
            })),
            payments: [],
            client_id: this.selectedCustomer?.id || null,
            client_name: this.selectedCustomer ? null : document.getElementById('customerSearch').value || 'Walk-in Customer'
        };
        
        // Get payment amounts
        const total = this.getCartTotal();
        
        if (selectedMethods.length === 1) {
            saleData.payments.push({
                method_id: selectedMethods[0].value,
                amount: total
            });
        } else {
            // Split payment
            let totalPaid = 0;
            document.querySelectorAll('.payment-amount-input').forEach(input => {
                const amount = parseFloat(input.value) || 0;
                if (amount > 0) {
                    saleData.payments.push({
                        method_id: input.dataset.methodId,
                        amount: amount
                    });
                    totalPaid += amount;
                }
            });
            
            if (Math.abs(totalPaid - total) > 0.01) {
                alert(`Payment amounts (€${totalPaid.toFixed(2)}) do not match total (€${total.toFixed(2)})`);
                return;
            }
        }
        
        // Disable button to prevent double submission
        const btn = document.getElementById('completePaymentBtn');
        btn.disabled = true;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        
        // Submit sale
        fetch(`${window.POS_BASE_URL || ''}/pos/process-sale`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify(saleData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Clear cart
                this.clearCart();
                this.selectedCustomer = null;
                document.getElementById('selectedCustomer').style.display = 'none';
                
                // Reset payment methods
                document.querySelectorAll('input[name="payment_method"]').forEach(input => {
                    input.checked = false;
                });
                
                // Show success and redirect to receipt
                alert('Sale completed successfully!');
                window.location.href = data.redirect;
            } else {
                alert(data.message || 'Error processing sale');
            }
        })
        .catch(error => {
            alert('Error: ' + error.message);
        })
        .finally(() => {
            btn.disabled = false;
            btn.innerHTML = '<i class="fas fa-cash-register"></i> Complete Sale';
        });
    },
    
    saveCart() {
        localStorage.setItem('pos_cart', JSON.stringify(this.cart));
    },
    
    loadSavedCart() {
        const saved = localStorage.getItem('pos_cart');
        if (saved) {
            try {
                this.cart = JSON.parse(saved);
            } catch (e) {
                this.cart = [];
            }
        }
    }
};

// Initialize POS when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    POS.init();
});