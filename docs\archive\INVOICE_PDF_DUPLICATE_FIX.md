# Invoice PDF Duplicate Entries Fix

## Problem
The invoice PDF was showing duplicate entries for invoice lines. This was caused by the PDF generation code loading invoice data twice:
1. First through the Invoice model's `getInvoiceWithDetails()` method
2. Then through a separate direct database query

## Root Cause
In `public/invoice-pdf.php`:
- The code was making its own database queries instead of using the centralized Invoice model
- This resulted in loading invoice lines twice, causing duplicates in the PDF

## Solution Implemented

### 1. Modified `public/invoice-pdf.php`
- Replaced direct database queries with the Invoice model's `getInvoiceWithDetails()` method
- Removed the duplicate query for invoice lines
- Now uses `$invoice['lines']` from the model data instead of a separate query

### Key Changes:
```php
// OLD: Direct database query
$stmt = $db->prepare("SELECT * FROM invoices WHERE id = :id");
// Plus another query for invoice_lines

// NEW: Using the model
$invoiceModel = new \App\Models\Invoice();
$invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);
$items = $invoice['lines'] ?? [];
```

## Testing
1. Use `test_invoice_pdf_fix.php?id=XXX` to verify the fix
2. Use `clear_invoice_cache.php?id=XXX` to clear cache if needed
3. Generate PDF and verify no duplicate entries appear

## Benefits
- Eliminates duplicate entries in PDFs
- Ensures consistency between web view and PDF view
- Reduces database queries
- Uses centralized data loading logic
- Maintains data integrity

## Files Modified
- `/public/invoice-pdf.php` - Main fix to use model instead of duplicate queries

## Files Created for Testing
- `/public/test_invoice_pdf_fix.php` - Test script to verify the fix
- `/public/clear_invoice_cache.php` - Utility to clear invoice cache