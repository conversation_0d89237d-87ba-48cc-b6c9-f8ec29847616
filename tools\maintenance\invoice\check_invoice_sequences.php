<?php
// Check current invoice numbers and sequences

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Invoice Number Analysis</h2>";
    
    // 1. Check recent invoices
    echo "<h3>Recent Invoices (Last 20)</h3>";
    $stmt = $db->query("
        SELECT i.id, i.invoice_number, i.type_id, i.document_type_id, i.issue_date, i.status,
               it.name as invoice_type_name, it.prefix as invoice_type_prefix,
               dt.code as doc_type_code, dt.prefix as doc_type_prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        LEFT JOIN document_types dt ON i.document_type_id = dt.id
        ORDER BY i.id DESC LIMIT 20
    ");
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Invoice Number</th><th>Type</th><th>Prefix</th><th>Doc Type</th><th>Issue Date</th><th>Status</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td><strong>" . $row['invoice_number'] . "</strong></td>";
        echo "<td>" . ($row['invoice_type_name'] ?? 'N/A') . "</td>";
        echo "<td>" . ($row['invoice_type_prefix'] ?? 'N/A') . "</td>";
        echo "<td>" . ($row['doc_type_code'] ?? 'N/A') . "</td>";
        echo "<td>" . $row['issue_date'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Check highest invoice numbers by pattern
    echo "<h3>Highest Invoice Numbers by Pattern (2025)</h3>";
    $stmt = $db->query("
        SELECT 
            SUBSTRING_INDEX(invoice_number, '-', 2) as prefix_pattern,
            MAX(CAST(SUBSTRING_INDEX(invoice_number, '-', -1) AS UNSIGNED)) as highest_number,
            COUNT(*) as count
        FROM invoices 
        WHERE invoice_number LIKE '%2025%'
        GROUP BY prefix_pattern
        ORDER BY highest_number DESC
    ");
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Pattern</th><th>Highest Number</th><th>Count</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['prefix_pattern'] . "</td>";
        echo "<td>" . str_pad($row['highest_number'], 4, '0', STR_PAD_LEFT) . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 3. Check document sequences
    echo "<h3>Document Sequences</h3>";
    $stmt = $db->query("
        SELECT ds.*, dt.code, dt.prefix, dt.counter_type
        FROM document_sequences ds
        JOIN document_types dt ON ds.document_type_id = dt.id
        ORDER BY ds.document_type_id, ds.year DESC, ds.month DESC
    ");
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Doc Type</th><th>Code</th><th>Prefix</th><th>Counter Type</th><th>Year</th><th>Month</th><th>Last Number</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['document_type_id'] . "</td>";
        echo "<td>" . $row['code'] . "</td>";
        echo "<td>" . $row['prefix'] . "</td>";
        echo "<td>" . $row['counter_type'] . "</td>";
        echo "<td>" . ($row['year'] ?? 'N/A') . "</td>";
        echo "<td>" . ($row['month'] ?? 'N/A') . "</td>";
        echo "<td><strong>" . $row['last_number'] . "</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 4. Check invoice types
    echo "<h3>Invoice Types Configuration</h3>";
    $stmt = $db->query("SELECT * FROM config_invoice_types ORDER BY id");
    
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Name</th><th>Prefix</th><th>Display Name</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td><strong>" . ($row['prefix'] ?? 'N/A') . "</strong></td>";
        echo "<td>" . ($row['display_name'] ?? $row['name']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 5. Check deleted invoice numbers
    echo "<h3>Deleted Invoice Numbers Pool</h3>";
    $stmt = $db->query("
        SELECT * FROM deleted_invoice_numbers 
        WHERE reused_at IS NULL 
        ORDER BY document_type_id, year DESC, sequence_number ASC
        LIMIT 20
    ");
    
    if ($stmt->rowCount() > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Invoice Number</th><th>Sequence</th><th>Year</th><th>Deleted At</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['invoice_number'] . "</td>";
            echo "<td>" . $row['sequence_number'] . "</td>";
            echo "<td>" . ($row['year'] ?? 'N/A') . "</td>";
            echo "<td>" . $row['deleted_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No deleted invoice numbers available for reuse.</p>";
    }
    
    // 6. Recommendations
    echo "<h3>Recommendations</h3>";
    echo "<ul>";
    echo "<li>The next invoice number should be based on the document sequences table</li>";
    echo "<li>Make sure invoice types have proper prefixes configured</li>";
    echo "<li>The LOY prefix should be associated with rental invoices</li>";
    echo "</ul>";
    
    echo "<br><a href='setup_invoice_sequence.php' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Set Up Sequence for Next Invoice</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>