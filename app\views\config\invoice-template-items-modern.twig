{% extends "base-modern.twig" %}

{% block title %}{{ title }} - {{ template.name }}{% endblock %}

{% block styles %}
<style>
    .line-item-row {
        transition: background-color 0.2s;
    }
    
    .line-item-row:hover {
        background-color: rgba(0,0,0,0.02);
    }
    
    .drag-handle {
        cursor: move;
        color: #6c757d;
    }
    
    .drag-handle:hover {
        color: #495057;
    }
    
    .line-items-container {
        min-height: 200px;
    }
    
    .sortable-ghost {
        opacity: 0.4;
        background-color: #e9ecef;
    }
    
    .formula-help {
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
            <p class="text-muted mb-0">{{ template.name }} - {{ invoice_types[template.invoice_type]|default(template.invoice_type) }}</p>
        </div>
        <a href="{{ base_url }}/config/invoice-templates" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
        </a>
    </div>

    <!-- Template Info Card -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>{{ __('config.template_code') }}:</strong> <code>{{ template.code }}</code>
                </div>
                <div class="col-md-3">
                    <strong>{{ __('invoices.invoice_type') }}:</strong> 
                    <span class="badge bg-secondary">{{ invoice_types[template.invoice_type]|default(template.invoice_type) }}</span>
                </div>
                <div class="col-md-3">
                    <strong>{{ __('config.owner_type') }}:</strong> 
                    <span class="badge bg-{{ template.owner_type == 'system' ? 'primary' : (template.owner_type == 'group' ? 'info' : 'secondary') }}">
                        {{ __('config.owner_type_' ~ template.owner_type) }}
                    </span>
                </div>
                <div class="col-md-3">
                    <strong>{{ __('common.status') }}:</strong> 
                    <span class="badge bg-{{ template.is_active ? 'success' : 'danger' }}">
                        {{ template.is_active ? __('common.active') : __('common.inactive') }}
                    </span>
                </div>
            </div>
        </div>
    </div>

    <!-- Line Items -->
    <div class="card shadow-sm">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">{{ __('config.template_line_items') }}</h5>
            <button type="button" class="btn btn-primary btn-sm" onclick="addLineItem()">
                <i class="bi bi-plus-circle me-1"></i>{{ __('config.add_line_item') }}
            </button>
        </div>
        <div class="card-body">
            <form id="lineItemsForm" method="POST" action="{{ base_url }}/config/invoice-templates/{{ template.id }}/items">
                {{ csrf_field()|raw }}
                
                <!-- Line Items Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 40px;"></th>
                                <th>{{ __('config.line_type') }}</th>
                                <th>{{ __('common.description') }}</th>
                                <th style="width: 100px;">{{ __('common.quantity') }}</th>
                                <th style="width: 120px;">{{ __('products.unit_price') }}</th>
                                <th style="width: 150px;">{{ __('invoices.vat_rate') }}</th>
                                <th>{{ __('config.calculation_formula') }}</th>
                                <th class="text-center" style="width: 100px;">{{ __('config.mandatory') }}</th>
                                <th style="width: 80px;">{{ __('common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody id="lineItemsContainer" class="line-items-container">
                            {% for item in template.line_items|default([]) %}
                            <tr class="line-item-row" data-id="{{ item.id }}">
                                <td class="text-center">
                                    <i class="bi bi-grip-vertical drag-handle"></i>
                                    <input type="hidden" name="items[{{ loop.index0 }}][id]" value="{{ item.id }}">
                                    <input type="hidden" name="items[{{ loop.index0 }}][sort_order]" value="{{ item.sort_order }}" class="sort-order">
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" name="items[{{ loop.index0 }}][line_type]" required>
                                        {% for key, label in line_types %}
                                        <option value="{{ key }}" {{ item.line_type == key ? 'selected' : '' }}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" 
                                           name="items[{{ loop.index0 }}][description]" 
                                           value="{{ item.description }}" 
                                           required>
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" 
                                           name="items[{{ loop.index0 }}][default_quantity]" 
                                           value="{{ item.default_quantity }}" 
                                           step="0.01" min="0">
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm" 
                                           name="items[{{ loop.index0 }}][default_unit_price]" 
                                           value="{{ item.default_unit_price }}" 
                                           step="0.01" min="0">
                                </td>
                                <td>
                                    <select class="form-select form-select-sm" name="items[{{ loop.index0 }}][vat_rate_id]">
                                        <option value="">{{ __('common.none') }}</option>
                                        {% for rate in vatRates %}
                                        <option value="{{ rate.id }}" {{ item.vat_rate_id == rate.id ? 'selected' : '' }}>
                                            {{ rate.rate }}% - {{ rate.description|default(rate.code) }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm" 
                                           name="items[{{ loop.index0 }}][calculation_formula]" 
                                           value="{{ item.calculation_formula }}"
                                           placeholder="{{ __('config.formula_placeholder') }}">
                                </td>
                                <td class="text-center">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" 
                                               name="items[{{ loop.index0 }}][is_mandatory]" 
                                               value="1" 
                                               {{ item.is_mandatory ? 'checked' : '' }}>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLineItem(this)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                {% if template.line_items is empty %}
                <div class="text-center py-4 text-muted" id="emptyMessage">
                    <i class="bi bi-list-ul fs-1"></i>
                    <p class="mt-2">{{ __('config.no_line_items') }}</p>
                </div>
                {% endif %}

                <!-- Formula Help -->
                <div class="alert alert-info mt-3">
                    <h6 class="alert-heading">{{ __('config.formula_help_title') }}</h6>
                    <p class="mb-2 formula-help">{{ __('config.formula_help_text') }}</p>
                    <ul class="mb-0 formula-help">
                        <li><code>{quantity}</code> - {{ __('config.formula_quantity') }}</li>
                        <li><code>{unit_price}</code> - {{ __('config.formula_unit_price') }}</li>
                        <li><code>{subtotal}</code> - {{ __('config.formula_subtotal') }}</li>
                        <li><code>{vat_amount}</code> - {{ __('config.formula_vat_amount') }}</li>
                        <li>{{ __('config.formula_example') }}: <code>{subtotal} * 0.3</code></li>
                    </ul>
                </div>

                <!-- Save Button -->
                <div class="d-flex justify-content-end mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-2"></i>{{ __('common.save_changes') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Line Item Template -->
<template id="lineItemTemplate">
    <tr class="line-item-row">
        <td class="text-center">
            <i class="bi bi-grip-vertical drag-handle"></i>
            <input type="hidden" name="items[INDEX][id]" value="">
            <input type="hidden" name="items[INDEX][sort_order]" value="0" class="sort-order">
        </td>
        <td>
            <select class="form-select form-select-sm" name="items[INDEX][line_type]" required>
                {% for key, label in line_types %}
                <option value="{{ key }}">{{ label }}</option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" 
                   name="items[INDEX][description]" 
                   required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" 
                   name="items[INDEX][default_quantity]" 
                   value="1" 
                   step="0.01" min="0">
        </td>
        <td>
            <input type="number" class="form-control form-control-sm" 
                   name="items[INDEX][default_unit_price]" 
                   value="0" 
                   step="0.01" min="0">
        </td>
        <td>
            <select class="form-select form-select-sm" name="items[INDEX][vat_rate_id]">
                <option value="">{{ __('common.none') }}</option>
                {% for rate in vatRates %}
                <option value="{{ rate.id }}">
                    {{ rate.rate }}% - {{ rate.description|default(rate.code) }}
                </option>
                {% endfor %}
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm" 
                   name="items[INDEX][calculation_formula]" 
                   placeholder="{{ __('config.formula_placeholder') }}">
        </td>
        <td class="text-center">
            <div class="form-check">
                <input type="checkbox" class="form-check-input" 
                       name="items[INDEX][is_mandatory]" 
                       value="1">
            </div>
        </td>
        <td class="text-center">
            <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeLineItem(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    </tr>
</template>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
let itemIndex = {{ template.line_items|length|default(0) }};
let sortable;

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sortable
    const container = document.getElementById('lineItemsContainer');
    if (container) {
        sortable = Sortable.create(container, {
            handle: '.drag-handle',
            animation: 150,
            ghostClass: 'sortable-ghost',
            onEnd: function(evt) {
                updateSortOrder();
            }
        });
    }
    
    // Form submission
    document.getElementById('lineItemsForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateSortOrder();
        
        // Show loading
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}';
        
        // Submit form data
        const formData = new FormData(this);
        
        fetch(this.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message || '{{ __("common.saved_successfully") }}');
                // Optionally reload to show updated data
                setTimeout(() => location.reload(), 1500);
            } else {
                toastr.error(data.message || '{{ __("common.error_occurred") }}');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('{{ __("common.error_occurred") }}');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        });
    });
});

function addLineItem() {
    const template = document.getElementById('lineItemTemplate');
    const clone = template.content.cloneNode(true);
    
    // Replace INDEX with actual index
    const html = clone.querySelector('tr').outerHTML.replace(/INDEX/g, itemIndex);
    
    // Remove empty message if exists
    const emptyMessage = document.getElementById('emptyMessage');
    if (emptyMessage) {
        emptyMessage.remove();
    }
    
    // Add to container
    const container = document.getElementById('lineItemsContainer');
    container.insertAdjacentHTML('beforeend', html);
    
    itemIndex++;
    updateSortOrder();
}

function removeLineItem(button) {
    if (confirm('{{ __("common.delete_confirm") }}')) {
        const row = button.closest('tr');
        row.remove();
        
        // Reindex remaining items
        reindexItems();
        
        // Show empty message if no items
        const container = document.getElementById('lineItemsContainer');
        if (container.children.length === 0) {
            const emptyHtml = `
                <div class="text-center py-4 text-muted" id="emptyMessage">
                    <i class="bi bi-list-ul fs-1"></i>
                    <p class="mt-2">{{ __('config.no_line_items') }}</p>
                </div>
            `;
            container.closest('.table-responsive').insertAdjacentHTML('afterend', emptyHtml);
        }
    }
}

function reindexItems() {
    const rows = document.querySelectorAll('.line-item-row');
    rows.forEach((row, index) => {
        // Update all input names with new index
        row.querySelectorAll('input, select').forEach(input => {
            if (input.name) {
                input.name = input.name.replace(/\[(\d+)\]/, '[' + index + ']');
            }
        });
    });
    itemIndex = rows.length;
}

function updateSortOrder() {
    const rows = document.querySelectorAll('.line-item-row');
    rows.forEach((row, index) => {
        const sortInput = row.querySelector('.sort-order');
        if (sortInput) {
            sortInput.value = index;
        }
    });
}
</script>
{% endblock %}