<?php
// Start session
session_start();

// Check authentication
if (!isset($_SESSION['user_id'])) {
    die("Unauthorized access. Please login first.");
}

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin']) {
    $isAdmin = true;
} elseif (isset($_SESSION['user_groups'])) {
    if (is_array($_SESSION['user_groups'])) {
        foreach ($_SESSION['user_groups'] as $group) {
            if (strtolower($group['name']) === 'administrators') {
                $isAdmin = true;
                break;
            }
        }
    } elseif ($_SESSION['user_groups'] === 'Administrators') {
        $isAdmin = true;
    }
}

if (!$isAdmin) {
    die("Only administrators can run this script.");
}

// Load bootstrap
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Error Logs Table - Fit360 AdminDesk</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 800px; margin-top: 50px; }
        .card { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .log-output { background-color: #1e1e1e; color: #d4d4d4; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; max-height: 400px; overflow-y: auto; }
        .success { color: #4ec9b0; }
        .error { color: #f48771; }
        .info { color: #569cd6; }
        .warning { color: #dcdcaa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Create Error Logs Table</h4>
            </div>
            <div class="card-body">
                <p>This script will create the <code>error_logs</code> table with proper compatibility.</p>
                
                <div class="log-output">
                    <?php
                    try {
                        echo '<span class="info">[INFO] Starting table creation...</span><br>';
                        
                        // Get database connection
                        $db = Flight::db();
                        echo '<span class="success">[✓] Database connection established</span><br>';
                        
                        // Drop existing table if requested
                        if (isset($_GET['force']) && $_GET['force'] === 'true') {
                            echo '<span class="warning">[!] Force mode: Dropping existing table...</span><br>';
                            try {
                                $db->exec("DROP TABLE IF EXISTS error_logs");
                                echo '<span class="success">[✓] Existing table dropped</span><br>';
                            } catch (Exception $e) {
                                echo '<span class="error">[✗] Failed to drop table: ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                            }
                        }
                        
                        // Check if table exists
                        $tableExists = false;
                        try {
                            $result = $db->query("SHOW TABLES LIKE 'error_logs'");
                            $tableExists = $result->rowCount() > 0;
                        } catch (Exception $e) {
                            // Ignore
                        }
                        
                        if ($tableExists && !isset($_GET['force'])) {
                            echo '<span class="warning">[!] Table already exists. Add ?force=true to recreate it.</span><br>';
                        } else {
                            // Create table without foreign key first
                            echo '<span class="info">[INFO] Creating error_logs table...</span><br>';
                            
                            $createSQL = "CREATE TABLE error_logs (
                                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                                error_id VARCHAR(50) NOT NULL,
                                level VARCHAR(20) DEFAULT 'error',
                                message TEXT NOT NULL,
                                file VARCHAR(500),
                                line INT,
                                user_id INT(11),
                                request_method VARCHAR(10),
                                request_uri VARCHAR(500),
                                user_agent VARCHAR(500),
                                ip_address VARCHAR(45),
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                
                                UNIQUE KEY unique_error_id (error_id),
                                INDEX idx_user_id (user_id),
                                INDEX idx_created_at (created_at),
                                INDEX idx_level (level)
                            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                            
                            $db->exec($createSQL);
                            echo '<span class="success">[✓] Table created successfully!</span><br>';
                            
                            // Try to add foreign key separately
                            echo '<span class="info">[INFO] Adding foreign key constraint...</span><br>';
                            try {
                                $db->exec("ALTER TABLE error_logs 
                                          ADD CONSTRAINT fk_error_logs_user 
                                          FOREIGN KEY (user_id) REFERENCES users(id) 
                                          ON DELETE SET NULL");
                                echo '<span class="success">[✓] Foreign key added successfully!</span><br>';
                            } catch (PDOException $e) {
                                echo '<span class="warning">[!] Could not add foreign key: ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                                echo '<span class="info">[INFO] Table will work without foreign key constraint</span><br>';
                            }
                            
                            // Add extended columns
                            echo '<span class="info">[INFO] Adding extended columns...</span><br>';
                            
                            $extendedColumns = [
                                "ALTER TABLE error_logs ADD COLUMN timestamp DATETIME DEFAULT CURRENT_TIMESTAMP AFTER level",
                                "ALTER TABLE error_logs ADD COLUMN environment VARCHAR(50) DEFAULT 'production' AFTER timestamp",
                                "ALTER TABLE error_logs ADD COLUMN code INT DEFAULT 0 AFTER message",
                                "ALTER TABLE error_logs ADD COLUMN class VARCHAR(255) AFTER line",
                                "ALTER TABLE error_logs ADD COLUMN trace TEXT AFTER class",
                                "ALTER TABLE error_logs ADD COLUMN username VARCHAR(100) AFTER user_id",
                                "ALTER TABLE error_logs ADD COLUMN execution_time DECIMAL(10,3) AFTER username",
                                "ALTER TABLE error_logs ADD COLUMN memory_usage DECIMAL(10,2) AFTER execution_time",
                                "ALTER TABLE error_logs ADD COLUMN memory_peak DECIMAL(10,2) AFTER memory_usage",
                                "ALTER TABLE error_logs ADD COLUMN request_headers TEXT AFTER memory_peak",
                                "ALTER TABLE error_logs ADD COLUMN request_params TEXT AFTER request_headers",
                                "ALTER TABLE error_logs ADD COLUMN session_data TEXT AFTER request_params",
                                "ALTER TABLE error_logs ADD COLUMN resolved BOOLEAN DEFAULT FALSE AFTER session_data",
                                "ALTER TABLE error_logs ADD COLUMN notes TEXT AFTER resolved"
                            ];
                            
                            $addedColumns = 0;
                            foreach ($extendedColumns as $sql) {
                                try {
                                    $db->exec($sql);
                                    $addedColumns++;
                                } catch (PDOException $e) {
                                    // Column might already exist
                                }
                            }
                            
                            echo '<span class="success">[✓] Added ' . $addedColumns . ' extended columns</span><br>';
                        }
                        
                        // Test the table
                        echo '<br><span class="info">[INFO] Testing error logging...</span><br>';
                        
                        $testErrorId = 'test_' . uniqid();
                        $stmt = $db->prepare("INSERT INTO error_logs 
                            (error_id, level, message, file, line, user_id, request_method, request_uri, user_agent, ip_address) 
                            VALUES 
                            (:error_id, :level, :message, :file, :line, :user_id, :request_method, :request_uri, :user_agent, :ip_address)");
                        
                        $stmt->execute([
                            'error_id' => $testErrorId,
                            'level' => 'info',
                            'message' => 'Test error log entry',
                            'file' => __FILE__,
                            'line' => __LINE__,
                            'user_id' => $_SESSION['user_id'],
                            'request_method' => 'GET',
                            'request_uri' => $_SERVER['REQUEST_URI'],
                            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
                        ]);
                        
                        echo '<span class="success">[✓] Test error logged successfully!</span><br>';
                        
                        // Clean up test
                        $db->prepare("DELETE FROM error_logs WHERE error_id = ?")->execute([$testErrorId]);
                        echo '<span class="info">[INFO] Test entry cleaned up</span><br>';
                        
                        echo '<br><span class="success"><strong>✓ Error logs table is ready!</strong></span><br>';
                        
                    } catch (Exception $e) {
                        echo '<span class="error">[ERROR] ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                        echo '<pre class="error">' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
                    }
                    ?>
                </div>
                
                <div class="mt-4">
                    <a href="/fit/public/config" class="btn btn-primary">Back to Configuration</a>
                    <a href="/fit/public" class="btn btn-secondary">Back to Dashboard</a>
                    <?php if (!isset($_GET['force'])): ?>
                    <a href="?force=true" class="btn btn-warning" onclick="return confirm('This will drop and recreate the error_logs table. Are you sure?')">Force Recreate</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</body>
</html>