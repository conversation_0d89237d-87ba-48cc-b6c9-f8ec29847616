<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\UserRetrocessionSetting;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? 'localhost';
$dbname = $_ENV['DB_NAME'] ?? 'healthcenter_billing';
$username = $_ENV['DB_USER'] ?? 'root';
$password = $_ENV['DB_PASS'] ?? '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n\n";
    
    // Get parameters
    $userId = $argv[1] ?? null;
    $action = $argv[2] ?? 'check';
    
    if (!$userId) {
        echo "Usage: php fix_retrocession_overlap.php [user_id] [action]\n";
        echo "Actions:\n";
        echo "  check    - Check for overlapping settings (default)\n";
        echo "  end      - End current active settings at a specific date\n";
        echo "  disable  - Disable specific retrocession settings\n";
        exit;
    }
    
    switch ($action) {
        case 'check':
            checkOverlaps($pdo, $userId);
            break;
            
        case 'end':
            $endDate = $argv[3] ?? null;
            if (!$endDate) {
                echo "Please provide end date: php fix_retrocession_overlap.php $userId end YYYY-MM-DD\n";
                exit;
            }
            endActiveSettings($pdo, $userId, $endDate);
            break;
            
        case 'disable':
            $settingId = $argv[3] ?? null;
            if (!$settingId) {
                echo "Please provide setting ID: php fix_retrocession_overlap.php $userId disable [setting_id]\n";
                checkOverlaps($pdo, $userId);
                exit;
            }
            disableSetting($pdo, $userId, $settingId);
            break;
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

function checkOverlaps($pdo, $userId) {
    $stmt = $pdo->prepare("
        SELECT urs.*, rp.name as profile_name
        FROM user_retrocession_settings urs
        LEFT JOIN rate_profiles rp ON urs.rate_profile_id = rp.id
        WHERE urs.user_id = ? AND urs.is_active = 1
        ORDER BY urs.valid_from
    ");
    $stmt->execute([$userId]);
    $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Active retrocession settings for user $userId:\n";
    echo str_repeat("-", 80) . "\n";
    
    $overlaps = [];
    for ($i = 0; $i < count($settings); $i++) {
        $current = $settings[$i];
        echo sprintf(
            "ID: %d | %s to %s | Profile: %s\n",
            $current['id'],
            date('d/m/Y', strtotime($current['valid_from'])),
            $current['valid_to'] ? date('d/m/Y', strtotime($current['valid_to'])) : 'Ongoing',
            $current['profile_name'] ?? 'None'
        );
        
        // Check for overlaps with subsequent settings
        for ($j = $i + 1; $j < count($settings); $j++) {
            $other = $settings[$j];
            if (datesOverlap($current, $other)) {
                $overlaps[] = [$current['id'], $other['id']];
                echo "  ⚠️  OVERLAPS with ID " . $other['id'] . "\n";
            }
        }
    }
    
    if (!empty($overlaps)) {
        echo "\n⚠️  Found " . count($overlaps) . " overlap(s). Use 'end' or 'disable' action to fix.\n";
    } else {
        echo "\n✓ No overlapping settings found.\n";
    }
}

function datesOverlap($setting1, $setting2) {
    $start1 = strtotime($setting1['valid_from']);
    $end1 = $setting1['valid_to'] ? strtotime($setting1['valid_to']) : PHP_INT_MAX;
    $start2 = strtotime($setting2['valid_from']);
    $end2 = $setting2['valid_to'] ? strtotime($setting2['valid_to']) : PHP_INT_MAX;
    
    return !($end1 < $start2 || $end2 < $start1);
}

function endActiveSettings($pdo, $userId, $endDate) {
    // Validate date
    $timestamp = strtotime($endDate);
    if (!$timestamp) {
        echo "Invalid date format. Use YYYY-MM-DD\n";
        return;
    }
    
    // Update all active settings without end date
    $stmt = $pdo->prepare("
        UPDATE user_retrocession_settings 
        SET valid_to = ?, updated_at = NOW()
        WHERE user_id = ? 
        AND is_active = 1 
        AND (valid_to IS NULL OR valid_to > ?)
        AND valid_from <= ?
    ");
    
    $stmt->execute([$endDate, $userId, $endDate, $endDate]);
    $affected = $stmt->rowCount();
    
    echo "✓ Updated $affected retrocession setting(s) to end on " . date('d/m/Y', $timestamp) . "\n";
    
    // Show updated state
    checkOverlaps($pdo, $userId);
}

function disableSetting($pdo, $userId, $settingId) {
    // Verify the setting belongs to the user
    $stmt = $pdo->prepare("
        UPDATE user_retrocession_settings 
        SET is_active = 0, updated_at = NOW()
        WHERE id = ? AND user_id = ?
    ");
    
    $stmt->execute([$settingId, $userId]);
    $affected = $stmt->rowCount();
    
    if ($affected > 0) {
        echo "✓ Disabled retrocession setting ID $settingId\n";
    } else {
        echo "❌ Setting ID $settingId not found or doesn't belong to user $userId\n";
    }
    
    // Show updated state
    checkOverlaps($pdo, $userId);
}