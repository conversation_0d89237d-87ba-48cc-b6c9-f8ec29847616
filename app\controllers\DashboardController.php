<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Services\DashboardService;
use Flight;

class DashboardController extends Controller
{
    private $dashboardService;
    
    public function __construct()
    {
        $this->dashboardService = new DashboardService();
    }
    
    /**
     * Display the main dashboard
     */
    public function index()
    {
        // Get dashboard data
        $stats = $this->dashboardService->getStats();
        $recentInvoices = $this->dashboardService->getRecentInvoices(10);
        $monthlyRevenue = $this->dashboardService->getMonthlyRevenue();
        
        // Get current user
        $user = $_SESSION['user'] ?? null;
        
        // Prepare data for template
        $data = [
            'title' => 'Dashboard',
            'stats' => $stats,
            'recent_invoices' => $recentInvoices,
            'monthly_revenue' => $monthlyRevenue,
            'user' => $user,
            'current_month_name' => date('F Y'),
            'greeting' => $this->getGreeting()
        ];
        
        // Render dashboard template
        $this->render('dashboard-simple', $data);
    }
    
    /**
     * Get dashboard stats via AJAX
     */
    public function getStats()
    {
        $stats = $this->dashboardService->getStats();
        $this->json($stats);
    }
    
    /**
     * Get recent invoices via AJAX
     */
    public function getRecentInvoices()
    {
        $limit = Flight::request()->query->limit ?? 10;
        $invoices = $this->dashboardService->getRecentInvoices($limit);
        $this->json($invoices);
    }
    
    /**
     * Get revenue chart data via AJAX
     */
    public function getRevenueChart()
    {
        $data = $this->dashboardService->getMonthlyRevenue();
        $this->json($data);
    }
    
    /**
     * Get invoice status chart data via AJAX
     */
    public function getInvoiceStatusChart()
    {
        $data = $this->dashboardService->getInvoiceStatusChart();
        $this->json($data);
    }
    
    /**
     * Get recent activities via AJAX
     */
    public function getRecentActivities()
    {
        $activities = $this->dashboardService->getRecentActivities();
        $this->json($activities);
    }
    
    /**
     * Get appropriate greeting based on time of day
     */
    private function getGreeting()
    {
        $hour = date('H');
        
        if ($hour < 12) {
            return __('dashboard.good_morning');
        } elseif ($hour < 18) {
            return __('dashboard.good_afternoon');
        } else {
            return __('dashboard.good_evening');
        }
    }
}