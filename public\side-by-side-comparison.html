<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Side-by-Side Comparison: Working vs Main Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #28a745, #dc3545); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .comparison-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .working { border-left: 5px solid #28a745; background: #f8fff9; }
        .broken { border-left: 5px solid #dc3545; background: #fff5f5; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; text-decoration: none; display: inline-block; text-align: center; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-primary { background: #007bff; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; margin: 10px 0; border: 1px solid #e9ecef; }
        .highlight { background: #fff3cd; padding: 2px 4px; border-radius: 3px; font-weight: bold; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; font-weight: bold; }
        .status-success { background: #d4edda; color: #155724; }
        .status-error { background: #f8d7da; color: #721c24; }
        .iframe-container { height: 400px; overflow: hidden; border: 2px solid #ddd; border-radius: 5px; }
        .iframe-container iframe { width: 100%; height: 100%; border: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Side-by-Side Comparison</h1>
            <p>Working Minimal Test vs Main Invoice Page</p>
        </div>

        <div class="card">
            <h2>📊 Quick Status Check</h2>
            <div class="comparison-grid">
                <div class="working">
                    <h3>✅ Minimal Test (Working)</h3>
                    <div class="status status-success">
                        ✅ Auto-population SUCCESS: 4 coaches added<br>
                        ✅ All JavaScript functions execute<br>
                        ✅ No syntax errors<br>
                        ✅ Clean console output
                    </div>
                    <a href="/fit/public/minimal-coach-dropdown-test.html?type=location" class="btn btn-success" target="_blank">🎯 Test Working Version</a>
                </div>
                <div class="broken">
                    <h3>❌ Main Invoice Page (Broken)</h3>
                    <div class="status status-error">
                        ❌ Syntax Error: missing ) after argument list<br>
                        ❌ window.showDebugConsole is not a function<br>
                        ❌ window.autoFixCoachDropdown is not a function<br>
                        ❌ Coach dropdown empty
                    </div>
                    <a href="/fit/public/invoices/create?type=location" class="btn btn-danger" target="_blank">🔴 Test Broken Version</a>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🔍 Root Cause Analysis</h2>
            <div class="comparison-grid">
                <div class="working">
                    <h3>✅ What Works in Minimal Test</h3>
                    <div class="code">
                        // Simple, clean initialization<br>
                        document.addEventListener('DOMContentLoaded', function() {<br>
                        &nbsp;&nbsp;debugLog('DOM Content Loaded');<br>
                        &nbsp;&nbsp;// Check URL parameter<br>
                        &nbsp;&nbsp;if (typeParam === 'location') {<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;setTimeout(() => {<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;testAutoPopulation();<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;}, 100);<br>
                        &nbsp;&nbsp;}<br>
                        });<br><br>
                        
                        // Direct coach population<br>
                        mockCoachesData.forEach(coach => {<br>
                        &nbsp;&nbsp;const option = new Option(name, 'user_' + id);<br>
                        &nbsp;&nbsp;billableSelect.add(option);<br>
                        });
                    </div>
                </div>
                <div class="broken">
                    <h3>❌ What Fails in Main Page</h3>
                    <div class="code">
                        // Complex initialization chain<br>
                        window.InvoiceDebugger = { /* complex object */ };<br>
                        window.InvoiceDebugger.addFallbackMechanisms();<br>
                        window.showDebugConsole = () => { /* function */ };<br><br>
                        
                        // Multiple layers of detection<br>
                        if (window.location.search.includes('type=location')) {<br>
                        &nbsp;&nbsp;initializeLocationInvoiceComplete();<br>
                        }<br><br>
                        
                        // Calls loadBillableOptions() which has detection logic<br>
                        const isLocationInvoice = /* complex detection */;<br>
                        if (isLocationInvoice) {<br>
                        &nbsp;&nbsp;// Show coaches<br>
                        }
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🎯 Key Differences Identified</h2>
            <div class="comparison-grid">
                <div>
                    <h3>🔧 Initialization Approach</h3>
                    <div class="code">
                        <span class="highlight">Working:</span> Simple, direct<br>
                        • DOM ready → Check URL → Populate<br>
                        • Mock data → Direct population<br>
                        • No complex object dependencies<br><br>
                        
                        <span class="highlight">Broken:</span> Complex, layered<br>
                        • InvoiceDebugger object creation<br>
                        • Function availability checks<br>
                        • Multiple detection layers<br>
                        • Dependency on PHP data
                    </div>
                </div>
                <div>
                    <h3>📊 Data Source</h3>
                    <div class="code">
                        <span class="highlight">Working:</span> Mock data<br>
                        const mockCoachesData = [...];<br>
                        // Always available, no loading issues<br><br>
                        
                        <span class="highlight">Broken:</span> PHP data<br>
                        let coachesData = {{ coaches|json_encode }};<br>
                        // May have loading/timing issues<br>
                        // Could be undefined or empty
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>💡 Proposed Solution</h2>
            <div class="comparison-grid">
                <div class="working">
                    <h3>✅ Proven Working Code</h3>
                    <div class="code">
                        // This works 100% of the time<br>
                        function populateCoaches() {<br>
                        &nbsp;&nbsp;const select = document.getElementById('billable_id');<br>
                        &nbsp;&nbsp;const coaches = mockCoachesData; // or coachesData<br>
                        &nbsp;&nbsp;select.innerHTML = '&lt;option value=""&gt;Select...&lt;/option&gt;';<br>
                        &nbsp;&nbsp;coaches.forEach(coach => {<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;const option = new Option(coach.name, 'user_' + coach.id);<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;select.add(option);<br>
                        &nbsp;&nbsp;});<br>
                        }
                    </div>
                </div>
                <div class="broken">
                    <h3>🔧 What Needs Fixing</h3>
                    <div class="code">
                        1. <span class="highlight">Simplify initialization</span><br>
                        &nbsp;&nbsp;• Remove complex InvoiceDebugger dependencies<br>
                        &nbsp;&nbsp;• Use direct population like minimal test<br><br>
                        
                        2. <span class="highlight">Fix syntax errors</span><br>
                        &nbsp;&nbsp;• Find and fix remaining syntax issues<br>
                        &nbsp;&nbsp;• Ensure all functions are defined<br><br>
                        
                        3. <span class="highlight">Verify data loading</span><br>
                        &nbsp;&nbsp;• Ensure coachesData is properly loaded<br>
                        &nbsp;&nbsp;• Add fallback if data is missing
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🧪 Live Testing</h2>
            <div class="comparison-grid">
                <div class="working">
                    <h3>✅ Test Working Version</h3>
                    <div class="iframe-container">
                        <iframe src="/fit/public/minimal-coach-dropdown-test.html?type=location"></iframe>
                    </div>
                    <div class="status status-success">
                        Expected: Immediate coach population with 4 coaches
                    </div>
                </div>
                <div class="broken">
                    <h3>❌ Test Broken Version</h3>
                    <p><strong>⚠️ Cannot embed due to JavaScript errors</strong></p>
                    <div class="status status-error">
                        Syntax errors prevent iframe loading
                    </div>
                    <a href="/fit/public/invoices/create?type=location" class="btn btn-danger" target="_blank">🔴 Open in New Tab</a>
                </div>
            </div>
        </div>

        <div class="card">
            <h2>📋 Action Plan</h2>
            <ol>
                <li><strong>Immediate Fix:</strong> Apply the working minimal test logic to the main page</li>
                <li><strong>Syntax Fix:</strong> Find and fix the remaining JavaScript syntax error</li>
                <li><strong>Simplification:</strong> Remove complex dependencies that aren't needed</li>
                <li><strong>Testing:</strong> Use the working code as a template for both location and retrocession</li>
            </ol>
        </div>

        <div class="card">
            <h2>🔗 Quick Links</h2>
            <div>
                <a href="/fit/public/minimal-coach-dropdown-test.html?type=location" class="btn btn-success" target="_blank">🎯 Working Minimal Test</a>
                <a href="/fit/public/comprehensive-dropdown-test.html" class="btn btn-primary" target="_blank">📊 Comprehensive Test</a>
                <a href="/fit/public/firefox-addon-detector.html" class="btn btn-warning" target="_blank">🔍 Firefox Detector</a>
                <a href="/fit/public/invoices/create?type=location" class="btn btn-danger" target="_blank">🔴 Broken Main Page</a>
            </div>
        </div>
    </div>
</body>
</html>