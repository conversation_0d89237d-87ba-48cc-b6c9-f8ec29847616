<?php
/**
 * Run migration to add exclude_patient_line column
 */

require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, '=') !== false && strpos($line, '#') !== 0) {
            list($key, $value) = explode('=', $line, 2);
            $key = trim($key);
            $value = trim($value);
            putenv("$key=$value");
            $_ENV[$key] = $value;
        }
    }
}

// Database configuration
$config = [
    'host' => getenv('DB_HOST') ?: 'localhost',
    'port' => getenv('DB_PORT') ?: 3306,
    'database' => getenv('DB_DATABASE') ?: 'healthcenter_billing',
    'username' => getenv('DB_USERNAME') ?: 'root',
    'password' => getenv('DB_PASSWORD') ?: '',
    'charset' => 'utf8mb4',
];

try {
    // Connect to database
    $dsn = sprintf(
        'mysql:host=%s;port=%d;dbname=%s;charset=%s',
        $config['host'],
        $config['port'],
        $config['database'],
        $config['charset']
    );
    
    $db = new PDO($dsn, $config['username'], $config['password']);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Check if column already exists
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('exclude_patient_line', $columns)) {
        echo "✓ Column 'exclude_patient_line' already exists.\n";
    } else {
        echo "Adding 'exclude_patient_line' column...\n";
        
        // Run the migration
        $sql = "ALTER TABLE retrocession_data_entry 
                ADD COLUMN exclude_patient_line TINYINT(1) DEFAULT 0 AFTER status";
        
        $db->exec($sql);
        echo "✓ Column added successfully!\n";
        
        // Add index
        try {
            $db->exec("ALTER TABLE retrocession_data_entry ADD INDEX idx_exclude_patient (exclude_patient_line)");
            echo "✓ Index added successfully!\n";
        } catch (Exception $e) {
            echo "Note: Index might already exist or couldn't be added: " . $e->getMessage() . "\n";
        }
        
        // Update any existing entries to default value
        $db->exec("UPDATE retrocession_data_entry SET exclude_patient_line = 0 WHERE exclude_patient_line IS NULL");
        echo "✓ Existing entries updated with default value.\n";
    }
    
    // Verify the column exists
    $stmt = $db->query("DESCRIBE retrocession_data_entry");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "\nColumn details:\n";
    foreach ($columns as $column) {
        if ($column['Field'] === 'exclude_patient_line') {
            echo "Field: " . $column['Field'] . "\n";
            echo "Type: " . $column['Type'] . "\n";
            echo "Null: " . $column['Null'] . "\n";
            echo "Default: " . $column['Default'] . "\n";
            break;
        }
    }
    
    echo "\n✅ Migration completed successfully!\n";
    echo "\nYou can now use the exclude patient line feature:\n";
    echo "1. Go to http://localhost/fit/public/retrocession/\n";
    echo "2. Click on a practitioner to enter data\n";
    echo "3. Check the 'Exclude patient line' checkbox when needed\n";
    echo "4. The generated invoice will not include the patient part\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Please check your database configuration in .env\n";
}