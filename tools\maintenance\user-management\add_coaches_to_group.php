<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== ADDING COACHES TO GROUP ===\n\n";
    
    // User IDs of the coaches that were just created
    $coachUserIds = [14, 15, 16];
    
    // Find the Coach group
    $stmt = $pdo->prepare("SELECT id, name FROM user_groups WHERE id = 24");
    $stmt->execute();
    $group = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$group) {
        echo "❌ Coach group (ID: 24) not found!\n";
        exit(1);
    }
    
    echo "✓ Found group: {$group['name']} (ID: {$group['id']})\n\n";
    
    // Add each user to the group
    foreach ($coachUserIds as $userId) {
        // Get user info
        $stmt = $pdo->prepare("SELECT username, first_name, last_name FROM users WHERE id = :id");
        $stmt->execute([':id' => $userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo "❌ User ID {$userId} not found\n";
            continue;
        }
        
        echo "Adding {$user['first_name']} {$user['last_name']} ({$user['username']}) to Coach group...\n";
        
        // Check if already in group
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM user_group_members 
            WHERE user_id = :user_id AND group_id = :group_id
        ");
        $stmt->execute([
            ':user_id' => $userId,
            ':group_id' => $group['id']
        ]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "  ℹ️  Already in Coach group\n";
        } else {
            // Add to group - using joined_at instead of created_at
            $stmt = $pdo->prepare("
                INSERT INTO user_group_members (user_id, group_id, joined_at) 
                VALUES (:user_id, :group_id, NOW())
            ");
            $stmt->execute([
                ':user_id' => $userId,
                ':group_id' => $group['id']
            ]);
            echo "  ✅ Added to Coach group\n";
        }
    }
    
    echo "\n=== COACH GROUP MEMBERS ===\n\n";
    
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.first_name, u.last_name, u.email, ugm.joined_at
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = :group_id
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute([':group_id' => $group['id']]);
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($members as $member) {
        echo "- {$member['first_name']} {$member['last_name']} ({$member['username']}) - {$member['email']}\n";
        echo "  Joined: " . date('d/m/Y H:i', strtotime($member['joined_at'])) . "\n";
    }
    
    echo "\nTotal members in Coach group: " . count($members) . "\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}