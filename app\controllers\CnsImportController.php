<?php

namespace App\Controllers;

use Flight;
use App\Models\Client;
use App\Services\CnsImportService;
use Exception;
use PDO;

class CnsImportController extends \App\Core\Controller
{
    private $client;
    private $importService;
    
    public function __construct()
    {
        $this->client = new Client();
        $this->importService = new CnsImportService();
    }
    
    /**
     * CNS import dashboard
     */
    public function index()
    {
        $page = Flight::request()->query->page ?: 1;
        $limit = Flight::request()->query->limit ?: 20;
        
        // Get import history
        $imports = $this->getImportHistory($page, $limit);
        
        // Get statistics
        $stats = $this->getImportStatistics();
        
        $this->render('cns/import-index', [
            'imports' => $imports['data'],
            'pagination' => $imports['pagination'],
            'stats' => $stats
        ]);
    }
    
    /**
     * Show upload form
     */
    public function upload()
    {
        $practitioners = $this->client->getAll([
            'client_type' => 'practitioner',
            'is_active' => true
        ]);
        
        $this->render('cns/upload', [
            'practitioners' => $practitioners,
            'importTypes' => [
                'pdf' => __('cns.import_type.pdf'),
                'excel' => __('cns.import_type.excel'),
                'csv' => __('cns.import_type.csv'),
                'xml' => __('cns.import_type.xml')
            ]
        ]);
    }
    
    /**
     * Process file upload
     */
    public function processUpload()
    {
        try {
            // Validate upload
            if (!isset($_FILES['cns_file']) || $_FILES['cns_file']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception(__('cns.upload_failed'));
            }
            
            $file = $_FILES['cns_file'];
            $practitionerId = Flight::request()->data->practitioner_id;
            $importType = Flight::request()->data->import_type;
            $month = Flight::request()->data->month;
            $year = Flight::request()->data->year;
            
            // Validate file type
            $allowedTypes = [
                'pdf' => ['application/pdf'],
                'excel' => ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
                'csv' => ['text/csv', 'text/plain'],
                'xml' => ['text/xml', 'application/xml']
            ];
            
            if (!in_array($file['type'], $allowedTypes[$importType] ?? [])) {
                throw new Exception(__('cns.invalid_file_type'));
            }
            
            // Save uploaded file
            $uploadDir = __DIR__ . '/../../storage/cns_imports/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            $filename = uniqid('cns_') . '_' . basename($file['name']);
            $filepath = $uploadDir . $filename;
            
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception(__('cns.file_save_failed'));
            }
            
            // Create import record
            $importId = $this->createImportRecord([
                'practitioner_id' => $practitionerId,
                'period_month' => $month,
                'period_year' => $year,
                'file_name' => $filename,
                'file_path' => $filepath,
                'file_type' => $importType,
                'file_size' => $file['size'],
                'status' => 'uploaded'
            ]);
            
            // Process based on type
            if ($importType === 'pdf') {
                // Redirect to OCR processing
                Flight::redirect($this->url('/cns/import/' . $importId . '/ocr'));
            } else {
                // Process directly
                Flight::redirect($this->url('/cns/import/' . $importId . '/process'));
            }
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/cns/import/upload'));
        }
    }
    
    /**
     * OCR processing for PDF
     */
    public function ocr($importId)
    {
        $import = $this->getImportById($importId);
        
        if (!$import || $import['file_type'] !== 'pdf') {
            Flight::notFound();
            return;
        }
        
        // Get OCR preview if available
        $ocrPreview = null;
        if ($import['ocr_status'] === 'completed') {
            $ocrPreview = $this->importService->getOcrPreview($importId);
        }
        
        $this->render('cns/ocr-process', [
            'import' => $import,
            'ocrPreview' => $ocrPreview
        ]);
    }
    
    /**
     * Process OCR
     */
    public function processOcr($importId)
    {
        try {
            $import = $this->getImportById($importId);
            
            if (!$import || $import['file_type'] !== 'pdf') {
                throw new Exception(__('cns.invalid_import'));
            }
            
            // Update status
            $this->updateImportStatus($importId, 'processing_ocr');
            
            // Process OCR
            $result = $this->importService->processOcr($import['file_path']);
            
            // Save OCR result
            $this->saveOcrResult($importId, $result);
            
            // Update status
            $this->updateImportStatus($importId, 'ocr_completed');
            
            Flight::flash('success', __('cns.ocr_completed'));
            Flight::redirect($this->url('/cns/import/' . $importId . '/ocr'));
            
        } catch (Exception $e) {
            $this->updateImportStatus($importId, 'ocr_failed', $e->getMessage());
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/cns/import/' . $importId . '/ocr'));
        }
    }
    
    /**
     * Review and edit OCR results
     */
    public function reviewOcr($importId)
    {
        $import = $this->getImportById($importId);
        
        if (!$import || $import['ocr_status'] !== 'completed') {
            Flight::notFound();
            return;
        }
        
        $ocrData = $this->importService->getOcrData($importId);
        
        $this->render('cns/ocr-review', [
            'import' => $import,
            'ocrData' => $ocrData,
            'fields' => [
                'cns_number' => __('cns.field.cns_number'),
                'practitioner_name' => __('cns.field.practitioner_name'),
                'period' => __('cns.field.period'),
                'total_amount' => __('cns.field.total_amount'),
                'cns_amount' => __('cns.field.cns_amount'),
                'patient_amount' => __('cns.field.patient_amount')
            ]
        ]);
    }
    
    /**
     * Save reviewed OCR data
     */
    public function saveOcrReview($importId)
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Validate data
            $this->validateCnsData($data);
            
            // Update OCR data
            $this->importService->updateOcrData($importId, $data);
            
            // Update status
            $this->updateImportStatus($importId, 'ready_to_process');
            
            Flight::flash('success', __('cns.ocr_data_saved'));
            Flight::redirect($this->url('/cns/import/' . $importId . '/process'));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/cns/import/' . $importId . '/review'));
        }
    }
    
    /**
     * Process import
     */
    public function process($importId)
    {
        $import = $this->getImportById($importId);
        
        if (!$import) {
            Flight::notFound();
            return;
        }
        
        // Get preview based on file type
        $preview = $this->importService->getImportPreview($importId);
        
        $this->render('cns/process', [
            'import' => $import,
            'preview' => $preview
        ]);
    }
    
    /**
     * Execute import
     */
    public function executeImport($importId)
    {
        try {
            $import = $this->getImportById($importId);
            
            if (!$import) {
                throw new Exception(__('cns.import_not_found'));
            }
            
            // Update status
            $this->updateImportStatus($importId, 'processing');
            
            // Process import based on type
            $result = $this->importService->processImport($import);
            
            // Create data entry record
            if ($result['success']) {
                $this->createDataEntryFromImport($import, $result['data']);
                $this->updateImportStatus($importId, 'completed', null, $result['summary']);
            } else {
                throw new Exception($result['error']);
            }
            
            Flight::flash('success', __('cns.import_completed', ['count' => $result['summary']['processed']]));
            Flight::redirect($this->url('/retrocession/' . $import['practitioner_id'] . '/data-entry?month=' . $import['period_month'] . '&year=' . $import['period_year']));
            
        } catch (Exception $e) {
            $this->updateImportStatus($importId, 'failed', $e->getMessage());
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/cns/import/' . $importId . '/process'));
        }
    }
    
    /**
     * Download template
     */
    public function downloadTemplate($type)
    {
        try {
            $templates = [
                'excel' => 'cns_import_template.xlsx',
                'csv' => 'cns_import_template.csv'
            ];
            
            if (!isset($templates[$type])) {
                throw new Exception(__('cns.invalid_template_type'));
            }
            
            $templatePath = __DIR__ . '/../../resources/templates/' . $templates[$type];
            
            if (!file_exists($templatePath)) {
                throw new Exception(__('cns.template_not_found'));
            }
            
            // Send file
            header('Content-Type: application/octet-stream');
            header('Content-Disposition: attachment; filename="' . $templates[$type] . '"');
            header('Content-Length: ' . filesize($templatePath));
            readfile($templatePath);
            exit;
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/cns/import/upload'));
        }
    }
    
    /**
     * View import details
     */
    public function view($importId)
    {
        $import = $this->getImportById($importId);
        
        if (!$import) {
            Flight::notFound();
            return;
        }
        
        // Get related data entry if exists
        $dataEntry = null;
        if ($import['data_entry_id']) {
            $dataEntry = $this->getDataEntry($import['data_entry_id']);
        }
        
        // Get error details if failed
        $errorDetails = null;
        if ($import['status'] === 'failed' && $import['error_message']) {
            $errorDetails = json_decode($import['error_message'], true) ?: $import['error_message'];
        }
        
        $this->render('cns/view', [
            'import' => $import,
            'dataEntry' => $dataEntry,
            'errorDetails' => $errorDetails
        ]);
    }
    
    /**
     * Delete import
     */
    public function delete($importId)
    {
        try {
            $import = $this->getImportById($importId);
            
            if (!$import) {
                throw new Exception(__('cns.import_not_found'));
            }
            
            if ($import['status'] === 'completed') {
                throw new Exception(__('cns.cannot_delete_completed'));
            }
            
            // Delete file
            if (file_exists($import['file_path'])) {
                unlink($import['file_path']);
            }
            
            // Delete record
            $db = Flight::db();
            $stmt = $db->prepare("DELETE FROM cns_imports WHERE id = ?");
            $stmt->execute([$importId]);
            
            Flight::flash('success', __('cns.import_deleted'));
            Flight::redirect($this->url('/cns/import'));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/cns/import'));
        }
    }
    
    /**
     * Private helper methods
     */
    
    private function getImportHistory($page, $limit)
    {
        $db = Flight::db();
        
        // Count total
        $stmt = $db->query("SELECT COUNT(*) as total FROM cns_imports");
        $total = $stmt->fetch()['total'];
        
        // Get data
        $offset = ($page - 1) * $limit;
        $stmt = $db->prepare("
            SELECT 
                ci.*,
                c.name as practitioner_name,
                c.client_number
            FROM cns_imports ci
            LEFT JOIN clients c ON ci.practitioner_id = c.id
            ORDER BY ci.created_at DESC
            LIMIT :limit OFFSET :offset
        ");
        
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        return [
            'data' => $stmt->fetchAll(),
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }
    
    private function getImportStatistics()
    {
        $db = Flight::db();
        
        $stmt = $db->query("
            SELECT 
                COUNT(*) as total_imports,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                COUNT(DISTINCT practitioner_id) as practitioners,
                MAX(created_at) as last_import
            FROM cns_imports
        ");
        
        return $stmt->fetch();
    }
    
    private function createImportRecord($data)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            INSERT INTO cns_imports (
                practitioner_id, period_month, period_year,
                file_name, file_path, file_type, file_size,
                status, created_by
            ) VALUES (
                :practitioner_id, :period_month, :period_year,
                :file_name, :file_path, :file_type, :file_size,
                :status, :created_by
            )
        ");
        
        $stmt->execute([
            ':practitioner_id' => $data['practitioner_id'],
            ':period_month' => $data['period_month'],
            ':period_year' => $data['period_year'],
            ':file_name' => $data['file_name'],
            ':file_path' => $data['file_path'],
            ':file_type' => $data['file_type'],
            ':file_size' => $data['file_size'],
            ':status' => $data['status'],
            ':created_by' => $_SESSION['user_id'] ?? 1
        ]);
        
        return $db->lastInsertId();
    }
    
    private function getImportById($id)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                ci.*,
                c.name as practitioner_name,
                c.client_number
            FROM cns_imports ci
            LEFT JOIN clients c ON ci.practitioner_id = c.id
            WHERE ci.id = ?
        ");
        
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
    
    private function updateImportStatus($importId, $status, $error = null, $summary = null)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE cns_imports 
            SET status = :status,
                error_message = :error_message,
                processing_summary = :processing_summary,
                processed_at = CASE WHEN :status IN ('completed', 'failed') THEN NOW() ELSE processed_at END,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':status' => $status,
            ':error_message' => $error,
            ':processing_summary' => $summary ? json_encode($summary) : null,
            ':id' => $importId
        ]);
    }
    
    private function saveOcrResult($importId, $result)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE cns_imports 
            SET ocr_status = :ocr_status,
                ocr_result = :ocr_result,
                ocr_confidence = :ocr_confidence,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':ocr_status' => 'completed',
            ':ocr_result' => json_encode($result['data']),
            ':ocr_confidence' => $result['confidence'] ?? null,
            ':id' => $importId
        ]);
    }
    
    private function validateCnsData($data)
    {
        $required = ['cns_amount', 'patient_amount', 'period_month', 'period_year'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception(__('validation.required', ['field' => $field]));
            }
        }
        
        if (!is_numeric($data['cns_amount']) || !is_numeric($data['patient_amount'])) {
            throw new Exception(__('cns.invalid_amounts'));
        }
        
        if ($data['cns_amount'] < 0 || $data['patient_amount'] < 0) {
            throw new Exception(__('cns.negative_amounts'));
        }
    }
    
    private function createDataEntryFromImport($import, $data)
    {
        $db = Flight::db();
        
        // Check if entry already exists
        $stmt = $db->prepare("
            SELECT id FROM retrocession_data_entry
            WHERE practitioner_id = :practitioner_id
            AND period_month = :period_month
            AND period_year = :period_year
        ");
        
        $stmt->execute([
            ':practitioner_id' => $import['practitioner_id'],
            ':period_month' => $import['period_month'],
            ':period_year' => $import['period_year']
        ]);
        
        $existing = $stmt->fetch();
        
        if ($existing) {
            // Update existing
            $stmt = $db->prepare("
                UPDATE retrocession_data_entry
                SET cns_amount = :cns_amount,
                    patient_amount = :patient_amount,
                    data_source = 'cns_import',
                    cns_import_id = :cns_import_id,
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $stmt->execute([
                ':cns_amount' => $data['cns_amount'],
                ':patient_amount' => $data['patient_amount'],
                ':cns_import_id' => $import['id'],
                ':id' => $existing['id']
            ]);
            
            $dataEntryId = $existing['id'];
        } else {
            // Create new
            $stmt = $db->prepare("
                INSERT INTO retrocession_data_entry (
                    practitioner_id, period_month, period_year,
                    cns_amount, patient_amount,
                    data_source, cns_import_id,
                    entered_by, entered_at
                ) VALUES (
                    :practitioner_id, :period_month, :period_year,
                    :cns_amount, :patient_amount,
                    'cns_import', :cns_import_id,
                    :entered_by, NOW()
                )
            ");
            
            $stmt->execute([
                ':practitioner_id' => $import['practitioner_id'],
                ':period_month' => $import['period_month'],
                ':period_year' => $import['period_year'],
                ':cns_amount' => $data['cns_amount'],
                ':patient_amount' => $data['patient_amount'],
                ':cns_import_id' => $import['id'],
                ':entered_by' => $_SESSION['user_id'] ?? 1
            ]);
            
            $dataEntryId = $db->lastInsertId();
        }
        
        // Update import with data entry reference
        $stmt = $db->prepare("
            UPDATE cns_imports 
            SET data_entry_id = :data_entry_id 
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':data_entry_id' => $dataEntryId,
            ':id' => $import['id']
        ]);
    }
    
    private function getDataEntry($id)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT rde.*, c.name as practitioner_name
            FROM retrocession_data_entry rde
            LEFT JOIN clients c ON rde.practitioner_id = c.id
            WHERE rde.id = ?
        ");
        
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
}