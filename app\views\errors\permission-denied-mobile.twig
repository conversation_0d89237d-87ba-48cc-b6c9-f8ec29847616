{% extends 'base-modern.twig' %}

{% block title %}{{ __('errors.permission_denied') }}{% endblock %}

{% block content %}
<div class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
    <div class="text-center permission-denied-mobile">
        <i class="fas fa-shield-alt mb-4" style="font-size: 4rem; color: var(--bs-danger);"></i>
        
        <h2 class="mb-3">{{ __('errors.permission_denied') }}</h2>
        
        <p class="text-muted mb-4">
            {{ message|default(__('errors.permission_denied_message')) }}
        </p>
        
        {% if required_permission is defined %}
        <div class="alert alert-warning d-inline-block mb-4">
            <small>
                <i class="fas fa-lock me-1"></i>
                {{ __('errors.required_permission') }}: <strong>{{ required_permission }}</strong>
            </small>
        </div>
        {% endif %}
        
        <div class="d-grid gap-2 d-md-block">
            <a href="{{ url('/dashboard') }}" class="btn btn-primary">
                <i class="fas fa-home me-2"></i>
                {{ __('common.go_to_dashboard') }}
            </a>
            
            <button type="button" class="btn btn-outline-secondary" onclick="history.back()">
                <i class="fas fa-arrow-left me-2"></i>
                {{ __('common.go_back') }}
            </button>
        </div>
        
        {% if current_user and current_user.is_admin %}
        <div class="mt-4">
            <small class="text-muted">
                <a href="{{ url('/permissions') }}" class="text-decoration-none">
                    <i class="fas fa-cog me-1"></i>
                    {{ __('permissions.manage_permissions') }}
                </a>
            </small>
        </div>
        {% endif %}
    </div>
</div>

<!-- Mobile-specific styling -->
<style>
@media (max-width: 767px) {
    .permission-denied-mobile {
        padding: 2rem 1rem;
    }
    
    .permission-denied-mobile h2 {
        font-size: 1.5rem;
    }
    
    .permission-denied-mobile p {
        font-size: 0.9rem;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Auto-redirect after 10 seconds on mobile
if (window.innerWidth < 768) {
    setTimeout(() => {
        window.location.href = '{{ url("/dashboard") }}';
    }, 10000);
}

// Add haptic feedback on mobile
if ('vibrate' in navigator) {
    navigator.vibrate([100, 50, 100]);
}
</script>
{% endblock %}