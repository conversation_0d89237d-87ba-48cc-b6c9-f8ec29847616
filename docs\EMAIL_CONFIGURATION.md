# Email Configuration Guide

## Overview

The Fit360 AdminDesk email system provides a robust solution for sending emails with the following features:

- SMTP authentication support
- HTML and plain text emails
- File attachments
- CC/BCC recipients
- Reply-to headers
- Email queue for bulk sending
- Retry logic for failed sends
- Email activity logging
- Blacklist management
- Rate limiting

## Configuration

### Environment Variables (.env)

```env
# Basic SMTP Configuration
MAIL_MAILER=smtp
MAIL_HOST=localhost          # SMTP server hostname
MAIL_PORT=1025              # SMTP server port
MAIL_USERNAME=              # SMTP username (if required)
MAIL_PASSWORD=              # SMTP password (if required)
MAIL_ENCRYPTION=            # tls, ssl, or empty
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

# Email Queue Settings
MAIL_QUEUE_ENABLED=true     # Enable/disable email queue
MAIL_QUEUE_BATCH_SIZE=50    # Emails per batch
MAIL_QUEUE_RETRY_ATTEMPTS=3 # Max retry attempts
MAIL_QUEUE_RETRY_DELAY=300  # Delay between retries (seconds)

# Rate Limiting
MAIL_RATE_LIMIT_PER_MINUTE=30
MAIL_RATE_LIMIT_PER_HOUR=500
```

### Database Setup

Run the migration to create the required tables:

```bash
mysql -u root -p fitapp < database/migrations/create_email_queue_tables.sql
```

This creates the following tables:
- `email_queue` - Stores pending emails
- `email_logs` - Logs all email activity
- `email_blacklist` - Manages blacklisted emails
- `email_rate_limits` - Tracks rate limiting

## Usage

### Sending Emails Directly

```php
use App\Services\EmailService;

$emailService = new EmailService();

// Send immediate email (bypasses queue)
$result = $emailService->send([
    'to' => '<EMAIL>',
    'cc' => ['<EMAIL>', '<EMAIL>'],
    'bcc' => ['<EMAIL>'],
    'reply_to' => '<EMAIL>',
    'subject' => 'Test Email',
    'body_html' => '<h1>Hello</h1><p>This is a test email.</p>',
    'body_text' => 'Hello, This is a test email.',
    'attachments' => [
        [
            'name' => 'document.pdf',
            'content' => $pdfContent,
            'type' => 'application/pdf'
        ]
    ],
    'immediate' => true // Bypass queue
]);
```

### Using the Email Queue

```php
use App\Services\EmailQueueService;

$queueService = new EmailQueueService();

// Add email to queue
$queueId = $queueService->queueEmail([
    'to' => '<EMAIL>',
    'subject' => 'Queued Email',
    'body_html' => '<p>This email will be sent via queue.</p>',
    'priority' => 'high', // low, normal, high
    'scheduled_at' => '2025-02-01 10:00:00' // Optional scheduling
]);

// Process queue manually
$results = $queueService->processQueue(50); // Process up to 50 emails
```

### Automated Queue Processing

Set up a cron job to process the email queue automatically:

```bash
# Process email queue every minute
* * * * * php /path/to/fit/app/cron/process_email_queue.php

# With options
* * * * * php /path/to/fit/app/cron/process_email_queue.php --limit=100 --verbose
```

### Sending Invoice Emails

```php
$emailService = new EmailService();

// Send invoice email to recipient
$result = $emailService->sendInvoiceEmail($invoiceId);

// Send to specific email
$result = $emailService->sendInvoiceEmail($invoiceId, '<EMAIL>');
```

### Managing Blacklist

```php
$queueService = new EmailQueueService();

// Add to blacklist
$queueService->blacklistEmail(
    '<EMAIL>',
    'unsubscribed', // unsubscribed, bounced, complaint, manual
    $userId,
    'User requested removal'
);

// Remove from blacklist
$queueService->unblacklistEmail('<EMAIL>');
```

## Email Templates

The system uses database-stored email templates with variable replacement:

### Basic Variables
- `{INVOICE_NUMBER}` - Invoice number
- `{CLIENT_NAME}` - Client/recipient name
- `{TOTAL_AMOUNT}` - Formatted total amount
- `{DUE_DATE}` - Formatted due date
- `{COMPANY_NAME}` - Company name from config

### Advanced Template Syntax

```html
<!-- Nested variables -->
{{invoice.number}} - {{client.name}}

<!-- Conditionals -->
{{#if invoice.is_credit_note}}
  This is a credit note for {{invoice.reference_invoice_number}}
{{else}}
  Regular invoice
{{/if}}

<!-- Loops -->
{{#each lines}}
  <tr>
    <td>{{description}}</td>
    <td>{{quantity}}</td>
    <td>{{unit_price}}</td>
    <td>{{total}}</td>
  </tr>
{{/each}}

<!-- Default values -->
{{client.phone|Not provided}}
```

## API Endpoints

### Email Management Dashboard
- `GET /email/dashboard` - Email management interface

### Queue Operations
- `GET /api/email/queue` - List queued emails
- `POST /api/email/queue/process` - Process queue manually
- `POST /api/email/queue/:id/resend` - Resend failed email
- `POST /api/email/queue/:id/cancel` - Cancel pending email

### Blacklist Management
- `GET /api/email/blacklist` - List blacklisted emails
- `POST /api/email/blacklist` - Add to blacklist
- `DELETE /api/email/blacklist/:email` - Remove from blacklist

### Testing
- `POST /api/email/test` - Send test email

### Logs
- `GET /api/email/logs` - View email logs with search

## Error Handling

The system handles common SMTP errors with user-friendly messages:

- Connection failures
- Authentication errors
- Invalid addresses
- Rate limiting
- Quota exceeded
- TLS/SSL errors

Failed emails are automatically retried with exponential backoff.

## Development with Mailhog

For local development, the system is configured to use Mailhog:

1. Start Mailhog:
   ```bash
   docker run -d -p 1025:1025 -p 8025:8025 mailhog/mailhog
   ```

2. Configure .env:
   ```env
   MAIL_HOST=localhost
   MAIL_PORT=1025
   ```

3. View emails at: http://localhost:8025

## Monitoring

### Queue Statistics
```php
$stats = $queueService->getQueueStats();
// Returns queue counts by status, hourly activity, blacklist count
```

### Cleanup Old Entries
```php
// Remove sent/failed emails older than 30 days
$deleted = $queueService->cleanOldEntries(30);
```

## Troubleshooting

### Emails Not Sending
1. Check SMTP configuration in .env
2. Verify credentials are correct
3. Check email logs for errors
4. Test with immediate send (bypass queue)

### Queue Not Processing
1. Verify cron job is running
2. Check for PHP errors in cron output
3. Manually process queue from dashboard
4. Check rate limits

### High Failure Rate
1. Review email logs for patterns
2. Check blacklist for valid addresses
3. Verify SMTP server limits
4. Monitor rate limiting

## Security Considerations

1. Store SMTP credentials securely in .env
2. Use TLS/SSL encryption when possible
3. Implement rate limiting to prevent abuse
4. Monitor blacklist for suspicious activity
5. Regularly clean old email logs
6. Validate email addresses before sending