name: Database Backup

on:
  schedule:
    # Run daily at 2:00 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      retention_days:
        description: 'Number of days to retain backups'
        required: false
        default: '7'
        type: string
      compress:
        description: 'Compress backup files'
        required: false
        default: true
        type: boolean

jobs:
  backup:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: ${{ secrets.DB_PASSWORD }}
          MYSQL_DATABASE: ${{ secrets.DB_DATABASE }}
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.1'
        extensions: mysqli, pdo_mysql
        tools: composer

    - name: Install dependencies
      run: composer install --no-dev --optimize-autoloader

    - name: Wait for MyS<PERSON>
      run: |
        until mysqladmin ping -h 127.0.0.1 -P 3306 -u root -p${{ secrets.DB_PASSWORD }} --silent; do
          echo 'Waiting for MySQL...'
          sleep 2
        done

    - name: Restore database from latest backup (if exists)
      run: |
        # Check if we have any existing backups in the repository
        if ls storage/backups/fitapp_backup_*.sql* 1> /dev/null 2>&1; then
          echo "Found existing backup files, restoring latest..."
          LATEST_BACKUP=$(ls -t storage/backups/fitapp_backup_*.sql* | head -n1)
          echo "Restoring from: $LATEST_BACKUP"
          
          if [[ $LATEST_BACKUP == *.gz ]]; then
            gunzip -c "$LATEST_BACKUP" | mysql -h 127.0.0.1 -P 3306 -u root -p${{ secrets.DB_PASSWORD }} ${{ secrets.DB_DATABASE }}
          else
            mysql -h 127.0.0.1 -P 3306 -u root -p${{ secrets.DB_PASSWORD }} ${{ secrets.DB_DATABASE }} < "$LATEST_BACKUP"
          fi
          echo "Database restored successfully"
        else
          echo "No existing backups found, skipping restore"
        fi

    - name: Create database backup
      env:
        DB_HOST: 127.0.0.1
        DB_PORT: 3306
        DB_DATABASE: ${{ secrets.DB_DATABASE }}
        DB_USERNAME: root
        DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
      run: |
        RETENTION_DAYS="${{ github.event.inputs.retention_days || '7' }}"
        COMPRESS_FLAG=""
        if [[ "${{ github.event.inputs.compress || 'true' }}" == "true" ]]; then
          COMPRESS_FLAG="--compress"
        else
          COMPRESS_FLAG="--no-compress"
        fi
        
        php scripts/database-backup.php $COMPRESS_FLAG --retention=$RETENTION_DAYS

    - name: List backup files
      run: php scripts/database-backup.php --list

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Commit and push backup
      run: |
        git add storage/backups/
        
        # Check if there are any changes to commit
        if git diff --staged --quiet; then
          echo "No new backup files to commit"
        else
          BACKUP_DATE=$(date '+%Y-%m-%d %H:%M:%S UTC')
          git commit -m "Database backup - $BACKUP_DATE"
          git push
          echo "Backup committed and pushed successfully"
        fi

    - name: Create backup summary
      run: |
        echo "## Database Backup Summary" > backup-summary.md
        echo "**Date:** $(date '+%Y-%m-%d %H:%M:%S UTC')" >> backup-summary.md
        echo "**Database:** ${{ secrets.DB_DATABASE }}" >> backup-summary.md
        echo "" >> backup-summary.md
        echo "### Available Backups:" >> backup-summary.md
        php scripts/database-backup.php --list >> backup-summary.md

    - name: Upload backup summary as artifact
      uses: actions/upload-artifact@v4
      with:
        name: backup-summary-${{ github.run_number }}
        path: backup-summary.md
        retention-days: 30
