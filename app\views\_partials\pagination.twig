{# Bootstrap 5 Pagination Component #}
{% if total_pages > 1 %}
<nav aria-label="Page navigation">
    <ul class="pagination">
        {# Previous Page Link #}
        {% if current_page > 1 %}
            <li class="page-item">
                <a class="page-link" href="{{ base_url }}?page={{ current_page - 1 }}" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </span>
            </li>
        {% endif %}
        
        {# Page Numbers #}
        {% set start_page = max(1, current_page - 2) %}
        {% set end_page = min(total_pages, current_page + 2) %}
        
        {# First page if not in range #}
        {% if start_page > 1 %}
            <li class="page-item">
                <a class="page-link" href="{{ base_url }}?page=1">1</a>
            </li>
            {% if start_page > 2 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endif %}
        
        {# Page number links #}
        {% for page in start_page..end_page %}
            {% if page == current_page %}
                <li class="page-item active" aria-current="page">
                    <span class="page-link">{{ page }}</span>
                </li>
            {% else %}
                <li class="page-item">
                    <a class="page-link" href="{{ base_url }}?page={{ page }}">{{ page }}</a>
                </li>
            {% endif %}
        {% endfor %}
        
        {# Last page if not in range #}
        {% if end_page < total_pages %}
            {% if end_page < total_pages - 1 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
            <li class="page-item">
                <a class="page-link" href="{{ base_url }}?page={{ total_pages }}">{{ total_pages }}</a>
            </li>
        {% endif %}
        
        {# Next Page Link #}
        {% if current_page < total_pages %}
            <li class="page-item">
                <a class="page-link" href="{{ base_url }}?page={{ current_page + 1 }}" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        {% else %}
            <li class="page-item disabled">
                <span class="page-link" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </span>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}