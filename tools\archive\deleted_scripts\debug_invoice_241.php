<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
.diff { background: yellow; }
</style>';

echo "<h1>Debug Invoice #241 (FAC-2025-0187)</h1>";

// Get invoice using model method (what PDF uses)
$invoice = new Invoice();
$invoiceData = $invoice->getInvoiceWithDetails(241);

if (!$invoiceData) {
    echo '<div class="error">Invoice 241 not found!</div>';
    exit;
}

echo '<div class="section">';
echo '<h2>1. Invoice Basic Info</h2>';
echo '<table>';
echo '<tr><th>Field</th><th>Value</th></tr>';
echo '<tr><td>ID</td><td>' . $invoiceData['id'] . '</td></tr>';
echo '<tr><td>Invoice Number</td><td>' . $invoiceData['invoice_number'] . '</td></tr>';
echo '<tr><td>Status</td><td>' . $invoiceData['status'] . '</td></tr>';
echo '<tr><td>Issue Date</td><td>' . $invoiceData['issue_date'] . '</td></tr>';
echo '</table>';
echo '</div>';

// Check what edit view would see
echo '<div class="section">';
echo '<h2>2. What Edit View Sees (lines renamed to items)</h2>';
if (isset($invoiceData['lines']) && !empty($invoiceData['lines'])) {
    $items = $invoiceData['lines'];
    echo '<p>Items count: ' . count($items) . '</p>';
    echo '<table>';
    echo '<tr><th>Index</th><th>ID</th><th>Description</th><th>Qty</th><th>Price</th><th>VAT</th><th>Sort Order</th></tr>';
    foreach ($items as $index => $item) {
        echo '<tr>';
        echo '<td>' . $index . '</td>';
        echo '<td>' . $item['id'] . '</td>';
        echo '<td>' . htmlspecialchars($item['description']) . '</td>';
        echo '<td>' . $item['quantity'] . '</td>';
        echo '<td>' . $item['unit_price'] . '</td>';
        echo '<td>' . $item['vat_rate'] . '%</td>';
        echo '<td>' . ($item['sort_order'] ?? 'NULL') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}
echo '</div>';

// Check what PDF would see
echo '<div class="section">';
echo '<h2>3. What PDF Sees (invoice lines)</h2>';
echo '<p>Lines array exists: ' . (isset($invoiceData['lines']) ? 'Yes' : 'No') . '</p>';
echo '<p>Lines count: ' . (isset($invoiceData['lines']) ? count($invoiceData['lines']) : 0) . '</p>';
if (isset($invoiceData['lines'])) {
    echo '<table>';
    echo '<tr><th>Index</th><th>ID</th><th>Description</th><th>Qty</th><th>Price</th><th>VAT</th><th>Sort Order</th></tr>';
    foreach ($invoiceData['lines'] as $index => $line) {
        echo '<tr>';
        echo '<td>' . $index . '</td>';
        echo '<td>' . $line['id'] . '</td>';
        echo '<td>' . htmlspecialchars($line['description']) . '</td>';
        echo '<td>' . $line['quantity'] . '</td>';
        echo '<td>' . $line['unit_price'] . '</td>';
        echo '<td>' . $line['vat_rate'] . '%</td>';
        echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
}
echo '</div>';

// Raw database check
echo '<div class="section">';
echo '<h2>4. Raw Database Check</h2>';
$stmt = $db->prepare("
    SELECT * FROM invoice_lines 
    WHERE invoice_id = ? 
    ORDER BY sort_order ASC, id ASC
");
$stmt->execute([241]);
$dbLines = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<p>Database lines count: ' . count($dbLines) . '</p>';
echo '<table>';
echo '<tr><th>ID</th><th>Description</th><th>Qty</th><th>Price</th><th>VAT</th><th>Sort Order</th><th>Line Total</th></tr>';
foreach ($dbLines as $line) {
    echo '<tr>';
    echo '<td>' . $line['id'] . '</td>';
    echo '<td>' . htmlspecialchars($line['description']) . '</td>';
    echo '<td>' . $line['quantity'] . '</td>';
    echo '<td>' . $line['unit_price'] . '</td>';
    echo '<td>' . $line['vat_rate'] . '%</td>';
    echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
    echo '<td>' . $line['line_total'] . '</td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';

// Check for duplicates
echo '<div class="section">';
echo '<h2>5. Duplicate Analysis</h2>';
$descriptions = array_column($dbLines, 'description');
$uniqueDescriptions = array_unique($descriptions);

if (count($descriptions) !== count($uniqueDescriptions)) {
    echo '<div class="warning">';
    echo '<strong>⚠️ Duplicate descriptions found!</strong><br>';
    $counts = array_count_values($descriptions);
    foreach ($counts as $desc => $count) {
        if ($count > 1) {
            echo "- \"$desc\" appears $count times<br>";
        }
    }
    echo '</div>';
} else {
    echo '<div class="success">✓ No duplicate descriptions found</div>';
    
    // Check if all descriptions are the same
    if (count($uniqueDescriptions) == 1 && count($descriptions) > 1) {
        echo '<div class="warning">';
        echo '<strong>⚠️ All lines have the same description!</strong><br>';
        echo 'Description: "' . htmlspecialchars($descriptions[0]) . '"<br>';
        echo 'This may appear as duplicates in the PDF even though they are separate lines.';
        echo '</div>';
    }
}
echo '</div>';

// Check sequence issue
echo '<div class="section">';
echo '<h2>6. Invoice Numbering Issue</h2>';
$stmt = $db->query("
    SELECT * FROM document_sequences 
    WHERE document_type_id = (SELECT id FROM document_types WHERE code = 'invoice') 
    AND year = 2025
");
$sequences = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<p>Current sequences for 2025:</p>';
echo '<table>';
echo '<tr><th>ID</th><th>Year</th><th>Month</th><th>Last Number</th><th>Updated</th></tr>';
foreach ($sequences as $seq) {
    echo '<tr>';
    echo '<td>' . $seq['id'] . '</td>';
    echo '<td>' . $seq['year'] . '</td>';
    echo '<td>' . ($seq['month'] ?? 'NULL') . '</td>';
    echo '<td>' . $seq['last_number'] . '</td>';
    echo '<td>' . $seq['updated_at'] . '</td>';
    echo '</tr>';
}
echo '</table>';

echo '<div class="warning">';
echo '<strong>To fix the numbering:</strong><br>';
echo '1. Run <a href="/fit/public/force_reset_sequence.php">force_reset_sequence.php</a> to reset to 185<br>';
echo '2. The invoice number field is readonly - we need to make it editable for draft invoices';
echo '</div>';
echo '</div>';

// Summary
echo '<div class="section">';
echo '<h2>Summary & Recommendations</h2>';
echo '<ul>';
echo '<li><strong>Lines Display:</strong> The edit view and PDF both see the same data (' . count($dbLines) . ' lines)</li>';
if (count($uniqueDescriptions) == 1 && count($descriptions) > 1) {
    echo '<li class="text-warning"><strong>Issue Found:</strong> All lines have identical descriptions, which may appear as duplicates in the PDF</li>';
}
echo '<li><strong>Invoice Number:</strong> Currently readonly in edit form - needs to be editable for drafts</li>';
echo '<li><strong>Sequence:</strong> Last number is ' . ($sequences[0]['last_number'] ?? 'unknown') . ', next will be ' . ($sequences[0]['last_number'] + 1 ?? 'unknown') . '</li>';
echo '</ul>';
echo '</div>';