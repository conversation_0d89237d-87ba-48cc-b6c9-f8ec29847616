{% extends "base-modern.twig" %}

{% block title %}{{ __('config.translation_editor') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.translation_editor') }}</h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#exportModal">
                <i class="bi bi-download me-2"></i>{{ __('translations.export') }}
            </button>
            <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#importModal">
                <i class="bi bi-upload me-2"></i>{{ __('translations.import') }}
            </button>
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
        </div>
    </div>

    <!-- Language and File Selection -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/translations" class="row g-3">
                <div class="col-md-3">
                    <label for="language" class="form-label">{{ __('translations.language') }}</label>
                    <select class="form-select" id="language" name="lang" onchange="this.form.submit()">
                        <option value="fr" {{ current_language == 'fr' ? 'selected' : '' }}>Français</option>
                        <option value="en" {{ current_language == 'en' ? 'selected' : '' }}>English</option>
                        <option value="de" {{ current_language == 'de' ? 'selected' : '' }}>Deutsch</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="file" class="form-label">{{ __('translations.translation_file') }}</label>
                    <select class="form-select" id="file" name="file" onchange="this.form.submit()">
                        {% for file in translation_files %}
                            <option value="{{ file }}" {{ current_file == file ? 'selected' : '' }}>{{ file }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-md-4">
                    <label for="search" class="form-label">{{ __('common.search') }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_term }}" placeholder="{{ __('translations.search_placeholder') }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
                
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="show_missing" name="missing_only" 
                               value="1" {{ missing_only ? 'checked' : '' }} onchange="this.form.submit()">
                        <label class="form-check-label" for="show_missing">
                            {{ __('translations.show_missing_only') }}
                        </label>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Translation Statistics -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                        {{ __('translations.total_keys') }}
                    </div>
                    <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_keys|default(0) }}</div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="text-xs fw-bold text-success text-uppercase mb-1">
                        {{ __('translations.translated') }}
                    </div>
                    <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.translated|default(0) }}</div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                        {{ __('translations.missing') }}
                    </div>
                    <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.missing|default(0) }}</div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="text-xs fw-bold text-info text-uppercase mb-1">
                        {{ __('translations.completion_rate') }}
                    </div>
                    <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.completion_rate|default(0) }}%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Translations Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ current_file }} - {{ current_language|upper }}</h6>
        </div>
        <div class="card-body">
            <form method="POST" action="{{ base_url }}/translations/save" id="translationsForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="language" value="{{ current_language }}">
                <input type="hidden" name="file" value="{{ current_file }}">
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="30%">{{ __('translations.key') }}</th>
                                <th width="35%">{{ __('translations.reference') }} (FR)</th>
                                <th width="35%">{{ __('translations.translation') }} ({{ current_language|upper }})</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for key, reference in translations %}
                            <tr class="{{ not translation_values[key] ? 'table-warning' : '' }}">
                                <td>
                                    <code>{{ key }}</code>
                                    {% if not translation_values[key] %}
                                        <i class="bi bi-exclamation-triangle text-warning ms-2" 
                                           title="{{ __('translations.missing_translation') }}"></i>
                                    {% endif %}
                                </td>
                                <td class="text-muted">{{ reference }}</td>
                                <td>
                                    {% if reference is iterable and reference is not string %}
                                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                                onclick="editArray('{{ key }}')">
                                            <i class="bi bi-list-ul me-1"></i>{{ __('translations.edit_array') }}
                                        </button>
                                    {% else %}
                                        <input type="text" class="form-control form-control-sm" 
                                               name="translations[{{ key }}]" 
                                               value="{{ translation_values[key] }}" 
                                               placeholder="{{ reference }}">
                                    {% endif %}
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center py-4 text-muted">
                                    {{ __('translations.no_translations_found') }}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                {% if translations|length > 0 %}
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-2"></i>{{ __('common.save_changes') }}
                    </button>
                    <button type="button" class="btn btn-secondary" onclick="resetForm()">
                        <i class="bi bi-arrow-counterclockwise me-2"></i>{{ __('common.reset') }}
                    </button>
                </div>
                {% endif %}
            </form>
        </div>
    </div>
</div>

<!-- Export Modal -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/translations/export">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('translations.export_translations') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">{{ __('translations.select_languages') }}</label>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="export_all" checked>
                            <label class="form-check-label" for="export_all">
                                {{ __('common.select_all') }}
                            </label>
                        </div>
                        <hr>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input export-lang" name="languages[]" 
                                   value="fr" id="export_fr" checked>
                            <label class="form-check-label" for="export_fr">Français</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input export-lang" name="languages[]" 
                                   value="en" id="export_en" checked>
                            <label class="form-check-label" for="export_en">English</label>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input export-lang" name="languages[]" 
                                   value="de" id="export_de" checked>
                            <label class="form-check-label" for="export_de">Deutsch</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="export_format" class="form-label">{{ __('translations.export_format') }}</label>
                        <select class="form-select" id="export_format" name="format">
                            <option value="json">JSON</option>
                            <option value="csv">CSV</option>
                            <option value="xlsx">Excel (XLSX)</option>
                        </select>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-download me-2"></i>{{ __('translations.export') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/translations/import" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('translations.import_translations') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('translations.import_warning') }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="import_file" class="form-label">{{ __('translations.select_file') }} *</label>
                        <input type="file" class="form-control" id="import_file" name="file" 
                               accept=".json,.csv,.xlsx" required>
                        <small class="text-muted">{{ __('translations.supported_formats') }}: JSON, CSV, XLSX</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="import_mode" class="form-label">{{ __('translations.import_mode') }}</label>
                        <select class="form-select" id="import_mode" name="mode">
                            <option value="merge">{{ __('translations.merge_existing') }}</option>
                            <option value="overwrite">{{ __('translations.overwrite_existing') }}</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="create_backup" name="create_backup" 
                               value="1" checked>
                        <label class="form-check-label" for="create_backup">
                            {{ __('translations.create_backup') }}
                        </label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-upload me-2"></i>{{ __('translations.import') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Select all languages for export
document.getElementById('export_all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.export-lang');
    checkboxes.forEach(cb => cb.checked = this.checked);
});

// Auto-save translations
let saveTimeout;
const inputs = document.querySelectorAll('input[name^="translations["]');
inputs.forEach(input => {
    input.addEventListener('input', function() {
        clearTimeout(saveTimeout);
        saveTimeout = setTimeout(() => {
            // Auto-save via AJAX
            const formData = new FormData();
            formData.append('csrf_token', '{{ csrf_token }}');
            formData.append('language', '{{ current_language }}');
            formData.append('file', '{{ current_file }}');
            formData.append('key', this.name.match(/\[(.+)\]/)[1]);
            formData.append('value', this.value);
            
            fetch('{{ base_url }}/translations/save-single', {
                method: 'POST',
                body: formData
            }).then(response => {
                if (response.ok) {
                    // Show success indicator
                    this.classList.add('is-valid');
                    setTimeout(() => {
                        this.classList.remove('is-valid');
                    }, 2000);
                }
            });
        }, 1000);
    });
});

// Edit array translations
function editArray(key) {
    window.location.href = '{{ base_url }}/translations/edit-array?lang={{ current_language }}&file={{ current_file }}&key=' + encodeURIComponent(key);
}

// Reset form
function resetForm() {
    document.getElementById('translationsForm').reset();
}

// Mark form as changed
let formChanged = false;
inputs.forEach(input => {
    input.addEventListener('change', () => {
        formChanged = true;
    });
});

// Warn before leaving if changes not saved
window.addEventListener('beforeunload', (e) => {
    if (formChanged) {
        e.preventDefault();
        e.returnValue = '';
    }
});

// Reset flag on form submit
document.getElementById('translationsForm').addEventListener('submit', () => {
    formChanged = false;
});
</script>
{% endblock %}