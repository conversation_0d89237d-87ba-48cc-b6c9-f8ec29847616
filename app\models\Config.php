<?php

namespace App\Models;

use App\Core\Model;

class Config extends Model
{
    protected $table = 'config';
    
    protected $fillable = [
        'key',
        'value',
        'category',
        'description'
    ];
    
    /**
     * Get a config value by key
     */
    public static function getValue($key, $default = null)
    {
        try {
            $db = self::db();
            $stmt = $db->prepare("SELECT value FROM config WHERE `key` = :key LIMIT 1");
            $stmt->execute(['key' => $key]);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            return $result ? $result['value'] : $default;
        } catch (\Exception $e) {
            return $default;
        }
    }
    
    /**
     * Set a config value
     */
    public static function setValue($key, $value, $category = 'general')
    {
        try {
            $db = self::db();
            $stmt = $db->prepare("INSERT INTO config (`key`, `value`, `category`) VALUES (:key, :value, :category) 
                                  ON DUPLICATE KEY UPDATE `value` = :value2, `category` = :category2");
            return $stmt->execute([
                'key' => $key,
                'value' => $value,
                'category' => $category,
                'value2' => $value,
                'category2' => $category
            ]);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Get all configs by category
     */
    public static function getByCategory($category)
    {
        try {
            $db = self::db();
            $stmt = $db->prepare("SELECT * FROM config WHERE `category` = :category");
            $stmt->execute(['category' => $category]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\Exception $e) {
            return [];
        }
    }
}