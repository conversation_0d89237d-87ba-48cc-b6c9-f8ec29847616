<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Models\User;
use App\Models\UserGroup;
use App\Models\UserCourse;
use Flight;

class UserController extends Controller
{
    /**
     * Display users list
     */
    public function index(Request $request, Response $response)
    {
        $users = User::getAllWithGroups();
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'users/index-modern';
        
        return $this->render($viewName, [
            'title' => __('users.user_management'),
            'users' => $users
        ]);
    }
    
    /**
     * Show create user form
     */
    public function create(Request $request, Response $response)
    {
        $groups = UserGroup::getAll();
        
        $template = $this->getTemplate();
        $viewName = 'users/form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.create_user'),
            'user' => null,
            'groups' => $groups,
            'userGroups' => []
        ]);
    }
    
    /**
     * Store new user
     */
    public function store(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            $data = $_POST;
            
            // Debug: Log the incoming data
            error_log('User create - Raw POST data: ' . json_encode($data));
            
            // Validate required fields (username will be generated if empty)
            $required = ['email', 'password', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Generate username if not provided
            if (empty($data['username'])) {
                // Clean the first name
                $cleanFirstName = strtolower($data['first_name']);
                // Remove accents
                $cleanFirstName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanFirstName);
                // Remove special characters and spaces
                $cleanFirstName = preg_replace('/[^a-z0-9]/', '', $cleanFirstName);
                
                // Clean the last name for potential use
                $cleanLastName = strtolower($data['last_name']);
                $cleanLastName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanLastName);
                $cleanLastName = preg_replace('/[^a-z0-9]/', '', $cleanLastName);
                
                if (!empty($cleanFirstName)) {
                    // Try first name only
                    $username = $cleanFirstName;
                    
                    // Check if username exists
                    if (User::findByUsername($username)) {
                        // Try first name + first letter of last name
                        if (!empty($cleanLastName)) {
                            $username = $cleanFirstName . substr($cleanLastName, 0, 1);
                            
                            // If still exists, add numbers
                            $counter = 2;
                            $baseUsername = $username;
                            while (User::findByUsername($username)) {
                                $username = $baseUsername . $counter;
                                $counter++;
                            }
                        } else {
                            // No last name, just add numbers
                            $counter = 2;
                            while (User::findByUsername($username)) {
                                $username = $cleanFirstName . $counter;
                                $counter++;
                            }
                        }
                    }
                    
                    $data['username'] = $username;
                }
            }
            
            // Validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new \Exception(__('users.username_format'));
            }
            
            // Validate username length
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new \Exception(__('users.username_length'));
            }
            
            // Generate default email if not provided or not @fit-360.lu
            if (empty($data['email']) || !str_ends_with($data['email'], '@fit-360.lu')) {
                // Clean the first name
                $cleanName = strtolower($data['first_name']);
                // Remove accents
                $cleanName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanName);
                // Remove special characters and spaces
                $cleanName = preg_replace('/[^a-z0-9]/', '', $cleanName);
                
                if (!empty($cleanName)) {
                    // Check for uniqueness
                    $baseEmail = $cleanName . '@fit-360.lu';
                    $email = $baseEmail;
                    $counter = 1;
                    
                    while (User::findByEmail($email)) {
                        $counter++;
                        $email = $cleanName . $counter . '@fit-360.lu';
                    }
                    
                    $data['email'] = $email;
                }
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('users.invalid_email'));
            }
            
            // Validate password length
            if (strlen($data['password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Set defaults only if the fields are truly empty
            $data['language'] = (!empty($data['language'])) ? $data['language'] : 'fr';
            $data['timezone'] = (!empty($data['timezone'])) ? $data['timezone'] : 'Europe/Luxembourg';
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;
            
            // Only set address defaults if the fields are not provided or truly empty
            if (!isset($data['address']) || $data['address'] === '') {
                $data['address'] = '15, am Pëtz';
            }
            if (!isset($data['postal_code']) || $data['postal_code'] === '') {
                $data['postal_code'] = 'L-9579';
            }
            if (!isset($data['city']) || $data['city'] === '') {
                $data['city'] = 'Weidingen';
            }
            if (!isset($data['country']) || $data['country'] === '') {
                $data['country'] = 'LU';
            }
            
            // Debug: Log processed data
            error_log('User create - Processed data: ' . json_encode([
                'address' => $data['address'] ?? 'null',
                'postal_code' => $data['postal_code'] ?? 'null', 
                'city' => $data['city'] ?? 'null',
                'country' => $data['country'] ?? 'null'
            ]));
            
            // Handle avatar upload
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $data['avatar'] = $this->uploadAvatar($_FILES['avatar']);
            }
            
            // Create user
            $userId = User::create($data);
            
            // Assign groups
            if (!empty($data['groups'])) {
                User::syncGroups($userId, $data['groups']);
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.user_created'),
                'data' => ['id' => $userId]
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(Request $request, Response $response, $id)
    {
        $user = User::getWithGroups($id);
        
        if (!$user) {
            Flight::redirect('{{ base_url }}/users');
            return;
        }
        
        $groups = UserGroup::getAll();
        $userGroupIds = array_column($user['groups'], 'id');
        
        // Get active retrocession settings for user
        try {
            $retrocessionSettings = \App\Models\UserRetrocessionSetting::getActiveSettingsForUser($id);
        } catch (\Exception $e) {
            // Table might not exist, set to null
            $retrocessionSettings = null;
        }
        
        // Get financial obligations for user
        $financialObligations = User::getCurrentFinancialObligations($id);
        
        // Check if current user can edit financial obligations
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $canEditFinancial = User::canEditFinancialObligations($currentUserId);
        
        
        $template = $this->getTemplate();
        $viewName = 'users/form-modern';
        
        // Find Kiné group ID
        $kineGroupId = null;
        foreach ($groups as $group) {
            if ($group['name'] === 'Kiné') {
                $kineGroupId = $group['id'];
                break;
            }
        }
        
        return $this->render($viewName, [
            'title' => __('users.edit_user'),
            'user' => $user,
            'groups' => $groups,
            'userGroups' => $userGroupIds,
            'retrocession_settings' => $retrocessionSettings,
            'financial_obligations' => $financialObligations,
            'can_edit_financial' => $canEditFinancial,
            'currency' => 'EUR',
            'kine_group_id' => $kineGroupId
        ]);
    }
    
    /**
     * Update user
     */
    public function update(Request $request, Response $response, $id)
    {
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
        }
        
        try {
            $data = $_POST;
            
            // Validate required fields
            $required = ['username', 'email', 'first_name', 'last_name'];
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Validate username format
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
                throw new \Exception(__('users.username_format'));
            }
            
            // Validate username length
            if (strlen($data['username']) < 3 || strlen($data['username']) > 50) {
                throw new \Exception(__('users.username_length'));
            }
            
            // Validate email format
            if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('users.invalid_email'));
            }
            
            // If password is provided, validate it
            if (!empty($data['password']) && strlen($data['password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Set defaults for update - don't override existing values with defaults
            $data['language'] = (!empty($data['language'])) ? $data['language'] : 'fr';
            $data['timezone'] = (!empty($data['timezone'])) ? $data['timezone'] : 'Europe/Luxembourg';
            $data['is_active'] = isset($data['is_active']) ? 1 : 0;
            $data['is_intracommunity'] = isset($data['is_intracommunity']) ? 1 : 0;
            $data['exclude_patient_line_default'] = isset($data['exclude_patient_line_default']) ? 1 : 0;
            
            // For update, only set address fields if they are provided in the form
            // Don't set defaults as user might want to clear them
            
            // Handle avatar upload
            if (isset($_FILES['avatar']) && $_FILES['avatar']['error'] === UPLOAD_ERR_OK) {
                $data['avatar'] = $this->uploadAvatar($_FILES['avatar'], $id);
            }
            
            // Handle avatar removal
            if (isset($data['remove_avatar']) && $data['remove_avatar']) {
                $this->removeAvatar($id);
                $data['avatar'] = null;
            }
            
            // Update user
            User::update($id, $data);
            
            // Update groups
            $groups = $data['groups'] ?? [];
            User::syncGroups($id, $groups);
            
            // Handle retrocession settings if provided
            if (isset($data['retrocession']) && is_array($data['retrocession'])) {
                $retrocession = $data['retrocession'];
                
                // Validate retrocession data
                if (!empty($retrocession['valid_from'])) {
                    // Check for date overlap
                    $validTo = $retrocession['valid_to'] ?: null;
                    if (!\App\Models\UserRetrocessionSetting::validateDateOverlap($id, $retrocession['valid_from'], $validTo)) {
                        throw new \Exception(__('users.retrocession_date_overlap'));
                    }
                    
                    // Prepare retrocession data
                    $retrocessionData = [
                        'user_id' => $id,
                        'cns_type' => $retrocession['cns_type'] ?? 'percentage',
                        'cns_value' => floatval($retrocession['cns_value'] ?? 20),
                        'patient_type' => $retrocession['patient_type'] ?? 'percentage',
                        'patient_value' => floatval($retrocession['patient_value'] ?? 20),
                        'secretary_type' => $retrocession['secretary_type'] ?? 'percentage',
                        'secretary_value' => floatval($retrocession['secretary_value'] ?? 10),
                        'ceiling_enabled' => isset($retrocession['ceiling_enabled']) ? 1 : 0,
                        'ceiling_amount' => floatval($retrocession['ceiling_amount'] ?? 5000),
                        'valid_from' => $retrocession['valid_from'],
                        'valid_to' => $retrocession['valid_to'] ?: null,
                        'notes' => $retrocession['notes'] ?? null,
                        'created_by' => $_SESSION['user_id'],
                        'is_active' => 1
                    ];
                    
                    // Create new retrocession setting
                    \App\Models\UserRetrocessionSetting::create($retrocessionData);
                }
            }
            
            // Handle course management
            if (isset($data['courses']) && is_array($data['courses'])) {
                $this->handleCourseUpdates($id, $data['courses'], $data['is_intracommunity'] ?? false);
            }
            
            // If intracommunity status changed, update all existing course VAT rates
            $oldUser = User::find($id);
            if ($oldUser && ($oldUser['is_intracommunity'] ? 1 : 0) != ($data['is_intracommunity'] ? 1 : 0)) {
                UserCourse::updateVATRatesForUser($id, $data['is_intracommunity'] ? true : false);
            }
            
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => __('users.user_updated')
                ]);
                exit;
            } else {
                // Set flash message and redirect back to edit page
                $_SESSION['flash']['success'] = __('users.user_updated');
                Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/edit');
                exit;
            }
        } catch (\Exception $e) {
            if ($isAjax) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                // Set flash error message and redirect back to edit form
                $_SESSION['flash']['error'] = $e->getMessage();
                Flight::redirect(Flight::get('flight.base_url') . '/users/' . $id . '/edit');
                exit;
            }
        }
    }
    
    /**
     * Delete user
     */
    public function delete(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Don't allow self-deletion
            if ($id == $_SESSION['user_id']) {
                throw new \Exception(__('users.cannot_delete_self'));
            }
            
            User::delete($id);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.user_deleted')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Toggle user active status
     */
    public function toggleActive(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Don't allow self-deactivation
            if ($id == $_SESSION['user_id']) {
                throw new \Exception(__('users.cannot_deactivate_self'));
            }
            
            User::toggleActive($id);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.status_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Show user profile
     */
    public function profile(Request $request, Response $response, $id = null)
    {
        // If no ID provided, show current user's profile
        if (!$id) {
            $id = $_SESSION['user_id'];
        }
        
        $user = User::getWithGroups($id);
        
        if (!$user) {
            Flight::redirect('{{ base_url }}/users');
            return;
        }
        
        $permissions = User::getPermissions($id);
        
        // Organize permissions by category
        $permissionsByCategory = [];
        foreach ($permissions as $permission) {
            $category = $permission['category'];
            if (!isset($permissionsByCategory[$category])) {
                $permissionsByCategory[$category] = [];
            }
            $permissionsByCategory[$category][] = $permission;
        }
        
        // Check if current user can edit this profile
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $currentUser = User::find($currentUserId);
        $canEdit = false;
        
        if ($currentUser) {
            // Can edit if: own profile OR current user is admin/manager
            $canEdit = ($id == $currentUserId) || 
                       User::isAdmin($currentUserId) || 
                       User::isManager($currentUserId);
        }
        
        $template = $this->getTemplate();
        $viewName = 'users/profile-modern';
        
        return $this->render($viewName, [
            'title' => __('users.user_profile'),
            'user' => $user,
            'permissions' => $permissionsByCategory,
            'isOwnProfile' => ($id == $_SESSION['user_id']),
            'canEdit' => $canEdit,
            'currentUser' => $currentUser
        ]);
    }
    
    /**
     * Upload avatar
     */
    private function uploadAvatar($file, $userId = null)
    {
        // Validate file type
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file['type'], $allowedTypes)) {
            throw new \Exception(__('users.invalid_file_type'));
        }
        
        // Validate file size (max 5MB)
        $maxSize = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $maxSize) {
            throw new \Exception(__('users.file_size_exceeded'));
        }
        
        // Create upload directory if it doesn't exist
        $uploadDir = dirname(__DIR__, 2) . '/public/uploads/avatars';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Generate unique filename
        $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $filename = uniqid('avatar_') . '.' . $extension;
        $uploadPath = $uploadDir . '/' . $filename;
        
        // Move uploaded file
        if (move_uploaded_file($file['tmp_name'], $uploadPath)) {
            // If updating, remove old avatar
            if ($userId) {
                $this->removeAvatar($userId);
            }
            
            // Return the web path
            return '{{ base_url }}/uploads/avatars/' . $filename;
        } else {
            throw new \Exception(__('users.upload_failed'));
        }
    }
    
    /**
     * Remove avatar
     */
    private function removeAvatar($userId)
    {
        $user = User::find($userId);
        if ($user && $user['avatar']) {
            $filePath = dirname(__DIR__, 2) . '/public' . str_replace('{{ base_url }}', '', $user['avatar']);
            if (file_exists($filePath)) {
                unlink($filePath);
            }
        }
    }
    
    /**
     * Get retrocession history for a user
     */
    public function retrocessionHistory(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check if user exists
            $user = User::find($id);
            if (!$user) {
                throw new \Exception(__('users.user_not_found'));
            }
            
            // Get retrocession history
            try {
                $history = \App\Models\UserRetrocessionSetting::getAllForUser($id);
            } catch (\Exception $e) {
                $history = [];
            }
            
            echo json_encode([
                'success' => true,
                'history' => $history
            ]);
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Show financial obligations for a user
     */
    public function financialObligations(Request $request, Response $response, $id)
    {
        $user = User::find($id);
        if (!$user) {
            Flight::redirect('/users');
            return;
        }
        
        // Check if current user can edit
        $currentUserId = $_SESSION['user_id'] ?? 0;
        $canEdit = User::canEditFinancialObligations($currentUserId);
        
        // Get current and historical obligations
        $currentObligations = User::getCurrentFinancialObligations($id);
        $obligationsHistory = User::getFinancialObligationsHistory($id);
        
        $template = $this->getTemplate();
        $viewName = 'users/financial-obligations-modern';
        
        return $this->render($viewName, [
            'title' => __('users.financial_obligations') . ' - ' . $user['first_name'] . ' ' . $user['last_name'],
            'user' => $user,
            'currentObligations' => $currentObligations,
            'obligationsHistory' => $obligationsHistory,
            'canEdit' => $canEdit,
            'currency' => 'EUR'
        ]);
    }
    
    /**
     * Update financial obligations for a user
     */
    public function updateFinancialObligations(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $currentUserId = $_SESSION['user_id'] ?? 0;
            
            // Check permissions
            if (!User::canEditFinancialObligations($currentUserId)) {
                throw new \Exception(__('users.unauthorized_financial_edit'));
            }
            
            $data = $_POST;
            
            // Validate required fields
            $required = ['rent_amount', 'charges_amount', 'effective_date'];
            foreach ($required as $field) {
                if (!isset($data[$field]) || $data[$field] === '') {
                    throw new \Exception(__('users.field_required', ['field' => ucfirst(str_replace('_', ' ', $field))]));
                }
            }
            
            // Calculate total (rent + charges + secretary_tvac_17 only)
            $data['total_tvac'] = floatval($data['rent_amount']) + floatval($data['charges_amount']) + 
                                  floatval($data['secretary_tvac_17'] ?? 0);
            
            // Add created_by
            $data['created_by'] = $currentUserId;
            
            // Update obligations
            User::updateFinancialObligations($id, $data, $currentUserId);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.financial_obligations_updated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Reset password for a user (admin only)
     */
    public function resetPassword(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $currentUserId = $_SESSION['user_id'] ?? 0;
            $currentUser = User::find($currentUserId);
            
            // Check permissions - only admins and managers can reset passwords
            if (!$currentUser || (!User::isAdmin($currentUserId) && !User::isManager($currentUserId))) {
                throw new \Exception(__('users.unauthorized_password_reset'));
            }
            
            // Check if trying to reset own password
            if ($id == $currentUserId) {
                throw new \Exception(__('users.use_change_password_instead'));
            }
            
            $data = $_POST;
            
            // Validate new password
            if (empty($data['new_password'])) {
                throw new \Exception(__('users.new_password_required'));
            }
            
            if (strlen($data['new_password']) < 6) {
                throw new \Exception(__('users.password_length'));
            }
            
            // Validate password confirmation
            if (empty($data['confirm_password'])) {
                throw new \Exception(__('users.confirm_password_required'));
            }
            
            if ($data['new_password'] !== $data['confirm_password']) {
                throw new \Exception(__('users.password_mismatch'));
            }
            
            // Get target user
            $targetUser = User::find($id);
            if (!$targetUser) {
                throw new \Exception(__('users.user_not_found'));
            }
            
            // Update password
            User::update($id, [
                'password' => $data['new_password']
            ]);
            
            // Send email notification if requested
            if (!empty($data['send_email']) && filter_var($targetUser['email'], FILTER_VALIDATE_EMAIL)) {
                // TODO: Implement email notification
                // For now, just log the action
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('users.password_reset_success')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update user preferences (language, theme, etc.)
     */
    public function updatePreferences(Request $request, Response $response)
    {
        try {
            $userId = $_SESSION['user_id'] ?? 0;
            if (!$userId) {
                throw new \Exception(__('common.unauthorized'));
            }
            
            $data = $_POST;
            
            // Only allow updating specific preference fields
            $allowedFields = ['language', 'timezone', 'theme', 'email_notifications'];
            $updateData = [];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateData[$field] = $data[$field];
                }
            }
            
            // Update user preferences
            User::update($userId, $updateData);
            
            // Update session if language or theme changed
            if (isset($updateData['language'])) {
                $_SESSION['user_language'] = $updateData['language'];
            }
            if (isset($updateData['theme'])) {
                $_SESSION['template'] = $updateData['theme'];
            }
            
            // Redirect back to profile with success message
            $_SESSION['flash']['success'] = __('users.preferences_updated');
            Flight::redirect(Flight::get('flight.base_url') . '/profile');
            
        } catch (\Exception $e) {
            $_SESSION['flash']['error'] = $e->getMessage();
            Flight::redirect(Flight::get('flight.base_url') . '/profile');
        }
    }
    
    /**
     * Get courses for a user (AJAX endpoint)
     */
    public function getCourses(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check if user_courses table exists
            $db = Flight::db();
            $stmt = $db->query("SHOW TABLES LIKE 'user_courses'");
            if (!$stmt->fetch()) {
                throw new \Exception("user_courses table does not exist. Please run database migrations.");
            }
            
            $courses = UserCourse::getCoursesForUser($id, false); // Include inactive courses for editing
            
            echo json_encode([
                'success' => true,
                'courses' => $courses
            ]);
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Handle course updates (create, update, delete)
     */
    private function handleCourseUpdates($userId, $coursesData, $isIntracommunity = false)
    {
        try {
            // Get existing courses
            $existingCourses = UserCourse::getCoursesForUser($userId, false);
            $existingIds = array_column($existingCourses, 'id');
            $submittedIds = [];
            
            // Process each submitted course
            foreach ($coursesData as $index => $courseData) {
                // Skip empty course names
                if (empty($courseData['course_name'])) {
                    continue;
                }
                
                // Prepare course data
                $courseInfo = [
                    'user_id' => $userId,
                    'course_name' => trim($courseData['course_name']),
                    'hourly_rate' => floatval($courseData['hourly_rate'] ?? 0),
                    'vat_rate' => $isIntracommunity ? 0.00 : floatval($courseData['vat_rate'] ?? 17.00),
                    'is_active' => true,
                    'display_order' => intval($courseData['display_order'] ?? $index)
                ];
                
                if (!empty($courseData['id']) && is_numeric($courseData['id'])) {
                    // Update existing course
                    $courseId = intval($courseData['id']);
                    UserCourse::updateCourse($courseId, $courseInfo);
                    $submittedIds[] = $courseId;
                } else {
                    // Create new course
                    UserCourse::createCourse($courseInfo);
                }
            }
            
            // Delete courses that were not submitted (removed by user)
            $coursesToDelete = array_diff($existingIds, $submittedIds);
            foreach ($coursesToDelete as $courseId) {
                UserCourse::deleteCourse($courseId);
            }
            
        } catch (\Exception $e) {
            throw new \Exception("Error managing courses: " . $e->getMessage());
        }
    }
}