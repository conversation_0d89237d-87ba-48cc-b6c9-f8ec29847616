<?php
// Force reset sequence to 185 and add unused numbers to deleted pool

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Force Reset Invoice Sequence</h2>";
    
    // Get the invoice document type
    $stmt = $db->query("SELECT * FROM document_types WHERE code = 'invoice'");
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$docType) {
        exit("Error: Invoice document type not found!");
    }
    
    echo "Document type: <strong>" . $docType['code'] . "</strong> (ID: " . $docType['id'] . ")<br><br>";
    
    // Get current sequence
    $stmt = $db->prepare("
        SELECT * FROM document_sequences 
        WHERE document_type_id = ? AND year = 2025
    ");
    $stmt->execute([$docType['id']]);
    $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$sequence) {
        echo "No sequence found for 2025. Creating new one...<br>";
        $stmt = $db->prepare("
            INSERT INTO document_sequences (document_type_id, year, month, last_number) 
            VALUES (?, 2025, NULL, 185)
        ");
        $stmt->execute([$docType['id']]);
        echo "✓ Created sequence at 185. Next invoice will be 0186<br>";
    } else {
        echo "Current sequence: <strong>" . $sequence['last_number'] . "</strong><br>";
        
        // Add numbers 186-190 to deleted pool (except 186 which we want to use)
        echo "<h3>Adding unused numbers to deleted pool</h3>";
        
        for ($num = 187; $num <= $sequence['last_number']; $num++) {
            // Check for each possible prefix pattern
            $patterns = [
                'FAC-2025-' . str_pad($num, 4, '0', STR_PAD_LEFT),
                'FAC-LOY-2025-' . str_pad($num, 4, '0', STR_PAD_LEFT),
                'FAC-RET-2025-' . str_pad($num, 4, '0', STR_PAD_LEFT),
                'FAC-DIV-2025-' . str_pad($num, 4, '0', STR_PAD_LEFT)
            ];
            
            foreach ($patterns as $invoiceNumber) {
                // Check if this invoice number exists
                $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ?");
                $stmt->execute([$invoiceNumber]);
                
                if (!$stmt->fetch()) {
                    // Number doesn't exist, add to deleted pool
                    try {
                        $stmt = $db->prepare("
                            INSERT INTO deleted_invoice_numbers (
                                document_type_id, 
                                invoice_number, 
                                year, 
                                sequence_number, 
                                deleted_by,
                                deleted_at
                            ) VALUES (?, ?, 2025, ?, 1, NOW())
                        ");
                        $stmt->execute([$docType['id'], $invoiceNumber, $num]);
                        echo "✓ Added $invoiceNumber to deleted pool<br>";
                    } catch (Exception $e) {
                        // Might already exist
                        if (strpos($e->getMessage(), 'Duplicate') === false) {
                            echo "! Error adding $invoiceNumber: " . $e->getMessage() . "<br>";
                        }
                    }
                }
            }
        }
        
        // Now reset the sequence to 185
        echo "<h3>Resetting sequence</h3>";
        $stmt = $db->prepare("
            UPDATE document_sequences 
            SET last_number = 185 
            WHERE document_type_id = ? AND year = 2025
        ");
        $stmt->execute([$docType['id']]);
        echo "✓ Reset sequence to 185<br>";
        echo "<h2 style='color: #28a745;'>Next invoice will be: 0186</h2>";
    }
    
    // Show deleted numbers pool
    echo "<h3>Current Deleted Numbers Pool</h3>";
    $stmt = $db->query("
        SELECT * FROM deleted_invoice_numbers 
        WHERE reused_at IS NULL 
        ORDER BY sequence_number ASC
        LIMIT 10
    ");
    
    if ($stmt->rowCount() > 0) {
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Invoice Number</th><th>Sequence</th><th>Available for Reuse</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . $row['invoice_number'] . "</td>";
            echo "<td>" . $row['sequence_number'] . "</td>";
            echo "<td>✓ Yes</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No numbers in deleted pool.</p>";
    }
    
    echo "<br><strong>✓ Done!</strong> The next invoice created will be numbered <strong>0186</strong>";
    echo "<br><br>";
    echo "<a href='/fit/public/invoices/create' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>Create New Invoice</a>";
    echo "<a href='check_invoice_sequences.php' style='font-size: 16px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Check Sequences</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>