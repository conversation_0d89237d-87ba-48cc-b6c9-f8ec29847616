# Duplicate Invoice Lines Check Report

## How to Check for Duplicates

Since we cannot run PHP from command line, you can check for duplicate invoice lines using these methods:

### Method 1: Web Interface (Recommended)
1. Open your browser and navigate to:
   ```
   http://localhost/fit/public/check_duplicates_web.php
   ```
   This will show you:
   - Total statistics
   - Any exact duplicate lines
   - Invoices affected by duplicates
   - Ability to view details of specific invoices

### Method 2: Use Maintenance Tool
1. Navigate to:
   ```
   http://localhost/fit/tools/maintenance/invoice/check_duplicate_invoice_lines.php
   ```
   This provides a detailed analysis of duplicates.

### Method 3: Fix Duplicates
If duplicates are found, you can fix them using:
   ```
   http://localhost/fit/tools/maintenance/invoice/fix_duplicate_invoice_lines.php
   ```
   This tool will:
   - Show all duplicates grouped by invoice
   - Allow you to review before removing
   - Keep the first occurrence and remove subsequent duplicates

## What to Look For

1. **Exact Duplicates**: Lines with identical:
   - Description
   - Quantity
   - Unit Price
   - VAT Rate
   
2. **Affected Invoices**: Invoices that contain duplicate lines

3. **Orphaned Lines**: Invoice lines without a corresponding invoice (data integrity issue)

## Next Steps

1. Run the duplicate check to see current status
2. If duplicates exist, review them carefully (some may be intentional)
3. Use the fix tool to remove unwanted duplicates
4. Consider implementing database constraints to prevent future duplicates