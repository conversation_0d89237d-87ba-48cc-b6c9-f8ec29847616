<?php
/**
 * Restore from backup and apply minimal fix for retrocession VAT display
 */

echo "<pre>";
echo "=== Restoring and Fixing Retrocession VAT Display ===\n\n";

$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';

// Find the most recent backup
$backupPattern = $createFile . '.backup.*';
$backups = glob($backupPattern);
if (empty($backups)) {
    echo "❌ No backup found. Cannot restore.\n";
    echo "The file may have syntax errors that need manual fixing.\n";
    exit;
}

// Sort backups by timestamp (newest first)
rsort($backups);
$latestBackup = $backups[0];

echo "Found backup: " . basename($latestBackup) . "\n";
echo "Restoring from backup...\n";

// Restore from backup
$content = file_get_contents($latestBackup);
file_put_contents($createFile, $content);
echo "✓ Restored from backup\n\n";

// Now apply minimal fix for VAT display
echo "Applying minimal VAT display fix...\n";

// 1. Find the VAT info display section and add retrocession check
$searchPattern = "// Intracommunity VAT status
            if (userData.is_intracommunity || userData.vat_intercommunautaire) {";

if (strpos($content, $searchPattern) !== false) {
    $replaceWith = "// Intracommunity VAT status
            // Check if this is a retrocession invoice
            const invTypeEl = document.getElementById('invoice_type_id');
            const selectedOpt = invTypeEl ? invTypeEl.options[invTypeEl.selectedIndex] : null;
            const typePrefix = selectedOpt ? selectedOpt.getAttribute('data-prefix') : '';
            const isRetroInvoice = window.location.search.includes('type=retrocession') || 
                                  (typePrefix && typePrefix.startsWith('RET'));
            
            if (!isRetroInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {";
    
    $content = str_replace($searchPattern, $replaceWith, $content);
    echo "✓ Added retrocession check for intracommunity VAT\n";
}

// 2. Also update the VAT number display
$vatNumberSearch = "// VAT number
            if (userData.vat_number) {";

if (strpos($content, $vatNumberSearch) !== false) {
    $vatNumberReplace = "// VAT number
            if (!isRetroInvoice && userData.vat_number) {";
    
    $content = str_replace($vatNumberSearch, $vatNumberReplace, $content);
    echo "✓ Added retrocession check for VAT number display\n";
}

// 3. Update the final VAT check
$finalVatSearch = "} else if (userData.vat_number) {";

if (strpos($content, $finalVatSearch) !== false) {
    $finalVatReplace = "} else if (!isRetroInvoice && userData.vat_number) {";
    
    $content = str_replace($finalVatSearch, $finalVatReplace, $content);
    echo "✓ Added retrocession check for standard VAT\n";
}

// Save the fixed content
file_put_contents($createFile, $content);

echo "\n=== Testing the fix ===\n";
// Quick syntax check
$syntaxCheck = shell_exec('php -l ' . escapeshellarg($createFile) . ' 2>&1');
if (strpos($syntaxCheck, 'No syntax errors') !== false) {
    echo "✓ PHP syntax check passed\n";
} else {
    echo "⚠ PHP syntax check: " . $syntaxCheck . "\n";
}

echo "\n✅ Minimal fix applied successfully!\n";
echo "\nThe retrocession invoice creation should now:\n";
echo "- Hide VAT information for RET type invoices\n";
echo "- Work without JavaScript errors\n";
echo "- Display cleanly for practitioners without VAT numbers\n";

echo "</pre>";