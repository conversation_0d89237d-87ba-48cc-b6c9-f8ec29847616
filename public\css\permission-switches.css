/* ========================================
   PERMISSION SWITCHES STYLING
   Modern toggle switches for permissions
   ======================================== */

/* Base switch styles */
.permission-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 26px;
    vertical-align: middle;
}

.permission-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.permission-switch .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: all 0.3s ease;
    border-radius: 26px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.permission-switch .slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: all 0.3s ease;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Checked state */
.permission-switch input:checked + .slider {
    background-color: #28a745;
}

.permission-switch input:checked + .slider:before {
    transform: translateX(24px);
}

/* Focus state */
.permission-switch input:focus + .slider {
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

/* Hover state */
.permission-switch:hover .slider {
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(0, 0, 0, 0.05);
}

/* Disabled state */
.permission-switch input:disabled + .slider {
    background-color: #e9ecef;
    cursor: not-allowed;
    opacity: 0.6;
}

.permission-switch input:disabled + .slider:before {
    background-color: #f8f9fa;
}

/* Different sizes */
.permission-switch.switch-sm {
    width: 40px;
    height: 22px;
}

.permission-switch.switch-sm .slider:before {
    height: 16px;
    width: 16px;
}

.permission-switch.switch-sm input:checked + .slider:before {
    transform: translateX(18px);
}

.permission-switch.switch-lg {
    width: 60px;
    height: 30px;
}

.permission-switch.switch-lg .slider:before {
    height: 24px;
    width: 24px;
}

.permission-switch.switch-lg input:checked + .slider:before {
    transform: translateX(30px);
}

/* Color variants */
.permission-switch.switch-primary input:checked + .slider {
    background-color: #007bff;
}

.permission-switch.switch-danger input:checked + .slider {
    background-color: #dc3545;
}

.permission-switch.switch-warning input:checked + .slider {
    background-color: #ffc107;
}

.permission-switch.switch-info input:checked + .slider {
    background-color: #17a2b8;
}

/* Special permission switches */
.permission-switch.switch-special input:checked + .slider {
    background-color: #6f42c1;
}

/* Permission table specific styles */
.permissions-table .permission-switch {
    margin: 0 auto;
    display: block;
}

/* Mobile optimizations */
@media (max-width: 767px) {
    .permission-switch {
        width: 44px;
        height: 24px;
    }
    
    .permission-switch .slider:before {
        height: 18px;
        width: 18px;
    }
    
    .permission-switch input:checked + .slider:before {
        transform: translateX(20px);
    }
    
    /* Larger touch target on mobile */
    .permission-switch {
        padding: 10px;
        margin: -10px;
    }
}

/* Switch with labels */
.switch-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.switch-container .switch-label {
    font-size: 0.875rem;
    color: #6c757d;
    user-select: none;
    cursor: pointer;
}

.switch-container .switch-label.active {
    color: #212529;
    font-weight: 500;
}

/* Animated state indicators */
.permission-switch .slider:after {
    content: '';
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    transition: opacity 0.3s ease;
    font-size: 12px;
    color: white;
    font-weight: bold;
}

.permission-switch .slider:after {
    content: '✗';
    left: 6px;
    opacity: 0.5;
}

.permission-switch input:checked + .slider:after {
    content: '✓';
    left: auto;
    right: 6px;
    opacity: 1;
}

/* Loading state */
.permission-switch.loading .slider {
    background-color: #6c757d;
    cursor: wait;
}

.permission-switch.loading .slider:before {
    animation: switch-pulse 1s infinite;
}

@keyframes switch-pulse {
    0%, 100% {
        transform: translateX(0);
    }
    50% {
        transform: translateX(24px);
    }
}

/* Group toggle switches (for Select All) */
.group-switch {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.25rem 0;
}

.group-switch .permission-switch {
    margin: 0;
}

.group-switch label {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    user-select: none;
}

/* Indeterminate state for partial selection */
.permission-switch input:indeterminate + .slider {
    background-color: #ffc107;
}

.permission-switch input:indeterminate + .slider:before {
    transform: translateX(12px);
}

.permission-switch input:indeterminate + .slider:after {
    content: '—';
    left: 50%;
    transform: translate(-50%, -50%);
    right: auto;
}

/* Accessibility improvements */
.permission-switch input:focus-visible + .slider {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .permission-switch .slider {
        background-color: #495057;
    }
    
    .permission-switch input:checked + .slider {
        background-color: #198754;
    }
    
    .permission-switch .slider:before {
        background-color: #f8f9fa;
    }
}

/* Transitions for batch operations */
.permission-switch.batch-updating .slider {
    transition: all 0.15s ease;
}

.permission-switch.batch-updating .slider:before {
    transition: all 0.15s ease;
}

/* Success feedback animation */
.permission-switch.success .slider {
    animation: switch-success 0.5s ease;
}

@keyframes switch-success {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

/* Mobile accordion specific styles */
.permission-grid .permission-switch {
    width: 100%;
    max-width: 50px;
}

.permission-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.permission-item .permission-switch {
    order: -1;
}

/* Special permissions in dropdown */
.dropdown-menu .permission-switch {
    width: 36px;
    height: 20px;
}

.dropdown-menu .permission-switch .slider:before {
    height: 14px;
    width: 14px;
}

.dropdown-menu .permission-switch input:checked + .slider:before {
    transform: translateX(16px);
}