<?php
/**
 * Test creating a retrocession invoice without patient line
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Services\RetrocessionCalculator;
use App\Models\Client;

try {
    $db = Flight::db();
    
    // Find a practitioner
    $stmt = $db->query("
        SELECT c.id, CONCAT(c.first_name, ' ', c.last_name) as name
        FROM clients c
        WHERE c.client_type = 'practitioner' 
        AND c.is_active = TRUE
        LIMIT 1
    ");
    $practitioner = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$practitioner) {
        echo "No active practitioners found\n";
        exit;
    }
    
    echo "Testing with practitioner: " . $practitioner['name'] . " (ID: " . $practitioner['id'] . ")\n\n";
    
    // Create test data entry for current month
    $month = date('n');
    $year = date('Y');
    
    // Check if entry already exists
    $stmt = $db->prepare("
        SELECT * FROM retrocession_data_entry
        WHERE practitioner_id = :practitioner_id
        AND period_month = :month
        AND period_year = :year
    ");
    $stmt->execute([
        ':practitioner_id' => $practitioner['id'],
        ':month' => $month,
        ':year' => $year
    ]);
    $existing = $stmt->fetch();
    
    if ($existing) {
        echo "Found existing entry for " . $month . "/" . $year . "\n";
        echo "Current exclude_patient_line value: " . ($existing['exclude_patient_line'] ? 'Yes' : 'No') . "\n";
        
        // Update to exclude patient line
        $stmt = $db->prepare("
            UPDATE retrocession_data_entry
            SET exclude_patient_line = 1
            WHERE id = :id
        ");
        $stmt->execute([':id' => $existing['id']]);
        echo "✓ Updated to exclude patient line\n";
    } else {
        // Create new test entry
        $stmt = $db->prepare("
            INSERT INTO retrocession_data_entry (
                practitioner_id, period_month, period_year,
                cns_amount, patient_amount, total_amount,
                exclude_patient_line, status,
                entered_by, entered_at
            ) VALUES (
                :practitioner_id, :month, :year,
                :cns_amount, :patient_amount, :total_amount,
                1, 'draft',
                1, NOW()
            )
        ");
        
        $stmt->execute([
            ':practitioner_id' => $practitioner['id'],
            ':month' => $month,
            ':year' => $year,
            ':cns_amount' => 1500.00,
            ':patient_amount' => 800.00,
            ':total_amount' => 2300.00
        ]);
        
        echo "✓ Created test entry with exclude_patient_line = Yes\n";
    }
    
    // Now test the calculation
    $calculator = new RetrocessionCalculator();
    
    echo "\n=== Testing Invoice Generation Preview ===\n";
    
    // Get the data entry
    $stmt = $db->prepare("
        SELECT * FROM retrocession_data_entry
        WHERE practitioner_id = :practitioner_id
        AND period_month = :month
        AND period_year = :year
    ");
    $stmt->execute([
        ':practitioner_id' => $practitioner['id'],
        ':month' => $month,
        ':year' => $year
    ]);
    $dataEntry = $stmt->fetch();
    
    // Calculate retrocession
    $calculation = $calculator->calculate([
        'cns_amount' => $dataEntry['cns_amount'],
        'patient_amount' => $dataEntry['patient_amount'],
        'cns_percent' => 20,
        'patient_percent' => 20,
        'secretariat_percent' => 10,
        'practitioner_id' => $practitioner['id']
    ]);
    
    echo "\nOriginal Calculation:\n";
    echo "- CNS Amount: €" . number_format($dataEntry['cns_amount'], 2) . "\n";
    echo "- Patient Amount: €" . number_format($dataEntry['patient_amount'], 2) . "\n";
    echo "- CNS Part (20%): €" . number_format($calculation['cns_part'], 2) . "\n";
    echo "- Patient Part (20%): €" . number_format($calculation['patient_part'], 2) . "\n";
    echo "- Secretary Fee (10%): €" . number_format($calculation['secretariat_tvac'], 2) . "\n";
    
    // Show what invoice will contain
    echo "\nInvoice Lines (exclude_patient_line = " . ($dataEntry['exclude_patient_line'] ? 'Yes' : 'No') . "):\n";
    echo "1. Part CNS: €" . number_format($calculation['cns_part'], 2) . "\n";
    
    if (!$dataEntry['exclude_patient_line']) {
        echo "2. Part Patient: €" . number_format($calculation['patient_part'], 2) . "\n";
        echo "3. Frais de secrétariat (TVAC): €" . number_format($calculation['secretariat_tvac'], 2) . "\n";
        echo "\nInvoice Total: €" . number_format($calculation['invoice_total'], 2) . "\n";
    } else {
        echo "2. Frais de secrétariat (TVAC): €" . number_format($calculation['secretariat_tvac'], 2) . "\n";
        $invoiceTotal = $calculation['cns_part'] + $calculation['secretariat_tvac'];
        echo "\nInvoice Total: €" . number_format($invoiceTotal, 2) . " (Patient part excluded)\n";
    }
    
    echo "\n✓ Test completed successfully!\n";
    echo "\nYou can now:\n";
    echo "1. Go to http://localhost/fit/public/retrocession/" . $practitioner['id'] . "/data-entry?month=" . $month . "&year=" . $year . "\n";
    echo "2. Check the 'Exclude patient line' checkbox\n";
    echo "3. Save and generate the invoice\n";
    echo "4. The invoice will only contain CNS part and secretary fees\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}