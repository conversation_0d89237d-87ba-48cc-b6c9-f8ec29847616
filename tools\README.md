# Fit360 AdminDesk Tools

This directory contains maintenance and utility scripts for the Fit360 AdminDesk application.

## Directory Structure

### `/maintenance/invoice/`
Invoice-related maintenance scripts:
- `check_invoice_sequences.php` - Verify invoice number sequences
- `check_invoice_structure.php` - Validate invoice database structure
- `check_invoice_lines.php` - Check invoice line items
- `check_duplicate_invoice_lines.php` - Find duplicate invoice lines
- `fix_duplicate_invoice_lines.php` - Remove duplicate invoice lines
- `clear_invoice_cache.php` - Clear invoice-related cache
- `find_recent_invoices.php` - List recent invoices
- `check_sequence_and_invoices.php` - Verify sequences match invoices
- `setup_invoice_sequence.php` - Initialize invoice sequences
- `check_invoice_types_prefixes.php` - Verify invoice type prefixes
- `update_invoice_type_prefixes.php` - Update invoice type prefixes

### `/maintenance/diagnostics/`
Database and system diagnostic scripts:
- `check_column_types.php` - Verify database column types
- `check_config_table.php` - Validate configuration table
- `check_invoice_table_structure.php` - Check invoice table schema
- `check_document_types.php` - Verify document types configuration
- `check_payment_terms_data.php` - Check payment terms data
- `debug_payment_terms.php` - Debug payment terms issues

### `/maintenance/user-management/`
User and group management scripts:
- `add_users_to_coach_group.php` - Add users to coach group
- `add_coaches_to_group.php` - Add coaches to specific groups
- `create_coach_users.php` - Create new coach user accounts
- `capitalize_usernames.php` - Fix username capitalization
- `check_user_addresses.php` - Verify user address data
- `add_address_to_all_users.php` - Bulk add addresses to users

### `/archive/deleted_scripts/`
Contains archived one-time fix scripts and test files that were used during development and debugging. These are kept for reference but should not be used in production.

## Usage

All maintenance scripts should be run from the browser or command line (if PHP CLI is available):

**Browser:**
```
http://localhost/fit/tools/maintenance/invoice/check_invoice_sequences.php
```

**Command Line:**
```bash
php tools/maintenance/invoice/check_invoice_sequences.php
```

## Important Notes

1. Always backup your database before running any fix scripts
2. Most scripts are read-only diagnostics, but some make changes to the database
3. Scripts that modify data will typically show a preview before making changes
4. These tools are for administrative use only and should not be publicly accessible