<?php
/**
 * Dashboard Database Optimization Script
 * Run this to check and apply dashboard performance optimizations
 */

require_once __DIR__ . '/../.env.php';

try {
    $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== DASHBOARD DATABASE OPTIMIZATION ===\n\n";
    
    // 1. Check if activity_logs table exists
    echo "1. Checking activity_logs table...\n";
    $result = $pdo->query("SHOW TABLES LIKE 'activity_logs'");
    $activityLogsExists = $result->fetch() !== false;
    
    if (!$activityLogsExists) {
        echo "   ✗ Table does not exist - Run migration 106_create_activity_logs_table.sql\n";
        echo "   Command: cd database && php migrate.php\n\n";
    } else {
        echo "   ✓ Table exists\n\n";
    }
    
    // 2. Check for missing indexes
    echo "2. Checking for missing indexes...\n";
    
    $indexChecks = [
        ['table' => 'invoices', 'column' => 'status', 'index' => 'idx_status'],
        ['table' => 'invoices', 'column' => 'issue_date', 'index' => 'idx_issue_date'],
        ['table' => 'invoices', 'column' => 'created_at', 'index' => 'idx_created_at'],
        ['table' => 'invoices', 'columns' => ['status', 'issue_date'], 'index' => 'idx_status_issue_date'],
        ['table' => 'clients', 'column' => 'is_active', 'index' => 'idx_is_active'],
        ['table' => 'config_settings', 'column' => 'key', 'index' => 'idx_key']
    ];
    
    $missingIndexes = [];
    
    foreach ($indexChecks as $check) {
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE table_schema = :db_name 
            AND table_name = :table_name 
            AND index_name = :index_name
        ");
        
        $stmt->execute([
            ':db_name' => DB_NAME,
            ':table_name' => $check['table'],
            ':index_name' => $check['index']
        ]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] == 0) {
            $missingIndexes[] = $check;
            $columnDisplay = isset($check['columns']) ? implode(', ', $check['columns']) : $check['column'];
            echo "   ✗ Missing: {$check['table']}.{$columnDisplay} (index: {$check['index']})\n";
        } else {
            $columnDisplay = isset($check['columns']) ? implode(', ', $check['columns']) : $check['column'];
            echo "   ✓ Exists: {$check['table']}.{$columnDisplay}\n";
        }
    }
    
    echo "\n";
    
    // 3. Offer to create missing indexes
    if (!empty($missingIndexes)) {
        echo "3. Missing indexes found. Create them? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        
        if (trim($line) == 'y') {
            echo "\nCreating indexes...\n";
            
            foreach ($missingIndexes as $index) {
                try {
                    if (isset($index['columns'])) {
                        $columns = '`' . implode('`, `', $index['columns']) . '`';
                        $sql = "ALTER TABLE `{$index['table']}` ADD INDEX `{$index['index']}` ({$columns})";
                    } else {
                        $sql = "ALTER TABLE `{$index['table']}` ADD INDEX `{$index['index']}` (`{$index['column']}`)";
                    }
                    
                    $pdo->exec($sql);
                    echo "   ✓ Created: {$index['index']} on {$index['table']}\n";
                } catch (Exception $e) {
                    echo "   ✗ Error creating {$index['index']}: " . $e->getMessage() . "\n";
                }
            }
            
            echo "\nOptimization complete!\n";
        } else {
            echo "\nSkipping index creation. Run migration 107_optimize_dashboard_performance.sql manually.\n";
        }
        
        fclose($handle);
    } else {
        echo "3. All recommended indexes are already in place!\n";
    }
    
    // 4. Performance test
    echo "\n4. Running performance test...\n";
    
    // Test invoice stats query
    $start = microtime(true);
    $pdo->query("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'unpaid' THEN 1 ELSE 0 END) as unpaid,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid
        FROM invoices
    ")->fetch();
    $time1 = (microtime(true) - $start) * 1000;
    
    // Test monthly revenue query
    $start = microtime(true);
    $pdo->query("
        SELECT SUM(total) as monthly_revenue 
        FROM invoices 
        WHERE MONTH(issue_date) = MONTH(CURRENT_DATE()) 
        AND YEAR(issue_date) = YEAR(CURRENT_DATE())
        AND status = 'paid'
    ")->fetch();
    $time2 = (microtime(true) - $start) * 1000;
    
    // Test recent invoices query
    $start = microtime(true);
    $pdo->query("
        SELECT i.*, c.name, c.company_name
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        ORDER BY i.created_at DESC
        LIMIT 10
    ")->fetch();
    $time3 = (microtime(true) - $start) * 1000;
    
    echo "   Invoice stats query: " . number_format($time1, 2) . " ms\n";
    echo "   Monthly revenue query: " . number_format($time2, 2) . " ms\n";
    echo "   Recent invoices query: " . number_format($time3, 2) . " ms\n";
    
    // Performance assessment
    $totalTime = $time1 + $time2 + $time3;
    if ($totalTime < 50) {
        echo "\n   ✓ Excellent performance! Total: " . number_format($totalTime, 2) . " ms\n";
    } elseif ($totalTime < 100) {
        echo "\n   ✓ Good performance. Total: " . number_format($totalTime, 2) . " ms\n";
    } elseif ($totalTime < 200) {
        echo "\n   ⚠ Acceptable performance. Total: " . number_format($totalTime, 2) . " ms\n";
        echo "   Consider adding the missing indexes for better performance.\n";
    } else {
        echo "\n   ✗ Poor performance. Total: " . number_format($totalTime, 2) . " ms\n";
        echo "   Strongly recommend adding the missing indexes!\n";
    }
    
    echo "\n=== OPTIMIZATION CHECK COMPLETE ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}