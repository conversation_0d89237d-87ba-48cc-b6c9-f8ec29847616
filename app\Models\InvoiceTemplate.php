<?php

namespace App\Models;

use Flight;
use PDO;

class InvoiceTemplate extends \App\Core\Model
{
    protected $table = 'invoice_templates';
    
    protected $fillable = [
        'name',
        'code',
        'invoice_type',
        'owner_type',
        'owner_id',
        'parent_template_id',
        'description',
        'is_active',
        'created_by'
    ];
    
    /**
     * Get all templates with their details
     */
    public function getAllWithDetails($filters = [])
    {
        $db = Flight::db();
        
        $where = ['1=1'];
        $params = [];
        
        if (!empty($filters['invoice_type'])) {
            $where[] = 'it.invoice_type = :invoice_type';
            $params[':invoice_type'] = $filters['invoice_type'];
        }
        
        if (!empty($filters['owner_type'])) {
            $where[] = 'it.owner_type = :owner_type';
            $params[':owner_type'] = $filters['owner_type'];
        }
        
        if (!empty($filters['is_active'])) {
            $where[] = 'it.is_active = :is_active';
            $params[':is_active'] = $filters['is_active'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        $stmt = $db->prepare("
            SELECT it.*, 
                   u.username as creator_name,
                   pt.name as parent_template_name,
                   COUNT(DISTINCT tli.id) as item_count,
                   COUNT(DISTINCT its.id) as setting_count
            FROM {$this->table} it
            LEFT JOIN users u ON it.created_by = u.id
            LEFT JOIN {$this->table} pt ON it.parent_template_id = pt.id
            LEFT JOIN template_line_items tli ON it.id = tli.template_id
            LEFT JOIN invoice_template_settings its ON it.id = its.template_id
            WHERE $whereClause
            GROUP BY it.id
            ORDER BY it.owner_type ASC, it.invoice_type ASC, it.name ASC
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get templates for dropdown selection
     */
    public function getForDropdown($invoiceType = null, $userId = null)
    {
        $db = Flight::db();
        
        $where = ['it.is_active = 1'];
        $params = [];
        
        if ($invoiceType) {
            $where[] = 'it.invoice_type = :invoice_type';
            $params[':invoice_type'] = $invoiceType;
        }
        
        // Get system templates and user's own templates
        if ($userId) {
            $where[] = '(it.owner_type = "system" OR (it.owner_type = "user" AND it.owner_id = :user_id))';
            $params[':user_id'] = $userId;
        } else {
            $where[] = 'it.owner_type = "system"';
        }
        
        $whereClause = implode(' AND ', $where);
        
        $stmt = $db->prepare("
            SELECT it.id, it.name, it.code, it.owner_type
            FROM {$this->table} it
            WHERE $whereClause
            ORDER BY it.owner_type ASC, it.name ASC
        ");
        
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Duplicate a template
     */
    public function duplicate($id, $newName, $newCode, $userId)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get original template as array
            $stmt = $db->prepare("SELECT * FROM {$this->table} WHERE id = :id LIMIT 1");
            $stmt->execute([':id' => $id]);
            $original = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$original) {
                throw new \Exception('Template not found');
            }
            
            // Create new template
            $newTemplate = $original;
            unset($newTemplate['id']);
            $newTemplate['name'] = $newName;
            $newTemplate['code'] = $newCode;
            $newTemplate['created_by'] = $userId;
            $newTemplate['created_at'] = date('Y-m-d H:i:s');
            $newTemplate['updated_at'] = date('Y-m-d H:i:s');
            
            // Insert new template
            $columns = array_keys($newTemplate);
            $values = array_map(function($col) { return ':' . $col; }, $columns);
            
            $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ")";
            $stmt = $db->prepare($sql);
            
            foreach ($newTemplate as $key => $value) {
                $stmt->bindValue(':' . $key, $value);
            }
            
            $stmt->execute();
            $newId = $db->lastInsertId();
            
            // Duplicate template settings
            $stmt = $db->prepare("
                INSERT INTO invoice_template_settings (template_id, setting_key, setting_value, use_parent_value, setting_type, description)
                SELECT :new_id, setting_key, setting_value, use_parent_value, setting_type, description
                FROM invoice_template_settings
                WHERE template_id = :old_id
            ");
            $stmt->execute([':new_id' => $newId, ':old_id' => $id]);
            
            // Duplicate template line items
            $stmt = $db->prepare("
                INSERT INTO template_line_items (template_id, line_type, description, default_quantity, default_unit_price, vat_rate_id, calculation_formula, is_mandatory, sort_order)
                SELECT :new_id, line_type, description, default_quantity, default_unit_price, vat_rate_id, calculation_formula, is_mandatory, sort_order
                FROM template_line_items
                WHERE template_id = :old_id
            ");
            $stmt->execute([':new_id' => $newId, ':old_id' => $id]);
            
            // Duplicate VAT configs
            $stmt = $db->prepare("
                INSERT INTO template_vat_configs (template_id, vat_rate_id, is_default, service_type, conditions)
                SELECT :new_id, vat_rate_id, is_default, service_type, conditions
                FROM template_vat_configs
                WHERE template_id = :old_id
            ");
            $stmt->execute([':new_id' => $newId, ':old_id' => $id]);
            
            $db->commit();
            return $newId;
            
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get template with all its settings
     */
    public function getWithSettings($id)
    {
        $db = Flight::db();
        
        // Get the template as array
        $stmt = $db->prepare("SELECT * FROM {$this->table} WHERE id = :id LIMIT 1");
        $stmt->execute([':id' => $id]);
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$template) {
            return null;
        }
        
        // Get settings
        $stmt = $db->prepare("
            SELECT * FROM invoice_template_settings 
            WHERE template_id = :id
            ORDER BY setting_type, setting_key
        ");
        $stmt->execute([':id' => $id]);
        $template['settings'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get line items
        $stmt = $db->prepare("
            SELECT tli.*, vr.rate as vat_rate_value, vr.description as vat_rate_name
            FROM template_line_items tli
            LEFT JOIN config_vat_rates vr ON tli.vat_rate_id = vr.id
            WHERE tli.template_id = :id
            ORDER BY tli.sort_order
        ");
        $stmt->execute([':id' => $id]);
        $template['line_items'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get VAT configs
        $stmt = $db->prepare("
            SELECT tvc.*, vr.rate, vr.description as vat_description
            FROM template_vat_configs tvc
            JOIN config_vat_rates vr ON tvc.vat_rate_id = vr.id
            WHERE tvc.template_id = :id
            ORDER BY tvc.is_default DESC, vr.rate ASC
        ");
        $stmt->execute([':id' => $id]);
        $template['vat_configs'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return $template;
    }
    
    /**
     * Check if template code is unique
     */
    public function isCodeUnique($code, $invoiceType, $ownerType, $ownerId, $excludeId = null)
    {
        $db = Flight::db();
        
        $where = 'code = :code AND invoice_type = :invoice_type AND owner_type = :owner_type';
        $params = [
            ':code' => $code,
            ':invoice_type' => $invoiceType,
            ':owner_type' => $ownerType
        ];
        
        if ($ownerType !== 'system') {
            $where .= ' AND owner_id = :owner_id';
            $params[':owner_id'] = $ownerId;
        }
        
        if ($excludeId) {
            $where .= ' AND id != :exclude_id';
            $params[':exclude_id'] = $excludeId;
        }
        
        $stmt = $db->prepare("SELECT COUNT(*) FROM {$this->table} WHERE $where");
        $stmt->execute($params);
        
        return $stmt->fetchColumn() == 0;
    }
}