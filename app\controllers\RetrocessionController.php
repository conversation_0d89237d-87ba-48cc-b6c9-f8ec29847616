<?php

namespace App\Controllers;

use Flight;
use App\Models\Client;
use App\Models\RateProfile;
use App\Services\RetrocessionCalculator;
use Exception;
use PDO;

class RetrocessionController extends \App\Core\Controller
{
    private $client;
    private $rateProfile;
    private $calculator;
    
    public function __construct()
    {
        $this->client = new Client();
        $this->rateProfile = new RateProfile();
        $this->calculator = new RetrocessionCalculator();
    }
    
    /**
     * Monthly data entry overview
     */
    public function index()
    {
        $month = Flight::request()->query->month ?: date('n');
        $year = Flight::request()->query->year ?: date('Y');
        
        // Get all practitioners
        $practitioners = $this->getPractitionersWithRetrocessionData($month, $year);
        
        // Get summary statistics
        $stats = $this->getMonthlyStatistics($month, $year);
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'retrocession/index-modern';
        
        $this->render($viewName, [
            'practitioners' => $practitioners,
            'month' => $month,
            'year' => $year,
            'monthName' => $this->getMonthName($month),
            'stats' => $stats,
            'months' => $this->getMonthsList(),
            'years' => range(date('Y') - 2, date('Y') + 1)
        ]);
    }
    
    /**
     * Data entry form for practitioner
     */
    public function dataEntry($practitionerId)
    {
        $month = Flight::request()->query->month ?: date('n');
        $year = Flight::request()->query->year ?: date('Y');
        
        $practitioner = $this->client->getById($practitionerId);
        if (!$practitioner || $practitioner['client_type'] !== 'practitioner') {
            Flight::notFound();
            return;
        }
        
        // Get existing data entry
        $dataEntry = $this->getDataEntry($practitionerId, $month, $year);
        
        // Get auto-fill suggestion
        $suggestion = null;
        if (!$dataEntry) {
            $suggestion = $this->calculator->generateAutoFillSuggestion($practitionerId, $month, $year);
        }
        
        // Get rate profile
        $dateStr = sprintf('%04d-%02d-01', $year, $month);
        $rates = [
            'cns_percent' => $this->rateProfile->getEffectiveRate($practitionerId, 'cns_percent', $dateStr) ?? 20,
            'patient_percent' => $this->rateProfile->getEffectiveRate($practitionerId, 'patient_percent', $dateStr) ?? 20,
            'secretariat_percent' => $this->rateProfile->getEffectiveRate($practitionerId, 'secretariat_percent', $dateStr) ?? 10
        ];
        
        // Get CNS imports for this period
        $cnsImports = $this->getCnsImports($practitionerId, $month, $year);
        
        // Get historical data for reference
        $history = $this->getHistoricalData($practitionerId, $month, $year);
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'retrocession/data-entry-modern';
        
        $this->render($viewName, [
            'practitioner' => $practitioner,
            'month' => $month,
            'year' => $year,
            'monthName' => $this->getMonthName($month),
            'dataEntry' => $dataEntry,
            'suggestion' => $suggestion,
            'rates' => $rates,
            'cnsImports' => $cnsImports,
            'history' => $history
        ]);
    }
    
    /**
     * Save data entry
     */
    public function saveDataEntry($practitionerId)
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Validate amounts
            if (!is_numeric($data['cns_amount']) || !is_numeric($data['patient_amount'])) {
                throw new Exception(__('retrocession.invalid_amounts'));
            }
            
            $db = Flight::db();
            
            // Check if entry exists
            $existing = $this->getDataEntry($practitionerId, $data['month'], $data['year']);
            
            if ($existing) {
                // Update existing
                $stmt = $db->prepare("
                    UPDATE retrocession_data_entry
                    SET cns_amount = :cns_amount,
                        patient_amount = :patient_amount,
                        override_cns_percent = :override_cns_percent,
                        override_patient_percent = :override_patient_percent,
                        override_secretariat_percent = :override_secretariat_percent,
                        override_notes = :override_notes,
                        data_source = :data_source,
                        cns_import_id = :cns_import_id,
                        updated_at = NOW()
                    WHERE id = :id
                ");
                
                $stmt->execute([
                    ':cns_amount' => $data['cns_amount'],
                    ':patient_amount' => $data['patient_amount'],
                    ':override_cns_percent' => $data['override_cns_percent'] ?: null,
                    ':override_patient_percent' => $data['override_patient_percent'] ?: null,
                    ':override_secretariat_percent' => $data['override_secretariat_percent'] ?: null,
                    ':override_notes' => $data['override_notes'] ?: null,
                    ':data_source' => $data['data_source'] ?? 'manual',
                    ':cns_import_id' => $data['cns_import_id'] ?: null,
                    ':id' => $existing['id']
                ]);
                
                $dataEntryId = $existing['id'];
                
            } else {
                // Create new
                $stmt = $db->prepare("
                    INSERT INTO retrocession_data_entry (
                        practitioner_id, period_month, period_year,
                        cns_amount, patient_amount,
                        override_cns_percent, override_patient_percent, override_secretariat_percent,
                        override_notes, data_source, cns_import_id,
                        entered_by, entered_at
                    ) VALUES (
                        :practitioner_id, :period_month, :period_year,
                        :cns_amount, :patient_amount,
                        :override_cns_percent, :override_patient_percent, :override_secretariat_percent,
                        :override_notes, :data_source, :cns_import_id,
                        :entered_by, NOW()
                    )
                ");
                
                $stmt->execute([
                    ':practitioner_id' => $practitionerId,
                    ':period_month' => $data['month'],
                    ':period_year' => $data['year'],
                    ':cns_amount' => $data['cns_amount'],
                    ':patient_amount' => $data['patient_amount'],
                    ':override_cns_percent' => $data['override_cns_percent'] ?: null,
                    ':override_patient_percent' => $data['override_patient_percent'] ?: null,
                    ':override_secretariat_percent' => $data['override_secretariat_percent'] ?: null,
                    ':override_notes' => $data['override_notes'] ?: null,
                    ':data_source' => $data['data_source'] ?? 'manual',
                    ':cns_import_id' => $data['cns_import_id'] ?: null,
                    ':entered_by' => $_SESSION['user_id'] ?? 1
                ]);
                
                $dataEntryId = $db->lastInsertId();
            }
            
            // Update auto-fill feedback if used
            if (!empty($data['used_suggestion']) && !empty($data['suggestion_id'])) {
                $stmt = $db->prepare("
                    UPDATE retrocession_autofill
                    SET accepted = :accepted,
                        modified_amount = :modified_amount,
                        feedback_notes = :feedback_notes,
                        updated_at = NOW()
                    WHERE id = :id
                ");
                
                $modified = ($data['cns_amount'] != $data['suggested_cns']) || 
                           ($data['patient_amount'] != $data['suggested_patient']);
                
                $stmt->execute([
                    ':accepted' => !$modified,
                    ':modified_amount' => $modified ? ($data['cns_amount'] + $data['patient_amount']) : null,
                    ':feedback_notes' => $data['feedback_notes'] ?: null,
                    ':id' => $data['suggestion_id']
                ]);
            }
            
            Flight::flash('success', __('retrocession.data_saved'));
            Flight::redirect($this->url('/retrocession?month=' . $data['month'] . '&year=' . $data['year']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/retrocession/' . $practitionerId . '/data-entry?month=' . $data['month'] . '&year=' . $data['year']));
        }
    }
    
    /**
     * Calculate preview
     */
    public function calculatePreview()
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Perform calculation
            $result = $this->calculator->calculate($data);
            
            // Format for display
            $formatted = [
                'cns_part' => number_format($result['cns_part'], 2),
                'patient_part' => number_format($result['patient_part'], 2),
                'practitioner_total' => number_format($result['practitioner_total'], 2),
                'secretariat_tvac' => number_format($result['secretariat_tvac'], 2),
                'secretariat_htva' => number_format($result['secretariat_htva'], 2),
                'vat_amount' => number_format($result['vat_amount'], 2),
                'invoice_total' => number_format($result['invoice_total'], 2)
            ];
            
            Flight::json(['success' => true, 'calculation' => $formatted]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Confirm data entry
     */
    public function confirmEntry($practitionerId)
    {
        try {
            $month = Flight::request()->data->month;
            $year = Flight::request()->data->year;
            
            $dataEntry = $this->getDataEntry($practitionerId, $month, $year);
            
            if (!$dataEntry) {
                throw new Exception(__('retrocession.no_data_to_confirm'));
            }
            
            if ($dataEntry['status'] !== 'draft') {
                throw new Exception(__('retrocession.already_confirmed'));
            }
            
            $db = Flight::db();
            $stmt = $db->prepare("
                UPDATE retrocession_data_entry
                SET status = 'confirmed',
                    confirmed_by = :confirmed_by,
                    confirmed_at = NOW(),
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $stmt->execute([
                ':confirmed_by' => $_SESSION['user_id'] ?? 1,
                ':id' => $dataEntry['id']
            ]);
            
            Flight::flash('success', __('retrocession.data_confirmed'));
            Flight::redirect($this->url('/retrocession?month=' . $month . '&year=' . $year));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/retrocession'));
        }
    }
    
    /**
     * Generate invoice from data entry
     */
    public function generateInvoice($practitionerId)
    {
        try {
            $month = Flight::request()->data->month;
            $year = Flight::request()->data->year;
            
            $invoice = $this->calculator->generateInvoice($practitionerId, $month, $year);
            
            Flight::flash('success', __('retrocession.invoice_generated'));
            Flight::redirect($this->url('/invoices/' . $invoice['id']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/retrocession/' . $practitionerId . '/data-entry?month=' . $month . '&year=' . $year));
        }
    }
    
    /**
     * Bulk generate invoices
     */
    public function bulkGenerate()
    {
        try {
            $month = Flight::request()->data->month;
            $year = Flight::request()->data->year;
            $practitionerIds = Flight::request()->data->practitioner_ids;
            
            if (empty($practitionerIds)) {
                throw new Exception(__('retrocession.no_practitioners_selected'));
            }
            
            $success = 0;
            $errors = [];
            
            foreach ($practitionerIds as $practitionerId) {
                try {
                    $this->calculator->generateInvoice($practitionerId, $month, $year);
                    $success++;
                } catch (Exception $e) {
                    $errors[] = sprintf(
                        __('retrocession.invoice_generation_failed'),
                        $this->client->getById($practitionerId)['name'],
                        $e->getMessage()
                    );
                }
            }
            
            if ($success > 0) {
                Flight::flash('success', __('retrocession.bulk_invoices_generated', ['count' => $success]));
            }
            
            if (!empty($errors)) {
                Flight::flash('error', implode('<br>', $errors));
            }
            
            Flight::redirect($this->url('/retrocession?month=' . $month . '&year=' . $year));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/retrocession'));
        }
    }
    
    /**
     * Rate profiles management
     */
    public function rateProfiles()
    {
        $db = Flight::db();
        
        // Fetch profiles with current rates
        $stmt = $db->query("
            SELECT 
                p.*,
                MAX(CASE WHEN rr.rate_type = 'cns_percent' AND rr.is_current = 1 THEN rr.base_value END) as current_cns_rate,
                MAX(CASE WHEN rr.rate_type = 'patient_percent' AND rr.is_current = 1 THEN rr.base_value END) as current_patient_rate,
                MAX(CASE WHEN rr.rate_type = 'secretariat_percent' AND rr.is_current = 1 THEN rr.base_value END) as current_secretariat_rate,
                MAX(CASE WHEN rr.rate_type = 'hourly_rate' AND rr.is_current = 1 THEN rr.base_value END) as hourly_rate,
                MAX(CASE WHEN rr.rate_type = 'rent_amount' AND rr.is_current = 1 THEN rr.base_value END) as rent_amount,
                MAX(CASE WHEN rr.rate_type = 'charges_amount' AND rr.is_current = 1 THEN rr.base_value END) as charges_amount,
                COUNT(DISTINCT pra.practitioner_id) as practitioner_count
            FROM rate_profiles p
            LEFT JOIN rate_profile_rates rr ON p.id = rr.profile_id
            LEFT JOIN practitioner_rate_assignments pra ON p.id = pra.profile_id 
                AND pra.assigned_to IS NULL
            GROUP BY p.id
            ORDER BY p.is_default DESC, p.name
        ");
        
        $profiles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'retrocession/rate-profiles-modern';
        
        $this->render($viewName, [
            'profiles' => $profiles
        ]);
    }
    
    /**
     * Create rate profile form
     */
    public function createProfile()
    {
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'retrocession/profile-form-modern';
        
        $this->render($viewName, [
            'profile' => null,
            'profileTypes' => [
                RateProfile::TYPE_RETROCESSION => __('retrocession.type_retrocession'),
                RateProfile::TYPE_HOURLY => __('retrocession.type_hourly'),
                RateProfile::TYPE_RENTAL => __('retrocession.type_rental'),
                RateProfile::TYPE_MIXED => __('retrocession.type_mixed')
            ]
        ]);
    }
    
    /**
     * Edit rate profile
     */
    public function editProfile($id)
    {
        $profile = $this->rateProfile->getProfileWithRates($id);
        
        if (!$profile) {
            Flight::notFound();
            return;
        }
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'retrocession/profile-form-modern';
        
        $this->render($viewName, [
            'profile' => $profile,
            'profileTypes' => [
                RateProfile::TYPE_RETROCESSION => __('retrocession.type_retrocession'),
                RateProfile::TYPE_HOURLY => __('retrocession.type_hourly'),
                RateProfile::TYPE_RENTAL => __('retrocession.type_rental'),
                RateProfile::TYPE_MIXED => __('retrocession.type_mixed')
            ]
        ]);
    }
    
    /**
     * Store rate profile
     */
    public function storeProfile()
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Validate required fields
            if (empty($data['name']) || empty($data['code']) || empty($data['profile_type'])) {
                throw new Exception(__('validation.required_fields'));
            }
            
            $profile = $this->rateProfile->createProfile($data);
            
            Flight::flash('success', __('retrocession.profile_created'));
            Flight::redirect($this->url('/retrocession/rate-profiles/' . $profile['id'] . '/edit'));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/retrocession/rate-profiles/create'));
        }
    }
    
    /**
     * Update rate profile
     */
    public function updateProfile($id)
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Update profile
            $this->rateProfile->update($id, [
                'name' => $data['name'],
                'description' => $data['description'],
                'is_active' => $data['is_active'] ?? false
            ]);
            
            // Update rates if provided
            if (!empty($data['rates'])) {
                foreach ($data['rates'] as $rateType => $rateData) {
                    if (!empty($rateData['value'])) {
                        $this->rateProfile->addRate(
                            $id,
                            $rateType,
                            $rateData['value'],
                            $rateData['valid_from'] ?? date('Y-m-d'),
                            $rateData['valid_to'] ?? null,
                            $rateData['notes'] ?? null
                        );
                    }
                }
            }
            
            // Handle cascade update if requested
            if (!empty($data['cascade_update'])) {
                $changes = array_filter($data['rates'], function($rate) {
                    return !empty($rate['value']);
                });
                
                $updated = $this->rateProfile->applyCascadeUpdate($id, $changes);
                
                if ($updated > 0) {
                    Flight::flash('info', __('retrocession.cascade_updated', ['count' => $updated]));
                }
            }
            
            Flight::flash('success', __('retrocession.profile_updated'));
            Flight::redirect($this->url('/retrocession/rate-profiles/' . $id . '/edit'));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/retrocession/rate-profiles/' . $id . '/edit'));
        }
    }
    
    /**
     * Assign profile to practitioner
     */
    public function assignProfile()
    {
        try {
            $data = Flight::request()->data->getData();
            
            $this->rateProfile->assignToPractitioner(
                $data['profile_id'],
                $data['practitioner_id'],
                $data['assigned_from'],
                $data['assigned_to'] ?? null,
                $data['notes'] ?? null
            );
            
            Flight::json(['success' => true, 'message' => __('retrocession.profile_assigned')]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Private helper methods
     */
    
    private function getPractitionersWithRetrocessionData($month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                c.id, 
                COALESCE(c.company_name, CONCAT(c.first_name, ' ', c.last_name)) as name,
                c.client_number, c.email,
                rde.id as data_entry_id,
                rde.cns_amount, rde.patient_amount, rde.total_amount,
                rde.status as entry_status,
                rde.invoice_id,
                i.invoice_number,
                i.status as invoice_status
            FROM clients c
            LEFT JOIN retrocession_data_entry rde ON c.id = rde.practitioner_id 
                AND rde.period_month = :month 
                AND rde.period_year = :year
            LEFT JOIN invoices i ON rde.invoice_id = i.id
            WHERE c.is_practitioner = TRUE 
            AND c.is_active = TRUE
            ORDER BY COALESCE(c.company_name, CONCAT(c.first_name, ' ', c.last_name))
        ");
        
        $stmt->execute([':month' => $month, ':year' => $year]);
        return $stmt->fetchAll();
    }
    
    private function getMonthlyStatistics($month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                COUNT(DISTINCT c.id) as total_practitioners,
                COUNT(DISTINCT rde.id) as entries_created,
                SUM(CASE WHEN rde.status = 'confirmed' THEN 1 ELSE 0 END) as entries_confirmed,
                SUM(CASE WHEN rde.status = 'invoiced' THEN 1 ELSE 0 END) as entries_invoiced,
                SUM(rde.cns_amount) as total_cns,
                SUM(rde.patient_amount) as total_patient,
                SUM(rde.total_amount) as grand_total
            FROM clients c
            LEFT JOIN retrocession_data_entry rde ON c.id = rde.practitioner_id 
                AND rde.period_month = :month 
                AND rde.period_year = :year
            WHERE c.client_type = 'practitioner' 
            AND c.is_active = TRUE
        ");
        
        $stmt->execute([':month' => $month, ':year' => $year]);
        return $stmt->fetch();
    }
    
    private function getDataEntry($practitionerId, $month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT * FROM retrocession_data_entry
            WHERE practitioner_id = :practitioner_id
            AND period_month = :month
            AND period_year = :year
        ");
        
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':month' => $month,
            ':year' => $year
        ]);
        
        return $stmt->fetch();
    }
    
    private function getCnsImports($practitionerId, $month, $year)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT * FROM cns_imports
            WHERE practitioner_id = :practitioner_id
            AND period_month = :month
            AND period_year = :year
            ORDER BY import_date DESC
        ");
        
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':month' => $month,
            ':year' => $year
        ]);
        
        return $stmt->fetchAll();
    }
    
    private function getHistoricalData($practitionerId, $currentMonth, $currentYear)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                period_month, period_year,
                cns_amount, patient_amount, total_amount,
                status
            FROM retrocession_data_entry
            WHERE practitioner_id = :practitioner_id
            AND ((period_year = :year AND period_month < :month) 
                 OR (period_year = :prev_year AND period_month >= :month))
            ORDER BY period_year DESC, period_month DESC
            LIMIT 6
        ");
        
        $prevYear = $currentYear - 1;
        
        $stmt->execute([
            ':practitioner_id' => $practitionerId,
            ':month' => $currentMonth,
            ':year' => $currentYear,
            ':prev_year' => $prevYear
        ]);
        
        return $stmt->fetchAll();
    }
    
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
    
    private function getMonthsList()
    {
        return [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
    }
}