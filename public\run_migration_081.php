<?php
/**
 * Run migration to add exclude_patient_line_default to users table
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment variables FIRST
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Now require bootstrap which will use the env vars
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

echo "<pre>";
echo "=== Running Migration 081: Add exclude_patient_line_default to users ===\n\n";

try {
    $db = Flight::db();
    
    // Check if column already exists
    $stmt = $db->prepare("SHOW COLUMNS FROM users LIKE 'exclude_patient_line_default'");
    $stmt->execute();
    $column = $stmt->fetch();
    
    if ($column) {
        echo "✓ Column 'exclude_patient_line_default' already exists in users table\n";
    } else {
        // Read and execute migration
        $migrationFile = dirname(__DIR__) . '/database/migrations/081_add_exclude_patient_line_default_to_users.sql';
        $sql = file_get_contents($migrationFile);
        
        // Split by semicolon to execute statements separately
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                echo "Executing: " . substr($statement, 0, 60) . "...\n";
                $db->exec($statement);
            }
        }
        
        echo "\n✓ Migration completed successfully!\n";
        echo "Added 'exclude_patient_line_default' column to users table\n";
    }
    
    // Show current users table structure
    echo "\nCurrent users table structure (relevant columns):\n";
    $stmt = $db->query("DESCRIBE users");
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        if (in_array($row['Field'], ['id', 'username', 'first_name', 'last_name', 'is_practitioner', 'exclude_patient_line_default'])) {
            echo sprintf("%-30s %-20s %-10s %s\n", 
                $row['Field'], 
                $row['Type'], 
                $row['Null'], 
                $row['Default'] ?? 'NULL'
            );
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";