<?php

namespace Database\Seeders;

use PDO;
use Exception;

class ExistingPermissionSeeder
{
    private PDO $db;
    
    public function __construct(PDO $db)
    {
        $this->db = $db;
    }
    
    /**
     * Run the permission seeder for existing permission structure
     */
    public function run(): void
    {
        echo "Seeding permissions for existing structure...\n\n";
        
        try {
            $this->db->beginTransaction();
            
            // Create default groups if they don't exist
            $this->createDefaultGroups();
            
            // Ensure all permissions exist
            $this->ensurePermissionsExist();
            
            // Assign permissions to groups
            $this->assignPermissionsToGroups();
            
            $this->db->commit();
            echo "\n✅ Permissions seeded successfully!\n";
            
        } catch (Exception $e) {
            $this->db->rollBack();
            echo "\n❌ Error seeding permissions: " . $e->getMessage() . "\n";
            throw $e;
        }
    }
    
    /**
     * Create default user groups
     */
    private function createDefaultGroups(): void
    {
        echo "Creating default user groups...\n";
        
        $groups = [
            [
                'name' => 'Administrators',
                'description' => 'Full system access',
                'color' => '#dc3545',
                'icon' => 'fas fa-crown',
                'is_system' => 1
            ],
            [
                'name' => 'Managers',
                'description' => 'Access to most features except system configuration',
                'color' => '#007bff',
                'icon' => 'fas fa-user-tie',
                'is_system' => 1
            ],
            [
                'name' => 'Staff',
                'description' => 'Basic access to operational features',
                'color' => '#28a745',
                'icon' => 'fas fa-users',
                'is_system' => 1
            ],
            [
                'name' => 'Practitioners',
                'description' => 'Access to patient management and their own invoicing',
                'color' => '#17a2b8',
                'icon' => 'fas fa-user-md',
                'is_system' => 0
            ],
        ];
        
        // Check if user_groups table has all required columns
        $stmt = $this->db->query("SHOW COLUMNS FROM user_groups");
        $columns = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $columns[] = $row['Field'];
        }
        
        // Build insert query based on available columns
        $fields = ['name', 'description'];
        $values = [':name', ':description'];
        
        if (in_array('color', $columns)) {
            $fields[] = 'color';
            $values[] = ':color';
        }
        if (in_array('icon', $columns)) {
            $fields[] = 'icon';
            $values[] = ':icon';
        }
        if (in_array('is_system', $columns)) {
            $fields[] = 'is_system';
            $values[] = ':is_system';
        }
        if (in_array('is_active', $columns)) {
            $fields[] = 'is_active';
            $values[] = '1';
        }
        if (in_array('created_at', $columns)) {
            $fields[] = 'created_at';
            $values[] = 'NOW()';
        }
        
        $sql = "INSERT INTO user_groups (" . implode(', ', $fields) . ") 
                VALUES (" . implode(', ', $values) . ")
                ON DUPLICATE KEY UPDATE description = VALUES(description)";
        
        if (in_array('color', $columns)) {
            $sql .= ", color = VALUES(color)";
        }
        if (in_array('icon', $columns)) {
            $sql .= ", icon = VALUES(icon)";
        }
        
        $stmt = $this->db->prepare($sql);
        
        foreach ($groups as $group) {
            $params = [
                'name' => $group['name'],
                'description' => $group['description']
            ];
            
            if (in_array('color', $columns)) {
                $params['color'] = $group['color'];
            }
            if (in_array('icon', $columns)) {
                $params['icon'] = $group['icon'];
            }
            if (in_array('is_system', $columns)) {
                $params['is_system'] = $group['is_system'];
            }
            
            $stmt->execute($params);
            echo "✓ Created/Updated group: {$group['name']}\n";
        }
    }
    
    /**
     * Ensure all required permissions exist
     */
    private function ensurePermissionsExist(): void
    {
        echo "\nChecking permissions...\n";
        
        // Define additional permissions that might not exist
        $permissions = [
            // Permissions
            ['category' => 'permissions', 'name' => 'View Permissions', 'code' => 'permissions.view', 'description' => 'Can view permission assignments'],
            ['category' => 'permissions', 'name' => 'Manage Permissions', 'code' => 'permissions.manage', 'description' => 'Can manage permission assignments'],
            
            // Additional invoice permissions
            ['category' => 'invoices', 'name' => 'Export Invoices', 'code' => 'invoices.export', 'description' => 'Can export invoices'],
            ['category' => 'invoices', 'name' => 'Send Invoices', 'code' => 'invoices.send', 'description' => 'Can send invoices via email'],
            ['category' => 'invoices', 'name' => 'Void Invoices', 'code' => 'invoices.void', 'description' => 'Can void invoices'],
            
            // Product permissions
            ['category' => 'products', 'name' => 'Manage Stock', 'code' => 'products.manage_stock', 'description' => 'Can manage product stock'],
            ['category' => 'products', 'name' => 'Import Products', 'code' => 'products.import', 'description' => 'Can import products'],
            
            // Report permissions
            ['category' => 'reports', 'name' => 'View Financial Reports', 'code' => 'reports.view_financial', 'description' => 'Can view financial reports'],
            ['category' => 'reports', 'name' => 'View Inventory Reports', 'code' => 'reports.view_inventory', 'description' => 'Can view inventory reports'],
            ['category' => 'reports', 'name' => 'Export Reports', 'code' => 'reports.export', 'description' => 'Can export reports'],
            
            // Config permissions
            ['category' => 'configuration', 'name' => 'Manage Company', 'code' => 'config.manage_company', 'description' => 'Can manage company settings'],
            ['category' => 'configuration', 'name' => 'Manage System', 'code' => 'config.manage_system', 'description' => 'Can manage system settings'],
            ['category' => 'configuration', 'name' => 'Manage Templates', 'code' => 'config.manage_templates', 'description' => 'Can manage email templates'],
        ];
        
        // Get max sort_order
        $stmt = $this->db->query("SELECT MAX(sort_order) as max_order FROM permissions");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $sortOrder = ($result['max_order'] ?? 0) + 1;
        
        $insertStmt = $this->db->prepare("
            INSERT INTO permissions (category, name, code, description, sort_order, created_at)
            VALUES (:category, :name, :code, :description, :sort_order, NOW())
            ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                description = VALUES(description)
        ");
        
        $addedCount = 0;
        foreach ($permissions as $permission) {
            try {
                $insertStmt->execute([
                    'category' => $permission['category'],
                    'name' => $permission['name'],
                    'code' => $permission['code'],
                    'description' => $permission['description'],
                    'sort_order' => $sortOrder++
                ]);
                if ($insertStmt->rowCount() > 0) {
                    $addedCount++;
                }
            } catch (PDOException $e) {
                // Ignore duplicate key errors
                if ($e->getCode() != '23000') {
                    throw $e;
                }
            }
        }
        
        echo "✓ Added $addedCount new permissions\n";
    }
    
    /**
     * Assign permissions to default groups
     */
    private function assignPermissionsToGroups(): void
    {
        echo "\nAssigning permissions to groups...\n";
        
        // Get group IDs
        $groups = [];
        $stmt = $this->db->query("SELECT id, name FROM user_groups WHERE name IN ('Administrators', 'Managers', 'Staff', 'Practitioners')");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $groups[$row['name']] = $row['id'];
        }
        
        if (empty($groups)) {
            echo "⚠️  No default groups found\n";
            return;
        }
        
        // Get all permission IDs mapped by code
        $permissions = [];
        $stmt = $this->db->query("SELECT id, code FROM permissions");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $permissions[$row['code']] = $row['id'];
        }
        
        // Define permission assignments
        $assignments = [
            'Administrators' => 'ALL', // All permissions
            'Managers' => [
                // Users - no delete
                'users.view', 'users.create', 'users.edit',
                // Invoices - all
                'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.delete', 
                'invoices.export', 'invoices.send', 'invoices.void',
                // Clients - all
                'clients.view', 'clients.create', 'clients.edit', 'clients.delete',
                // Products - all
                'products.view', 'products.create', 'products.edit', 'products.delete',
                'products.manage_stock', 'products.import',
                // Reports - all
                'reports.view', 'reports.view_financial', 'reports.view_inventory', 'reports.export',
                // Limited config
                'config.view', 'config.manage_company'
            ],
            'Staff' => [
                'users.view',
                'invoices.view', 'invoices.create', 'invoices.edit', 'invoices.send',
                'clients.view', 'clients.create', 'clients.edit',
                'products.view',
                'reports.view'
            ],
            'Practitioners' => [
                'clients.view',
                'invoices.view', 'invoices.create',
                'products.view'
            ]
        ];
        
        // Clear existing group permissions for these groups
        if (!empty($groups)) {
            $groupIds = array_values($groups);
            $placeholders = array_fill(0, count($groupIds), '?');
            $stmt = $this->db->prepare("DELETE FROM group_permissions WHERE group_id IN (" . implode(',', $placeholders) . ")");
            $stmt->execute($groupIds);
        }
        
        // Insert new permissions
        $insertStmt = $this->db->prepare("
            INSERT INTO group_permissions (group_id, permission_id, granted_at, granted_by)
            VALUES (:group_id, :permission_id, NOW(), :granted_by)
            ON DUPLICATE KEY UPDATE granted_at = NOW()
        ");
        
        $currentUserId = $_SESSION['user_id'] ?? 1;
        
        foreach ($assignments as $groupName => $permissionCodes) {
            if (!isset($groups[$groupName])) {
                echo "⚠️  Group not found: $groupName\n";
                continue;
            }
            
            $groupId = $groups[$groupName];
            $assignedCount = 0;
            
            if ($permissionCodes === 'ALL') {
                // Assign all permissions
                foreach ($permissions as $code => $permissionId) {
                    $insertStmt->execute([
                        'group_id' => $groupId,
                        'permission_id' => $permissionId,
                        'granted_by' => $currentUserId
                    ]);
                    $assignedCount++;
                }
            } else {
                // Assign specific permissions
                foreach ($permissionCodes as $code) {
                    if (isset($permissions[$code])) {
                        $insertStmt->execute([
                            'group_id' => $groupId,
                            'permission_id' => $permissions[$code],
                            'granted_by' => $currentUserId
                        ]);
                        $assignedCount++;
                    }
                }
            }
            
            echo "✓ Assigned $assignedCount permissions to: $groupName\n";
        }
    }
}

// If run directly from command line
if (php_sapi_name() === 'cli' && basename($argv[0]) === basename(__FILE__)) {
    try {
        // Load database configuration
        require_once __DIR__ . '/../../vendor/autoload.php';
        require_once __DIR__ . '/../../app/config/bootstrap.php';
        
        $seeder = new ExistingPermissionSeeder(Flight::db());
        $seeder->run();
    } catch (Exception $e) {
        echo "\n❌ Fatal error: " . $e->getMessage() . "\n";
        exit(1);
    }
}