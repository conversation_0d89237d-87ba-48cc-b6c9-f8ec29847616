<?php
/**
 * Fix all template literals with Twig expressions
 */

echo "<pre>";
echo "=== Fixing ALL Template Literals with Twig Expressions ===\n\n";

$file = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($file);

// Create backup
$backup = $file . '.backup.' . date('YmdHis');
file_put_contents($backup, $content);
echo "✓ Created backup: " . basename($backup) . "\n\n";

// Find the template function area and look for specific patterns
$patterns = [
    // Pattern 1: innerHTML assignments with Twig inside backticks
    '/innerHTML\s*=\s*`([^`]*\{\{[^}]+\}\}[^`]*)`/s' => function($matches) {
        $content = $matches[1];
        // Convert backticks to single quotes and concatenate
        $parts = preg_split('/(\{\{[^}]+\}\})/', $content, -1, PREG_SPLIT_DELIM_CAPTURE);
        $result = "innerHTML = ";
        $first = true;
        foreach ($parts as $part) {
            if (!$first) $result .= " + ";
            if (preg_match('/\{\{[^}]+\}\}/', $part)) {
                $result .= "'" . $part . "'";
            } else {
                $result .= "'" . str_replace("'", "\\'", $part) . "'";
            }
            $first = false;
        }
        return $result;
    }
];

$changeCount = 0;
foreach ($patterns as $pattern => $replacer) {
    $newContent = preg_replace_callback($pattern, function($matches) use ($replacer, &$changeCount) {
        $changeCount++;
        return $replacer($matches);
    }, $content);
    
    if ($newContent !== $content) {
        $content = $newContent;
    }
}

// Also fix the specific console.log issue that might have Twig
$content = preg_replace(
    '/console\.log\(`([^`]*)\$\{([^}]+)\}([^`]*)`\);/',
    'console.log(\'$1\' + $2 + \'$3\');',
    $content
);

// Save the file
file_put_contents($file, $content);

echo "✅ Fixed $changeCount template literal patterns\n\n";

// Clear Twig cache
$cacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($cacheDir)) {
    $files = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($cacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($files as $fileinfo) {
        $todo = ($fileinfo->isDir() ? 'rmdir' : 'unlink');
        $todo($fileinfo->getRealPath());
    }
    echo "✅ Cleared Twig cache\n";
}

echo "\nThe syntax errors should now be fixed!\n";
echo "\nPlease:\n";
echo "1. Clear your browser cache (Ctrl+Shift+R)\n";
echo "2. Visit: http://localhost/fit/public/invoices/create\n";

echo "</pre>";
?>