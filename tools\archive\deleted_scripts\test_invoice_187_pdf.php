<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Services\PdfService;

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
</style>';

echo "<h1>Testing Invoice FAC-2025-0187 PDF Data</h1>";

// Get invoice model
$invoice = Invoice::findBy('invoice_number', 'FAC-2025-0187');

if (!$invoice) {
    echo '<div class="warning">Invoice not found!</div>';
    exit;
}

echo '<div class="section">';
echo '<h2>Invoice Model Data</h2>';
echo '<pre>';
echo "ID: " . $invoice->id . "\n";
echo "Number: " . $invoice->invoice_number . "\n";
echo "Lines count: " . count($invoice->lines) . "\n";
echo '</pre>';
echo '</div>';

// Get invoice data as array (this is what PDF service receives)
$invoiceData = $invoice->toArray();

// Load relations
$invoice->load(['lines', 'client', 'user']);
$invoiceData = $invoice->toArray();

echo '<div class="section">';
echo '<h2>Invoice Data Array (what PDF receives)</h2>';
echo '<pre>';
echo "Lines in array: " . count($invoiceData['lines']) . "\n\n";
foreach ($invoiceData['lines'] as $i => $line) {
    echo "Line " . ($i + 1) . ":\n";
    echo "  ID: " . $line['id'] . "\n";
    echo "  Description: " . $line['description'] . "\n";
    echo "  Quantity: " . $line['quantity'] . "\n";
    echo "  Unit Price: " . $line['unit_price'] . "\n";
    echo "  VAT Rate: " . $line['vat_rate'] . "%\n";
    echo "  Sort Order: " . ($line['sort_order'] ?? 'NULL') . "\n\n";
}
echo '</pre>';
echo '</div>';

// Check raw database query
echo '<div class="section">';
echo '<h2>Raw Database Query</h2>';
$stmt = $db->prepare("
    SELECT id, description, quantity, unit_price, vat_rate, sort_order 
    FROM invoice_lines 
    WHERE invoice_id = ? 
    ORDER BY sort_order ASC, id ASC
");
$stmt->execute([$invoice->id]);
$dbLines = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<pre>';
echo "Database lines count: " . count($dbLines) . "\n\n";
foreach ($dbLines as $i => $line) {
    echo "DB Line " . ($i + 1) . ":\n";
    echo "  ID: " . $line['id'] . "\n";
    echo "  Description: " . $line['description'] . "\n";
    echo "  Sort Order: " . ($line['sort_order'] ?? 'NULL') . "\n\n";
}
echo '</pre>';
echo '</div>';

// Check for duplicates
$descriptions = array_column($dbLines, 'description');
$uniqueDescriptions = array_unique($descriptions);

if (count($descriptions) !== count($uniqueDescriptions)) {
    echo '<div class="warning">';
    echo '<strong>⚠️ Duplicate descriptions found in database!</strong><br>';
    $counts = array_count_values($descriptions);
    foreach ($counts as $desc => $count) {
        if ($count > 1) {
            echo "- \"$desc\" appears $count times<br>";
        }
    }
    echo '</div>';
} else {
    echo '<div class="section" style="background: #d4edda;">';
    echo '<strong>✓ No duplicate descriptions in database</strong>';
    echo '</div>';
}

// Test PDF service data preparation
echo '<div class="section">';
echo '<h2>Testing PDF Service Data</h2>';
try {
    $pdfService = new PdfService();
    
    // This is what the PDF service does internally
    $testInvoiceData = $invoiceData;
    
    // Check if lines are being processed
    echo '<pre>';
    echo "Invoice data has 'lines' key: " . (isset($testInvoiceData['lines']) ? 'Yes' : 'No') . "\n";
    echo "Number of lines: " . (isset($testInvoiceData['lines']) ? count($testInvoiceData['lines']) : 0) . "\n";
    
    if (isset($testInvoiceData['lines'])) {
        foreach ($testInvoiceData['lines'] as $i => $line) {
            echo "\nPDF Line " . ($i + 1) . ":\n";
            echo "  Description: " . $line['description'] . "\n";
        }
    }
    echo '</pre>';
} catch (Exception $e) {
    echo '<div class="warning">Error: ' . $e->getMessage() . '</div>';
}
echo '</div>';