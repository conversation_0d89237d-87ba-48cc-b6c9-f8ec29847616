<?php

namespace App\Models;

use App\Core\Model;

class Config extends Model
{
    protected $table = 'config';
    
    protected $fillable = [
        'key',
        'value',
        'category',
        'description'
    ];
    
    /**
     * Get a config value by key
     */
    public static function getValue($key, $default = null)
    {
        $config = self::where('key', $key)->first();
        return $config ? $config->value : $default;
    }
    
    /**
     * Set a config value
     */
    public static function setValue($key, $value, $category = 'general')
    {
        return self::updateOrCreate(
            ['key' => $key],
            ['value' => $value, 'category' => $category]
        );
    }
    
    /**
     * Get all configs by category
     */
    public static function getByCategory($category)
    {
        return self::where('category', $category)->get();
    }
}