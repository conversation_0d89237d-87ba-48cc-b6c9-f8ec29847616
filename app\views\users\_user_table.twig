<div class="table-responsive">
    <table class="table table-hover" id="usersTable-{{ tabId }}">
        <thead>
            <tr>
                <th>{{ __('users.user') }}</th>
                <th>{{ __('users.username') }}</th>
                <th>{{ __('common.email') }}</th>
                <th>{{ __('users.groups') }}</th>
                <th>{{ __('users.last_login') }}</th>
                <th>{{ __('common.status') }}</th>
                <th>{{ __('common.actions') }}</th>
            </tr>
        </thead>
        <tbody>
            {% for user in tabUsers %}
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        {% if user.avatar %}
                            <img src="{{ user.avatar starts with base_url ? user.avatar : base_url ~ '/uploads/avatars/' ~ user.avatar }}" 
                                 class="rounded-circle me-2" style="width: 40px; height: 40px;" 
                                 alt="{{ user.name }}">
                        {% else %}
                            <div class="avatar avatar-sm me-2">
                                <span class="avatar-text rounded-circle bg-primary">
                                    {{ user.name|first|upper }}
                                </span>
                            </div>
                        {% endif %}
                        <div>
                            <div class="fw-bold">{{ user.name }}</div>
                            <small class="text-muted">ID: {{ user.id }}</small>
                        </div>
                    </div>
                </td>
                <td>{{ user.username }}</td>
                <td>
                    <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                </td>
                <td>
                    {% if user.groups %}
                        {{ user.groups }}
                    {% else %}
                        <span class="text-muted">-</span>
                    {% endif %}
                </td>
                <td>
                    {% if user.last_login %}
                        {{ user.last_login|date('d/m/Y H:i') }}
                    {% else %}
                        <span class="text-muted">{{ __('users.never') }}</span>
                    {% endif %}
                </td>
                <td>
                    {% if user.is_active %}
                        <span class="badge bg-success">{{ __('common.active') }}</span>
                    {% else %}
                        <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                    {% endif %}
                </td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="{{ base_url }}/users/{{ user.id }}" 
                           class="btn btn-outline-primary" 
                           data-bs-toggle="tooltip" 
                           title="{{ __('common.view') }}">
                            <i class="bi bi-eye"></i>
                        </a>
                        <a href="{{ base_url }}/users/{{ user.id }}/edit" 
                           class="btn btn-outline-secondary" 
                           data-bs-toggle="tooltip" 
                           title="{{ __('common.edit') }}">
                            <i class="bi bi-pencil"></i>
                        </a>
                        {% if user.id != session.user_id %}
                        <button type="button" 
                                class="btn btn-outline-{{ user.is_active ? 'warning' : 'success' }}" 
                                onclick="toggleUserStatus({{ user.id }}, {{ user.is_active ? 'false' : 'true' }})"
                                data-bs-toggle="tooltip" 
                                title="{{ user.is_active ? __('users.deactivate') : __('users.activate') }}">
                            <i class="bi bi-{{ user.is_active ? 'x-circle' : 'check-circle' }}"></i>
                        </button>
                        <button type="button" 
                                class="btn btn-outline-info" 
                                onclick="resetPassword({{ user.id }})"
                                data-bs-toggle="tooltip" 
                                title="{{ __('users.reset_password') }}">
                            <i class="bi bi-key"></i>
                        </button>
                        <a href="{{ base_url }}/invoices/create?type=rental&user_id={{ user.id }}" 
                           class="btn btn-outline-success" 
                           data-bs-toggle="tooltip" 
                           title="{{ __('invoices.create_loy_invoice') | default('Create LOY Invoice') }}">
                            <i class="bi bi-receipt"></i>
                        </a>
                        <button type="button" 
                                class="btn btn-outline-danger" 
                                onclick="deleteUser({{ user.id }})"
                                data-bs-toggle="tooltip" 
                                title="{{ __('common.delete') }}">
                            <i class="bi bi-trash"></i>
                        </button>
                        {% endif %}
                    </div>
                </td>
            </tr>
            {% else %}
            <tr>
                <td colspan="7" class="text-center py-4 text-muted">
                    {{ __('users.no_users_found') }}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>