<?php
/**
 * <PERSON>ript to categorize and organize maintenance scripts
 */

$scripts = [
    // Invoice-related scripts
    'invoice' => [
        'check_invoice_sequences.php' => 'Check invoice number sequences',
        'check_invoice_structure.php' => 'Check invoice table structure',
        'check_invoice_lines.php' => 'Check invoice lines data',
        'check_duplicate_invoice_lines.php' => 'Check for duplicate invoice lines',
        'fix_duplicate_invoice_lines.php' => 'Fix duplicate invoice lines',
        'fix_invoice_number_0186.php' => 'Fix specific invoice number issue',
        'fix_invoice_187_and_sequence.php' => 'Fix invoice 187 and sequence',
        'fix_invoice_246_lines.php' => 'Fix invoice 246 lines',
        'clear_invoice_cache.php' => 'Clear invoice cache',
        'reset_invoice_sequence.php' => 'Reset invoice sequence',
        'test_invoice_pdf_fix.php' => 'Test invoice PDF generation',
        'debug_invoice_187.php' => 'Debug specific invoice',
        'debug_invoice_241.php' => 'Debug specific invoice',
        'debug_invoice_246_pdf.php' => 'Debug specific invoice PDF',
        'diagnose_invoice_187.php' => 'Diagnose specific invoice',
        'diagnose_invoice_246.php' => 'Diagnose specific invoice',
        'find_recent_invoices.php' => 'Find recent invoices',
        'check_sequence_and_invoices.php' => 'Check sequences and invoices',
        'force_sequence_reset.php' => 'Force reset sequence',
        'setup_invoice_sequence.php' => 'Setup invoice sequence',
        'test_new_invoice_numbering.php' => 'Test new invoice numbering',
        'test_next_invoice_number.php' => 'Test next invoice number',
        'update_invoice_sequence.php' => 'Update invoice sequence',
        'check_invoice_types_prefixes.php' => 'Check invoice type prefixes',
        'fix_invoice_loy_prefix.php' => 'Fix LOY prefix issues',
        'update_invoice_type_prefixes.php' => 'Update invoice type prefixes',
    ],
    
    // Database diagnostics
    'diagnostics' => [
        'check_column_types.php' => 'Check database column types',
        'check_config_table.php' => 'Check configuration table',
        'check_invoice_table_structure.php' => 'Check invoice table structure',
        'check_document_types.php' => 'Check document types',
        'check_payment_terms_data.php' => 'Check payment terms data',
        'debug_payment_terms.php' => 'Debug payment terms',
        'simple_check.php' => 'Simple database check',
    ],
    
    // Migration scripts
    'migration' => [
        'run_migration_072.php' => 'Run migration 072',
        'run_migration_072_simple.php' => 'Run migration 072 simple',
        'run_migration_env.php' => 'Run migration with env',
        'run_migration_fixed.php' => 'Run fixed migration',
        'run_migration_manual.php' => 'Run manual migration',
        'run_prefix_migration.php' => 'Run prefix migration',
    ],
    
    // User management
    'user' => [
        'add_users_to_coach_group.php' => 'Add users to coach group',
        'add_coaches_to_group.php' => 'Add coaches to group',
        'create_coach_users.php' => 'Create coach users',
        'capitalize_usernames.php' => 'Capitalize usernames',
        'check_user_addresses.php' => 'Check user addresses',
        'add_address_to_all_users.php' => 'Add addresses to users',
    ],
    
    // One-time fixes (can be deleted)
    'one_time_fixes' => [
        'fix_to_0186.php' => 'One-time fix',
        'fix_invoice_number_186.php' => 'One-time fix',
        'fix_sequence_to_186.php' => 'One-time fix',
        'reset_sequence_185.php' => 'One-time fix',
        'fix_invoice_187_duplicates.php' => 'One-time fix',
        'force_reset_sequence.php' => 'One-time fix',
        'reset_sequence_to_185.php' => 'One-time fix',
        'fix_invoice_sequence_186.php' => 'One-time fix',
    ],
    
    // Test scripts (development only)
    'test_scripts' => [
        'test_invoice_create.php' => 'Test invoice creation',
        'test_invoice_creation.php' => 'Test invoice creation',
        'test_invoice_post.php' => 'Test invoice POST',
        'test_create_invoice.php' => 'Test create invoice',
        'test_pdf.php' => 'Test PDF generation',
        'test_pdf_simple.php' => 'Test simple PDF',
        'test_tcpdf_simple.php' => 'Test TCPDF',
        'test-*.html' => 'Various HTML test files',
        'invoice-creation-fix-summary.php' => 'Fix summary',
        'footer-test.php' => 'Footer test',
    ],
];

// Scripts to definitely keep
$keep_scripts = [
    'index.php',
    'install.php',
    'invoice-pdf.php',
];

echo "Script Categorization Report\n";
echo "============================\n\n";

foreach ($scripts as $category => $files) {
    echo "## $category\n";
    echo str_repeat('-', 40) . "\n";
    foreach ($files as $file => $description) {
        echo "- $file: $description\n";
    }
    echo "\n";
}

echo "\n## Recommendations:\n";
echo "1. Move useful maintenance scripts to /tools/maintenance/\n";
echo "2. Delete one-time fix scripts that are no longer needed\n";
echo "3. Keep core scripts (index.php, install.php, invoice-pdf.php)\n";
echo "4. Archive test scripts or move to a test directory\n";