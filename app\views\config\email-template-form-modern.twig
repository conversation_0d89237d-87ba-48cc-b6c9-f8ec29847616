{% extends "base-modern.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style>
    .variable-tag {
        cursor: pointer;
        user-select: none;
    }
    .variable-tag:hover {
        transform: scale(1.05);
    }
    .condition-row {
        background: #f8f9fa;
        padding: 10px;
        margin-bottom: 10px;
        border-radius: 5px;
    }
    .html-editor {
        min-height: 400px;
        border: 1px solid #dee2e6;
        border-radius: 0.25rem;
        padding: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config/email-templates" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="submit" form="templateForm" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
            </button>
        </div>
    </div>

    <form id="templateForm" method="POST" 
          action="{{ action == 'create' ? base_url ~ '/config/email-templates' : base_url ~ '/config/email-templates/' ~ template.id }}" 
          class="needs-validation" novalidate>
        
        {% if action == 'edit' %}
            <input type="hidden" name="_method" value="PUT">
        {% endif %}
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">

        <div class="row">
            <!-- Main Form -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header bg-white">
                        <ul class="nav nav-tabs card-header-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#basic-tab" role="tab">
                                    <i class="bi bi-info-circle me-2"></i>{{ __('config.basic_info') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#content-tab" role="tab">
                                    <i class="bi bi-file-text me-2"></i>{{ __('config.content') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#conditions-tab" role="tab">
                                    <i class="bi bi-filter me-2"></i>{{ __('config.conditions') }}
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#preview-tab" role="tab">
                                    <i class="bi bi-eye me-2"></i>{{ __('common.preview') }}
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <!-- Basic Info Tab -->
                            <div class="tab-pane fade show active" id="basic-tab" role="tabpanel">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="name" class="form-label">{{ __('config.template_name') }} *</label>
                                        <input type="text" class="form-control" id="name" name="name" 
                                               value="{{ template.name|default('') }}" required>
                                        <div class="invalid-feedback">{{ __('config.template_name_required') }}</div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="code" class="form-label">{{ __('config.template_code') }} *</label>
                                        <input type="text" class="form-control" id="code" name="code" 
                                               value="{{ template.code|default('') }}" required
                                               pattern="[a-z0-9_]+" title="{{ __('config.code_format_hint') }}">
                                        <small class="text-muted">{{ __('config.code_format_hint') }}</small>
                                        <div class="invalid-feedback">{{ __('config.template_code_required') }}</div>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="email_type" class="form-label">{{ __('config.email_type') }}</label>
                                        <select class="form-select" id="email_type" name="email_type">
                                            {% for key, label in emailTypes %}
                                                <option value="{{ key }}" {{ template.email_type == key ? 'selected' : '' }}>
                                                    {{ label }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="invoice_type" class="form-label">{{ __('config.invoice_type') }}</label>
                                        <select class="form-select" id="invoice_type" name="invoice_type">
                                            <option value="">{{ __('config.all_invoice_types') }}</option>
                                            {% for type in invoiceTypes %}
                                                <option value="{{ type.code }}" {{ template.invoice_type == type.code ? 'selected' : '' }}>
                                                    {{ type.name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="priority" class="form-label">{{ __('config.priority') }}</label>
                                        <select class="form-select" id="priority" name="priority">
                                            <option value="1" {{ template.priority == 1 ? 'selected' : '' }}>Lowest</option>
                                            <option value="2" {{ template.priority == 2 ? 'selected' : '' }}>Low</option>
                                            <option value="3" {{ template.priority == 3 ? 'selected' : '' }}>Normal</option>
                                            <option value="4" {{ template.priority == 4 ? 'selected' : '' }}>High</option>
                                            <option value="5" {{ template.priority == 5 ? 'selected' : '' }}>Highest</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="parent_template_id" class="form-label">{{ __('config.parent_template') }}</label>
                                        <select class="form-select" id="parent_template_id" name="parent_template_id">
                                            <option value="">{{ __('config.no_parent') }}</option>
                                            {% for parent in parentTemplates %}
                                                <option value="{{ parent.id }}" {{ template.parent_template_id == parent.id ? 'selected' : '' }}>
                                                    {{ parent.name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                        <small class="text-muted">{{ __('config.parent_template_hint') }}</small>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label class="form-label">{{ __('common.status') }}</label>
                                        <div class="form-check form-switch mt-2">
                                            <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                                   value="1" {{ template.is_active|default(true) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active">
                                                {{ __('config.template_active') }}
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Content Tab -->
                            <div class="tab-pane fade" id="content-tab" role="tabpanel">
                                <div class="mb-3">
                                    <label for="subject" class="form-label">{{ __('config.email_subject') }} *</label>
                                    <input type="text" class="form-control" id="subject" name="subject" 
                                           value="{{ template.subject|default('') }}" required>
                                    <div class="invalid-feedback">{{ __('config.template_subject_required') }}</div>
                                </div>
                                
                                <ul class="nav nav-pills mb-3" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active" data-bs-toggle="pill" data-bs-target="#html-content" type="button">
                                            HTML Version
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" data-bs-toggle="pill" data-bs-target="#text-content" type="button">
                                            Text Version
                                        </button>
                                    </li>
                                </ul>
                                
                                <div class="tab-content">
                                    <div class="tab-pane fade show active" id="html-content">
                                        <div class="mb-3">
                                            <label for="body_html" class="form-label">{{ __('config.email_body_html') }}</label>
                                            <textarea class="form-control html-editor" id="body_html" name="body_html" rows="15">{{ template.body_html|default(template.body|default('')) }}</textarea>
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="text-content">
                                        <div class="mb-3">
                                            <label for="body_text" class="form-label">{{ __('config.email_body_text') }}</label>
                                            <textarea class="form-control" id="body_text" name="body_text" rows="15">{{ template.body_text|default('') }}</textarea>
                                            <button type="button" class="btn btn-sm btn-secondary mt-2" onclick="generateTextVersion()">
                                                <i class="bi bi-arrow-repeat me-2"></i>{{ __('config.generate_from_html') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Hidden field for backward compatibility -->
                                <input type="hidden" id="body" name="body" value="{{ template.body|default('') }}">
                            </div>
                            
                            <!-- Conditions Tab -->
                            <div class="tab-pane fade" id="conditions-tab" role="tabpanel">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    {{ __('config.conditions_explanation') }}
                                </div>
                                
                                <div id="conditionsContainer">
                                    <!-- Conditions will be added here dynamically -->
                                </div>
                                
                                <button type="button" class="btn btn-outline-primary" onclick="addCondition()">
                                    <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_condition') }}
                                </button>
                                
                                <input type="hidden" id="conditions" name="conditions" value="{{ template.conditions|json_encode }}">
                            </div>
                            
                            <!-- Preview Tab -->
                            <div class="tab-pane fade" id="preview-tab" role="tabpanel">
                                <div class="row g-3 mb-3">
                                    <div class="col-md-6">
                                        <label class="form-label">{{ __('config.test_with_invoice') }}</label>
                                        <select class="form-select" id="previewInvoiceId">
                                            <option value="">{{ __('config.use_sample_data') }}</option>
                                            {% for invoice in recentInvoices|default([]) %}
                                                <option value="{{ invoice.id }}">
                                                    #{{ invoice.invoice_number }} - {{ invoice.client_name }}
                                                </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-primary mt-4" onclick="updatePreview()">
                                            <i class="bi bi-arrow-clockwise me-2"></i>{{ __('config.update_preview') }}
                                        </button>
                                    </div>
                                </div>
                                
                                <div class="border rounded p-3">
                                    <div class="mb-3">
                                        <strong>{{ __('config.subject') }}:</strong>
                                        <div id="previewSubject" class="text-muted">-</div>
                                    </div>
                                    <hr>
                                    <div id="previewBody">
                                        <p class="text-muted">{{ __('config.click_update_preview') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Available Variables -->
                <div class="card shadow mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-code-slash me-2"></i>{{ __('config.available_variables') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="accordion accordion-flush" id="variablesAccordion">
                            {% for category, vars in availableVariables %}
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button {{ loop.first ? '' : 'collapsed' }}" type="button" 
                                            data-bs-toggle="collapse" data-bs-target="#vars-{{ category }}">
                                        {{ __('config.vars_' ~ category) }}
                                    </button>
                                </h2>
                                <div id="vars-{{ category }}" class="accordion-collapse collapse {{ loop.first ? 'show' : '' }}" 
                                     data-bs-parent="#variablesAccordion">
                                    <div class="accordion-body">
                                        {% for var, desc in vars %}
                                        <span class="badge bg-secondary variable-tag mb-1" 
                                              onclick="insertVariable('{{ var }}')" 
                                              title="{{ desc }}">
                                            {{'{'}}{{ var }}{{'}'}}
                                        </span>
                                        {% endfor %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>

                <!-- Help -->
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-question-circle me-2"></i>{{ __('common.help') }}</h6>
                    </div>
                    <div class="card-body">
                        <h6>{{ __('config.template_tips') }}</h6>
                        <ul class="small">
                            <li>{{ __('config.tip_use_variables') }}</li>
                            <li>{{ __('config.tip_html_formatting') }}</li>
                            <li>{{ __('config.tip_test_thoroughly') }}</li>
                            <li>{{ __('config.tip_priority_system') }}</li>
                            <li>{{ __('config.tip_conditions') }}</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
// Initialize conditions
let conditions = {{ template.conditions|default([])|json_encode|raw }};

// Initialize conditions UI
function initializeConditions() {
    const container = document.getElementById('conditionsContainer');
    container.innerHTML = '';
    
    if (Array.isArray(conditions)) {
        conditions.forEach((condition, index) => {
            addConditionRow(condition, index);
        });
    }
}

// Add condition row
function addCondition() {
    const condition = {
        field: '',
        operator: '=',
        value: ''
    };
    conditions.push(condition);
    addConditionRow(condition, conditions.length - 1);
    updateConditionsInput();
}

// Add condition row to UI
function addConditionRow(condition, index) {
    const container = document.getElementById('conditionsContainer');
    const row = document.createElement('div');
    row.className = 'condition-row';
    row.innerHTML = `
        <div class="row g-2">
            <div class="col-md-4">
                <input type="text" class="form-control" placeholder="{{ __('config.field_name') }}"
                       value="${condition.field || ''}" onchange="updateCondition(${index}, 'field', this.value)">
            </div>
            <div class="col-md-3">
                <select class="form-select" onchange="updateCondition(${index}, 'operator', this.value)">
                    <option value="=" ${condition.operator === '=' ? 'selected' : ''}>=</option>
                    <option value="!=" ${condition.operator === '!=' ? 'selected' : ''}>!=</option>
                    <option value=">" ${condition.operator === '>' ? 'selected' : ''}>></option>
                    <option value=">=" ${condition.operator === '>=' ? 'selected' : ''}>>=</option>
                    <option value="<" ${condition.operator === '<' ? 'selected' : ''}><</option>
                    <option value="<=" ${condition.operator === '<=' ? 'selected' : ''}><=</option>
                    <option value="contains" ${condition.operator === 'contains' ? 'selected' : ''}>Contains</option>
                    <option value="not_contains" ${condition.operator === 'not_contains' ? 'selected' : ''}>Not Contains</option>
                </select>
            </div>
            <div class="col-md-4">
                <input type="text" class="form-control" placeholder="{{ __('config.value') }}"
                       value="${condition.value || ''}" onchange="updateCondition(${index}, 'value', this.value)">
            </div>
            <div class="col-md-1">
                <button type="button" class="btn btn-sm btn-danger" onclick="removeCondition(${index})">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(row);
}

// Update condition
function updateCondition(index, field, value) {
    conditions[index][field] = value;
    updateConditionsInput();
}

// Remove condition
function removeCondition(index) {
    conditions.splice(index, 1);
    initializeConditions();
    updateConditionsInput();
}

// Update hidden conditions input
function updateConditionsInput() {
    document.getElementById('conditions').value = JSON.stringify(conditions);
}

// Insert variable at cursor position
function insertVariable(variable) {
    const activeElement = document.activeElement;
    if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
        const start = activeElement.selectionStart;
        const end = activeElement.selectionEnd;
        const text = activeElement.value;
        const before = text.substring(0, start);
        const after = text.substring(end, text.length);
        activeElement.value = before + '{' + variable + '}' + after;
        activeElement.selectionStart = activeElement.selectionEnd = start + variable.length + 2;
        activeElement.focus();
    } else {
        // If no input is focused, insert into subject field
        const subjectField = document.getElementById('subject');
        subjectField.value += '{' + variable + '}';
        subjectField.focus();
    }
}

// Generate text version from HTML
function generateTextVersion() {
    const htmlContent = document.getElementById('body_html').value;
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = htmlContent;
    
    // Simple HTML to text conversion
    let textContent = tempDiv.textContent || tempDiv.innerText || '';
    
    // Preserve some formatting
    textContent = textContent.replace(/\n\s*\n/g, '\n\n');
    
    document.getElementById('body_text').value = textContent.trim();
}

// Update preview
function updatePreview() {
    const templateId = {{ template.id|default('null') }};
    const invoiceId = document.getElementById('previewInvoiceId')?.value || '';
    
    // Get current form values
    const formData = new FormData();
    formData.append('template_id', templateId || 'preview');
    formData.append('test_data[invoice_id]', invoiceId);
    formData.append('csrf_token', '{{ csrf_token }}');
    
    // If creating new template, send current content
    if (!templateId) {
        formData.append('preview_subject', document.getElementById('subject').value);
        formData.append('preview_body', document.getElementById('body_html').value);
    }
    
    fetch('{{ base_url }}/config/email-templates/preview', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('previewSubject').textContent = data.preview.subject;
            document.getElementById('previewBody').innerHTML = data.preview.body_html || data.preview.body;
        } else {
            toastr.error(data.message);
        }
    })
    .catch(error => {
        toastr.error('Error loading preview');
    });
}

// Sync body field on form submit
document.getElementById('templateForm').addEventListener('submit', function(e) {
    // Copy HTML content to body field for backward compatibility
    document.getElementById('body').value = document.getElementById('body_html').value;
});

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeConditions();
    
    // Initialize a rich text editor if available
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#body_html',
            height: 400,
            menubar: false,
            plugins: [
                'advlist autolink lists link image charmap print preview anchor',
                'searchreplace visualblocks code fullscreen',
                'insertdatetime media table paste code help wordcount'
            ],
            toolbar: 'undo redo | formatselect | ' +
                'bold italic backcolor | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }'
        });
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>
{% endblock %}