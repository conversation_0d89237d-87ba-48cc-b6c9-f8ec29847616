<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Fresh Test - Cache Busting</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; text-align: center; }
        .container { max-width: 600px; margin: 0 auto; }
        .header { background: #dc3545; color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { padding: 12px 24px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .countdown { font-size: 24px; font-weight: bold; color: #dc3545; }
        .instructions { text-align: left; margin: 20px 0; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Force Fresh Test</h1>
            <p>Cache-busting page to ensure fresh JavaScript loading</p>
        </div>

        <div class="card">
            <h2>🎯 Automatic Cache Busting</h2>
            <p>This page will automatically redirect to the coach dropdown test with cache-busting parameters.</p>
            <div class="countdown" id="countdown">5</div>
            <p>Redirecting in <span id="seconds">5</span> seconds...</p>
        </div>

        <div class="card">
            <h2>🛠️ Manual Options</h2>
            <a href="#" class="btn btn-success" id="minimalTest">🎯 Minimal Dropdown Test</a>
            <a href="#" class="btn btn-primary" id="fullInvoiceTest">📋 Full Invoice Test</a>
            <a href="#" class="btn btn-warning" id="diagnosticTest">🔍 Diagnostic Test</a>
            <button class="btn btn-danger" onclick="clearAllCacheAndReload()">🧹 Clear All Cache</button>
        </div>

        <div class="card">
            <h2>📋 Cache Clearing Instructions</h2>
            <div class="instructions">
                <h3>Firefox:</h3>
                <div class="code">
                    1. Press Ctrl+Shift+Delete<br>
                    2. Select "Everything" in time range<br>
                    3. Check ALL boxes<br>
                    4. Click "Clear Now"<br>
                    5. Close and reopen browser
                </div>
                
                <h3>Alternative Method:</h3>
                <div class="code">
                    1. Open Private Window (Ctrl+Shift+P)<br>
                    2. Test in private mode<br>
                    3. No cache interference
                </div>
            </div>
        </div>

        <div class="card">
            <h2>🔍 Expected Results</h2>
            <p>After cache clearing, you should see:</p>
            <ul style="text-align: left;">
                <li>✅ No JavaScript syntax errors in console</li>
                <li>✅ Debug functions properly defined</li>
                <li>✅ Coach dropdown populates immediately</li>
                <li>✅ All 4 coaches visible in dropdown</li>
            </ul>
        </div>
    </div>

    <script>
        // Generate cache-busting timestamp
        const timestamp = Date.now();
        const cacheParam = '_cache_bust=' + timestamp;
        const testParam = '_test=fresh';
        
        // Set up URLs with cache busting
        const minimalTestUrl = '/fit/public/minimal-coach-dropdown-test.html?type=location&' + cacheParam + '&' + testParam;
        const fullInvoiceUrl = '/fit/public/invoices/create?type=location&' + cacheParam + '&' + testParam;
        const diagnosticUrl = '/fit/public/comprehensive-javascript-debug.html?' + cacheParam + '&' + testParam;
        
        // Update button links
        document.getElementById('minimalTest').href = minimalTestUrl;
        document.getElementById('fullInvoiceTest').href = fullInvoiceUrl;
        document.getElementById('diagnosticTest').href = diagnosticUrl;
        
        // Countdown timer
        let seconds = 5;
        const countdownElement = document.getElementById('countdown');
        const secondsElement = document.getElementById('seconds');
        
        const countdownInterval = setInterval(() => {
            seconds--;
            countdownElement.textContent = seconds;
            secondsElement.textContent = seconds;
            
            if (seconds <= 0) {
                clearInterval(countdownInterval);
                // Redirect to minimal test
                window.location.href = minimalTestUrl;
            }
        }, 1000);
        
        // Clear all cache function
        function clearAllCacheAndReload() {
            // Clear localStorage
            try {
                localStorage.clear();
                sessionStorage.clear();
                console.log('✅ Cleared localStorage and sessionStorage');
            } catch (error) {
                console.log('⚠️ Could not clear storage:', error);
            }
            
            // Clear any cached data
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            // Force reload with cache busting
            const url = new URL(window.location.href);
            url.searchParams.set('_force_reload', Date.now());
            window.location.href = url.toString();
        }
        
        // Show cache busting info
        console.log('🔄 Cache-busting parameters:');
        console.log('Timestamp:', timestamp);
        console.log('Minimal test URL:', minimalTestUrl);
        console.log('Full invoice URL:', fullInvoiceUrl);
        console.log('Diagnostic URL:', diagnosticUrl);
        
        // Detect if this is a forced reload
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('_force_reload')) {
            console.log('✅ This is a forced reload with cache busting');
        }
    </script>
</body>
</html>