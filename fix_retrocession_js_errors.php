<?php
/**
 * Fix JavaScript errors in retrocession invoice creation
 */

echo "<pre>";
echo "=== Fixing JavaScript Errors in Retrocession Invoice Creation ===\n\n";

// Fix create-modern.twig
$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($createFile);

// 1. Fix the displayBillableInfo function - move isRetrocessionInvoice declaration to the top
$searchPattern = 'function displayBillableInfo(billableType, billableId) {
    console.log(\'🔍 displayBillableInfo called:\', billableType, billableId);
    
    // Check if this is a retrocession invoice
    const invoiceTypeSelect = document.getElementById(\'invoice_type_id\');
    const selectedOption = invoiceTypeSelect ? invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex] : null;
    const typeCode = selectedOption ? selectedOption.getAttribute(\'data-prefix\') : \'\';
    const isRetrocessionInvoice = window.location.search.includes(\'type=retrocession\') || 
                                  (typeCode && typeCode.startsWith(\'RET\'));
    
    if (!billableId) {';

$replaceWith = 'function displayBillableInfo(billableType, billableId) {
    console.log(\'🔍 displayBillableInfo called:\', billableType, billableId);
    
    if (!billableId) {';

$content = str_replace($searchPattern, $replaceWith, $content);

// 2. Fix the VAT status section - add isRetrocessionInvoice check inside the function
$vatStatusSearch = '// Intracommunity VAT status
            // Don\'t show VAT info for retrocession invoices
            const isRetrocessionInvoice = window.location.search.includes(\'type=retrocession\') || 
                                        (typeCode && typeCode.startsWith(\'RET\'));
            
            if (!isRetrocessionInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {';

$vatStatusReplace = '// Intracommunity VAT status
            // Check if this is a retrocession invoice
            const invoiceTypeSelect = document.getElementById(\'invoice_type_id\');
            const selectedOption = invoiceTypeSelect ? invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex] : null;
            const typeCode = selectedOption ? selectedOption.getAttribute(\'data-prefix\') : \'\';
            const isRetrocessionInvoice = window.location.search.includes(\'type=retrocession\') || 
                                        (typeCode && typeCode.startsWith(\'RET\'));
            
            if (!isRetrocessionInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {';

$content = str_replace($vatStatusSearch, $vatStatusReplace, $content);

// 3. Fix the exclude_patient_line checkbox access - add null check
$checkboxSearch = '// Check if patient line should be excluded
        const excludePatient = document.getElementById(\'exclude_patient_line\').checked;';

$checkboxReplace = '// Check if patient line should be excluded
        const excludePatientCheckbox = document.getElementById(\'exclude_patient_line\');
        const excludePatient = excludePatientCheckbox ? excludePatientCheckbox.checked : false;';

$content = str_replace($checkboxSearch, $checkboxReplace, $content);

// 4. Fix the addEventListener for exclude_patient_line - add null check
$eventListenerSearch = '// Also handle checkbox change
    document.getElementById(\'exclude_patient_line\').addEventListener(\'change\', function() {';

$eventListenerReplace = '// Also handle checkbox change
    const excludePatientCheckbox = document.getElementById(\'exclude_patient_line\');
    if (excludePatientCheckbox) {
        excludePatientCheckbox.addEventListener(\'change\', function() {';

$content = str_replace($eventListenerSearch, $eventListenerReplace, $content);

// Need to close the if statement
$closingBracketSearch = '            calculateRetrocessionTotals();
        }
    });';

$closingBracketReplace = '            calculateRetrocessionTotals();
        }
    });
    }';

$content = str_replace($closingBracketSearch, $closingBracketReplace, $content);

// 5. Fix the toggleExcludePatientLine function
$toggleSearch = '// Add event listener for document type change
    document.getElementById(\'document_type_id\').addEventListener(\'change\', toggleExcludePatientLine);';

$toggleReplace = '// Add event listener for document type change
    const docTypeSelect = document.getElementById(\'document_type_id\');
    if (docTypeSelect) {
        docTypeSelect.addEventListener(\'change\', toggleExcludePatientLine);
    }';

$content = str_replace($toggleSearch, $toggleReplace, $content);

// Save the fixed content
file_put_contents($createFile, $content);

echo "✓ Fixed JavaScript errors in create-modern.twig\n";
echo "\nChanges made:\n";
echo "1. Removed duplicate isRetrocessionInvoice declaration in displayBillableInfo\n";
echo "2. Added null checks for exclude_patient_line checkbox\n";
echo "3. Added null checks for event listeners\n";
echo "4. Fixed variable scope issues\n";

echo "\n✅ JavaScript errors should now be resolved!\n";
echo "\nThe retrocession invoice creation should work without errors.\n";

echo "</pre>";