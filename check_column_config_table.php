<?php
// Check document_type_column_configs table structure
require_once __DIR__ . '/vendor/autoload.php';

try {
    // Load environment variables
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    // Database connection
    $dsn = sprintf(
        'mysql:host=%s;port=%s;dbname=%s;charset=utf8mb4',
        $_ENV['DB_HOST'],
        $_ENV['DB_PORT'],
        $_ENV['DB_DATABASE']
    );
    
    $pdo = new PDO($dsn, $_ENV['DB_USERNAME'], $_ENV['DB_PASSWORD']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database: {$_ENV['DB_DATABASE']}\n\n";
    
    // Check if table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'document_type_column_configs'");
    if ($stmt->rowCount() === 0) {
        echo "Table 'document_type_column_configs' does not exist!\n";
        exit;
    }
    
    // Get table structure
    echo "Table structure for 'document_type_column_configs':\n";
    echo str_repeat('-', 80) . "\n";
    
    $stmt = $pdo->query("DESCRIBE document_type_column_configs");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    printf("%-20s %-30s %-10s %-10s %-20s\n", "Field", "Type", "Null", "Key", "Default");
    echo str_repeat('-', 80) . "\n";
    
    foreach ($columns as $column) {
        printf("%-20s %-30s %-10s %-10s %-20s\n", 
            $column['Field'], 
            $column['Type'], 
            $column['Null'], 
            $column['Key'], 
            $column['Default'] ?? 'NULL'
        );
    }
    
    echo "\n";
    
    // Check for missing columns
    $expectedColumns = ['table_name', 'column_configs', 'document_type_id'];
    $actualColumns = array_column($columns, 'Field');
    $missingColumns = array_diff($expectedColumns, $actualColumns);
    
    if (!empty($missingColumns)) {
        echo "Missing expected columns: " . implode(', ', $missingColumns) . "\n";
    } else {
        echo "All expected columns are present.\n";
    }
    
    // Check sample data
    echo "\nSample data (first 5 rows):\n";
    echo str_repeat('-', 80) . "\n";
    
    $stmt = $pdo->query("SELECT * FROM document_type_column_configs LIMIT 5");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rows)) {
        echo "No data found in table.\n";
    } else {
        foreach ($rows as $i => $row) {
            echo "Row " . ($i + 1) . ":\n";
            foreach ($row as $key => $value) {
                if ($key === 'column_configs' && $value) {
                    echo "  $key: " . substr($value, 0, 100) . "...\n";
                } else {
                    echo "  $key: $value\n";
                }
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}