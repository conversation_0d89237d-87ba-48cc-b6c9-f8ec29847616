<?php
/**
 * Fix rounding issue for existing location invoices
 * This script updates invoices that show 794.97 instead of 795.00
 */

require_once __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS'] ?? '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== FIXING INVOICE ROUNDING ISSUES ===\n\n";
    
    // Find location invoices with the rounding issue
    $stmt = $pdo->prepare("
        SELECT i.id, i.invoice_number, i.subtotal, i.vat_amount, i.total, i.user_id,
               u.first_name, u.last_name, u.vat_intercommunautaire,
               it.code as invoice_type_code, it.prefix as invoice_type_prefix
        FROM invoices i
        LEFT JOIN users u ON i.user_id = u.id
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.total = 794.97
        AND (it.code = 'location' OR it.prefix LIKE '%LOC%' OR i.invoice_number LIKE '%LOC%')
    ");
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invoices) === 0) {
        echo "No invoices found with total of 794.97\n";
        exit;
    }
    
    echo "Found " . count($invoices) . " invoice(s) to fix:\n\n";
    
    foreach ($invoices as $invoice) {
        echo "Invoice: {$invoice['invoice_number']}\n";
        echo "User: {$invoice['first_name']} {$invoice['last_name']}\n";
        echo "VAT Number: {$invoice['vat_intercommunautaire']}\n";
        
        // Check if this is a Luxembourg VAT or no VAT
        $vatNumber = $invoice['vat_intercommunautaire'] ?? '';
        $isLuxembourgVat = !empty($vatNumber) && strpos(strtoupper($vatNumber), 'LU') === 0;
        $hasVatNumber = !empty($vatNumber);
        
        if (!$hasVatNumber || $isLuxembourgVat) {
            echo "Eligible for TTC rounding fix\n";
            
            // Get invoice items
            $stmt2 = $pdo->prepare("
                SELECT * FROM invoice_lines 
                WHERE invoice_id = ? 
                ORDER BY id
            ");
            $stmt2->execute([$invoice['id']]);
            $items = $stmt2->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate expected TTC total
            $expectedTTCTotal = 0;
            foreach ($items as $item) {
                $quantity = floatval($item['quantity']);
                $unitPrice = floatval($item['unit_price']);
                
                // Check for known TTC prices
                if (abs($unitPrice - 12.82) < 0.01) { // €15 / 1.17
                    $expectedTTCTotal += $quantity * 15.00;
                    echo "  - {$item['description']}: {$quantity} x €15.00 TTC = €" . ($quantity * 15.00) . "\n";
                } elseif (abs($unitPrice - 25.64) < 0.01) { // €30 / 1.17
                    $expectedTTCTotal += $quantity * 30.00;
                    echo "  - {$item['description']}: {$quantity} x €30.00 TTC = €" . ($quantity * 30.00) . "\n";
                }
            }
            
            echo "Expected TTC Total: €" . number_format($expectedTTCTotal, 2) . "\n";
            echo "Current Total: €" . number_format($invoice['total'], 2) . "\n";
            
            if ($expectedTTCTotal == 795.00 && $invoice['total'] == 794.97) {
                // Apply the fix
                $newVatAmount = $invoice['vat_amount'] + 0.03;
                $newTotal = 795.00;
                
                echo "Applying fix:\n";
                echo "  - VAT: €" . number_format($invoice['vat_amount'], 2) . " → €" . number_format($newVatAmount, 2) . "\n";
                echo "  - Total: €" . number_format($invoice['total'], 2) . " → €" . number_format($newTotal, 2) . "\n";
                
                // Update the invoice
                $updateStmt = $pdo->prepare("
                    UPDATE invoices 
                    SET vat_amount = ?, total = ?
                    WHERE id = ?
                ");
                $updateStmt->execute([$newVatAmount, $newTotal, $invoice['id']]);
                
                echo "✓ Invoice updated successfully\n";
                
                // Clear any cache for this invoice
                $cacheKey = "invoice_details_{$invoice['id']}";
                // Note: Cache clearing would need to be done through your cache system
                
            } else {
                echo "⚠️  Expected total doesn't match standard TTC prices, skipping\n";
            }
        } else {
            echo "Not eligible (non-Luxembourg EU VAT)\n";
        }
        
        echo "\n";
    }
    
    echo "\nDone!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}