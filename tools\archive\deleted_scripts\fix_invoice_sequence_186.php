<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== FIXING INVOICE SEQUENCE TO GET 0186 ===\n\n";
    
    // Get invoice document type
    $stmt = $pdo->prepare("SELECT id, code, counter_type FROM document_types WHERE code = 'invoice'");
    $stmt->execute();
    $docType = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$docType) {
        echo "ERROR: Invoice document type not found!\n";
        exit(1);
    }
    
    echo "Found invoice document type: ID={$docType['id']}, Counter Type={$docType['counter_type']}\n\n";
    
    // Check current sequence
    echo "Current sequences:\n";
    $stmt = $pdo->prepare("SELECT * FROM document_sequences WHERE document_type_id = ? AND year = 2025 ORDER BY month DESC");
    $stmt->execute([$docType['id']]);
    $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo "- Year: {$seq['year']}, Month: " . ($seq['month'] ?? 'N/A') . ", Last Number: {$seq['last_number']}\n";
    }
    
    // Check if invoices 186-188 exist
    echo "\nChecking for existing invoices FAC-2025-0186 to FAC-2025-0190:\n";
    $existingInvoices = [];
    for ($i = 186; $i <= 190; $i++) {
        $invoiceNumber = 'FAC-2025-' . str_pad($i, 4, '0', STR_PAD_LEFT);
        $stmt = $pdo->prepare("SELECT id, invoice_number FROM invoices WHERE invoice_number = ?");
        $stmt->execute([$invoiceNumber]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            $existingInvoices[] = $invoice;
            echo "  ❌ {$invoiceNumber} EXISTS (ID: {$invoice['id']})\n";
        } else {
            echo "  ✓ {$invoiceNumber} does not exist\n";
        }
    }
    
    if (count($existingInvoices) > 0) {
        echo "\n⚠️  WARNING: Some invoice numbers already exist!\n";
        echo "Cannot set sequence to 185 because it would create duplicates.\n";
        
        // Find the lowest available number
        $lowestAvailable = 186;
        for ($i = 186; $i <= 300; $i++) {
            $invoiceNumber = 'FAC-2025-' . str_pad($i, 4, '0', STR_PAD_LEFT);
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM invoices WHERE invoice_number = ?");
            $stmt->execute([$invoiceNumber]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] == 0) {
                $lowestAvailable = $i;
                break;
            }
        }
        
        echo "\nThe next available invoice number would be: FAC-2025-" . str_pad($lowestAvailable, 4, '0', STR_PAD_LEFT) . "\n";
        echo "\nTo use FAC-2025-0186, you would need to delete or renumber the existing invoices.\n";
    } else {
        echo "\n✅ No conflicting invoices found. Updating sequence...\n";
        
        // Update the yearly sequence to 185
        $stmt = $pdo->prepare("
            UPDATE document_sequences 
            SET last_number = 185, updated_at = NOW() 
            WHERE document_type_id = ? 
            AND year = 2025 
            AND month IS NULL
        ");
        $result = $stmt->execute([$docType['id']]);
        
        if ($stmt->rowCount() > 0) {
            echo "✅ Updated yearly sequence for 2025 to last_number = 185\n";
        } else {
            // Create the sequence if it doesn't exist
            $stmt = $pdo->prepare("
                INSERT INTO document_sequences (document_type_id, year, month, last_number, created_at, updated_at)
                VALUES (?, 2025, NULL, 185, NOW(), NOW())
                ON DUPLICATE KEY UPDATE last_number = 185, updated_at = NOW()
            ");
            $stmt->execute([$docType['id']]);
            echo "✅ Created/Updated yearly sequence for 2025 to last_number = 185\n";
        }
        
        // Also check and remove any monthly sequences for 2025 if counter_type is yearly
        if ($docType['counter_type'] === 'yearly') {
            $stmt = $pdo->prepare("
                DELETE FROM document_sequences 
                WHERE document_type_id = ? 
                AND year = 2025 
                AND month IS NOT NULL
            ");
            $stmt->execute([$docType['id']]);
            if ($stmt->rowCount() > 0) {
                echo "✅ Removed " . $stmt->rowCount() . " monthly sequences (using yearly counter)\n";
            }
        }
        
        echo "\n🎉 SUCCESS! The next invoice number will be: FAC-2025-0186\n";
    }
    
    // Show final state
    echo "\nFinal sequences:\n";
    $stmt = $pdo->prepare("SELECT * FROM document_sequences WHERE document_type_id = ? AND year = 2025 ORDER BY month DESC");
    $stmt->execute([$docType['id']]);
    $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo "- Year: {$seq['year']}, Month: " . ($seq['month'] ?? 'N/A') . ", Last Number: {$seq['last_number']}\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}