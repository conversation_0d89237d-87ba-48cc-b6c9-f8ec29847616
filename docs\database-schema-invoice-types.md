# Invoice Types Database Schema Documentation

## Critical Issue Fixed

### Problem
The ConfigController was querying for a column `display_order` that doesn't exist in the `invoice_types` table. The actual column name is `order`.

### Solution Applied
1. **Immediate Fix**: Updated ConfigController.php to use `ORDER BY order` instead of `ORDER BY display_order`
2. **Migration Created**: Migration 131_fix_invoice_types_display_order.sql created to add `display_order` as an alias column

## Table Confusion: invoice_types vs config_invoice_types

There are references to two different tables throughout the codebase:
- `invoice_types` - Created in migration 005_create_config_tables.sql
- `config_invoice_types` - Referenced in various migrations and maintenance scripts

### Current Status
- Primary table appears to be `invoice_types` based on the initial migration
- Some migrations attempt to work with `config_invoice_types`
- Migration 131 creates sync between both tables if needed

## Schema for invoice_types Table

```sql
CREATE TABLE `invoice_types` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` VARCHAR(100) NOT NULL,
    `code` VARCHAR(20) NOT NULL,
    `prefix` VARCHAR(10) NOT NULL,
    `description` VARCHAR(255),
    `color` VARCHAR(7),
    `order` INT(11) DEFAULT 0,              -- Original column for sorting
    `display_order` INT(11) DEFAULT 0,      -- Added by migration 131 as alias
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_code` (`code`),
    KEY `idx_active` (`is_active`),
    KEY `idx_order` (`order`),
    KEY `idx_display_order` (`display_order`)
);
```

## Default Invoice Types

Based on migration 005:
1. Standard Invoice (INV) - code: standard
2. Proforma Invoice (PRO) - code: proforma  
3. Credit Note (CN) - code: credit
4. Quote (QUO) - code: quote

Additional types added in later migrations:
- Rental/Location invoices (LOY) - added in migrations 070/077
- Course invoices (COU) - added in migration 102

## Code References

### ConfigController.php
- **Lines 3803, 3918**: Query invoice types ordered by display_order
- **Fixed**: Now uses `ORDER BY order`

### Maintenance Scripts Using config_invoice_types
- `/tools/maintenance/invoice/update_invoice_type_prefixes.php`
- `/tools/maintenance/invoice/setup_invoice_sequence.php`
- `/tools/maintenance/invoice/check_invoice_types_prefixes.php`
- `/tools/maintenance/invoice/check_invoice_sequences.php`

## Recommendations

1. **Standardize Table Name**: Decide on either `invoice_types` or `config_invoice_types` and update all references
2. **Column Naming**: Use consistent column names across the application
3. **Migration Order**: Some migrations reference tables/columns that don't exist yet
4. **Data Integrity**: Ensure foreign key constraints are properly set up for invoice type references

## Migration History Related to Invoice Types

- 005_create_config_tables.sql - Creates `invoice_types` table with `order` column
- 009_add_performance_indexes.sql - Adds index on `config_invoice_types`
- 015_enhance_invoices_phase3.sql - Modifies `config_invoice_types`
- 028_add_order_to_invoice_types.sql - Adds `order` to `config_invoice_types`
- 070_add_rental_invoice_type.sql - Adds rental type
- 071_add_prefix_to_invoice_types.sql - Adds prefix handling
- 077_add_location_invoice_type.sql - Location invoice support
- 078_fix_location_invoice_setup.sql - Attempts to add `display_order`
- 079_add_missing_columns.sql - Another attempt at `display_order`
- 102_add_cours_invoice_type.sql - Adds course type
- 131_fix_invoice_types_display_order.sql - Fixes the display_order issue

## Testing the Fix

To verify the fix works:
1. Access any page that lists invoice types (e.g., email template creation)
2. Check that invoice types are displayed without SQL errors
3. Verify they are sorted correctly

## Future Considerations

1. Run migration 131 when database access is available
2. Consider consolidating duplicate table references
3. Update all maintenance scripts to use consistent table names
4. Add proper foreign key constraints where invoice_type_id is referenced