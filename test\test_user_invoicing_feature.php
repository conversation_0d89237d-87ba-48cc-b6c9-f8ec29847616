<?php
/**
 * Comprehensive Test for User Invoicing Feature
 * 
 * This test script verifies all aspects of the user invoicing implementation
 * Run after executing migration 038_add_user_invoicing
 */

// Configuration
$configFile = dirname(__DIR__) . '/config/database.php';
if (!file_exists($configFile)) {
    // Fallback to direct configuration if config file doesn't exist
    define('DB_HOST', '127.0.0.1');
    define('DB_NAME', 'fitapp');
    define('DB_USER', 'root');
    define('DB_PASS', 'test1234');
} else {
    require_once $configFile;
}

// Test results storage
$testResults = [];
$totalTests = 0;
$passedTests = 0;

// Connect to database
try {
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
} catch (PDOException $e) {
    die("Database connection failed: " . $e->getMessage());
}

// Test functions
function runTest($name, $testFunction) {
    global $testResults, $totalTests, $passedTests, $pdo;
    
    $totalTests++;
    try {
        $result = $testFunction($pdo);
        if ($result === true) {
            $passedTests++;
            $testResults[] = ['name' => $name, 'status' => 'PASS', 'message' => 'Test passed'];
        } else {
            $testResults[] = ['name' => $name, 'status' => 'FAIL', 'message' => $result];
        }
    } catch (Exception $e) {
        $testResults[] = ['name' => $name, 'status' => 'ERROR', 'message' => $e->getMessage()];
    }
}

// Test 1: Check if migration was executed
runTest('Migration Execution Check', function($pdo) {
    $stmt = $pdo->query("SHOW COLUMNS FROM invoices LIKE 'user_id'");
    if ($stmt->rowCount() === 0) {
        return "user_id column not found in invoices table";
    }
    return true;
});

// Test 2: Check users table modifications
runTest('Users Table Billing Fields', function($pdo) {
    $requiredColumns = [
        'billing_address', 'billing_city', 'billing_postal_code', 
        'billing_country', 'vat_number', 'billing_email', 
        'preferred_payment_method_id', 'can_be_invoiced'
    ];
    
    $stmt = $pdo->query("SHOW COLUMNS FROM users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($requiredColumns as $col) {
        if (!in_array($col, $columns)) {
            return "Missing column: $col in users table";
        }
    }
    return true;
});

// Test 3: Check user_invoice_preferences table
runTest('User Invoice Preferences Table', function($pdo) {
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_invoice_preferences'");
    if ($stmt->rowCount() === 0) {
        return "user_invoice_preferences table not found";
    }
    
    // Check structure
    $stmt = $pdo->query("DESCRIBE user_invoice_preferences");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    $required = ['user_id', 'invoice_language', 'send_invoice_copy', 'invoice_notes', 'discount_percentage'];
    
    foreach ($required as $col) {
        if (!in_array($col, $columns)) {
            return "Missing column: $col in user_invoice_preferences table";
        }
    }
    return true;
});

// Test 4: Check permissions
runTest('User Invoicing Permissions', function($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) FROM permissions WHERE code IN ('invoices.create_user', 'invoices.view_own')");
    $count = $stmt->fetchColumn();
    
    if ($count !== 2) {
        return "Expected 2 permissions, found $count";
    }
    return true;
});

// Test 5: Check invoice_recipients view
runTest('Invoice Recipients View', function($pdo) {
    $stmt = $pdo->query("SHOW FULL TABLES WHERE Table_type = 'VIEW' AND Tables_in_" . DB_NAME . " = 'invoice_recipients'");
    if ($stmt->rowCount() === 0) {
        return "invoice_recipients view not found";
    }
    
    // Test view functionality
    $stmt = $pdo->query("SELECT recipient_type, COUNT(*) as count FROM invoice_recipients GROUP BY recipient_type");
    $results = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
    
    if (!isset($results['user'])) {
        return "No users found in invoice_recipients view";
    }
    return true;
});

// Test 6: Test creating a user invoice
runTest('Create User Invoice', function($pdo) {
    // Get a test user
    $stmt = $pdo->query("SELECT id FROM users WHERE is_active = 1 LIMIT 1");
    $userId = $stmt->fetchColumn();
    
    if (!$userId) {
        return "No active users found for testing";
    }
    
    // Get document type
    $stmt = $pdo->query("SELECT id FROM document_types WHERE code = 'invoice' LIMIT 1");
    $docTypeId = $stmt->fetchColumn();
    
    if (!$docTypeId) {
        // Create default invoice document type
        $pdo->exec("
            INSERT INTO document_types (code, name, description, prefix, is_negative, requires_reference) 
            VALUES ('invoice', '{\"en\":\"Invoice\",\"fr\":\"Facture\"}', '{\"en\":\"Standard invoice\",\"fr\":\"Facture standard\"}', 'INV', 0, 0)
        ");
        $docTypeId = $pdo->lastInsertId();
    }
    
    // Create test invoice
    $stmt = $pdo->prepare("
        INSERT INTO invoices (
            invoice_number, document_type_id, user_id, client_id,
            status, issue_date, due_date, subtotal, vat_amount, total
        ) VALUES (
            :invoice_number, :document_type_id, :user_id, NULL,
            'draft', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 30 DAY),
            100.00, 17.00, 117.00
        )
    ");
    
    $testInvoiceNumber = 'TEST-USER-' . time();
    $result = $stmt->execute([
        ':invoice_number' => $testInvoiceNumber,
        ':document_type_id' => $docTypeId,
        ':user_id' => $userId
    ]);
    
    if (!$result) {
        return "Failed to create user invoice";
    }
    
    // Store invoice ID for cleanup
    $GLOBALS['test_invoice_id'] = $pdo->lastInsertId();
    
    return true;
});

// Test 7: Query user invoices
runTest('Query User Invoices', function($pdo) {
    $stmt = $pdo->query("
        SELECT i.*, u.username, CONCAT(u.first_name, ' ', u.last_name) as user_name
        FROM invoices i
        JOIN users u ON i.user_id = u.id
        WHERE i.user_id IS NOT NULL
    ");
    
    if ($stmt->rowCount() === 0) {
        return "No user invoices found in database";
    }
    
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    if (empty($invoice['user_name'])) {
        return "User name not properly fetched";
    }
    
    return true;
});

// Test 8: Test invoice list query with users
runTest('Invoice List with Users', function($pdo) {
    $query = "
        SELECT i.*, 
               CASE 
                   WHEN i.client_id IS NOT NULL THEN
                       CASE 
                           WHEN c.client_type = 'individual' THEN CONCAT(c.first_name, ' ', c.last_name)
                           ELSE c.company_name 
                       END
                   WHEN i.user_id IS NOT NULL THEN 
                       CONCAT(u.first_name, ' ', u.last_name, ' (', u.username, ')')
                   ELSE 'N/A'
               END as recipient_name,
               CASE 
                   WHEN i.client_id IS NOT NULL THEN 'client'
                   WHEN i.user_id IS NOT NULL THEN 'user'
                   ELSE 'unknown'
               END as recipient_type
        FROM invoices i
        LEFT JOIN clients c ON i.client_id = c.id
        LEFT JOIN users u ON i.user_id = u.id
        WHERE 1=1
        ORDER BY i.created_at DESC
        LIMIT 10
    ";
    
    $stmt = $pdo->query($query);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Check if we have at least one user invoice
    $hasUserInvoice = false;
    foreach ($results as $row) {
        if ($row['recipient_type'] === 'user') {
            $hasUserInvoice = true;
            break;
        }
    }
    
    if (!$hasUserInvoice) {
        return "No user invoices found in invoice list query";
    }
    
    return true;
});

// Test 9: Admin user billing data
runTest('Admin User Billing Data', function($pdo) {
    // Check if role column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM users WHERE Field = 'role'");
    $hasRoleColumn = $stmt->rowCount() > 0;
    
    if ($hasRoleColumn) {
        $stmt = $pdo->query("
            SELECT billing_address, billing_city, billing_postal_code, billing_country 
            FROM users 
            WHERE role = 'admin' 
            LIMIT 1
        ");
    } else {
        // Check for admin via user_groups
        $stmt = $pdo->query("
            SELECT u.billing_address, u.billing_city, u.billing_postal_code, u.billing_country 
            FROM users u
            JOIN user_group_members ugm ON u.id = ugm.user_id
            WHERE ugm.group_id = 1
            LIMIT 1
        ");
    }
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    if (!$admin) {
        return "No admin user found";
    }
    
    if (empty($admin['billing_address'])) {
        return "Admin user has no billing address set";
    }
    
    return true;
});

// Test 10: User invoice preferences for admins
runTest('Admin Invoice Preferences', function($pdo) {
    // Check if role column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM users WHERE Field = 'role'");
    $hasRoleColumn = $stmt->rowCount() > 0;
    
    if ($hasRoleColumn) {
        $stmt = $pdo->query("
            SELECT COUNT(*) 
            FROM user_invoice_preferences uip
            JOIN users u ON uip.user_id = u.id
            WHERE u.role = 'admin'
        ");
    } else {
        // Check for admin via user_groups
        $stmt = $pdo->query("
            SELECT COUNT(*) 
            FROM user_invoice_preferences uip
            JOIN users u ON uip.user_id = u.id
            JOIN user_group_members ugm ON u.id = ugm.user_id
            WHERE ugm.group_id = 1
        ");
    }
    
    $count = $stmt->fetchColumn();
    if ($count === 0) {
        return "No invoice preferences found for admin users";
    }
    
    return true;
});

// Cleanup test data
if (isset($GLOBALS['test_invoice_id'])) {
    $pdo->exec("DELETE FROM invoices WHERE id = " . $GLOBALS['test_invoice_id']);
}

// Display results
?>
<!DOCTYPE html>
<html>
<head>
    <title>User Invoicing Feature Test Results</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .summary {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .summary.success {
            background: #d4edda;
            color: #155724;
        }
        .summary.warning {
            background: #fff3cd;
            color: #856404;
        }
        .summary.error {
            background: #f8d7da;
            color: #721c24;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .status {
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
        .status.pass {
            background: #28a745;
            color: white;
        }
        .status.fail {
            background: #dc3545;
            color: white;
        }
        .status.error {
            background: #ffc107;
            color: #212529;
        }
        .message {
            font-size: 14px;
            color: #666;
        }
        .next-steps {
            margin-top: 30px;
            padding: 20px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .next-steps h3 {
            margin-top: 0;
        }
        .next-steps ul {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>User Invoicing Feature Test Results</h1>
        
        <?php
        $percentage = $totalTests > 0 ? round(($passedTests / $totalTests) * 100) : 0;
        $summaryClass = $percentage === 100 ? 'success' : ($percentage >= 70 ? 'warning' : 'error');
        ?>
        
        <div class="summary <?php echo $summaryClass; ?>">
            <h2>Test Summary</h2>
            <p>
                <strong>Total Tests:</strong> <?php echo $totalTests; ?><br>
                <strong>Passed:</strong> <?php echo $passedTests; ?><br>
                <strong>Failed:</strong> <?php echo $totalTests - $passedTests; ?><br>
                <strong>Success Rate:</strong> <?php echo $percentage; ?>%
            </p>
        </div>
        
        <table>
            <thead>
                <tr>
                    <th width="40%">Test Name</th>
                    <th width="10%">Status</th>
                    <th width="50%">Details</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($testResults as $test): ?>
                <tr>
                    <td><?php echo htmlspecialchars($test['name']); ?></td>
                    <td>
                        <span class="status <?php echo strtolower($test['status']); ?>">
                            <?php echo $test['status']; ?>
                        </span>
                    </td>
                    <td class="message"><?php echo htmlspecialchars($test['message']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        
        <div class="next-steps">
            <h3>Next Steps</h3>
            
            <?php if ($percentage === 100): ?>
                <p><strong>✅ All tests passed!</strong> The user invoicing feature is working correctly.</p>
                <ul>
                    <li>Navigate to <a href="/fit/public/invoices/create">/invoices/create</a> to create a user invoice</li>
                    <li>Select "User (Internal)" from the Bill To dropdown</li>
                    <li>Choose a user and create the invoice</li>
                    <li>View the invoice list to see user invoices</li>
                </ul>
            <?php else: ?>
                <p><strong>⚠️ Some tests failed.</strong> Please review the failed tests above and:</p>
                <ul>
                    <li>Ensure the migration was run successfully</li>
                    <li>Check the database for any missing tables or columns</li>
                    <li>Review the error messages for specific issues</li>
                </ul>
                
                <h4>Common Issues:</h4>
                <ul>
                    <li><strong>Migration not executed:</strong> Run the migration using the migration tool</li>
                    <li><strong>Missing permissions:</strong> The migration may have failed to insert permissions</li>
                    <li><strong>No admin users:</strong> Ensure you have at least one admin user in the system</li>
                </ul>
            <?php endif; ?>
        </div>
        
        <div style="margin-top: 30px; text-align: center;">
            <a href="/fit/public/database/migrate.php" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;">
                Go to Migration Tool
            </a>
            <a href="/fit/public/invoices/create" style="display: inline-block; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; margin-left: 10px;">
                Create Invoice
            </a>
        </div>
    </div>
</body>
</html>