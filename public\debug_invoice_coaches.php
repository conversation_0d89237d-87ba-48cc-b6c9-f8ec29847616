<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "Please log in first.";
    exit;
}

// Show session info
echo "<h3>Session Info</h3>";
echo "User ID: " . $_SESSION['user_id'] . "<br>";
echo "Username: " . ($_SESSION['username'] ?? 'N/A') . "<br><br>";

// Simulate what the controller does
$db = Flight::db();

// Get coach group ID
$coachGroupId = 24;
try {
    $configStmt = $db->prepare("SELECT value FROM config_settings WHERE category = 'users' AND `key` = 'coach_group_id'");
    $configStmt->execute();
    $configValue = $configStmt->fetchColumn();
    if ($configValue !== false) {
        $coachGroupId = intval($configValue);
    }
} catch (\Exception $e) {
    echo "Using default coach group ID: $coachGroupId<br>";
}

echo "<h3>Coach Group</h3>";
echo "Coach Group ID: $coachGroupId<br><br>";

// Get all users
$stmt = $db->prepare("
    SELECT u.id, u.username, u.email, 
           CONCAT(u.first_name, ' ', u.last_name) as name,
           u.billing_email, u.billing_address, u.billing_city,
           u.billing_postal_code, u.billing_country, u.vat_number,
           u.is_intracommunity, u.vat_intercommunautaire,
           u.course_name, u.hourly_rate, u.hourly_vat_rate,
           u.is_active, u.can_be_invoiced,
           uip.invoice_language, uip.discount_percentage,
           -- Flag to identify coaches (members of coach group)
           CASE WHEN ugm.user_id IS NOT NULL THEN 1 ELSE 0 END as is_coach
    FROM users u
    LEFT JOIN user_invoice_preferences uip ON u.id = uip.user_id
    LEFT JOIN user_group_members ugm ON u.id = ugm.user_id AND ugm.group_id = :coach_group_id
    WHERE u.is_active = 1 AND u.can_be_invoiced = 1
    ORDER BY u.first_name, u.last_name
");
$stmt->execute(['coach_group_id' => $coachGroupId]);
$users = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo "<h3>All Active Billable Users</h3>";
echo "Total count: " . count($users) . "<br><br>";

// Filter coaches
$coaches = array_filter($users, function($user) {
    return $user['is_coach'] == 1;
});
$coaches = array_values($coaches);

echo "<h3>Coaches Only</h3>";
echo "Total coaches: " . count($coaches) . "<br><br>";

if (count($coaches) > 0) {
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>ID</th><th>Username</th><th>Name</th><th>Email</th><th>Course</th><th>Active</th><th>Can Invoice</th></tr>";
    foreach ($coaches as $coach) {
        echo "<tr>";
        echo "<td>{$coach['id']}</td>";
        echo "<td>{$coach['username']}</td>";
        echo "<td>{$coach['name']}</td>";
        echo "<td>{$coach['email']}</td>";
        echo "<td>" . ($coach['course_name'] ?? 'N/A') . "</td>";
        echo "<td>" . ($coach['is_active'] ? 'Yes' : 'No') . "</td>";
        echo "<td>" . ($coach['can_be_invoiced'] ? 'Yes' : 'No') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "No coaches found!<br><br>";
    
    // Check if there are users in the coach group at all
    $stmt = $db->prepare("
        SELECT COUNT(*) FROM user_group_members 
        WHERE group_id = :coach_group_id
    ");
    $stmt->execute(['coach_group_id' => $coachGroupId]);
    $totalInGroup = $stmt->fetchColumn();
    echo "Total users in coach group: $totalInGroup<br><br>";
    
    // Check if there are any users with is_active = 1 and can_be_invoiced = 1
    $stmt = $db->query("SELECT COUNT(*) FROM users WHERE is_active = 1 AND can_be_invoiced = 1");
    $activeBillable = $stmt->fetchColumn();
    echo "Total active billable users (any group): $activeBillable<br>";
}

// Test JavaScript variable generation
echo "<h3>JavaScript Test</h3>";
echo "<pre>";
echo "let coachesData = " . json_encode($coaches, JSON_PRETTY_PRINT) . ";";
echo "</pre>";
?>