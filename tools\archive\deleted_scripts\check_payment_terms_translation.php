<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

// Simulate the conditions when viewing payment terms page
$_SESSION['user_language'] = 'fr';
\App\Helpers\Language::setLanguage('fr');

header('Content-Type: text/html; charset=UTF-8');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test Payment Terms Translations</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Payment Terms Translation Test</h1>
        
        <h2>Translation Results:</h2>
        <table class="table">
            <thead>
                <tr>
                    <th>Key</th>
                    <th>Result</th>
                    <th>Expected</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $tests = [
                    'common.name' => 'Nom',
                    'config.code' => 'Code',
                    'config.payment_days' => 'Jours de paiement',
                    'common.description' => 'Description',
                    'config.payment_term_name_hint' => 'ex: Net 30, Paiement à réception',
                    'config.payment_term_code_hint' => 'Identifiant unique (lettres, chiffres, tirets bas)',
                    'config.payment_days_hint' => 'Nombre de jours à partir de la date de facturation (0 pour immédiat)',
                    'config.payment_term_description_hint' => 'Détails supplémentaires sur cette condition de paiement'
                ];
                
                foreach ($tests as $key => $expected) {
                    $result = __($key);
                    $status = ($result === $expected) ? 'OK' : 'FAIL';
                    $class = ($status === 'OK') ? 'table-success' : 'table-danger';
                    echo "<tr class='$class'>";
                    echo "<td>$key</td>";
                    echo "<td>$result</td>";
                    echo "<td>$expected</td>";
                    echo "<td>$status</td>";
                    echo "</tr>";
                }
                ?>
            </tbody>
        </table>
        
        <h2>Current Language Settings:</h2>
        <ul>
            <li>Current Language: <?php echo \App\Helpers\Language::getCurrentLanguage(); ?></li>
            <li>Session Language: <?php echo $_SESSION['user_language'] ?? 'Not set'; ?></li>
            <li>User Language: <?php echo $_SESSION['user']['language'] ?? 'Not set'; ?></li>
        </ul>
        
        <h2>Test Translation Function Directly:</h2>
        <?php
        // Test direct translation
        echo "<p>Direct test of __('config.code'): " . __('config.code') . "</p>";
        
        // Test with Language class directly
        \App\Helpers\Language::load('config', 'fr');
        echo "<p>After loading config group: " . \App\Helpers\Language::get('config.code') . "</p>";
        
        // Check if translation file exists and is readable
        $configFile = __DIR__ . '/../app/lang/fr/config.php';
        echo "<p>Config file exists: " . (file_exists($configFile) ? 'YES' : 'NO') . "</p>";
        echo "<p>Config file readable: " . (is_readable($configFile) ? 'YES' : 'NO') . "</p>";
        
        // Try to load translations manually
        if (file_exists($configFile)) {
            $translations = include $configFile;
            echo "<p>Manual load - 'code' key: " . ($translations['code'] ?? 'NOT FOUND') . "</p>";
        }
        ?>
    </div>
</body>
</html>