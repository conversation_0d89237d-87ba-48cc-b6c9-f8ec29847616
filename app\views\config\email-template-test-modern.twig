{% extends "base-modern.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config/email-templates" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <a href="{{ base_url }}/config/email-templates/{{ template.id }}/edit" class="btn btn-primary">
                <i class="bi bi-pencil me-2"></i>{{ __('common.edit') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Template Info -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="bi bi-envelope me-2"></i>{{ __('config.template_info') }}</h6>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-5">{{ __('common.name') }}:</dt>
                        <dd class="col-sm-7">{{ template.name }}</dd>
                        
                        <dt class="col-sm-5">{{ __('config.code') }}:</dt>
                        <dd class="col-sm-7"><code>{{ template.code }}</code></dd>
                        
                        <dt class="col-sm-5">{{ __('config.email_type') }}:</dt>
                        <dd class="col-sm-7">{{ template.getEmailTypeLabel() }}</dd>
                        
                        <dt class="col-sm-5">{{ __('config.invoice_type') }}:</dt>
                        <dd class="col-sm-7">{{ template.getInvoiceTypeLabel() }}</dd>
                        
                        <dt class="col-sm-5">{{ __('config.priority') }}:</dt>
                        <dd class="col-sm-7">{{ template.getPriorityLabel() }}</dd>
                        
                        <dt class="col-sm-5">{{ __('common.status') }}:</dt>
                        <dd class="col-sm-7">
                            {% if template.is_active %}
                                <span class="badge bg-success">{{ __('common.active') }}</span>
                            {% else %}
                                <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                            {% endif %}
                        </dd>
                    </dl>
                </div>
            </div>

            <!-- Test Data -->
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="bi bi-database me-2"></i>{{ __('config.test_data') }}</h6>
                </div>
                <div class="card-body">
                    <form id="testForm">
                        <div class="mb-3">
                            <label for="recipientEmail" class="form-label">{{ __('config.recipient_email') }} *</label>
                            <input type="email" class="form-control" id="recipientEmail" required 
                                   value="{{ app.user.email|default('') }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="testInvoice" class="form-label">{{ __('config.test_with_invoice') }}</label>
                            <select class="form-select" id="testInvoice" onchange="loadInvoiceData()">
                                <option value="">{{ __('config.use_sample_data') }}</option>
                                {% for invoice in recentInvoices %}
                                    <option value="{{ invoice.id }}">
                                        #{{ invoice.invoice_number }} - {{ invoice.client_name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="accordion" id="testDataAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                            data-bs-target="#customData">
                                        {{ __('config.custom_test_data') }}
                                    </button>
                                </h2>
                                <div id="customData" class="accordion-collapse collapse" data-bs-parent="#testDataAccordion">
                                    <div class="accordion-body">
                                        <div class="row g-2">
                                            <div class="col-12">
                                                <small class="text-muted">{{ __('config.custom_data_hint') }}</small>
                                            </div>
                                            {% for category, vars in template.getAvailableVariables()|default({}) %}
                                                {% for var, desc in vars %}
                                                <div class="col-md-6">
                                                    <label class="form-label small">{{ desc }}</label>
                                                    <input type="text" class="form-control form-control-sm test-var" 
                                                           data-var="{{ var }}" placeholder="{{ '{' ~ var ~ '}' }}">
                                                </div>
                                                {% endfor %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2 mt-3">
                            <button type="button" class="btn btn-primary" onclick="updatePreview()">
                                <i class="bi bi-arrow-clockwise me-2"></i>{{ __('config.update_preview') }}
                            </button>
                            <button type="button" class="btn btn-success" onclick="sendTestEmail()">
                                <i class="bi bi-send me-2"></i>{{ __('config.send_test_email') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Preview -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-white">
                    <ul class="nav nav-tabs card-header-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#preview-html" role="tab">
                                <i class="bi bi-filetype-html me-2"></i>HTML Preview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#preview-text" role="tab">
                                <i class="bi bi-file-text me-2"></i>Text Preview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#preview-raw" role="tab">
                                <i class="bi bi-code-slash me-2"></i>Raw HTML
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        {{ __('config.test_preview_info') }}
                    </div>
                    
                    <div class="border rounded p-3 mb-3">
                        <strong>{{ __('config.subject') }}:</strong>
                        <div id="previewSubject" class="text-muted">{{ template.subject }}</div>
                    </div>
                    
                    <div class="tab-content">
                        <div class="tab-pane fade show active" id="preview-html" role="tabpanel">
                            <div class="border rounded p-3" style="min-height: 400px;">
                                <div id="previewBodyHtml">
                                    {{ template.body_html|default(template.body)|raw }}
                                </div>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="preview-text" role="tabpanel">
                            <div class="border rounded p-3" style="min-height: 400px;">
                                <pre id="previewBodyText" style="white-space: pre-wrap;">{{ template.body_text|default(template.body|striptags) }}</pre>
                            </div>
                        </div>
                        
                        <div class="tab-pane fade" id="preview-raw" role="tabpanel">
                            <div class="border rounded p-3" style="min-height: 400px;">
                                <pre><code id="previewRawHtml" class="language-html">{{ template.body_html|default(template.body)|e }}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mb-0">{{ __('common.please_wait') }}...</p>
            </div>
        </div>
    </div>
</div>

<script>
let currentTestData = {};

// Load invoice data
function loadInvoiceData() {
    const invoiceId = document.getElementById('testInvoice').value;
    if (!invoiceId) {
        // Clear custom fields
        document.querySelectorAll('.test-var').forEach(input => {
            input.value = '';
        });
        return;
    }
    
    // In a real implementation, you would fetch invoice data via AJAX
    // For now, we'll just update the preview
    updatePreview();
}

// Collect test data from form
function collectTestData() {
    const data = {};
    
    // Get invoice ID
    const invoiceId = document.getElementById('testInvoice').value;
    if (invoiceId) {
        data.invoice_id = invoiceId;
    }
    
    // Get custom variables
    document.querySelectorAll('.test-var').forEach(input => {
        if (input.value) {
            data[input.dataset.var] = input.value;
        }
    });
    
    return data;
}

// Update preview
function updatePreview() {
    const testData = collectTestData();
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    fetch('{{ base_url }}/config/email-templates/preview', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            template_id: {{ template.id }},
            'test_data[invoice_id]': testData.invoice_id || '',
            ...Object.entries(testData).reduce((acc, [key, value]) => {
                if (key !== 'invoice_id') {
                    acc[`test_data[${key}]`] = value;
                }
                return acc;
            }, {}),
            csrf_token: '{{ csrf_token }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            document.getElementById('previewSubject').textContent = data.preview.subject;
            document.getElementById('previewBodyHtml').innerHTML = data.preview.body_html || data.preview.body;
            document.getElementById('previewBodyText').textContent = data.preview.body_text || stripHtml(data.preview.body);
            document.getElementById('previewRawHtml').textContent = data.preview.body_html || data.preview.body;
            
            // Highlight code if Prism is available
            if (typeof Prism !== 'undefined') {
                Prism.highlightElement(document.getElementById('previewRawHtml'));
            }
        } else {
            toastr.error(data.message);
        }
    })
    .catch(error => {
        loadingModal.hide();
        toastr.error('Error loading preview');
    });
}

// Send test email
function sendTestEmail() {
    const recipientEmail = document.getElementById('recipientEmail').value;
    
    if (!recipientEmail) {
        toastr.error('{{ __("config.recipient_email_required") }}');
        return;
    }
    
    if (!confirm('{{ __("config.send_test_email_confirm") }} ' + recipientEmail + '?')) {
        return;
    }
    
    const testData = collectTestData();
    const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
    loadingModal.show();
    
    fetch('{{ base_url }}/config/email-templates/send-test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: new URLSearchParams({
            template_id: {{ template.id }},
            recipient_email: recipientEmail,
            'test_data[invoice_id]': testData.invoice_id || '',
            ...Object.entries(testData).reduce((acc, [key, value]) => {
                if (key !== 'invoice_id') {
                    acc[`test_data[${key}]`] = value;
                }
                return acc;
            }, {}),
            csrf_token: '{{ csrf_token }}'
        })
    })
    .then(response => response.json())
    .then(data => {
        loadingModal.hide();
        if (data.success) {
            toastr.success(data.message);
        } else {
            toastr.error(data.message);
        }
    })
    .catch(error => {
        loadingModal.hide();
        toastr.error('Error sending test email');
    });
}

// Strip HTML tags
function stripHtml(html) {
    const tmp = document.createElement('DIV');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Load initial preview
    updatePreview();
});
</script>
{% endblock %}