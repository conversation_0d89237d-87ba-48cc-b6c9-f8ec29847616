<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\User;
use App\Models\Config;
use DateTime;
use Exception;

class MonthlyInvoiceGenerator
{
    private $defaultVatRate = 17.0;
    
    /**
     * Generate monthly rental invoices for users with financial obligations
     * 
     * @param string $month Month in format 'Y-m' (e.g., '2025-01')
     * @param array $userIds Optional array of specific user IDs to generate for
     * @return array Array with 'success' and 'invoices' keys
     */
    public function generateMonthlyInvoices($month, $userIds = [])
    {
        $results = [
            'success' => [],
            'errors' => []
        ];
        
        try {
            // Parse month
            $date = new DateTime($month . '-01');
            $monthName = $this->getMonthName($date->format('n'));
            $year = $date->format('Y');
            $period = $monthName . ' ' . $year;
            
            // Get users with financial obligations
            $users = $this->getUsersToInvoice($userIds);
            
            foreach ($users as $user) {
                try {
                    $invoice = $this->createInvoiceForUser($user, $period, $date);
                    if ($invoice) {
                        $results['success'][] = [
                            'user' => $user['display_name'],
                            'invoice' => $invoice
                        ];
                    }
                } catch (Exception $e) {
                    $results['errors'][] = [
                        'user' => $user['display_name'],
                        'error' => $e->getMessage()
                    ];
                }
            }
            
        } catch (Exception $e) {
            throw new Exception("Error generating monthly invoices: " . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Preview monthly invoices without creating them
     * 
     * @param string $month Month in format 'Y-m'
     * @param array $userIds Optional array of specific user IDs
     * @return array Array of invoice previews
     */
    public function previewMonthlyInvoices($month, $userIds = [])
    {
        $previews = [];
        
        try {
            $date = new DateTime($month . '-01');
            $monthName = $this->getMonthName($date->format('n'));
            $year = $date->format('Y');
            $period = $monthName . ' ' . $year;
            
            $users = $this->getUsersToInvoice($userIds);
            
            foreach ($users as $user) {
                $obligations = json_decode($user['financial_obligations'], true);
                
                $items = [];
                $subtotal = 0;
                
                // Add rent
                if (!empty($obligations['rent_amount'])) {
                    $items[] = [
                        'description' => 'Loyer - ' . $period,
                        'quantity' => 1,
                        'unit_price' => $obligations['rent_amount'],
                        'vat_rate' => 0,
                        'total' => $obligations['rent_amount']
                    ];
                    $subtotal += $obligations['rent_amount'];
                }
                
                // Add charges
                if (!empty($obligations['charges_amount'])) {
                    $items[] = [
                        'description' => 'Charges - ' . $period,
                        'quantity' => 1,
                        'unit_price' => $obligations['charges_amount'],
                        'vat_rate' => 0,
                        'total' => $obligations['charges_amount']
                    ];
                    $subtotal += $obligations['charges_amount'];
                }
                
                // Add secretary services with VAT
                if (!empty($obligations['secretary_tvac_17'])) {
                    $htva = $obligations['secretary_tvac_17'] / 1.17;
                    $items[] = [
                        'description' => 'Secrétariat - ' . $period,
                        'quantity' => 1,
                        'unit_price' => $htva,
                        'vat_rate' => 17,
                        'total' => $obligations['secretary_tvac_17']
                    ];
                    $subtotal += $htva;
                }
                
                $total = !empty($obligations['total_tvac']) ? $obligations['total_tvac'] : $subtotal;
                
                $previews[] = [
                    'user_id' => $user['id'],
                    'user_name' => $user['display_name'],
                    'period' => $period,
                    'items' => $items,
                    'subtotal' => $subtotal,
                    'total' => $total,
                    'obligations' => $obligations
                ];
            }
            
        } catch (Exception $e) {
            throw new Exception("Error previewing invoices: " . $e->getMessage());
        }
        
        return $previews;
    }
    
    /**
     * Get users with active financial obligations
     */
    private function getUsersToInvoice($userIds = [])
    {
        $userModel = new User();
        $users = $userModel->getUsersWithFinancialObligations();
        
        if (!empty($userIds)) {
            $users = array_filter($users, function($user) use ($userIds) {
                return in_array($user['id'], $userIds);
            });
        }
        
        return $users;
    }
    
    /**
     * Create invoice for a specific user
     */
    private function createInvoiceForUser($user, $period, $date)
    {
        $obligations = json_decode($user['financial_obligations'], true);
        
        if (empty($obligations)) {
            return null;
        }
        
        // Get invoice configuration
        $configModel = new Config();
        $invoiceType = $this->getRentalInvoiceType();
        
        // Create invoice
        $invoiceModel = new Invoice();
        
        // Prepare invoice data with items
        $items = [];
        $subtotal = 0;
        $taxAmount = 0;
        
        // Add rent
        if (!empty($obligations['rent_amount'])) {
            $items[] = [
                'description' => 'Loyer - ' . $period,
                'quantity' => 1,
                'unit_price' => $obligations['rent_amount'],
                'vat_rate' => 0,
                'total' => $obligations['rent_amount']
            ];
            $subtotal += $obligations['rent_amount'];
        }
        
        // Add charges
        if (!empty($obligations['charges_amount'])) {
            $items[] = [
                'description' => 'Charges - ' . $period,
                'quantity' => 1,
                'unit_price' => $obligations['charges_amount'],
                'vat_rate' => 0,
                'total' => $obligations['charges_amount']
            ];
            $subtotal += $obligations['charges_amount'];
        }
        
        // Add secretary services with VAT
        if (!empty($obligations['secretary_tvac_17'])) {
            $htva = $obligations['secretary_tvac_17'] / 1.17;
            $vat = $obligations['secretary_tvac_17'] - $htva;
            
            $items[] = [
                'description' => 'Secrétariat - ' . $period,
                'quantity' => 1,
                'unit_price' => $htva,
                'vat_rate' => 17,
                'total' => $obligations['secretary_tvac_17']
            ];
            
            $subtotal += $htva;
            $taxAmount += $vat;
        }
        
        $total = !empty($obligations['total_tvac']) ? $obligations['total_tvac'] : ($subtotal + $taxAmount);
        
        $invoiceData = [
            'client_id' => null,
            'user_id' => $user['id'],
            'type_id' => $invoiceType,
            'invoice_type' => 'rental',
            'status' => 'draft',
            'issue_date' => $date->format('Y-m-d'),
            'due_date' => $date->format('Y-m-t'), // End of month
            'subtotal' => $subtotal,
            'vat_amount' => $taxAmount,
            'total' => $total,
            'notes' => 'Facture générée automatiquement pour ' . $period . ' - Loyer + Charges',
            'lines' => $items
        ];
        
        try {
            $invoice = $invoiceModel->createInvoice($invoiceData);
            if (!$invoice || !is_array($invoice)) {
                throw new Exception("Failed to create invoice");
            }
            return $invoice;
        } catch (Exception $e) {
            throw new Exception("Failed to create invoice for user " . $user['display_name'] . ": " . $e->getMessage());
        }
    }
    
    /**
     * Get or create rental invoice type
     */
    private function getRentalInvoiceType()
    {
        $db = \Flight::db();
        
        // Check if LOC type exists (by code)
        $stmt = $db->prepare("SELECT * FROM invoice_types WHERE code = 'LOC' LIMIT 1");
        $stmt->execute();
        $type = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if (!$type) {
            // Create LOC invoice type
            $stmt = $db->prepare("
                INSERT INTO invoice_types (name, code, is_active, created_at) 
                VALUES (:name, :code, 1, NOW())
            ");
            $stmt->execute([
                'name' => '"LOC"',
                'code' => 'LOC'
            ]);
            
            // Get the newly created ID
            return $db->lastInsertId();
        }
        
        return $type['id'];
    }
    
    /**
     * Get month name in French
     */
    private function getMonthName($monthNumber)
    {
        $months = [
            1 => 'JANVIER',
            2 => 'FÉVRIER',
            3 => 'MARS',
            4 => 'AVRIL',
            5 => 'MAI',
            6 => 'JUIN',
            7 => 'JUILLET',
            8 => 'AOÛT',
            9 => 'SEPTEMBRE',
            10 => 'OCTOBRE',
            11 => 'NOVEMBRE',
            12 => 'DÉCEMBRE'
        ];
        
        return $months[$monthNumber] ?? '';
    }
    
    /**
     * Check if an invoice already exists for a client/month/year
     * 
     * @param int $clientId
     * @param int $month
     * @param int $year
     * @param string $type Type of invoice (rental, etc.)
     * @return array|null Invoice data if exists, null otherwise
     */
    public function checkExistingInvoice($clientId, $month, $year, $type = 'rental')
    {
        $db = \Flight::db();
        
        // Get invoice type IDs for LOC invoices only
        $invoiceTypeQuery = "SELECT id FROM invoice_types WHERE code = 'LOC'";
        $stmt = $db->query($invoiceTypeQuery);
        $invoiceTypeIds = $stmt->fetchAll(\PDO::FETCH_COLUMN);
        
        if (empty($invoiceTypeIds)) {
            return null;
        }
        
        // Check for existing invoice
        $query = "
            SELECT * FROM invoices 
            WHERE client_id = :client_id 
            AND invoice_type_id IN (" . implode(',', $invoiceTypeIds) . ")
            AND MONTH(issue_date) = :month 
            AND YEAR(issue_date) = :year 
            LIMIT 1
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            ':client_id' => $clientId,
            ':month' => $month,
            ':year' => $year
        ]);
        
        return $stmt->fetch(\PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * Generate a rental invoice for a specific user
     * 
     * @param array $userData User data including financial obligations
     * @param int $month
     * @param int $year
     * @return array|null Invoice data if created, null otherwise
     */
    public function generateRentalInvoice($userData, $month, $year)
    {
        if (empty($userData['client_id']) || empty($userData['rental_amount'])) {
            throw new Exception("Missing client ID or rental amount");
        }
        
        $date = new DateTime($year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01');
        $monthName = $this->getMonthName($month);
        $period = $monthName . ' ' . $year;
        
        // Prepare invoice data
        $invoiceData = [
            'client_id' => $userData['client_id'],
            'invoice_type' => $userData['is_practitioner'] ? 'rental' : 'LOCATION',
            'issue_date' => $date->format('Y-m-d'),
            'due_date' => $date->modify('+30 days')->format('Y-m-d'),
            'currency' => 'EUR',
            'status' => 'draft',
            'notes' => 'Facture de location pour ' . $period
        ];
        
        // Create invoice
        $invoice = new Invoice();
        $invoice->create($invoiceData);
        
        if (!$invoice->id) {
            throw new Exception("Failed to create invoice");
        }
        
        // Add invoice items
        $item = new InvoiceItem();
        $itemData = [
            'invoice_id' => $invoice->id,
            'description' => 'Location - ' . $period,
            'quantity' => 1,
            'unit_price' => $userData['rental_amount'],
            'vat_rate' => 0,
            'total' => $userData['rental_amount']
        ];
        $item->create($itemData);
        
        // Update invoice totals
        $invoice->updateTotals();
        
        return [
            'id' => $invoice->id,
            'invoice_number' => $invoice->invoice_number
        ];
    }
    
    /**
     * Check if an invoice already exists for a user/month/year
     * 
     * @param int $userId
     * @param int $month
     * @param int $year
     * @param string $type Type of invoice (rental, etc.)
     * @return array|null Invoice data if exists, null otherwise
     */
    public function checkExistingInvoiceForUser($userId, $month, $year, $type = 'rental')
    {
        $db = \Flight::db();
        
        // Get invoice type IDs for LOC invoices only
        $invoiceTypeQuery = "SELECT id FROM invoice_types WHERE code = 'LOC'";
        $stmt = $db->query($invoiceTypeQuery);
        $invoiceTypeIds = $stmt->fetchAll(\PDO::FETCH_COLUMN);
        
        if (empty($invoiceTypeIds)) {
            return null;
        }
        
        // Check for existing invoice
        $query = "
            SELECT * FROM invoices 
            WHERE user_id = :user_id 
            AND invoice_type_id IN (" . implode(',', $invoiceTypeIds) . ")
            AND MONTH(issue_date) = :month 
            AND YEAR(issue_date) = :year 
            LIMIT 1
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([
            ':user_id' => $userId,
            ':month' => $month,
            ':year' => $year
        ]);
        
        return $stmt->fetch(\PDO::FETCH_ASSOC) ?: null;
    }
    
    /**
     * Generate a rental invoice for a specific user (not client)
     * 
     * @param array $userData User data including financial obligations
     * @param int $month
     * @param int $year
     * @return array|null Invoice data if created, null otherwise
     */
    public function generateRentalInvoiceForUser($userData, $month, $year)
    {
        if (empty($userData['id'])) {
            throw new Exception("Missing user ID");
        }
        
        $date = new DateTime($year . '-' . str_pad($month, 2, '0', STR_PAD_LEFT) . '-01');
        $monthName = $this->getMonthName($month);
        $period = $monthName . ' ' . $year;
        
        // Always use LOC type for all Loyer invoices
        $db = \Flight::db();
        $stmt = $db->prepare("SELECT id FROM invoice_types WHERE code = 'LOC'");
        $stmt->execute();
        $invoiceType = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if (!$invoiceType) {
            throw new Exception("Invoice type LOC not found");
        }
        
        // Also get the type_id from config_invoice_types for invoice number generation
        $stmt = $db->prepare("SELECT id FROM config_invoice_types WHERE prefix = 'LOC'");
        $stmt->execute();
        $configType = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        // Calculate totals before creating invoice
        $subtotal = 0;
        $vatAmount = 0;
        
        // Add up all amounts
        $subtotal += !empty($userData['rent_amount']) ? $userData['rent_amount'] : 0;
        $subtotal += !empty($userData['charges_amount']) ? $userData['charges_amount'] : 0;
        
        // Handle secretary amounts - use EITHER TVAC OR (HTVA + TVA), not both
        if (!empty($userData['secretary_tvac_17']) && $userData['secretary_tvac_17'] > 0) {
            // If we have TVAC, use it (already includes VAT)
            $htva = $userData['secretary_tvac_17'] / 1.17;
            $subtotal += $htva;
            $vatAmount += ($userData['secretary_tvac_17'] - $htva);
        } else {
            // Otherwise use HTVA and TVA separately
            if (!empty($userData['secretary_htva'])) {
                $subtotal += $userData['secretary_htva'];
            }
            if (!empty($userData['tva_17'])) {
                // tva_17 IS the VAT amount, not a base amount
                $vatAmount += $userData['tva_17'];
            }
        }
        
        $total = $subtotal + $vatAmount;
        
        // Prepare invoice data with totals
        $invoiceData = [
            'user_id' => $userData['id'],
            'client_id' => null, // No client for user invoices
            'invoice_type_id' => $invoiceType['id'],
            'type_id' => $configType ? $configType['id'] : null, // Add type_id for invoice number generation
            'issue_date' => $date->format('Y-m-d'),
            'due_date' => $date->modify('+30 days')->format('Y-m-d'),
            'currency' => 'EUR',
            'status' => 'draft',
            'notes' => 'Facture de location pour ' . $period,
            'created_by' => $_SESSION['user_id'] ?? null,
            'subtotal' => $subtotal,
            'vat_amount' => $vatAmount,
            'total' => $total
        ];
        
        // Create invoice
        $invoice = new Invoice();
        $createdInvoice = $invoice->createInvoice($invoiceData);
        
        if (!$createdInvoice || !isset($createdInvoice['id'])) {
            throw new Exception("Failed to create invoice");
        }
        
        $invoiceId = $createdInvoice['id'];
        $invoiceData = $createdInvoice;
        
        // Add invoice items based on financial obligations
        $lineNumber = 1;
        
        // Add rent amount if exists
        if (!empty($userData['rent_amount']) && $userData['rent_amount'] > 0) {
            $stmt = $db->prepare("
                INSERT INTO invoice_lines (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order)
                VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order)
            ");
            $stmt->execute([
                'invoice_id' => $invoiceId,
                'line_type' => 'rent',
                'description' => 'Loyer - ' . $period,
                'quantity' => 1,
                'unit_price' => $userData['rent_amount'],
                'vat_rate' => 0,
                'line_total' => $userData['rent_amount'],
                'sort_order' => $lineNumber++
            ]);
        }
        
        // Add charges amount if exists
        if (!empty($userData['charges_amount']) && $userData['charges_amount'] > 0) {
            $stmt = $db->prepare("
                INSERT INTO invoice_lines (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order)
                VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order)
            ");
            $stmt->execute([
                'invoice_id' => $invoiceId,
                'line_type' => 'charges',
                'description' => 'Charges - ' . $period,
                'quantity' => 1,
                'unit_price' => $userData['charges_amount'],
                'vat_rate' => 0,
                'line_total' => $userData['charges_amount'],
                'sort_order' => $lineNumber++
            ]);
        }
        
        // Handle secretary amounts - use EITHER TVAC OR (HTVA + TVA), not both
        if (!empty($userData['secretary_tvac_17']) && $userData['secretary_tvac_17'] > 0) {
            // If we have TVAC amount, use it (it includes VAT)
            $htva = $userData['secretary_tvac_17'] / 1.17;
            $stmt = $db->prepare("
                INSERT INTO invoice_lines (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order)
                VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order)
            ");
            $stmt->execute([
                'invoice_id' => $invoiceId,
                'line_type' => 'secretariat',
                'description' => 'Secrétariat - ' . $period,
                'quantity' => 1,
                'unit_price' => $htva,
                'vat_rate' => 17,
                'line_total' => $userData['secretary_tvac_17'],
                'sort_order' => $lineNumber++
            ]);
        } else {
            // Otherwise, use HTVA and TVA separately if they exist
            if (!empty($userData['secretary_htva']) && $userData['secretary_htva'] > 0) {
                // Add secretary HTVA line
                $stmt = $db->prepare("
                    INSERT INTO invoice_lines (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order)
                    VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order)
                ");
                $stmt->execute([
                    'invoice_id' => $invoiceId,
                    'line_type' => 'secretariat',
                    'description' => 'Secrétariat - ' . $period,
                    'quantity' => 1,
                    'unit_price' => $userData['secretary_htva'],
                    'vat_rate' => 0,
                    'line_total' => $userData['secretary_htva'],
                    'sort_order' => $lineNumber++
                ]);
            }
            
            if (!empty($userData['tva_17']) && $userData['tva_17'] > 0) {
                // Add VAT as a separate line (not as a service with VAT added)
                $stmt = $db->prepare("
                    INSERT INTO invoice_lines (invoice_id, line_type, description, quantity, unit_price, vat_rate, line_total, sort_order)
                    VALUES (:invoice_id, :line_type, :description, :quantity, :unit_price, :vat_rate, :line_total, :sort_order)
                ");
                $stmt->execute([
                    'invoice_id' => $invoiceId,
                    'line_type' => 'service',
                    'description' => 'TVA 17% - ' . $period,
                    'quantity' => 1,
                    'unit_price' => $userData['tva_17'],
                    'vat_rate' => 0,  // This IS the VAT, don't add VAT on VAT
                    'line_total' => $userData['tva_17'],
                    'sort_order' => $lineNumber++
                ]);
            }
        }
        
        // Invoice totals are already calculated and included in createInvoice
        // No need to update totals separately
        
        return [
            'id' => $invoiceId,
            'invoice_number' => $invoiceData['invoice_number']
        ];
    }
}