<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\User;
use App\Models\Config;
use DateTime;
use Exception;

class MonthlyInvoiceGenerator
{
    private $defaultVatRate = 17.0;
    
    /**
     * Generate monthly rental invoices for users with financial obligations
     * 
     * @param string $month Month in format 'Y-m' (e.g., '2025-01')
     * @param array $userIds Optional array of specific user IDs to generate for
     * @return array Array with 'success' and 'invoices' keys
     */
    public function generateMonthlyInvoices($month, $userIds = [])
    {
        $results = [
            'success' => [],
            'errors' => []
        ];
        
        try {
            // Parse month
            $date = new DateTime($month . '-01');
            $monthName = $this->getMonthName($date->format('n'));
            $year = $date->format('Y');
            $period = $monthName . ' ' . $year;
            
            // Get users with financial obligations
            $users = $this->getUsersToInvoice($userIds);
            
            foreach ($users as $user) {
                try {
                    $invoice = $this->createInvoiceForUser($user, $period, $date);
                    if ($invoice) {
                        $results['success'][] = [
                            'user' => $user['display_name'],
                            'invoice' => $invoice
                        ];
                    }
                } catch (Exception $e) {
                    $results['errors'][] = [
                        'user' => $user['display_name'],
                        'error' => $e->getMessage()
                    ];
                }
            }
            
        } catch (Exception $e) {
            throw new Exception("Error generating monthly invoices: " . $e->getMessage());
        }
        
        return $results;
    }
    
    /**
     * Preview monthly invoices without creating them
     * 
     * @param string $month Month in format 'Y-m'
     * @param array $userIds Optional array of specific user IDs
     * @return array Array of invoice previews
     */
    public function previewMonthlyInvoices($month, $userIds = [])
    {
        $previews = [];
        
        try {
            $date = new DateTime($month . '-01');
            $monthName = $this->getMonthName($date->format('n'));
            $year = $date->format('Y');
            $period = $monthName . ' ' . $year;
            
            $users = $this->getUsersToInvoice($userIds);
            
            foreach ($users as $user) {
                $obligations = json_decode($user['financial_obligations'], true);
                
                $items = [];
                $subtotal = 0;
                
                // Add rent
                if (!empty($obligations['rent_amount'])) {
                    $items[] = [
                        'description' => 'Loyer - ' . $period,
                        'quantity' => 1,
                        'unit_price' => $obligations['rent_amount'],
                        'vat_rate' => 0,
                        'total' => $obligations['rent_amount']
                    ];
                    $subtotal += $obligations['rent_amount'];
                }
                
                // Add charges
                if (!empty($obligations['charges_amount'])) {
                    $items[] = [
                        'description' => 'Charges - ' . $period,
                        'quantity' => 1,
                        'unit_price' => $obligations['charges_amount'],
                        'vat_rate' => 0,
                        'total' => $obligations['charges_amount']
                    ];
                    $subtotal += $obligations['charges_amount'];
                }
                
                // Add secretary services with VAT
                if (!empty($obligations['secretary_tvac_17'])) {
                    $htva = $obligations['secretary_tvac_17'] / 1.17;
                    $items[] = [
                        'description' => 'Secrétariat - ' . $period,
                        'quantity' => 1,
                        'unit_price' => $htva,
                        'vat_rate' => 17,
                        'total' => $obligations['secretary_tvac_17']
                    ];
                    $subtotal += $htva;
                }
                
                $total = !empty($obligations['total_tvac']) ? $obligations['total_tvac'] : $subtotal;
                
                $previews[] = [
                    'user_id' => $user['id'],
                    'user_name' => $user['display_name'],
                    'period' => $period,
                    'items' => $items,
                    'subtotal' => $subtotal,
                    'total' => $total,
                    'obligations' => $obligations
                ];
            }
            
        } catch (Exception $e) {
            throw new Exception("Error previewing invoices: " . $e->getMessage());
        }
        
        return $previews;
    }
    
    /**
     * Get users with active financial obligations
     */
    private function getUsersToInvoice($userIds = [])
    {
        $userModel = new User();
        $users = $userModel->getUsersWithFinancialObligations();
        
        if (!empty($userIds)) {
            $users = array_filter($users, function($user) use ($userIds) {
                return in_array($user['id'], $userIds);
            });
        }
        
        return $users;
    }
    
    /**
     * Create invoice for a specific user
     */
    private function createInvoiceForUser($user, $period, $date)
    {
        $obligations = json_decode($user['financial_obligations'], true);
        
        if (empty($obligations)) {
            return null;
        }
        
        // Get invoice configuration
        $configModel = new Config();
        $invoiceType = $this->getRentalInvoiceType();
        
        // Create invoice
        $invoiceModel = new Invoice();
        
        // Prepare invoice data with items
        $items = [];
        $subtotal = 0;
        $taxAmount = 0;
        
        // Add rent
        if (!empty($obligations['rent_amount'])) {
            $items[] = [
                'description' => 'Loyer - ' . $period,
                'quantity' => 1,
                'unit_price' => $obligations['rent_amount'],
                'vat_rate' => 0,
                'total' => $obligations['rent_amount']
            ];
            $subtotal += $obligations['rent_amount'];
        }
        
        // Add charges
        if (!empty($obligations['charges_amount'])) {
            $items[] = [
                'description' => 'Charges - ' . $period,
                'quantity' => 1,
                'unit_price' => $obligations['charges_amount'],
                'vat_rate' => 0,
                'total' => $obligations['charges_amount']
            ];
            $subtotal += $obligations['charges_amount'];
        }
        
        // Add secretary services with VAT
        if (!empty($obligations['secretary_tvac_17'])) {
            $htva = $obligations['secretary_tvac_17'] / 1.17;
            $vat = $obligations['secretary_tvac_17'] - $htva;
            
            $items[] = [
                'description' => 'Secrétariat - ' . $period,
                'quantity' => 1,
                'unit_price' => $htva,
                'vat_rate' => 17,
                'total' => $obligations['secretary_tvac_17']
            ];
            
            $subtotal += $htva;
            $taxAmount += $vat;
        }
        
        $total = !empty($obligations['total_tvac']) ? $obligations['total_tvac'] : ($subtotal + $taxAmount);
        
        $invoiceData = [
            'client_id' => null,
            'user_id' => $user['id'],
            'type_id' => $invoiceType,
            'invoice_type' => 'rental',
            'status' => 'draft',
            'issue_date' => $date->format('Y-m-d'),
            'due_date' => $date->format('Y-m-t'), // End of month
            'subtotal' => $subtotal,
            'vat_amount' => $taxAmount,
            'total' => $total,
            'notes' => 'Facture générée automatiquement pour ' . $period . ' - Loyer + Charges',
            'lines' => $items
        ];
        
        try {
            $invoice = $invoiceModel->createInvoice($invoiceData);
            if (!$invoice || !is_array($invoice)) {
                throw new Exception("Failed to create invoice");
            }
            return $invoice;
        } catch (Exception $e) {
            throw new Exception("Failed to create invoice for user " . $user['display_name'] . ": " . $e->getMessage());
        }
    }
    
    /**
     * Get or create rental invoice type
     */
    private function getRentalInvoiceType()
    {
        $db = \Flight::db();
        
        // Check if rental type exists
        $stmt = $db->prepare("SELECT * FROM invoice_types WHERE code = 'rental' LIMIT 1");
        $stmt->execute();
        $type = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if (!$type) {
            // Create rental invoice type
            $stmt = $db->prepare("
                INSERT INTO invoice_types (name, code, prefix, created_at) 
                VALUES (:name, :code, :prefix, NOW())
            ");
            $stmt->execute([
                'name' => json_encode([
                    'fr' => 'Facture de location',
                    'en' => 'Rental Invoice',
                    'de' => 'Mietrechnung'
                ]),
                'code' => 'rental',
                'prefix' => 'LOC'
            ]);
            
            // Get the newly created ID
            return $db->lastInsertId();
        }
        
        return $type['id'];
    }
    
    /**
     * Get month name in French
     */
    private function getMonthName($monthNumber)
    {
        $months = [
            1 => 'JANVIER',
            2 => 'FÉVRIER',
            3 => 'MARS',
            4 => 'AVRIL',
            5 => 'MAI',
            6 => 'JUIN',
            7 => 'JUILLET',
            8 => 'AOÛT',
            9 => 'SEPTEMBRE',
            10 => 'OCTOBRE',
            11 => 'NOVEMBRE',
            12 => 'DÉCEMBRE'
        ];
        
        return $months[$monthNumber] ?? '';
    }
}