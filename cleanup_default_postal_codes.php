<?php
/**
 * Clean up default postal codes for users without actual addresses
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

echo "<pre>";
echo "=== Cleaning Up Default Postal Codes ===\n\n";

try {
    $db = Flight::db();
    
    // Find users with default postal code but no VAT number
    $stmt = $db->query("
        SELECT id, username, first_name, last_name, 
               postal_code, city, address,
               vat_number, vat_intercommunautaire,
               billing_postal_code, billing_city, billing_address
        FROM users
        WHERE postal_code = 'L-9579'
        AND (vat_number IS NULL OR vat_number = '')
        AND (vat_intercommunautaire IS NULL OR vat_intercommunautaire = '')
        AND is_practitioner = 1
    ");
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($users) . " practitioners with default postal code but no VAT number\n\n";
    
    foreach ($users as $user) {
        echo "User: " . $user['first_name'] . " " . $user['last_name'] . " (" . $user['username'] . ")\n";
        echo "  Current postal code: " . $user['postal_code'] . "\n";
        echo "  Current city: " . $user['city'] . "\n";
        
        // Check if they have billing address info
        if (!empty($user['billing_postal_code']) && $user['billing_postal_code'] != 'L-9579') {
            echo "  Has different billing postal code: " . $user['billing_postal_code'] . "\n";
            echo "  → Keeping address info as it may be intentional\n";
        } else {
            echo "  → Clearing default address values\n";
            
            // Clear default values
            $updateStmt = $db->prepare("
                UPDATE users 
                SET postal_code = NULL,
                    city = NULL,
                    address = NULL
                WHERE id = :id
                AND postal_code = 'L-9579'
                AND city = 'Weidingen'
                AND address = '15, am Pëtz'
            ");
            $updateStmt->execute(['id' => $user['id']]);
        }
        echo "\n";
    }
    
    echo "\n✅ Cleanup complete!\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
echo "</pre>";