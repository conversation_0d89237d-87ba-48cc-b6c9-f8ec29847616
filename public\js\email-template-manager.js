/**
 * Email Template Manager
 * Advanced JavaScript for dynamic email template UI
 * Features: Conditional logic builder, variable picker, live preview, drag-drop reordering
 */

(function() {
    'use strict';

    // Global template manager object
    window.EmailTemplateManager = {
        // Current template data
        currentTemplate: null,
        templates: [],
        variables: {},
        
        // UI elements
        elements: {
            templateList: null,
            editor: null,
            preview: null,
            variablePicker: null,
            conditionBuilder: null
        },

        // Initialize the template manager
        init: function() {
            this.cacheElements();
            this.bindEvents();
            this.loadTemplates();
            this.initializeDragDrop();
            this.initializeEditor();
            this.initializeVariablePicker();
            this.initializeConditionBuilder();
        },

        // Cache DOM elements
        cacheElements: function() {
            this.elements.templateList = document.getElementById('templateList');
            this.elements.editor = document.getElementById('templateEditor');
            this.elements.preview = document.getElementById('templatePreview');
            this.elements.variablePicker = document.getElementById('variablePicker');
            this.elements.conditionBuilder = document.getElementById('conditionBuilder');
        },

        // Bind event handlers
        bindEvents: function() {
            // Template selection
            document.addEventListener('click', (e) => {
                if (e.target.matches('.template-item')) {
                    this.loadTemplate(e.target.dataset.templateId);
                }
            });

            // Save button
            const saveBtn = document.getElementById('saveTemplateBtn');
            if (saveBtn) {
                saveBtn.addEventListener('click', () => this.saveTemplate());
            }

            // Preview update on input
            const inputs = ['templateSubject', 'templateBody', 'templateFooter'];
            inputs.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('input', () => this.updatePreview());
                }
            });

            // Variable insertion
            document.addEventListener('click', (e) => {
                if (e.target.matches('.variable-tag')) {
                    this.insertVariable(e.target.dataset.variable);
                }
            });

            // Tab switching
            document.querySelectorAll('[data-tab-target]').forEach(tab => {
                tab.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.switchTab(tab.dataset.tabTarget);
                });
            });
        },

        // Load all templates
        loadTemplates: function() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/email-templates', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.templates = JSON.parse(xhr.responseText);
                    this.renderTemplateList();
                }
            };
            
            xhr.send();
        },

        // Load specific template
        loadTemplate: function(templateId) {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', `/api/email-templates/${templateId}`, true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.currentTemplate = JSON.parse(xhr.responseText);
                    this.renderTemplate();
                    this.updatePreview();
                    this.highlightActiveTemplate(templateId);
                }
            };
            
            xhr.send();
        },

        // Render template list
        renderTemplateList: function() {
            if (!this.elements.templateList) return;
            
            this.elements.templateList.innerHTML = this.templates.map(template => `
                <div class="template-item ${template.is_active ? 'active' : ''}" 
                     data-template-id="${template.id}"
                     data-priority="${template.priority || 0}"
                     draggable="true">
                    <div class="template-header">
                        <h4>${this.escapeHtml(template.name)}</h4>
                        <span class="priority-badge priority-${template.priority || 'low'}">
                            Priority: ${template.priority || 0}
                        </span>
                    </div>
                    <p class="template-description">${this.escapeHtml(template.description || '')}</p>
                    ${template.conditions && template.conditions.length ? 
                        '<span class="condition-indicator">Has conditions</span>' : ''}
                </div>
            `).join('');
        },

        // Render current template in editor
        renderTemplate: function() {
            if (!this.currentTemplate) return;
            
            // Update form fields
            document.getElementById('templateSubject').value = this.currentTemplate.subject || '';
            document.getElementById('templateBody').value = this.currentTemplate.body || '';
            document.getElementById('templateFooter').value = this.currentTemplate.footer || '';
            
            // Update settings
            document.getElementById('templateActive').checked = this.currentTemplate.is_active;
            document.getElementById('templatePriority').value = this.currentTemplate.priority || 0;
            
            // Update conditions
            this.renderConditions();
            
            // Update available variables
            this.updateVariableList();
        },

        // Initialize drag and drop for priority reordering
        initializeDragDrop: function() {
            let draggedElement = null;
            
            document.addEventListener('dragstart', (e) => {
                if (e.target.matches('.template-item')) {
                    draggedElement = e.target;
                    e.target.classList.add('dragging');
                    e.dataTransfer.effectAllowed = 'move';
                }
            });
            
            document.addEventListener('dragend', (e) => {
                if (e.target.matches('.template-item')) {
                    e.target.classList.remove('dragging');
                }
            });
            
            document.addEventListener('dragover', (e) => {
                e.preventDefault();
                const container = this.elements.templateList;
                if (!container || !draggedElement) return;
                
                const afterElement = this.getDragAfterElement(container, e.clientY);
                if (afterElement == null) {
                    container.appendChild(draggedElement);
                } else {
                    container.insertBefore(draggedElement, afterElement);
                }
            });
            
            document.addEventListener('drop', (e) => {
                e.preventDefault();
                if (draggedElement) {
                    this.updateTemplatePriorities();
                    draggedElement = null;
                }
            });
        },

        // Get element after which to insert dragged item
        getDragAfterElement: function(container, y) {
            const draggableElements = [...container.querySelectorAll('.template-item:not(.dragging)')];
            
            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;
                
                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        },

        // Update template priorities after reordering
        updateTemplatePriorities: function() {
            const items = this.elements.templateList.querySelectorAll('.template-item');
            const updates = [];
            
            items.forEach((item, index) => {
                const templateId = item.dataset.templateId;
                const newPriority = items.length - index;
                
                if (parseInt(item.dataset.priority) !== newPriority) {
                    updates.push({ id: templateId, priority: newPriority });
                    item.dataset.priority = newPriority;
                    item.querySelector('.priority-badge').textContent = `Priority: ${newPriority}`;
                }
            });
            
            if (updates.length > 0) {
                this.savePriorityUpdates(updates);
            }
        },

        // Save priority updates to server
        savePriorityUpdates: function(updates) {
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/api/email-templates/update-priorities', true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-CSRF-Token', this.getCsrfToken());
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.showNotification('Priorities updated successfully', 'success');
                }
            };
            
            xhr.send(JSON.stringify({ updates: updates }));
        },

        // Initialize code editor with syntax highlighting
        initializeEditor: function() {
            const bodyTextarea = document.getElementById('templateBody');
            if (!bodyTextarea) return;
            
            // Add line numbers
            this.addLineNumbers(bodyTextarea);
            
            // Add syntax highlighting for variables
            bodyTextarea.addEventListener('input', () => {
                this.highlightVariables(bodyTextarea);
            });
            
            // Tab key support
            bodyTextarea.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = bodyTextarea.selectionStart;
                    const end = bodyTextarea.selectionEnd;
                    const value = bodyTextarea.value;
                    
                    bodyTextarea.value = value.substring(0, start) + '    ' + value.substring(end);
                    bodyTextarea.selectionStart = bodyTextarea.selectionEnd = start + 4;
                }
            });
        },

        // Add line numbers to editor
        addLineNumbers: function(textarea) {
            const wrapper = document.createElement('div');
            wrapper.className = 'editor-wrapper';
            
            const lineNumbers = document.createElement('div');
            lineNumbers.className = 'line-numbers';
            
            textarea.parentNode.insertBefore(wrapper, textarea);
            wrapper.appendChild(lineNumbers);
            wrapper.appendChild(textarea);
            
            const updateLineNumbers = () => {
                const lines = textarea.value.split('\n').length;
                lineNumbers.innerHTML = Array.from({ length: lines }, (_, i) => i + 1).join('<br>');
            };
            
            textarea.addEventListener('input', updateLineNumbers);
            textarea.addEventListener('scroll', () => {
                lineNumbers.scrollTop = textarea.scrollTop;
            });
            
            updateLineNumbers();
        },

        // Initialize variable picker with autocomplete
        initializeVariablePicker: function() {
            this.loadAvailableVariables();
            
            // Create autocomplete functionality
            const editors = ['templateSubject', 'templateBody', 'templateFooter'];
            editors.forEach(editorId => {
                const editor = document.getElementById(editorId);
                if (editor) {
                    this.setupAutocomplete(editor);
                }
            });
        },

        // Load available variables
        loadAvailableVariables: function() {
            const xhr = new XMLHttpRequest();
            xhr.open('GET', '/api/email-templates/variables', true);
            xhr.setRequestHeader('Accept', 'application/json');
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.variables = JSON.parse(xhr.responseText);
                    this.renderVariablePicker();
                }
            };
            
            xhr.send();
        },

        // Render variable picker
        renderVariablePicker: function() {
            if (!this.elements.variablePicker) return;
            
            const categories = Object.keys(this.variables);
            this.elements.variablePicker.innerHTML = categories.map(category => `
                <div class="variable-category">
                    <h5 class="category-title">${this.escapeHtml(category)}</h5>
                    <div class="variable-list">
                        ${this.variables[category].map(variable => `
                            <span class="variable-tag" data-variable="{${variable.key}}" 
                                  title="${this.escapeHtml(variable.description)}">
                                {${variable.key}}
                            </span>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        },

        // Setup autocomplete for variables
        setupAutocomplete: function(input) {
            let autocompleteList = null;
            
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                const cursorPos = e.target.selectionStart;
                const textBefore = value.substring(0, cursorPos);
                const match = textBefore.match(/\{([^}]*)$/);
                
                if (match) {
                    const partial = match[1].toLowerCase();
                    const suggestions = this.getVariableSuggestions(partial);
                    
                    if (suggestions.length > 0) {
                        this.showAutocomplete(input, suggestions, match.index);
                    } else {
                        this.hideAutocomplete();
                    }
                } else {
                    this.hideAutocomplete();
                }
            });
            
            // Hide autocomplete on blur
            input.addEventListener('blur', () => {
                setTimeout(() => this.hideAutocomplete(), 200);
            });
        },

        // Get variable suggestions
        getVariableSuggestions: function(partial) {
            const allVariables = [];
            Object.values(this.variables).forEach(category => {
                category.forEach(variable => {
                    if (variable.key.toLowerCase().includes(partial)) {
                        allVariables.push(variable);
                    }
                });
            });
            return allVariables.slice(0, 10); // Limit to 10 suggestions
        },

        // Show autocomplete dropdown
        showAutocomplete: function(input, suggestions, startPos) {
            this.hideAutocomplete();
            
            const dropdown = document.createElement('div');
            dropdown.className = 'autocomplete-dropdown';
            dropdown.id = 'autocompleteDropdown';
            
            suggestions.forEach(suggestion => {
                const item = document.createElement('div');
                item.className = 'autocomplete-item';
                item.innerHTML = `
                    <strong>{${suggestion.key}}</strong>
                    <small>${this.escapeHtml(suggestion.description)}</small>
                `;
                item.addEventListener('click', () => {
                    this.insertVariableAtPosition(input, suggestion.key, startPos);
                    this.hideAutocomplete();
                });
                dropdown.appendChild(item);
            });
            
            // Position dropdown
            const rect = input.getBoundingClientRect();
            dropdown.style.position = 'absolute';
            dropdown.style.left = rect.left + 'px';
            dropdown.style.top = (rect.bottom + 2) + 'px';
            dropdown.style.width = Math.min(300, rect.width) + 'px';
            
            document.body.appendChild(dropdown);
        },

        // Hide autocomplete dropdown
        hideAutocomplete: function() {
            const dropdown = document.getElementById('autocompleteDropdown');
            if (dropdown) {
                dropdown.remove();
            }
        },

        // Insert variable at cursor position
        insertVariable: function(variable) {
            const activeElement = document.activeElement;
            if (activeElement && (activeElement.tagName === 'TEXTAREA' || activeElement.tagName === 'INPUT')) {
                const start = activeElement.selectionStart;
                const end = activeElement.selectionEnd;
                const value = activeElement.value;
                
                activeElement.value = value.substring(0, start) + variable + value.substring(end);
                activeElement.selectionStart = activeElement.selectionEnd = start + variable.length;
                activeElement.focus();
                
                this.updatePreview();
            }
        },

        // Insert variable at specific position
        insertVariableAtPosition: function(input, variableKey, startPos) {
            const value = input.value;
            const before = value.substring(0, startPos);
            const after = value.substring(input.selectionStart);
            
            input.value = before + '{' + variableKey + '}' + after;
            input.selectionStart = input.selectionEnd = startPos + variableKey.length + 2;
            input.focus();
            
            this.updatePreview();
        },

        // Initialize condition builder
        initializeConditionBuilder: function() {
            const addConditionBtn = document.getElementById('addConditionBtn');
            if (addConditionBtn) {
                addConditionBtn.addEventListener('click', () => this.addCondition());
            }
        },

        // Render conditions
        renderConditions: function() {
            if (!this.elements.conditionBuilder || !this.currentTemplate) return;
            
            const conditions = this.currentTemplate.conditions || [];
            this.elements.conditionBuilder.innerHTML = conditions.map((condition, index) => `
                <div class="condition-item" data-index="${index}">
                    <div class="condition-header">
                        <span class="condition-number">Condition ${index + 1}</span>
                        <button type="button" class="btn-remove" onclick="EmailTemplateManager.removeCondition(${index})">
                            <i class="icon-trash"></i>
                        </button>
                    </div>
                    <div class="condition-content">
                        ${this.renderConditionEditor(condition, index)}
                    </div>
                </div>
            `).join('');
        },

        // Render condition editor
        renderConditionEditor: function(condition, index) {
            return `
                <div class="condition-row">
                    <label>If</label>
                    <select class="condition-field" data-index="${index}" data-property="field">
                        <option value="">Select field...</option>
                        ${this.getConditionFields().map(field => `
                            <option value="${field.value}" ${condition.field === field.value ? 'selected' : ''}>
                                ${field.label}
                            </option>
                        `).join('')}
                    </select>
                    
                    <select class="condition-operator" data-index="${index}" data-property="operator">
                        <option value="equals" ${condition.operator === 'equals' ? 'selected' : ''}>equals</option>
                        <option value="not_equals" ${condition.operator === 'not_equals' ? 'selected' : ''}>not equals</option>
                        <option value="contains" ${condition.operator === 'contains' ? 'selected' : ''}>contains</option>
                        <option value="greater_than" ${condition.operator === 'greater_than' ? 'selected' : ''}>greater than</option>
                        <option value="less_than" ${condition.operator === 'less_than' ? 'selected' : ''}>less than</option>
                        <option value="is_empty" ${condition.operator === 'is_empty' ? 'selected' : ''}>is empty</option>
                        <option value="is_not_empty" ${condition.operator === 'is_not_empty' ? 'selected' : ''}>is not empty</option>
                    </select>
                    
                    <input type="text" class="condition-value" data-index="${index}" data-property="value"
                           value="${this.escapeHtml(condition.value || '')}" 
                           placeholder="Value..."
                           ${condition.operator === 'is_empty' || condition.operator === 'is_not_empty' ? 'disabled' : ''}>
                </div>
                <div class="condition-row">
                    <label>Then use template:</label>
                    <select class="condition-template" data-index="${index}" data-property="template_id">
                        <option value="">Default template</option>
                        ${this.templates.filter(t => t.id !== this.currentTemplate.id).map(template => `
                            <option value="${template.id}" ${condition.template_id === template.id ? 'selected' : ''}>
                                ${this.escapeHtml(template.name)}
                            </option>
                        `).join('')}
                    </select>
                </div>
            `;
        },

        // Get available fields for conditions
        getConditionFields: function() {
            return [
                { value: 'invoice.type', label: 'Invoice Type' },
                { value: 'invoice.total_amount', label: 'Invoice Total' },
                { value: 'invoice.is_overdue', label: 'Is Overdue' },
                { value: 'patient.language', label: 'Patient Language' },
                { value: 'patient.has_insurance', label: 'Has Insurance' },
                { value: 'patient.payment_method', label: 'Payment Method' },
                { value: 'company.type', label: 'Company Type' },
                { value: 'reminder.count', label: 'Reminder Count' }
            ];
        },

        // Add new condition
        addCondition: function() {
            if (!this.currentTemplate.conditions) {
                this.currentTemplate.conditions = [];
            }
            
            this.currentTemplate.conditions.push({
                field: '',
                operator: 'equals',
                value: '',
                template_id: null
            });
            
            this.renderConditions();
        },

        // Remove condition
        removeCondition: function(index) {
            this.currentTemplate.conditions.splice(index, 1);
            this.renderConditions();
        },

        // Update live preview
        updatePreview: function() {
            const previewSubject = document.getElementById('previewSubject');
            const previewBody = document.getElementById('previewBody');
            const previewFooter = document.getElementById('previewFooter');
            
            if (previewSubject) {
                previewSubject.textContent = this.processVariables(
                    document.getElementById('templateSubject').value
                );
            }
            
            if (previewBody) {
                previewBody.innerHTML = this.processVariables(
                    document.getElementById('templateBody').value
                );
            }
            
            if (previewFooter) {
                previewFooter.innerHTML = this.processVariables(
                    document.getElementById('templateFooter').value
                );
            }
        },

        // Process variables for preview
        processVariables: function(text) {
            const sampleData = {
                invoice_number: 'INV-2025-001',
                invoice_date: '2025-01-28',
                due_date: '2025-02-28',
                total_amount: '€150.00',
                patient_name: 'John Doe',
                patient_email: '<EMAIL>',
                company_name: 'Medical Practice Ltd',
                payment_link: '#'
            };
            
            return text.replace(/\{([^}]+)\}/g, (match, variable) => {
                return sampleData[variable] || match;
            });
        },

        // Save template
        saveTemplate: function() {
            if (!this.currentTemplate) return;
            
            // Gather form data
            const formData = {
                subject: document.getElementById('templateSubject').value,
                body: document.getElementById('templateBody').value,
                footer: document.getElementById('templateFooter').value,
                is_active: document.getElementById('templateActive').checked,
                priority: parseInt(document.getElementById('templatePriority').value),
                conditions: this.gatherConditions()
            };
            
            // Validate
            if (!formData.subject || !formData.body) {
                this.showNotification('Subject and body are required', 'error');
                return;
            }
            
            // Send update request
            const xhr = new XMLHttpRequest();
            xhr.open('PUT', `/api/email-templates/${this.currentTemplate.id}`, true);
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-CSRF-Token', this.getCsrfToken());
            
            xhr.onload = () => {
                if (xhr.status === 200) {
                    this.showNotification('Template saved successfully', 'success');
                    this.loadTemplates(); // Refresh list
                } else {
                    this.showNotification('Failed to save template', 'error');
                }
            };
            
            xhr.send(JSON.stringify(formData));
        },

        // Gather conditions from form
        gatherConditions: function() {
            const conditions = [];
            document.querySelectorAll('.condition-item').forEach(item => {
                const index = item.dataset.index;
                const condition = {
                    field: item.querySelector(`[data-index="${index}"][data-property="field"]`).value,
                    operator: item.querySelector(`[data-index="${index}"][data-property="operator"]`).value,
                    value: item.querySelector(`[data-index="${index}"][data-property="value"]`).value,
                    template_id: item.querySelector(`[data-index="${index}"][data-property="template_id"]`).value || null
                };
                
                if (condition.field) {
                    conditions.push(condition);
                }
            });
            return conditions;
        },

        // Switch tabs
        switchTab: function(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            document.querySelectorAll('[data-tab-target]').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            const selectedContent = document.getElementById(`${tabName}-tab`);
            const selectedTab = document.querySelector(`[data-tab-target="${tabName}"]`);
            
            if (selectedContent) selectedContent.classList.add('active');
            if (selectedTab) selectedTab.classList.add('active');
        },

        // Highlight active template in list
        highlightActiveTemplate: function(templateId) {
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            const activeItem = document.querySelector(`[data-template-id="${templateId}"]`);
            if (activeItem) {
                activeItem.classList.add('selected');
            }
        },

        // Show notification
        showNotification: function(message, type) {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        },

        // Get CSRF token
        getCsrfToken: function() {
            const meta = document.querySelector('meta[name="csrf-token"]');
            return meta ? meta.content : '';
        },

        // Escape HTML
        escapeHtml: function(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        },

        // Highlight variables in editor
        highlightVariables: function(textarea) {
            // This would integrate with a syntax highlighting library
            // For now, it's a placeholder for future enhancement
        }
    };

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            EmailTemplateManager.init();
        });
    } else {
        EmailTemplateManager.init();
    }
})();