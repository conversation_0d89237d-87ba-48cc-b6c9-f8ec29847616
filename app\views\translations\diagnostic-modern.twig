{% extends "base-modern.twig" %}

{% block title %}{{ __('translations.diagnostic') }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ __('translations.diagnostic') }}</h3>
                <div class="card-tools">
                    <a href="{{ base_url }}/translations" class="btn btn-primary btn-sm">
                        <i class="bi bi-arrow-left"></i> {{ __('common.back') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Translation Coverage Matrix -->
                <div class="mb-5">
                    <h4 class="mb-3">{{ __('translations.coverage_matrix') }}</h4>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>{{ __('translations.group') }}</th>
                                    {% for lang in languages %}
                                        <th class="text-center">{{ lang|upper }}</th>
                                    {% endfor %}
                                    <th class="text-center">{{ __('common.total') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for group, counts in matrix %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ group }}</span>
                                    </td>
                                    {% set groupTotal = 0 %}
                                    {% for lang in languages %}
                                        {% set count = counts[lang]|default(0) %}
                                        {% set groupTotal = groupTotal + count %}
                                        <td class="text-center">
                                            {% if count > 0 %}
                                                <span class="badge bg-success">{{ count }}</span>
                                            {% else %}
                                                <span class="badge bg-danger">0</span>
                                            {% endif %}
                                        </td>
                                    {% endfor %}
                                    <td class="text-center">
                                        <strong>{{ groupTotal }}</strong>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                            <tfoot class="table-light">
                                <tr>
                                    <th>{{ __('common.total') }}</th>
                                    {% set grandTotal = 0 %}
                                    {% for lang in languages %}
                                        {% set langTotal = 0 %}
                                        {% for group, counts in matrix %}
                                            {% set langTotal = langTotal + counts[lang]|default(0) %}
                                        {% endfor %}
                                        {% set grandTotal = grandTotal + langTotal %}
                                        <th class="text-center">
                                            <span class="badge bg-primary">{{ langTotal }}</span>
                                        </th>
                                    {% endfor %}
                                    <th class="text-center">
                                        <span class="badge bg-dark">{{ grandTotal }}</span>
                                    </th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>

                <!-- Duplicate Keys -->
                {% if duplicates|length > 0 %}
                <div class="mb-5">
                    <h4 class="mb-3 text-danger">
                        <i class="bi bi-exclamation-triangle"></i> {{ __('translations.duplicate_keys') }}
                    </h4>
                    <div class="alert alert-danger">
                        <p>{{ __('translations.duplicate_keys_warning') }}</p>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>{{ __('translations.language') }}</th>
                                    <th>{{ __('translations.group') }}</th>
                                    <th>{{ __('translations.key') }}</th>
                                    <th>{{ __('translations.duplicates') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for dup in duplicates %}
                                <tr>
                                    <td>{{ dup.language|upper }}</td>
                                    <td><span class="badge bg-secondary">{{ dup.group }}</span></td>
                                    <td><code>{{ dup.key }}</code></td>
                                    <td><span class="badge bg-danger">{{ dup.duplicates }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- Missing Translations -->
                {% if missingTranslations|length > 0 %}
                <div class="mb-5">
                    <h4 class="mb-3 text-warning">
                        <i class="bi bi-exclamation-circle"></i> {{ __('translations.missing_translations') }}
                    </h4>
                    <p class="text-muted">{{ __('translations.missing_translations_description') }}</p>
                    
                    {% for lang, groups in missingTranslations %}
                        <div class="card mb-3">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">{{ lang|upper }} - {{ __('translations.missing_count', {'count': groups|length}) }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>{{ __('translations.group') }}</th>
                                                <th>{{ __('translations.missing_keys') }}</th>
                                                <th>{{ __('common.actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for group, keys in groups %}
                                            <tr>
                                                <td><span class="badge bg-secondary">{{ group }}</span></td>
                                                <td>
                                                    {% for key in keys %}
                                                        <code class="me-2">{{ key }}</code>
                                                    {% endfor %}
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-primary fix-missing" 
                                                            data-lang="{{ lang }}" 
                                                            data-group="{{ group }}">
                                                        <i class="bi bi-wrench"></i> {{ __('common.fix') }}
                                                    </button>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Inconsistent Keys -->
                {% if inconsistentKeys|length > 0 %}
                <div class="mb-5">
                    <h4 class="mb-3 text-info">
                        <i class="bi bi-info-circle"></i> {{ __('translations.inconsistent_keys') }}
                    </h4>
                    <p class="text-muted">{{ __('translations.inconsistent_keys_description') }}</p>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{{ __('translations.group') }}</th>
                                    <th>{{ __('translations.key') }}</th>
                                    <th>{{ __('translations.available_in') }}</th>
                                    <th>{{ __('translations.missing_in') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inconsistentKeys %}
                                <tr>
                                    <td><span class="badge bg-secondary">{{ item.group }}</span></td>
                                    <td><code>{{ item.key }}</code></td>
                                    <td>
                                        {% for lang in item.available %}
                                            <span class="badge bg-success">{{ lang|upper }}</span>
                                        {% endfor %}
                                    </td>
                                    <td>
                                        {% for lang in item.missing %}
                                            <span class="badge bg-danger">{{ lang|upper }}</span>
                                        {% endfor %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
                {% endif %}

                <!-- Summary -->
                <div class="alert alert-info">
                    <h5>{{ __('translations.diagnostic_summary') }}</h5>
                    <ul class="mb-0">
                        <li>{{ __('translations.total_groups') }}: <strong>{{ matrix|length }}</strong></li>
                        <li>{{ __('translations.total_languages') }}: <strong>{{ languages|length }}</strong></li>
                        {% if duplicates|length > 0 %}
                        <li class="text-danger">{{ __('translations.duplicate_entries') }}: <strong>{{ duplicates|length }}</strong></li>
                        {% endif %}
                        {% if missingTranslations|length > 0 %}
                        <li class="text-warning">{{ __('translations.languages_with_missing') }}: <strong>{{ missingTranslations|length }}</strong></li>
                        {% endif %}
                        {% if inconsistentKeys|length > 0 %}
                        <li class="text-info">{{ __('translations.inconsistent_entries') }}: <strong>{{ inconsistentKeys|length }}</strong></li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Fix Missing Modal -->
<div class="modal fade" id="fixMissingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="fixMissingForm">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('translations.fix_missing_translations') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('translations.fix_missing_description') }}</p>
                    <input type="hidden" name="language" id="fix-language">
                    <input type="hidden" name="group" id="fix-group">
                    
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="action" id="action-copy" value="copy" checked>
                        <label class="form-check-label" for="action-copy">
                            {{ __('translations.copy_from_reference') }}
                        </label>
                    </div>
                    <div class="form-check mb-2">
                        <input class="form-check-input" type="radio" name="action" id="action-empty" value="empty">
                        <label class="form-check-label" for="action-empty">
                            {{ __('translations.create_empty') }}
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="action" id="action-key" value="key">
                        <label class="form-check-label" for="action-key">
                            {{ __('translations.use_key_as_value') }}
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-wrench"></i> {{ __('common.fix') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Fix missing translations
    $('.fix-missing').on('click', function() {
        var lang = $(this).data('lang');
        var group = $(this).data('group');
        
        $('#fix-language').val(lang);
        $('#fix-group').val(group);
        $('#fixMissingModal').modal('show');
    });
    
    // Handle fix form submission
    $('#fixMissingForm').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var data = form.serialize() + '&csrf_token={{ csrf_token }}';
        
        $.ajax({
            url: '{{ base_url }}/translations/fix-missing',
            method: 'POST',
            data: data,
            success: function(response) {
                if (response.success) {
                    $('#fixMissingModal').modal('hide');
                    toastr.success(response.message || '{{ __("translations.missing_fixed") }}');
                    
                    // Reload page after a delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    toastr.error(response.message || '{{ __("common.error_occurred") }}');
                }
            },
            error: function() {
                toastr.error('{{ __("common.error_occurred") }}');
            }
        });
    });
});
</script>
{% endblock %}