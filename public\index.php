<?php
/**
 * Health Center Billing System
 * Entry point for Flight PHP application
 */

// Load composer autoloader
require __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Initialize Flight
require __DIR__ . '/../app/config/bootstrap.php';

// Enable method spoofing for PUT/DELETE via _method parameter
Flight::before('start', function(&$params, &$output) {
    if (isset($_POST['_method'])) {
        $_SERVER['REQUEST_METHOD'] = strtoupper($_POST['_method']);
        Flight::request()->method = strtoupper($_POST['_method']);
    }
    
    // Fix doubled base URL in request URI
    $baseUrl = Flight::get('flight.base_url');
    $requestUri = $_SERVER['REQUEST_URI'] ?? '';
    $doubledBase = $baseUrl . $baseUrl;
    
    if (strpos($requestUri, $doubledBase) === 0) {
        // Redirect to correct URL without the doubled base
        $correctUri = $baseUrl . substr($requestUri, strlen($doubledBase));
        header('Location: ' . $correctUri);
        exit;
    }
});

// Load routes
require __DIR__ . '/../app/config/routes.php';

// Start Flight
Flight::start();