<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment
if (file_exists(dirname(__DIR__) . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
    $dotenv->load();
}

// Database connection
$host = getenv('DB_HOST') ?: '127.0.0.1';
$dbname = getenv('DB_DATABASE') ?: 'fitapp';
$username = getenv('DB_USERNAME') ?: 'root';
$password = getenv('DB_PASSWORD') ?: 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Checking Invoices Table Structure</h2>";
    echo "<pre>";
    
    // Get the column information for invoices.id
    $sql = "SELECT COLUMN_NAME, COLUMN_TYPE, IS_NULLABLE, COLUMN_KEY, EXTRA 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = :dbname 
            AND TABLE_NAME = 'invoices' 
            AND COLUMN_NAME = 'id'";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['dbname' => $dbname]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Invoices.id column info:\n";
    print_r($result);
    
    echo "</pre>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
}