<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CHECKING USER ADDRESSES ===\n\n";
    
    // Get distinct addresses
    $stmt = $pdo->query("
        SELECT DISTINCT address, postal_code, city, country, COUNT(*) as user_count
        FROM users 
        WHERE address IS NOT NULL AND address != ''
        GROUP BY address, postal_code, city, country
        ORDER BY user_count DESC
        LIMIT 10
    ");
    $addresses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Most common addresses:\n\n";
    foreach ($addresses as $addr) {
        echo "Address: {$addr['address']}\n";
        echo "City: {$addr['postal_code']} {$addr['city']}, {$addr['country']}\n";
        echo "Used by: {$addr['user_count']} users\n\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}