# Phase 1 Detailed Tasks - Health Center Billing System

## Week 1: Project Setup & Architecture

### Task 1.1: Initialize Project Structure
**Estimated Time:** 8-10 hours total

#### Subtasks:
1. **1.1.1 Create base directory structure** (30 min)
   - Create all required directories as per project structure
   - Test: `ls -la /mnt/c/wamp64/www/fit/` should show all directories
   - Command: `find /mnt/c/wamp64/www/fit -type d | sort`
   - Expected: All directories from project structure exist

2. **1.1.2 Initialize Git repository** (15 min)
   - Initialize git and create .gitignore
   - Test: `git status` shows clean working directory
   - Command: `cd /mnt/c/wamp64/www/fit && git init && git status`
   - Expected: "Initialized empty Git repository"

3. **1.1.3 Create initial README.md** (30 min)
   - Add project title, description, and setup instructions
   - Test: `cat README.md | head -n 5`
   - Expected: Shows project information

4. **1.1.4 Set up .gitignore file** (30 min)
   - Add vendor/, .env, storage/logs/*, etc.
   - Test: `cat .gitignore | grep -E "vendor|\.env|logs"`
   - Expected: Shows ignored patterns

5. **1.1.5 Create composer.json** (45 min)
   - Initialize composer with project metadata
   - Test: `composer validate`
   - Command: `cd /mnt/c/wamp64/www/fit && composer init --no-interaction`
   - Expected: "Valid composer.json"

6. **1.1.6 Install Flight PHP framework** (30 min)
   - Add Flight PHP as dependency
   - Test: `composer show mikecao/flight`
   - Command: `cd /mnt/c/wamp64/www/fit && composer require mikecao/flight:^3.0`
   - Expected: Shows installed version 3.x

7. **1.1.7 Create public/index.php entry point** (1 hour)
   - Basic Flight PHP bootstrap
   - Test: `php -l public/index.php`
   - Expected: "No syntax errors detected"

8. **1.1.8 Configure Apache .htaccess** (45 min)
   - URL rewriting for Flight PHP
   - Test: `cat public/.htaccess | grep RewriteEngine`
   - Expected: "RewriteEngine On"

9. **1.1.9 Create environment configuration** (45 min)
   - Create .env.example with all variables
   - Test: `grep -E "DB_|APP_" .env.example | wc -l`
   - Expected: At least 10 configuration variables

10. **1.1.10 Test basic route** (1 hour)
    - Create test route and verify it works
    - Test: `curl -s http://localhost{{ base_url }}/ | grep -o "Hello"`
    - Expected: "Hello"

**Dependencies:** None
**Blocking:** All subsequent tasks

### Task 1.2: Database Setup & Connection
**Estimated Time:** 6-8 hours total

#### Subtasks:
1. **1.2.1 Create database schema file** (1 hour)
   - Write initial SQL schema
   - Test: `ls -la database/schema.sql && wc -l database/schema.sql`
   - Expected: File exists with 50+ lines

2. **1.2.2 Create database in MySQL** (15 min)
   - Create billing_system database
   - Test: `mysql -u root -e "SHOW DATABASES" | grep billing_system`
   - Command: `mysql -u root -e "CREATE DATABASE IF NOT EXISTS billing_system"`
   - Expected: "billing_system"

3. **1.2.3 Create Database connection class** (1.5 hours)
   - PDO wrapper with error handling
   - Test: `php -l app/config/database.php`
   - Expected: "No syntax errors detected"

4. **1.2.4 Implement connection pooling** (1 hour)
   - Singleton pattern for DB connections
   - Test: Create test script to verify single instance
   - Expected: Same connection object returned

5. **1.2.5 Create migration system structure** (1 hour)
   - Migration runner class
   - Test: `php -l database/Migration.php`
   - Expected: "No syntax errors detected"

6. **1.2.6 Create first migration** (45 min)
   - Users table migration
   - Test: `ls database/migrations/*_create_users_table.php`
   - Expected: Migration file exists

7. **1.2.7 Test database connection** (30 min)
   - Connection test script
   - Test: `php scripts/test-db.php`
   - Expected: "Database connection successful"

8. **1.2.8 Create migration runner command** (1 hour)
   - CLI script to run migrations
   - Test: `php scripts/migrate.php --status`
   - Expected: Shows pending migrations

**Dependencies:** Task 1.1 completed
**Blocking:** Task 1.3, 2.1

### Task 1.3: Routing & Middleware System
**Estimated Time:** 8-10 hours total

#### Subtasks:
1. **1.3.1 Create routes configuration file** (45 min)
   - Structured route definitions
   - Test: `php -l app/config/routes.php`
   - Expected: "No syntax errors detected"

2. **1.3.2 Implement route loader** (1 hour)
   - Dynamic route registration
   - Test: Route loading without errors
   - Expected: Routes registered in Flight

3. **1.3.3 Create base Controller class** (1.5 hours)
   - Common controller functionality
   - Test: `php -l app/controllers/BaseController.php`
   - Expected: "No syntax errors detected"

4. **1.3.4 Implement request validation** (1.5 hours)
   - Input validation helpers
   - Test: Validation test cases pass
   - Expected: Validation works correctly

5. **1.3.5 Create middleware interface** (1 hour)
   - Middleware contract definition
   - Test: `php -l app/middleware/MiddlewareInterface.php`
   - Expected: "No syntax errors detected"

6. **1.3.6 Build middleware runner** (1.5 hours)
   - Middleware chain execution
   - Test: Middleware execution order test
   - Expected: Correct execution order

7. **1.3.7 Create CORS middleware** (1 hour)
   - Cross-origin request handling
   - Test: OPTIONS request returns headers
   - Expected: Access-Control headers present

8. **1.3.8 Test middleware pipeline** (1 hour)
   - Integration test for middleware
   - Test: `curl -I http://localhost{{ base_url }}/test`
   - Expected: Custom headers from middleware

**Dependencies:** Task 1.2 completed
**Blocking:** Task 2.3

### Task 1.4: Error Handling & Logging
**Estimated Time:** 6-7 hours total

#### Subtasks:
1. **1.4.1 Create custom exception classes** (1 hour)
   - Application-specific exceptions
   - Test: `php -l app/exceptions/AppException.php`
   - Expected: "No syntax errors detected"

2. **1.4.2 Implement error handler** (1.5 hours)
   - Global error catching
   - Test: Trigger test error
   - Expected: Custom error page displayed

3. **1.4.3 Create logging service** (1.5 hours)
   - PSR-3 compatible logger
   - Test: `php scripts/test-logger.php`
   - Expected: Log file created with entry

4. **1.4.4 Configure log rotation** (45 min)
   - Daily log file rotation
   - Test: Check log file naming
   - Expected: Date-based log files

5. **1.4.5 Implement debug mode** (45 min)
   - Development error display
   - Test: Toggle debug mode
   - Expected: Detailed errors in debug mode

6. **1.4.6 Create error templates** (1 hour)
   - User-friendly error pages
   - Test: View 404 and 500 pages
   - Expected: Styled error pages

7. **1.4.7 Add request logging** (30 min)
   - Log all HTTP requests
   - Test: Make requests and check logs
   - Expected: Request details in logs

**Dependencies:** Task 1.3 completed
**Blocking:** None

### Task 1.5: Development Tools Setup
**Estimated Time:** 5-6 hours total

#### Subtasks:
1. **1.5.1 Install PHPUnit** (30 min)
   - Add testing framework
   - Test: `vendor/bin/phpunit --version`
   - Command: `composer require --dev phpunit/phpunit`
   - Expected: PHPUnit version displayed

2. **1.5.2 Configure PHPUnit** (45 min)
   - Create phpunit.xml
   - Test: `vendor/bin/phpunit --configuration phpunit.xml`
   - Expected: "No tests executed"

3. **1.5.3 Create test directory structure** (30 min)
   - Unit and integration test folders
   - Test: `ls -la tests/unit tests/integration`
   - Expected: Directories exist

4. **1.5.4 Write first unit test** (1 hour)
   - Basic test case
   - Test: `vendor/bin/phpunit tests/unit/ExampleTest.php`
   - Expected: "OK (1 test, 1 assertion)"

5. **1.5.5 Install PHP CodeSniffer** (30 min)
   - Code style checking
   - Test: `vendor/bin/phpcs --version`
   - Command: `composer require --dev squizlabs/php_codesniffer`
   - Expected: PHPCS version displayed

6. **1.5.6 Configure coding standards** (45 min)
   - PSR-12 configuration
   - Test: `vendor/bin/phpcs --standard=PSR12 app/`
   - Expected: Shows any style violations

7. **1.5.7 Create composer scripts** (45 min)
   - Shortcuts for common tasks
   - Test: `composer test`
   - Expected: Runs test suite

8. **1.5.8 Set up autoloading** (1 hour)
   - PSR-4 autoload configuration
   - Test: Class autoloading works
   - Expected: Classes load without require

**Dependencies:** Task 1.1 completed
**Blocking:** All testing tasks

## Week 2: Configuration Engine Core

### Task 2.1: Configuration Database Schema
**Estimated Time:** 6-8 hours total

#### Subtasks:
1. **2.1.1 Design config_categories table** (1 hour)
   - Category hierarchy structure
   - Test: Migration creates table
   - Expected: Table with proper indexes

2. **2.1.2 Create config_items table** (1.5 hours)
   - Configuration item definitions
   - Test: Check table structure
   - Expected: All required columns present

3. **2.1.3 Design config_values table** (1.5 hours)
   - Polymorphic value storage
   - Test: Insert different value types
   - Expected: Values stored correctly

4. **2.1.4 Create config_history table** (1 hour)
   - Audit trail for changes
   - Test: Trigger saves history
   - Expected: Changes logged

5. **2.1.5 Add foreign key constraints** (45 min)
   - Referential integrity
   - Test: Try invalid insert
   - Expected: Constraint violation error

6. **2.1.6 Create indexes** (45 min)
   - Performance optimization
   - Test: `SHOW INDEXES FROM config_items`
   - Expected: Key indexes present

7. **2.1.7 Seed initial configuration** (1 hour)
   - Default configuration data
   - Test: `SELECT COUNT(*) FROM config_items`
   - Expected: 20+ configuration items

8. **2.1.8 Test schema integrity** (30 min)
   - Verify all relationships
   - Test: Run integrity check script
   - Expected: No orphaned records

**Dependencies:** Task 1.2 completed
**Blocking:** Task 2.2

### Task 2.2: Configuration Service Layer
**Estimated Time:** 10-12 hours total

#### Subtasks:
1. **2.2.1 Create Configuration model** (1.5 hours)
   - Entity representation
   - Test: `php -l app/models/Configuration.php`
   - Expected: "No syntax errors detected"

2. **2.2.2 Implement ConfigurationService** (2 hours)
   - CRUD operations
   - Test: Unit tests pass
   - Expected: All CRUD methods work

3. **2.2.3 Add value type handling** (1.5 hours)
   - Type casting and validation
   - Test: Different types stored/retrieved
   - Expected: Types preserved correctly

4. **2.2.4 Create configuration cache** (1.5 hours)
   - Memory and file caching
   - Test: Cache hit/miss tracking
   - Expected: Performance improvement

5. **2.2.5 Implement cache invalidation** (1 hour)
   - Smart cache clearing
   - Test: Update clears cache
   - Expected: Fresh data after update

6. **2.2.6 Build configuration loader** (1.5 hours)
   - Lazy loading system
   - Test: Memory usage tracking
   - Expected: Efficient memory use

7. **2.2.7 Add validation rules** (1.5 hours)
   - Configuration constraints
   - Test: Invalid values rejected
   - Expected: Validation errors returned

8. **2.2.8 Create helper functions** (1 hour)
   - config() helper
   - Test: `config('app.name')`
   - Expected: Returns configuration value

9. **2.2.9 Write service tests** (1 hour)
   - Comprehensive test suite
   - Test: `vendor/bin/phpunit tests/unit/ConfigurationServiceTest.php`
   - Expected: All tests pass

**Dependencies:** Task 2.1 completed
**Blocking:** Task 2.3

### Task 2.3: Configuration Admin UI
**Estimated Time:** 8-10 hours total

#### Subtasks:
1. **2.3.1 Create admin layout template** (1.5 hours)
   - AdminLTE integration
   - Test: View renders correctly
   - Expected: AdminLTE theme applied

2. **2.3.2 Build configuration list view** (1.5 hours)
   - Category tree display
   - Test: Navigate to /admin/config
   - Expected: Configuration items listed

3. **2.3.3 Implement search/filter** (1 hour)
   - Real-time filtering
   - Test: Type in search box
   - Expected: Results filter instantly

4. **2.3.4 Create edit forms** (2 hours)
   - Dynamic form generation
   - Test: Edit different config types
   - Expected: Appropriate input types

5. **2.3.5 Add AJAX save** (1.5 hours)
   - Async configuration updates
   - Test: Save without page reload
   - Expected: Success notification

6. **2.3.6 Implement validation UI** (1 hour)
   - Client-side validation
   - Test: Submit invalid data
   - Expected: Validation errors shown

7. **2.3.7 Add change history view** (1 hour)
   - Audit trail display
   - Test: View configuration history
   - Expected: Changes listed with details

8. **2.3.8 Create import/export** (1.5 hours)
   - Configuration backup
   - Test: Export and reimport
   - Expected: Configuration preserved

**Dependencies:** Task 2.2 completed
**Blocking:** None

## Week 3: Authentication & User Management

### Task 3.1: Authentication System Core
**Estimated Time:** 8-10 hours total

#### Subtasks:
1. **3.1.1 Install JWT library** (30 min)
   - Firebase JWT package
   - Test: `composer show firebase/php-jwt`
   - Command: `composer require firebase/php-jwt`
   - Expected: Package installed

2. **3.1.2 Create JWT service** (2 hours)
   - Token generation/validation
   - Test: Generate and verify token
   - Expected: Valid tokens created

3. **3.1.3 Implement session fallback** (1.5 hours)
   - Cookie-based sessions
   - Test: Session persistence
   - Expected: Sessions maintained

4. **3.1.4 Build AuthService** (2 hours)
   - Authentication logic
   - Test: Login/logout flow
   - Expected: Auth state managed

5. **3.1.5 Create password hashing** (1 hour)
   - Bcrypt implementation
   - Test: Hash and verify
   - Expected: Passwords secure

6. **3.1.6 Add remember me** (1 hour)
   - Persistent login
   - Test: Close browser and return
   - Expected: Still logged in

7. **3.1.7 Implement rate limiting** (1 hour)
   - Brute force protection
   - Test: Multiple failed attempts
   - Expected: Account locked temporarily

8. **3.1.8 Create auth middleware** (1 hour)
   - Route protection
   - Test: Access protected route
   - Expected: Redirect to login

**Dependencies:** Task 1.3 completed
**Blocking:** Task 3.2, 3.3

### Task 3.2: User & Group Management
**Estimated Time:** 8-10 hours total

#### Subtasks:
1. **3.2.1 Create users table migration** (1 hour)
   - User schema design
   - Test: Migration runs successfully
   - Expected: Users table created

2. **3.2.2 Design groups schema** (1 hour)
   - Groups and membership
   - Test: Check table relationships
   - Expected: Proper foreign keys

3. **3.2.3 Build User model** (1.5 hours)
   - User entity class
   - Test: CRUD operations
   - Expected: User management works

4. **3.2.4 Create Group model** (1 hour)
   - Group entity class
   - Test: Group operations
   - Expected: Groups manageable

5. **3.2.5 Implement user-group relations** (1.5 hours)
   - Many-to-many handling
   - Test: Assign users to groups
   - Expected: Relationships work

6. **3.2.6 Add user preferences** (1 hour)
   - Personal settings storage
   - Test: Save/load preferences
   - Expected: Preferences persist

7. **3.2.7 Create UserService** (1.5 hours)
   - Business logic layer
   - Test: Service methods
   - Expected: All operations work

8. **3.2.8 Build profile management** (1 hour)
   - User profile updates
   - Test: Update profile data
   - Expected: Changes saved

**Dependencies:** Task 3.1 completed
**Blocking:** Task 3.3

### Task 3.3: Permission System
**Estimated Time:** 10-12 hours total

#### Subtasks:
1. **3.3.1 Design permission schema** (1.5 hours)
   - Flexible permission storage
   - Test: Schema supports complex permissions
   - Expected: JSON permissions work

2. **3.3.2 Create permission models** (1.5 hours)
   - Permission entities
   - Test: Model operations
   - Expected: CRUD works

3. **3.3.3 Build permission service** (2 hours)
   - Permission checking logic
   - Test: Check various permissions
   - Expected: Accurate results

4. **3.3.4 Implement role inheritance** (1.5 hours)
   - Hierarchical roles
   - Test: Child inherits parent permissions
   - Expected: Inheritance works

5. **3.3.5 Create permission middleware** (1.5 hours)
   - Route-level checks
   - Test: Access with/without permission
   - Expected: Proper access control

6. **3.3.6 Add dynamic permissions** (1.5 hours)
   - Runtime permission creation
   - Test: Create new permissions
   - Expected: Immediately usable

7. **3.3.7 Build permission UI** (2 hours)
   - Permission management interface
   - Test: Assign/revoke permissions
   - Expected: UI updates permissions

8. **3.3.8 Create permission helpers** (1 hour)
   - can() and similar helpers
   - Test: Use in views/controllers
   - Expected: Easy permission checks

**Dependencies:** Task 3.2 completed
**Blocking:** All subsequent features

### Task 3.4: Login/Logout UI
**Estimated Time:** 6-8 hours total

#### Subtasks:
1. **3.4.1 Create login template** (1.5 hours)
   - AdminLTE login page
   - Test: Navigate to /login
   - Expected: Styled login form

2. **3.4.2 Implement login controller** (1.5 hours)
   - Handle login requests
   - Test: Submit login form
   - Expected: Successful login

3. **3.4.3 Add form validation** (1 hour)
   - Client and server validation
   - Test: Submit invalid data
   - Expected: Validation errors

4. **3.4.4 Create logout functionality** (30 min)
   - Session termination
   - Test: Click logout
   - Expected: Redirected to login

5. **3.4.5 Build password reset** (1.5 hours)
   - Reset token system
   - Test: Request password reset
   - Expected: Reset email sent

6. **3.4.6 Add two-factor auth** (1.5 hours)
   - Optional 2FA
   - Test: Enable and use 2FA
   - Expected: Additional verification

7. **3.4.7 Create session timeout** (1 hour)
   - Auto-logout on inactivity
   - Test: Wait for timeout
   - Expected: Session expires

**Dependencies:** Task 3.3 completed
**Blocking:** None

## Testing Strategy for Phase 1

### Integration Points to Test:
1. **Database Connectivity**
   - Test: `php scripts/test-db-connection.php`
   - Expected: "Connection successful"

2. **Route Registration**
   - Test: `php scripts/list-routes.php`
   - Expected: All routes listed

3. **Authentication Flow**
   - Test: Complete login/logout cycle
   - Expected: Session managed correctly

4. **Configuration Loading**
   - Test: `php scripts/test-config.php`
   - Expected: Configuration values loaded

5. **Permission Checking**
   - Test: Various permission scenarios
   - Expected: Access control works

### Performance Benchmarks:
1. **Page Load Time**
   - Target: < 200ms for admin pages
   - Test: `ab -n 100 -c 10 http://localhost{{ base_url }}/admin`

2. **Database Queries**
   - Target: < 50ms for config loads
   - Test: Enable query logging and monitor

3. **Authentication Speed**
   - Target: < 100ms for login
   - Test: Time login requests

## Dependency Chain Summary

```
1.1 Project Setup
    └─> 1.2 Database Setup
            └─> 1.3 Routing & Middleware
                    └─> 1.4 Error Handling
                    └─> 2.1 Config Schema
                            └─> 2.2 Config Service
                                    └─> 2.3 Config UI
                    └─> 3.1 Auth Core
                            └─> 3.2 User Management
                                    └─> 3.3 Permissions
                                            └─> 3.4 Login UI

1.5 Dev Tools (parallel to other tasks)
```

## Critical Path
The critical path for Phase 1 completion:
1.1 -> 1.2 -> 1.3 -> 3.1 -> 3.2 -> 3.3 -> 3.4

This path must be completed for Phase 1 to be considered done. Other tasks can be worked on in parallel but these form the backbone of the system.