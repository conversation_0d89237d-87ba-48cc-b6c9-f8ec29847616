<?php
/**
 * Check invoice_items table structure
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Get table structure
$stmt = $db->query("DESCRIBE invoice_items");
$columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h1>Invoice Items Table Structure</h1>";
echo "<pre>";
print_r($columns);
echo "</pre>";

// Show column names only
echo "<h2>Column Names:</h2>";
echo "<ul>";
foreach ($columns as $col) {
    echo "<li>" . $col['Field'] . " (" . $col['Type'] . ")</li>";
}
echo "</ul>";