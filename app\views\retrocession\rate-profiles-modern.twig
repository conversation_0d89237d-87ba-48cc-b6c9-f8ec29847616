{% extends "base-modern.twig" %}

{% block title %}{{ __('retrocession.rate_profiles') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ base_url }}">{{ __('common.home') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ base_url }}/retrocession">{{ __('retrocession.title') }}</a></li>
                    <li class="breadcrumb-item active">{{ __('retrocession.rate_profiles') }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800 mt-2">{{ __('retrocession.rate_profiles') }}</h1>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/retrocession" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <a href="{{ base_url }}/retrocession/rate-profiles/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('retrocession.create_profile') }}
            </a>
        </div>
    </div>

    <!-- Rate Profiles List -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0">{{ __('retrocession.rate_profiles') }}</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>{{ __('retrocession.profile') }}</th>
                            <th>{{ __('common.code') }}</th>
                            <th>{{ __('common.type') }}</th>
                            <th>{{ __('retrocession.rates') }}</th>
                            <th>{{ __('retrocession.practitioners') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th class="text-end">{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for profile in profiles %}
                        <tr>
                            <td>
                                <div>
                                    <div class="fw-semibold">{{ profile.name }}</div>
                                    {% if profile.description %}
                                        <small class="text-muted">{{ profile.description }}</small>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <code>{{ profile.code }}</code>
                                {% if profile.is_default %}
                                    <span class="badge bg-primary ms-1">{{ __('common.default') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-info">
                                    {% if profile.profile_type == 'retrocession' %}
                                        {{ __('retrocession.type_retrocession') }}
                                    {% elseif profile.profile_type == 'hourly' %}
                                        {{ __('retrocession.type_hourly') }}
                                    {% elseif profile.profile_type == 'rental' %}
                                        {{ __('retrocession.type_rental') }}
                                    {% elseif profile.profile_type == 'mixed' %}
                                        {{ __('retrocession.type_mixed') }}
                                    {% endif %}
                                </span>
                            </td>
                            <td>
                                <small>
                                    {% if profile.current_cns_rate is defined %}
                                        {{ __('retrocession.cns') }}: {{ profile.current_cns_rate }} %<br>
                                    {% endif %}
                                    {% if profile.current_patient_rate is defined %}
                                        {{ __('retrocession.patient') }}: {{ profile.current_patient_rate }} %<br>
                                    {% endif %}
                                    {% if profile.current_secretariat_rate is defined %}
                                        {{ __('retrocession.secretariat') }}: {{ profile.current_secretariat_rate }} %
                                    {% endif %}
                                    {% if profile.hourly_rate is defined %}
                                        {{ __('retrocession.hourly') }}: € {{ profile.hourly_rate|number_format(2) }}
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ profile.practitioner_count|default(0) }} {{ __('common.active') }}</span>
                            </td>
                            <td>
                                {% if profile.is_active %}
                                    <span class="badge bg-success">
                                        <i class="bi bi-check-circle me-1"></i>{{ __('common.active') }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="bi bi-x-circle me-1"></i>{{ __('common.inactive') }}
                                    </span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                <div class="btn-group">
                                    <a href="{{ base_url }}/retrocession/rate-profiles/{{ profile.id }}/edit" 
                                       class="btn btn-sm btn-outline-primary" title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            data-bs-toggle="modal" data-bs-target="#assignModal" 
                                            onclick="setAssignProfile({{ profile.id }}, '{{ profile.name|e('js') }}')"
                                            title="{{ __('retrocession.assign_profile') }}">
                                        <i class="bi bi-person-plus"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="text-center py-5 text-muted">
                                <i class="bi bi-folder2-open fs-1 mb-3 d-block"></i>
                                <p>{{ __('retrocession.no_rate_profiles') }}</p>
                                <a href="{{ base_url }}/retrocession/rate-profiles/create" class="btn btn-primary">
                                    <i class="bi bi-plus-circle me-2"></i>{{ __('retrocession.create_profile') }}
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Assign Profile Modal -->
<div class="modal fade" id="assignModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('retrocession.assign_rate_profile') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="assignForm" method="post" action="{{ base_url }}/retrocession/rate-profiles/assign">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="profile_id" id="assignProfileId">
                
                <div class="modal-body">
                    <p>{{ __('retrocession.assign_profile_to') }} <strong id="assignProfileName"></strong>:</p>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('retrocession.practitioner') }}</label>
                        <select name="practitioner_id" class="form-select" required>
                            <option value="">{{ __('retrocession.select_practitioner') }}</option>
                            {% for practitioner in practitioners|default([]) %}
                                <option value="{{ practitioner.id }}">
                                    {{ practitioner.name }} ({{ practitioner.client_number }})
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('retrocession.valid_from') }}</label>
                        <input type="date" name="assigned_from" class="form-control" value="{{ "now"|date('Y-m-d') }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('retrocession.valid_to') }} ({{ __('common.optional') }})</label>
                        <input type="date" name="assigned_to" class="form-control">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">{{ __('common.notes') }}</label>
                        <textarea name="notes" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>{{ __('retrocession.assign_profile') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function setAssignProfile(profileId, profileName) {
    document.getElementById('assignProfileId').value = profileId;
    document.getElementById('assignProfileName').textContent = profileName;
}

// Handle form submission
document.addEventListener('DOMContentLoaded', function() {
    const assignForm = document.getElementById('assignForm');
    if (assignForm) {
        assignForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            
            // Disable button
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}...';
            
            // Use XMLHttpRequest
            const xhr = new XMLHttpRequest();
            xhr.open('POST', this.action, true);
            xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
            
            xhr.onload = function() {
                try {
                    const data = JSON.parse(xhr.responseText);
                    
                    if (data.success) {
                        // Close modal and reload page
                        bootstrap.Modal.getInstance(document.getElementById('assignModal')).hide();
                        
                        if (typeof toastr !== 'undefined') {
                            toastr.success(data.message || '{{ __("retrocession.profile_assigned") }}');
                        }
                        
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);
                    } else {
                        if (typeof toastr !== 'undefined') {
                            toastr.error(data.message || '{{ __("common.error_occurred") }}');
                        }
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    }
                } catch (e) {
                    console.error('Parse error:', e);
                    toastr.error('{{ __("common.error_occurred") }}');
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            };
            
            xhr.onerror = function() {
                console.error('XHR Error');
                toastr.error('{{ __("common.error_occurred") }}');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            };
            
            xhr.send(formData);
        });
    }
});
</script>
{% endblock %}