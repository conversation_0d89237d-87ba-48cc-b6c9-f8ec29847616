<?php
/**
 * Add exclude patient line checkbox to invoice creation form in the proper location
 */

echo "<pre>";
echo "=== Adding Exclude Patient Line Checkbox to Invoice Form ===\n\n";

$createFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';

// Check if file exists
if (!file_exists($createFile)) {
    echo "❌ File not found: $createFile\n";
    echo "</pre>";
    exit;
}

$content = file_get_contents($createFile);

// Create backup
$backupFile = $createFile . '.backup.' . date('YmdHis');
file_put_contents($backupFile, $content);
echo "✓ Created backup: " . basename($backupFile) . "\n\n";

// Find the location after billable selection and before User/Client Information Display
$searchPattern = '                </div>
            </div>
        </div>

        <!-- User/Client Information Display -->';

$insertPos = strpos($content, $searchPattern);

if ($insertPos !== false) {
    // Insert the checkbox HTML before the User/Client Information Display
    $insertHtml = '
                
                <!-- Exclude Patient Line (for retrocession invoices only) -->
                <div class="row g-3 mt-3" id="exclude_patient_line_container" style="display: none;">
                    <div class="col-md-12">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="exclude_patient_line" 
                                   name="exclude_patient_line" value="1">
                            <label class="form-check-label" for="exclude_patient_line">
                                {{ __("retrocession.exclude_patient_line")|default("Exclure la ligne patient") }}
                                <small class="text-muted d-block">{{ __("retrocession.exclude_patient_line_help")|default("Si coché, la facture ne contiendra pas la ligne RÉTROCESSION PATIENTS") }}</small>
                            </label>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- User/Client Information Display -->';
    
    // Replace the pattern with our new HTML
    $content = str_replace($searchPattern, $insertHtml, $content);
    
    echo "✓ Added exclude patient line checkbox HTML\n";
    
    // Save the updated content
    file_put_contents($createFile, $content);
    
    echo "\n✅ Checkbox added successfully!\n\n";
    echo "The checkbox will now appear:\n";
    echo "1. In the Invoice Details section\n";
    echo "2. Below the 'Bill To' selection\n";
    echo "3. Only when you select a retrocession invoice type\n";
    echo "4. When checked, it will exclude the patient line from the invoice\n";
    
    echo "\nTo test with Rémi Heine:\n";
    echo "1. Go to: http://localhost/fit/public/invoices/create?type=retrocession_25\n";
    echo "2. Select 'Rémi Heine' as the user\n";
    echo "3. The checkbox 'Exclure la ligne patient' should appear\n";
    echo "4. Check it to exclude the 'RÉTROCESSION PATIENTS 20%' line\n";
} else {
    echo "❌ Could not find the insertion point\n";
    echo "Let me check the current structure...\n";
    
    // Try to find a different pattern
    $altPattern = '</div>
                    </div>
                </div>
            </div>
        </div>';
    
    $positions = [];
    $offset = 0;
    while (($pos = strpos($content, $altPattern, $offset)) !== false) {
        $positions[] = $pos;
        $offset = $pos + 1;
    }
    
    echo "Found " . count($positions) . " occurrences of the closing pattern\n";
}

echo "</pre>";