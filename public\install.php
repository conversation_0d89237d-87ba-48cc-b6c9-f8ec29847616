<?php
/**
 * Installation Script
 */

// Check if already installed
if (file_exists(__DIR__ . '/../storage/installed.lock')) {
    die('System is already installed. Delete storage/installed.lock to reinstall.');
}

$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Test database connection
    try {
        $pdo = new PDO(
            "mysql:host={$_POST['db_host']};dbname={$_POST['db_name']};charset=utf8mb4",
            $_POST['db_user'],
            $_POST['db_pass']
        );
        
        // Create first admin user
        $password = password_hash($_POST['admin_pass'], PASSWORD_DEFAULT);
        
        $stmt = $pdo->prepare("INSERT INTO users (email, password, first_name, last_name) VALUES (?, ?, ?, ?)");
        $stmt->execute([$_POST['admin_email'], $password, 'Admin', 'User']);
        $userId = $pdo->lastInsertId();
        
        // Assign to admin group
        $stmt = $pdo->prepare("INSERT INTO user_group_assignments (user_id, group_id) VALUES (?, 1)");
        $stmt->execute([$userId]);
        
        // Create lock file
        file_put_contents(__DIR__ . '/../storage/installed.lock', date('Y-m-d H:i:s'));
        
        $success = true;
    } catch (Exception $e) {
        $errors[] = 'Database error: ' . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Installation - Fit360 AdminDesk</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/css/adminlte.min.css">
</head>
<body class="hold-transition login-page">
    <div class="login-box" style="width: 500px;">
        <div class="card card-outline card-primary">
            <div class="card-header text-center">
                <h1><b>Fit360</b> AdminDesk</h1>
                <p>Installation</p>
            </div>
            <div class="card-body">
                <?php if ($success): ?>
                    <div class="alert alert-success">
                        Installation completed successfully!
                        <br><br>
                        <a href="{{ base_url }}/" class="btn btn-primary btn-block">Go to Application</a>
                    </div>
                <?php else: ?>
                    <?php if (!empty($errors)): ?>
                        <div class="alert alert-danger">
                            <?php foreach ($errors as $error): ?>
                                <?php echo htmlspecialchars($error); ?><br>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST">
                        <h4>Database Configuration</h4>
                        <div class="form-group">
                            <label>Database Host</label>
                            <input type="text" name="db_host" class="form-control" value="localhost" required>
                        </div>
                        <div class="form-group">
                            <label>Database Name</label>
                            <input type="text" name="db_name" class="form-control" value="fitapp" required>
                        </div>
                        <div class="form-group">
                            <label>Database Username</label>
                            <input type="text" name="db_user" class="form-control" value="root" required>
                        </div>
                        <div class="form-group">
                            <label>Database Password</label>
                            <input type="password" name="db_pass" class="form-control">
                        </div>
                        
                        <hr>
                        
                        <h4>Admin Account</h4>
                        <div class="form-group">
                            <label>Admin Email</label>
                            <input type="email" name="admin_email" class="form-control" required>
                        </div>
                        <div class="form-group">
                            <label>Admin Password</label>
                            <input type="password" name="admin_pass" class="form-control" required minlength="8">
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-block">Install</button>
                    </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>