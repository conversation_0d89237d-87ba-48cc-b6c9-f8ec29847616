<?php
// Check config table structure

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Config Table Structure</h2>";
    
    // Show columns
    $stmt = $db->query("SHOW COLUMNS FROM config");
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($col = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . $col['Field'] . "</td>";
        echo "<td>" . $col['Type'] . "</td>";
        echo "<td>" . $col['Null'] . "</td>";
        echo "<td>" . $col['Key'] . "</td>";
        echo "<td>" . ($col['Default'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show sample data
    echo "<h3>Sample Config Data</h3>";
    $stmt = $db->query("SELECT * FROM config LIMIT 5");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($rows) {
        echo "<pre>" . print_r($rows, true) . "</pre>";
    }
    
    // Now add the config properly
    echo "<h3>Adding Invoice Number Reuse Config</h3>";
    
    // Try to figure out the correct column names
    $stmt = $db->query("SHOW COLUMNS FROM config");
    $columns = [];
    while ($col = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $col['Field'];
    }
    
    // Common patterns for key/value columns
    $keyColumn = null;
    $valueColumn = null;
    
    foreach ($columns as $col) {
        if (in_array(strtolower($col), ['key', 'config_key', 'name', 'setting', 'param'])) {
            $keyColumn = $col;
        }
        if (in_array(strtolower($col), ['value', 'config_value', 'setting_value', 'val'])) {
            $valueColumn = $col;
        }
    }
    
    if ($keyColumn && $valueColumn) {
        echo "Found columns: $keyColumn (key), $valueColumn (value)<br>";
        
        // Check if already exists
        $stmt = $db->prepare("SELECT * FROM config WHERE $keyColumn = ?");
        $stmt->execute(['reuse_deleted_invoice_numbers']);
        
        if ($stmt->fetch()) {
            // Update
            $stmt = $db->prepare("UPDATE config SET $valueColumn = 'true' WHERE $keyColumn = ?");
            $stmt->execute(['reuse_deleted_invoice_numbers']);
            echo "✓ Updated existing config<br>";
        } else {
            // Insert
            $stmt = $db->prepare("INSERT INTO config ($keyColumn, $valueColumn) VALUES (?, ?)");
            $stmt->execute(['reuse_deleted_invoice_numbers', 'true']);
            echo "✓ Added new config<br>";
        }
    } else {
        echo "Could not determine config table column names<br>";
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>