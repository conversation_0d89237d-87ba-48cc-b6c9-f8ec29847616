<?php

namespace App\Models;

use Flight;
use PDO;
use Exception;

class Invoice extends \App\Core\Model
{
    protected $table = 'invoices';
    
    // Invoice types
    const TYPE_RENTAL = 'rental';
    const TYPE_HOURLY = 'hourly';
    const TYPE_RETROCESSION_30 = 'retrocession_30';
    const TYPE_RETROCESSION_25 = 'retrocession_25';
    const TYPE_SERVICE = 'service';
    
    // Invoice statuses
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_PAID = 'paid';
    const STATUS_PARTIAL = 'partial';
    const STATUS_OVERDUE = 'overdue';
    const STATUS_CANCELLED = 'cancelled';
    
    /**
     * Generate document number based on document type
     */
    public function generateDocumentNumber($documentTypeId)
    {
        $db = Flight::db();
        
        // Get document type configuration
        $stmt = $db->prepare("
            SELECT id, code, prefix, counter_type, current_number, current_year, current_month 
            FROM document_types 
            WHERE id = ?
        ");
        $stmt->execute([$documentTypeId]);
        $docType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$docType) {
            throw new Exception("Document type not found");
        }
        
        // Determine sequence parameters based on counter type
        $year = date('Y');
        $month = date('m');
        $sequenceYear = null;
        $sequenceMonth = null;
        
        switch ($docType['counter_type']) {
            case 'yearly':
                $sequenceYear = $year;
                break;
            case 'monthly':
                $sequenceYear = $year;
                $sequenceMonth = $month;
                break;
            // 'global' doesn't use year/month
        }
        
        // Get or create sequence
        $stmt = $db->prepare("
            SELECT last_number 
            FROM document_sequences 
            WHERE document_type_id = ? 
              AND (year = ? OR year IS NULL)
              AND (month = ? OR month IS NULL)
            FOR UPDATE
        ");
        $stmt->execute([$documentTypeId, $sequenceYear, $sequenceMonth]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($sequence) {
            $nextNumber = $sequence['last_number'] + 1;
            // Update sequence
            $stmt = $db->prepare("
                UPDATE document_sequences 
                SET last_number = ?, updated_at = NOW() 
                WHERE document_type_id = ?
                  AND (year = ? OR year IS NULL)
                  AND (month = ? OR month IS NULL)
            ");
            $stmt->execute([$nextNumber, $documentTypeId, $sequenceYear, $sequenceMonth]);
        } else {
            $nextNumber = 1;
            // Create new sequence
            $stmt = $db->prepare("
                INSERT INTO document_sequences (document_type_id, year, month, last_number) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$documentTypeId, $sequenceYear, $sequenceMonth, $nextNumber]);
        }
        
        // Format the document number
        $numberParts = [$docType['prefix']];
        
        if ($docType['counter_type'] === 'yearly' || $docType['counter_type'] === 'monthly') {
            $numberParts[] = $year;
        }
        
        if ($docType['counter_type'] === 'monthly') {
            $numberParts[] = $month;
        }
        
        $numberParts[] = str_pad($nextNumber, 5, '0', STR_PAD_LEFT);
        
        return implode('-', $numberParts);
    }
    
    /**
     * Format invoice number based on pattern
     */
    private function formatInvoiceNumber($pattern, $values)
    {
        $formatted = $pattern;
        
        // Replace placeholders
        foreach ($values as $key => $value) {
            // Handle padded numbers like {NUMBER:5}
            if (preg_match('/\{' . $key . ':(\d+)\}/', $pattern, $matches)) {
                $padLength = $matches[1];
                $paddedValue = str_pad($value, $padLength, '0', STR_PAD_LEFT);
                $formatted = str_replace($matches[0], $paddedValue, $formatted);
            } else {
                // Simple replacement
                $formatted = str_replace('{' . $key . '}', $value, $formatted);
            }
        }
        
        return $formatted;
    }
    
    /**
     * Create invoice with all Phase 3 features
     */
    public function createInvoice($data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get document type if not provided
            if (empty($data['document_type_id'])) {
                // Default to invoice type
                $stmt = $db->prepare("SELECT id FROM document_types WHERE code = 'invoice' LIMIT 1");
                $stmt->execute();
                $defaultType = $stmt->fetch(PDO::FETCH_ASSOC);
                $data['document_type_id'] = $defaultType['id'];
            }
            
            // Generate document number if not provided
            if (empty($data['invoice_number'])) {
                $data['invoice_number'] = $this->generateDocumentNumber($data['document_type_id']);
            }
            
            // Set draft period if not specified
            if ($data['status'] === self::STATUS_DRAFT && empty($data['draft_until'])) {
                $data['draft_until'] = date('Y-m-d H:i:s', strtotime('+48 hours'));
            }
            
            // Calculate totals if not provided
            if (empty($data['total'])) {
                $data['total'] = $data['subtotal'] + $data['vat_amount'];
            }
            
            // Apply staff limit if applicable
            $staffLimitApplied = $this->applyStaffLimit($data);
            
            // Insert invoice
            $stmt = $db->prepare("
                INSERT INTO invoices (
                    invoice_number, document_type_id, type_id, invoice_type, template_id, profile_id,
                    client_id, user_id, status, issue_date, due_date, 
                    subtotal, vat_amount, secretariat_vat_amount, total,
                    original_amount, staff_limit_applied, staff_limit_amount,
                    secretariat_vat_note_shown, cns_reference, draft_until,
                    currency, notes, internal_notes, payment_terms,
                    reference_document_id, reference_document_number, credit_reason, created_by
                ) VALUES (
                    :invoice_number, :document_type_id, :type_id, :invoice_type, :template_id, :profile_id,
                    :client_id, :user_id, :status, :issue_date, :due_date,
                    :subtotal, :vat_amount, :secretariat_vat_amount, :total,
                    :original_amount, :staff_limit_applied, :staff_limit_amount,
                    :secretariat_vat_note_shown, :cns_reference, :draft_until,
                    :currency, :notes, :internal_notes, :payment_terms,
                    :reference_document_id, :reference_document_number, :credit_reason, :created_by
                )
            ");
            
            $stmt->execute([
                ':invoice_number' => $data['invoice_number'],
                ':document_type_id' => $data['document_type_id'],
                ':type_id' => $data['type_id'] ?? 1,
                ':invoice_type' => $data['invoice_type'] ?? null,
                ':template_id' => $data['template_id'] ?? null,
                ':profile_id' => $data['profile_id'] ?? null,
                ':client_id' => $data['client_id'] ?? null,
                ':user_id' => $data['user_id'] ?? null,
                ':status' => $data['status'] ?? self::STATUS_DRAFT,
                ':issue_date' => $data['issue_date'],
                ':due_date' => $data['due_date'],
                ':subtotal' => $data['subtotal'] ?? 0,
                ':vat_amount' => $data['vat_amount'] ?? 0,
                ':secretariat_vat_amount' => $data['secretariat_vat_amount'] ?? 0,
                ':total' => $data['total'],
                ':original_amount' => $data['original_amount'] ?? null,
                ':staff_limit_applied' => $data['staff_limit_applied'] ?? false,
                ':staff_limit_amount' => $data['staff_limit_amount'] ?? null,
                ':secretariat_vat_note_shown' => $data['secretariat_vat_note_shown'] ?? false,
                ':cns_reference' => $data['cns_reference'] ?? null,
                ':draft_until' => $data['draft_until'] ?? null,
                ':currency' => $data['currency'] ?? 'EUR',
                ':notes' => $data['notes'] ?? null,
                ':internal_notes' => $data['internal_notes'] ?? null,
                ':payment_terms' => $data['payment_terms'] ?? 'Dès réception',
                ':reference_document_id' => $data['reference_document_id'] ?? null,
                ':reference_document_number' => $data['reference_document_number'] ?? null,
                ':credit_reason' => $data['credit_reason'] ?? null,
                ':created_by' => $data['created_by'] ?? $_SESSION['user_id'] ?? 1
            ]);
            
            $invoiceId = $db->lastInsertId();
            
            // Add invoice lines if provided
            if (!empty($data['lines'])) {
                $this->addInvoiceLines($invoiceId, $data['lines']);
            }
            
            // Create retrocession record if this is a retrocession invoice
            if (in_array($data['invoice_type'], [self::TYPE_RETROCESSION_30, self::TYPE_RETROCESSION_25])) {
                $this->createRetrocessionRecord($invoiceId, $data);
            }
            
            $db->commit();
            
            return $this->getById($invoiceId);
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    
    /**
     * Create retrocession record
     */
    private function createRetrocessionRecord($invoiceId, $data)
    {
        $db = Flight::db();
        
        // Calculate VAT amount (formula: Amount - (Amount / 1.17))
        $secretariatTvac = $data['secretariat_tvac'] ?? 0;
        $vatAmount = $secretariatTvac - ($secretariatTvac / 1.17);
        $secretariatHtva = $secretariatTvac - $vatAmount;
        
        $stmt = $db->prepare("
            INSERT INTO invoice_retrocessions (
                invoice_id, total_amount, cns_amount, patient_amount,
                cns_percentage, patient_percentage, secretariat_percentage,
                secretariat_tvac, secretariat_htva, vat_amount,
                has_overrides, override_notes
            ) VALUES (
                :invoice_id, :total_amount, :cns_amount, :patient_amount,
                :cns_percentage, :patient_percentage, :secretariat_percentage,
                :secretariat_tvac, :secretariat_htva, :vat_amount,
                :has_overrides, :override_notes
            )
        ");
        
        $stmt->execute([
            ':invoice_id' => $invoiceId,
            ':total_amount' => $data['retro_total_amount'] ?? 0,
            ':cns_amount' => $data['retro_cns_amount'] ?? 0,
            ':patient_amount' => $data['retro_patient_amount'] ?? 0,
            ':cns_percentage' => $data['retro_cns_percentage'] ?? 20,
            ':patient_percentage' => $data['retro_patient_percentage'] ?? 20,
            ':secretariat_percentage' => $data['retro_secretariat_percentage'] ?? 10,
            ':secretariat_tvac' => $secretariatTvac,
            ':secretariat_htva' => $secretariatHtva,
            ':vat_amount' => $vatAmount,
            ':has_overrides' => $data['retro_has_overrides'] ?? false,
            ':override_notes' => $data['retro_override_notes'] ?? null
        ]);
    }
    
    /**
     * Get invoice by ID with caching
     */
    public function getById($id)
    {
        $cache = Flight::cache();
        $cacheKey = "invoice_{$id}";
        
        // Try to get from cache first
        $invoice = $cache->get($cacheKey);
        if ($invoice !== null) {
            return $invoice;
        }
        
        // Fetch from database
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
        $stmt->execute([$id]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Cache for 1 hour
        if ($invoice) {
            $cache->set($cacheKey, $invoice, 3600);
        }
        
        return $invoice;
    }
    
    /**
     * Apply staff invoice limit if applicable
     */
    private function applyStaffLimit(&$data)
    {
        $db = Flight::db();
        
        // Check if staff limit is enabled
        $stmt = $db->query("SELECT value FROM config WHERE `key` = 'staff_invoice_limit_enabled'");
        $limitEnabled = $stmt->fetchColumn();
        
        if ($limitEnabled != '1') {
            return false;
        }
        
        // Check if client is a staff member
        $stmt = $db->prepare("SELECT is_staff_member, staff_limit_override FROM clients WHERE id = ?");
        $stmt->execute([$data['client_id']]);
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$client || !$client['is_staff_member']) {
            return false;
        }
        
        // Get the limit configuration
        $config = [];
        $stmt = $db->query("SELECT `key`, value FROM config WHERE `key` LIKE 'staff_invoice_limit_%'");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $config[$row['key']] = $row['value'];
        }
        
        // Check validity dates
        $today = date('Y-m-d');
        if (!empty($config['staff_invoice_limit_valid_from']) && $today < $config['staff_invoice_limit_valid_from']) {
            return false;
        }
        if (!empty($config['staff_invoice_limit_valid_to']) && $today > $config['staff_invoice_limit_valid_to']) {
            return false;
        }
        
        // Get the limit amount - check in order: individual override, group limit, global limit
        $limitAmount = null;
        
        // 1. Check individual override
        if (!empty($client['staff_limit_override'])) {
            $limitAmount = $client['staff_limit_override'];
        } else {
            // 2. Check group-based limits
            $stmt = $db->prepare("
                SELECT sil.limit_amount 
                FROM staff_invoice_limits sil
                JOIN clients c ON c.id = ?
                JOIN user_groups_members ugm ON ugm.user_id = c.created_by
                WHERE sil.group_id = ugm.group_id
                  AND sil.is_active = 1
                  AND sil.valid_from <= CURDATE()
                  AND (sil.valid_to IS NULL OR sil.valid_to >= CURDATE())
                ORDER BY sil.limit_amount DESC
                LIMIT 1
            ");
            $stmt->execute([$data['client_id']]);
            $groupLimit = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($groupLimit) {
                $limitAmount = $groupLimit['limit_amount'];
            } else {
                // 3. Use global limit
                $limitAmount = $config['staff_invoice_limit_amount'] ?? 5000;
            }
        }
        
        // Check if invoice exceeds limit
        if ($data['total'] > $limitAmount) {
            // Store original amount
            $data['original_amount'] = $data['total'];
            $data['staff_limit_applied'] = true;
            $data['staff_limit_amount'] = $limitAmount;
            
            // Apply the limit
            $ratio = $limitAmount / $data['total'];
            $data['subtotal'] = round($data['subtotal'] * $ratio, 2);
            $data['vat_amount'] = round($data['vat_amount'] * $ratio, 2);
            $data['total'] = $limitAmount;
            
            // Add note about limit
            $limitMessage = $config['staff_invoice_limit_message'] ?? 'Invoice amount has been limited to the maximum allowed for staff members';
            $data['internal_notes'] = trim(($data['internal_notes'] ?? '') . "\n\n" . $limitMessage);
            
            // Adjust invoice lines if present
            if (!empty($data['lines'])) {
                foreach ($data['lines'] as &$line) {
                    $line['unit_price'] = round($line['unit_price'] * $ratio, 2);
                    $line['line_total'] = $line['quantity'] * $line['unit_price'];
                }
            }
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Check if invoice can be edited
     */
    public function canEdit($invoiceId = null)
    {
        if ($invoiceId) {
            $invoice = $this->getById($invoiceId);
        } else {
            $invoice = $this;
        }
        
        // Cannot edit if locked
        if (!empty($invoice['locked_at'])) {
            return false;
        }
        
        // Cannot edit if not draft
        if ($invoice['status'] !== self::STATUS_DRAFT) {
            return false;
        }
        
        // Cannot edit if draft period expired
        if (!empty($invoice['draft_until']) && strtotime($invoice['draft_until']) < time()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Lock invoice
     */
    public function lockInvoice($invoiceId, $userId = null)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            UPDATE invoices 
            SET locked_at = NOW(), 
                locked_by = :locked_by,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        return $stmt->execute([
            ':id' => $invoiceId,
            ':locked_by' => $userId ?? $_SESSION['user_id'] ?? 1
        ]);
    }
    
    /**
     * Update invoice status
     */
    public function updateStatus($invoiceId, $status)
    {
        $db = Flight::db();
        
        // Validate status transition
        if (!$this->isValidStatusTransition($invoiceId, $status)) {
            throw new Exception("Invalid status transition");
        }
        
        $updates = ['status' => $status];
        
        // Set additional fields based on status
        switch ($status) {
            case self::STATUS_SENT:
                $updates['sent_at'] = date('Y-m-d H:i:s');
                break;
            case self::STATUS_PAID:
                $updates['paid_at'] = date('Y-m-d H:i:s');
                break;
        }
        
        $setClause = [];
        $params = ['id' => $invoiceId];
        
        foreach ($updates as $field => $value) {
            $setClause[] = "$field = :$field";
            $params[$field] = $value;
        }
        
        $sql = "UPDATE invoices SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = :id";
        $stmt = $db->prepare($sql);
        
        return $stmt->execute($params);
    }
    
    /**
     * Validate status transition
     */
    private function isValidStatusTransition($invoiceId, $newStatus)
    {
        $invoice = $this->getById($invoiceId);
        $currentStatus = $invoice['status'];
        
        // Define valid transitions
        $validTransitions = [
            self::STATUS_DRAFT => [self::STATUS_SENT, self::STATUS_CANCELLED],
            self::STATUS_SENT => [self::STATUS_PAID, self::STATUS_PARTIAL, self::STATUS_OVERDUE, self::STATUS_CANCELLED],
            self::STATUS_PARTIAL => [self::STATUS_PAID, self::STATUS_OVERDUE],
            self::STATUS_OVERDUE => [self::STATUS_PAID, self::STATUS_PARTIAL],
            self::STATUS_PAID => [], // No transitions from paid
            self::STATUS_CANCELLED => [] // No transitions from cancelled
        ];
        
        return in_array($newStatus, $validTransitions[$currentStatus] ?? []);
    }
    
    /**
     * Calculate totals from lines
     */
    public function calculateTotals($invoiceId)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                SUM(line_total) as subtotal,
                SUM(vat_amount) as total_vat
            FROM invoice_lines
            WHERE invoice_id = ?
        ");
        $stmt->execute([$invoiceId]);
        $totals = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $subtotal = $totals['subtotal'] ?? 0;
        $vatAmount = $totals['total_vat'] ?? 0;
        $total = $subtotal; // Line totals already include VAT
        
        // Update invoice totals
        $stmt = $db->prepare("
            UPDATE invoices 
            SET subtotal = :subtotal,
                vat_amount = :vat_amount,
                total = :total,
                updated_at = NOW()
            WHERE id = :id
        ");
        
        $stmt->execute([
            ':subtotal' => $subtotal,
            ':vat_amount' => $vatAmount,
            ':total' => $total,
            ':id' => $invoiceId
        ]);
        
        return [
            'subtotal' => $subtotal,
            'vat_amount' => $vatAmount,
            'total' => $total
        ];
    }
    
    /**
     * Get invoice with all relations (with caching)
     */
    public function getInvoiceWithDetails($invoiceId)
    {
        $cache = Flight::cache();
        $cacheKey = "invoice_details_{$invoiceId}";
        
        // Try to get from cache first
        $cachedData = $cache->get($cacheKey);
        if ($cachedData !== null) {
            return $cachedData;
        }
        
        $invoice = $this->getById($invoiceId);
        
        if (!$invoice) {
            return null;
        }
        
        // Get invoice lines
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT * FROM invoice_lines 
            WHERE invoice_id = ? 
            ORDER BY sort_order
        ");
        $stmt->execute([$invoiceId]);
        $invoice['lines'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get retrocession details if applicable
        if (in_array($invoice['invoice_type'], [self::TYPE_RETROCESSION_30, self::TYPE_RETROCESSION_25])) {
            $stmt = $db->prepare("SELECT * FROM invoice_retrocessions WHERE invoice_id = ?");
            $stmt->execute([$invoiceId]);
            $invoice['retrocession'] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Get client details
        if (!empty($invoice['client_id'])) {
            $client = new Client();
            $invoice['client'] = $client->getById($invoice['client_id']);
        }
        
        // Get user details if applicable
        if (!empty($invoice['user_id'])) {
            $stmt = $db->prepare("
                SELECT u.*, CONCAT(u.first_name, ' ', u.last_name) as full_name,
                       COALESCE(u.billing_email, u.email) as invoice_email
                FROM users u
                WHERE u.id = ?
            ");
            $stmt->execute([$invoice['user_id']]);
            $invoice['user'] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Get document type details
        if (!empty($invoice['document_type_id'])) {
            $stmt = $db->prepare("SELECT * FROM document_types WHERE id = ?");
            $stmt->execute([$invoice['document_type_id']]);
            $invoice['document_type'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Decode JSON fields
            if ($invoice['document_type']) {
                $invoice['document_type']['name'] = json_decode($invoice['document_type']['name'], true);
                $invoice['document_type']['description'] = json_decode($invoice['document_type']['description'], true);
            }
        }
        
        // Get invoice type details (legacy)
        if (!empty($invoice['type_id'])) {
            $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$invoice['type_id']]);
            $invoice['type_details'] = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Cache the complete data for 30 minutes
        $cache->set($cacheKey, $invoice, 1800);
        
        return $invoice;
    }
    
    /**
     * Create credit note from invoice
     */
    public function createCreditNote($originalInvoiceId, $data)
    {
        $originalInvoice = $this->getInvoiceWithDetails($originalInvoiceId);
        
        if (!$originalInvoice) {
            throw new Exception("Original invoice not found");
        }
        
        // Get credit note document type
        $db = Flight::db();
        $stmt = $db->prepare("SELECT id FROM document_types WHERE code = 'credit_note'");
        $stmt->execute();
        $cnType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$cnType) {
            throw new Exception("Credit note document type not found");
        }
        
        // Prepare credit note data
        $creditNoteData = array_merge($data, [
            'document_type_id' => $cnType['id'],
            'type_id' => $originalInvoice['type_id'],
            'invoice_type' => 'credit_note',
            'client_id' => $originalInvoice['client_id'],
            'reference_document_id' => $originalInvoiceId,
            'reference_document_number' => $originalInvoice['invoice_number'],
            'subtotal' => -abs($data['amount'] ?? $originalInvoice['subtotal']),
            'vat_amount' => -abs($data['vat_amount'] ?? $originalInvoice['vat_amount']),
            'total' => -abs($data['total'] ?? $originalInvoice['total']),
            'status' => self::STATUS_SENT,
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d')
        ]);
        
        // Copy invoice lines as negative amounts if not provided
        if (empty($data['lines']) && !empty($originalInvoice['lines'])) {
            $creditNoteData['lines'] = [];
            foreach ($originalInvoice['lines'] as $line) {
                $creditNoteData['lines'][] = [
                    'description' => $line['description'],
                    'quantity' => $line['quantity'],
                    'unit_price' => -abs($line['unit_price']),
                    'vat_rate' => $line['vat_rate'],
                    'line_type' => $line['line_type']
                ];
            }
        }
        
        return $this->createInvoice($creditNoteData);
    }
    
    /**
     * Add invoice lines
     */
    public function addInvoiceLines($invoiceId, $lines)
    {
        $db = Flight::db();
        
        foreach ($lines as $index => $line) {
            // Skip if no description
            if (empty($line['description'])) {
                continue;
            }
            
            // Calculate VAT and totals
            $quantity = $line['quantity'] ?? 1;
            $unitPrice = $line['unit_price'] ?? 0;
            $vatRate = $line['vat_rate'] ?? 0;
            
            $subtotal = $quantity * $unitPrice;
            $vatAmount = $subtotal * ($vatRate / 100);
            $lineTotal = $subtotal + $vatAmount;
            
            // Determine line type based on description or default to 'service'
            $lineType = $line['line_type'] ?? 'service';
            
            $stmt = $db->prepare("
                INSERT INTO invoice_lines (
                    invoice_id, line_type, description, quantity, unit_price,
                    vat_rate, line_total, sort_order
                ) VALUES (
                    :invoice_id, :line_type, :description, :quantity, :unit_price,
                    :vat_rate, :line_total, :sort_order
                )
            ");
            
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':line_type' => $lineType,
                ':description' => $line['description'],
                ':quantity' => $quantity,
                ':unit_price' => $unitPrice,
                ':vat_rate' => $vatRate,
                ':line_total' => $lineTotal,
                ':sort_order' => $index
            ]);
        }
    }
    
    /**
     * Clear invoice cache
     */
    public function clearInvoiceCache($invoiceId)
    {
        $cache = Flight::cache();
        $cache->forget("invoice_{$invoiceId}");
        $cache->forget("invoice_details_{$invoiceId}");
    }
    
    /**
     * Update invoice with cache invalidation
     */
    public function updateInvoice($invoiceId, $data)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Define allowed fields for update
            $allowedFields = [
                'status', 'issue_date', 'due_date', 'notes', 'internal_notes',
                'payment_terms', 'footer_text', 'subtotal', 'vat_amount', 'total'
            ];
            
            // Filter data to only include allowed fields
            $updateData = array_intersect_key($data, array_flip($allowedFields));
            
            if (!empty($updateData)) {
                $setClause = [];
                $params = ['id' => $invoiceId];
                
                foreach ($updateData as $field => $value) {
                    $setClause[] = "{$field} = :{$field}";
                    $params[$field] = $value;
                }
                
                $sql = "UPDATE invoices SET " . implode(', ', $setClause) . ", updated_at = NOW() WHERE id = :id";
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
            }
            
            // Update invoice lines if provided
            if (!empty($data['lines'])) {
                // Delete existing lines
                $stmt = $db->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
                $stmt->execute([$invoiceId]);
                
                // Add new lines
                $this->addInvoiceLines($invoiceId, $data['lines']);
            }
            
            // Recalculate totals
            $this->calculateTotals($invoiceId);
            
            $db->commit();
            
            // Clear cache after successful update
            $this->clearInvoiceCache($invoiceId);
            
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Archive an invoice
     */
    public function archiveInvoice($invoiceId, $userId = null, $reason = null)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Get current invoice
            $invoice = $this->getById($invoiceId);
            if (!$invoice) {
                throw new Exception("Invoice not found");
            }
            
            // Check if invoice can be archived (only paid or cancelled)
            if (!in_array($invoice['status'], [self::STATUS_PAID, self::STATUS_CANCELLED])) {
                throw new Exception("Only paid or cancelled invoices can be archived");
            }
            
            // Archive the invoice
            $stmt = $db->prepare("
                UPDATE invoices 
                SET is_archived = TRUE, 
                    archived_at = NOW(), 
                    archived_by = :archived_by,
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $userId = $userId ?? $_SESSION['user_id'] ?? 1;
            $stmt->execute([
                ':id' => $invoiceId,
                ':archived_by' => $userId
            ]);
            
            // Log the archive action
            $stmt = $db->prepare("
                INSERT INTO invoice_archive_logs (invoice_id, action, user_id, reason)
                VALUES (:invoice_id, 'archived', :user_id, :reason)
            ");
            
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':user_id' => $userId,
                ':reason' => $reason
            ]);
            
            $db->commit();
            
            // Clear cache
            $this->clearInvoiceCache($invoiceId);
            
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Restore an archived invoice
     */
    public function restoreInvoice($invoiceId, $userId = null, $reason = null)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Restore the invoice
            $stmt = $db->prepare("
                UPDATE invoices 
                SET is_archived = FALSE, 
                    archived_at = NULL, 
                    archived_by = NULL,
                    updated_at = NOW()
                WHERE id = :id
            ");
            
            $stmt->execute([':id' => $invoiceId]);
            
            // Log the restore action
            $stmt = $db->prepare("
                INSERT INTO invoice_archive_logs (invoice_id, action, user_id, reason)
                VALUES (:invoice_id, 'restored', :user_id, :reason)
            ");
            
            $userId = $userId ?? $_SESSION['user_id'] ?? 1;
            $stmt->execute([
                ':invoice_id' => $invoiceId,
                ':user_id' => $userId,
                ':reason' => $reason
            ]);
            
            $db->commit();
            
            // Clear cache
            $this->clearInvoiceCache($invoiceId);
            
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get archive statistics by document type
     */
    public function getArchiveStatistics()
    {
        $db = Flight::db();
        
        $stmt = $db->query("
            SELECT 
                dt.id as document_type_id,
                dt.code as document_type_code,
                dt.name as document_type_name,
                dt.color,
                dt.icon,
                COUNT(i.id) as count,
                SUM(i.total) as total_amount
            FROM document_types dt
            LEFT JOIN invoices i ON dt.id = i.document_type_id AND i.is_archived = TRUE
            WHERE dt.is_active = TRUE
            GROUP BY dt.id
            ORDER BY dt.sort_order
        ");
        
        $stats = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Process JSON names
        $lang = $_SESSION['lang'] ?? 'fr';
        foreach ($stats as &$stat) {
            if (!empty($stat['document_type_name'])) {
                $nameData = json_decode($stat['document_type_name'], true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($nameData)) {
                    $stat['document_type_display_name'] = $nameData[$lang] ?? $nameData['fr'] ?? $nameData['en'] ?? $stat['document_type_code'];
                } else {
                    $stat['document_type_display_name'] = $stat['document_type_name'];
                }
            } else {
                $stat['document_type_display_name'] = $stat['document_type_code'];
            }
        }
        
        return $stats;
    }
}