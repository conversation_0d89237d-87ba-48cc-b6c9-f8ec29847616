/**
 * Inline Invoice Editor
 * Enables click-to-edit functionality for invoice line items
 */

class InlineInvoiceEditor {
    constructor() {
        this.editingCell = null;
        this.originalValue = null;
        this.saveTimeout = null;
        this.editHistory = [];
        this.maxHistorySize = 10;
        this.currentHistoryIndex = -1;
        
        this.init();
    }
    
    init() {
        // Check if we're on an invoice page and it's editable
        const invoiceForm = document.querySelector('#invoiceForm');
        if (!invoiceForm) return;
        
        // Check if invoice is already sent/paid (non-editable)
        const invoiceStatus = invoiceForm.dataset.status;
        if (invoiceStatus === 'sent' || invoiceStatus === 'paid') {
            document.body.classList.add(`invoice-${invoiceStatus}`);
            return;
        }
        
        // Check if we're on create page (no invoice ID yet)
        this.isCreatePage = !this.getInvoiceId();
        
        // Initialize editable fields (only on edit page)
        if (!this.isCreatePage) {
            this.initializeEditableFields();
        } else {
            // On create page, wait for invoice to be saved first
            this.observeCreatePageSave();
        }
        
        // Setup keyboard shortcuts
        this.setupKeyboardShortcuts();
        
        // Setup save queue
        this.saveQueue = [];
        this.processingQueue = false;
    }
    
    observeCreatePageSave() {
        // Watch for when the invoice is saved and we get an ID
        // This could be triggered by watching for URL changes or form submission
        const form = document.querySelector('#invoiceForm');
        if (!form) return;
        
        // Add note to user that inline editing is available after saving
        const note = document.createElement('div');
        note.className = 'alert alert-info mt-2';
        note.innerHTML = '<i class="bi bi-info-circle me-2"></i>Inline editing will be available after saving the invoice as draft.';
        
        const itemsTable = document.querySelector('#itemsTable');
        if (itemsTable && itemsTable.parentElement) {
            itemsTable.parentElement.insertBefore(note, itemsTable);
        }
    }
    
    initializeEditableFields() {
        // Get all invoice line rows
        const itemRows = document.querySelectorAll('.invoice-item, .invoice-item-row');
        
        itemRows.forEach(row => {
            // Make specific fields editable
            const editableSelectors = [
                '.item-description',
                '.item-quantity', 
                '.item-price',
                '.item-vat'
            ];
            
            editableSelectors.forEach(selector => {
                const field = row.querySelector(selector);
                if (field && !field.classList.contains('editable-field')) {
                    this.makeFieldEditable(field);
                }
            });
        });
        
        // Watch for new rows being added
        this.observeNewRows();
    }
    
    makeFieldEditable(field) {
        // Add editable class
        field.classList.add('editable-field');
        
        // Store original value
        field.dataset.originalValue = this.getFieldValue(field);
        
        // Add click handler
        field.addEventListener('click', (e) => this.startEditing(e));
        
        // Prevent form submission on Enter within editable fields
        field.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && this.editingCell === field) {
                e.preventDefault();
                this.finishEditing(field);
            }
        });
    }
    
    getFieldValue(field) {
        if (field.tagName === 'SELECT') {
            return field.value;
        } else if (field.tagName === 'INPUT') {
            return field.value;
        } else {
            return field.textContent.trim();
        }
    }
    
    setFieldValue(field, value) {
        if (field.tagName === 'SELECT') {
            field.value = value;
        } else if (field.tagName === 'INPUT') {
            field.value = value;
        } else {
            field.textContent = value;
        }
    }
    
    startEditing(e) {
        const field = e.currentTarget;
        
        // Don't start editing if already editing
        if (this.editingCell === field) return;
        
        // Finish any other editing
        if (this.editingCell) {
            this.finishEditing(this.editingCell);
        }
        
        // Start editing this field
        this.editingCell = field;
        this.originalValue = this.getFieldValue(field);
        
        field.classList.add('editing');
        
        // Create inline input
        if (field.tagName !== 'SELECT' && field.tagName !== 'INPUT') {
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'inline-edit-input';
            input.value = this.originalValue;
            
            // Set input type based on field
            if (field.classList.contains('item-quantity') || field.classList.contains('item-price')) {
                input.type = 'number';
                input.step = '0.01';
                input.min = '0';
            }
            
            field.innerHTML = '';
            field.appendChild(input);
            input.focus();
            input.select();
            
            // Handle input events
            input.addEventListener('blur', () => this.finishEditing(field));
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.finishEditing(field);
                } else if (e.key === 'Escape') {
                    e.preventDefault();
                    this.cancelEditing(field);
                }
            });
        } else if (field.tagName === 'INPUT' || field.tagName === 'SELECT') {
            field.focus();
            if (field.tagName === 'INPUT') {
                field.select();
            }
            
            // Add blur handler
            const blurHandler = () => {
                field.removeEventListener('blur', blurHandler);
                this.finishEditing(field);
            };
            field.addEventListener('blur', blurHandler);
        }
    }
    
    finishEditing(field) {
        if (!field || this.editingCell !== field) return;
        
        let newValue;
        const input = field.querySelector('.inline-edit-input');
        
        if (input) {
            newValue = input.value.trim();
        } else {
            newValue = this.getFieldValue(field);
        }
        
        // Validate the new value
        const validation = this.validateField(field, newValue);
        if (!validation.valid) {
            this.showError(field, validation.message);
            if (input) input.focus();
            return;
        }
        
        // Remove input and restore text
        if (input) {
            field.innerHTML = '';
            field.textContent = newValue;
        }
        
        field.classList.remove('editing');
        this.editingCell = null;
        
        // Check if value changed
        if (newValue !== this.originalValue) {
            field.classList.add('modified');
            this.addToHistory(field, this.originalValue, newValue);
            this.queueSave(field, newValue);
        }
    }
    
    cancelEditing(field) {
        if (!field || this.editingCell !== field) return;
        
        const input = field.querySelector('.inline-edit-input');
        if (input) {
            field.innerHTML = '';
            field.textContent = this.originalValue;
        } else {
            this.setFieldValue(field, this.originalValue);
        }
        
        field.classList.remove('editing');
        this.editingCell = null;
    }
    
    validateField(field, value) {
        // Description validation
        if (field.classList.contains('item-description')) {
            if (!value || value.trim() === '') {
                return { valid: false, message: 'Description is required' };
            }
            if (value.length > 500) {
                return { valid: false, message: 'Description max 500 characters' };
            }
        }
        
        // Quantity validation
        if (field.classList.contains('item-quantity')) {
            const num = parseFloat(value);
            if (isNaN(num) || num <= 0) {
                return { valid: false, message: 'Quantity must be positive' };
            }
        }
        
        // Price validation
        if (field.classList.contains('item-price')) {
            const num = parseFloat(value);
            if (isNaN(num) || num < 0) {
                return { valid: false, message: 'Price must be non-negative' };
            }
        }
        
        return { valid: true };
    }
    
    showError(field, message) {
        // Remove any existing error
        const existingError = field.querySelector('.inline-edit-error');
        if (existingError) existingError.remove();
        
        const error = document.createElement('div');
        error.className = 'inline-edit-error';
        error.textContent = message;
        field.appendChild(error);
        
        field.classList.add('error');
        
        setTimeout(() => {
            error.remove();
            field.classList.remove('error');
        }, 3000);
    }
    
    queueSave(field, value) {
        // Clear any pending save for this field
        clearTimeout(this.saveTimeout);
        
        // Add to save queue
        const fieldData = {
            field: field,
            value: value,
            fieldType: this.getFieldType(field),
            rowIndex: this.getRowIndex(field)
        };
        
        // Remove any pending save for the same field
        this.saveQueue = this.saveQueue.filter(item => item.field !== field);
        this.saveQueue.push(fieldData);
        
        // Show saving state
        field.classList.add('saving');
        
        // Debounce save
        this.saveTimeout = setTimeout(() => {
            this.processSaveQueue();
        }, 500);
    }
    
    async processSaveQueue() {
        if (this.processingQueue || this.saveQueue.length === 0) return;
        
        this.processingQueue = true;
        const itemsToSave = [...this.saveQueue];
        this.saveQueue = [];
        
        try {
            // Get invoice ID from URL or form
            const invoiceId = this.getInvoiceId();
            if (!invoiceId) {
                throw new Error('Invoice ID not found');
            }
            
            // Get CSRF token
            const csrfToken = document.querySelector('input[name="csrf_token"]')?.value;
            if (!csrfToken) {
                throw new Error('CSRF token not found');
            }
            
            // Save each item
            for (const item of itemsToSave) {
                await this.saveFieldToServer(invoiceId, item, csrfToken);
                
                // Show success
                item.field.classList.remove('saving');
                item.field.classList.add('saved');
                
                // Update any dependent calculations
                this.updateCalculations(item.field);
                
                setTimeout(() => {
                    item.field.classList.remove('saved');
                }, 1000);
            }
            
        } catch (error) {
            console.error('Save error:', error);
            itemsToSave.forEach(item => {
                item.field.classList.remove('saving');
                this.showError(item.field, error.message || 'Save failed');
            });
        }
        
        this.processingQueue = false;
    }
    
    async saveFieldToServer(invoiceId, fieldData, csrfToken) {
        const baseUrl = window.location.origin + '/fit/public';
        
        const response = await fetch(`${baseUrl}/api/invoices/${invoiceId}/update-line`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                csrf_token: csrfToken,
                line_index: fieldData.rowIndex,
                field: fieldData.fieldType,
                value: fieldData.value
            })
        });
        
        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.message || 'Failed to save');
        }
        
        return data;
    }
    
    getInvoiceId() {
        // Try to get from URL (edit page)
        const pathMatch = window.location.pathname.match(/invoices\/(\d+)\/edit/);
        if (pathMatch) {
            return pathMatch[1];
        }
        
        // Try to get from form (might be in a hidden field for create page)
        const invoiceIdField = document.querySelector('input[name="invoice_id"]');
        if (invoiceIdField) {
            return invoiceIdField.value;
        }
        
        // For create page, we might need to save the invoice first
        // This would require a different approach
        return null;
    }
    
    updateCalculations(field) {
        // Find the row
        const row = field.closest('.invoice-item, .invoice-item-row, tr');
        if (!row) return;
        
        // Trigger calculation update
        if (window.calculateItemTotal) {
            window.calculateItemTotal(row);
        }
        
        // Trigger total recalculation
        if (window.calculateTotals) {
            window.calculateTotals();
        }
    }
    
    getFieldType(field) {
        if (field.classList.contains('item-description')) return 'description';
        if (field.classList.contains('item-quantity')) return 'quantity';
        if (field.classList.contains('item-price')) return 'unit_price';
        if (field.classList.contains('item-vat')) return 'vat_rate_id';
        return 'unknown';
    }
    
    getRowIndex(field) {
        const row = field.closest('.invoice-item, .invoice-item-row, tr');
        if (!row) return -1;
        
        const allRows = document.querySelectorAll('.invoice-item, .invoice-item-row, tr.invoice-item');
        return Array.from(allRows).indexOf(row);
    }
    
    addToHistory(field, oldValue, newValue) {
        const historyItem = {
            field: field,
            fieldType: this.getFieldType(field),
            oldValue: oldValue,
            newValue: newValue,
            timestamp: Date.now()
        };
        
        // Add to history
        if (this.currentHistoryIndex < this.editHistory.length - 1) {
            // Remove any history after current index
            this.editHistory = this.editHistory.slice(0, this.currentHistoryIndex + 1);
        }
        
        this.editHistory.push(historyItem);
        this.currentHistoryIndex++;
        
        // Limit history size
        if (this.editHistory.length > this.maxHistorySize) {
            this.editHistory.shift();
            this.currentHistoryIndex--;
        }
    }
    
    undo() {
        if (this.currentHistoryIndex < 0) return;
        
        const historyItem = this.editHistory[this.currentHistoryIndex];
        this.setFieldValue(historyItem.field, historyItem.oldValue);
        this.queueSave(historyItem.field, historyItem.oldValue);
        
        this.currentHistoryIndex--;
    }
    
    redo() {
        if (this.currentHistoryIndex >= this.editHistory.length - 1) return;
        
        this.currentHistoryIndex++;
        const historyItem = this.editHistory[this.currentHistoryIndex];
        this.setFieldValue(historyItem.field, historyItem.newValue);
        this.queueSave(historyItem.field, historyItem.newValue);
    }
    
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Z for undo
            if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
                e.preventDefault();
                this.undo();
            }
            
            // Ctrl+Y or Ctrl+Shift+Z for redo
            if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'z')) {
                e.preventDefault();
                this.redo();
            }
        });
    }
    
    observeNewRows() {
        // Watch for new rows being added to the table
        const table = document.querySelector('#itemsTable tbody');
        if (!table) return;
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === 1 && (node.classList.contains('invoice-item') || 
                        node.classList.contains('invoice-item-row') || 
                        node.tagName === 'TR')) {
                        // Make fields in new row editable
                        const editableSelectors = [
                            '.item-description',
                            '.item-quantity', 
                            '.item-price',
                            '.item-vat'
                        ];
                        
                        editableSelectors.forEach(selector => {
                            const field = node.querySelector(selector);
                            if (field && !field.classList.contains('editable-field')) {
                                this.makeFieldEditable(field);
                            }
                        });
                    }
                });
            });
        });
        
        observer.observe(table, { childList: true });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize on invoice create/edit pages
    if (document.querySelector('#invoiceForm')) {
        window.inlineInvoiceEditor = new InlineInvoiceEditor();
    }
});