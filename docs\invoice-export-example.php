<?php
/**
 * Example implementation for handling enhanced table export with column order and sorting
 * This can be added to your InvoiceController
 */

/**
 * Export invoices with column order and sorting support
 */
public function export()
{
    $format = Flight::request()->query->format ?? 'csv';
    
    // Get filters
    $filters = [
        'status' => Flight::request()->query->status,
        'type' => Flight::request()->query->type,
        'date_from' => Flight::request()->query->date_from,
        'date_to' => Flight::request()->query->date_to,
        'search' => Flight::request()->query->search
    ];
    
    // Get column order and sort parameters
    $columnOrder = json_decode(Flight::request()->query->column_order ?? '[]', true);
    $sortColumn = Flight::request()->query->sort_column;
    $sortDirection = Flight::request()->query->sort_direction ?? 'asc';
    
    // Get filtered invoices
    $invoices = $this->getInvoices($filters, 1, 9999); // Get all for export
    $data = $invoices['data'];
    
    // Define column mapping (index => field)
    $columnMapping = [
        0 => null, // Checkbox column - skip
        1 => 'invoice_number',
        2 => 'doc_type_display_name',
        3 => 'type_name',
        4 => 'client_name',
        5 => 'issue_date',
        6 => 'due_date',
        7 => 'total',
        8 => 'status',
        9 => null  // Actions column - skip
    ];
    
    // Apply sorting if specified
    if ($sortColumn !== null && isset($columnMapping[$sortColumn])) {
        $sortField = $columnMapping[$sortColumn];
        if ($sortField) {
            usort($data, function($a, $b) use ($sortField, $sortDirection) {
                $aVal = $a[$sortField] ?? '';
                $bVal = $b[$sortField] ?? '';
                
                // Handle numeric comparison for amount field
                if ($sortField === 'total') {
                    $aVal = floatval($aVal);
                    $bVal = floatval($bVal);
                }
                
                // Handle date comparison
                if (in_array($sortField, ['issue_date', 'due_date'])) {
                    $aVal = strtotime($aVal);
                    $bVal = strtotime($bVal);
                }
                
                $result = $aVal <=> $bVal;
                return $sortDirection === 'desc' ? -$result : $result;
            });
        }
    }
    
    // Prepare data for export
    $exportData = [];
    
    // Headers
    $headers = [
        'invoice_number' => __('invoices.invoice_number'),
        'doc_type_display_name' => __('invoices.document_type'),
        'type_name' => __('invoices.invoice_type'),
        'client_name' => __('clients.client'),
        'issue_date' => __('invoices.issue_date'),
        'due_date' => __('invoices.due_date'),
        'total' => __('invoices.amount'),
        'status' => __('common.status')
    ];
    
    // If column order is specified, reorder headers
    if (!empty($columnOrder)) {
        $orderedHeaders = [];
        foreach ($columnOrder as $index) {
            if (isset($columnMapping[$index]) && $columnMapping[$index]) {
                $field = $columnMapping[$index];
                if (isset($headers[$field])) {
                    $orderedHeaders[$field] = $headers[$field];
                }
            }
        }
        $headers = $orderedHeaders;
    }
    
    $exportData[] = array_values($headers); // Header row
    
    // Data rows
    foreach ($data as $invoice) {
        $row = [];
        foreach (array_keys($headers) as $field) {
            switch ($field) {
                case 'issue_date':
                case 'due_date':
                    $row[] = $invoice[$field] ? date('d/m/Y', strtotime($invoice[$field])) : '';
                    break;
                case 'total':
                    $row[] = number_format($invoice[$field], 2, ',', ' ');
                    break;
                case 'status':
                    $row[] = __('invoices.status.' . $invoice[$field]);
                    break;
                case 'client_name':
                    // Handle both client and patient names
                    $row[] = $invoice['patient_id'] ? $invoice['patient_name'] : $invoice['client_name'];
                    break;
                default:
                    $row[] = $invoice[$field] ?? '';
            }
        }
        $exportData[] = $row;
    }
    
    // Export based on format
    switch ($format) {
        case 'csv':
            $this->exportCsv($exportData, 'invoices_' . date('Y-m-d'));
            break;
        case 'excel':
            $this->exportExcel($exportData, 'invoices_' . date('Y-m-d'));
            break;
        case 'pdf':
            $this->exportPdf($exportData, 'invoices_' . date('Y-m-d'));
            break;
    }
}

/**
 * Export data as CSV
 */
private function exportCsv($data, $filename)
{
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Add BOM for UTF-8
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    foreach ($data as $row) {
        fputcsv($output, $row, ';');
    }
    
    fclose($output);
    exit;
}

/**
 * Export data as Excel (using a library like PhpSpreadsheet)
 */
private function exportExcel($data, $filename)
{
    // This is a simplified example
    // In production, use PhpSpreadsheet or similar library
    
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    echo '<html xmlns:x="urn:schemas-microsoft-com:office:excel">';
    echo '<head>';
    echo '<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">';
    echo '</head>';
    echo '<body>';
    echo '<table border="1">';
    
    $isHeader = true;
    foreach ($data as $row) {
        echo '<tr>';
        foreach ($row as $cell) {
            $tag = $isHeader ? 'th' : 'td';
            echo "<{$tag}>" . htmlspecialchars($cell) . "</{$tag}>";
        }
        echo '</tr>';
        $isHeader = false;
    }
    
    echo '</table>';
    echo '</body>';
    echo '</html>';
    exit;
}

/**
 * Handle bulk export with selected IDs
 */
public function bulkExport()
{
    $ids = Flight::request()->data->ids ?? [];
    $format = Flight::request()->data->format ?? 'csv';
    
    if (empty($ids)) {
        $this->flash('error', __('invoices.no_invoices_selected'));
        $this->redirect('/invoices');
        return;
    }
    
    // Get selected invoices
    $invoices = $this->invoice->getByIds($ids);
    
    // Prepare data for export
    $exportData = [];
    
    // Headers
    $headers = [
        __('invoices.invoice_number'),
        __('invoices.document_type'),
        __('clients.client'),
        __('invoices.issue_date'),
        __('invoices.due_date'),
        __('invoices.amount'),
        __('common.status')
    ];
    
    $exportData[] = $headers;
    
    // Data rows
    foreach ($invoices as $invoice) {
        $exportData[] = [
            $invoice['invoice_number'],
            $invoice['doc_type_display_name'],
            $invoice['patient_id'] ? $invoice['patient_name'] : $invoice['client_name'],
            date('d/m/Y', strtotime($invoice['issue_date'])),
            $invoice['due_date'] ? date('d/m/Y', strtotime($invoice['due_date'])) : '',
            number_format($invoice['total'], 2, ',', ' '),
            __('invoices.status.' . $invoice['status'])
        ];
    }
    
    // Export based on format
    $filename = 'invoices_selection_' . date('Y-m-d');
    
    switch ($format) {
        case 'csv':
            $this->exportCsv($exportData, $filename);
            break;
        case 'excel':
            $this->exportExcel($exportData, $filename);
            break;
        case 'pdf':
            $this->exportPdf($exportData, $filename);
            break;
    }
}