<!DOCTYPE html>
<html>
<head>
    <title>Debug Column Rendering</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Debug Column Rendering</h2>
        
        <div class="card">
            <div class="card-header">
                <h5>Column List Container</h5>
            </div>
            <div class="card-body">
                <div id="columnList" class="list-group">
                    <!-- Columns will be rendered here -->
                </div>
            </div>
        </div>
        
        <div class="mt-3">
            <button class="btn btn-primary" onclick="testRender()">Test Render with API Data</button>
            <button class="btn btn-secondary" onclick="clearList()">Clear List</button>
        </div>
        
        <div class="mt-3">
            <h5>Console Output:</h5>
            <pre id="console" style="background: #f5f5f5; padding: 10px; height: 200px; overflow-y: auto;"></pre>
        </div>
    </div>

    <script>
    // Console logging to page
    const consoleEl = document.getElementById('console');
    const log = (msg) => {
        console.log(msg);
        consoleEl.innerHTML += msg + '\n';
    };

    // Table columns definition (same as in the actual page)
    const tableColumns = {
        invoice_items: [
            { id: 'description', name: 'Description', required: true },
            { id: 'reference', name: 'Référence' },
            { id: 'quantity', name: 'Quantité' },
            { id: 'unit', name: 'Unité' },
            { id: 'unit_price', name: 'Prix unitaire' },
            { id: 'discount', name: 'Remise' },
            { id: 'vat_rate', name: 'Taux TVA' },
            { id: 'subtotal', name: 'Sous-total' },
            { id: 'total', name: 'Total', required: true }
        ]
    };

    // Current config from API
    let currentConfig = [];

    function clearList() {
        document.getElementById('columnList').innerHTML = '';
        consoleEl.innerHTML = '';
    }

    function testRender() {
        log('Starting render test...');
        
        // Simulate API response
        currentConfig = [
            {"order":0,"column":"description","visible":true,"required":true},
            {"order":1,"column":"reference","visible":false,"required":false},
            {"order":2,"column":"quantity","visible":true,"required":false},
            {"order":3,"column":"unit","visible":false,"required":false},
            {"order":4,"column":"unit_price","visible":true,"required":false},
            {"order":5,"column":"discount","visible":false,"required":false},
            {"order":6,"column":"vat_rate","visible":true,"required":false},
            {"order":7,"column":"subtotal","visible":false,"required":false},
            {"order":8,"column":"total","visible":true,"required":true}
        ];
        
        log('Current config loaded: ' + currentConfig.length + ' items');
        renderColumns('invoice_items');
    }

    function renderColumns(tableName) {
        log('renderColumns called with table: ' + tableName);
        
        const columns = tableColumns[tableName] || [];
        const columnList = document.getElementById('columnList');
        
        log('Found ' + columns.length + ' columns for table');
        
        columnList.innerHTML = '';
        
        if (!columns.length) {
            columnList.innerHTML = '<div class="alert alert-warning">No columns defined for this table.</div>';
            return;
        }
        
        // Create config map for easy lookup
        const configMap = {};
        if (Array.isArray(currentConfig)) {
            log('Processing currentConfig array...');
            currentConfig.forEach((config, index) => {
                log('Processing config ' + index + ': ' + JSON.stringify(config));
                if (typeof config === 'object' && config.column) {
                    configMap[config.column] = config;
                    log('Added to configMap: ' + config.column);
                }
            });
        }
        
        log('Config map created with ' + Object.keys(configMap).length + ' entries');
        
        // Merge with default columns and sort
        const mergedColumns = columns.map(col => {
            const config = configMap[col.id] || {};
            const merged = {
                ...col,
                visible: config.visible !== undefined ? config.visible : true,
                order: config.order !== undefined ? config.order : columns.findIndex(c => c.id === col.id),
                custom_name: config.custom_name || ''
            };
            log('Merged column ' + col.id + ': visible=' + merged.visible + ', order=' + merged.order);
            return merged;
        });
        
        mergedColumns.sort((a, b) => a.order - b.order);
        log('Sorted ' + mergedColumns.length + ' columns');
        
        // Render each column
        mergedColumns.forEach((column, index) => {
            const item = document.createElement('div');
            item.className = 'list-group-item d-flex align-items-center';
            if (!column.visible) {
                item.classList.add('opacity-50');
            }
            
            const customName = column.custom_name || '';
            const displayName = customName || column.name;
            
            item.innerHTML = `
                <div class="me-3">
                    <i class="bi bi-grip-vertical text-muted"></i>
                </div>
                <div class="flex-grow-1">
                    <strong>${displayName}</strong>
                    ${column.required ? '<span class="badge bg-danger ms-2">Required</span>' : ''}
                    ${!column.visible ? '<span class="badge bg-secondary ms-2">Hidden</span>' : ''}
                </div>
                <div>
                    <span class="badge bg-primary">${index + 1}</span>
                </div>
            `;
            
            columnList.appendChild(item);
            log('Rendered column: ' + column.id);
        });
        
        log('Rendering complete! Total items in DOM: ' + columnList.children.length);
    }
    </script>
</body>
</html>