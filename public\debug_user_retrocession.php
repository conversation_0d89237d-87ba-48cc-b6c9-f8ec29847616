<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$userId = $_GET['user_id'] ?? 17;
$month = $_GET['month'] ?? date('n');
$year = $_GET['year'] ?? date('Y');

$db = Flight::db();

echo "<h3>Debug Retrocession for User ID: $userId</h3>";
echo "<p>Checking for Month: $month, Year: $year</p>";

// Check if user exists
$stmt = $db->prepare("SELECT id, full_name, client_id FROM users WHERE id = ?");
$stmt->execute([$userId]);
$user = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$user) {
    die("User not found!");
}

echo "<h4>User Info:</h4>";
echo "<pre>" . print_r($user, true) . "</pre>";

// Check if client is practitioner
if ($user['client_id']) {
    $stmt = $db->prepare("SELECT id, name, is_practitioner FROM clients WHERE id = ?");
    $stmt->execute([$user['client_id']]);
    $client = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<h4>Client Info:</h4>";
    echo "<pre>" . print_r($client, true) . "</pre>";
}

// Check monthly amounts
echo "<h4>Monthly Amounts:</h4>";

// Check if year column exists
$yearCheck = $db->query("SHOW COLUMNS FROM user_monthly_retrocession_amounts LIKE 'year'");
$hasYearColumn = $yearCheck->rowCount() > 0;

if ($hasYearColumn) {
    $stmt = $db->prepare("
        SELECT * FROM user_monthly_retrocession_amounts 
        WHERE user_id = ? AND month = ? AND year = ?
    ");
    $stmt->execute([$userId, $month, $year]);
} else {
    $stmt = $db->prepare("
        SELECT * FROM user_monthly_retrocession_amounts 
        WHERE user_id = ? AND month = ?
    ");
    $stmt->execute([$userId, $month]);
}

$amounts = $stmt->fetch(PDO::FETCH_ASSOC);
echo "<pre>" . print_r($amounts, true) . "</pre>";

// Check ALL monthly amounts for this user
echo "<h4>All Monthly Amounts for User:</h4>";
$stmt = $db->prepare("SELECT * FROM user_monthly_retrocession_amounts WHERE user_id = ? ORDER BY year DESC, month DESC");
$stmt->execute([$userId]);
$allAmounts = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<pre>" . print_r($allAmounts, true) . "</pre>";

// Check retrocession settings
echo "<h4>Retrocession Settings:</h4>";
$stmt = $db->prepare("
    SELECT * FROM user_retrocession_settings 
    WHERE user_id = ? 
    ORDER BY created_at DESC
");
$stmt->execute([$userId]);
$settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<pre>" . print_r($settings, true) . "</pre>";

// Check if invoice already exists
echo "<h4>Existing Invoices:</h4>";
$stmt = $db->prepare("
    SELECT i.id, i.invoice_number, i.status, i.created_at 
    FROM invoices i
    WHERE i.user_id = ? 
    AND i.invoice_type_code = 'retrocession'
    AND MONTH(i.issue_date) = ? 
    AND YEAR(i.issue_date) = ?
    ORDER BY i.created_at DESC
");
$stmt->execute([$userId, $month, $year]);
$invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<pre>" . print_r($invoices, true) . "</pre>";