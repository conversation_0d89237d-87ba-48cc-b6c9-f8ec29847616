<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#2563eb">
    <title>Fit360 AdminDesk - Modern Dashboard</title>
    
    <!-- Bootstrap 5.3 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    
    <style>
        /* ===== CSS Variables for Theming ===== */
        :root {
            --primary-color: #2563eb;
            --primary-hover: #1d4ed8;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-bg: #f9fafb;
            --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --card-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --transition-speed: 0.3s;
            --border-radius: 0.75rem;
            --min-touch-target: 44px;
        }

        /* Dark mode variables */
        [data-theme="dark"] {
            --bg-primary: #111827;
            --bg-secondary: #1f2937;
            --text-primary: #f9fafb;
            --text-secondary: #d1d5db;
            --border-color: #374151;
        }

        /* ===== Base Styles ===== */
        * {
            -webkit-tap-highlight-color: transparent;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: var(--light-bg);
            color: var(--dark-color);
            font-size: 16px;
            line-height: 1.6;
            -webkit-text-size-adjust: 100%;
            padding-bottom: 80px; /* Space for bottom nav on mobile */
        }

        /* ===== Mobile First Typography ===== */
        h1 { font-size: 1.75rem; font-weight: 700; }
        h2 { font-size: 1.5rem; font-weight: 600; }
        h3 { font-size: 1.25rem; font-weight: 600; }
        h4 { font-size: 1.125rem; font-weight: 500; }

        @media (min-width: 768px) {
            h1 { font-size: 2.25rem; }
            h2 { font-size: 1.875rem; }
            h3 { font-size: 1.5rem; }
            h4 { font-size: 1.25rem; }
            body { padding-bottom: 0; }
        }

        /* ===== Layout Components ===== */
        .dashboard-container {
            padding: 1rem;
            max-width: 1400px;
            margin: 0 auto;
        }

        @media (min-width: 768px) {
            .dashboard-container {
                padding: 2rem;
            }
        }

        /* ===== Header Bar ===== */
        .header-bar {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            position: sticky;
            top: 0;
            z-index: 100;
            padding: 1rem;
            margin: -1rem -1rem 1rem -1rem;
        }

        @media (min-width: 768px) {
            .header-bar {
                padding: 1.5rem 2rem;
                margin: -2rem -2rem 2rem -2rem;
            }
        }

        .header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 1400px;
            margin: 0 auto;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* ===== Cards with Enhanced Shadow ===== */
        .dashboard-card {
            background: white;
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            margin-bottom: 1rem;
            overflow: hidden;
        }

        .dashboard-card:hover {
            box-shadow: var(--card-hover-shadow);
            transform: translateY(-2px);
        }

        .card-header {
            padding: 1.25rem;
            border-bottom: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .card-body {
            padding: 1.25rem;
        }

        /* ===== Stats Cards ===== */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 576px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 992px) {
            .stats-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        .stat-card {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            box-shadow: var(--card-shadow);
            transition: all var(--transition-speed) ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
            transition: width var(--transition-speed) ease;
        }

        .stat-card:hover::before {
            width: 8px;
        }

        .stat-card:hover {
            box-shadow: var(--card-hover-shadow);
            transform: translateX(4px);
        }

        .stat-card.primary::before { background: var(--primary-color); }
        .stat-card.success::before { background: var(--success-color); }
        .stat-card.warning::before { background: var(--warning-color); }
        .stat-card.info::before { background: var(--info-color); }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .stat-icon.primary { background: rgba(37, 99, 235, 0.1); color: var(--primary-color); }
        .stat-icon.success { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
        .stat-icon.warning { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
        .stat-icon.info { background: rgba(59, 130, 246, 0.1); color: var(--info-color); }

        .stat-value {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
            line-height: 1;
        }

        .stat-label {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.5rem;
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
        }

        .stat-change.positive {
            color: var(--success-color);
            background: rgba(16, 185, 129, 0.1);
        }

        .stat-change.negative {
            color: var(--danger-color);
            background: rgba(239, 68, 68, 0.1);
        }

        /* ===== Charts Section ===== */
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 992px) {
            .charts-grid {
                grid-template-columns: 2fr 1fr;
            }
        }

        .chart-container {
            position: relative;
            height: 300px;
        }

        @media (max-width: 767px) {
            .chart-container {
                height: 250px;
            }
        }

        /* ===== Quick Actions ===== */
        .quick-actions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.75rem;
            margin-bottom: 2rem;
        }

        @media (min-width: 768px) {
            .quick-actions {
                grid-template-columns: repeat(4, 1fr);
                gap: 1rem;
            }
        }

        .action-btn {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: var(--border-radius);
            padding: 1rem;
            text-align: center;
            text-decoration: none;
            color: var(--dark-color);
            transition: all var(--transition-speed) ease;
            min-height: var(--min-touch-target);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .action-btn:hover {
            border-color: var(--primary-color);
            background: rgba(37, 99, 235, 0.05);
            transform: translateY(-2px);
            box-shadow: var(--card-shadow);
        }

        .action-btn i {
            font-size: 1.5rem;
            color: var(--primary-color);
        }

        .action-btn span {
            font-size: 0.875rem;
            font-weight: 500;
        }

        /* ===== Activity Feed ===== */
        .activity-feed {
            max-height: 400px;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }

        .activity-item {
            display: flex;
            gap: 1rem;
            padding: 1rem 0;
            border-bottom: 1px solid #f3f4f6;
            transition: background var(--transition-speed) ease;
        }

        .activity-item:hover {
            background: #f9fafb;
            margin: 0 -1.25rem;
            padding: 1rem 1.25rem;
        }

        .activity-item:last-child {
            border-bottom: none;
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .activity-icon.success { background: rgba(16, 185, 129, 0.1); color: var(--success-color); }
        .activity-icon.warning { background: rgba(245, 158, 11, 0.1); color: var(--warning-color); }
        .activity-icon.info { background: rgba(59, 130, 246, 0.1); color: var(--info-color); }
        .activity-icon.danger { background: rgba(239, 68, 68, 0.1); color: var(--danger-color); }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .activity-description {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.25rem;
        }

        .activity-time {
            font-size: 0.75rem;
            color: #9ca3af;
        }

        /* ===== Recent Items Table ===== */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            margin: 0 -1.25rem;
            padding: 0 1.25rem;
        }

        .data-table {
            width: 100%;
            font-size: 0.875rem;
        }

        .data-table th {
            font-weight: 600;
            color: #6b7280;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
            padding: 0.75rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .data-table td {
            padding: 0.75rem;
            border-bottom: 1px solid #f3f4f6;
        }

        .data-table tbody tr {
            transition: background var(--transition-speed) ease;
        }

        .data-table tbody tr:hover {
            background: #f9fafb;
        }

        /* Mobile card view for tables */
        @media (max-width: 767px) {
            .mobile-cards .data-table thead {
                display: none;
            }

            .mobile-cards .data-table tbody tr {
                display: block;
                margin-bottom: 1rem;
                background: white;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 1rem;
                box-shadow: var(--card-shadow);
            }

            .mobile-cards .data-table td {
                display: flex;
                justify-content: space-between;
                padding: 0.5rem 0;
                border: none;
            }

            .mobile-cards .data-table td::before {
                content: attr(data-label);
                font-weight: 600;
                color: #6b7280;
            }
        }

        /* ===== Status Badges ===== */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            gap: 0.25rem;
        }

        .status-badge.paid {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .status-badge.pending {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-color);
        }

        .status-badge.overdue {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .status-badge.draft {
            background: rgba(107, 114, 128, 0.1);
            color: #6b7280;
        }

        /* ===== Loading States ===== */
        .skeleton {
            background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 0.375rem;
        }

        @keyframes loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        .skeleton-text {
            height: 1rem;
            margin-bottom: 0.5rem;
        }

        .skeleton-heading {
            height: 1.5rem;
            width: 60%;
            margin-bottom: 1rem;
        }

        /* ===== Empty States ===== */
        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-icon {
            font-size: 3rem;
            color: #d1d5db;
            margin-bottom: 1rem;
        }

        .empty-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .empty-description {
            color: #6b7280;
            margin-bottom: 1.5rem;
        }

        /* ===== Mobile Navigation ===== */
        .mobile-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            padding: 0.5rem 0;
            z-index: 1000;
            box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        @media (min-width: 768px) {
            .mobile-nav {
                display: none;
            }
        }

        .nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.25rem;
            padding: 0.5rem;
            text-decoration: none;
            color: #6b7280;
            transition: color var(--transition-speed) ease;
            min-height: var(--min-touch-target);
        }

        .nav-item:hover,
        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item i {
            font-size: 1.25rem;
        }

        .nav-item span {
            font-size: 0.625rem;
            font-weight: 500;
        }

        /* ===== Floating Action Button ===== */
        .fab {
            position: fixed;
            bottom: 100px;
            right: 1.5rem;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            border: none;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            z-index: 999;
        }

        .fab:hover {
            background: var(--primary-hover);
            transform: scale(1.1);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        @media (min-width: 768px) {
            .fab {
                bottom: 2rem;
            }
        }

        /* ===== Refresh Button ===== */
        .refresh-btn {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 0.5rem 1rem;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: #6b7280;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
        }

        .refresh-btn:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .refresh-btn.spinning i {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* ===== Dark Mode Toggle ===== */
        .theme-toggle {
            background: #f3f4f6;
            border: none;
            border-radius: 9999px;
            padding: 0.5rem;
            cursor: pointer;
            transition: all var(--transition-speed) ease;
            min-width: var(--min-touch-target);
            min-height: var(--min-touch-target);
        }

        .theme-toggle:hover {
            background: #e5e7eb;
        }

        /* ===== Utility Classes ===== */
        .d-none { display: none !important; }
        .d-block { display: block !important; }
        .d-flex { display: flex !important; }
        .align-items-center { align-items: center !important; }
        .justify-content-between { justify-content: space-between !important; }
        .gap-2 { gap: 0.5rem !important; }
        .gap-3 { gap: 1rem !important; }
        .mb-0 { margin-bottom: 0 !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .text-end { text-align: end !important; }
        
        /* Responsive utilities */
        @media (max-width: 767px) {
            .mobile-hide { display: none !important; }
            .mobile-full { width: 100% !important; }
        }
        
        @media (min-width: 768px) {
            .desktop-hide { display: none !important; }
        }
    </style>
</head>
<body>
    <!-- Skip to main content for accessibility -->
    <a href="#main-content" class="visually-hidden-focusable">Skip to main content</a>

    <!-- Header Bar -->
    <header class="header-bar">
        <div class="header-content">
            <div class="d-flex align-items-center gap-3">
                <h1 class="mb-0">Dashboard</h1>
                <button class="refresh-btn" id="refreshDashboard" aria-label="Refresh dashboard">
                    <i class="bi bi-arrow-clockwise"></i>
                    <span class="mobile-hide">Refresh</span>
                </button>
            </div>
            <div class="user-info">
                <button class="theme-toggle" id="themeToggle" aria-label="Toggle dark mode">
                    <i class="bi bi-moon"></i>
                </button>
                <div class="user-avatar">JD</div>
                <div class="mobile-hide">
                    <div class="fw-semibold">John Doe</div>
                    <div class="text-muted small">Administrator</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="dashboard-container">
        <!-- Welcome Section -->
        <div class="dashboard-card mb-3">
            <div class="card-body">
                <h2 class="mb-2">Welcome back, John!</h2>
                <p class="text-muted mb-0">Here's what's happening with your health center today.</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <a href="#" class="action-btn" aria-label="Create new invoice">
                <i class="bi bi-file-earmark-plus"></i>
                <span>New Invoice</span>
            </a>
            <a href="#" class="action-btn" aria-label="Add new patient">
                <i class="bi bi-person-plus"></i>
                <span>Add Patient</span>
            </a>
            <a href="#" class="action-btn" aria-label="View appointments">
                <i class="bi bi-calendar-check"></i>
                <span>Appointments</span>
            </a>
            <a href="#" class="action-btn" aria-label="Generate reports">
                <i class="bi bi-graph-up"></i>
                <span>Reports</span>
            </a>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
            <div class="stat-card primary" role="button" tabindex="0" aria-label="Total patients: 1,248">
                <div class="stat-icon primary">
                    <i class="bi bi-people"></i>
                </div>
                <div class="stat-value">1,248</div>
                <div class="stat-label">Total Patients</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>12% from last month</span>
                </div>
            </div>

            <div class="stat-card success" role="button" tabindex="0" aria-label="Revenue this month: €45,231">
                <div class="stat-icon success">
                    <i class="bi bi-currency-euro"></i>
                </div>
                <div class="stat-value">€45,231</div>
                <div class="stat-label">Monthly Revenue</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>8% increase</span>
                </div>
            </div>

            <div class="stat-card warning" role="button" tabindex="0" aria-label="Pending invoices: 23">
                <div class="stat-icon warning">
                    <i class="bi bi-clock-history"></i>
                </div>
                <div class="stat-value">23</div>
                <div class="stat-label">Pending Invoices</div>
                <div class="stat-change negative">
                    <i class="bi bi-arrow-down"></i>
                    <span>3 overdue</span>
                </div>
            </div>

            <div class="stat-card info" role="button" tabindex="0" aria-label="Today's appointments: 18">
                <div class="stat-icon info">
                    <i class="bi bi-calendar3"></i>
                </div>
                <div class="stat-value">18</div>
                <div class="stat-label">Today's Appointments</div>
                <div class="stat-change positive">
                    <i class="bi bi-arrow-up"></i>
                    <span>Full schedule</span>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="charts-grid">
            <!-- Revenue Chart -->
            <div class="dashboard-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="mb-0">Revenue Overview</h3>
                    <div class="btn-group btn-group-sm" role="group" aria-label="Time period">
                        <button type="button" class="btn btn-outline-secondary active">Week</button>
                        <button type="button" class="btn btn-outline-secondary">Month</button>
                        <button type="button" class="btn btn-outline-secondary">Year</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="revenueChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Invoice Status Chart -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3 class="mb-0">Invoice Status</h3>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusChart"></canvas>
                    </div>
                    <div class="mt-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="status-badge paid">
                                <i class="bi bi-check-circle-fill"></i> Paid
                            </span>
                            <strong>145</strong>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="status-badge pending">
                                <i class="bi bi-clock-fill"></i> Pending
                            </span>
                            <strong>23</strong>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span class="status-badge overdue">
                                <i class="bi bi-exclamation-circle-fill"></i> Overdue
                            </span>
                            <strong>3</strong>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity & Invoices -->
        <div class="row">
            <div class="col-lg-6 mb-3">
                <!-- Recent Activity -->
                <div class="dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">Recent Activity</h3>
                        <a href="#" class="text-primary text-decoration-none small">View all</a>
                    </div>
                    <div class="card-body">
                        <div class="activity-feed">
                            <div class="activity-item">
                                <div class="activity-icon success">
                                    <i class="bi bi-check-lg"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Payment received</div>
                                    <div class="activity-description">Invoice #2024-0156 paid by Marie Dupont</div>
                                    <div class="activity-time">2 minutes ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon info">
                                    <i class="bi bi-person-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">New patient registered</div>
                                    <div class="activity-description">Jean Martin added to patient database</div>
                                    <div class="activity-time">15 minutes ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon warning">
                                    <i class="bi bi-file-earmark"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Invoice created</div>
                                    <div class="activity-description">New invoice #2024-0157 for Pierre Bernard</div>
                                    <div class="activity-time">1 hour ago</div>
                                </div>
                            </div>
                            <div class="activity-item">
                                <div class="activity-icon danger">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-title">Payment overdue</div>
                                    <div class="activity-description">Invoice #2024-0145 is 3 days overdue</div>
                                    <div class="activity-time">2 hours ago</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-3">
                <!-- Recent Invoices -->
                <div class="dashboard-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="mb-0">Recent Invoices</h3>
                        <a href="#" class="text-primary text-decoration-none small">View all</a>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive mobile-cards">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>Invoice</th>
                                        <th>Client</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td data-label="Invoice">#2024-0157</td>
                                        <td data-label="Client">Pierre Bernard</td>
                                        <td data-label="Amount">€235.00</td>
                                        <td data-label="Status">
                                            <span class="status-badge draft">Draft</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td data-label="Invoice">#2024-0156</td>
                                        <td data-label="Client">Marie Dupont</td>
                                        <td data-label="Amount">€189.50</td>
                                        <td data-label="Status">
                                            <span class="status-badge paid">Paid</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td data-label="Invoice">#2024-0155</td>
                                        <td data-label="Client">Jacques Martin</td>
                                        <td data-label="Amount">€421.00</td>
                                        <td data-label="Status">
                                            <span class="status-badge pending">Pending</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td data-label="Invoice">#2024-0154</td>
                                        <td data-label="Client">Sophie Dubois</td>
                                        <td data-label="Amount">€567.25</td>
                                        <td data-label="Status">
                                            <span class="status-badge overdue">Overdue</span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loading State Example -->
        <div class="dashboard-card d-none" id="loadingExample">
            <div class="card-header">
                <div class="skeleton skeleton-heading"></div>
            </div>
            <div class="card-body">
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text"></div>
                <div class="skeleton skeleton-text" style="width: 70%;"></div>
            </div>
        </div>

        <!-- Empty State Example -->
        <div class="dashboard-card d-none" id="emptyExample">
            <div class="card-body">
                <div class="empty-state">
                    <div class="empty-icon">
                        <i class="bi bi-inbox"></i>
                    </div>
                    <h4 class="empty-title">No data available</h4>
                    <p class="empty-description">There's no data to display at the moment.</p>
                    <button class="btn btn-primary">Add New Item</button>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Bottom Navigation -->
    <nav class="mobile-nav" role="navigation" aria-label="Mobile navigation">
        <a href="#" class="nav-item active" aria-current="page">
            <i class="bi bi-house-door"></i>
            <span>Home</span>
        </a>
        <a href="#" class="nav-item">
            <i class="bi bi-people"></i>
            <span>Patients</span>
        </a>
        <a href="#" class="nav-item">
            <i class="bi bi-file-earmark-text"></i>
            <span>Invoices</span>
        </a>
        <a href="#" class="nav-item">
            <i class="bi bi-calendar3"></i>
            <span>Calendar</span>
        </a>
        <a href="#" class="nav-item">
            <i class="bi bi-three-dots"></i>
            <span>More</span>
        </a>
    </nav>

    <!-- Floating Action Button -->
    <button class="fab" aria-label="Create new invoice">
        <i class="bi bi-plus"></i>
    </button>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    
    <script>
        // Dashboard functionality
        (function() {
            'use strict';

            // Theme management
            const themeToggle = document.getElementById('themeToggle');
            const currentTheme = localStorage.getItem('theme') || 'light';
            
            function setTheme(theme) {
                document.documentElement.setAttribute('data-theme', theme);
                localStorage.setItem('theme', theme);
                themeToggle.innerHTML = theme === 'dark' ? '<i class="bi bi-sun"></i>' : '<i class="bi bi-moon"></i>';
            }

            setTheme(currentTheme);

            themeToggle.addEventListener('click', () => {
                const newTheme = document.documentElement.getAttribute('data-theme') === 'dark' ? 'light' : 'dark';
                setTheme(newTheme);
            });

            // Refresh functionality
            const refreshBtn = document.getElementById('refreshDashboard');
            refreshBtn.addEventListener('click', () => {
                refreshBtn.classList.add('spinning');
                
                // Simulate refresh
                setTimeout(() => {
                    refreshBtn.classList.remove('spinning');
                    showToast('Dashboard refreshed successfully');
                }, 1500);
            });

            // Initialize charts
            function initCharts() {
                // Revenue Chart
                const revenueCtx = document.getElementById('revenueChart').getContext('2d');
                new Chart(revenueCtx, {
                    type: 'line',
                    data: {
                        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                        datasets: [{
                            label: 'Revenue',
                            data: [3200, 4500, 3800, 5200, 4900, 6100, 5800],
                            borderColor: '#2563eb',
                            backgroundColor: 'rgba(37, 99, 235, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            tooltip: {
                                mode: 'index',
                                intersect: false,
                                callbacks: {
                                    label: function(context) {
                                        return '€' + context.parsed.y.toLocaleString();
                                    }
                                }
                            }
                        },
                        scales: {
                            x: {
                                grid: {
                                    display: false
                                }
                            },
                            y: {
                                ticks: {
                                    callback: function(value) {
                                        return '€' + value.toLocaleString();
                                    }
                                }
                            }
                        }
                    }
                });

                // Status Chart
                const statusCtx = document.getElementById('statusChart').getContext('2d');
                new Chart(statusCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Paid', 'Pending', 'Overdue'],
                        datasets: [{
                            data: [145, 23, 3],
                            backgroundColor: [
                                '#10b981',
                                '#f59e0b',
                                '#ef4444'
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }

            // Initialize charts when DOM is ready
            if (typeof Chart !== 'undefined') {
                initCharts();
            }

            // Toast notification
            function showToast(message) {
                const toast = document.createElement('div');
                toast.className = 'position-fixed top-0 start-50 translate-middle-x mt-3 bg-dark text-white px-4 py-2 rounded-3';
                toast.style.zIndex = '9999';
                toast.textContent = message;
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.remove();
                }, 3000);
            }

            // Mobile touch interactions
            let touchStartY = 0;
            let touchEndY = 0;

            document.addEventListener('touchstart', e => {
                touchStartY = e.changedTouches[0].screenY;
            });

            document.addEventListener('touchend', e => {
                touchEndY = e.changedTouches[0].screenY;
                handleSwipe();
            });

            function handleSwipe() {
                if (touchStartY < 50 && touchEndY > 100) {
                    // Pull to refresh
                    if (touchEndY - touchStartY > 100) {
                        refreshBtn.click();
                    }
                }
            }

            // Keyboard navigation
            document.addEventListener('keydown', e => {
                if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    refreshBtn.click();
                }
            });

            // Stats card interactions
            document.querySelectorAll('.stat-card').forEach(card => {
                card.addEventListener('click', () => {
                    showToast('Opening detailed view...');
                });

                card.addEventListener('keypress', e => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        card.click();
                    }
                });
            });

            // Mobile navigation active state
            document.querySelectorAll('.nav-item').forEach(item => {
                item.addEventListener('click', e => {
                    e.preventDefault();
                    document.querySelectorAll('.nav-item').forEach(nav => nav.classList.remove('active'));
                    item.classList.add('active');
                });
            });

            // FAB action
            document.querySelector('.fab').addEventListener('click', () => {
                showToast('Opening new invoice form...');
            });

            // Period selector for revenue chart
            document.querySelectorAll('.btn-group button').forEach(btn => {
                btn.addEventListener('click', () => {
                    document.querySelectorAll('.btn-group button').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                    showToast(`Showing ${btn.textContent.toLowerCase()} data`);
                });
            });

        })();
    </script>
</body>
</html>