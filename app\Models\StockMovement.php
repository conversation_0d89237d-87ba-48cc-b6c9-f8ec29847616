<?php
namespace App\Models;

use App\Core\Model;

class StockMovement extends Model
{
    protected $table = 'stock_movements';
    
    protected $fillable = [
        'item_id',
        'movement_type',
        'quantity',
        'reference_type',
        'reference_id',
        'notes',
        'created_by'
    ];
    
    protected $casts = [
        'item_id' => 'integer',
        'quantity' => 'integer',
        'reference_id' => 'integer',
        'created_by' => 'integer'
    ];
    
    protected $with = ['item', 'user'];
    
    /**
     * Get the item
     */
    public function item()
    {
        return $this->belongsTo(CatalogItem::class, 'item_id');
    }
    
    /**
     * Get the user who created the movement
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
    
    /**
     * Get the reference model
     */
    public function reference()
    {
        if ($this->reference_type && $this->reference_id) {
            return $this->morphTo('reference', 'reference_type', 'reference_id');
        }
        return null;
    }
    
    /**
     * Scope for sales movements
     */
    public function scopeSales($query)
    {
        return $query->where('movement_type', 'sale');
    }
    
    /**
     * Scope for purchase movements
     */
    public function scopePurchases($query)
    {
        return $query->where('movement_type', 'purchase');
    }
    
    /**
     * Scope for adjustments
     */
    public function scopeAdjustments($query)
    {
        return $query->where('movement_type', 'adjustment');
    }
    
    /**
     * Scope for returns
     */
    public function scopeReturns($query)
    {
        return $query->where('movement_type', 'return');
    }
    
    /**
     * Scope for positive movements (stock increases)
     */
    public function scopePositive($query)
    {
        return $query->where('quantity', '>', 0);
    }
    
    /**
     * Scope for negative movements (stock decreases)
     */
    public function scopeNegative($query)
    {
        return $query->where('quantity', '<', 0);
    }
    
    /**
     * Get movement type label
     */
    public function getTypeLabel()
    {
        $labels = [
            'sale' => 'Sale',
            'purchase' => 'Purchase',
            'adjustment' => 'Adjustment',
            'return' => 'Return'
        ];
        
        return $labels[$this->movement_type] ?? $this->movement_type;
    }
    
    /**
     * Get movement type icon
     */
    public function getTypeIcon()
    {
        $icons = [
            'sale' => 'fa-shopping-cart',
            'purchase' => 'fa-truck',
            'adjustment' => 'fa-edit',
            'return' => 'fa-undo'
        ];
        
        return $icons[$this->movement_type] ?? 'fa-exchange-alt';
    }
    
    /**
     * Get movement type color
     */
    public function getTypeColor()
    {
        $colors = [
            'sale' => 'success',
            'purchase' => 'primary',
            'adjustment' => 'warning',
            'return' => 'info'
        ];
        
        return $colors[$this->movement_type] ?? 'secondary';
    }
    
    /**
     * Format quantity with sign
     */
    public function formatQuantity()
    {
        return ($this->quantity > 0 ? '+' : '') . $this->quantity;
    }
    
    /**
     * Get reference description
     */
    public function getReferenceDescription()
    {
        if (!$this->reference_type || !$this->reference_id) {
            return '-';
        }
        
        // Handle different reference types
        switch ($this->reference_type) {
            case 'App\\Models\\SalesInvoice':
                $invoice = SalesInvoice::find($this->reference_id);
                return $invoice ? "Invoice #{$invoice->invoice_number}" : "Invoice #{$this->reference_id}";
                
            case 'sales_invoice':
                return "Invoice #{$this->reference_id}";
                
            default:
                return "{$this->reference_type} #{$this->reference_id}";
        }
    }
    
    /**
     * Get stock level after this movement
     */
    public function getStockAfter()
    {
        // Calculate stock level after this movement
        $previousMovements = static::where('item_id', $this->item_id)
                                  ->where('created_at', '<=', $this->created_at)
                                  ->where('id', '<=', $this->id)
                                  ->sum('quantity');
        
        return $previousMovements;
    }
    
    /**
     * Create movement from sale
     */
    public static function createFromSale($item, $quantity, $invoice = null, $notes = null)
    {
        return static::create([
            'item_id' => $item->id,
            'movement_type' => 'sale',
            'quantity' => -abs($quantity),
            'reference_type' => $invoice ? get_class($invoice) : null,
            'reference_id' => $invoice ? $invoice->id : null,
            'notes' => $notes,
            'created_by' => auth()->id()
        ]);
    }
}