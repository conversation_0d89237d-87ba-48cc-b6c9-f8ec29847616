<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;

$invoiceNumber = $_GET['number'] ?? 'FAC-2025-00064';

// Get invoice
$db = Flight::db();
$stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = ?");
$stmt->execute([$invoiceNumber]);
$result = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$result) {
    die("Invoice not found");
}

$invoiceId = $result['id'];
$invoiceModel = new Invoice();
$invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);

echo "<h1>Invoice Data Structure</h1>";
echo "<pre>";
print_r($invoice);
echo "</pre>";

// Check what's missing
echo "<h2>Data Check:</h2>";
echo "<ul>";
echo "<li>Has client data: " . (isset($invoice['client']) ? 'YES' : 'NO') . "</li>";
echo "<li>Has lines/items: " . (isset($invoice['lines']) && count($invoice['lines']) > 0 ? 'YES (' . count($invoice['lines']) . ' items)' : 'NO') . "</li>";
echo "<li>Invoice type: " . ($invoice['invoice_type'] ?? 'NOT SET') . "</li>";
echo "<li>Document type ID: " . ($invoice['document_type_id'] ?? 'NOT SET') . "</li>";
echo "</ul>";

// Try to generate PDF with minimal data
echo "<h2>Attempting PDF Generation:</h2>";

try {
    // Ensure required fields exist
    if (!isset($invoice['invoice_type'])) {
        $invoice['invoice_type'] = 'service';
    }
    if (!isset($invoice['client'])) {
        $invoice['client'] = [
            'name' => 'Test Client',
            'email' => '<EMAIL>',
            'address' => 'Test Address'
        ];
    }
    if (!isset($invoice['lines']) || empty($invoice['lines'])) {
        echo "<p style='color: red;'>Warning: No invoice lines found!</p>";
    }
    
    // Add VAT rate if missing from lines
    if (isset($invoice['lines'])) {
        foreach ($invoice['lines'] as &$line) {
            if (!isset($line['vat_rate']) && isset($line['vat_rate_id'])) {
                // Get VAT rate from DB
                $stmt = $db->prepare("SELECT rate FROM config_vat_rates WHERE id = ?");
                $stmt->execute([$line['vat_rate_id']]);
                $vatData = $stmt->fetch(PDO::FETCH_ASSOC);
                $line['vat_rate'] = $vatData ? $vatData['rate'] : 0;
            }
        }
    }
    
    echo "<p>Invoice data prepared for PDF generation.</p>";
    echo "<p><a href='/fit/public/download_invoice_pdf.php?id={$invoiceId}' class='btn btn-primary'>Try Download Now</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}