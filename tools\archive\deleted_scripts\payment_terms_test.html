<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Payment Terms Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Payment Terms Modal Test</h1>
        
        <p>This page tests the payment terms modal to see if the issue is with translation or JavaScript.</p>
        
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#paymentTermModal">
            Open Modal
        </button>
        
        <!-- Modal copied from payment-terms-modern.twig -->
        <div class="modal fade" id="paymentTermModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <form id="paymentTermForm">
                        <input type="hidden" name="csrf_token" value="test">
                        <input type="hidden" id="paymentTermId" name="id" value="">
                        
                        <div class="modal-header">
                            <h5 class="modal-title" id="paymentTermModalTitle">Ajouter une condition de paiement</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="name" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <small class="text-muted">ex: Net 30, Paiement à réception</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="code" class="form-label">Code *</label>
                                <input type="text" class="form-control" id="code" name="code" required pattern="[a-zA-Z0-9_\-]+" maxlength="50">
                                <small class="text-muted">Identifiant unique (lettres, chiffres, tirets bas)</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="days" class="form-label">Jours de paiement *</label>
                                <input type="number" class="form-control" id="days" name="days" min="0" max="365" value="0" required>
                                <small class="text-muted">Nombre de jours à partir de la date de facturation (0 pour immédiat)</small>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                                <small class="text-muted">Détails supplémentaires sur cette condition de paiement</small>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input type="checkbox" class="form-check-input" id="is_default" name="is_default" value="1">
                                <label class="form-check-label" for="is_default">
                                    Définir par défaut
                                </label>
                                <small class="text-muted d-block">Cette condition de paiement sera sélectionnée par défaut pour les nouvelles factures</small>
                            </div>
                            
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="is_active">
                                    Actif
                                </label>
                            </div>
                        </div>
                        
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Enregistrer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <hr class="my-5">
        
        <h2>Test Edit Function</h2>
        <p>Click the button below to test the edit function with sample data:</p>
        <button type="button" class="btn btn-warning" onclick="testEdit()">
            Test Edit Payment Term
        </button>
        
        <div id="debug" class="mt-3"></div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let paymentTermModal;
        
        document.addEventListener('DOMContentLoaded', function() {
            paymentTermModal = new bootstrap.Modal(document.getElementById('paymentTermModal'));
        });
        
        function testEdit() {
            // Test with values that might be causing issues
            document.getElementById('paymentTermId').value = 1;
            document.getElementById('paymentTermModalTitle').textContent = 'Modifier la condition de paiement';
            document.getElementById('name').value = 'Net 30';
            document.getElementById('code').value = 'net_30';
            document.getElementById('code').readOnly = true;
            document.getElementById('days').value = 30;
            document.getElementById('description').value = 'Paiement dans 30 jours';
            document.getElementById('is_default').checked = false;
            document.getElementById('is_active').checked = true;
            
            // Show debug info
            document.getElementById('debug').innerHTML = `
                <div class="alert alert-info">
                    <h5>Values set in form:</h5>
                    <ul>
                        <li>Name: ${document.getElementById('name').value}</li>
                        <li>Code: ${document.getElementById('code').value}</li>
                        <li>Days: ${document.getElementById('days').value}</li>
                        <li>Description: ${document.getElementById('description').value}</li>
                    </ul>
                </div>
            `;
            
            paymentTermModal.show();
        }
    </script>
</body>
</html>