{% extends "base-modern.twig" %}

{% block title %}{{ __('config.document_types') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.document_types') }}</h1>
        <div>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_document_type') }}
            </button>
        </div>
    </div>

    <!-- Document Types Table -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="documentTypesTable">
                    <thead>
                        <tr>
                            <th>{{ __('config.document_type_code') }}</th>
                            <th>{{ __('common.name') }}</th>
                            <th>{{ __('config.prefix') }}</th>
                            <th>{{ __('config.counter_type') }}</th>
                            <th>{{ __('config.properties') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for type in documentTypes %}
                        <tr data-id="{{ type.id }}">
                            <td>
                                <code>{{ type.code }}</code>
                            </td>
                            <td>
                                <span class="badge" style="background-color: {{ type.color }};">
                                    <i class="{{ type.icon }} me-1"></i>
                                    {{ type.name[current_language()] ?? type.name.fr ?? type.code }}
                                </span>
                            </td>
                            <td>{{ type.prefix }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ __('config.counter_' ~ type.counter_type) }}</span>
                            </td>
                            <td>
                                {% if type.is_negative %}
                                    <span class="badge bg-danger" title="{{ __('config.negative_amounts') }}">
                                        <i class="bi bi-dash-circle"></i>
                                    </span>
                                {% endif %}
                                {% if type.requires_reference %}
                                    <span class="badge bg-info" title="{{ __('config.requires_reference') }}">
                                        <i class="bi bi-link-45deg"></i>
                                    </span>
                                {% endif %}
                            </td>
                            <td>
                                {% if type.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary" onclick="editDocumentType({{ type.id }})" 
                                        {% if type.is_system %}disabled title="{{ __('config.system_type_locked') }}"{% endif %}>
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteDocumentType({{ type.id }})"
                                        {% if type.is_system %}disabled title="{{ __('config.system_type_locked') }}"{% endif %}>
                                    <i class="bi bi-trash"></i>
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/config/document-types" id="createForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('config.add_document_type') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="code" class="form-label">{{ __('config.document_type_code') }} *</label>
                            <input type="text" class="form-control" id="code" name="code" required pattern="[a-z_]+" 
                                   placeholder="invoice, credit_note, quote">
                            <small class="text-muted">{{ __('config.code_hint') }}</small>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="prefix" class="form-label">{{ __('config.prefix') }} *</label>
                            <input type="text" class="form-control" id="prefix" name="prefix" required maxlength="20"
                                   placeholder="FAC, AV, DEV">
                        </div>
                        
                        <div class="col-md-4">
                            <label for="counter_type" class="form-label">{{ __('config.counter_type') }} *</label>
                            <select class="form-select" id="counter_type" name="counter_type" required>
                                <option value="yearly">{{ __('config.counter_yearly') }}</option>
                                <option value="monthly">{{ __('config.counter_monthly') }}</option>
                                <option value="global">{{ __('config.counter_global') }}</option>
                            </select>
                        </div>
                        
                        <div class="col-12">
                            <h6>{{ __('config.multilingual_name') }} *</h6>
                            <div class="row g-2">
                                {% for lang, label in languages %}
                                <div class="col-md-3">
                                    <label for="name_{{ lang }}" class="form-label">{{ label }}</label>
                                    <input type="text" class="form-control" id="name_{{ lang }}" name="name_{{ lang }}" 
                                           {% if lang == 'fr' %}required{% endif %}>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="col-12">
                            <h6>{{ __('config.multilingual_description') }}</h6>
                            <div class="row g-2">
                                {% for lang, label in languages %}
                                <div class="col-md-6">
                                    <label for="description_{{ lang }}" class="form-label">{{ label }}</label>
                                    <textarea class="form-control" id="description_{{ lang }}" name="description_{{ lang }}" rows="2"></textarea>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="color" class="form-label">{{ __('common.color') }}</label>
                            <input type="color" class="form-control" id="color" name="color" value="#6c757d">
                        </div>
                        
                        <div class="col-md-4">
                            <label for="icon" class="form-label">{{ __('config.icon') }}</label>
                            <div class="input-group">
                                <span class="input-group-text" id="iconPreview"><i class="bi bi-file-text"></i></span>
                                <input type="text" class="form-control" id="icon" name="icon" value="bi bi-file-text">
                            </div>
                            <small class="text-muted">{{ __('config.icon_hint') }}</small>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="sort_order" class="form-label">{{ __('config.sort_order') }}</label>
                            <input type="number" class="form-control" id="sort_order" name="sort_order" value="0">
                        </div>
                        
                        <div class="col-12">
                            <h6>{{ __('config.properties') }}</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_negative" name="is_negative" value="1">
                                <label class="form-check-label" for="is_negative">
                                    {{ __('config.negative_amounts') }}
                                    <small class="text-muted">{{ __('config.negative_amounts_hint') }}</small>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="requires_reference" name="requires_reference" value="1">
                                <label class="form-check-label" for="requires_reference">
                                    {{ __('config.requires_reference') }}
                                    <small class="text-muted">{{ __('config.requires_reference_hint') }}</small>
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" value="1" checked>
                                <label class="form-check-label" for="is_active">
                                    {{ __('common.active') }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-12" id="allowedReferencesDiv" style="display: none;">
                            <label for="allowed_references" class="form-label">{{ __('config.allowed_references') }}</label>
                            <input type="text" class="form-control" id="allowed_references" name="allowed_references" 
                                   placeholder="invoice,quote">
                            <small class="text-muted">{{ __('config.allowed_references_hint') }}</small>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" id="editForm">
                <input type="hidden" name="_method" value="PUT">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" id="edit_id">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('config.edit_document_type') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <!-- Same fields as create modal -->
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Document types data for editing
const documentTypes = {{ documentTypes|json_encode|raw }};

// Icon preview
document.getElementById('icon').addEventListener('input', function() {
    document.getElementById('iconPreview').innerHTML = '<i class="' + this.value + '"></i>';
});

// Toggle allowed references based on checkbox
document.getElementById('requires_reference').addEventListener('change', function() {
    document.getElementById('allowedReferencesDiv').style.display = this.checked ? 'block' : 'none';
});

function editDocumentType(id) {
    const type = documentTypes.find(t => t.id == id);
    if (!type) return;
    
    // Fill edit form
    document.getElementById('edit_id').value = id;
    document.getElementById('editForm').action = '{{ base_url }}/config/document-types/' + id;
    
    // Copy create modal body to edit modal
    const createBody = document.querySelector('#createModal .modal-body').innerHTML;
    document.querySelector('#editModal .modal-body').innerHTML = createBody;
    
    // Add edit prefix to all IDs in edit modal
    document.querySelectorAll('#editModal [id]').forEach(el => {
        el.id = 'edit_' + el.id;
        if (el.name) el.name = el.name; // Keep same name for form submission
    });
    
    // Fill values
    document.getElementById('edit_code').value = type.code;
    document.getElementById('edit_code').readOnly = type.is_system;
    document.getElementById('edit_prefix').value = type.prefix;
    document.getElementById('edit_counter_type').value = type.counter_type;
    document.getElementById('edit_color').value = type.color;
    document.getElementById('edit_icon').value = type.icon;
    document.getElementById('edit_sort_order').value = type.sort_order;
    document.getElementById('edit_is_negative').checked = type.is_negative == 1;
    document.getElementById('edit_requires_reference').checked = type.requires_reference == 1;
    document.getElementById('edit_is_active').checked = type.is_active == 1;
    
    // Fill multilingual fields
    {% for lang in languages|keys %}
    if (type.name && type.name['{{ lang }}']) {
        document.getElementById('edit_name_{{ lang }}').value = type.name['{{ lang }}'];
    }
    if (type.description && type.description['{{ lang }}']) {
        document.getElementById('edit_description_{{ lang }}').value = type.description['{{ lang }}'];
    }
    {% endfor %}
    
    // Fill allowed references
    if (type.allowed_references) {
        document.getElementById('edit_allowed_references').value = type.allowed_references.join(',');
    }
    
    // Show/hide allowed references
    document.getElementById('edit_allowedReferencesDiv').style.display = 
        type.requires_reference == 1 ? 'block' : 'none';
    
    // Re-attach event listeners for edit modal
    document.getElementById('edit_icon').addEventListener('input', function() {
        document.getElementById('edit_iconPreview').innerHTML = '<i class="' + this.value + '"></i>';
    });
    
    document.getElementById('edit_requires_reference').addEventListener('change', function() {
        document.getElementById('edit_allowedReferencesDiv').style.display = this.checked ? 'block' : 'none';
    });
    
    // Update icon preview
    document.getElementById('edit_iconPreview').innerHTML = '<i class="' + type.icon + '"></i>';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

function deleteDocumentType(id) {
    if (confirm('{{ __("config.delete_document_type_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/config/document-types/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>

{% endblock %}