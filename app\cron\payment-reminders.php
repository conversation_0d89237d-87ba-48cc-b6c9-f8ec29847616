#!/usr/bin/env php
<?php
/**
 * Payment Reminder Cron Job
 * 
 * Run this script daily to check for overdue invoices and send payment reminders
 * Usage: php /path/to/payment-reminders.php
 * 
 * Recommended cron schedule: 0 9 * * * (daily at 9 AM)
 */

// Bootstrap the application
define('APP_PATH', dirname(dirname(__DIR__)));
require APP_PATH . '/vendor/autoload.php';

// Load environment variables
if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
}

// Load bootstrap
require APP_PATH . '/app/config/bootstrap.php';

use App\Services\ReminderService;

// Check if running from CLI
if (php_sapi_name() !== 'cli') {
    die("This script can only be run from the command line\n");
}

echo "Payment Reminder Job Started at " . date('Y-m-d H:i:s') . "\n";
echo str_repeat('-', 50) . "\n";

try {
    // Initialize reminder service
    $reminderService = new ReminderService();
    
    // Check and send reminders
    $results = $reminderService->checkOverdueInvoices();
    
    // Output results
    if ($results['status'] === 'disabled') {
        echo "Payment reminders are currently disabled in settings.\n";
    } else {
        echo "Invoices checked: " . $results['checked'] . "\n";
        echo "Reminders sent: " . $results['sent'] . "\n";
        echo "Errors: " . $results['errors'] . "\n";
        
        if (!empty($results['details'])) {
            echo "\nReminders sent to:\n";
            foreach ($results['details'] as $detail) {
                echo sprintf(
                    "- %s (%s) - Reminder #%d - %d days overdue\n",
                    $detail['invoice'],
                    $detail['client'],
                    $detail['reminder'],
                    $detail['days_overdue']
                );
            }
        }
    }
    
    // Log to file
    $logFile = APP_PATH . '/storage/logs/payment-reminders.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $logEntry = sprintf(
        "[%s] Checked: %d, Sent: %d, Errors: %d\n",
        date('Y-m-d H:i:s'),
        $results['checked'] ?? 0,
        $results['sent'] ?? 0,
        $results['errors'] ?? 0
    );
    
    file_put_contents($logFile, $logEntry, FILE_APPEND);
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    error_log("Payment reminder cron error: " . $e->getMessage());
    exit(1);
}

echo str_repeat('-', 50) . "\n";
echo "Payment Reminder Job Completed at " . date('Y-m-d H:i:s') . "\n";
exit(0);