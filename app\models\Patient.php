<?php

namespace App\Models;

use App\Core\Model;
use App\Models\CustomField;
use App\Models\ConfigSettings;
use App\Helpers\NumberFormat;
use PDO;

class Patient extends Model
{
    protected $table = 'patients';
    protected $fillable = [
        'patient_number', 'first_name', 'last_name', 'birth_date', 'gender',
        'email', 'phone', 'mobile', 'address_line1', 'address_line2',
        'city', 'postal_code', 'country', 'national_id',
        'insurance_provider', 'insurance_number',
        'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',
        'blood_type', 'allergies', 'chronic_conditions', 'current_medications',
        'notes', 'is_active', 'created_by', 'updated_by'
    ];
    
    /**
     * Find patient by ID
     */
    public static function findById($id)
    {
        $db = \Flight::db();
        $stmt = $db->prepare("SELECT * FROM patients WHERE id = :id LIMIT 1");
        $stmt->execute(['id' => $id]);
        $patient = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        // Handle invalid date_of_birth
        if ($patient && isset($patient['date_of_birth'])) {
            if (empty($patient['date_of_birth']) || $patient['date_of_birth'] === '0000-00-00' || $patient['date_of_birth'] === '-0001-11-30') {
                $patient['date_of_birth'] = null;
            }
        }
        
        return $patient;
    }
    
    /**
     * Delete patient by ID
     */
    public function delete($id = null): bool
    {
        if ($id === null && isset($this->id)) {
            $id = $this->id;
        }
        
        if (!$id) {
            return false;
        }
        
        $db = \Flight::db();
        
        try {
            // Delete custom field values
            $stmt = $db->prepare("DELETE FROM custom_field_values WHERE module = 'patients' AND entity_id = :id");
            $stmt->execute(['id' => $id]);
            
            // Delete the patient
            $stmt = $db->prepare("DELETE FROM patients WHERE id = :id");
            return $stmt->execute(['id' => $id]);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Update patient by ID (static method)
     */
    public static function updateById($id, $data): bool
    {
        $db = \Flight::db();
        
        // Remove non-updateable fields
        unset($data['id'], $data['patient_number'], $data['created_at'], $data['created_by']);
        
        // Build update query
        $fields = [];
        foreach ($data as $key => $value) {
            $fields[] = "$key = :$key";
        }
        
        $sql = "UPDATE patients SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = :id";
        $data['id'] = $id;
        
        try {
            $stmt = $db->prepare($sql);
            return $stmt->execute($data);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Create new patient (instance method for controller compatibility)
     */
    public function createNew($data)
    {
        $db = \Flight::db();
        
        // Set timestamps
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['is_active'] = $data['is_active'] ?? 1;
        
        // Build insert query
        $fields = array_keys($data);
        $placeholders = array_map(function($f) { return ":$f"; }, $fields);
        
        $sql = "INSERT INTO patients (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        try {
            $stmt = $db->prepare($sql);
            if ($stmt->execute($data)) {
                return $db->lastInsertId();
            }
            return false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Generate next patient number
     * 
     * @return string
     */
    public static function generatePatientNumber(): string
    {
        // Get the next sequential number
        $nextNumber = NumberFormat::getNextNumber('patient', 'patients', 'patient_number');
        
        // Generate the formatted patient number
        return NumberFormat::generate('patient', $nextNumber);
    }
    
    /**
     * Get all patients with pagination
     * 
     * @param int $page
     * @param int $perPage
     * @param array $filters
     * @return array
     */
    public static function getAllPaginated($page = 1, $perPage = 20, $filters = []): array
    {
        $db = \Flight::db();
        $offset = ($page - 1) * $perPage;
        
        $where = ['1=1'];
        $params = [];
        
        // Apply filters
        if (!empty($filters['search'])) {
            $search = '%' . $filters['search'] . '%';
            $where[] = "(
                patient_number LIKE :search OR 
                first_name LIKE :search OR 
                last_name LIKE :search OR 
                email LIKE :search OR 
                phone LIKE :search
            )";
            $params['search'] = $search;
        }
        
        if (isset($filters['is_active'])) {
            $where[] = "is_active = :is_active";
            $params['is_active'] = $filters['is_active'];
        }
        
        if (!empty($filters['insurance_provider'])) {
            $where[] = "insurance_provider = :insurance_provider";
            $params['insurance_provider'] = $filters['insurance_provider'];
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Get total count
        $countStmt = $db->prepare("
            SELECT COUNT(*) FROM patients WHERE {$whereClause}
        ");
        $countStmt->execute($params);
        $total = $countStmt->fetchColumn();
        
        // Get patients
        $stmt = $db->prepare("
            SELECT p.*, 
                   TIMESTAMPDIFF(YEAR, p.birth_date, CURDATE()) as age,
                   u1.first_name as created_by_name,
                   u2.first_name as updated_by_name
            FROM patients p
            LEFT JOIN users u1 ON p.created_by = u1.id
            LEFT JOIN users u2 ON p.updated_by = u2.id
            WHERE {$whereClause}
            ORDER BY p.created_at DESC
            LIMIT :offset, :limit
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue('offset', $offset, PDO::PARAM_INT);
        $stmt->bindValue('limit', $perPage, PDO::PARAM_INT);
        
        $stmt->execute();
        $patients = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Handle invalid date_of_birth and get custom field values for each patient
        foreach ($patients as &$patient) {
            if (isset($patient['date_of_birth'])) {
                if (empty($patient['date_of_birth']) || $patient['date_of_birth'] === '0000-00-00' || $patient['date_of_birth'] === '-0001-11-30') {
                    $patient['date_of_birth'] = null;
                    $patient['age'] = null;
                }
            }
            $patient['custom_fields'] = CustomField::getCustomFieldValues('patients', $patient['id']);
        }
        
        return [
            'data' => $patients,
            'total' => $total,
            'page' => $page,
            'per_page' => $perPage,
            'total_pages' => ceil($total / $perPage)
        ];
    }
    
    /**
     * Get patient by ID with related data
     * 
     * @param int $id
     * @return array|null
     */
    public static function getWithDetails($id): ?array
    {
        $db = \Flight::db();
        
        // Get patient
        $stmt = $db->prepare("
            SELECT p.*, 
                   TIMESTAMPDIFF(YEAR, p.birth_date, CURDATE()) as age,
                   u1.first_name as created_by_name,
                   u2.first_name as updated_by_name
            FROM patients p
            LEFT JOIN users u1 ON p.created_by = u1.id
            LEFT JOIN users u2 ON p.updated_by = u2.id
            WHERE p.id = :id
        ");
        $stmt->execute(['id' => $id]);
        $patient = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$patient) {
            return null;
        }
        
        // Handle invalid date_of_birth
        if (isset($patient['date_of_birth'])) {
            if (empty($patient['date_of_birth']) || $patient['date_of_birth'] === '0000-00-00' || $patient['date_of_birth'] === '-0001-11-30') {
                $patient['date_of_birth'] = null;
                $patient['age'] = null;
            }
        }
        
        // Get recent notes
        $stmt = $db->prepare("
            SELECT n.*, u.first_name as created_by_name
            FROM patient_notes n
            JOIN users u ON n.created_by = u.id
            WHERE n.patient_id = :patient_id
            ORDER BY n.created_at DESC
            LIMIT 10
        ");
        $stmt->execute(['patient_id' => $id]);
        $patient['recent_notes'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get recent visits
        $stmt = $db->prepare("
            SELECT v.*, u.first_name as created_by_name
            FROM patient_visits v
            JOIN users u ON v.created_by = u.id
            WHERE v.patient_id = :patient_id
            ORDER BY v.visit_date DESC
            LIMIT 10
        ");
        $stmt->execute(['patient_id' => $id]);
        $patient['recent_visits'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get documents count
        $stmt = $db->prepare("
            SELECT COUNT(*) FROM patient_documents WHERE patient_id = :patient_id
        ");
        $stmt->execute(['patient_id' => $id]);
        $patient['documents_count'] = $stmt->fetchColumn();
        
        // Get custom field values
        $patient['custom_fields'] = CustomField::getCustomFieldValues('patients', $id);
        
        return $patient;
    }
    
    /**
     * Add note to patient
     * 
     * @param int $patientId
     * @param string $note
     * @param string $noteType
     * @param int $userId
     * @param bool $isPrivate
     * @return int
     */
    public static function addNote($patientId, $note, $noteType = 'general', $userId = null, $isPrivate = false): int
    {
        $db = \Flight::db();
        $stmt = $db->prepare("
            INSERT INTO patient_notes (patient_id, note_type, note, is_private, created_by)
            VALUES (:patient_id, :note_type, :note, :is_private, :created_by)
        ");
        
        $stmt->execute([
            'patient_id' => $patientId,
            'note_type' => $noteType,
            'note' => $note,
            'is_private' => $isPrivate ? 1 : 0,
            'created_by' => $userId ?: $_SESSION['user_id']
        ]);
        
        return $db->lastInsertId();
    }
    
    /**
     * Get patient notes
     * 
     * @param int $patientId
     * @param int $limit
     * @return array
     */
    public static function getNotes($patientId, $limit = 50): array
    {
        $db = \Flight::db();
        $userId = $_SESSION['user_id'] ?? 0;
        
        $stmt = $db->prepare("
            SELECT n.*, u.first_name as created_by_name, u.last_name as created_by_lastname
            FROM patient_notes n
            JOIN users u ON n.created_by = u.id
            WHERE n.patient_id = :patient_id
            AND (n.is_private = 0 OR n.created_by = :user_id)
            ORDER BY n.created_at DESC
            LIMIT :limit
        ");
        
        $stmt->bindValue('patient_id', $patientId, PDO::PARAM_INT);
        $stmt->bindValue('user_id', $userId, PDO::PARAM_INT);
        $stmt->bindValue('limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Search patients
     * 
     * @param string $query
     * @param int $limit
     * @return array
     */
    public static function search($query, $limit = 10): array
    {
        $db = \Flight::db();
        $search = '%' . $query . '%';
        
        $stmt = $db->prepare("
            SELECT id, patient_number, first_name, last_name, birth_date, phone, email,
                   TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) as age
            FROM patients
            WHERE is_active = 1
            AND (
                patient_number LIKE :search
                OR first_name LIKE :search
                OR last_name LIKE :search
                OR CONCAT(first_name, ' ', last_name) LIKE :search
                OR email LIKE :search
                OR phone LIKE :search
            )
            ORDER BY last_name, first_name
            LIMIT :limit
        ");
        
        $stmt->bindValue('search', $search);
        $stmt->bindValue('limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get insurance providers list
     * 
     * @return array
     */
    public static function getInsuranceProviders(): array
    {
        $db = \Flight::db();
        $stmt = $db->query("
            SELECT DISTINCT insurance_provider 
            FROM patients 
            WHERE insurance_provider IS NOT NULL 
            AND insurance_provider != ''
            ORDER BY insurance_provider
        ");
        
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    
    /**
     * Get patient statistics
     * 
     * @return array
     */
    public static function getStatistics(): array
    {
        $db = \Flight::db();
        
        $stats = [];
        
        // Total patients
        $stmt = $db->query("SELECT COUNT(*) FROM patients WHERE is_active = 1");
        $stats['total_active'] = $stmt->fetchColumn();
        
        // New patients this month
        $stmt = $db->query("
            SELECT COUNT(*) 
            FROM patients 
            WHERE created_at >= DATE_FORMAT(NOW(), '%Y-%m-01')
        ");
        $stats['new_this_month'] = $stmt->fetchColumn();
        
        // Gender distribution
        $stmt = $db->query("
            SELECT gender, COUNT(*) as count 
            FROM patients 
            WHERE is_active = 1 
            GROUP BY gender
        ");
        $stats['by_gender'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Age groups
        $stmt = $db->query("
            SELECT 
                CASE
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) < 18 THEN '0-17'
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) < 30 THEN '18-29'
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) < 50 THEN '30-49'
                    WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) < 65 THEN '50-64'
                    ELSE '65+'
                END as age_group,
                COUNT(*) as count
            FROM patients
            WHERE is_active = 1
            GROUP BY age_group
            ORDER BY age_group
        ");
        $stats['by_age_group'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        return $stats;
    }
}