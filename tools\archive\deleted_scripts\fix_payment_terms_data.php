<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== FIXING PAYMENT TERMS DATA ===\n\n";

try {
    $db = Flight::db();
    
    // Find payment terms with translation keys as values
    $stmt = $db->query("SELECT * FROM config_payment_terms WHERE name LIKE '%config.%' OR name LIKE '%common.%' OR description LIKE '%config.%' OR description LIKE '%common.%'");
    $badTerms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($badTerms) > 0) {
        echo "Found " . count($badTerms) . " payment terms with translation keys as values.\n\n";
        
        foreach ($badTerms as $term) {
            echo "Fixing term ID {$term['id']} (code: {$term['code']}):\n";
            
            // Generate proper name based on code
            $properName = ucwords(str_replace(['_', '-'], ' ', $term['code']));
            $nameJson = json_encode([
                'fr' => $properName,
                'en' => $properName,
                'de' => $properName
            ]);
            
            // Fix description if it contains translation keys
            $description = $term['description'];
            if (strpos($description, 'config.') !== false || strpos($description, 'common.') !== false) {
                $description = json_encode([
                    'fr' => '',
                    'en' => '',
                    'de' => ''
                ]);
            }
            
            // Update the record
            $updateStmt = $db->prepare("UPDATE config_payment_terms SET name = ?, description = ? WHERE id = ?");
            $updateStmt->execute([$nameJson, $description, $term['id']]);
            
            echo "  - Old name: {$term['name']}\n";
            echo "  - New name: $nameJson\n";
            echo "  - Fixed!\n\n";
        }
        
        echo "All payment terms have been fixed.\n";
    } else {
        echo "No payment terms found with translation keys as values.\n";
        echo "The issue might be with the translation system itself.\n\n";
        
        // Create a default payment term if none exist
        $stmt = $db->query("SELECT COUNT(*) FROM config_payment_terms");
        $count = $stmt->fetchColumn();
        
        if ($count == 0) {
            echo "No payment terms exist. Creating default ones...\n\n";
            
            $defaultTerms = [
                ['code' => 'immediate', 'name' => 'Paiement immédiat', 'days' => 0],
                ['code' => 'net_30', 'name' => 'Net 30 jours', 'days' => 30],
                ['code' => 'net_60', 'name' => 'Net 60 jours', 'days' => 60],
            ];
            
            foreach ($defaultTerms as $term) {
                $nameJson = json_encode([
                    'fr' => $term['name'],
                    'en' => str_replace(['Paiement immédiat', 'Net 30 jours', 'Net 60 jours'], 
                                       ['Immediate payment', 'Net 30 days', 'Net 60 days'], 
                                       $term['name']),
                    'de' => str_replace(['Paiement immédiat', 'Net 30 jours', 'Net 60 jours'], 
                                       ['Sofortzahlung', 'Netto 30 Tage', 'Netto 60 Tage'], 
                                       $term['name'])
                ]);
                
                $stmt = $db->prepare("INSERT INTO config_payment_terms (code, name, days, is_active, is_default, sort_order) VALUES (?, ?, ?, 1, ?, ?)");
                $stmt->execute([
                    $term['code'],
                    $nameJson,
                    $term['days'],
                    $term['code'] === 'net_30' ? 1 : 0,
                    $term['days']
                ]);
                
                echo "Created: {$term['code']} - {$term['name']}\n";
            }
        }
    }
    
    echo "\nDone! Please refresh the payment terms page.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}