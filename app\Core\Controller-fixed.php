<?php

namespace App\Core;

use Flight;

abstract class Controller
{
    /**
     * <PERSON><PERSON> a view with the Twig engine
     */
    protected function render($template, $data = [])
    {
        $view = Flight::get('view');
        
        // Add common data
        $data['app_name'] = Flight::get('config')['app_name'] ?? 'Fit360 AdminDesk';
        
        // IMPORTANT: Don't pass the raw session array - it causes issues
        // Instead, pass specific session values
        $data['session_user'] = $_SESSION['user'] ?? null;
        $data['session_user_id'] = $_SESSION['user_id'] ?? null;
        $data['session_user_name'] = $_SESSION['user_name'] ?? null;
        $data['session_user_email'] = $_SESSION['user_email'] ?? null;
        $data['session_template'] = $_SESSION['template'] ?? 'modern';
        
        // For backward compatibility, create a safe session object
        $data['session'] = new \ArrayObject([
            'user' => $_SESSION['user'] ?? null,
            'user_id' => $_SESSION['user_id'] ?? null,
            'user_name' => $_SESSION['user_name'] ?? null,
            'user_email' => $_SESSION['user_email'] ?? null,
            'template' => $_SESSION['template'] ?? 'modern',
            'user_language' => $_SESSION['user_language'] ?? 'en'
        ]);
        
        $data['current_template'] = $this->getTemplate();
        $data['template_name'] = $this->getTemplate();
        $data['base_url'] = Flight::get('flight.base_url');
        $data['current_route'] = Flight::request()->url ?? '/';
        
        // Handle flash messages - ensure they are strings, not arrays
        $data['flash'] = new \ArrayObject([
            'success' => isset($_SESSION['flash']['success']) && is_string($_SESSION['flash']['success']) ? $_SESSION['flash']['success'] : null,
            'error' => isset($_SESSION['flash']['error']) && is_string($_SESSION['flash']['error']) ? $_SESSION['flash']['error'] : null,
            'warning' => isset($_SESSION['flash']['warning']) && is_string($_SESSION['flash']['warning']) ? $_SESSION['flash']['warning'] : null,
            'info' => isset($_SESSION['flash']['info']) && is_string($_SESSION['flash']['info']) ? $_SESSION['flash']['info'] : null,
        ]);
        
        // Clear flash messages after reading
        unset($_SESSION['flash']);
        
        // Add config data - use ArrayObject to prevent array to string conversion
        if (!isset($data['config'])) {
            $data['config'] = new \ArrayObject([
                'currency_symbol' => '€',
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i'
            ]);
        } elseif (is_array($data['config'])) {
            $data['config'] = new \ArrayObject($data['config']);
        }
        
        echo $view->render($template . '.twig', $data);
    }
    
    /**
     * Return JSON response
     */
    protected function json($data, $code = 200)
    {
        Flight::json($data, $code);
    }
    
    /**
     * Redirect to a URL
     */
    protected function redirect($url)
    {
        Flight::redirect($url);
    }
    
    /**
     * Generate URL with base path
     */
    protected function url($path = '')
    {
        $baseUrl = Flight::get('flight.base_url');
        // Remove trailing slash from base URL
        $baseUrl = rtrim($baseUrl, '/');
        // Ensure path starts with /
        if ($path && $path[0] !== '/') {
            $path = '/' . $path;
        }
        return $baseUrl . $path;
    }
    
    /**
     * Get request data
     */
    protected function getRequestData()
    {
        $data = Flight::request()->data->getData();
        
        // If Flight data is empty and this is a PUT request, check $_POST
        if (empty($data) && in_array(Flight::request()->method, ['PUT', 'PATCH', 'DELETE'])) {
            $data = $_POST;
        }
        
        return $data;
    }
    
    /**
     * Check if request is AJAX
     */
    protected function isAjax()
    {
        return Flight::request()->ajax;
    }
    
    /**
     * Validate request data
     */
    protected function validate($data, $rules)
    {
        $errors = [];
        $validated = [];
        
        foreach ($rules as $field => $rule) {
            $fieldRules = explode('|', $rule);
            $value = $data[$field] ?? null;
            
            foreach ($fieldRules as $fieldRule) {
                if ($fieldRule === 'required' && empty($value)) {
                    $errors[$field][] = "The $field field is required.";
                }
                
                if (strpos($fieldRule, 'max:') === 0) {
                    $max = (int) str_replace('max:', '', $fieldRule);
                    if (strlen($value) > $max) {
                        $errors[$field][] = "The $field field must not exceed $max characters.";
                    }
                }
                
                if (strpos($fieldRule, 'min:') === 0) {
                    $min = (int) str_replace('min:', '', $fieldRule);
                    if (is_numeric($value) && $value < $min) {
                        $errors[$field][] = "The $field field must be at least $min.";
                    }
                }
                
                if ($fieldRule === 'numeric' && !is_numeric($value)) {
                    $errors[$field][] = "The $field field must be numeric.";
                }
                
                if ($fieldRule === 'email' && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field][] = "The $field field must be a valid email address.";
                }
                
                if ($fieldRule === 'boolean') {
                    $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                }
            }
            
            if (!isset($errors[$field])) {
                $validated[$field] = $value;
            }
        }
        
        if (!empty($errors)) {
            throw new ValidationException($errors);
        }
        
        return $validated;
    }
    
    /**
     * Get current template preference
     */
    protected function getTemplate()
    {
        // Check URL parameter first
        if (isset($_GET['template'])) {
            $_SESSION['template'] = $_GET['template'];
            return $_GET['template'];
        }
        
        // Then check session
        if (isset($_SESSION['template'])) {
            return $_SESSION['template'];
        }
        
        // Finally check system config
        try {
            $db = Flight::db();
            $stmt = $db->prepare("SELECT `value` FROM `config` WHERE `key` = 'app_template' AND `category` = 'system'");
            $stmt->execute();
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            if ($result && $result['value']) {
                // Set in session for performance
                $_SESSION['template'] = $result['value'];
                return $result['value'];
            }
        } catch (\Exception $e) {
            error_log('Template fetch error: ' . $e->getMessage());
        }
        
        return 'modern';
    }
    
    /**
     * Set template preference
     */
    protected function setTemplate($template)
    {
        $_SESSION['template'] = $template;
    }
    
    /**
     * Generate CSRF token
     */
    protected function generateCsrfToken(): string
    {
        if (empty($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCsrfToken($token = null): bool
    {
        $csrfEnabled = filter_var($_ENV['CSRF_PROTECTION'] ?? true, FILTER_VALIDATE_BOOLEAN);
        if (!$csrfEnabled) {
            return true;
        }
        
        if ($token === null) {
            $token = Flight::request()->data->csrf_token ?? '';
            
            if (empty($token) && isset($_POST['csrf_token'])) {
                $token = $_POST['csrf_token'];
            }
            
            if (empty($token) && in_array(Flight::request()->method, ['DELETE', 'PUT'])) {
                $token = Flight::request()->query->csrf_token ?? '';
                
                if (empty($token)) {
                    $body = Flight::request()->getBody();
                    if (!empty($body)) {
                        parse_str($body, $data);
                        $token = $data['csrf_token'] ?? '';
                    }
                }
            }
            
            if (empty($token)) {
                $token = Flight::request()->header('X-CSRF-Token') ?? '';
            }
            
            if (empty($token) && isset($_REQUEST['csrf_token'])) {
                $token = $_REQUEST['csrf_token'];
            }
        }
        
        if (empty($_SESSION['csrf_token']) || empty($token)) {
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * Check CSRF token from request
     */
    protected function checkCsrfToken(): void
    {
        if (!$this->validateCsrfToken()) {
            throw new \Exception('Invalid CSRF token', 403);
        }
    }
    
    /**
     * Get sanitized input data
     */
    protected function input(string $key, $default = null, int $filter = FILTER_SANITIZE_SPECIAL_CHARS)
    {
        $data = Flight::request()->data->getData();
        
        if (empty($data) && in_array(Flight::request()->method, ['PUT', 'PATCH', 'DELETE'])) {
            $data = $_POST;
        }
        
        if (!isset($data[$key])) {
            return $default;
        }
        
        if (is_array($data[$key])) {
            return array_map(function($value) use ($filter) {
                return filter_var($value, $filter);
            }, $data[$key]);
        }
        
        return filter_var($data[$key], $filter);
    }
}