<?php
// Debug version to trace table generation
error_reporting(E_ALL);
ini_set('display_errors', 0);

// Load composer autoloader
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Load application bootstrap
require_once __DIR__ . '/../app/config/bootstrap.php';

// Get invoice ID from parameter
$invoiceId = $_GET['id'] ?? 246;

// Load invoice data using the Invoice model
try {
    $invoiceModel = new \App\Models\Invoice();
    $invoice = $invoiceModel->getInvoiceWithDetails($invoiceId);
    
    if (!$invoice) {
        die("Invoice not found");
    }
    
    // Get additional data
    $db = Flight::db();
    
    // Get invoice type code if not already loaded
    if (!isset($invoice['invoice_type_code']) && !empty($invoice['invoice_type_id'])) {
        $stmt = $db->prepare("SELECT code FROM config_invoice_types WHERE id = :id");
        $stmt->execute(['id' => $invoice['invoice_type_id']]);
        $typeData = $stmt->fetch(PDO::FETCH_ASSOC);
        $invoice['invoice_type_code'] = $typeData ? $typeData['code'] : null;
    }
    
    // Use invoice lines from the model data
    $items = $invoice['lines'] ?? [];
    
    // Calculate total for each item if not already calculated
    foreach ($items as &$item) {
        if (!isset($item['total'])) {
            $item['total'] = $item['quantity'] * $item['unit_price'];
        }
    }
    
} catch (Exception $e) {
    die("Error loading invoice: " . $e->getMessage());
}

// Now simulate the table generation
$documentTypeId = $invoice['document_type_id'] ?? null;
$visibleColumns = [];

if ($documentTypeId) {
    $visibleColumns = \App\Models\DocumentTypeColumnConfig::getVisibleInvoiceColumns($documentTypeId);
}

// Default columns if no configuration
if (empty($visibleColumns)) {
    $visibleColumns = [
        'description' => ['visible' => true, 'order' => 1],
        'quantity' => ['visible' => true, 'order' => 2],
        'unit_price' => ['visible' => true, 'order' => 3],
        'vat_rate' => ['visible' => true, 'order' => 4],
        'total' => ['visible' => true, 'order' => 5]
    ];
}

// Sort columns by order
uasort($visibleColumns, function($a, $b) {
    return ($a['order'] ?? 999) - ($b['order'] ?? 999);
});

// Column headers
$columnHeaders = [
    'description' => 'Description',
    'quantity' => 'Quantité',
    'unit_price' => 'Prix unit.',
    'vat_rate' => 'TVA',
    'total' => 'Total'
];

// Output as HTML for debugging
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Invoice PDF Debug - Table Generation</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #007bff; color: white; }
        .highlight { background: yellow; }
    </style>
</head>
<body>
    <h1>Invoice PDF Table Generation Debug</h1>
    
    <div class="debug">
        <h2>Invoice Details</h2>
        <p>Invoice Number: <?= htmlspecialchars($invoice['invoice_number']) ?></p>
        <p>Document Type ID: <?= $documentTypeId ?></p>
        <p>Items count: <?= count($items) ?></p>
    </div>
    
    <div class="debug">
        <h2>Items Array</h2>
        <pre><?= htmlspecialchars(print_r($items, true)) ?></pre>
    </div>
    
    <div class="debug">
        <h2>Visible Columns Configuration</h2>
        <pre><?= htmlspecialchars(print_r($visibleColumns, true)) ?></pre>
    </div>
    
    <h2>Generated Table (as it would appear in PDF)</h2>
    
    <table>
        <thead>
            <tr>
                <?php foreach ($visibleColumns as $columnId => $config): ?>
                    <th><?= htmlspecialchars($columnHeaders[$columnId] ?? $columnId) ?></th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php 
            $rowCount = 0;
            foreach ($items as $index => $item): 
                $rowCount++;
                $total = $item['quantity'] * $item['unit_price'];
                $bgColor = ($index % 2 == 1) ? '#f9f9f9' : 'white';
            ?>
                <tr style="background-color: <?= $bgColor ?>">
                    <?php foreach ($visibleColumns as $columnId => $config): 
                        $value = '';
                        
                        switch ($columnId) {
                            case 'description':
                                $value = htmlspecialchars($item['description']);
                                break;
                            case 'quantity':
                                $value = number_format($item['quantity'], 2, ',', '.');
                                break;
                            case 'unit_price':
                                $value = number_format($item['unit_price'], 2, ',', '.') . '€';
                                break;
                            case 'vat_rate':
                                $value = number_format($item['vat_rate'], 2, ',', '.') . '%';
                                break;
                            case 'total':
                                $totalWithVat = $total * (1 + $item['vat_rate'] / 100);
                                $value = number_format($totalWithVat, 2, ',', '.') . '€';
                                break;
                        }
                    ?>
                        <td class="<?= $columnId === 'description' ? 'highlight' : '' ?>">
                            <?= $value ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    
    <div class="debug">
        <h2>Row Count</h2>
        <p>Total rows generated: <strong><?= $rowCount ?></strong></p>
        <p>Expected rows: <strong><?= count($items) ?></strong></p>
        <?php if ($rowCount !== count($items)): ?>
            <p style="color: red;">⚠️ MISMATCH: Generated <?= $rowCount ?> rows but expected <?= count($items) ?></p>
        <?php else: ?>
            <p style="color: green;">✓ Row count matches</p>
        <?php endif; ?>
    </div>
    
    <div class="debug">
        <h2>Descriptions Found</h2>
        <ul>
            <?php foreach ($items as $item): ?>
                <li><?= htmlspecialchars($item['description']) ?> (ID: <?= $item['id'] ?>)</li>
            <?php endforeach; ?>
        </ul>
    </div>
    
    <div class="debug">
        <h2>Actions</h2>
        <p><a href="invoice-pdf.php?id=<?= $invoiceId ?>">View Actual PDF</a></p>
        <p><a href="invoices/<?= $invoiceId ?>">View Invoice Web Page</a></p>
    </div>
</body>
</html>