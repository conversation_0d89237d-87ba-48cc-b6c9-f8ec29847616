<?php

return [
    'required' => 'Ce champ est obligatoire',
    'email' => 'Veuillez entrer une adresse email valide',
    'numeric' => 'Ce champ doit être un nombre',
    'min' => 'Ce champ doit contenir au moins :min caractères',
    'max' => 'Ce champ ne doit pas dépasser :max caractères',
    'unique' => 'Cette valeur existe déjà',
    'confirmed' => 'La confirmation ne correspond pas',
    'date' => 'Veuillez entrer une date valide',
    'date_format' => 'Veuillez entrer une date au format :format',
    'before' => 'Cette date doit être antérieure à :date',
    'after' => 'Cette date doit être postérieure à :date',
    'regex' => 'Le format est invalide',
    'in' => 'La valeur sélectionnée est invalide',
    'between' => 'Cette valeur doit être comprise entre :min et :max',
    'required_field' => ':field est obligatoire',
    'date_range_invalid' => 'La date de fin doit être postérieure à la date de début',
    'invalid_data_format' => 'Format de données invalide reçu',
    'required_fields' => 'Veuillez remplir tous les champs obligatoires',
];