<?php
/**
 * Final fix for persistent JavaScript syntax error
 */

// Set headers to prevent any caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");
header("Expires: 0");

// Clear all server caches
if (function_exists('opcache_reset')) {
    opcache_reset();
}

// Clear Twig cache
$twigCacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($twigCacheDir)) {
    $iterator = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($twigCacheDir, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::CHILD_FIRST
    );
    foreach ($iterator as $file) {
        if ($file->isDir()) {
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
        }
    }
}

// Update the template file to add cache-busting timestamps
$templateFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($templateFile);

// Add cache-busting version to the template itself by adding a timestamp comment
$timestamp = time();
$versionComment = "\n{# Cache version: $timestamp #}\n";

// Add version comment at the beginning if not already present
if (!preg_match('/{# Cache version:/', $content)) {
    $content = preg_replace('/^(\s*{% extends)/', $versionComment . '$1', $content);
    file_put_contents($templateFile, $content);
}

// Touch the file to update modification time
touch($templateFile);
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Final Syntax Error Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 40px;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
            font-size: 18px;
            text-align: center;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .steps {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .steps ol {
            margin: 10px 0;
            padding-left: 25px;
        }
        .steps li {
            margin: 10px 0;
            line-height: 1.6;
        }
        .button {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 15px 30px;
            border-radius: 5px;
            text-decoration: none;
            font-size: 18px;
            margin: 10px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #218838;
        }
        .button.secondary {
            background: #6c757d;
        }
        .button.secondary:hover {
            background: #5a6268;
        }
        .button.primary {
            background: #007bff;
        }
        .button.primary:hover {
            background: #0056b3;
        }
        .button-container {
            text-align: center;
            margin: 30px 0;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
            color: #e83e8c;
        }
        .highlight {
            background: #ffeaa7;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Final JavaScript Syntax Error Fix</h1>
        
        <div class="success">
            <strong>✅ All Server-Side Fixes Applied Successfully!</strong><br>
            Template expressions have been fixed and caches cleared.
        </div>
        
        <div class="warning">
            <h3>⚠️ Browser Cache Is The Issue</h3>
            <p>Your browser is persistently caching the old JavaScript file with the syntax error at line 3638.</p>
            <p><strong>This is why you still see the error even in new browsers - they're all caching the same file!</strong></p>
        </div>
        
        <div class="steps">
            <h3>🚀 Immediate Solution (Choose ONE):</h3>
            
            <h4>Option A: Developer Tools Method (Most Reliable)</h4>
            <ol>
                <li>Open Developer Tools by pressing <code>F12</code></li>
                <li>Go to the <strong>Network</strong> tab</li>
                <li>Check the box that says <strong>"Disable cache"</strong></li>
                <li>Keep DevTools open and click the button below to open the invoice page</li>
                <li>The page will load without any cache</li>
            </ol>
            
            <h4>Option B: Force Reload Method</h4>
            <ol>
                <li>Click the button below to open the invoice page</li>
                <li>Immediately press <code>Ctrl + Shift + R</code> (Windows/Linux) or <code>Cmd + Shift + R</code> (Mac)</li>
                <li>This forces the browser to ignore cache and reload everything</li>
            </ol>
            
            <h4>Option C: Incognito/Private Window</h4>
            <ol>
                <li>Close all browser windows</li>
                <li>Open a new incognito/private window (<code>Ctrl/Cmd + Shift + N</code>)</li>
                <li>Visit the invoice page from there</li>
            </ol>
        </div>
        
        <?php
        // Generate unique URLs with cache busters
        $uniqueId = time() . '_' . rand(10000, 99999);
        $invoiceUrl = "/fit/public/invoices/create?_cb=" . $uniqueId . "&nocache=1&v=" . $timestamp;
        ?>
        
        <div class="button-container">
            <a href="<?php echo $invoiceUrl; ?>" class="button" target="_blank">
                🚀 Open Invoice Page (New Tab)
            </a>
            <a href="/fit/public/debug_syntax_error.php?_cb=<?php echo $uniqueId; ?>" class="button secondary" target="_blank">
                🔍 Run Debug Check
            </a>
        </div>
        
        <div class="info">
            <h3>📝 What Was Fixed:</h3>
            <ul>
                <li>Line 3598: <code>{{ __('invoices.select_course') }}</code> in template literal → Fixed</li>
                <li>Line 3607: <code>{{ __('invoices.hours') }}</code> in template literal → Fixed</li>
                <li>Line 3638: Missing closing parenthesis → Fixed</li>
                <li>Multiple instances of <code>{{ base_url }}</code> in template literals → Fixed</li>
                <li>All Twig expressions inside JavaScript template literals → Converted to variables</li>
            </ul>
        </div>
        
        <div class="steps">
            <h3>💾 If The Error Still Persists:</h3>
            <p>This means your browser or a proxy server is aggressively caching. Try:</p>
            <ol>
                <li><strong>Clear ALL browser data:</strong> Settings → Privacy → Clear browsing data → Select "All time" → Check all boxes → Clear</li>
                <li><strong>Use a different browser:</strong> If using Chrome, try Firefox or Edge</li>
                <li><strong>Check proxy settings:</strong> If behind a corporate proxy, it might be caching</li>
                <li><strong>Restart your browser completely:</strong> Close all windows and restart</li>
            </ol>
        </div>
        
        <script>
        // Try to bust any service worker cache
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for(let registration of registrations) {
                    registration.unregister();
                }
            });
        }
        
        // Clear caches if available
        if ('caches' in window) {
            caches.keys().then(function(keyList) {
                return Promise.all(keyList.map(function(key) {
                    return caches.delete(key);
                }));
            }).then(function() {
                console.log('All caches cleared');
            });
        }
        
        // Add random parameter to all links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href*="/fit/"]');
            links.forEach(function(link) {
                const url = new URL(link.href);
                url.searchParams.set('_t', Date.now());
                link.href = url.toString();
            });
        });
        </script>
    </div>
</body>
</html>