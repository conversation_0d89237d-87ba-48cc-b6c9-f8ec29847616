{"objective": "Optimize Fit360 AdminDesk UI and templates for mobile responsiveness while auditing force permissions implementation", "swarmName": "ui-optimization-swarm", "topology": "mesh", "maxAgents": 8, "agents": [{"type": "UI Analyzer", "capabilities": ["responsive-design", "css-analysis", "mobile-optimization"], "priority": "high"}, {"type": "Frontend Developer", "capabilities": ["css", "responsive-design", "bootstrap", "javascript"], "priority": "high"}, {"type": "Performance Optimizer", "capabilities": ["performance", "css-optimization", "asset-loading"], "priority": "medium"}, {"type": "Responsive Tester", "capabilities": ["mobile-testing", "cross-browser", "ui-validation"], "priority": "medium"}, {"type": "Template Architect", "capabilities": ["template-optimization", "twig", "layout-design"], "priority": "high"}, {"type": "Permissions Auditor", "capabilities": ["security", "permissions", "access-control"], "priority": "high"}, {"type": "UI Lead", "capabilities": ["project-management", "coordination", "quality-assurance"], "priority": "critical"}], "tasks": ["Analyze current UI/UX and identify responsiveness issues", "Audit existing CSS files for mobile responsiveness problems", "Review all Twig templates for responsive design patterns", "Implement proper mobile breakpoints across all pages", "Optimize CSS for better performance and mobile loading", "Update Bootstrap components for better mobile support", "Audit force permissions implementation", "Ensure permission UI is mobile-friendly", "Test responsiveness on multiple devices/viewports", "Fix layout issues on small screens", "Optimize images for mobile loading", "Update JavaScript for mobile interactions"], "strategy": "parallel", "coordination": {"memory_sharing": true, "real_time_sync": true, "progress_tracking": true, "conflict_resolution": "consensus"}}