# Permission System Documentation

## Overview

The Fit360 AdminDesk application uses a role-based access control (RBAC) system with permissions managed through user groups. This document explains how the permission system works and how to configure it.

## Architecture

### Components

1. **Permissions** - Individual actions that can be controlled (e.g., `users.create`, `invoices.view`)
2. **User Groups** - Collections of permissions that can be assigned to users
3. **Group Permissions** - The many-to-many relationship between groups and permissions
4. **User Group Members** - The many-to-many relationship between users and groups

### Database Tables

- `permissions` - Stores all available permissions
- `user_groups` - Stores user groups/roles
- `group_permissions` - Maps permissions to groups
- `user_group_members` - Maps users to groups

## Permission Categories

Permissions are organized into categories:

- **users** - User management permissions
- **system** - System configuration and settings
- **invoicing** - Invoice and payment management
- **patients** - Patient management
- **clients** - Client management
- **products** - Product and package management
- **pos** - Point of sale operations
- **reports** - Reporting and analytics

## Default User Groups

The system comes with four default groups:

### 1. Administrators
- **Color**: Red (#dc3545)
- **Icon**: Crown (fas fa-crown)
- **Access**: Full system access to all features
- **System Group**: Yes (cannot be deleted)

### 2. Managers
- **Color**: Blue (#007bff)
- **Icon**: User Tie (fas fa-user-tie)
- **Access**: Most features except system configuration
- **System Group**: Yes (cannot be deleted)

### 3. Staff
- **Color**: Green (#28a745)
- **Icon**: Users (fas fa-users)
- **Access**: Basic operational features
- **System Group**: Yes (cannot be deleted)

### 4. Practitioners
- **Color**: Cyan (#17a2b8)
- **Icon**: User MD (fas fa-user-md)
- **Access**: Patient management and personal invoicing
- **System Group**: No (can be customized/deleted)

## Permission Codes

### User Management
- `users.view` - View user list and details
- `users.create` - Create new users
- `users.update` - Update user information
- `users.delete` - Delete users
- `users.reset_password` - Reset user passwords
- `user_groups.view` - View user groups
- `user_groups.create` - Create user groups
- `user_groups.update` - Update user groups
- `user_groups.delete` - Delete user groups
- `user_groups.manage_members` - Add/remove users from groups

### Configuration
- `config.view` - View system configuration
- `config.manage` - Modify system configuration
- `translations.view` - View translations
- `translations.manage` - Modify translations

### Invoicing
- `invoices.view` - View invoices
- `invoices.create` - Create invoices
- `invoices.update` - Update invoices
- `invoices.delete` - Delete invoices
- `invoices.send` - Send invoices via email
- `invoices.void` - Void invoices
- `payments.view` - View payments
- `payments.create` - Record payments
- `payments.delete` - Delete payments

### Other Modules
- `patients.*` - Patient management permissions
- `clients.*` - Client management permissions
- `products.*` - Product management permissions
- `pos.*` - Point of sale permissions
- `packages.*` - Package management permissions
- `reports.*` - Reporting permissions

## Implementation

### Route Protection

Routes are protected using the permission middleware in `app/config/routes.php`:

```php
// Example route protection
$modulePermissions = [
    'users' => [
        'GET /users' => 'users.view',
        'POST /users' => 'users.create',
        'DELETE /users/@id' => 'users.delete',
    ],
];
```

### Checking Permissions in Code

Use the PermissionService to check permissions:

```php
use App\Services\PermissionService;

$permissionService = PermissionService::getInstance();

// Check single permission
if ($permissionService->hasPermission('users.create')) {
    // User can create users
}

// Check multiple permissions (ANY)
if ($permissionService->hasAnyPermission(['users.create', 'users.update'])) {
    // User has at least one permission
}

// Check multiple permissions (ALL)
if ($permissionService->hasAllPermissions(['users.view', 'users.create'])) {
    // User has all permissions
}

// Check if super admin
if ($permissionService->isSuperAdmin()) {
    // User is in Administrators group
}
```

### Using Permission Middleware

The PermissionMiddleware class provides route protection:

```php
// In route definitions
Flight::route('GET /admin/users', 
    PermissionMiddleware::require('users.view'), 
    function() {
        // Route handler
    }
);

// For API endpoints
Flight::route('POST /api/users', 
    PermissionMiddleware::requireApi('users.create'), 
    function() {
        // API handler
    }
);
```

## Setup Instructions

### Initial Setup

1. Run the permission seeder by accessing:
   ```
   http://your-domain/run-permission-seeder.php?key=fit360-permission-setup-2024
   ```
   
2. The seeder will:
   - Create all system permissions
   - Create default user groups
   - Assign appropriate permissions to each group

3. **Important**: Delete `run-permission-seeder.php` after running in production!

### Managing Permissions

1. Go to **Users > User Groups** to manage groups
2. Click on a group to view/edit its permissions
3. Use the checkbox interface to grant/revoke permissions
4. Changes take effect immediately

### Adding Users to Groups

1. Go to **Users > User Groups**
2. Click on a group name
3. Click "Add Members"
4. Select users to add to the group
5. Users inherit all permissions from their groups

## Best Practices

1. **Principle of Least Privilege**: Give users only the permissions they need
2. **Use Groups**: Manage permissions through groups rather than individual assignments
3. **Regular Audits**: Periodically review group memberships and permissions
4. **Custom Groups**: Create custom groups for specific roles in your organization
5. **Test Permissions**: Always test permission changes with a non-admin account

## Troubleshooting

### User Can't Access a Feature

1. Check if the user is logged in
2. Verify the user is active (`is_active = 1`)
3. Check which groups the user belongs to
4. Verify the group has the required permission
5. Check if the route requires a specific permission in `routes.php`

### Permission Not Working

1. Ensure the permission exists in the database
2. Check if the permission is assigned to any group
3. Verify the permission code matches in both database and code
4. Clear any permission caches if implemented

### Adding New Permissions

1. Add the permission to the seeder
2. Run the seeder again (it will update existing data)
3. Add the permission check to the relevant routes
4. Update this documentation

## Security Considerations

1. **Never hardcode permissions** - Always use the database
2. **Validate on server side** - Don't rely on client-side permission checks
3. **Log permission changes** - Keep an audit trail
4. **Regular reviews** - Audit user access periodically
5. **Secure the seeder** - Delete or protect the seeder file in production