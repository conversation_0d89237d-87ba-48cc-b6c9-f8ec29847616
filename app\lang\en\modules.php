<?php

return [
    // Module Names
    'invoices' => 'Invoices',
    'retrocession' => 'Retrocession',
    'recurring_invoices' => 'Recurring Invoices',
    'clients' => 'Clients',
    'products' => 'Products',
    'reports' => 'Reports',
    'configuration' => 'Configuration',
    'users' => 'Users',
    'stock' => 'Stock',
    'documents' => 'Documents',
    'payments' => 'Payments',
    'categories' => 'Categories',
    'permissions' => 'Permissions',
    'patients' => 'Members',
    'courses' => 'Courses',
    'pos' => 'Point of Sale',
    'packages' => 'Packages',
    'sales' => 'Sales',
    'admin' => 'Administration',
    'auth' => 'Authentication',
    
    // Module Descriptions
    'invoices_desc' => 'Manage customer invoices and billing for memberships, personal training, and services',
    'retrocession_desc' => 'Handle commission payments and revenue sharing with trainers and partners',
    'recurring_invoices_desc' => 'Set up and manage automatic billing for monthly memberships and subscriptions',
    'clients_desc' => 'Maintain complete member profiles, contact information, and fitness history',
    'products_desc' => 'Manage fitness services, training sessions, supplements, and equipment inventory',
    'reports_desc' => 'Generate comprehensive business analytics, revenue reports, and member statistics',
    'configuration_desc' => 'Configure system settings, business rules, and fitness center parameters',
    'users_desc' => 'Manage staff accounts, trainers, and system access permissions',
    'stock_desc' => 'Track inventory levels for supplements, merchandise, and equipment',
    'documents_desc' => 'Store and manage contracts, waivers, medical forms, and member documents',
    'payments_desc' => 'Process member payments, track transactions, and manage payment methods',
    'categories_desc' => 'Organize services, products, and memberships into logical groups',
    'permissions_desc' => 'Control access rights and define user roles for staff and trainers',
    'patients_desc' => 'Manage member health profiles, fitness goals, and progress tracking',
    'courses_desc' => 'Schedule and manage group fitness classes, workshops, and training programs',
    'pos_desc' => 'Quick sales interface for supplements, merchandise, and walk-in services',
    'packages_desc' => 'Create and manage membership bundles, training packages, and promotional offers',
    'sales_desc' => 'Track all sales transactions, revenue streams, and business performance',
    'admin_desc' => 'System administration tools for database management and advanced configurations',
    'auth_desc' => 'Secure login system and authentication management for all users',
];