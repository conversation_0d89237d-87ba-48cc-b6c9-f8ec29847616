<?php
// Fix sequence to 186 so next invoice will be 187

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix Sequence to 186</h2>";
    
    // Show current status
    echo "<h3>Current Status</h3>";
    
    // Check current sequence
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $seq = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "<p>Current sequence: <strong>" . ($seq['last_number'] ?? 'Not found') . "</strong></p>";
    
    // Check highest invoice number
    $stmt = $db->query("
        SELECT invoice_number 
        FROM invoices 
        WHERE invoice_number LIKE '%2025%'
        ORDER BY CAST(SUBSTRING_INDEX(invoice_number, '-', -1) AS UNSIGNED) DESC
        LIMIT 1
    ");
    $highest = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($highest) {
        echo "<p>Highest invoice number: <strong>" . $highest['invoice_number'] . "</strong></p>";
        
        // Extract the number
        $parts = explode('-', $highest['invoice_number']);
        $highestNum = intval(end($parts));
        echo "<p>Highest sequence number used: <strong>" . $highestNum . "</strong></p>";
    }
    
    // Update sequence to 186
    echo "<h3>Updating Sequence</h3>";
    
    $stmt = $db->prepare("
        UPDATE document_sequences 
        SET last_number = 186 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $stmt->execute();
    
    echo "<p style='color: green; font-size: 18px;'>✓ Sequence updated to <strong>186</strong></p>";
    
    // Preview next numbers
    echo "<h3>Next Invoice Numbers Will Be:</h3>";
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
    echo "<tr style='background: #e8f5e9;'><th>Invoice Type</th><th>Next Invoice Number</th></tr>";
    echo "<tr><td>Loyer (Rental)</td><td style='font-size: 18px; color: #28a745;'><strong>FAC-LOY-2025-0187</strong> ✓</td></tr>";
    echo "<tr><td>Rétrocession</td><td style='font-size: 18px;'><strong>FAC-RET-2025-0187</strong></td></tr>";
    echo "<tr><td>Divers</td><td style='font-size: 18px;'><strong>FAC-DIV-2025-0187</strong></td></tr>";
    echo "<tr><td>No Type</td><td style='font-size: 18px;'><strong>FAC-2025-0187</strong></td></tr>";
    echo "</table>";
    
    echo "<br><br>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; border-radius: 5px;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>✅ Success!</h3>";
    echo "<p style='margin: 0; font-size: 16px;'>The sequence has been set to 186.<br>";
    echo "Your next invoice will be numbered <strong>0187</strong>.</p>";
    echo "</div>";
    
    echo "<br>";
    echo "<a href='/fit/public/invoices/create' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>Create New Invoice</a>";
    echo "<a href='check_invoice_sequences.php' style='font-size: 16px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Check Sequences</a>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>