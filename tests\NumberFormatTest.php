<?php

namespace Tests;

use PHPUnit\Framework\TestCase;
use App\Helpers\NumberFormat;

class NumberFormatTest extends TestCase
{
    /**
     * Test basic number generation with default format
     */
    public function testGenerateWithDefaultFormat()
    {
        $result = NumberFormat::generate('client', 1);
        $this->assertStringContainsString('CLT', $result);
        $this->assertStringContainsString(date('Y'), $result);
        $this->assertStringContainsString('0001', $result);
    }
    
    /**
     * Test number generation with custom format
     */
    public function testFormatNumber()
    {
        // Test with year placeholder
        $format = '{PREFIX}-{YEAR}-{NUMBER:5}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 123, 5]);
        $expected = '{PREFIX}-' . date('Y') . '-00123';
        $this->assertEquals($expected, $result);
        
        // Test with 2-digit year
        $format = '{PREFIX}-{YEAR:2}-{NUMBER:3}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 45, 3]);
        $expected = '{PREFIX}-' . date('y') . '-045';
        $this->assertEquals($expected, $result);
        
        // Test with month and day
        $format = '{YEAR}{MONTH}{DAY}-{NUMBER:4}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 1, 4]);
        $expected = date('Ymd') . '-0001';
        $this->assertEquals($expected, $result);
    }
    
    /**
     * Test format validation
     */
    public function testValidateFormat()
    {
        // Valid formats
        $this->assertTrue(NumberFormat::validateFormat('{YEAR}-{NUMBER}'));
        $this->assertTrue(NumberFormat::validateFormat('{PREFIX}-{YEAR}-{NUMBER:5}'));
        $this->assertTrue(NumberFormat::validateFormat('{MONTH}{DAY}-{NUMBER:3}'));
        $this->assertTrue(NumberFormat::validateFormat('INV-{NUMBER}'));
        
        // Invalid formats (no number placeholder)
        $this->assertFalse(NumberFormat::validateFormat('{YEAR}-{MONTH}'));
        $this->assertFalse(NumberFormat::validateFormat('STATIC-TEXT'));
        $this->assertFalse(NumberFormat::validateFormat(''));
    }
    
    /**
     * Test number padding
     */
    public function testNumberPadding()
    {
        $format = '{NUMBER:3}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 5, 3]);
        $this->assertEquals('005', $result);
        
        $format = '{NUMBER:6}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 123, 6]);
        $this->assertEquals('000123', $result);
        
        // Test default padding
        $format = '{NUMBER}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 99, 4]);
        $this->assertEquals('0099', $result);
    }
    
    /**
     * Test edge cases
     */
    public function testEdgeCases()
    {
        // Large number
        $format = '{NUMBER:3}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 99999, 3]);
        $this->assertEquals('99999', $result); // Should not truncate
        
        // Zero
        $format = '{NUMBER:5}';
        $result = $this->invokeMethod(new NumberFormat(), 'formatNumber', [$format, 0, 5]);
        $this->assertEquals('00000', $result);
    }
    
    /**
     * Helper method to invoke private methods
     */
    private function invokeMethod($object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);
        return $method->invokeArgs($object, $parameters);
    }
}