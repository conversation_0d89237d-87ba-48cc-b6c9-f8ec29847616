<!DOCTYPE html>
<html>
<head>
    <title>Test Invoice Items Columns</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Invoice Items Column Configuration</h2>
        
        <div class="mb-3">
            <button class="btn btn-primary" onclick="testDirectAjax()">Test Direct AJAX</button>
            <button class="btn btn-secondary" onclick="testConfiguredRoute()">Test Configured Route</button>
        </div>
        
        <div id="result" class="mb-3"></div>
        
        <h3 class="mt-4">Expected Columns (9 total):</h3>
        <ol>
            <li>Description (Description) - Required</li>
            <li>Reference (Référence)</li>
            <li>Quantity (Quantité)</li>
            <li>Unit (Unité)</li>
            <li>Unit Price (Prix unitaire)</li>
            <li>Discount (Remise)</li>
            <li>VAT Rate (Taux TVA)</li>
            <li>Subtotal (Sous-total)</li>
            <li>Total (Total) - Required</li>
        </ol>
    </div>

    <script>
    function showResult(data, title) {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = `
            <h4>${title}</h4>
            <pre class="bg-light p-3">${JSON.stringify(data, null, 2)}</pre>
            ${data.columnConfig ? `<p class="text-success">✓ Found ${data.columnConfig.length} columns</p>` : '<p class="text-danger">✗ No columns found</p>'}
        `;
    }
    
    function testDirectAjax() {
        const url = '/fit/public/test-column-ajax.php?documentTypeId=1&table=invoice_items';
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                showResult(data, 'Direct AJAX Test Result');
            })
            .catch(error => {
                document.getElementById('result').innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
            });
    }
    
    function testConfiguredRoute() {
        const url = '/fit/public/config/document-type-columns/invoice_items?documentTypeId=1';
        
        fetch(url)
            .then(response => {
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                try {
                    const data = JSON.parse(text);
                    showResult(data, 'Configured Route Test Result');
                } catch (e) {
                    document.getElementById('result').innerHTML = `
                        <div class="alert alert-danger">
                            <h5>Error parsing JSON</h5>
                            <p>${e.message}</p>
                            <h6>Raw Response:</h6>
                            <pre class="bg-light p-2">${text}</pre>
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('result').innerHTML = `<div class="alert alert-danger">Network Error: ${error.message}</div>`;
            });
    }
    </script>
</body>
</html>