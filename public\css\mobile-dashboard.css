/* ========================================
   MOBILE-SPECIFIC STYLES FOR DASHBOARD
   Fit360 AdminDesk - Dashboard
   ======================================== */

/* ========================================
   1. DASHBOARD WELCOME SECTION - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Welcome card - responsive layout */
    .bg-gradient-primary .row {
        text-align: center;
    }
    
    .bg-gradient-primary h2 {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .bg-gradient-primary p {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }
    
    /* Create invoice button - full width on mobile */
    .bg-gradient-primary .btn-lg {
        width: 100%;
        padding: 0.75rem 1rem;
        font-size: 1rem;
    }
}

/* ========================================
   2. STATS CARDS - MOBILE OPTIMIZATION
   ======================================== */

@media (max-width: 767px) {
    /* Stats cards - stack on mobile */
    .stat-card {
        margin-bottom: 1rem;
    }
    
    /* Adjust card body spacing */
    .stat-card .card-body {
        padding: 1.25rem 1rem;
    }
    
    /* Card title and value sizing */
    .stat-card .card-title {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
    }
    
    .stat-card h2 {
        font-size: 1.75rem;
        margin-bottom: 0;
    }
    
    /* Icon adjustments */
    .stat-card .fs-1 {
        font-size: 2.5rem !important;
        opacity: 0.3;
    }
    
    /* Better touch targets for card links */
    .stat-card .stretched-link::after {
        content: '';
        position: absolute;
        top: -10px;
        right: -10px;
        bottom: -10px;
        left: -10px;
    }
}

/* ========================================
   3. QUICK ACTIONS - MOBILE LAYOUT
   ======================================== */

@media (max-width: 767px) {
    /* Quick actions grid - 2 columns on mobile */
    .quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }
    
    /* Quick action buttons - smaller padding */
    .btn-outline-primary.py-3,
    .btn-outline-success.py-3,
    .btn-outline-info.py-3,
    .btn-outline-warning.py-3 {
        padding: 1rem 0.5rem !important;
        font-size: 0.875rem;
    }
    
    /* Icon size adjustment */
    .btn-outline-primary .fs-3,
    .btn-outline-success .fs-3,
    .btn-outline-info .fs-3,
    .btn-outline-warning .fs-3 {
        font-size: 1.75rem !important;
        margin-bottom: 0.5rem !important;
    }
    
    /* Text wrap for longer labels */
    .quick-actions-grid .btn {
        white-space: normal;
        line-height: 1.2;
    }
}

/* ========================================
   4. RECENT INVOICES TABLE - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Table header actions */
    .card-header.d-flex {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .card-header h5 {
        width: 100%;
        margin-bottom: 0.5rem !important;
    }
    
    /* Convert table to cards on mobile */
    .recent-invoices-mobile table {
        display: block;
    }
    
    .recent-invoices-mobile thead {
        display: none;
    }
    
    .recent-invoices-mobile tbody,
    .recent-invoices-mobile tr {
        display: block;
    }
    
    .recent-invoices-mobile tr {
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 1rem;
        background: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .recent-invoices-mobile td {
        display: block;
        padding: 0.25rem 0;
        border: none;
        text-align: left !important;
    }
    
    /* Invoice number as header */
    .recent-invoices-mobile td:first-child {
        font-weight: 600;
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
        padding-bottom: 0.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    /* Add labels for mobile */
    .recent-invoices-mobile td:not(:first-child)::before {
        content: attr(data-label) ": ";
        font-weight: 600;
        display: inline-block;
        width: 40%;
    }
    
    /* Status badge styling */
    .recent-invoices-mobile .badge {
        margin-left: 0.5rem;
    }
}

/* ========================================
   5. DASHBOARD CHARTS - MOBILE
   ======================================== */

@media (max-width: 767px) {
    /* Chart containers - responsive height */
    .chart-container {
        position: relative;
        height: 250px !important;
        margin-bottom: 1rem;
    }
    
    /* Chart canvas - responsive */
    .chart-container canvas {
        max-height: 250px !important;
    }
    
    /* Chart cards spacing */
    .chart-card {
        margin-bottom: 1rem;
    }
    
    .chart-card .card-body {
        padding: 1rem;
    }
    
    /* Chart legends - smaller font */
    .chart-legend {
        font-size: 0.75rem;
    }
}

/* ========================================
   6. MOBILE-SPECIFIC DASHBOARD FEATURES
   ======================================== */

@media (max-width: 767px) {
    /* Pull to refresh indicator for dashboard */
    .dashboard-refresh-indicator {
        position: fixed;
        top: -60px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 40px;
        background: #007bff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: top 0.3s ease;
        z-index: 1100;
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .dashboard-refresh-indicator.show {
        top: 20px;
    }
    
    /* Loading skeleton for dashboard cards */
    .dashboard-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 0.5rem;
        height: 100px;
        margin-bottom: 1rem;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Activity feed - mobile optimized */
    .activity-feed {
        padding: 0;
    }
    
    .activity-item {
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .activity-item:last-child {
        border-bottom: none;
    }
    
    .activity-icon {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f8f9fa;
        border-radius: 50%;
        float: left;
        margin-right: 1rem;
    }
    
    .activity-content {
        overflow: hidden;
    }
    
    .activity-title {
        font-weight: 600;
        font-size: 0.875rem;
        margin-bottom: 0.25rem;
    }
    
    .activity-time {
        font-size: 0.75rem;
        color: #6c757d;
    }
}

/* ========================================
   7. DASHBOARD MOBILE NAVIGATION
   ======================================== */

@media (max-width: 767px) {
    /* Dashboard quick stats bar - fixed bottom */
    .dashboard-quick-stats {
        position: fixed;
        bottom: 4rem;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #dee2e6;
        display: flex;
        justify-content: space-around;
        padding: 0.5rem 0;
        box-shadow: 0 -2px 5px rgba(0,0,0,0.05);
        z-index: 99;
    }
    
    .quick-stat-item {
        text-align: center;
        flex: 1;
        padding: 0.5rem;
    }
    
    .quick-stat-value {
        font-size: 1.25rem;
        font-weight: 600;
        line-height: 1;
    }
    
    .quick-stat-label {
        font-size: 0.75rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
    
    /* Floating refresh button */
    .dashboard-fab-refresh {
        position: fixed;
        bottom: 5rem;
        right: 1rem;
        width: 56px;
        height: 56px;
        border-radius: 50%;
        background: #17a2b8;
        color: white;
        border: none;
        box-shadow: 0 4px 6px rgba(0,0,0,0.2);
        font-size: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 100;
    }
    
    .dashboard-fab-refresh:active {
        transform: scale(0.9);
    }
    
    .dashboard-fab-refresh.spinning {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
}

/* ========================================
   8. RESPONSIVE UTILITIES - DASHBOARD
   ======================================== */

@media (max-width: 767px) {
    /* Hide desktop-only dashboard elements */
    .dashboard-desktop-only {
        display: none !important;
    }
    
    /* Show mobile-only dashboard elements */
    .dashboard-mobile-only {
        display: block !important;
    }
    
    /* Dashboard spacing adjustments */
    .dashboard-mobile-spacing {
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }
    
    /* Text size adjustments */
    .dashboard-mobile-text {
        font-size: 0.875rem !important;
    }
    
    /* Compact card headers */
    .card-header-mobile {
        padding: 0.75rem 1rem !important;
    }
}

/* ========================================
   9. PERFORMANCE OPTIMIZATIONS - DASHBOARD
   ======================================== */

@media (max-width: 767px) {
    /* Reduce animation durations */
    .stat-card,
    .dashboard-card,
    .chart-card {
        transition-duration: 0.2s !important;
    }
    
    /* Optimize shadows for performance */
    .shadow-sm {
        box-shadow: 0 1px 2px rgba(0,0,0,0.05) !important;
    }
    
    /* Disable hover effects on touch devices */
    @media (hover: none) {
        .stat-card:hover {
            transform: none !important;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075) !important;
        }
    }
}

/* ========================================
   10. LANDSCAPE MODE ADJUSTMENTS
   ======================================== */

@media (max-width: 767px) and (orientation: landscape) {
    /* Reduce vertical spacing in landscape */
    .stat-card .card-body {
        padding: 1rem 0.75rem;
    }
    
    .stat-card h2 {
        font-size: 1.5rem;
    }
    
    /* Quick actions - 4 columns in landscape */
    .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    /* Hide welcome message in landscape to save space */
    .bg-gradient-primary {
        display: none;
    }
}