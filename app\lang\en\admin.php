<?php

return [
    'admin_tools' => 'Admin Tools',
    'admin_dashboard' => 'Admin Dashboard',
    'menu_configuration' => 'Menu Configuration',
    'permissions' => 'Permissions',
    'menu_item' => 'Menu Item',
    'visibility' => 'Visibility',
    'enabled' => 'Enabled',
    'visible_to_all' => 'Visible to all',
    'depends_on_parent' => 'Depends on parent',
    'admin_only' => 'Admin Only',
    'preview' => 'Preview',
    'enabled_items' => 'Enabled Items',
    'disabled_items' => 'Disabled Items',
    'admin_only_items' => 'Admin Only Items',
    'menu_config_saved' => 'Menu configuration saved',
    'menu_config_info' => 'Configure the visibility of menu items for your organization. Disabled items will not be visible to regular users but will remain accessible to administrators.',
    
    // Error logs
    'error_logs' => 'Error Logs',
    'error_details' => 'Error Details',
    'error_id' => 'Error ID',
    'error_message' => 'Error Message',
    'environment' => 'Environment',
    'status' => 'Status',
    'resolved' => 'Resolved',
    'unresolved' => 'Unresolved',
    'mark_resolved' => 'Mark as Resolved',
    'mark_error_resolved' => 'Mark Error as Resolved',
    'resolution_notes' => 'Resolution Notes',
    'resolution_notes_placeholder' => 'Describe how the error was resolved...',
    'cleanup_old_logs' => 'Cleanup Old Logs',
    'cleanup_logs_description' => 'Delete error logs older than the specified number of days.',
    'delete_logs_older_than' => 'Delete logs older than',
    'search_error_logs' => 'Search error logs',
    'no_error_logs_found' => 'No error logs found',
    'performance' => 'Performance',
    'execution_time' => 'Execution Time',
    'memory_usage' => 'Memory Usage',
    'memory_peak' => 'Peak Memory',
    'location' => 'Location',
    'file' => 'File',
    'line' => 'Line',
    'stack_trace' => 'Stack Trace',
    'request_information' => 'Request Information',
    'method' => 'Method',
    'uri' => 'URI',
    'ip_address' => 'IP Address',
    'user_agent' => 'User Agent',
    'request_parameters' => 'Request Parameters',
    'request_headers' => 'Request Headers',
    'error_metadata' => 'Error Metadata',
    'timestamp' => 'Timestamp',
    'error_code' => 'Error Code',
    'user_information' => 'User Information',
    'guest_user' => 'Guest User',
    'session_data' => 'Session Data',
    'performance_metrics' => 'Performance Metrics',
];