<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== FIXING PAYMENT TERMS DAYS VALUES ===\n\n";

try {
    $db = Flight::db();
    
    // Fix Net 30
    $stmt = $db->prepare("UPDATE config_payment_terms SET days = 30 WHERE (code = 'net_30' OR name LIKE '%Net 30%') AND days != 30");
    $result = $stmt->execute();
    $count = $stmt->rowCount();
    echo "Fixed $count 'Net 30' payment terms to have 30 days\n";
    
    // Fix Net 60
    $stmt = $db->prepare("UPDATE config_payment_terms SET days = 60 WHERE (code = 'net_60' OR name LIKE '%Net 60%') AND days != 60");
    $result = $stmt->execute();
    $count = $stmt->rowCount();
    echo "Fixed $count 'Net 60' payment terms to have 60 days\n";
    
    // Fix Net 90
    $stmt = $db->prepare("UPDATE config_payment_terms SET days = 90 WHERE (code = 'net_90' OR name LIKE '%Net 90%') AND days != 90");
    $result = $stmt->execute();
    $count = $stmt->rowCount();
    echo "Fixed $count 'Net 90' payment terms to have 90 days\n";
    
    // Fix immediate payment
    $stmt = $db->prepare("UPDATE config_payment_terms SET days = 0 WHERE code = 'immediate' AND days != 0");
    $result = $stmt->execute();
    $count = $stmt->rowCount();
    echo "Fixed $count 'Immediate' payment terms to have 0 days\n";
    
    // Show current state
    echo "\n\nCurrent payment terms after fix:\n";
    echo str_repeat("-", 80) . "\n";
    
    $stmt = $db->query("SELECT id, code, days, is_active, is_default FROM config_payment_terms ORDER BY days ASC");
    $terms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($terms as $term) {
        // Decode name for display
        $displayName = $term['code'];
        if (!empty($term['name']) && $term['name'] !== 'null') {
            $nameData = json_decode($term['name'], true);
            if ($nameData) {
                $displayName = $nameData['fr'] ?? $nameData['en'] ?? $term['code'];
            }
        }
        
        echo "ID: {$term['id']} | Code: {$term['code']} | Days: {$term['days']} | Active: {$term['is_active']} | Default: {$term['is_default']}\n";
    }
    
    echo "\nDone! Payment terms days values have been fixed.\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}