<?php

namespace App\Services;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\EmailTemplate;
use App\Services\TemplateSelector;
use PDO;
use Exception;
use PHPMailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\SMTP;

class EnhancedEmailService extends EmailService
{
    private $templateSelector;
    private $db;
    private $mailer;
    
    public function __construct()
    {
        parent::__construct();
        $this->db = Flight::db();
        $this->templateSelector = new TemplateSelector();
        $this->initializeMailer();
    }
    
    /**
     * Initialize PHPMailer with configuration
     */
    private function initializeMailer()
    {
        $this->mailer = new PHPMailer(true);
        
        // Server settings
        $this->mailer->isSMTP();
        $this->mailer->Host = $_ENV['SMTP_HOST'] ?? 'localhost';
        $this->mailer->SMTPAuth = true;
        $this->mailer->Username = $_ENV['SMTP_USERNAME'] ?? '';
        $this->mailer->Password = $_ENV['SMTP_PASSWORD'] ?? '';
        $this->mailer->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $this->mailer->Port = $_ENV['SMTP_PORT'] ?? 587;
        $this->mailer->CharSet = 'UTF-8';
    }
    
    /**
     * Send invoice email with intelligent template selection
     */
    public function sendInvoiceEmail($invoiceId, $recipientEmail = null, $templateOverride = null)
    {
        try {
            // Get invoice with all details
            $invoice = Invoice::with(['client', 'items', 'payments'])->find($invoiceId);
            
            if (!$invoice) {
                throw new Exception('Invoice not found');
            }
            
            // Get client
            $client = $invoice->client;
            
            // Determine recipient email
            if (!$recipientEmail) {
                $recipientEmail = $this->determineRecipientEmail($invoice, $client);
            }
            
            if (!$recipientEmail) {
                throw new Exception('No recipient email found');
            }
            
            // Determine payment status
            $paymentStatus = $this->calculatePaymentStatus($invoice);
            
            // Build context for template selection
            $context = [
                'invoice' => $invoice,
                'client' => $client,
                'payment_status' => $paymentStatus,
                'language' => $client->language ?? 'fr',
                'custom_conditions' => [
                    'is_first_invoice' => $this->isFirstInvoice($client),
                    'has_previous_late_payments' => $this->hasLatePaymentHistory($client),
                    'client_since_days' => $this->getClientAge($client)
                ]
            ];
            
            // Select appropriate template
            if ($templateOverride) {
                $template = EmailTemplate::getByCode($templateOverride);
            } else {
                $templateType = $this->determineTemplateType($invoice, $paymentStatus);
                $template = $this->templateSelector->selectTemplate($templateType, $context);
            }
            
            if (!$template) {
                throw new Exception('No suitable email template found');
            }
            
            // Prepare template variables
            $variables = $this->prepareTemplateVariables($invoice, $client, $paymentStatus);
            
            // Parse template with inheritance
            $emailContent = $this->templateSelector->parseWithInheritance($template, $variables);
            
            // Send email
            $result = $this->sendEmail(
                $recipientEmail,
                $emailContent['subject'],
                $emailContent['body'],
                $this->prepareAttachments($invoice)
            );
            
            // Log email
            $this->logEmail($invoice, $template, $emailContent, $result);
            
            // Track template usage
            $this->trackTemplateUsage($template, $emailContent['metadata']);
            
            return $result;
            
        } catch (Exception $e) {
            $this->logError($e, $invoiceId);
            throw $e;
        }
    }
    
    /**
     * Send reminder emails with escalating templates
     */
    public function sendReminderEmails()
    {
        // Get overdue invoices
        $overdueInvoices = Invoice::where('status', 'pending')
            ->where('due_date', '<', date('Y-m-d'))
            ->with(['client', 'payments'])
            ->get();
        
        $results = [];
        
        foreach ($overdueInvoices as $invoice) {
            try {
                // Skip if already paid
                if ($invoice->paid_amount >= $invoice->total_amount) {
                    continue;
                }
                
                // Skip if reminder sent recently
                if ($this->reminderSentRecently($invoice)) {
                    continue;
                }
                
                // Send reminder
                $result = $this->sendInvoiceEmail($invoice->id);
                $results[] = [
                    'invoice_id' => $invoice->id,
                    'success' => true,
                    'message' => 'Reminder sent successfully'
                ];
                
            } catch (Exception $e) {
                $results[] = [
                    'invoice_id' => $invoice->id,
                    'success' => false,
                    'error' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
    
    /**
     * Send appointment reminder with smart template selection
     */
    public function sendAppointmentReminder($appointmentData, $recipientEmail)
    {
        $context = [
            'appointment' => $appointmentData,
            'client' => $appointmentData['client'] ?? null,
            'language' => $appointmentData['language'] ?? 'fr',
            'custom_conditions' => [
                'is_first_appointment' => $appointmentData['is_first'] ?? false,
                'appointment_type' => $appointmentData['type'] ?? 'standard'
            ]
        ];
        
        $template = $this->templateSelector->selectTemplate(
            TemplateSelector::TYPE_APPOINTMENT_REMINDER,
            $context
        );
        
        if (!$template) {
            throw new Exception('No appointment reminder template found');
        }
        
        $variables = [
            'client_name' => $appointmentData['client_name'],
            'appointment_date' => $appointmentData['date'],
            'appointment_time' => $appointmentData['time'],
            'practitioner_name' => $appointmentData['practitioner_name'],
            'location' => $appointmentData['location'],
            'duration' => $appointmentData['duration']
        ];
        
        $emailContent = $this->templateSelector->parseWithInheritance($template, $variables);
        
        return $this->sendEmail(
            $recipientEmail,
            $emailContent['subject'],
            $emailContent['body']
        );
    }
    
    /**
     * Determine template type based on invoice and payment status
     */
    private function determineTemplateType($invoice, $paymentStatus)
    {
        // Check if it's a credit note
        if ($invoice->type === 'CN' || $invoice->type === 'AVOIR') {
            return TemplateSelector::TYPE_INVOICE_CANCELLED;
        }
        
        // Check payment status
        switch ($paymentStatus) {
            case 'paid':
                return TemplateSelector::TYPE_INVOICE_PAID;
            case 'partial':
                return TemplateSelector::TYPE_INVOICE_PARTIAL;
            case 'overdue':
                return TemplateSelector::TYPE_INVOICE_OVERDUE;
            default:
                // Check if this is a reminder
                if ($this->isReminder($invoice)) {
                    return TemplateSelector::TYPE_INVOICE_REMINDER;
                }
                return TemplateSelector::TYPE_INVOICE_NEW;
        }
    }
    
    /**
     * Calculate payment status
     */
    private function calculatePaymentStatus($invoice)
    {
        if ($invoice->paid_amount >= $invoice->total_amount) {
            return 'paid';
        } elseif ($invoice->paid_amount > 0) {
            return 'partial';
        } elseif (strtotime($invoice->due_date) < time()) {
            return 'overdue';
        }
        
        return 'pending';
    }
    
    /**
     * Prepare template variables
     */
    private function prepareTemplateVariables($invoice, $client, $paymentStatus)
    {
        $variables = [
            // Client information
            'client_name' => $client->name,
            'client_company' => $client->company_name,
            'client_address' => $client->address,
            'client_city' => $client->city,
            'client_postal_code' => $client->postal_code,
            'client_country' => $client->country,
            'client_vat_number' => $client->vat_number,
            
            // Invoice information
            'invoice_number' => $invoice->invoice_number,
            'invoice_date' => date('d/m/Y', strtotime($invoice->invoice_date)),
            'due_date' => date('d/m/Y', strtotime($invoice->due_date)),
            'invoice_type' => $invoice->type,
            
            // Amounts
            'subtotal' => number_format($invoice->subtotal, 2, ',', ' ') . ' €',
            'vat_amount' => number_format($invoice->vat_amount, 2, ',', ' ') . ' €',
            'total_amount' => number_format($invoice->total_amount, 2, ',', ' ') . ' €',
            'paid_amount' => number_format($invoice->paid_amount, 2, ',', ' ') . ' €',
            'balance_due' => number_format($invoice->total_amount - $invoice->paid_amount, 2, ',', ' ') . ' €',
            
            // Status information
            'payment_status' => $paymentStatus,
            'is_overdue' => $paymentStatus === 'overdue',
            'is_paid' => $paymentStatus === 'paid',
            'is_partial' => $paymentStatus === 'partial',
            'days_overdue' => max(0, floor((time() - strtotime($invoice->due_date)) / 86400)),
            
            // Items (for templates with item lists)
            'has_items' => count($invoice->items) > 0,
            'items' => array_map(function($item) {
                return [
                    'description' => $item->description,
                    'quantity' => $item->quantity,
                    'unit_price' => number_format($item->unit_price, 2, ',', ' ') . ' €',
                    'total' => number_format($item->total, 2, ',', ' ') . ' €'
                ];
            }, $invoice->items->toArray()),
            
            // Company information
            'company_name' => Flight::get('config')['company_name'] ?? '',
            'company_address' => Flight::get('config')['company_address'] ?? '',
            'company_email' => Flight::get('config')['company_email'] ?? '',
            'company_phone' => Flight::get('config')['company_phone'] ?? '',
            
            // Payment link
            'payment_link' => $this->generatePaymentLink($invoice),
            
            // Additional context
            'current_year' => date('Y'),
            'current_date' => date('d/m/Y')
        ];
        
        return $variables;
    }
    
    /**
     * Send email using PHPMailer
     */
    private function sendEmail($to, $subject, $body, $attachments = [])
    {
        try {
            $this->mailer->clearAddresses();
            $this->mailer->clearAttachments();
            
            // Recipients
            $this->mailer->setFrom(
                $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>',
                $_ENV['MAIL_FROM_NAME'] ?? 'Invoice System'
            );
            $this->mailer->addAddress($to);
            
            // Content
            $this->mailer->isHTML(true);
            $this->mailer->Subject = $subject;
            $this->mailer->Body = $body;
            $this->mailer->AltBody = strip_tags($body);
            
            // Attachments
            foreach ($attachments as $attachment) {
                $this->mailer->addAttachment($attachment['path'], $attachment['name']);
            }
            
            // Send
            $this->mailer->send();
            
            return [
                'success' => true,
                'message' => 'Email sent successfully'
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Prepare invoice attachments
     */
    private function prepareAttachments($invoice)
    {
        $attachments = [];
        
        // Generate PDF if needed
        if (Flight::get('config')['attach_pdf_to_emails'] ?? true) {
            $pdfService = new PdfService();
            $pdfPath = $pdfService->generateInvoicePdf($invoice);
            
            if ($pdfPath) {
                $attachments[] = [
                    'path' => $pdfPath,
                    'name' => 'Invoice_' . $invoice->invoice_number . '.pdf'
                ];
            }
        }
        
        return $attachments;
    }
    
    /**
     * Log email send attempt
     */
    private function logEmail($invoice, $template, $content, $result)
    {
        $stmt = $this->db->prepare("
            INSERT INTO email_logs (
                invoice_id, template_id, template_code, recipient_email,
                subject, body, status, error_message, metadata, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $invoice->id,
            $template->id,
            $template->code,
            $invoice->client->email,
            $content['subject'],
            $content['body'],
            $result['success'] ? 'sent' : 'failed',
            $result['error'] ?? null,
            json_encode($content['metadata'] ?? [])
        ]);
    }
    
    /**
     * Track template usage
     */
    private function trackTemplateUsage($template, $metadata)
    {
        // Update usage count
        $stmt = $this->db->prepare("
            UPDATE email_templates 
            SET usage_count = usage_count + 1 
            WHERE id = ?
        ");
        $stmt->execute([$template->id]);
        
        // Log usage details
        $stmt = $this->db->prepare("
            INSERT INTO template_usage_log (
                template_id, template_code, selection_metadata, context_data, created_at
            ) VALUES (?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $template->id,
            $template->code,
            json_encode($metadata),
            json_encode($this->context ?? [])
        ]);
    }
    
    /**
     * Helper methods
     */
    
    private function determineRecipientEmail($invoice, $client)
    {
        // Priority: invoice email > client email > user email
        if (!empty($invoice->recipient_email)) {
            return $invoice->recipient_email;
        }
        if (!empty($client->email)) {
            return $client->email;
        }
        if (!empty($invoice->user) && !empty($invoice->user->email)) {
            return $invoice->user->email;
        }
        return null;
    }
    
    private function isFirstInvoice($client)
    {
        $count = Invoice::where('client_id', $client->id)
            ->where('status', '!=', 'draft')
            ->count();
        return $count <= 1;
    }
    
    private function hasLatePaymentHistory($client)
    {
        $latePayments = Invoice::where('client_id', $client->id)
            ->where('paid_date', '>', 'due_date')
            ->count();
        return $latePayments > 0;
    }
    
    private function getClientAge($client)
    {
        $created = strtotime($client->created_at);
        return floor((time() - $created) / 86400);
    }
    
    private function isReminder($invoice)
    {
        // Check if any reminder email was sent for this invoice
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM email_logs 
            WHERE invoice_id = ? 
            AND template_code LIKE '%reminder%'
        ");
        $stmt->execute([$invoice->id]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function reminderSentRecently($invoice, $days = 7)
    {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM email_logs 
            WHERE invoice_id = ? 
            AND created_at > DATE_SUB(NOW(), INTERVAL ? DAY)
            AND status = 'sent'
        ");
        $stmt->execute([$invoice->id, $days]);
        return $stmt->fetchColumn() > 0;
    }
    
    private function generatePaymentLink($invoice)
    {
        $baseUrl = Flight::get('config')['base_url'] ?? 'http://localhost';
        $token = base64_encode($invoice->id . ':' . $invoice->invoice_number);
        return $baseUrl . '/pay/' . $token;
    }
    
    private function logError($exception, $invoiceId = null)
    {
        error_log(sprintf(
            "[EmailService Error] Invoice: %s, Error: %s, Trace: %s",
            $invoiceId ?? 'N/A',
            $exception->getMessage(),
            $exception->getTraceAsString()
        ));
    }
}