{% extends "base-modern.twig" %}

{% block title %}{{ __('clients.add_client') }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('common.home') }}</a></li>
<li class="breadcrumb-item"><a href="{{ base_url }}/clients">{{ __('clients.title') }}</a></li>
<li class="breadcrumb-item active">{{ __('clients.add_client') }}</li>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-xl-8">
        <form action="{{ base_url }}/clients" method="POST" id="clientForm" class="needs-validation" novalidate>
            <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
            
            <!-- Client Information Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>{{ __('clients.client_information') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Client Type -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="client_type" name="client_type" required>
                                    <option value="">{{ __('common.select') }}</option>
                                    <option value="individual">{{ __('clients.individual') }}</option>
                                    <option value="company">{{ __('clients.company') }}</option>
                                </select>
                                <label for="client_type">{{ __('clients.client_type') }} *</label>
                                <div class="invalid-feedback">
                                    {{ __('validation.required', {'field': __('clients.client_type')}) }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="status" name="status" required>
                                    <option value="1" selected>{{ __('common.active') }}</option>
                                    <option value="0">{{ __('common.inactive') }}</option>
                                </select>
                                <label for="status">{{ __('common.status') }} *</label>
                            </div>
                        </div>
                        
                        <!-- Client Number -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="client_number" name="client_number" value="{{ client_number }}" readonly required>
                                <label for="client_number">{{ __('clients.client_number') }} *</label>
                            </div>
                        </div>
                        
                        <!-- Company Name (shown for companies) -->
                        <div class="col-12" id="company_name_group" style="display: none;">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="company_name" name="company_name" placeholder="{{ __('clients.company_name') }}">
                                <label for="company_name">{{ __('clients.company_name') }} *</label>
                                <div class="invalid-feedback">
                                    {{ __('validation.required', {'field': __('clients.company_name')}) }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- First Name -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="first_name" name="first_name" placeholder="{{ __('clients.first_name') }}" required>
                                <label for="first_name">{{ __('clients.first_name') }} *</label>
                                <div class="invalid-feedback">
                                    {{ __('validation.required', {'field': __('clients.first_name')}) }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Last Name -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="last_name" name="last_name" placeholder="{{ __('clients.last_name') }}" required>
                                <label for="last_name">{{ __('clients.last_name') }} *</label>
                                <div class="invalid-feedback">
                                    {{ __('validation.required', {'field': __('clients.last_name')}) }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="email" class="form-control" id="email" name="email" placeholder="{{ __('clients.email') }}">
                                <label for="email">{{ __('clients.email') }}</label>
                                <div class="invalid-feedback">
                                    {{ __('validation.email') }}
                                </div>
                            </div>
                        </div>
                        
                        <!-- Phone -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="tel" class="form-control" id="phone" name="phone" placeholder="{{ __('clients.phone') }}">
                                <label for="phone">{{ __('clients.phone') }}</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Address Information Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-geo-alt me-2"></i>{{ __('clients.address_information') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Address Line 1 -->
                        <div class="col-12">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="address_line1" name="address_line1" placeholder="{{ __('clients.address_line1') }}">
                                <label for="address_line1">{{ __('clients.address_line1') }}</label>
                            </div>
                        </div>
                        
                        <!-- Address Line 2 -->
                        <div class="col-12">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="address_line2" name="address_line2" placeholder="{{ __('clients.address_line2') }}">
                                <label for="address_line2">{{ __('clients.address_line2') }}</label>
                            </div>
                        </div>
                        
                        <!-- City -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="city" name="city" placeholder="{{ __('clients.city') }}">
                                <label for="city">{{ __('clients.city') }}</label>
                            </div>
                        </div>
                        
                        <!-- Postal Code -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="postal_code" name="postal_code" placeholder="{{ __('clients.postal_code') }}">
                                <label for="postal_code">{{ __('clients.postal_code') }}</label>
                            </div>
                        </div>
                        
                        <!-- Country -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="country" name="country" placeholder="{{ __('clients.country') }}" value="{{ config.default_country|default('') }}">
                                <label for="country">{{ __('clients.country') }}</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Additional Information Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-card-text me-2"></i>{{ __('clients.additional_information') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- VAT Number -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="vat_number" name="vat_number" placeholder="{{ __('clients.vat_number') }}">
                                <label for="vat_number">{{ __('clients.vat_number') }}</label>
                            </div>
                        </div>
                        
                        <!-- Company Registration -->
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="company_registration" name="company_registration" placeholder="{{ __('clients.company_registration') }}">
                                <label for="company_registration">{{ __('clients.company_registration') }}</label>
                            </div>
                        </div>
                        
                        <!-- Notes -->
                        <div class="col-12">
                            <div class="form-floating">
                                <textarea class="form-control" id="notes" name="notes" placeholder="{{ __('clients.notes') }}" style="height: 120px"></textarea>
                                <label for="notes">{{ __('clients.notes') }}</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Form Actions -->
            <div class="row">
                <div class="col-12">
                    <div class="d-flex justify-content-between">
                        <a href="{{ base_url }}/clients" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>{{ __('common.cancel') }}
                        </a>
                        <div>
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-check-lg me-2"></i>{{ __('common.save') }}
                            </button>
                            <button type="submit" name="save_and_new" value="1" class="btn btn-success ms-2">
                                <i class="bi bi-plus-lg me-2"></i>{{ __('common.save_and_new') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Progress Modal -->
<div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">{{ __('common.saving') }}...</span>
                </div>
                <p class="mb-0">{{ __('common.saving') }}...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Form validation
(function() {
    'use strict';
    
    // Fetch all forms that need validation
    const forms = document.querySelectorAll('.needs-validation');
    
    // Loop over them and prevent submission
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            } else {
                // Show progress modal
                const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
                progressModal.show();
            }
            
            form.classList.add('was-validated');
        }, false);
    });
})();

// Client type toggle
document.getElementById('client_type').addEventListener('change', function() {
    const companyGroup = document.getElementById('company_name_group');
    const companyNameField = document.getElementById('company_name');
    
    if (this.value === 'company') {
        companyGroup.style.display = 'block';
        companyNameField.setAttribute('required', 'required');
        
        // Animate the appearance
        companyGroup.classList.add('animate__animated', 'animate__fadeIn');
    } else {
        companyGroup.style.display = 'none';
        companyNameField.removeAttribute('required');
        companyNameField.value = '';
    }
});

// Auto-capitalize names
document.getElementById('first_name').addEventListener('blur', function() {
    this.value = this.value.charAt(0).toUpperCase() + this.value.slice(1);
});

document.getElementById('last_name').addEventListener('blur', function() {
    this.value = this.value.charAt(0).toUpperCase() + this.value.slice(1);
});

// Phone number formatting
document.getElementById('phone').addEventListener('input', function(e) {
    // Remove all non-digit characters
    let value = e.target.value.replace(/\D/g, '');
    
    // Format as (XXX) XXX-XXXX for US numbers
    if (value.length > 0) {
        if (value.length <= 3) {
            value = `(${value}`;
        } else if (value.length <= 6) {
            value = `(${value.slice(0, 3)}) ${value.slice(3)}`;
        } else if (value.length <= 10) {
            value = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6)}`;
        } else {
            value = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
        }
    }
    
    e.target.value = value;
});

// Email validation with visual feedback
document.getElementById('email').addEventListener('blur', function() {
    const email = this.value;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    
    if (email && !emailRegex.test(email)) {
        this.classList.add('is-invalid');
    } else {
        this.classList.remove('is-invalid');
    }
});

// Character counter for notes
const notesField = document.getElementById('notes');
const maxLength = 500;

// Create character counter element
const counterDiv = document.createElement('div');
counterDiv.className = 'form-text text-end';
counterDiv.innerHTML = `<small><span id="charCount">0</span> / ${maxLength} {{ __('common.characters') }}</small>`;
notesField.parentElement.appendChild(counterDiv);

notesField.addEventListener('input', function() {
    const count = this.value.length;
    document.getElementById('charCount').textContent = count;
    
    if (count > maxLength) {
        counterDiv.classList.add('text-danger');
    } else {
        counterDiv.classList.remove('text-danger');
    }
});
</script>

<!-- Client Form Handler Script -->
<script src="{{ base_url }}/js/client-form-handler.js"></script>
<script src="{{ base_url }}/js/client-number-fix-complete.js"></script>

<!-- Immediate Client Number Fix -->
<script>
// Fix client_number immediately if it contains {PREFIX}
document.addEventListener('DOMContentLoaded', function() {
    const field = document.getElementById('client_number');
    if (field && field.value.includes('{PREFIX}')) {
        field.value = field.value.replace('{PREFIX}', 'CLT');
        console.log('Fixed client_number:', field.value);
    }
});
</script>
{% endblock %}