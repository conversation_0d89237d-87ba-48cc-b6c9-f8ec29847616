# Claude Flow Settings Documentation - Fit360 AdminDesk Project

## Overview

This document provides a comprehensive analysis of Claude Flow settings and configuration in the Fit360 AdminDesk project. The project integrates Claude Flow for AI-assisted development with advanced orchestration capabilities.

## Project Information

- **Project Name**: Fit360 AdminDesk
- **Version**: 2.5.0
- **Description**: Health Center Billing Management System
- **Tech Stack**: PHP 8.2, Flight PHP 3.x, Twig 3.0, MySQL/MariaDB
- **Working Directory**: `/mnt/d/wamp64/www/fit`

## Claude Flow Configuration

### 1. MCP (Model Context Protocol) Integration

#### MCP Servers Configuration (`.mcp.json`)
```json
{
  "mcpServers": {
    "claude-flow": {
      "command": "npx",
      "args": ["claude-flow@alpha", "mcp", "start"],
      "type": "stdio"
    },
    "ruv-swarm": {
      "command": "npx",
      "args": ["ruv-swarm@latest", "mcp", "start"],
      "type": "stdio"
    }
  }
}
```

- **Claude Flow MCP**: Uses alpha version with stdio communication
- **RUV Swarm MCP**: Latest version for swarm coordination
- **Communication Type**: Both use stdio (no port needed)

### 2. Claude Flow Configuration (`claude-flow.config.json`)

```json
{
  "features": {
    "autoTopologySelection": true,
    "parallelExecution": true,
    "neuralTraining": true,
    "bottleneckAnalysis": true,
    "smartAutoSpawning": true,
    "selfHealingWorkflows": true,
    "crossSessionMemory": true,
    "githubIntegration": true
  },
  "performance": {
    "maxAgents": 10,
    "defaultTopology": "hierarchical",
    "executionStrategy": "parallel",
    "tokenOptimization": true,
    "cacheEnabled": true,
    "telemetryLevel": "detailed"
  }
}
```

#### Enabled Features:
- **Auto Topology Selection**: Automatically selects optimal swarm structure
- **Parallel Execution**: 2.8-4.4x speed improvements
- **Neural Training**: Continuous learning from operations
- **Bottleneck Analysis**: Real-time performance optimization
- **Smart Auto-Spawning**: Zero manual agent management
- **Self-Healing Workflows**: Automatic error recovery
- **Cross-Session Memory**: Persistent learning & context
- **GitHub Integration**: Repository-aware swarms

#### Performance Settings:
- **Max Agents**: 10 concurrent agents
- **Default Topology**: Hierarchical
- **Execution Strategy**: Parallel
- **Token Optimization**: Enabled
- **Cache**: Enabled
- **Telemetry**: Detailed level

### 3. Main Configuration File (`CLAUDE.md`)

The main configuration file emphasizes:

#### Core Principles:
1. **Concurrent Execution**: ALL operations MUST be concurrent/parallel in a single message
2. **SPARC Methodology**: Specification, Pseudocode, Architecture, Refinement, Completion
3. **Batchtools Optimization**: Parallel processing capabilities enabled
4. **Claude Code as Executor**: Claude Code performs ALL actual work

#### Mandatory Patterns:
- **TodoWrite**: ALWAYS batch ALL todos in ONE call (5-10+ todos minimum)
- **Task tool**: ALWAYS spawn ALL agents in ONE message with full instructions
- **File operations**: ALWAYS batch ALL reads/writes/edits in ONE message
- **Bash commands**: ALWAYS batch ALL terminal operations in ONE message
- **Memory operations**: ALWAYS batch ALL memory store/retrieve in ONE message

#### Available Agents (54 Total):
- Core Development: coder, reviewer, tester, planner, researcher
- Swarm Coordination: hierarchical-coordinator, mesh-coordinator, adaptive-coordinator
- Consensus & Distributed: byzantine-coordinator, raft-manager, gossip-coordinator
- Performance: perf-analyzer, performance-benchmarker, task-orchestrator
- GitHub Integration: github-modes, pr-manager, code-review-swarm
- SPARC Methodology: sparc-coord, sparc-coder, specification, architecture
- Specialized: backend-dev, mobile-dev, ml-developer, api-docs

### 4. Memory System

#### Memory Store Structure:
```
memory/
├── agents/
│   └── README.md          # Agent memory documentation
├── claude-flow-data.json  # Agent and task tracking
├── memory-store.json      # Persistent memory storage
└── sessions/
    └── README.md          # Session documentation
```

#### Current Memory State:
- **Claude Flow Data**: Empty agents and tasks arrays (last updated: 1753714812947)
- **Memory Store**: Contains swarm initialization data for mesh topology with 6 agents

### 5. Project-Specific Agent Configuration

#### Agents Directory (`/agents/`):
Contains 12 specialized agent configurations for the Fit360 AdminDesk project:

1. **Backend Engineer Agent**: PHP/Flight framework development
2. **Frontend Engineer Agent**: Multi-theme UI development
3. **Mobile UI/UX Agent**: Touch-optimized interfaces
4. **Billing & Invoice Agent**: Invoice generation and VAT calculations
5. **Retrocession Agent**: Practitioner billing calculations
6. **Database Migration Agent**: Schema updates and optimization
7. **Debugger Agent**: Issue diagnosis and resolution
8. **Testing & QA Agent**: PHPUnit test creation
9. **Localization Agent**: Multi-language support (FR/EN/DE)
10. **Research & Optimization Agent**: Real-time documentation research
11. **Agent Selector/Router**: Routes requests to appropriate agents
12. **Claude Code Agent Setup**: Instructions for creating Claude Code agents

### 6. Hive Configuration (`hive-config.json`)

```json
{
  "objective": "Optimize Fit360 AdminDesk UI and templates for mobile responsiveness while auditing force permissions implementation",
  "swarmName": "ui-optimization-swarm",
  "topology": "mesh",
  "maxAgents": 8,
  "agents": [
    {"type": "UI Analyzer", "priority": "high"},
    {"type": "Frontend Developer", "priority": "high"},
    {"type": "Performance Optimizer", "priority": "medium"},
    {"type": "Responsive Tester", "priority": "medium"},
    {"type": "Template Architect", "priority": "high"},
    {"type": "Permissions Auditor", "priority": "high"},
    {"type": "UI Lead", "priority": "critical"}
  ],
  "strategy": "parallel",
  "coordination": {
    "memory_sharing": true,
    "real_time_sync": true,
    "progress_tracking": true,
    "conflict_resolution": "consensus"
  }
}
```

### 7. Taskmaster Configuration (`taskmaster-config.json`)

```json
{
  "taskmaster": {
    "project_name": "Health Center Billing System",
    "default_priority": "medium",
    "task_categories": ["backend", "frontend", "database", "testing", "documentation"],
    "task_statuses": ["todo", "in_progress", "review", "completed", "blocked"],
    "auto_save": true,
    "task_file_path": "./tasks/taskmaster-tasks.json"
  }
}
```

### 8. Roo Modes Configuration (`.roomodes`)

Contains 13 custom development modes for the project:
- **Architect**: Designs scalable architectures
- **Auto-Coder**: Writes clean, efficient code
- **Tester (TDD)**: Implements Test-Driven Development
- **Debugger**: Troubleshoots runtime issues
- **Security Reviewer**: Performs security audits
- **Documentation Writer**: Creates Markdown documentation
- **System Integrator**: Merges outputs into working systems
- **Deployment Monitor**: Observes post-launch systems
- **Optimizer**: Refactors and improves performance
- **Ask**: Task-formulation guide
- **DevOps**: Deployment and infrastructure specialist
- **SPARC Tutorial**: Onboarding assistant
- **Supabase Admin**: Database and auth specialist

### 9. Coordination Structure

```
coordination/
├── memory_bank/      # (Currently empty)
├── orchestration/    # (Currently empty)
└── subtasks/         # (Currently empty)
```

### 10. Command Line Tools

#### Windows Batch Script (`claude-flow.bat`):
- Checks for Node.js installation
- Runs Claude Flow CLI with Windows compatibility

#### PowerShell Script (`claude-flow.ps1`):
- PowerShell implementation for Claude Flow CLI
- Includes error checking and proper exit code forwarding

## Key Workflows and Patterns

### 1. SPARC Development Workflow:
1. **Specification Phase**: Create detailed specifications with concurrent analysis
2. **Pseudocode Phase**: Develop algorithmic logic with parallel pattern analysis
3. **Architecture Phase**: Design system architecture with concurrent validation
4. **Refinement Phase**: Execute TDD with parallel test generation
5. **Completion Phase**: Integration with parallel validation and documentation

### 2. Concurrent Execution Requirements:
- All related operations must be batched in a single message
- Never send multiple messages for related operations
- Use BatchTool for ALL swarm operations
- Parallel execution is MANDATORY, not optional

### 3. Memory Coordination Pattern:
```javascript
// Store coordination data
mcp__claude-flow__memory_usage {
  action: "store",
  key: "swarm-{id}/agent-{name}/{step}",
  value: {
    timestamp: Date.now(),
    decision: "what was decided",
    implementation: "what was built",
    nextSteps: ["step1", "step2"],
    dependencies: ["dep1", "dep2"]
  }
}
```

## Performance Benchmarks

### Batchtools Performance Improvements:
- **File Operations**: Up to 300% faster with parallel processing
- **Code Analysis**: 250% improvement with concurrent pattern recognition
- **Test Generation**: 400% faster with parallel test creation
- **Documentation**: 200% improvement with concurrent content generation
- **Memory Operations**: 180% faster with batched read/write operations

### Overall Benefits:
- **84.8% SWE-Bench solve rate**: Better problem-solving through coordination
- **32.3% token reduction**: Efficient task breakdown reduces redundancy
- **2.8-4.4x speed improvement**: Parallel coordination strategies
- **27+ neural models**: Diverse cognitive approaches

## Best Practices

1. **Start Simple**: Begin with basic swarm init and single agent
2. **Scale Gradually**: Add more agents as task complexity increases
3. **Use Memory**: Store important decisions and context
4. **Monitor Progress**: Regular status checks ensure effective coordination
5. **Train Patterns**: Let neural agents learn from successful coordinations
6. **Enable Hooks**: Use pre-configured hooks for automation
7. **GitHub First**: Use GitHub tools for repository management

## Integration with Fit360 AdminDesk

The Claude Flow configuration is specifically tailored for the Fit360 AdminDesk project:

1. **Domain-Specific Agents**: Custom agents for billing, retrocession, and healthcare-specific features
2. **Multi-Language Support**: Agents configured for FR/EN/DE localization
3. **Mobile Optimization**: Specific UI optimization swarm configuration
4. **Permission System**: Dedicated auditor for permission implementation
5. **Database Management**: Specialized agents for migrations and schema optimization

## Conclusion

The Fit360 AdminDesk project has a comprehensive Claude Flow setup that emphasizes:
- Parallel execution and performance optimization
- Domain-specific agent specialization
- Persistent memory and cross-session learning
- SPARC methodology integration
- Advanced swarm coordination capabilities

This configuration enables efficient AI-assisted development with proper coordination, memory persistence, and specialized domain knowledge for healthcare billing systems.