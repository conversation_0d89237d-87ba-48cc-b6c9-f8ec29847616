<?php
/**
 * Debug and check the actual rendered output
 */
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Clear all possible caches
if (function_exists('opcache_reset')) {
    opcache_reset();
    echo "✓ OPcache cleared<br>\n";
}

// Clear Twig cache completely
$cacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($cacheDir)) {
    $it = new RecursiveDirectoryIterator($cacheDir, RecursiveDirectoryIterator::SKIP_DOTS);
    $files = new RecursiveIteratorIterator($it, RecursiveIteratorIterator::CHILD_FIRST);
    foreach($files as $file) {
        if ($file->isDir()){
            rmdir($file->getRealPath());
        } else {
            unlink($file->getRealPath());
        }
    }
    echo "✓ Twig cache directory cleared<br>\n";
}

// Now let's check the actual template file
$templateFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($templateFile);

echo "<h2>Checking for problematic patterns in template:</h2>\n";
echo "<pre>";

// Check lines around 3638
$lines = explode("\n", $content);
echo "Lines 3635-3640:\n";
for ($i = 3634; $i < 3640 && $i < count($lines); $i++) {
    $lineNum = $i + 1;
    $line = $lines[$i];
    echo "Line $lineNum: " . htmlspecialchars($line) . "\n";
    
    // Check for problematic patterns
    if (preg_match('/\{\{.*\}\}/', $line) && strpos($line, '`') !== false) {
        echo "  ⚠️ FOUND: Twig expression in or near template literal!\n";
    }
}

echo "\n\nSearching for all template literals with Twig expressions:\n";

// Search for any remaining issues
$matches = [];
preg_match_all('/`[^`]*\{\{[^}]*\}\}[^`]*`/m', $content, $matches);
if (!empty($matches[0])) {
    echo "❌ FOUND ISSUES:\n";
    foreach ($matches[0] as $match) {
        echo "  - " . htmlspecialchars($match) . "\n";
    }
} else {
    echo "✅ No template literals with Twig expressions found\n";
}

// Check for multiline template literals that might have Twig
$inTemplateLiteral = false;
$templateLiteralStart = 0;
$templateLiteralContent = '';

foreach ($lines as $i => $line) {
    if (!$inTemplateLiteral && strpos($line, '`') !== false) {
        $inTemplateLiteral = true;
        $templateLiteralStart = $i + 1;
        $templateLiteralContent = $line;
    } elseif ($inTemplateLiteral) {
        $templateLiteralContent .= "\n" . $line;
        if (strpos($line, '`') !== false) {
            // Check if this template literal contains Twig
            if (preg_match('/\{\{.*\}\}/', $templateLiteralContent)) {
                echo "\n⚠️ Multiline template literal with Twig found starting at line $templateLiteralStart\n";
                echo htmlspecialchars(substr($templateLiteralContent, 0, 200)) . "...\n";
            }
            $inTemplateLiteral = false;
            $templateLiteralContent = '';
        }
    }
}

echo "</pre>";

// Check if the file was recently modified
$lastModified = filemtime($templateFile);
echo "\n<p>Template file last modified: " . date('Y-m-d H:i:s', $lastModified) . "</p>\n";

// Try to render just the problematic function
echo "\n<h2>Testing the specific function:</h2>\n";
echo "<pre>";

// Extract the addLocationItem function
$functionStart = strpos($content, 'function addLocationItem()');
if ($functionStart !== false) {
    $functionEnd = strpos($content, 'function ', $functionStart + 10);
    if ($functionEnd === false) {
        $functionEnd = strlen($content);
    }
    $functionCode = substr($content, $functionStart, $functionEnd - $functionStart);
    
    // Check for any Twig expressions
    if (preg_match_all('/\{\{[^}]*\}\}/', $functionCode, $matches)) {
        echo "Found Twig expressions in addLocationItem:\n";
        foreach ($matches[0] as $match) {
            echo "  - " . htmlspecialchars($match) . "\n";
        }
    } else {
        echo "✅ No raw Twig expressions found in addLocationItem function\n";
    }
}

echo "</pre>";

// Direct link with cache buster
$timestamp = time();
echo '<p><a href="/fit/public/invoices/create?_debug=' . $timestamp . '" target="_blank" style="padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin-top: 20px;">Open Invoice Page with Cache Buster</a></p>';
?>