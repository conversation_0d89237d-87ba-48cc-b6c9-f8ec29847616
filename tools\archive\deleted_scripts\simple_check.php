<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// Get latest invoice
$stmt = $db->query("SELECT id, invoice_number, payment_terms, period FROM invoices ORDER BY id DESC LIMIT 1");
$invoice = $stmt->fetch(\PDO::FETCH_ASSOC);

echo "<pre>";
echo "Latest Invoice:\n";
print_r($invoice);

if ($invoice) {
    // Get its lines
    $stmt = $db->prepare("SELECT * FROM invoice_lines WHERE invoice_id = ?");
    $stmt->execute([$invoice['id']]);
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo "\nInvoice Lines:\n";
    foreach ($lines as $i => $line) {
        echo ($i + 1) . ". {$line['description']} - Qty: {$line['quantity']} x {$line['unit_price']}€\n";
    }
}

// Check sequence
$stmt = $db->query("SELECT * FROM document_sequences WHERE document_type_id = 1 AND year = 2025");
$seq = $stmt->fetch(\PDO::FETCH_ASSOC);
echo "\nSequence: Last number = " . ($seq['last_number'] ?? 'N/A');
echo "</pre>";