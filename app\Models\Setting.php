<?php

namespace App\Models;

use App\Core\Model;

class Setting extends Model
{
    protected $table = 'config_settings';
    
    protected $fillable = [
        'key',
        'value',
        'group',
        'description'
    ];
    
    /**
     * Get a setting value by key
     */
    public static function get($key, $default = null)
    {
        $db = \Flight::db();
        $stmt = $db->prepare("SELECT value FROM config_settings WHERE `key` = ? LIMIT 1");
        $stmt->execute([$key]);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($result && $result['value'] !== null) {
            // Handle boolean values
            if ($result['value'] === 'true' || $result['value'] === '1') {
                return true;
            } elseif ($result['value'] === 'false' || $result['value'] === '0') {
                return false;
            }
            
            // Try to decode JSON
            $decoded = json_decode($result['value'], true);
            if (json_last_error() === JSON_ERROR_NONE) {
                return $decoded;
            }
            
            return $result['value'];
        }
        
        return $default;
    }
    
    /**
     * Set a setting value
     */
    public static function set($key, $value, $category = 'general')
    {
        $db = \Flight::db();
        
        // Convert value to string for storage
        if (is_bool($value)) {
            $value = $value ? 'true' : 'false';
        } elseif (is_array($value) || is_object($value)) {
            $value = json_encode($value);
        }
        
        // Check if setting exists
        $stmt = $db->prepare("SELECT id FROM config_settings WHERE `key` = ? LIMIT 1");
        $stmt->execute([$key]);
        $exists = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($exists) {
            // Update existing
            $stmt = $db->prepare("UPDATE config_settings SET value = ?, updated_at = NOW() WHERE `key` = ?");
            $stmt->execute([$value, $key]);
        } else {
            // Insert new
            $stmt = $db->prepare("INSERT INTO config_settings (`key`, value, category, created_at, updated_at) VALUES (?, ?, ?, NOW(), NOW())");
            $stmt->execute([$key, $value, $category]);
        }
        
        return true;
    }
    
    /**
     * Delete a setting by key
     */
    public static function deleteByKey($key)
    {
        $db = \Flight::db();
        $stmt = $db->prepare("DELETE FROM config_settings WHERE `key` = ?");
        $stmt->execute([$key]);
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Get all settings by category
     */
    public static function getByCategory($category)
    {
        $db = \Flight::db();
        $stmt = $db->prepare("SELECT * FROM config_settings WHERE category = ? ORDER BY `key`");
        $stmt->execute([$category]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all settings
     */
    public static function getAll()
    {
        $db = \Flight::db();
        $stmt = $db->query("SELECT * FROM config_settings ORDER BY category, `key`");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
}