<?php

namespace App\Controllers;

use Flight;
use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Models\Config;
use App\Models\VatRate;
use App\Models\InvoiceType;
use App\Models\EmailTemplate;
use App\Models\DocumentType;
use App\Models\ColorScheme;

class ConfigController extends Controller
{
    /**
     * Display configuration dashboard
     */
    public function index(Request $request, Response $response)
    {
        try {
            $stats = [
                'vat_rates' => VatRate::count(),
                'invoice_types' => InvoiceType::count(),
                'email_templates' => EmailTemplate::count(),
                'company_settings' => Config::where('category', 'company')->count(),
                'system_settings' => Config::where('category', 'system')->count()
            ];
        } catch (\Exception $e) {
            // If tables don't exist yet, show zeros
            $stats = [
                'vat_rates' => 0,
                'invoice_types' => 0,
                'email_templates' => 0,
                'company_settings' => 0,
                'system_settings' => 0
            ];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/index-modern';

        return $this->render($viewName, [
            'title' => __('config.configuration'),
            'stats' => $stats
        ]);
    }

    /**
     * Display VAT rates management page
     */
    public function vatRates(Request $request, Response $response)
    {
        try {
            // Use direct SQL query
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config_vat_rates ORDER BY is_default DESC, rate ASC");
            $vatRates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Decode JSON fields for display
            $currentLang = $_SESSION['lang'] ?? 'fr';
            foreach ($vatRates as &$rate) {
                if (!empty($rate['name']) && $rate['name'] !== 'null') {
                    $nameData = json_decode($rate['name'], true);
                    if ($nameData) {
                        $rate['name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $rate['code'];
                    } else {
                        $rate['name'] = $rate['code']; // Fallback to code if JSON decode fails
                    }
                } else {
                    $rate['name'] = $rate['code']; // Use code as name if name is null
                }
            }
        } catch (\Exception $e) {
            $vatRates = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/vat-rates-modern';

        return $this->render($viewName, [
            'title' => __('config.vat_rates_management'),
            'vatRates' => $vatRates
        ]);
    }

    /**
     * Store new VAT rate
     */
    public function storeVatRate(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get sanitized input data
            $data = [
                'name' => $this->input('name', ''),
                'code' => $this->input('code', ''),
                'rate' => $this->input('rate', 0, FILTER_VALIDATE_FLOAT),
                'description' => $this->input('description', ''),
                'is_default' => $this->input('is_default', 0, FILTER_VALIDATE_BOOLEAN) ? 1 : 0,
                'is_active' => $this->input('is_active', 1, FILTER_VALIDATE_BOOLEAN) ? 1 : 0,
                'effective_from' => $this->input('effective_from', '') ?: null,
                'effective_to' => $this->input('effective_to', '') ?: null
            ];
            
            // Basic validation
            if (empty($data['name'])) {
                throw new \Exception(__('config.name_required'));
            }
            if (!is_numeric($data['rate']) || $data['rate'] < 0 || $data['rate'] > 100) {
                throw new \Exception(__('config.rate_range'));
            }
            // If setting as default, unset other defaults
            if (!empty($data['is_default'])) {
                $db = Flight::db();
                $stmt = $db->prepare("UPDATE config_vat_rates SET is_default = 0 WHERE is_default = 1");
                $stmt->execute();
            }

            // Convert name to JSON for multilingual support (description is VARCHAR)
            $jsonData = $data;
            $currentLang = $_SESSION['lang'] ?? 'fr';
            // Create proper JSON object for name field only
            $jsonData['name'] = json_encode(['fr' => $data['name'], 'en' => $data['name']]);
            // Description remains as plain text
            $jsonData['description'] = $data['description'];
            
            // Set timestamps
            $jsonData['created_at'] = date('Y-m-d H:i:s');
            $jsonData['updated_at'] = date('Y-m-d H:i:s');
            
            // Insert directly for now
            $db = Flight::db();
            $sql = "INSERT INTO config_vat_rates (name, code, rate, description, is_default, is_active, effective_from, effective_to, created_at, updated_at) 
                    VALUES (:name, :code, :rate, :description, :is_default, :is_active, :effective_from, :effective_to, :created_at, :updated_at)";
            $stmt = $db->prepare($sql);
            try {
                $result = $stmt->execute($jsonData);
            } catch (\PDOException $e) {
                // Check for duplicate code error
                if ($e->getCode() == '23000' && strpos($e->getMessage(), 'Duplicate entry') !== false) {
                    throw new \Exception(__('config.vat_rate_code_exists'));
                }
                throw new \Exception(__('config.vat_rate_insert_failed'));
            }
            
            if (!$result) {
                throw new \Exception(__('config.vat_rate_insert_failed'));
            }
            
            $data['id'] = $db->lastInsertId();

            // Return JSON response
            echo json_encode([
                'success' => true,
                'message' => __('config.vat_rate_created'),
                'data' => $data
            ]);
            exit;
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('VAT Rate Error: ' . $e->getMessage());
            error_log('VAT Rate Trace: ' . $e->getTraceAsString());
            
            // Return error JSON
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => __('config.vat_rate_create_failed', ['error' => $e->getMessage()]),
                'debug' => $_ENV['APP_DEBUG'] === 'true' ? [
                    'error' => $e->getMessage(),
                    'data' => $data ?? null
                ] : null
            ]);
            exit;
        }
    }

    /**
     * Bulk delete VAT rates
     */
    public function bulkDeleteVatRates(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $ids = $this->input('ids', []);
            if (empty($ids) || !is_array($ids)) {
                throw new \Exception(__('config.no_items_selected'));
            }
            
            $db = Flight::db();
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            
            // Don't delete default VAT rates
            $stmt = $db->prepare("DELETE FROM config_vat_rates WHERE id IN ($placeholders) AND is_default = 0");
            $stmt->execute($ids);
            
            $deleted = $stmt->rowCount();
            
            echo json_encode([
                'success' => true,
                'message' => __('config.vat_rates_deleted', ['count' => $deleted])
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => __('config.delete_failed', ['error' => $e->getMessage()])
            ]);
            exit;
        }
    }
    
    /**
     * Export VAT rates
     */
    public function exportVatRates(Request $request, Response $response)
    {
        try {
            $db = Flight::db();
            $stmt = $db->query("SELECT code, rate, description, is_default FROM config_vat_rates ORDER BY rate ASC");
            $vatRates = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="vat_rates_' . date('Y-m-d') . '.csv"');
            
            $output = fopen('php://output', 'w');
            fputcsv($output, ['Code', 'Rate', 'Description', 'Is Default']);
            
            foreach ($vatRates as $rate) {
                fputcsv($output, [
                    $rate['code'],
                    $rate['rate'],
                    $rate['description'],
                    $rate['is_default'] ? 'Yes' : 'No'
                ]);
            }
            
            fclose($output);
            exit;
        } catch (\Exception $e) {
            $this->redirect(Flight::get('flight.base_url') . '/config/vat-rates');
        }
    }
    
    /**
     * Import VAT rates
     */
    public function importVatRates(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                throw new \Exception(__('config.no_file_uploaded'));
            }
            
            $file = $_FILES['file']['tmp_name'];
            $handle = fopen($file, 'r');
            
            if (!$handle) {
                throw new \Exception('Failed to open file');
            }
            
            $db = Flight::db();
            $replaceExisting = $this->input('replace_existing', false, FILTER_VALIDATE_BOOLEAN);
            
            if ($replaceExisting) {
                $db->exec("DELETE FROM config_vat_rates");
            }
            
            // Skip header row
            fgetcsv($handle);
            
            $imported = 0;
            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) >= 4) {
                    try {
                        $stmt = $db->prepare("INSERT INTO config_vat_rates (code, rate, description, is_default, is_active) 
                                            VALUES (?, ?, ?, ?, 1) 
                                            ON DUPLICATE KEY UPDATE rate = VALUES(rate), description = VALUES(description)");
                        $stmt->execute([
                            $data[0], // code
                            floatval($data[1]), // rate
                            $data[2], // description
                            strtolower($data[3]) === 'yes' ? 1 : 0 // is_default
                        ]);
                        $imported++;
                    } catch (\Exception $e) {
                        // Skip invalid rows
                    }
                }
            }
            
            fclose($handle);
            
            echo json_encode([
                'success' => true,
                'message' => __('config.vat_rates_imported', ['count' => $imported])
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => __('config.import_failed', ['error' => $e->getMessage()])
            ]);
            exit;
        }
    }
    
    /**
     * Update VAT rate
     */
    public function updateVatRate(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get sanitized input data
            $data = [
                'name' => $this->input('name', ''),
                'code' => $this->input('code', ''),
                'rate' => $this->input('rate', 0, FILTER_VALIDATE_FLOAT),
                'description' => $this->input('description', ''),
                'is_default' => $this->input('is_default', 0, FILTER_VALIDATE_BOOLEAN) ? 1 : 0,
                'is_active' => $this->input('is_active', 1, FILTER_VALIDATE_BOOLEAN) ? 1 : 0,
                'effective_from' => $this->input('effective_from', '') ?: null,
                'effective_to' => $this->input('effective_to', '') ?: null
            ];
            
            // Basic validation
            if (empty($data['name'])) {
                throw new \Exception(__('config.name_required'));
            }
            if (!is_numeric($data['rate']) || $data['rate'] < 0 || $data['rate'] > 100) {
                throw new \Exception(__('config.rate_range'));
            }
            
            // If setting as default, unset other defaults
            if (!empty($data['is_default'])) {
                $db = Flight::db();
                $stmt = $db->prepare("UPDATE config_vat_rates SET is_default = 0 WHERE is_default = 1 AND id != ?");
                $stmt->execute([$id]);
            }
            
            // Convert name to JSON for multilingual support (description is VARCHAR)
            $jsonData = $data;
            $currentLang = $_SESSION['lang'] ?? 'fr';
            // Create proper JSON object for name field only
            $jsonData['name'] = json_encode(['fr' => $data['name'], 'en' => $data['name']]);
            // Description remains as plain text
            $jsonData['description'] = $data['description'];
            
            // Set update timestamp
            $jsonData['updated_at'] = date('Y-m-d H:i:s');
            
            // Update directly
            $db = Flight::db();
            $stmt = $db->prepare("UPDATE config_vat_rates SET name = :name, code = :code, rate = :rate, description = :description, 
                                  is_default = :is_default, is_active = :is_active, effective_from = :effective_from, 
                                  effective_to = :effective_to, updated_at = :updated_at WHERE id = :id");
            $jsonData['id'] = $id;
            $result = $stmt->execute($jsonData);
            
            if (!$result || $stmt->rowCount() === 0) {
                throw new \Exception('Failed to update VAT rate or rate not found');
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.vat_rate_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => __('config.update_failed', ['error' => $e->getMessage()])
            ]);
            exit;
        }
    }

    /**
     * Set default VAT rate
     */
    public function setDefaultVatRate(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $db = Flight::db();
            
            // First, unset all defaults
            $stmt = $db->prepare("UPDATE config_vat_rates SET is_default = 0 WHERE is_default = 1");
            $stmt->execute();
            
            // Then set the new default
            $stmt = $db->prepare("UPDATE config_vat_rates SET is_default = 1 WHERE id = ?");
            $stmt->execute([$id]);
            
            if ($stmt->rowCount() === 0) {
                throw new \Exception('VAT rate not found');
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.default_vat_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => __('config.set_default_failed', ['error' => $e->getMessage()])
            ]);
            exit;
        }
    }
    
    /**
     * Delete VAT rate
     */
    public function deleteVatRate(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $db = Flight::db();
            
            // Check if it's default
            $stmt = $db->prepare("SELECT is_default FROM config_vat_rates WHERE id = ?");
            $stmt->execute([$id]);
            $vatRate = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$vatRate) {
                throw new \Exception('VAT rate not found');
            }
            
            if ($vatRate['is_default']) {
                throw new \Exception('Cannot delete the default VAT rate');
            }
            
            // Delete the VAT rate
            $stmt = $db->prepare("DELETE FROM config_vat_rates WHERE id = ?");
            $stmt->execute([$id]);
            
            echo json_encode([
                'success' => true,
                'message' => __('config.vat_rate_deleted')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code($e->getMessage() === 'Cannot delete the default VAT rate' ? 400 : 500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }


    /**
     * Display invoice types management page
     */
    public function invoiceTypes(Request $request, Response $response)
    {
        $invoiceTypes = [];
        
        try {
            // Use direct SQL query
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config_invoice_types ORDER BY id ASC");
            $invoiceTypes = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Log the count for debugging
            error_log('Document types found: ' . count($invoiceTypes));
            
            // Decode JSON fields for display
            $currentLang = $_SESSION['lang'] ?? 'fr';
            foreach ($invoiceTypes as &$type) {
                // Decode name
                if (!empty($type['name']) && $type['name'] !== 'null') {
                    $nameData = json_decode($type['name'], true);
                    if ($nameData) {
                        $type['name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $type['code'];
                    }
                } else {
                    $type['name'] = $type['code']; // Fallback to code
                }
                
                // Decode description
                if (!empty($type['description']) && $type['description'] !== 'null') {
                    $descData = json_decode($type['description'], true);
                    if ($descData) {
                        $type['description'] = $descData[$currentLang] ?? $descData['fr'] ?? '';
                    }
                } else {
                    $type['description'] = '';
                }
                
                // Add prefix field for backward compatibility with view
                $type['prefix'] = $type['code']; // Use code as prefix
                
                // Add format field for backward compatibility
                // Also normalize the format placeholders to lowercase
                $format = $type['number_format'] ?? '{prefix}-{year}-{number:5}';
                $format = str_replace('{YEAR}', '{year}', $format);
                $format = str_replace('{CODE}', '{prefix}', $format);
                $format = str_replace('{NUMBER:', '{number:', $format);
                $type['format'] = $format;
                
                // Ensure other fields have defaults
                if (!empty($type['icon'])) {
                    // Convert Font Awesome icons to Bootstrap Icons if needed
                    $iconMap = [
                        'fas fa-file-invoice' => 'bi bi-file-text',
                        'fas fa-file-invoice-dollar' => 'bi bi-file-earmark-arrow-down',
                        'fas fa-hand-holding-usd' => 'bi bi-cash-coin',
                        'fas fa-ticket-alt' => 'bi bi-receipt'
                    ];
                    $type['icon'] = $iconMap[$type['icon']] ?? $type['icon'];
                } else {
                    $type['icon'] = 'bi bi-file-text';
                }
                
                $type['color'] = !empty($type['color']) ? $type['color'] : '#6366f1';
                $type['next_number'] = isset($type['next_number']) ? (int)$type['next_number'] : 1;
                $type['is_active'] = isset($type['is_active']) ? (int)$type['is_active'] : 1;
                
                // Check if this type has invoices (for now, set to false)
                // TODO: Add actual check against invoices table
                $type['has_invoices'] = false;
            }
        } catch (\Exception $e) {
            error_log('Error fetching invoice types: ' . $e->getMessage());
            $invoiceTypes = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/invoice-types-modern';

        return $this->render($viewName, [
            'title' => __('config.invoice_types_management'),
            'invoiceTypes' => $invoiceTypes,
            'timestamp' => time() // Add timestamp for cache busting
        ]);
    }

    /**
     * Store new invoice type
     */
    public function storeInvoiceType(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $name = $this->input('name', '');
            $code = strtoupper($this->input('code', ''));
            $description = $this->input('description', '');
            $format = $this->input('format', '{prefix}-{year}-{number:5}');
            $icon = $this->input('icon', 'bi bi-file-text');
            $color = $this->input('color', '#6366f1');
            $nextNumber = $this->input('next_number', 1);
            $isActive = $this->input('is_active', 1, FILTER_VALIDATE_BOOLEAN) ? 1 : 0;
            
            // Basic validation
            if (empty($name)) {
                throw new \Exception(__('config.name_required'));
            }
            if (empty($code)) {
                throw new \Exception(__('config.code_required'));
            }
            
            // Prepare JSON fields
            $currentLang = $_SESSION['lang'] ?? 'fr';
            $nameJson = json_encode([$currentLang => $name]);
            $descriptionJson = json_encode([$currentLang => $description]);
            
            // Check if code already exists
            $db = Flight::db();
            $stmt = $db->prepare("SELECT COUNT(*) FROM config_invoice_types WHERE code = ?");
            $stmt->execute([$code]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.document_type_code_exists'));
            }
            
            // Insert new invoice type
            $sql = "INSERT INTO config_invoice_types (name, description, code, number_format, icon, color, next_number, is_active, numbering_pattern, year_reset, is_system, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'sequential', 0, 0, NOW())";
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([
                $nameJson,
                $descriptionJson,
                $code,
                $format,
                $icon,
                $color,
                $nextNumber,
                $isActive
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.document_type_create_failed'));
            }

            // Return JSON response
            echo json_encode([
                'success' => true,
                'message' => __('config.document_type_created')
            ]);
            exit;
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log('Invoice Type Error: ' . $e->getMessage());
            
            // Return error JSON
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to create invoice type: ' . $e->getMessage(),
                'debug' => $_ENV['APP_DEBUG'] === 'true' ? [
                    'error' => $e->getMessage(),
                    'data' => $data ?? null
                ] : null
            ]);
            exit;
        }
    }

    /**
     * Update invoice type
     */
    public function updateInvoiceType(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $name = $this->input('name', '');
            $code = strtoupper($this->input('code', ''));
            $description = $this->input('description', '');
            $format = $this->input('format', '{prefix}-{year}-{number:5}');
            $icon = $this->input('icon', 'bi bi-file-text');
            $color = $this->input('color', '#6366f1');
            $nextNumber = $this->input('next_number', 1);
            $isActive = $this->input('is_active', 1, FILTER_VALIDATE_BOOLEAN) ? 1 : 0;
            
            // Basic validation
            if (empty($name)) {
                throw new \Exception(__('config.name_required'));
            }
            if (empty($code)) {
                throw new \Exception(__('config.code_required'));
            }
            
            $db = Flight::db();
            
            // Get current invoice type to preserve existing translations
            $stmt = $db->prepare("SELECT name, description FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$id]);
            $current = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$current) {
                throw new \Exception('Document type not found');
            }
            
            // Prepare JSON fields - merge with existing translations
            $currentLang = $_SESSION['lang'] ?? 'fr';
            
            // Handle name
            $nameData = json_decode($current['name'], true) ?: [];
            $nameData[$currentLang] = $name;
            $nameJson = json_encode($nameData);
            
            // Handle description
            $descData = json_decode($current['description'], true) ?: [];
            $descData[$currentLang] = $description;
            $descriptionJson = json_encode($descData);
            
            // Check if code already exists for other records
            $stmt = $db->prepare("SELECT COUNT(*) FROM config_invoice_types WHERE code = ? AND id != ?");
            $stmt->execute([$code, $id]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.document_type_code_exists'));
            }
            
            // Update invoice type
            $sql = "UPDATE config_invoice_types 
                    SET name = ?, description = ?, code = ?, number_format = ?, icon = ?, color = ?, next_number = ?, is_active = ?, updated_at = NOW()
                    WHERE id = ?";
            $stmt = $db->prepare($sql);
            $result = $stmt->execute([
                $nameJson,
                $descriptionJson,
                $code,
                $format,
                $icon,
                $color,
                $nextNumber,
                $isActive,
                $id
            ]);
            
            if (!$result || $stmt->rowCount() === 0) {
                throw new \Exception(__('config.document_type_update_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.document_type_updated')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Delete invoice type
     */
    public function deleteInvoiceType(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $db = Flight::db();
            
            // Check if invoice type exists
            $stmt = $db->prepare("SELECT * FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$id]);
            $invoiceType = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$invoiceType) {
                throw new \Exception('Document type not found');
            }
            
            // Check if invoice type is in use (you may want to add this check later)
            // For now, we'll just delete it
            
            // Delete the invoice type
            $stmt = $db->prepare("DELETE FROM config_invoice_types WHERE id = ?");
            $stmt->execute([$id]);
            
            echo json_encode([
                'success' => true,
                'message' => __('config.document_type_deleted')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code($e->getMessage() === 'Document type not found' ? 404 : 500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Reorder invoice types
     * Note: This method is kept for compatibility but does nothing as the table has no order column
     */
    public function reorderInvoiceTypes(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        // Since the table doesn't have an order column, just return success
        echo json_encode([
            'success' => true,
            'message' => 'Document types order saved'
        ]);
        exit;
    }

    /**
     * Display email templates management page
     */
    public function emailTemplates(Request $request, Response $response)
    {
        try {
            $emailTemplates = EmailTemplate::orderBy('name', 'ASC')->get();
        } catch (\Exception $e) {
            $emailTemplates = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/email-templates-modern';

        return $this->render($viewName, [
            'title' => __('config.email_templates_management'),
            'emailTemplates' => $emailTemplates
        ]);
    }

    /**
     * Display company settings page
     */
    public function companySettings(Request $request, Response $response)
    {
        try {
            // Use direct SQL query
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config WHERE category = 'company'");
            $settingsArray = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Convert to key-value format
            $settings = [];
            foreach ($settingsArray as $setting) {
                $settings[$setting['key']] = $setting;
            }
        } catch (\Exception $e) {
            $settings = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/company-modern';

        return $this->render($viewName, [
            'title' => __('config.company_settings'),
            'settings' => $settings
        ]);
    }

    /**
     * Update company settings
     */
    public function updateCompanySettings(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $data = [
                'company_name' => $this->input('company_name', ''),
                'company_email' => $this->input('company_email', ''),
                'company_phone' => $this->input('company_phone', ''),
                'company_address' => $this->input('company_address', ''),
                'company_city' => $this->input('company_city', ''),
                'company_postal_code' => $this->input('company_postal_code', ''),
                'company_country' => $this->input('company_country', ''),
                'company_tax_id' => $this->input('company_tax_id', ''),
                'company_registration_number' => $this->input('company_registration_number', ''),
                'company_iban' => $this->input('company_iban', ''),
                'company_bank' => $this->input('company_bank', ''),
                'company_bank_account_holder' => $this->input('company_bank_account_holder', ''),
                'company_bic_swift' => $this->input('company_bic_swift', ''),
                'company_bank_reference' => $this->input('company_bank_reference', ''),
                'company_website' => $this->input('company_website', ''),
                'company_legal_form' => $this->input('company_legal_form', ''),
                'company_court_registry' => $this->input('company_court_registry', ''),
                'company_capital_amount' => $this->input('company_capital_amount', ''),
                'invoice_footer' => $this->input('invoice_footer', ''),
                'invoice_due_days' => $this->input('invoice_due_days', 30),
                'invoice_late_fee' => $this->input('invoice_late_fee', 0)
            ];
            
            // Handle logo upload
            if (isset($_FILES['company_logo']) && $_FILES['company_logo']['error'] === UPLOAD_ERR_OK) {
                $uploadDir = dirname(__DIR__, 2) . '/public/uploads/logos/';
                
                // Create directory if it doesn't exist
                if (!is_dir($uploadDir)) {
                    mkdir($uploadDir, 0777, true);
                }
                
                // Generate unique filename
                $extension = pathinfo($_FILES['company_logo']['name'], PATHINFO_EXTENSION);
                $filename = 'company_logo_' . time() . '.' . $extension;
                $uploadPath = $uploadDir . $filename;
                
                // Validate file type
                $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                $finfo = finfo_open(FILEINFO_MIME_TYPE);
                $mimeType = finfo_file($finfo, $_FILES['company_logo']['tmp_name']);
                finfo_close($finfo);
                
                if (!in_array($mimeType, $allowedTypes)) {
                    throw new \Exception(__('config.invalid_file_type'));
                }
                
                // Move uploaded file
                if (move_uploaded_file($_FILES['company_logo']['tmp_name'], $uploadPath)) {
                    // Delete old logo if exists
                    $db = Flight::db();
                    $stmt = $db->prepare("SELECT value FROM config WHERE `key` = 'company_logo' AND category = 'company'");
                    $stmt->execute();
                    $oldLogo = $stmt->fetchColumn();
                    
                    if ($oldLogo && file_exists(dirname(__DIR__, 2) . '/public/' . $oldLogo)) {
                        unlink(dirname(__DIR__, 2) . '/public/' . $oldLogo);
                    }
                    
                    // Save relative path
                    $data['company_logo'] = 'uploads/logos/' . $filename;
                } else {
                    throw new \Exception(__('config.upload_failed'));
                }
            }
            
            // Basic validation
            if (empty($data['company_name'])) {
                throw new \Exception(__('config.company_name_required'));
            }
            if (empty($data['company_email']) || !filter_var($data['company_email'], FILTER_VALIDATE_EMAIL)) {
                throw new \Exception(__('config.valid_email_required'));
            }
            
            $db = Flight::db();
            $db->beginTransaction();
            
            try {
                foreach ($data as $key => $value) {
                    // Check if the config key exists
                    $stmt = $db->prepare("SELECT id FROM config WHERE `key` = ? AND category = 'company'");
                    $stmt->execute([$key]);
                    $exists = $stmt->fetch(\PDO::FETCH_ASSOC);
                    
                    if ($exists) {
                        // Update existing
                        $stmt = $db->prepare("UPDATE config SET value = ?, updated_at = NOW() WHERE `key` = ? AND category = 'company'");
                        $stmt->execute([$value, $key]);
                    } else {
                        // Insert new
                        $stmt = $db->prepare("INSERT INTO config (`key`, value, category, created_at, updated_at) VALUES (?, ?, 'company', NOW(), NOW())");
                        $stmt->execute([$key, $value]);
                    }
                }
                
                $db->commit();
                
                echo json_encode([
                    'success' => true,
                    'message' => __('config.company_settings_updated')
                ]);
                exit;
            } catch (\Exception $e) {
                $db->rollBack();
                throw $e;
            }
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Display system settings page
     */
    public function systemSettings(Request $request, Response $response)
    {
        try {
            // Use direct SQL query
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config WHERE category = 'system'");
            $settingsArray = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Convert to key-value format
            $settings = [];
            foreach ($settingsArray as $setting) {
                $settings[$setting['key']] = $setting;
            }
        } catch (\Exception $e) {
            $settings = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/system-modern';

        return $this->render($viewName, [
            'title' => __('config.system_settings'),
            'settings' => $settings
        ]);
    }

    /**
     * Update system settings
     */
    public function updateSystemSettings(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get all input data
            $data = Flight::request()->data->getData();
            
            // Remove the _method and csrf_token fields if present
            unset($data['_method']);
            unset($data['csrf_token']);
            
            // Debug log
            error_log('Received data: ' . json_encode($data));
            
            // Get existing settings to fill in missing values
            $db = Flight::db();
            $stmt = $db->query("SELECT `key`, `value` FROM config WHERE category = 'system'");
            $results = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Convert to key-value array
            $existingSettings = [];
            foreach ($results as $row) {
                $existingSettings[$row['key']] = $row['value'];
            }
            
            // Define all expected fields with defaults
            $defaults = [
                'app_name' => 'Fit360 AdminDesk',
                'app_url' => 'http://localhost',
                'app_timezone' => 'UTC',
                'app_locale' => 'en',
                'app_currency' => 'USD',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i',
                'invoice_number_format' => 'INV-{YEAR}-{NUMBER}',
                'invoice_start_number' => '1',
                'app_template' => 'adminlte'
            ];
            
            // Merge with existing values and defaults
            foreach ($defaults as $key => $defaultValue) {
                if (!isset($data[$key])) {
                    // Use existing value if available, otherwise use default
                    $data[$key] = $existingSettings[$key] ?? $defaultValue;
                }
            }
            
            // Basic validation only for truly required fields
            $required = ['app_name'];
            
            foreach ($required as $field) {
                if (empty($data[$field])) {
                    throw new \Exception("The {$field} field is required");
                }
            }
            
            // Handle checkbox for maintenance mode
            if (!isset($data['maintenance_mode'])) {
                $data['maintenance_mode'] = '0';
            }
            
            // Update settings
            foreach ($data as $key => $value) {
                try {
                    // First check if setting exists
                    $checkStmt = $db->prepare("SELECT id FROM config WHERE `key` = ? AND category = 'system'");
                    $checkStmt->execute([$key]);
                    $exists = $checkStmt->fetch();
                    
                    if ($exists) {
                        // Update existing
                        $updateStmt = $db->prepare("UPDATE config SET `value` = ? WHERE `key` = ? AND category = 'system'");
                        $updateStmt->execute([$value, $key]);
                    } else {
                        // Insert new
                        $insertStmt = $db->prepare("INSERT INTO config (`key`, `value`, category) VALUES (?, ?, 'system')");
                        $insertStmt->execute([$key, $value]);
                    }
                } catch (\PDOException $e) {
                    throw new \Exception("Database error for key '{$key}': " . $e->getMessage());
                }
            }
            
            // If app_template was changed, update session
            if (isset($data['app_template'])) {
                $_SESSION['template'] = $data['app_template'];
                // Also ensure it's saved to database
                error_log("Template changed to: " . $data['app_template']);
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'System settings updated successfully'
            ]);
            exit;
        } catch (\Exception $e) {
            error_log('System settings update error: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update system settings: ' . $e->getMessage(),
                'debug' => $_ENV['APP_DEBUG'] === 'true' ? [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'data' => $data ?? []
                ] : null
            ]);
            exit;
        }
    }

    /**
     * Display number formats configuration page
     */
    public function numberFormats(Request $request, Response $response)
    {
        try {
            // Use direct SQL query
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config WHERE category = 'number_format'");
            $settingsArray = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Convert to key-value format
            $settings = [];
            foreach ($settingsArray as $setting) {
                $settings[$setting['key']] = $setting;
            }

            // Default number format settings if not exist
            $defaults = [
                'client_number_format' => 'CLT-{YEAR}-{NUMBER:4}',
                'client_number_prefix' => 'CLT',
                'client_number_length' => '4',
                'client_number_separator' => '-',
                'patient_number_format' => 'PAT-{YEAR}-{NUMBER:4}',
                'patient_number_prefix' => 'PAT',
                'patient_number_length' => '4',
                'patient_number_separator' => '-',
                'invoice_number_format' => 'INV-{YEAR}-{NUMBER:4}',
                'invoice_number_prefix' => 'INV',
                'invoice_number_length' => '4',
                'invoice_number_separator' => '-'
            ];

            // Apply defaults for missing settings
            foreach ($defaults as $key => $value) {
                if (!isset($settings[$key])) {
                    $settings[$key] = [
                        'key' => $key,
                        'value' => $value,
                        'category' => 'number_format'
                    ];
                }
            }
        } catch (\Exception $e) {
            $settings = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/number-formats-modern';

        return $this->render($viewName, [
            'title' => __('config.number_formats_configuration'),
            'settings' => $settings
        ]);
    }

    /**
     * Update number formats settings
     */
    public function updateNumberFormats(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get all input data
            $data = Flight::request()->data->getData();
            
            // Remove the _method and csrf_token fields if present
            unset($data['_method']);
            unset($data['csrf_token']);
            
            // Validate number format fields
            $requiredFields = [
                'client_number_format', 'client_number_prefix', 'client_number_length',
                'patient_number_format', 'patient_number_prefix', 'patient_number_length',
                'invoice_number_format', 'invoice_number_prefix', 'invoice_number_length'
            ];
            
            foreach ($requiredFields as $field) {
                if (empty($data[$field])) {
                    throw new \Exception("The {$field} field is required");
                }
            }
            
            // Validate number lengths are numeric
            $lengthFields = ['client_number_length', 'patient_number_length', 'invoice_number_length'];
            foreach ($lengthFields as $field) {
                if (!is_numeric($data[$field]) || $data[$field] < 1 || $data[$field] > 10) {
                    throw new \Exception("The {$field} must be a number between 1 and 10");
                }
            }
            
            // Validate format patterns
            $formatFields = ['client_number_format', 'patient_number_format', 'invoice_number_format'];
            foreach ($formatFields as $field) {
                if (!$this->validateFormatPattern($data[$field])) {
                    throw new \Exception("Invalid format pattern for {$field}. Use placeholders like {PREFIX}, {YEAR}, {MONTH}, {NUMBER:N}");
                }
            }
            
            // Update settings
            $db = Flight::db();
            foreach ($data as $key => $value) {
                try {
                    // First check if setting exists
                    $checkStmt = $db->prepare("SELECT id FROM config WHERE `key` = ? AND category = 'number_format'");
                    $checkStmt->execute([$key]);
                    $exists = $checkStmt->fetch();
                    
                    if ($exists) {
                        // Update existing
                        $updateStmt = $db->prepare("UPDATE config SET `value` = ? WHERE `key` = ? AND category = 'number_format'");
                        $updateStmt->execute([$value, $key]);
                    } else {
                        // Insert new
                        $insertStmt = $db->prepare("INSERT INTO config (`key`, `value`, category) VALUES (?, ?, 'number_format')");
                        $insertStmt->execute([$key, $value]);
                    }
                } catch (\PDOException $e) {
                    throw new \Exception("Database error for key '{$key}': " . $e->getMessage());
                }
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Number format settings updated successfully'
            ]);
            exit;
        } catch (\Exception $e) {
            error_log('Number formats update error: ' . $e->getMessage());
            
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'message' => 'Failed to update number formats: ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Validate format pattern
     */
    private function validateFormatPattern($pattern)
    {
        // Check if pattern is not empty
        if (empty($pattern)) {
            return false;
        }
        
        // Check for valid placeholders
        $validPlaceholders = [
            '{PREFIX}',
            '{YEAR}',
            '{YEAR:2}',
            '{MONTH}',
            '{DAY}',
            '{NUMBER}',
            '{NUMBER:1}', '{NUMBER:2}', '{NUMBER:3}', '{NUMBER:4}', 
            '{NUMBER:5}', '{NUMBER:6}', '{NUMBER:7}', '{NUMBER:8}', 
            '{NUMBER:9}', '{NUMBER:10}'
        ];
        
        // Extract all placeholders from the pattern
        preg_match_all('/\{[^}]+\}/', $pattern, $matches);
        
        // Check if all placeholders are valid
        foreach ($matches[0] as $placeholder) {
            if (!in_array($placeholder, $validPlaceholders)) {
                return false;
            }
        }
        
        // Check if pattern contains at least one placeholder
        if (count($matches[0]) === 0) {
            return false;
        }
        
        // Check if pattern contains either {NUMBER} or {NUMBER:N}
        $hasNumber = false;
        foreach ($matches[0] as $placeholder) {
            if (strpos($placeholder, '{NUMBER') === 0) {
                $hasNumber = true;
                break;
            }
        }
        
        return $hasNumber;
    }
    
    /**
     * Display payment methods management page
     */
    public function paymentMethods(Request $request, Response $response)
    {
        try {
            // Get payment methods
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM payment_methods ORDER BY name ASC");
            $paymentMethods = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Decode JSON names for current language
            $currentLang = $_SESSION['lang'] ?? 'fr';
            foreach ($paymentMethods as &$method) {
                if (!empty($method['name']) && $method['name'] !== 'null') {
                    $nameData = json_decode($method['name'], true);
                    if ($nameData) {
                        $method['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $method['code'];
                    } else {
                        $method['display_name'] = $method['code'];
                    }
                } else {
                    $method['display_name'] = $method['code'];
                }
            }
        } catch (\Exception $e) {
            $paymentMethods = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/payment-methods-modern';

        return $this->render($viewName, [
            'title' => __('config.payment_methods'),
            'paymentMethods' => $paymentMethods
        ]);
    }
    
    /**
     * Create payment method
     */
    public function createPaymentMethod(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $name = $this->input('name', '');
            $code = strtoupper($this->input('code', ''));
            $isActive = $this->input('is_active', 1);
            
            // Validation
            if (empty($name) || empty($code)) {
                throw new \Exception(__('validation.required_fields_missing'));
            }
            
            // Prepare multi-language name
            $currentLang = $_SESSION['lang'] ?? 'fr';
            $nameData = [$currentLang => $name];
            
            $db = Flight::db();
            
            // Check if code already exists
            $stmt = $db->prepare("SELECT COUNT(*) FROM payment_methods WHERE code = ?");
            $stmt->execute([$code]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.payment_method_code_exists'));
            }
            
            // Insert payment method
            $stmt = $db->prepare("
                INSERT INTO payment_methods (name, code, is_active, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            
            $result = $stmt->execute([
                json_encode($nameData, JSON_UNESCAPED_UNICODE),
                $code,
                $isActive
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.payment_method_create_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.payment_method_created')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update payment method
     */
    public function updatePaymentMethod(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $name = $this->input('name', '');
            $code = strtoupper($this->input('code', ''));
            $isActive = $this->input('is_active', 1);
            
            // Validation
            if (empty($name) || empty($code)) {
                throw new \Exception(__('validation.required_fields_missing'));
            }
            
            $db = Flight::db();
            
            // Check if payment method exists
            $stmt = $db->prepare("SELECT * FROM payment_methods WHERE id = ?");
            $stmt->execute([$id]);
            $method = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$method) {
                throw new \Exception('Payment method not found');
            }
            
            // Check if code already exists (excluding current)
            $stmt = $db->prepare("SELECT COUNT(*) FROM payment_methods WHERE code = ? AND id != ?");
            $stmt->execute([$code, $id]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.payment_method_code_exists'));
            }
            
            // Update multi-language name
            $currentLang = $_SESSION['lang'] ?? 'fr';
            $existingName = json_decode($method['name'], true) ?: [];
            $existingName[$currentLang] = $name;
            
            // Update payment method
            $stmt = $db->prepare("
                UPDATE payment_methods 
                SET name = ?, code = ?, is_active = ?
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                json_encode($existingName, JSON_UNESCAPED_UNICODE),
                $code,
                $isActive,
                $id
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.payment_method_update_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.payment_method_updated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code($e->getMessage() === 'Payment method not found' ? 404 : 400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Delete payment method
     */
    public function deletePaymentMethod(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $db = Flight::db();
            
            // Check if payment method exists
            $stmt = $db->prepare("SELECT * FROM payment_methods WHERE id = ?");
            $stmt->execute([$id]);
            if (!$stmt->fetch()) {
                throw new \Exception('Payment method not found');
            }
            
            // Check if payment method is used in payments
            $stmt = $db->prepare("SELECT COUNT(*) FROM payments WHERE method_id = ?");
            $stmt->execute([$id]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.payment_method_in_use'));
            }
            
            // Delete payment method
            $stmt = $db->prepare("DELETE FROM payment_methods WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new \Exception('Failed to delete payment method');
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.payment_method_deleted')
            ]);
            exit;
        } catch (\Exception $e) {
            http_response_code($e->getMessage() === 'Payment method not found' ? 404 : 500);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Document Types Management
     */
    public function documentTypes()
    {
        $documentTypes = DocumentType::all()->toArray();
        
        // Get current sequences for each document type
        $db = Flight::db();
        $currentYear = date('Y');
        $currentMonth = date('m');
        
        foreach ($documentTypes as &$type) {
            // Get the current sequence based on counter type
            $query = "SELECT last_number FROM document_sequences WHERE document_type_id = ?";
            $params = [$type['id']];
            
            if ($type['counter_type'] === 'yearly') {
                $query .= " AND year = ?";
                $params[] = $currentYear;
            } elseif ($type['counter_type'] === 'monthly') {
                $query .= " AND year = ? AND month = ?";
                $params[] = $currentYear;
                $params[] = $currentMonth;
            }
            
            $stmt = $db->prepare($query);
            $stmt->execute($params);
            $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            $type['next_number'] = $sequence ? ($sequence['last_number'] + 1) : 1;
        }
        
        $template = $this->getTemplate();
        $viewName = 'config/document-types-modern';
        
        $this->render($viewName, [
            'documentTypes' => $documentTypes,
            'languages' => ['fr' => 'Français', 'en' => 'English', 'de' => 'Deutsch', 'lu' => 'Lëtzebuergesch'],
            'currentYear' => $currentYear
        ]);
    }
    
    /**
     * Create Document Type
     */
    public function createDocumentType()
    {
        try {
            $data = $this->getRequestData();
            
            // Prepare multilingual data
            $name = [];
            $description = [];
            
            foreach (['fr', 'en', 'de', 'lu'] as $lang) {
                if (!empty($data["name_$lang"])) {
                    $name[$lang] = $data["name_$lang"];
                }
                if (!empty($data["description_$lang"])) {
                    $description[$lang] = $data["description_$lang"];
                }
            }
            
            $documentType = new DocumentType();
            $documentType->saveDocumentType([
                'code' => $data['code'],
                'name' => $name,
                'description' => $description,
                'prefix' => $data['prefix'],
                'counter_type' => $data['counter_type'] ?? 'yearly',
                'is_negative' => $data['is_negative'] ?? 0,
                'requires_reference' => $data['requires_reference'] ?? 0,
                'allowed_references' => !empty($data['allowed_references']) ? explode(',', $data['allowed_references']) : null,
                'color' => $data['color'] ?? '#6c757d',
                'icon' => $data['icon'] ?? 'bi bi-file-text',
                'template' => $data['template'] ?? 'default',
                'email_template' => $data['email_template'] ?? null,
                'is_active' => $data['is_active'] ?? 1,
                'sort_order' => $data['sort_order'] ?? 0
            ]);
            
            Flight::flash('success', __('config.document_type_created'));
            Flight::redirect('/config/document-types');
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/config/document-types');
        }
    }
    
    /**
     * Update Document Type
     */
    public function updateDocumentType($id)
    {
        try {
            $data = $this->getRequestData();
            
            // Prepare multilingual data
            $name = [];
            $description = [];
            
            foreach (['fr', 'en', 'de', 'lu'] as $lang) {
                if (!empty($data["name_$lang"])) {
                    $name[$lang] = $data["name_$lang"];
                }
                if (!empty($data["description_$lang"])) {
                    $description[$lang] = $data["description_$lang"];
                }
            }
            
            $data['id'] = $id;
            $data['name'] = $name;
            $data['description'] = $description;
            
            if (!empty($data['allowed_references']) && is_string($data['allowed_references'])) {
                $data['allowed_references'] = explode(',', $data['allowed_references']);
            } else {
                $data['allowed_references'] = null;
            }
            
            $documentType = new DocumentType();
            $documentType->saveDocumentType($data);
            
            // Handle next number update
            if (!empty($data['next_number']) && $data['counter_type'] !== 'global') {
                $db = Flight::db();
                $nextNumber = intval($data['next_number']);
                $lastNumber = $nextNumber - 1;
                
                // Get the document type to ensure we have the ID
                $stmt = $db->prepare("SELECT id FROM document_types WHERE id = ?");
                $stmt->execute([$id]);
                $docType = $stmt->fetch(\PDO::FETCH_ASSOC);
                
                if ($docType) {
                    $currentYear = date('Y');
                    $currentMonth = date('m');
                    
                    if ($data['counter_type'] === 'yearly') {
                        // Check if sequence exists for this year
                        $stmt = $db->prepare("SELECT id FROM document_sequences WHERE document_type_id = ? AND year = ?");
                        $stmt->execute([$docType['id'], $currentYear]);
                        $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
                        
                        if ($sequence) {
                            // Update existing sequence
                            $stmt = $db->prepare("UPDATE document_sequences SET last_number = ? WHERE document_type_id = ? AND year = ?");
                            $stmt->execute([$lastNumber, $docType['id'], $currentYear]);
                        } else {
                            // Create new sequence
                            $stmt = $db->prepare("INSERT INTO document_sequences (document_type_id, year, last_number) VALUES (?, ?, ?)");
                            $stmt->execute([$docType['id'], $currentYear, $lastNumber]);
                        }
                    } elseif ($data['counter_type'] === 'monthly') {
                        // Check if sequence exists for this year/month
                        $stmt = $db->prepare("SELECT id FROM document_sequences WHERE document_type_id = ? AND year = ? AND month = ?");
                        $stmt->execute([$docType['id'], $currentYear, $currentMonth]);
                        $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
                        
                        if ($sequence) {
                            // Update existing sequence
                            $stmt = $db->prepare("UPDATE document_sequences SET last_number = ? WHERE document_type_id = ? AND year = ? AND month = ?");
                            $stmt->execute([$lastNumber, $docType['id'], $currentYear, $currentMonth]);
                        } else {
                            // Create new sequence
                            $stmt = $db->prepare("INSERT INTO document_sequences (document_type_id, year, month, last_number) VALUES (?, ?, ?, ?)");
                            $stmt->execute([$docType['id'], $currentYear, $currentMonth, $lastNumber]);
                        }
                    }
                }
            }
            
            Flight::flash('success', __('config.document_type_updated'));
            Flight::redirect('/config/document-types');
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/config/document-types');
        }
    }
    
    /**
     * Delete Document Type
     */
    public function deleteDocumentType($id)
    {
        try {
            $documentType = new DocumentType();
            $documentType->deleteDocumentType($id);
            
            Flight::flash('success', __('config.document_type_deleted'));
            Flight::redirect('/config/document-types');
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect('/config/document-types');
        }
    }
    
    /**
     * Display table columns configuration
     */
    public function tableColumns(Request $request, Response $response)
    {
        $template = $this->getTemplate();
        $viewName = 'config/table-columns-enhanced';
        
        // Get all document types
        $db = Flight::db();
        $stmt = $db->query("
            SELECT id, code, name, icon, color 
            FROM document_types 
            WHERE is_active = 1 
            ORDER BY sort_order, name
        ");
        $documentTypes = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Get all invoice types
        $stmt = $db->query("
            SELECT id, name 
            FROM config_invoice_types 
            WHERE is_active = 1 
            ORDER BY id ASC
        ");
        $invoiceTypes = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Decode JSON names
        $currentLang = $_SESSION['lang'] ?? 'fr';
        foreach ($documentTypes as &$type) {
            if (!empty($type['name'])) {
                $nameData = json_decode($type['name'], true);
                if ($nameData) {
                    $type['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $type['code'];
                } else {
                    $type['display_name'] = $type['name'];
                }
            }
        }
        
        // Decode invoice type names
        foreach ($invoiceTypes as &$type) {
            if (!empty($type['name'])) {
                $nameData = json_decode($type['name'], true);
                if ($nameData) {
                    $type['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $type['name'];
                } else {
                    $type['display_name'] = $type['name'];
                }
            }
        }
        
        return $this->render($viewName, [
            'title' => __('config.table_column_config'),
            'documentTypes' => $documentTypes,
            'invoiceTypes' => $invoiceTypes
        ]);
    }
    
    /**
     * Display invoice items columns configuration
     */
    public function invoiceItemsColumns(Request $request, Response $response)
    {
        $template = $this->getTemplate();
        $viewName = 'config/invoice-items-columns-modern';
        
        // Get all document types
        $db = Flight::db();
        $stmt = $db->query("
            SELECT id, code, name, icon, color 
            FROM document_types 
            WHERE is_active = 1 
            ORDER BY sort_order, name
        ");
        $documentTypes = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Get all invoice types
        $stmt = $db->query("
            SELECT id, name 
            FROM config_invoice_types 
            WHERE is_active = 1 
            ORDER BY id ASC
        ");
        $invoiceTypes = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Decode JSON names
        $currentLang = $_SESSION['lang'] ?? 'fr';
        foreach ($documentTypes as &$type) {
            if (!empty($type['name'])) {
                $nameData = json_decode($type['name'], true);
                if ($nameData) {
                    $type['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $type['code'];
                } else {
                    $type['display_name'] = $type['name'];
                }
            }
        }
        
        // Decode invoice type names
        foreach ($invoiceTypes as &$type) {
            if (!empty($type['name'])) {
                $nameData = json_decode($type['name'], true);
                if ($nameData) {
                    $type['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $type['name'];
                } else {
                    $type['display_name'] = $type['name'];
                }
            }
        }
        
        return $this->render($viewName, [
            'title' => __('config.invoice_items_columns'),
            'documentTypes' => $documentTypes,
            'invoiceTypes' => $invoiceTypes
        ]);
    }
    
    /**
     * Get table column configuration for a specific table
     */
    public function getTableColumns(Request $request, Response $response, $table)
    {
        try {
            $db = Flight::db();
            
            // First try to get enhanced config
            $stmt = $db->prepare("SELECT value FROM config WHERE `key` = :key AND category = 'table_columns_enhanced' LIMIT 1");
            $stmt->execute(['key' => $table . '_columns']);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if ($result && $result['value']) {
                // Return enhanced config
                $columnConfig = json_decode($result['value'], true) ?: [];
                return $this->json([
                    'success' => true,
                    'columnConfig' => $columnConfig
                ]);
            }
            
            // Fall back to legacy column order
            $stmt = $db->prepare("SELECT value FROM config WHERE `key` = :key AND category = 'table_columns' LIMIT 1");
            $stmt->execute(['key' => $table . '_column_order']);
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            $columnOrder = [];
            if ($result && $result['value']) {
                $columnOrder = json_decode($result['value'], true) ?: [];
            }
            
            return $this->json([
                'success' => true,
                'columnOrder' => $columnOrder
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Save table column configuration
     */
    public function saveTableColumns(Request $request, Response $response)
    {
        try {
            $data = $this->getRequestData();
            
            if (empty($data['table'])) {
                throw new \Exception(__('config.invalid_table'));
            }
            
            $table = $data['table'];
            $columns = $data['columns'] ?? [];
            
            $db = Flight::db();
            
            // Check if this is enhanced data (array with column details) or legacy data (simple order mapping)
            $isEnhanced = false;
            if (!empty($columns) && is_array($columns)) {
                $firstValue = reset($columns);
                if (is_array($firstValue) && isset($firstValue['column'])) {
                    $isEnhanced = true;
                }
            }
            
            if ($isEnhanced) {
                // Save enhanced column configuration
                $key = $table . '_columns';
                
                // Log what we're saving for debugging
                error_log("Saving enhanced columns for table: $table");
                error_log("Columns data: " . json_encode($columns));
                
                // Check if config exists
                $stmt = $db->prepare("SELECT id FROM config WHERE `key` = :key AND category = 'table_columns_enhanced' LIMIT 1");
                $stmt->execute(['key' => $key]);
                $exists = $stmt->fetch(\PDO::FETCH_ASSOC);
                
                if ($exists) {
                    // Update existing
                    $stmt = $db->prepare("UPDATE config SET value = :value, updated_at = NOW() WHERE `key` = :key AND category = 'table_columns_enhanced'");
                    $stmt->execute([
                        'value' => json_encode($columns),
                        'key' => $key
                    ]);
                } else {
                    // Insert new
                    $stmt = $db->prepare("INSERT INTO config (`key`, value, category, created_at, updated_at) VALUES (:key, :value, 'table_columns_enhanced', NOW(), NOW())");
                    $stmt->execute([
                        'key' => $key,
                        'value' => json_encode($columns)
                    ]);
                }
            } else {
                // Save legacy column order
                $key = $table . '_column_order';
                
                // Check if config exists
                $stmt = $db->prepare("SELECT id FROM config WHERE `key` = :key AND category = 'table_columns' LIMIT 1");
                $stmt->execute(['key' => $key]);
                $exists = $stmt->fetch(\PDO::FETCH_ASSOC);
                
                if ($exists) {
                    // Update existing
                    $stmt = $db->prepare("UPDATE config SET value = :value, updated_at = NOW() WHERE `key` = :key AND category = 'table_columns'");
                    $stmt->execute([
                        'value' => json_encode($columns),
                        'key' => $key
                    ]);
                } else {
                    // Insert new
                    $stmt = $db->prepare("INSERT INTO config (`key`, value, category, created_at, updated_at) VALUES (:key, :value, 'table_columns', NOW(), NOW())");
                    $stmt->execute([
                        'key' => $key,
                        'value' => json_encode($columns)
                    ]);
                }
            }
            
            return $this->json([
                'success' => true,
                'message' => __('config.column_config_saved')
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get document type specific table columns
     */
    public function getDocumentTypeColumns(Request $request, Response $response, $table)
    {
        try {
            $documentTypeId = $_GET['documentTypeId'] ?? null;
            $invoiceTypeId = $_GET['invoiceTypeId'] ?? null;
            
            // Allow documentTypeId = '0' for default template
            if ($documentTypeId === null || $documentTypeId === '') {
                throw new \Exception(__('config.document_type_required'));
            }
            
            // Get document type specific config
            $config = \App\Models\DocumentTypeColumnConfig::getConfig($documentTypeId, $table, $invoiceTypeId);
            
            return $this->json([
                'success' => true,
                'columnConfig' => $config ? $config['column_configs'] : [],
                'hasConfig' => !empty($config)
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get column configuration for API/AJAX calls
     */
    public function getColumnConfigForApi(Request $request, Response $response, $table)
    {
        try {
            $documentTypeId = $_GET['documentTypeId'] ?? null;
            $invoiceTypeId = $_GET['invoiceTypeId'] ?? null;
            
            // Check if this is a retrocession invoice type
            $isRetrocession = false;
            if ($invoiceTypeId) {
                $db = Flight::db();
                $stmt = $db->prepare("SELECT prefix FROM config_invoice_types WHERE id = ? AND is_active = 1");
                $stmt->execute([$invoiceTypeId]);
                $typeData = $stmt->fetch(\PDO::FETCH_ASSOC);
                if ($typeData && !empty($typeData['prefix']) && (strpos($typeData['prefix'], 'RET') === 0)) {
                    $isRetrocession = true;
                }
            }
            
            // Special handling for retrocession invoices - return empty columns to prevent standard rebuild
            if ($isRetrocession) {
                return $this->json([
                    'success' => true,
                    'columns' => [], // Empty columns array to prevent column rebuild
                    'documentTypeId' => $documentTypeId,
                    'invoiceTypeId' => $invoiceTypeId,
                    'isRetrocession' => true
                ]);
            } else {
                // Default columns for invoice_items table
                $defaultColumns = [
                    'description' => ['id' => 'description', 'name' => __('common.description'), 'visible' => true, 'required' => true, 'order' => 0],
                    'reference' => ['id' => 'reference', 'name' => __('common.reference'), 'visible' => false, 'required' => false, 'order' => 1],
                    'quantity' => ['id' => 'quantity', 'name' => __('common.quantity'), 'visible' => true, 'required' => false, 'order' => 2],
                    'unit' => ['id' => 'unit', 'name' => __('common.unit'), 'visible' => false, 'required' => false, 'order' => 3],
                    'unit_price' => ['id' => 'unit_price', 'name' => __('invoices.unit_price'), 'visible' => true, 'required' => false, 'order' => 4],
                    'discount' => ['id' => 'discount', 'name' => __('common.discount'), 'visible' => false, 'required' => false, 'order' => 5],
                    'vat_rate' => ['id' => 'vat_rate', 'name' => __('invoices.vat_rate'), 'visible' => true, 'required' => false, 'order' => 6],
                    'subtotal' => ['id' => 'subtotal', 'name' => __('invoices.subtotal'), 'visible' => false, 'required' => false, 'order' => 7],
                    'total' => ['id' => 'total', 'name' => __('common.total'), 'visible' => true, 'required' => true, 'order' => 8]
                ];
            }
            
            // Get custom configuration if exists
            if ($table === 'invoice_items') {
                if ($documentTypeId && $documentTypeId !== '0') {
                    // Try to get specific configuration
                    $config = \App\Models\DocumentTypeColumnConfig::getConfig($documentTypeId, $table, $invoiceTypeId);
                    
                    if (!$config || empty($config['column_configs'])) {
                        // No specific config found, try to get default template
                        $config = \App\Models\DocumentTypeColumnConfig::getConfig(0, $table, null);
                    }
                } else {
                    // Explicitly requesting default template
                    $config = \App\Models\DocumentTypeColumnConfig::getConfig(0, $table, null);
                }
                
                if ($config && !empty($config['column_configs'])) {
                    // Merge custom config with defaults
                    foreach ($config['column_configs'] as $colConfig) {
                        $colId = $colConfig['column'];
                        if (isset($defaultColumns[$colId])) {
                            $defaultColumns[$colId]['visible'] = (bool)$colConfig['visible'];
                            $defaultColumns[$colId]['order'] = (int)$colConfig['order'];
                            if (!empty($colConfig['custom_name'])) {
                                $defaultColumns[$colId]['custom_name'] = $colConfig['custom_name'];
                                $defaultColumns[$colId]['name'] = $colConfig['custom_name'];
                            }
                        }
                    }
                }
            }
            
            // Sort by order
            uasort($defaultColumns, function($a, $b) {
                return $a['order'] - $b['order'];
            });
            
            return $this->json([
                'success' => true,
                'columns' => array_values($defaultColumns),
                'documentTypeId' => $documentTypeId,
                'invoiceTypeId' => $invoiceTypeId,
                'isRetrocession' => $isRetrocession
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Save document type specific table columns
     */
    public function saveDocumentTypeColumns(Request $request, Response $response)
    {
        try {
            $data = $this->getRequestData();
            
            if (empty($data['table']) || empty($data['documentTypeId'])) {
                throw new \Exception(__('config.invalid_parameters'));
            }
            
            $table = $data['table'];
            $documentTypeId = $data['documentTypeId'];
            $invoiceTypeId = $data['invoiceTypeId'] ?? null;
            $columns = $data['columns'] ?? [];
            $userId = $_SESSION['user_id'] ?? null;
            
            // Save column configuration
            $result = \App\Models\DocumentTypeColumnConfig::saveConfig(
                $documentTypeId, 
                $table, 
                $columns, 
                $userId,
                $invoiceTypeId
            );
            
            if (!$result) {
                throw new \Exception(__('config.save_failed'));
            }
            
            return $this->json([
                'success' => true,
                'message' => __('config.column_config_saved')
            ]);
            
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Get all document types for dropdown
     */
    public function getDocumentTypes(Request $request, Response $response)
    {
        try {
            $db = Flight::db();
            $stmt = $db->query("
                SELECT id, code, name, icon, color 
                FROM document_types 
                WHERE is_active = 1 
                ORDER BY sort_order, name
            ");
            $types = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Decode JSON names
            $currentLang = $_SESSION['lang'] ?? 'fr';
            foreach ($types as &$type) {
                if (!empty($type['name'])) {
                    $nameData = json_decode($type['name'], true);
                    if ($nameData) {
                        $type['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $type['code'];
                    } else {
                        $type['display_name'] = $type['name'];
                    }
                }
            }
            
            return $this->json([
                'success' => true,
                'documentTypes' => $types
            ]);
        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
    
    /**
     * Manage payment terms
     */
    public function paymentTerms(Request $request, Response $response)
    {
        try {
            // Get payment terms
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM config_payment_terms ORDER BY sort_order ASC, id ASC");
            $paymentTerms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Decode JSON names for current language
            $currentLang = $_SESSION['lang'] ?? 'fr';
            foreach ($paymentTerms as &$term) {
                if (!empty($term['name']) && $term['name'] !== 'null') {
                    $nameData = json_decode($term['name'], true);
                    if ($nameData) {
                        $term['display_name'] = $nameData[$currentLang] ?? $nameData['fr'] ?? $term['code'];
                    } else {
                        $term['display_name'] = $term['code'];
                    }
                } else {
                    $term['display_name'] = $term['code'];
                }
                
                // Decode description
                if (!empty($term['description']) && $term['description'] !== 'null') {
                    $descData = json_decode($term['description'], true);
                    if ($descData) {
                        $term['display_description'] = $descData[$currentLang] ?? $descData['fr'] ?? '';
                    } else {
                        $term['display_description'] = '';
                    }
                } else {
                    $term['display_description'] = '';
                }
            }
        } catch (\Exception $e) {
            $paymentTerms = [];
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/payment-terms-modern';

        return $this->render($viewName, [
            'title' => __('config.payment_terms'),
            'paymentTerms' => $paymentTerms
        ]);
    }
    
    /**
     * Create payment term
     */
    public function createPaymentTerm(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $name = $this->input('name', '');
            $code = strtolower(str_replace(' ', '_', $this->input('code', '')));
            $days = intval($this->input('days', 0));
            $description = $this->input('description', '');
            $isActive = $this->input('is_active', 1);
            $isDefault = $this->input('is_default', 0);
            
            // Validation
            if (empty($name) || empty($code)) {
                throw new \Exception(__('validation.required_fields_missing'));
            }
            
            // Prepare multi-language data
            $currentLang = $_SESSION['lang'] ?? 'fr';
            $nameData = [$currentLang => $name];
            $descData = $description ? [$currentLang => $description] : null;
            
            $db = Flight::db();
            
            // Check if code already exists
            $stmt = $db->prepare("SELECT COUNT(*) FROM config_payment_terms WHERE code = ?");
            $stmt->execute([$code]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.payment_term_code_exists'));
            }
            
            // If setting as default, unset other defaults
            if ($isDefault) {
                $db->exec("UPDATE config_payment_terms SET is_default = 0");
            }
            
            // Get next sort order
            $stmt = $db->query("SELECT MAX(sort_order) FROM config_payment_terms");
            $maxOrder = $stmt->fetchColumn() ?: 0;
            
            // Insert payment term
            $stmt = $db->prepare("
                INSERT INTO config_payment_terms (name, code, days, description, is_default, is_active, sort_order) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $result = $stmt->execute([
                json_encode($nameData, JSON_UNESCAPED_UNICODE),
                $code,
                $days,
                $descData ? json_encode($descData, JSON_UNESCAPED_UNICODE) : null,
                $isDefault,
                $isActive,
                $maxOrder + 1
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.payment_term_create_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.payment_term_created')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update payment term
     */
    public function updatePaymentTerm(Request $request, Response $response, $id = null)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get ID from parameter or from input
            if ($id === null) {
                $id = intval($this->input('id', 0));
            } else {
                $id = intval($id);
            }
            
            if (!$id) {
                throw new \Exception(__('validation.invalid_id'));
            }
            
            // Get input data
            $name = $this->input('name', '');
            $days = intval($this->input('days', 0));
            $description = $this->input('description', '');
            $isActive = $this->input('is_active', 1);
            $isDefault = $this->input('is_default', 0);
            
            // Validation
            if (empty($name)) {
                throw new \Exception(__('validation.required_fields_missing'));
            }
            
            $db = Flight::db();
            
            // Get existing payment term
            $stmt = $db->prepare("SELECT * FROM config_payment_terms WHERE id = ?");
            $stmt->execute([$id]);
            $paymentTerm = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$paymentTerm) {
                throw new \Exception(__('config.payment_term_not_found'));
            }
            
            // Update multi-language data
            $currentLang = $_SESSION['lang'] ?? 'fr';
            
            // Update name
            $nameData = json_decode($paymentTerm['name'], true) ?: [];
            $nameData[$currentLang] = $name;
            
            // Update description
            $descData = $paymentTerm['description'] ? json_decode($paymentTerm['description'], true) : [];
            if ($description) {
                $descData[$currentLang] = $description;
            } else {
                unset($descData[$currentLang]);
            }
            
            // If setting as default, unset other defaults
            if ($isDefault) {
                $db->exec("UPDATE config_payment_terms SET is_default = 0");
            }
            
            // Update payment term
            $stmt = $db->prepare("
                UPDATE config_payment_terms 
                SET name = ?, days = ?, description = ?, is_default = ?, is_active = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                json_encode($nameData, JSON_UNESCAPED_UNICODE),
                $days,
                !empty($descData) ? json_encode($descData, JSON_UNESCAPED_UNICODE) : null,
                $isDefault,
                $isActive,
                $id
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.payment_term_update_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.payment_term_updated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Delete payment term
     */
    public function deletePaymentTerm(Request $request, Response $response, $id = null)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get ID from parameter or from input
            if ($id === null) {
                $id = intval($this->input('id', 0));
            } else {
                $id = intval($id);
            }
            
            if (!$id) {
                throw new \Exception(__('validation.invalid_id'));
            }
            
            $db = Flight::db();
            
            // Check if payment term is used in invoices
            $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE payment_term_id = ?");
            $stmt->execute([$id]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.payment_term_in_use'));
            }
            
            // Delete payment term
            $stmt = $db->prepare("DELETE FROM config_payment_terms WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new \Exception(__('config.payment_term_delete_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.payment_term_deleted')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Display color schemes management page
     */
    public function colorSchemes(Request $request, Response $response)
    {
        try {
            // Get all color schemes
            $db = Flight::db();
            $stmt = $db->query("SELECT * FROM color_schemes ORDER BY is_default DESC, is_system DESC, name ASC");
            $colorSchemes = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Get active scheme code
            $activeSchemeCode = Config::getValue('active_color_scheme', 'classic_blue');
            
            // Decode JSON fields and prepare for display
            $currentLang = $_SESSION['lang'] ?? 'en';
            foreach ($colorSchemes as &$scheme) {
                // Decode name
                $nameData = json_decode($scheme['name'], true);
                $scheme['display_name'] = $nameData[$currentLang] ?? $nameData['en'] ?? $scheme['code'];
                
                // Decode description
                if ($scheme['description']) {
                    $descData = json_decode($scheme['description'], true);
                    $scheme['display_description'] = $descData[$currentLang] ?? $descData['en'] ?? '';
                } else {
                    $scheme['display_description'] = '';
                }
                
                // Decode colors
                $scheme['colors'] = json_decode($scheme['colors'], true);
                
                // Mark as active
                $scheme['is_current'] = ($scheme['code'] === $activeSchemeCode);
            }
        } catch (\Exception $e) {
            $colorSchemes = [];
            $activeSchemeCode = 'classic_blue';
        }

        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'config/color-schemes-modern';

        return $this->render($viewName, [
            'title' => __('config.color_schemes'),
            'colorSchemes' => $colorSchemes,
            'activeSchemeCode' => $activeSchemeCode
        ]);
    }
    
    /**
     * Create new color scheme
     */
    public function createColorScheme(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get input data
            $name = $this->input('name', '');
            $code = strtolower(preg_replace('/[^a-zA-Z0-9_]/', '_', $this->input('code', '')));
            $description = $this->input('description', '');
            $baseScheme = $this->input('base_scheme', 'classic_blue');
            
            // Validation
            if (empty($name) || empty($code)) {
                throw new \Exception(__('validation.required_fields_missing'));
            }
            
            $db = Flight::db();
            
            // Check if code already exists
            $stmt = $db->prepare("SELECT COUNT(*) FROM color_schemes WHERE code = ?");
            $stmt->execute([$code]);
            if ($stmt->fetchColumn() > 0) {
                throw new \Exception(__('config.color_scheme_code_exists'));
            }
            
            // Get base scheme colors
            $stmt = $db->prepare("SELECT colors FROM color_schemes WHERE code = ?");
            $stmt->execute([$baseScheme]);
            $baseColors = $stmt->fetchColumn();
            
            if (!$baseColors) {
                // Default colors if base scheme not found
                $baseColors = json_encode([
                    'primary' => '#6366f1',
                    'primary_rgb' => '99, 102, 241',
                    'secondary' => '#64748b',
                    'secondary_rgb' => '100, 116, 139',
                    'success' => '#10b981',
                    'success_rgb' => '16, 185, 129',
                    'info' => '#06b6d4',
                    'info_rgb' => '6, 182, 212',
                    'warning' => '#f59e0b',
                    'warning_rgb' => '245, 158, 11',
                    'danger' => '#ef4444',
                    'danger_rgb' => '239, 68, 68',
                    'light' => '#f8fafc',
                    'light_rgb' => '248, 250, 252',
                    'dark' => '#1e293b',
                    'dark_rgb' => '30, 41, 59',
                    'sidebar_bg' => '#1e293b',
                    'sidebar_text' => '#cbd5e1',
                    'sidebar_hover' => '#334155',
                    'sidebar_active' => '#6366f1',
                    'navbar_bg' => '#ffffff',
                    'navbar_text' => '#1e293b',
                    'card_bg' => '#ffffff',
                    'body_bg' => '#f8fafc',
                    'text_primary' => '#1e293b',
                    'text_secondary' => '#64748b',
                    'border_color' => '#e2e8f0',
                    'shadow_color' => '0, 0, 0'
                ]);
            }
            
            // Prepare multilingual data
            $currentLang = $_SESSION['lang'] ?? 'en';
            $nameData = [$currentLang => $name];
            $descData = $description ? [$currentLang => $description] : null;
            
            // Insert new color scheme
            $stmt = $db->prepare("
                INSERT INTO color_schemes (name, code, description, colors, is_system, is_active, is_default, created_by, created_at) 
                VALUES (?, ?, ?, ?, 0, 1, 0, ?, NOW())
            ");
            
            $result = $stmt->execute([
                json_encode($nameData, JSON_UNESCAPED_UNICODE),
                $code,
                $descData ? json_encode($descData, JSON_UNESCAPED_UNICODE) : null,
                $baseColors,
                $_SESSION['user_id'] ?? null
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.color_scheme_create_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.color_scheme_created'),
                'id' => $db->lastInsertId()
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Update color scheme
     */
    public function updateColorScheme(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $db = Flight::db();
            
            // Get existing scheme
            $stmt = $db->prepare("SELECT * FROM color_schemes WHERE id = ?");
            $stmt->execute([$id]);
            $scheme = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$scheme) {
                throw new \Exception(__('config.color_scheme_not_found'));
            }
            
            // Don't allow editing system schemes directly
            if ($scheme['is_system']) {
                throw new \Exception(__('config.cannot_edit_system_scheme'));
            }
            
            // Get input data
            $name = $this->input('name', '');
            $description = $this->input('description', '');
            $colors = $this->input('colors', []);
            
            // Validation
            if (empty($name)) {
                throw new \Exception(__('validation.name_required'));
            }
            
            // Validate colors
            $requiredColors = ['primary', 'secondary', 'success', 'info', 'warning', 'danger', 'light', 'dark'];
            foreach ($requiredColors as $colorKey) {
                if (!isset($colors[$colorKey])) {
                    throw new \Exception(__('config.missing_required_color', ['color' => $colorKey]));
                }
            }
            
            // Update multilingual fields
            $currentLang = $_SESSION['lang'] ?? 'en';
            
            $nameData = json_decode($scheme['name'], true) ?: [];
            $nameData[$currentLang] = $name;
            
            $descData = $scheme['description'] ? json_decode($scheme['description'], true) : [];
            if ($description) {
                $descData[$currentLang] = $description;
            } else {
                unset($descData[$currentLang]);
            }
            
            // Auto-generate RGB values for hex colors
            foreach ($colors as $key => $value) {
                if (strpos($key, '_rgb') === false && strpos($value, '#') === 0) {
                    $rgbKey = $key . '_rgb';
                    if (!isset($colors[$rgbKey])) {
                        // Import the ColorScheme class if needed
                        require_once dirname(__DIR__) . '/Models/ColorScheme.php';
                        $colors[$rgbKey] = \App\Models\ColorScheme::hexToRgb($value);
                    }
                }
            }
            
            // Update color scheme
            $stmt = $db->prepare("
                UPDATE color_schemes 
                SET name = ?, description = ?, colors = ?, updated_at = NOW()
                WHERE id = ?
            ");
            
            $result = $stmt->execute([
                json_encode($nameData, JSON_UNESCAPED_UNICODE),
                !empty($descData) ? json_encode($descData, JSON_UNESCAPED_UNICODE) : null,
                json_encode($colors),
                $id
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.color_scheme_update_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.color_scheme_updated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Set active color scheme
     */
    public function setActiveColorScheme(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $db = Flight::db();
            
            // Get scheme
            $stmt = $db->prepare("SELECT code FROM color_schemes WHERE id = ? AND is_active = 1");
            $stmt->execute([$id]);
            $scheme = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$scheme) {
                throw new \Exception(__('config.color_scheme_not_found'));
            }
            
            // Update config
            $stmt = $db->prepare("
                INSERT INTO config (`key`, value, category, created_at, updated_at) 
                VALUES ('active_color_scheme', ?, 'appearance', NOW(), NOW())
                ON DUPLICATE KEY UPDATE value = VALUES(value), updated_at = NOW()
            ");
            
            $result = $stmt->execute([$scheme['code']]);
            
            if (!$result) {
                throw new \Exception(__('config.color_scheme_activation_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.color_scheme_activated')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Delete color scheme
     */
    public function deleteColorScheme(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $db = Flight::db();
            
            // Get scheme
            $stmt = $db->prepare("SELECT * FROM color_schemes WHERE id = ?");
            $stmt->execute([$id]);
            $scheme = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$scheme) {
                throw new \Exception(__('config.color_scheme_not_found'));
            }
            
            // Don't allow deleting system schemes
            if ($scheme['is_system']) {
                throw new \Exception(__('config.cannot_delete_system_scheme'));
            }
            
            // Don't allow deleting default scheme
            if ($scheme['is_default']) {
                throw new \Exception(__('config.cannot_delete_default_scheme'));
            }
            
            // Check if it's the active scheme
            $activeCode = Config::getValue('active_color_scheme', 'appearance');
            if ($scheme['code'] === $activeCode) {
                throw new \Exception(__('config.cannot_delete_active_scheme'));
            }
            
            // Delete the scheme
            $stmt = $db->prepare("DELETE FROM color_schemes WHERE id = ?");
            $result = $stmt->execute([$id]);
            
            if (!$result) {
                throw new \Exception(__('config.color_scheme_delete_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.color_scheme_deleted')
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Duplicate color scheme
     */
    public function duplicateColorScheme(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $db = Flight::db();
            
            // Get original scheme
            $stmt = $db->prepare("SELECT * FROM color_schemes WHERE id = ?");
            $stmt->execute([$id]);
            $original = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$original) {
                throw new \Exception(__('config.color_scheme_not_found'));
            }
            
            // Generate new code
            $baseCode = $original['code'] . '_copy';
            $newCode = $baseCode;
            $counter = 1;
            
            // Find unique code
            while (true) {
                $stmt = $db->prepare("SELECT COUNT(*) FROM color_schemes WHERE code = ?");
                $stmt->execute([$newCode]);
                if ($stmt->fetchColumn() == 0) {
                    break;
                }
                $counter++;
                $newCode = $baseCode . '_' . $counter;
            }
            
            // Update name in all languages
            $nameData = json_decode($original['name'], true);
            foreach ($nameData as $lang => &$name) {
                $name .= ' (Copy)';
            }
            
            // Insert duplicate
            $stmt = $db->prepare("
                INSERT INTO color_schemes (name, code, description, colors, is_system, is_active, is_default, created_by, created_at) 
                VALUES (?, ?, ?, ?, 0, 1, 0, ?, NOW())
            ");
            
            $result = $stmt->execute([
                json_encode($nameData, JSON_UNESCAPED_UNICODE),
                $newCode,
                $original['description'],
                $original['colors'],
                $_SESSION['user_id'] ?? null
            ]);
            
            if (!$result) {
                throw new \Exception(__('config.color_scheme_duplicate_failed'));
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.color_scheme_duplicated'),
                'id' => $db->lastInsertId()
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Preview color scheme
     */
    public function previewColorScheme(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $db = Flight::db();
            
            // Get scheme
            $stmt = $db->prepare("SELECT colors FROM color_schemes WHERE id = ?");
            $stmt->execute([$id]);
            $colors = $stmt->fetchColumn();
            
            if (!$colors) {
                throw new \Exception(__('config.color_scheme_not_found'));
            }
            
            $colors = json_decode($colors, true);
            
            // Generate CSS
            $css = ":root {\n";
            
            // Map colors to CSS variables
            $colorMap = [
                'primary' => '--bs-primary',
                'primary_rgb' => '--bs-primary-rgb',
                'secondary' => '--bs-secondary',
                'secondary_rgb' => '--bs-secondary-rgb',
                'success' => '--bs-success',
                'success_rgb' => '--bs-success-rgb',
                'info' => '--bs-info',
                'info_rgb' => '--bs-info-rgb',
                'warning' => '--bs-warning',
                'warning_rgb' => '--bs-warning-rgb',
                'danger' => '--bs-danger',
                'danger_rgb' => '--bs-danger-rgb',
                'light' => '--bs-light',
                'light_rgb' => '--bs-light-rgb',
                'dark' => '--bs-dark',
                'dark_rgb' => '--bs-dark-rgb',
                'sidebar_bg' => '--sidebar-bg',
                'sidebar_text' => '--sidebar-text',
                'sidebar_hover' => '--sidebar-hover',
                'sidebar_active' => '--sidebar-active',
                'navbar_bg' => '--navbar-bg',
                'navbar_text' => '--navbar-text',
                'card_bg' => '--bs-card-bg',
                'body_bg' => '--bs-body-bg',
                'text_primary' => '--bs-body-color',
                'text_secondary' => '--bs-secondary-color',
                'border_color' => '--bs-border-color',
                'shadow_color' => '--shadow-color'
            ];
            
            foreach ($colorMap as $key => $cssVar) {
                if (isset($colors[$key])) {
                    $css .= "    {$cssVar}: {$colors[$key]};\n";
                }
            }
            
            $css .= "}";
            
            echo json_encode([
                'success' => true,
                'css' => $css,
                'colors' => $colors
            ]);
            exit;
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * Display invoice templates management page
     */
    public function invoiceTemplates(Request $request, Response $response)
    {
        $invoiceTemplate = new \App\Models\InvoiceTemplate();
        
        // Get filters
        $filters = [
            'invoice_type' => Flight::request()->query->invoice_type,
            'owner_type' => Flight::request()->query->owner_type,
            'is_active' => Flight::request()->query->is_active
        ];
        
        $templates = $invoiceTemplate->getAllWithDetails($filters);
        
        // Get column configuration for invoice_templates table
        $db = Flight::db();
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = :key AND category = 'table_columns_enhanced' LIMIT 1");
        $stmt->execute(['key' => 'invoice_templates_columns']);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        $columnConfig = [];
        if ($result && $result['value']) {
            $columnConfig = json_decode($result['value'], true) ?: [];
            error_log("Loaded column config for invoice_templates: " . json_encode($columnConfig));
        } else {
            error_log("No column config found for invoice_templates");
        }
        
        // Process column configuration to determine visibility
        $visibleColumns = [
            'checkbox' => true, // Always visible
            'template_name' => true,
            'template_code' => true,
            'invoice_type' => true,
            'owner_type' => true,
            'parent_template' => true,
            'items_count' => true,
            'status' => true,
            'created_by' => true,
            'actions' => true // Always visible
        ];
        
        // Apply visibility settings from config
        foreach ($columnConfig as $config) {
            if (isset($config['column']) && isset($config['visible'])) {
                // Convert string '0'/'1' to boolean
                $visibleColumns[$config['column']] = ($config['visible'] == 1 || $config['visible'] === true);
                error_log("Column {$config['column']}: visible = {$config['visible']} -> " . ($visibleColumns[$config['column']] ? 'true' : 'false'));
            }
        }
        
        // Log final visibility settings
        error_log("Final visibleColumns: " . json_encode($visibleColumns));
        
        $template = $this->getTemplate();
        $viewName = 'config/invoice-templates-modern';
        
        return $this->render($viewName, [
            'title' => __('config.invoice_templates'),
            'templates' => $templates,
            'filters' => $filters,
            'visibleColumns' => $visibleColumns,
            'invoice_types' => [
                'rental' => __('invoices.type_rental'),
                'hourly' => __('invoices.type_hourly'),
                'retrocession_30' => __('invoices.type_retrocession_30'),
                'retrocession_25' => __('invoices.type_retrocession_25'),
                'service' => __('invoices.type_service')
            ],
            'owner_types' => [
                'system' => __('config.owner_type_system'),
                'group' => __('config.owner_type_group'),
                'user' => __('config.owner_type_user')
            ]
        ]);
    }
    
    /**
     * Show create invoice template form
     */
    public function createInvoiceTemplate(Request $request, Response $response)
    {
        $invoiceTemplate = new \App\Models\InvoiceTemplate();
        $parentTemplates = $invoiceTemplate->getForDropdown();
        
        $template = $this->getTemplate();
        $viewName = 'config/invoice-template-form-modern';
        
        return $this->render($viewName, [
            'title' => __('config.create_invoice_template'),
            'mode' => 'create',
            'parentTemplates' => $parentTemplates,
            'invoice_types' => [
                'rental' => __('invoices.type_rental'),
                'hourly' => __('invoices.type_hourly'),
                'retrocession_30' => __('invoices.type_retrocession_30'),
                'retrocession_25' => __('invoices.type_retrocession_25'),
                'service' => __('invoices.type_service')
            ]
        ]);
    }
    
    /**
     * Store new invoice template
     */
    public function storeInvoiceTemplate(Request $request, Response $response)
    {
        try {
            // Get POST data using the Flight request object
            $data = Flight::request()->data->getData();
            $invoiceTemplate = new \App\Models\InvoiceTemplate();
            
            // Validate
            if (empty($data['name']) || empty($data['code']) || empty($data['invoice_type'])) {
                throw new \Exception(__('validation.required_fields'));
            }
            
            // Check code uniqueness
            if (!$invoiceTemplate->isCodeUnique($data['code'], $data['invoice_type'], $data['owner_type'] ?? 'system', $data['owner_id'] ?? null)) {
                throw new \Exception(__('config.template_code_exists'));
            }
            
            // Set defaults
            $data['created_by'] = $_SESSION['user_id'] ?? 1;
            $data['owner_type'] = $data['owner_type'] ?? 'system';
            $data['is_active'] = $data['is_active'] ?? 1;
            
            // Clean up data before create
            // Convert empty strings to null for foreign key fields
            if (isset($data['parent_template_id']) && $data['parent_template_id'] === '') {
                $data['parent_template_id'] = null;
            }
            if (isset($data['owner_id']) && $data['owner_id'] === '') {
                $data['owner_id'] = null;
            }
            
            $id = $invoiceTemplate->create($data);
            
            $this->flash('success', __('config.template_created_successfully'));
            return $response->redirect('/config/invoice-templates');
            
        } catch (\Exception $e) {
            $this->flash('error', $e->getMessage());
            return $response->redirect('/config/invoice-templates/create');
        }
    }
    
    /**
     * Show edit invoice template form
     */
    public function editInvoiceTemplate(Request $request, Response $response, $id)
    {
        $invoiceTemplate = new \App\Models\InvoiceTemplate();
        $templateData = $invoiceTemplate->getWithSettings($id);
        
        if (!$templateData) {
            $this->flash('error', __('config.template_not_found'));
            return $response->redirect('/config/invoice-templates');
        }
        
        $parentTemplates = $invoiceTemplate->getForDropdown();
        // Remove current template from parent options
        $parentTemplates = array_filter($parentTemplates, function($t) use ($id) {
            return $t['id'] != $id;
        });
        
        $template = $this->getTemplate();
        $viewName = 'config/invoice-template-form-modern';
        
        return $this->render($viewName, [
            'title' => __('config.edit_invoice_template'),
            'mode' => 'edit',
            'template' => $templateData,
            'parentTemplates' => $parentTemplates,
            'invoice_types' => [
                'rental' => __('invoices.type_rental'),
                'hourly' => __('invoices.type_hourly'),
                'retrocession_30' => __('invoices.type_retrocession_30'),
                'retrocession_25' => __('invoices.type_retrocession_25'),
                'service' => __('invoices.type_service')
            ]
        ]);
    }
    
    /**
     * Update invoice template
     */
    public function updateInvoiceTemplate(Request $request, Response $response, $id)
    {
        try {
            // Get POST data - handle both Flight data and $_POST
            $data = Flight::request()->data->getData();
            
            // If data is empty or not an array, try $_POST
            if (empty($data) || !is_array($data)) {
                $data = $_POST;
            }
            
            // If still empty or not an array, try parsing the raw body
            if (empty($data) || !is_array($data)) {
                $contentType = Flight::request()->type ?? '';
                if (strpos($contentType, 'application/json') !== false) {
                    $rawBody = Flight::request()->getBody();
                    $data = json_decode($rawBody, true) ?? [];
                } else {
                    // Parse form data from raw body if needed
                    parse_str(Flight::request()->getBody(), $data);
                }
            }
            
            // Ensure data is an array
            if (!is_array($data)) {
                throw new \Exception(__('validation.invalid_data_format'));
            }
            
            // Find the existing template
            $template = \App\Models\InvoiceTemplate::find($id);
            if (!$template) {
                throw new \Exception(__('config.template_not_found'));
            }
            
            // Validate
            if (empty($data['name']) || empty($data['code']) || empty($data['invoice_type'])) {
                throw new \Exception(__('validation.required_fields'));
            }
            
            // Check code uniqueness
            $invoiceTemplate = new \App\Models\InvoiceTemplate();
            if (!$invoiceTemplate->isCodeUnique($data['code'], $data['invoice_type'], $data['owner_type'] ?? 'system', $data['owner_id'] ?? null, $id)) {
                throw new \Exception(__('config.template_code_exists'));
            }
            
            // Clean up data before update
            // Convert empty strings to null for foreign key fields
            if (isset($data['parent_template_id']) && $data['parent_template_id'] === '') {
                $data['parent_template_id'] = null;
            }
            if (isset($data['owner_id']) && $data['owner_id'] === '') {
                $data['owner_id'] = null;
            }
            
            // Ensure boolean fields are properly cast
            if (isset($data['is_active'])) {
                $data['is_active'] = (int) $data['is_active'];
            }
            
            // Update the template
            $template->update($data);
            
            $this->flash('success', __('config.template_updated_successfully'));
            return $response->redirect('/config/invoice-templates');
            
        } catch (\Exception $e) {
            $this->flash('error', $e->getMessage());
            return $response->redirect("/config/invoice-templates/$id/edit");
        }
    }
    
    /**
     * Delete invoice template
     */
    public function deleteInvoiceTemplate(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Find the template first
            $template = \App\Models\InvoiceTemplate::find($id);
            
            if (!$template) {
                throw new \Exception(__('config.template_not_found'));
            }
            
            // Check if it's a system template
            if ($template->owner_type === 'system') {
                throw new \Exception(__('config.cannot_delete_system_template'));
            }
            
            // Check permissions
            $userId = $_SESSION['user_id'] ?? null;
            $isAdmin = $_SESSION['user']['is_admin'] ?? false;
            
            // Handle user groups - could be string (temporary) or array
            $isManager = false;
            $userGroupIds = [];
            
            if (isset($_SESSION['user_groups'])) {
                if (is_string($_SESSION['user_groups'])) {
                    // Temporary fix - check if string contains manager/administrator
                    $groupString = strtolower($_SESSION['user_groups']);
                    $isManager = strpos($groupString, 'manager') !== false || strpos($groupString, 'administrator') !== false;
                    $isAdmin = $isAdmin || strpos($groupString, 'administrator') !== false;
                } elseif (is_array($_SESSION['user_groups'])) {
                    // Proper array of groups
                    $groupNames = array_column($_SESSION['user_groups'], 'name');
                    $isManager = in_array('manager', array_map('strtolower', $groupNames)) || 
                                in_array('managers', array_map('strtolower', $groupNames));
                    $userGroupIds = array_column($_SESSION['user_groups'], 'id');
                }
            }
            
            // Allow deletion if:
            // 1. User is admin or manager
            // 2. User owns the template (owner_type = 'user' and created_by = user_id)
            // 3. User is in the group that owns the template
            $canDelete = false;
            
            if ($isAdmin || $isManager) {
                $canDelete = true;
            } elseif ($template->owner_type === 'user' && $template->created_by == $userId) {
                $canDelete = true;
            } elseif ($template->owner_type === 'group' && !empty($userGroupIds)) {
                if (in_array($template->owner_id, $userGroupIds)) {
                    $canDelete = true;
                }
            }
            
            if (!$canDelete) {
                throw new \Exception(__('config.no_permission_delete_template'));
            }
            
            // Check if template is in use by any invoices
            $db = Flight::db();
            $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE template_id = :template_id");
            $stmt->execute([':template_id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                throw new \Exception(__('config.template_in_use_cannot_delete', ['count' => $count]));
            }
            
            // Delete related data first
            $db->beginTransaction();
            
            try {
                // Delete template line items
                $stmt = $db->prepare("DELETE FROM template_line_items WHERE template_id = :template_id");
                $stmt->execute([':template_id' => $id]);
                
                // Delete template settings
                $stmt = $db->prepare("DELETE FROM invoice_template_settings WHERE template_id = :template_id");
                $stmt->execute([':template_id' => $id]);
                
                // Delete template VAT configs
                $stmt = $db->prepare("DELETE FROM template_vat_configs WHERE template_id = :template_id");
                $stmt->execute([':template_id' => $id]);
                
                // Delete the template itself
                $template->delete();
                
                $db->commit();
                
                echo json_encode(['success' => true, 'message' => __('config.template_deleted_successfully')]);
            } catch (\Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
    
    /**
     * Duplicate invoice template
     */
    public function duplicateInvoiceTemplate(Request $request, Response $response, $id)
    {
        try {
            // Get POST data using the Flight request object
            $data = Flight::request()->data->getData();
            $invoiceTemplate = new \App\Models\InvoiceTemplate();
            
            // Get template name for default naming
            $templateName = '';
            if (empty($data['name'])) {
                $db = Flight::db();
                $stmt = $db->prepare("SELECT name FROM invoice_templates WHERE id = :id");
                $stmt->execute([':id' => $id]);
                $result = $stmt->fetch(\PDO::FETCH_ASSOC);
                $templateName = $result ? $result['name'] : 'Template';
            }
            
            $newName = $data['name'] ?? __('config.copy_of') . ' ' . $templateName;
            $newCode = $data['code'] ?? 'COPY_' . time();
            
            $newId = $invoiceTemplate->duplicate($id, $newName, $newCode, $_SESSION['user_id'] ?? 1);
            
            echo json_encode([
                'success' => true, 
                'message' => __('config.template_duplicated_successfully'),
                'redirect' => "/config/invoice-templates/$newId/edit"
            ]);
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
    
    /**
     * Manage template line items
     */
    public function manageTemplateItems(Request $request, Response $response, $id)
    {
        $invoiceTemplate = new \App\Models\InvoiceTemplate();
        $templateData = $invoiceTemplate->getWithSettings($id);
        
        if (!$templateData) {
            $this->flash('error', __('config.template_not_found'));
            return $response->redirect('/config/invoice-templates');
        }
        
        // Get VAT rates for dropdown
        $vatRates = \App\Models\VatRate::getActive();
        
        // Load line types from configuration
        $lineTypesConfig = require __DIR__ . '/../config/invoice-line-types.php';
        $lineTypes = [];
        foreach ($lineTypesConfig as $key => $translationKey) {
            $lineTypes[$key] = __($translationKey);
        }
        
        $template = $this->getTemplate();
        $viewName = 'config/invoice-template-items-modern';
        
        return $this->render($viewName, [
            'title' => __('config.manage_template_items'),
            'template' => $templateData,
            'vatRates' => $vatRates,
            'line_types' => $lineTypes
        ]);
    }
    
    /**
     * Create template from existing invoice
     */
    public function createTemplateFromInvoice(Request $request, Response $response)
    {
        try {
            // Get invoice ID from different possible sources
            $data = Flight::request()->data->getData();
            $invoiceId = null;
            
            // Try different ways to get the invoice ID
            if (!empty($data['invoice_id'])) {
                $invoiceId = $data['invoice_id'];
            } elseif (!empty($_POST['invoice_id'])) {
                $invoiceId = $_POST['invoice_id'];
            } else {
                // Try to get from JSON body
                $rawBody = Flight::request()->getBody();
                if ($rawBody) {
                    $jsonData = json_decode($rawBody, true);
                    if ($jsonData && isset($jsonData['invoice_id'])) {
                        $invoiceId = $jsonData['invoice_id'];
                    }
                }
            }
            
            if (!$invoiceId) {
                throw new \Exception(__('config.invoice_id_required'));
            }
            
            $db = Flight::db();
            
            // Get invoice details
            $stmt = $db->prepare("
                SELECT i.*, c.name as client_name, it.name as type_name
                FROM invoices i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN config_invoice_types it ON i.invoice_type = it.code
                WHERE i.id = :id
            ");
            $stmt->execute([':id' => $invoiceId]);
            $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
            
            if (!$invoice) {
                throw new \Exception(__('invoices.invoice_not_found'));
            }
            
            // Create new template
            // Format the date properly - check which date field exists
            $invoiceDate = $invoice['invoice_date'] ?? $invoice['issue_date'] ?? $invoice['created_at'] ?? date('Y-m-d');
            if ($invoiceDate && $invoiceDate !== '0000-00-00') {
                $formattedDate = date('d/m/Y', strtotime($invoiceDate));
            } else {
                $formattedDate = date('d/m/Y');
            }
            
            $templateData = [
                'name' => __('config.template_from_invoice', ['number' => $invoice['invoice_number']]),
                'code' => 'TPL_' . str_replace(['/', '\\', ' '], '_', $invoice['invoice_number']),
                'invoice_type' => $invoice['invoice_type'],
                'description' => __('config.created_from_invoice', [
                    'number' => $invoice['invoice_number'], 
                    'date' => $formattedDate,
                    'client' => $invoice['client_name'] ?? 'N/A'
                ]),
                'owner_type' => 'user',
                'owner_id' => $_SESSION['user_id'] ?? null,
                'created_by' => $_SESSION['user_id'] ?? 1,
                'is_active' => 1
            ];
            
            // Create the template
            $invoiceTemplate = new \App\Models\InvoiceTemplate();
            $template = $invoiceTemplate->create($templateData);
            $templateId = is_object($template) ? $template->id : $template;
            
            // Get invoice items
            $stmt = $db->prepare("
                SELECT * FROM invoice_items 
                WHERE invoice_id = :invoice_id 
                ORDER BY sort_order
            ");
            $stmt->execute([':invoice_id' => $invoiceId]);
            $items = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Create template items
            $sortOrder = 1;
            foreach ($items as $item) {
                $templateItem = [
                    'template_id' => $templateId,
                    'line_type' => $item['line_type'] ?? 'service',
                    'description' => $item['description'],
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'vat_rate_id' => $item['vat_rate_id'],
                    'calculation_formula' => null,
                    'is_mandatory' => 0,
                    'sort_order' => $sortOrder++
                ];
                
                $stmt = $db->prepare("
                    INSERT INTO template_line_items 
                    (template_id, line_type, description, quantity, unit_price, vat_rate_id, 
                     calculation_formula, is_mandatory, sort_order)
                    VALUES 
                    (:template_id, :line_type, :description, :quantity, :unit_price, :vat_rate_id,
                     :calculation_formula, :is_mandatory, :sort_order)
                ");
                $stmt->execute($templateItem);
            }
            
            echo json_encode([
                'success' => true,
                'message' => __('config.template_created_from_invoice'),
                'template_id' => $templateId,
                'redirect' => "/config/invoice-templates/{$templateId}/edit"
            ]);
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        exit;
    }
    
    /**
     * Update template line items
     */
    public function updateTemplateItems(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            $invoiceTemplate = new \App\Models\InvoiceTemplate();
            $template = $invoiceTemplate->getWithSettings($id);
            
            if (!$template) {
                throw new \Exception(__('config.template_not_found'));
            }
            
            // Get the items from the form
            $items = $_POST['items'] ?? [];
            $db = Flight::db();
            $db->beginTransaction();
            
            try {
                // Delete existing items
                $stmt = $db->prepare("DELETE FROM template_line_items WHERE template_id = :template_id");
                $stmt->execute([':template_id' => $id]);
                
                // Insert new items
                foreach ($items as $item) {
                    // Skip if description is empty
                    if (empty($item['description'])) {
                        continue;
                    }
                    
                    $stmt = $db->prepare("
                        INSERT INTO template_line_items (
                            template_id, line_type, description, default_quantity, 
                            default_unit_price, vat_rate_id, calculation_formula, 
                            is_mandatory, sort_order
                        ) VALUES (
                            :template_id, :line_type, :description, :default_quantity, 
                            :default_unit_price, :vat_rate_id, :calculation_formula, 
                            :is_mandatory, :sort_order
                        )
                    ");
                    
                    $stmt->execute([
                        ':template_id' => $id,
                        ':line_type' => $item['line_type'],
                        ':description' => $item['description'],
                        ':default_quantity' => $item['default_quantity'] ?: null,
                        ':default_unit_price' => $item['default_unit_price'] ?: null,
                        ':vat_rate_id' => $item['vat_rate_id'] ?: null,
                        ':calculation_formula' => $item['calculation_formula'] ?: null,
                        ':is_mandatory' => isset($item['is_mandatory']) ? 1 : 0,
                        ':sort_order' => $item['sort_order'] ?? 0
                    ]);
                }
                
                $db->commit();
                
                echo json_encode([
                    'success' => true,
                    'message' => __('config.template_items_updated')
                ]);
                
            } catch (\Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
        
        exit;
    }
}