<?php

namespace App\Services;

use Flight;
use PDO;
use Exception;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON>Mailer\PHPMailer\SMTP;

class EmailQueueService
{
    private $db;
    private $emailService;
    
    public function __construct()
    {
        $this->db = Flight::db();
        $this->emailService = new EmailService();
    }
    
    /**
     * Add email to queue
     */
    public function queueEmail($params)
    {
        try {
            // Check blacklist
            if ($this->isBlacklisted($params['to'])) {
                throw new Exception('Email address is blacklisted');
            }
            
            // Check rate limits
            if (!$this->checkRateLimit($params['to'])) {
                throw new Exception('Rate limit exceeded for this email address');
            }
            
            // Prepare queue entry
            $stmt = $this->db->prepare("
                INSERT INTO email_queue (
                    to_email, cc_emails, bcc_emails, reply_to,
                    subject, body_html, body_text, attachments,
                    priority, scheduled_at, metadata
                ) VALUES (
                    :to_email, :cc_emails, :bcc_emails, :reply_to,
                    :subject, :body_html, :body_text, :attachments,
                    :priority, :scheduled_at, :metadata
                )
            ");
            
            $stmt->execute([
                ':to_email' => $params['to'],
                ':cc_emails' => isset($params['cc']) ? (is_array($params['cc']) ? implode(',', $params['cc']) : $params['cc']) : null,
                ':bcc_emails' => isset($params['bcc']) ? (is_array($params['bcc']) ? implode(',', $params['bcc']) : $params['bcc']) : null,
                ':reply_to' => $params['reply_to'] ?? null,
                ':subject' => $params['subject'],
                ':body_html' => $params['body_html'] ?? null,
                ':body_text' => $params['body_text'] ?? null,
                ':attachments' => isset($params['attachments']) ? json_encode($params['attachments']) : null,
                ':priority' => $params['priority'] ?? 'normal',
                ':scheduled_at' => $params['scheduled_at'] ?? date('Y-m-d H:i:s'),
                ':metadata' => isset($params['metadata']) ? json_encode($params['metadata']) : null
            ]);
            
            return $this->db->lastInsertId();
            
        } catch (Exception $e) {
            error_log('EmailQueueService: Failed to queue email - ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Process queue - send pending emails
     */
    public function processQueue($limit = 50)
    {
        $results = [
            'processed' => 0,
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];
        
        try {
            // Get pending emails
            $stmt = $this->db->prepare("
                SELECT * FROM email_queue
                WHERE status = 'pending'
                AND (scheduled_at IS NULL OR scheduled_at <= NOW())
                ORDER BY priority DESC, created_at ASC
                LIMIT :limit
                FOR UPDATE SKIP LOCKED
            ");
            
            $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
            $stmt->execute();
            $emails = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($emails as $email) {
                $results['processed']++;
                
                // Mark as processing
                $this->updateStatus($email['id'], 'processing');
                
                try {
                    // Send email
                    $sendResult = $this->sendQueuedEmail($email);
                    
                    if ($sendResult['success']) {
                        $this->updateStatus($email['id'], 'sent', [
                            'sent_at' => date('Y-m-d H:i:s'),
                            'message_id' => $sendResult['message_id'] ?? null
                        ]);
                        $results['sent']++;
                        
                        // Update rate limit counter
                        $this->incrementRateLimit($email['to_email']);
                        
                    } else {
                        throw new Exception($sendResult['message'] ?? 'Unknown error');
                    }
                    
                } catch (Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "Email {$email['id']}: " . $e->getMessage();
                    
                    // Handle retry logic
                    $this->handleFailedEmail($email, $e->getMessage());
                }
            }
            
        } catch (Exception $e) {
            error_log('EmailQueueService: Queue processing error - ' . $e->getMessage());
            $results['errors'][] = 'Queue processing error: ' . $e->getMessage();
        }
        
        return $results;
    }
    
    /**
     * Send queued email
     */
    private function sendQueuedEmail($queuedEmail)
    {
        try {
            // Prepare email parameters
            $params = [
                'to' => $queuedEmail['to_email'],
                'subject' => $queuedEmail['subject'],
                'body_html' => $queuedEmail['body_html'],
                'body_text' => $queuedEmail['body_text']
            ];
            
            // Add CC/BCC if present
            if (!empty($queuedEmail['cc_emails'])) {
                $params['cc'] = explode(',', $queuedEmail['cc_emails']);
            }
            if (!empty($queuedEmail['bcc_emails'])) {
                $params['bcc'] = explode(',', $queuedEmail['bcc_emails']);
            }
            if (!empty($queuedEmail['reply_to'])) {
                $params['reply_to'] = $queuedEmail['reply_to'];
            }
            
            // Add attachments if present
            if (!empty($queuedEmail['attachments'])) {
                $params['attachments'] = json_decode($queuedEmail['attachments'], true);
            }
            
            // Get mail configuration
            $host = $_ENV['MAIL_HOST'] ?? 'localhost';
            $port = $_ENV['MAIL_PORT'] ?? 1025;
            $username = $_ENV['MAIL_USERNAME'] ?? '';
            $password = $_ENV['MAIL_PASSWORD'] ?? '';
            $encryption = $_ENV['MAIL_ENCRYPTION'] ?? '';
            $fromEmail = $_ENV['MAIL_FROM_ADDRESS'] ?? '<EMAIL>';
            $fromName = $_ENV['MAIL_FROM_NAME'] ?? 'Fit360 AdminDesk';
            
            // Create PHPMailer instance
            $mail = new PHPMailer(true);
            
            // Server settings
            $mail->isSMTP();
            $mail->Host = $host;
            $mail->Port = $port;
            
            // Authentication
            if ($host === 'localhost' && $port == 1025) {
                // Mailhog configuration
                $mail->SMTPAuth = false;
                $mail->SMTPSecure = false;
                $mail->SMTPAutoTLS = false;
            } else {
                // Production SMTP
                $mail->SMTPAuth = !empty($username);
                $mail->Username = $username;
                $mail->Password = $password;
                
                if ($encryption === 'tls') {
                    $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
                } elseif ($encryption === 'ssl') {
                    $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                }
            }
            
            // Set timeout
            $mail->Timeout = 30;
            
            // Recipients
            $mail->setFrom($fromEmail, $fromName);
            $mail->addAddress($params['to']);
            
            // CC recipients
            if (!empty($params['cc'])) {
                foreach ($params['cc'] as $cc) {
                    $mail->addCC(trim($cc));
                }
            }
            
            // BCC recipients
            if (!empty($params['bcc'])) {
                foreach ($params['bcc'] as $bcc) {
                    $mail->addBCC(trim($bcc));
                }
            }
            
            // Reply-To
            if (!empty($params['reply_to'])) {
                $mail->addReplyTo($params['reply_to']);
            }
            
            // Attachments
            if (!empty($params['attachments'])) {
                foreach ($params['attachments'] as $attachment) {
                    if (!empty($attachment['content']) && !empty($attachment['name'])) {
                        $tempFile = tempnam(sys_get_temp_dir(), 'queue_attach_');
                        file_put_contents($tempFile, $attachment['content']);
                        
                        $mail->addAttachment(
                            $tempFile, 
                            $attachment['name'], 
                            'base64', 
                            $attachment['type'] ?? 'application/octet-stream'
                        );
                        
                        // Cleanup temp file after sending
                        register_shutdown_function(function() use ($tempFile) {
                            if (file_exists($tempFile)) {
                                unlink($tempFile);
                            }
                        });
                    }
                }
            }
            
            // Content
            $mail->CharSet = 'UTF-8';
            $mail->Subject = $params['subject'];
            
            if (!empty($params['body_html'])) {
                $mail->isHTML(true);
                $mail->Body = $params['body_html'];
                $mail->AltBody = $params['body_text'] ?? strip_tags($params['body_html']);
            } else {
                $mail->isHTML(false);
                $mail->Body = $params['body_text'] ?? '';
            }
            
            // Enable message ID capture
            $mail->MessageID = '';
            
            // Send email
            $mail->send();
            
            // Log successful send
            $this->logEmailSent($queuedEmail, $mail->getLastMessageID());
            
            return [
                'success' => true,
                'message' => 'Email sent successfully',
                'message_id' => $mail->getLastMessageID()
            ];
            
        } catch (\PHPMailer\PHPMailer\Exception $e) {
            error_log('EmailQueueService: PHPMailer Exception - ' . $e->getMessage());
            throw new Exception('Mail sending failed: ' . $mail->ErrorInfo);
        } catch (Exception $e) {
            error_log('EmailQueueService: Send error - ' . $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Handle failed email with retry logic
     */
    private function handleFailedEmail($email, $errorMessage)
    {
        $retryCount = $email['retry_count'] + 1;
        $maxRetries = $email['max_retries'] ?? $_ENV['MAIL_QUEUE_RETRY_ATTEMPTS'] ?? 3;
        
        if ($retryCount < $maxRetries) {
            // Schedule retry with exponential backoff
            $retryDelay = $_ENV['MAIL_QUEUE_RETRY_DELAY'] ?? 300; // 5 minutes default
            $nextRetry = date('Y-m-d H:i:s', time() + ($retryDelay * $retryCount));
            
            $this->updateStatus($email['id'], 'pending', [
                'retry_count' => $retryCount,
                'scheduled_at' => $nextRetry,
                'error_message' => $errorMessage
            ]);
        } else {
            // Mark as permanently failed
            $this->updateStatus($email['id'], 'failed', [
                'failed_at' => date('Y-m-d H:i:s'),
                'error_message' => $errorMessage
            ]);
            
            // Log failed email
            $this->logEmailFailed($email, $errorMessage);
        }
    }
    
    /**
     * Update email status
     */
    private function updateStatus($id, $status, $additionalData = [])
    {
        $sql = "UPDATE email_queue SET status = :status";
        $params = [':status' => $status, ':id' => $id];
        
        foreach ($additionalData as $field => $value) {
            $sql .= ", {$field} = :{$field}";
            $params[":{$field}"] = $value;
        }
        
        $sql .= " WHERE id = :id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
    }
    
    /**
     * Check if email is blacklisted
     */
    private function isBlacklisted($email)
    {
        $stmt = $this->db->prepare("
            SELECT COUNT(*) FROM email_blacklist 
            WHERE email = :email
        ");
        
        $stmt->execute([':email' => $email]);
        return $stmt->fetchColumn() > 0;
    }
    
    /**
     * Check rate limit
     */
    private function checkRateLimit($email)
    {
        $limitPerMinute = $_ENV['MAIL_RATE_LIMIT_PER_MINUTE'] ?? 30;
        $limitPerHour = $_ENV['MAIL_RATE_LIMIT_PER_HOUR'] ?? 500;
        
        // Check minute limit
        $minuteAgo = date('Y-m-d H:i:s', strtotime('-1 minute'));
        $stmt = $this->db->prepare("
            SELECT sent_count FROM email_rate_limits
            WHERE identifier = :email
            AND identifier_type = 'email'
            AND window_start >= :window_start
        ");
        
        $stmt->execute([
            ':email' => $email,
            ':window_start' => $minuteAgo
        ]);
        
        $minuteCount = array_sum($stmt->fetchAll(PDO::FETCH_COLUMN));
        if ($minuteCount >= $limitPerMinute) {
            return false;
        }
        
        // Check hour limit
        $hourAgo = date('Y-m-d H:i:s', strtotime('-1 hour'));
        $stmt = $this->db->prepare("
            SELECT sent_count FROM email_rate_limits
            WHERE identifier = :email
            AND identifier_type = 'email'
            AND window_start >= :window_start
        ");
        
        $stmt->execute([
            ':email' => $email,
            ':window_start' => $hourAgo
        ]);
        
        $hourCount = array_sum($stmt->fetchAll(PDO::FETCH_COLUMN));
        if ($hourCount >= $limitPerHour) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Increment rate limit counter
     */
    private function incrementRateLimit($email)
    {
        $now = date('Y-m-d H:i:s');
        $windowStart = date('Y-m-d H:i:00'); // Current minute
        $windowEnd = date('Y-m-d H:i:59');
        
        $stmt = $this->db->prepare("
            INSERT INTO email_rate_limits 
            (identifier, identifier_type, sent_count, window_start, window_end)
            VALUES (:email, 'email', 1, :window_start, :window_end)
            ON DUPLICATE KEY UPDATE 
            sent_count = sent_count + 1,
            updated_at = NOW()
        ");
        
        $stmt->execute([
            ':email' => $email,
            ':window_start' => $windowStart,
            ':window_end' => $windowEnd
        ]);
    }
    
    /**
     * Log successful email
     */
    private function logEmailSent($queuedEmail, $messageId)
    {
        $stmt = $this->db->prepare("
            INSERT INTO email_logs (
                queue_id, recipient_email, cc_emails, bcc_emails,
                subject, body_preview, attachments_sent, status,
                sent_at, message_id
            ) VALUES (
                :queue_id, :recipient_email, :cc_emails, :bcc_emails,
                :subject, :body_preview, :attachments_sent, :status,
                :sent_at, :message_id
            )
        ");
        
        $bodyPreview = substr(
            $queuedEmail['body_text'] ?? strip_tags($queuedEmail['body_html'] ?? ''), 
            0, 
            500
        );
        
        $stmt->execute([
            ':queue_id' => $queuedEmail['id'],
            ':recipient_email' => $queuedEmail['to_email'],
            ':cc_emails' => $queuedEmail['cc_emails'],
            ':bcc_emails' => $queuedEmail['bcc_emails'],
            ':subject' => $queuedEmail['subject'],
            ':body_preview' => $bodyPreview,
            ':attachments_sent' => $queuedEmail['attachments'],
            ':status' => 'sent',
            ':sent_at' => date('Y-m-d H:i:s'),
            ':message_id' => $messageId
        ]);
    }
    
    /**
     * Log failed email
     */
    private function logEmailFailed($queuedEmail, $errorMessage)
    {
        $stmt = $this->db->prepare("
            INSERT INTO email_logs (
                queue_id, recipient_email, subject, status,
                error_message
            ) VALUES (
                :queue_id, :recipient_email, :subject, :status,
                :error_message
            )
        ");
        
        $stmt->execute([
            ':queue_id' => $queuedEmail['id'],
            ':recipient_email' => $queuedEmail['to_email'],
            ':subject' => $queuedEmail['subject'],
            ':status' => 'failed',
            ':error_message' => $errorMessage
        ]);
    }
    
    /**
     * Add email to blacklist
     */
    public function blacklistEmail($email, $reason, $addedBy = null, $notes = null)
    {
        $stmt = $this->db->prepare("
            INSERT IGNORE INTO email_blacklist 
            (email, reason, added_by, notes)
            VALUES (:email, :reason, :added_by, :notes)
        ");
        
        return $stmt->execute([
            ':email' => $email,
            ':reason' => $reason,
            ':added_by' => $addedBy,
            ':notes' => $notes
        ]);
    }
    
    /**
     * Remove email from blacklist
     */
    public function unblacklistEmail($email)
    {
        $stmt = $this->db->prepare("
            DELETE FROM email_blacklist WHERE email = :email
        ");
        
        return $stmt->execute([':email' => $email]);
    }
    
    /**
     * Get queue statistics
     */
    public function getQueueStats()
    {
        $stats = [];
        
        // Queue status counts
        $stmt = $this->db->query("
            SELECT status, COUNT(*) as count 
            FROM email_queue 
            GROUP BY status
        ");
        $stats['queue_by_status'] = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        // Recent activity
        $stmt = $this->db->query("
            SELECT 
                DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') as hour,
                COUNT(*) as queued,
                SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
            FROM email_queue
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            GROUP BY hour
            ORDER BY hour DESC
        ");
        $stats['hourly_activity'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Blacklist count
        $stmt = $this->db->query("SELECT COUNT(*) FROM email_blacklist");
        $stats['blacklisted_emails'] = $stmt->fetchColumn();
        
        return $stats;
    }
    
    /**
     * Clean old queue entries
     */
    public function cleanOldEntries($daysToKeep = 30)
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$daysToKeep} days"));
        
        $stmt = $this->db->prepare("
            DELETE FROM email_queue
            WHERE created_at < :cutoff_date
            AND status IN ('sent', 'failed', 'cancelled')
        ");
        
        $stmt->execute([':cutoff_date' => $cutoffDate]);
        
        return $stmt->rowCount();
    }
}