<?php
// Fix duplicate lines for invoice 246
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix Invoice 246 - Duplicate Lines</h2>";
    
    $invoiceId = 246;
    
    // 1. Check current lines
    echo "<h3>1. Current Invoice Lines</h3>";
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ? 
        ORDER BY sort_order, id
    ");
    $stmt->execute([$invoiceId]);
    $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Total lines found: <strong>" . count($lines) . "</strong></p>";
    
    if (count($lines) > 0) {
        echo "<table border='1' cellpadding='5' style='border-collapse: collapse;'>";
        echo "<tr style='background: #f0f0f0;'>";
        echo "<th>ID</th><th>Description</th><th>Quantity</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th>";
        echo "</tr>";
        
        foreach ($lines as $line) {
            echo "<tr>";
            echo "<td>" . $line['id'] . "</td>";
            echo "<td>" . htmlspecialchars($line['description']) . "</td>";
            echo "<td>" . $line['quantity'] . "</td>";
            echo "<td>" . $line['unit_price'] . "</td>";
            echo "<td>" . $line['vat_rate'] . "%</td>";
            echo "<td>" . $line['line_total'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 2. Check if we need to fix anything
        $shouldHaveLoyer = false;
        $shouldHaveCharges = false;
        $hasLoyer = false;
        $hasCharges = false;
        $duplicateLoyer = [];
        
        foreach ($lines as $line) {
            if (strpos($line['description'], 'Loyer mensuel') !== false) {
                if ($hasLoyer) {
                    $duplicateLoyer[] = $line['id'];
                }
                $hasLoyer = true;
            }
            if (strpos($line['description'], 'Charges locations') !== false) {
                $hasCharges = true;
            }
        }
        
        // Check invoice type
        $stmt = $db->prepare("
            SELECT i.*, cit.code as invoice_type_code 
            FROM invoices i
            LEFT JOIN config_invoice_types cit ON i.invoice_type_id = cit.id
            WHERE i.id = ?
        ");
        $stmt->execute([$invoiceId]);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "<h3>2. Invoice Details</h3>";
        echo "<p>Invoice Number: <strong>" . $invoice['invoice_number'] . "</strong></p>";
        echo "<p>Invoice Type: <strong>" . ($invoice['invoice_type_code'] ?? 'N/A') . "</strong></p>";
        
        if ($invoice['invoice_type_code'] === 'loyer' || $invoice['invoice_type_code'] === 'LOY') {
            echo "<p style='color: blue;'>This is a rental invoice - should have both Loyer mensuel and Charges locations</p>";
            $shouldHaveLoyer = true;
            $shouldHaveCharges = true;
        }
        
        // 3. Fix issues
        echo "<h3>3. Issues Found</h3>";
        $hasIssues = false;
        
        if (!empty($duplicateLoyer)) {
            echo "<p style='color: red;'>⚠️ Found duplicate 'Loyer mensuel' entries (IDs: " . implode(', ', $duplicateLoyer) . ")</p>";
            $hasIssues = true;
        }
        
        if ($shouldHaveCharges && !$hasCharges) {
            echo "<p style='color: red;'>⚠️ Missing 'Charges locations' entry</p>";
            $hasIssues = true;
        }
        
        if (!$hasIssues) {
            echo "<p style='color: green;'>✓ No issues found in database</p>";
        }
        
        // 4. Clear cache
        echo "<h3>4. Clear Cache</h3>";
        
        // Using Flight cache if available
        $cacheDir = __DIR__ . '/../storage/cache/invoices/';
        $cacheKey = 'invoice_details_' . $invoiceId;
        $cacheFile = $cacheDir . md5($cacheKey) . '.cache';
        
        if (file_exists($cacheFile)) {
            unlink($cacheFile);
            echo "<p style='color: green;'>✓ Cache file deleted</p>";
        } else {
            echo "<p>No cache file found</p>";
        }
        
        // Also try to clear any other possible cache files
        $patterns = [
            $cacheDir . '*' . $invoiceId . '*',
            $cacheDir . '*' . md5('invoice_' . $invoiceId) . '*',
            $cacheDir . '*' . md5('invoice_details_' . $invoiceId) . '*'
        ];
        
        foreach ($patterns as $pattern) {
            $files = glob($pattern);
            if ($files) {
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                        echo "<p style='color: green;'>✓ Deleted cache file: " . basename($file) . "</p>";
                    }
                }
            }
        }
        
        // 5. Fix action
        if ($hasIssues && isset($_GET['fix'])) {
            echo "<h3>5. Applying Fixes...</h3>";
            
            $db->beginTransaction();
            try {
                // Remove duplicate Loyer mensuel entries
                if (!empty($duplicateLoyer)) {
                    foreach ($duplicateLoyer as $dupId) {
                        $stmt = $db->prepare("DELETE FROM invoice_lines WHERE id = ?");
                        $stmt->execute([$dupId]);
                        echo "<p style='color: green;'>✓ Removed duplicate Loyer mensuel (ID: $dupId)</p>";
                    }
                }
                
                // Add missing Charges locations if needed
                if ($shouldHaveCharges && !$hasCharges) {
                    // Find a Loyer mensuel line to copy settings from
                    $stmt = $db->prepare("
                        SELECT * FROM invoice_lines 
                        WHERE invoice_id = ? AND description LIKE '%Loyer mensuel%'
                        LIMIT 1
                    ");
                    $stmt->execute([$invoiceId]);
                    $loyerLine = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($loyerLine) {
                        // Assume charges are typically around 50-100 EUR
                        $chargesAmount = 75.00; // Default charges amount
                        
                        $stmt = $db->prepare("
                            INSERT INTO invoice_lines (
                                invoice_id, description, quantity, unit_price, 
                                vat_rate, line_total, sort_order
                            ) VALUES (?, ?, ?, ?, ?, ?, ?)
                        ");
                        
                        $lineTotal = 1 * $chargesAmount;
                        $stmt->execute([
                            $invoiceId,
                            'Charges locations',
                            1,
                            $chargesAmount,
                            $loyerLine['vat_rate'],
                            $lineTotal,
                            $loyerLine['sort_order'] + 1
                        ]);
                        
                        echo "<p style='color: green;'>✓ Added Charges locations line (€$chargesAmount)</p>";
                    }
                }
                
                // Recalculate invoice totals
                $stmt = $db->prepare("
                    UPDATE invoices i
                    SET 
                        subtotal = (
                            SELECT COALESCE(SUM(quantity * unit_price), 0) 
                            FROM invoice_lines 
                            WHERE invoice_id = i.id
                        ),
                        vat_amount = (
                            SELECT COALESCE(SUM(quantity * unit_price * vat_rate / 100), 0) 
                            FROM invoice_lines 
                            WHERE invoice_id = i.id
                        ),
                        total = (
                            SELECT COALESCE(SUM(quantity * unit_price * (1 + vat_rate / 100)), 0) 
                            FROM invoice_lines 
                            WHERE invoice_id = i.id
                        )
                    WHERE i.id = ?
                ");
                $stmt->execute([$invoiceId]);
                echo "<p style='color: green;'>✓ Updated invoice totals</p>";
                
                $db->commit();
                echo "<p style='color: green;'><strong>✓ All fixes applied successfully!</strong></p>";
                
            } catch (Exception $e) {
                $db->rollBack();
                echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
            }
        } elseif ($hasIssues && !isset($_GET['fix'])) {
            echo "<br>";
            echo "<form method='GET'>";
            echo "<button type='submit' name='fix' value='1' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;'>Apply Fixes</button>";
            echo "</form>";
        }
    }
    
    // Test links
    echo "<h3>Test Links</h3>";
    echo "<p><a href='/fit/public/invoices/246' target='_blank'>View Invoice 246</a></p>";
    echo "<p><a href='/fit/public/invoice-pdf.php?id=246' target='_blank'>View PDF (should show correct items now)</a></p>";
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>