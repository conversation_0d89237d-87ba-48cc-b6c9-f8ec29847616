{% extends "base-modern.twig" %}

{% block title %}{{ __('config.configuration') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.configuration_management') }}</h1>
        <a href="{{ base_url }}/" class="btn btn-secondary">
            <i class="bi bi-house me-2"></i>{{ __('common.home') }}
        </a>
    </div>

    <!-- Configuration Areas Grid -->
    <div class="row g-4">
        <!-- Company Settings -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-building fs-1 text-primary"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.company_settings') }}</h5>
                    <p class="card-text text-muted">{{ __('config.company_settings_description') }}</p>
                    <a href="{{ base_url }}/config/company" class="btn btn-primary">
                        {{ __('config.configure') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- System Settings -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-info bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-gear fs-1 text-info"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.system_settings') }}</h5>
                    <p class="card-text text-muted">{{ __('config.system_settings_description') }}</p>
                    <a href="{{ base_url }}/config/system" class="btn btn-info text-white">
                        {{ __('config.configure') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- VAT Rates -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-success bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-percent fs-1 text-success"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.vat_rates') }}</h5>
                    <p class="card-text text-muted">{{ __('config.vat_rates_description') }}</p>
                    <a href="{{ base_url }}/config/vat-rates" class="btn btn-success">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Invoice Types -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-warning bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-file-earmark-text fs-1 text-warning"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.invoice_types') }}</h5>
                    <p class="card-text text-muted">{{ __('config.invoice_types_description') }}</p>
                    <a href="{{ base_url }}/config/invoice-types" class="btn btn-warning">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Field Manager -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-danger bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-list-check fs-1 text-danger"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.field_manager') }}</h5>
                    <p class="card-text text-muted">{{ __('config.field_manager_description') }}</p>
                    <a href="{{ base_url }}/config/fields" class="btn btn-danger">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Email Templates -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-secondary bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-envelope fs-1 text-secondary"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.email_templates') }}</h5>
                    <p class="card-text text-muted">{{ __('config.email_templates_description') }}</p>
                    <a href="{{ base_url }}/config/email-templates" class="btn btn-secondary">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Email Automation -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-envelope-open-text fs-1 text-primary"></i>
                    </div>
                    <h5 class="card-title">{{ __('email.automation.title') }}</h5>
                    <p class="card-text text-muted">{{ __('email.automation.description') }}</p>
                    <a href="{{ base_url }}/admin/email-automation" class="btn btn-primary">
                        {{ __('config.configure') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Invoice Templates -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-dark bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-file-text-fill fs-1 text-dark"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.invoice_templates') }}</h5>
                    <p class="card-text text-muted">{{ __('config.invoice_templates_description') }}</p>
                    <a href="{{ base_url }}/config/invoice-templates" class="btn btn-dark">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Color Schemes -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-gradient d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        <i class="bi bi-palette fs-1" style="color: #ffffff; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.color_schemes') }}</h5>
                    <p class="card-text text-muted">{{ __('config.color_schemes_description') }}</p>
                    <a href="{{ base_url }}/config/color-schemes" class="btn btn-primary">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Number Formats -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-indigo bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-hash fs-1 text-indigo"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.number_formats') }}</h5>
                    <p class="card-text text-muted">{{ __('config.number_formats_description') }}</p>
                    <a href="{{ base_url }}/config/number-formats" class="btn btn-indigo text-white">
                        {{ __('config.configure') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Document Types -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-purple bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-file-earmark-check fs-1 text-purple"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.document_types') }}</h5>
                    <p class="card-text text-muted">{{ __('config.document_types_description') }}</p>
                    <a href="{{ base_url }}/config/document-types" class="btn btn-purple text-white">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment Methods -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-teal bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-credit-card fs-1 text-teal"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.payment_methods') }}</h5>
                    <p class="card-text text-muted">{{ __('config.payment_methods_description') }}</p>
                    <a href="{{ base_url }}/config/payment-methods" class="btn btn-teal text-white">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Payment Terms -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-indigo bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-calendar-check fs-1 text-indigo"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.payment_terms') }}</h5>
                    <p class="card-text text-muted">{{ __('config.payment_terms_description') }}</p>
                    <a href="{{ base_url }}/config/payment-terms" class="btn btn-indigo text-white">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Translations -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-pink bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-translate fs-1 text-pink"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.translations') }}</h5>
                    <p class="card-text text-muted">{{ __('config.translations_description') }}</p>
                    <a href="{{ base_url }}/translations" class="btn btn-pink text-white">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Rate Profiles -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-purple bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-calculator fs-1 text-purple"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.rate_profiles') }}</h5>
                    <p class="card-text text-muted">{{ __('config.rate_profiles_description') }}</p>
                    <a href="{{ base_url }}/retrocession/rate-profiles" class="btn btn-purple text-white">
                        {{ __('config.manage') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Table Columns -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-cyan bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-table fs-1 text-cyan"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.table_columns') }}</h5>
                    <p class="card-text text-muted">{{ __('config.table_columns_description') }}</p>
                    <a href="{{ base_url }}/config/table-columns" class="btn btn-cyan text-white">
                        {{ __('config.configure') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Invoice Items Configuration -->
        <div class="col-xl-4 col-lg-6">
            <div class="card border-0 shadow-sm h-100 hover-shadow">
                <div class="card-body text-center">
                    <div class="rounded-circle bg-orange bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                        <i class="bi bi-receipt-cutoff fs-1 text-orange"></i>
                    </div>
                    <h5 class="card-title">{{ __('config.invoice_items_columns') }}</h5>
                    <p class="card-text text-muted">{{ __('config.invoice_items_columns_description') }}</p>
                    <a href="{{ base_url }}/config/invoice-items-columns" class="btn btn-orange text-white">
                        {{ __('config.configure') }} <i class="bi bi-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mt-5">
        <div class="col-12">
            <h4 class="mb-3">{{ __('config.system_status') }}</h4>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-check-circle-fill text-success fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ __('config.active_vat_rates') }}</h6>
                            <p class="mb-0 text-muted">{{ stats.vat_rates }} {{ __('config.rates') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-file-text-fill text-info fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ __('config.document_types') }}</h6>
                            <p class="mb-0 text-muted">{{ stats.invoice_types }} {{ __('config.types') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-envelope-fill text-warning fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ __('config.email_templates') }}</h6>
                            <p class="mb-0 text-muted">{{ stats.email_templates }} {{ __('config.templates') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="bi bi-list-ul text-danger fs-2"></i>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-0">{{ __('config.custom_fields') }}</h6>
                            <p class="mb-0 text-muted">{{ stats.custom_fields }} {{ __('config.fields') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.hover-shadow {
    transition: all 0.3s ease;
}
.hover-shadow:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
/* Background opacity classes with enhanced visibility */
.bg-opacity-10 {
    --bs-bg-opacity: 0.15 !important;
}
.bg-opacity-25 {
    --bs-bg-opacity: 0.25;
}
/* Icon circle styling with better contrast */
.rounded-circle[style*="width: 80px"] {
    position: relative;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}
.rounded-circle[style*="width: 80px"] i {
    font-size: 2.5rem !important;
    line-height: 1;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}
/* Ensure icon visibility with specific backgrounds */
.rounded-circle.bg-primary { background-color: rgba(var(--bs-primary-rgb), 0.15) !important; }
.rounded-circle.bg-info { background-color: rgba(var(--bs-info-rgb), 0.15) !important; }
.rounded-circle.bg-success { background-color: rgba(var(--bs-success-rgb), 0.15) !important; }
.rounded-circle.bg-warning { background-color: rgba(var(--bs-warning-rgb), 0.15) !important; }
.rounded-circle.bg-danger { background-color: rgba(var(--bs-danger-rgb), 0.15) !important; }
.rounded-circle.bg-secondary { background-color: rgba(var(--bs-secondary-rgb), 0.15) !important; }
.rounded-circle.bg-indigo { background-color: rgba(99, 102, 241, 0.15) !important; }
.rounded-circle.bg-purple { background-color: rgba(147, 51, 234, 0.15) !important; }
.rounded-circle.bg-pink { background-color: rgba(236, 72, 153, 0.15) !important; }
.rounded-circle.bg-teal { background-color: rgba(20, 184, 166, 0.15) !important; }
.rounded-circle.bg-cyan { background-color: rgba(6, 182, 212, 0.15) !important; }
.rounded-circle.bg-orange { background-color: rgba(251, 146, 60, 0.15) !important; }
/* Gradient background for color schemes */
.bg-gradient {
    background-size: 100% 100%;
    background-position: center;
}
/* Custom colors */
.bg-indigo {
    background-color: rgba(99, 102, 241, var(--bs-bg-opacity, 1)) !important;
}
.text-indigo {
    color: #6366f1 !important;
}
.btn-indigo {
    background-color: #6366f1;
    border-color: #6366f1;
}
.btn-indigo:hover {
    background-color: #4f46e5;
    border-color: #4f46e5;
}
.bg-teal {
    background-color: rgba(20, 184, 166, var(--bs-bg-opacity, 1)) !important;
}
.text-teal {
    color: #14b8a6 !important;
}
.btn-teal {
    background-color: #14b8a6;
    border-color: #14b8a6;
}
.btn-teal:hover {
    background-color: #0d9488;
    border-color: #0d9488;
}
.bg-purple {
    background-color: rgba(147, 51, 234, var(--bs-bg-opacity, 1)) !important;
}
.text-purple {
    color: #9333ea !important;
}
.btn-purple {
    background-color: #9333ea;
    border-color: #9333ea;
}
.btn-purple:hover {
    background-color: #7c3aed;
    border-color: #7c3aed;
}
.bg-pink {
    background-color: rgba(236, 72, 153, var(--bs-bg-opacity, 1)) !important;
}
.text-pink {
    color: #ec4899 !important;
}
.btn-pink {
    background-color: #ec4899;
    border-color: #ec4899;
}
.btn-pink:hover {
    background-color: #db2777;
    border-color: #db2777;
}
.bg-cyan {
    background-color: rgba(6, 182, 212, var(--bs-bg-opacity, 1)) !important;
}
.text-cyan {
    color: #06b6d4 !important;
}
.btn-cyan {
    background-color: #06b6d4;
    border-color: #06b6d4;
}
.btn-cyan:hover {
    background-color: #0891b2;
    border-color: #0891b2;
}
.bg-orange {
    background-color: rgba(251, 146, 60, var(--bs-bg-opacity, 1)) !important;
}
.text-orange {
    color: #fb923c !important;
}
.btn-orange {
    background-color: #fb923c;
    border-color: #fb923c;
}
.btn-orange:hover {
    background-color: #f97316;
    border-color: #f97316;
}
/* Ensure icon visibility in circles */
.rounded-circle i.text-primary { color: var(--bs-primary) !important; }
.rounded-circle i.text-info { color: var(--bs-info) !important; }
.rounded-circle i.text-success { color: var(--bs-success) !important; }
.rounded-circle i.text-warning { color: var(--bs-warning) !important; }
.rounded-circle i.text-danger { color: var(--bs-danger) !important; }
.rounded-circle i.text-secondary { color: var(--bs-secondary) !important; }
.rounded-circle i.text-indigo { color: #6366f1 !important; }
.rounded-circle i.text-purple { color: #9333ea !important; }
.rounded-circle i.text-pink { color: #ec4899 !important; }
.rounded-circle i.text-teal { color: #14b8a6 !important; }
.rounded-circle i.text-cyan { color: #06b6d4 !important; }
.rounded-circle i.text-orange { color: #fb923c !important; }
</style>
{% endblock %}