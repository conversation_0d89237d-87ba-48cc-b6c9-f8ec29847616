/**
 * Quick Add Client Fix for Invoice Form
 * This script fixes the + button functionality to add new clients
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Quick add client fix loaded');
    
    const addNewBtn = document.getElementById('addNewBillableBtn');
    if (!addNewBtn) {
        console.error('Add new billable button not found');
        return;
    }
    
    // Remove existing event listeners by cloning the button
    const newBtn = addNewBtn.cloneNode(true);
    addNewBtn.parentNode.replaceChild(newBtn, addNewBtn);
    
    // Add new event listener
    newBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        const type = document.getElementById('billable_type').value;
        
        if (!type) {
            alert('Please select a billable type first');
            return;
        }
        
        if (type === 'client') {
            // Option 1: Open in a modal instead of popup (more reliable)
            openQuickAddModal('client');
            
            // Option 2: If you prefer popup, uncomment this:
            // const popup = window.open(window.base_url + '/clients/create?quick=1', 'newClient', 'width=800,height=600,scrollbars=yes,resizable=yes');
            // if (!popup || popup.closed || typeof popup.closed == 'undefined') {
            //     alert('Please allow popups for this site to add new clients');
            // }
        } else if (type === 'patient') {
            openQuickAddModal('patient');
        }
    });
});

// Function to open quick add modal
function openQuickAddModal(type) {
    // Create modal HTML
    const modalHtml = `
        <div class="modal fade" id="quickAddModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Add New ${type === 'client' ? 'Client' : 'Patient'}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="quickAddForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">${type === 'client' ? 'Client' : 'Patient'} Number *</label>
                                        <input type="text" class="form-control" id="quick_number" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Type *</label>
                                        <select class="form-select" id="quick_type" required>
                                            <option value="individual">Individual</option>
                                            <option value="company">Company</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row" id="nameFields">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="quick_first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="quick_last_name" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row" id="companyFields" style="display: none;">
                                <div class="col-md-12">
                                    <div class="mb-3">
                                        <label class="form-label">Company Name *</label>
                                        <input type="text" class="form-control" id="quick_company_name">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Email</label>
                                        <input type="email" class="form-control" id="quick_email">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Phone</label>
                                        <input type="tel" class="form-control" id="quick_phone">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveQuickAdd('${type}')">Save ${type === 'client' ? 'Client' : 'Patient'}</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Remove existing modal if present
    const existingModal = document.getElementById('quickAddModal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // Handle type change
    document.getElementById('quick_type').addEventListener('change', function() {
        const isCompany = this.value === 'company';
        document.getElementById('nameFields').style.display = isCompany ? 'none' : 'flex';
        document.getElementById('companyFields').style.display = isCompany ? 'flex' : 'none';
        
        // Update required fields
        document.getElementById('quick_first_name').required = !isCompany;
        document.getElementById('quick_last_name').required = !isCompany;
        document.getElementById('quick_company_name').required = isCompany;
    });
    
    // Generate client/patient number
    generateQuickNumber(type);
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('quickAddModal'));
    modal.show();
}

// Generate client/patient number
function generateQuickNumber(type) {
    const prefix = type === 'client' ? 'CLT' : 'PAT';
    const timestamp = Date.now().toString().slice(-6);
    document.getElementById('quick_number').value = prefix + timestamp;
}

// Save quick add
function saveQuickAdd(type) {
    const form = document.getElementById('quickAddForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const clientType = document.getElementById('quick_type').value;
    const data = {
        client_number: document.getElementById('quick_number').value,
        client_type: clientType,
        email: document.getElementById('quick_email').value,
        phone: document.getElementById('quick_phone').value
    };
    
    if (clientType === 'company') {
        data.company_name = document.getElementById('quick_company_name').value;
        data.name = data.company_name; // For display
    } else {
        data.first_name = document.getElementById('quick_first_name').value;
        data.last_name = document.getElementById('quick_last_name').value;
        data.name = data.first_name + ' ' + data.last_name; // For display
    }
    
    // For now, just add to dropdown without server save
    // In production, you would send this to the server
    addToDropdown(type, data);
    
    // Close modal
    bootstrap.Modal.getInstance(document.getElementById('quickAddModal')).hide();
    
    // Show success message
    showSuccessMessage(`${type === 'client' ? 'Client' : 'Patient'} added successfully!`);
}

// Add new item to dropdown
function addToDropdown(type, data) {
    const select = document.getElementById('billable_id');
    
    // Create new option
    const option = document.createElement('option');
    option.value = type + '_temp_' + Date.now(); // Temporary ID
    option.textContent = data.name + ' (' + data.client_number + ')';
    option.selected = true;
    
    // Add to dropdown
    select.appendChild(option);
    
    // Store in session storage for later submission
    const tempClients = JSON.parse(sessionStorage.getItem('tempClients') || '[]');
    tempClients.push(data);
    sessionStorage.setItem('tempClients', JSON.stringify(tempClients));
}

// Show success message
function showSuccessMessage(message) {
    const alertHtml = `
        <div class="alert alert-success alert-dismissible fade show position-fixed top-0 start-50 translate-middle-x mt-3" style="z-index: 9999;">
            <i class="bi bi-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    document.body.insertAdjacentHTML('afterbegin', alertHtml);
    
    // Auto-hide after 3 seconds
    setTimeout(() => {
        const alert = document.querySelector('.alert');
        if (alert) {
            bootstrap.Alert.getInstance(alert)?.close();
        }
    }, 3000);
}

// Alternative: Simple popup approach
window.handleQuickAddClient = function() {
    const type = document.getElementById('billable_type').value;
    
    if (type === 'client') {
        // Try to open popup
        const url = window.base_url + '/clients/create?quick=1';
        const popup = window.open(url, 'quickAddClient', 'width=800,height=600,scrollbars=yes,resizable=yes');
        
        if (!popup || popup.closed || typeof popup.closed == 'undefined') {
            // Popup blocked, use fallback
            if (confirm('Popup blocked! Would you like to open the client creation page in a new tab?')) {
                window.open(url, '_blank');
            }
        } else {
            // Set up listener for when popup closes
            const checkClosed = setInterval(function() {
                if (popup.closed) {
                    clearInterval(checkClosed);
                    // Refresh the billable options
                    if (typeof loadBillableOptions === 'function') {
                        loadBillableOptions();
                    }
                }
            }, 1000);
        }
    }
};