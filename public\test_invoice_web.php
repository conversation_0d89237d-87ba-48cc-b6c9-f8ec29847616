<?php
/**
 * Web-based Invoice System Test
 * Access this file through your browser: http://localhost/fit/public/test_invoice_web.php
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Client;
use App\Models\Invoice;
use App\Models\User;

// Start output
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice System Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-passed { color: #28a745; font-weight: bold; }
        .test-failed { color: #dc3545; font-weight: bold; }
        .test-warning { color: #ffc107; font-weight: bold; }
        .test-info { color: #17a2b8; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #dee2e6; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
<div class="container mt-4">
    <h1 class="mb-4">Invoice System Test</h1>
    
    <?php
    $tests = [];
    $totalTests = 0;
    $passedTests = 0;
    
    // Test 1: Database Connection
    echo '<div class="test-section">';
    echo '<h3>1. Database Connection Test</h3>';
    try {
        $db = Flight::db();
        if ($db) {
            echo '<p class="test-passed">✓ Database connection successful</p>';
            $passedTests++;
            
            // Test query
            $stmt = $db->query("SELECT VERSION() as version");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo '<p class="test-passed">✓ Database queries working (MySQL ' . $result['version'] . ')</p>';
            $passedTests++;
        }
    } catch (Exception $e) {
        echo '<p class="test-failed">✗ Database connection failed: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    $totalTests += 2;
    echo '</div>';
    
    // Test 2: Required Tables
    echo '<div class="test-section">';
    echo '<h3>2. Required Tables Check</h3>';
    $requiredTables = [
        'clients' => 'Client management',
        'users' => 'User management', 
        'invoices' => 'Invoice storage',
        'invoice_items' => 'Invoice line items',
        'config_invoice_types' => 'Invoice type configuration',
        'config_vat_rates' => 'VAT rate configuration',
        'config_payment_terms' => 'Payment terms',
        'document_types' => 'Document type configuration',
        'payment_methods' => 'Payment method configuration'
    ];
    
    echo '<table class="table table-sm">';
    echo '<thead><tr><th>Table</th><th>Purpose</th><th>Status</th><th>Records</th></tr></thead>';
    echo '<tbody>';
    foreach ($requiredTables as $table => $purpose) {
        echo '<tr>';
        echo '<td><code>' . $table . '</code></td>';
        echo '<td>' . $purpose . '</td>';
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM $table");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo '<td class="test-passed">✓ Exists</td>';
            echo '<td>' . $count . ' records</td>';
            $passedTests++;
        } catch (Exception $e) {
            echo '<td class="test-failed">✗ Missing</td>';
            echo '<td>-</td>';
        }
        echo '</tr>';
        $totalTests++;
    }
    echo '</tbody></table>';
    echo '</div>';
    
    // Test 3: Client Check
    echo '<div class="test-section">';
    echo '<h3>3. Client Availability</h3>';
    try {
        $clientModel = new Client();
        $clients = $clientModel->getAllActive();
        $clientCount = count($clients);
        
        if ($clientCount > 0) {
            echo '<p class="test-passed">✓ Found ' . $clientCount . ' active client(s)</p>';
            echo '<div class="mt-2"><strong>Sample clients:</strong></div>';
            echo '<ul class="list-group mt-2">';
            foreach (array_slice($clients, 0, 3) as $client) {
                $name = trim($client['company_name'] ?: $client['first_name'] . ' ' . $client['last_name']);
                echo '<li class="list-group-item">';
                echo '<strong>' . htmlspecialchars($name) . '</strong> ';
                echo '<span class="text-muted">(' . $client['client_number'] . ')</span><br>';
                echo '<small>Email: ' . htmlspecialchars($client['email']) . '</small>';
                echo '</li>';
            }
            echo '</ul>';
            $passedTests++;
        } else {
            echo '<p class="test-failed">✗ No active clients found!</p>';
            echo '<div class="alert alert-warning mt-3">';
            echo '<strong>Action Required:</strong> ';
            echo '<a href="/fit/public/clients/create" class="btn btn-primary btn-sm">Create a Client</a>';
            echo '</div>';
        }
    } catch (Exception $e) {
        echo '<p class="test-failed">✗ Error checking clients: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    $totalTests++;
    echo '</div>';
    
    // Test 4: Configuration Check
    echo '<div class="test-section">';
    echo '<h3>4. System Configuration</h3>';
    
    // Invoice Types
    echo '<h4>Invoice Types:</h4>';
    try {
        $stmt = $db->query("SELECT * FROM config_invoice_types WHERE is_active = 1");
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (count($types) > 0) {
            echo '<p class="test-passed">✓ Found ' . count($types) . ' active invoice type(s)</p>';
            echo '<ul>';
            foreach ($types as $type) {
                $name = json_decode($type['name'], true);
                $displayName = is_array($name) ? ($name['en'] ?? $name['fr'] ?? $type['code']) : $type['name'];
                echo '<li>' . htmlspecialchars($displayName) . ' (Code: ' . $type['code'] . ')</li>';
            }
            echo '</ul>';
            $passedTests++;
        } else {
            echo '<p class="test-failed">✗ No active invoice types configured</p>';
        }
    } catch (Exception $e) {
        echo '<p class="test-failed">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    $totalTests++;
    
    // VAT Rates
    echo '<h4>VAT Rates:</h4>';
    try {
        $stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1 ORDER BY rate");
        $rates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (count($rates) > 0) {
            echo '<p class="test-passed">✓ Found ' . count($rates) . ' active VAT rate(s)</p>';
            echo '<ul>';
            foreach ($rates as $rate) {
                $default = $rate['is_default'] ? ' <span class="badge bg-primary">Default</span>' : '';
                echo '<li>' . $rate['rate'] . '% - ' . htmlspecialchars($rate['code']) . $default . '</li>';
            }
            echo '</ul>';
            $passedTests++;
        } else {
            echo '<p class="test-failed">✗ No active VAT rates configured</p>';
        }
    } catch (Exception $e) {
        echo '<p class="test-failed">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    $totalTests++;
    
    // Document Types
    echo '<h4>Document Types:</h4>';
    try {
        $stmt = $db->query("SELECT * FROM document_types WHERE is_active = 1");
        $docTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (count($docTypes) > 0) {
            echo '<p class="test-passed">✓ Found ' . count($docTypes) . ' active document type(s)</p>';
            echo '<table class="table table-sm">';
            echo '<thead><tr><th>Type</th><th>Prefix</th><th>Current Number</th></tr></thead>';
            echo '<tbody>';
            foreach ($docTypes as $type) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($type['name']) . '</td>';
                echo '<td><code>' . htmlspecialchars($type['prefix']) . '</code></td>';
                echo '<td>' . $type['current_number'] . '</td>';
                echo '</tr>';
            }
            echo '</tbody></table>';
            $passedTests++;
        } else {
            echo '<p class="test-failed">✗ No active document types configured</p>';
        }
    } catch (Exception $e) {
        echo '<p class="test-failed">✗ Error: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
    $totalTests++;
    echo '</div>';
    
    // Test 5: Session Check
    echo '<div class="test-section">';
    echo '<h3>5. Session & Authentication</h3>';
    if (session_status() === PHP_SESSION_ACTIVE) {
        echo '<p class="test-passed">✓ Session is active</p>';
        if (isset($_SESSION['user_id'])) {
            echo '<p class="test-passed">✓ User is logged in (ID: ' . $_SESSION['user_id'] . ')</p>';
            if (isset($_SESSION['username'])) {
                echo '<p class="test-info">Username: ' . htmlspecialchars($_SESSION['username']) . '</p>';
            }
            $passedTests += 2;
        } else {
            echo '<p class="test-warning">! No user logged in</p>';
            echo '<div class="alert alert-warning mt-3">';
            echo '<strong>Note:</strong> You may need to <a href="/fit/public/login">login</a> to create invoices.';
            echo '</div>';
            $passedTests++;
        }
    } else {
        echo '<p class="test-failed">✗ Session not active</p>';
    }
    $totalTests += 2;
    echo '</div>';
    
    // Test 6: Quick Invoice Creation Form
    echo '<div class="test-section">';
    echo '<h3>6. Quick Invoice Test</h3>';
    
    $canCreateInvoice = false;
    if ($clientCount > 0) {
        $stmt = $db->query("SELECT * FROM config_invoice_types WHERE is_active = 1 LIMIT 1");
        $invoiceType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $db->query("SELECT * FROM document_types WHERE is_active = 1 AND code = 'invoice' LIMIT 1");
        $docType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1 LIMIT 1");
        $vatRate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoiceType && $docType && $vatRate) {
            $canCreateInvoice = true;
            echo '<p class="test-passed">✓ All requirements met for invoice creation</p>';
            echo '<div class="mt-3">';
            echo '<a href="/fit/public/invoices/create" class="btn btn-success">Create New Invoice</a>';
            echo '</div>';
            $passedTests++;
        } else {
            echo '<p class="test-failed">✗ Missing configuration for invoice creation:</p>';
            echo '<ul>';
            if (!$invoiceType) echo '<li>No invoice types configured</li>';
            if (!$docType) echo '<li>No document types configured</li>';
            if (!$vatRate) echo '<li>No VAT rates configured</li>';
            echo '</ul>';
        }
    } else {
        echo '<p class="test-failed">✗ Cannot create invoices without clients</p>';
    }
    $totalTests++;
    echo '</div>';
    
    // Summary
    $percentage = $totalTests > 0 ? round(($passedTests / $totalTests) * 100) : 0;
    $summaryClass = $percentage >= 80 ? 'success' : ($percentage >= 50 ? 'warning' : 'danger');
    
    echo '<div class="test-section">';
    echo '<h3>Test Summary</h3>';
    echo '<div class="progress" style="height: 30px;">';
    echo '<div class="progress-bar bg-' . $summaryClass . '" role="progressbar" style="width: ' . $percentage . '%">';
    echo $percentage . '% (' . $passedTests . '/' . $totalTests . ' tests passed)';
    echo '</div>';
    echo '</div>';
    
    if ($percentage < 100) {
        echo '<div class="mt-4">';
        echo '<h4>Recommended Actions:</h4>';
        echo '<ol>';
        if ($clientCount == 0) {
            echo '<li><strong>Create a client:</strong> <a href="/fit/public/clients/create">Add New Client</a></li>';
        }
        if (!isset($_SESSION['user_id'])) {
            echo '<li><strong>Login to the system:</strong> <a href="/fit/public/login">Login</a></li>';
        }
        echo '<li><strong>Check configuration:</strong> <a href="/fit/public/config">System Configuration</a></li>';
        echo '<li><strong>Run database migrations:</strong> <code>cd database && php migrate.php</code></li>';
        echo '</ol>';
        echo '</div>';
    }
    echo '</div>';
    ?>
    
    <div class="mt-4 mb-5 text-center">
        <a href="/fit/public/" class="btn btn-primary">Go to Dashboard</a>
        <a href="/fit/public/invoices" class="btn btn-secondary">View Invoices</a>
        <a href="/fit/public/clients" class="btn btn-secondary">View Clients</a>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>