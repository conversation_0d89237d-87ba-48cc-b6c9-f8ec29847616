<?php
// Simple duplicate invoice lines checker
require_once __DIR__ . '/vendor/autoload.php';

try {
    // Load environment
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
    $dotenv->load();
    
    // Database connection - always use .env values
    $host = $_ENV['DB_HOST'];
    $dbname = $_ENV['DB_DATABASE'];
    $username = $_ENV['DB_USERNAME'];
    $password = $_ENV['DB_PASSWORD'];
    
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== Checking for Duplicate Invoice Lines ===\n\n";
    
    // 1. Check for exact duplicates
    echo "1. Checking for exact duplicate entries...\n";
    $stmt = $db->prepare("
        SELECT 
            invoice_id, 
            description, 
            quantity, 
            unit_price, 
            vat_rate,
            COUNT(*) as count
        FROM invoice_lines
        GROUP BY invoice_id, description, quantity, unit_price, vat_rate
        HAVING count > 1
        ORDER BY count DESC, invoice_id
        LIMIT 20
    ");
    $stmt->execute();
    $exactDuplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($exactDuplicates)) {
        echo "✓ No exact duplicate lines found.\n\n";
    } else {
        echo "⚠️  Found " . count($exactDuplicates) . " sets of duplicate lines!\n\n";
        echo "Invoice ID | Description | Qty | Price | VAT | Count\n";
        echo str_repeat("-", 70) . "\n";
        
        foreach ($exactDuplicates as $dup) {
            printf("%-10d | %-20s | %3d | %7.2f | %3d%% | %d\n",
                $dup['invoice_id'],
                substr($dup['description'], 0, 20),
                $dup['quantity'],
                $dup['unit_price'],
                $dup['vat_rate'],
                $dup['count']
            );
        }
        echo "\n";
    }
    
    // 2. Summary of affected invoices
    echo "2. Summary of affected invoices...\n";
    $stmt = $db->prepare("
        SELECT 
            i.id,
            i.invoice_number,
            i.status,
            COUNT(il.id) as total_lines,
            COUNT(DISTINCT CONCAT(il.description, il.quantity, il.unit_price, il.vat_rate)) as unique_lines
        FROM invoices i
        JOIN invoice_lines il ON i.id = il.invoice_id
        GROUP BY i.id
        HAVING total_lines > unique_lines
        ORDER BY i.id DESC
        LIMIT 10
    ");
    $stmt->execute();
    $affectedInvoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($affectedInvoices)) {
        echo "✓ No invoices with duplicate lines found.\n\n";
    } else {
        echo "Found " . count($affectedInvoices) . " invoices with duplicate lines:\n\n";
        echo "Invoice # | Status | Total Lines | Unique Lines | Duplicates\n";
        echo str_repeat("-", 70) . "\n";
        
        foreach ($affectedInvoices as $inv) {
            printf("%-15s | %-8s | %11d | %12d | %10d\n",
                $inv['invoice_number'],
                $inv['status'],
                $inv['total_lines'],
                $inv['unique_lines'],
                $inv['total_lines'] - $inv['unique_lines']
            );
        }
        echo "\n";
    }
    
    // 3. Check for orphaned lines
    echo "3. Checking for orphaned invoice lines...\n";
    $stmt = $db->prepare("
        SELECT COUNT(*) as orphaned_count
        FROM invoice_lines il
        LEFT JOIN invoices i ON il.invoice_id = i.id
        WHERE i.id IS NULL
    ");
    $stmt->execute();
    $orphaned = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($orphaned['orphaned_count'] == 0) {
        echo "✓ No orphaned invoice lines found.\n\n";
    } else {
        echo "⚠️  Found " . $orphaned['orphaned_count'] . " orphaned invoice lines!\n\n";
    }
    
    // 4. Overall statistics
    echo "4. Overall Statistics:\n";
    $stmt = $db->query("SELECT COUNT(*) as total FROM invoices");
    $totalInvoices = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $db->query("SELECT COUNT(*) as total FROM invoice_lines");
    $totalLines = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "- Total invoices: " . $totalInvoices . "\n";
    echo "- Total invoice lines: " . $totalLines . "\n";
    echo "- Average lines per invoice: " . ($totalInvoices > 0 ? round($totalLines / $totalInvoices, 2) : 0) . "\n";
    
    echo "\n=== Check Complete ===\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}