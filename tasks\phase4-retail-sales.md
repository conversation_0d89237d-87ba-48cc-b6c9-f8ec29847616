# Phase 4: Retail Sales System (Complete from New Specifications)

## Module 4.1: Sales System Foundation

### Task 4.1.1: Create Sales Invoice Tables
```sql
-- Sales invoices (separate from practitioner invoices)
CREATE TABLE `sales_invoices` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `invoice_number` VARCHAR(50) NOT NULL UNIQUE,
    `invoice_type` ENUM('receipt', 'invoice', 'package', 'voucher') DEFAULT 'invoice',
    `client_id` INT UNSIGNED NOT NULL,
    `client_type` ENUM('patient', 'staff', 'external') DEFAULT 'patient',
    `issue_date` DATE NOT NULL,
    `subtotal` DECIMAL(10,2) NOT NULL,
    `discount_amount` DECIMAL(10,2) DEFAULT 0,
    `total_vat` DECIMAL(10,2) NOT NULL,
    `total_amount` DECIMAL(10,2) NOT NULL,
    `payment_status` ENUM('unpaid', 'partial', 'paid', 'refunded') DEFAULT 'unpaid',
    `amount_paid` DECIMAL(10,2) DEFAULT 0,
    `notes` TEXT,
    `internal_notes` TEXT,
    `therapist_id` INT UNSIGNED NULL,
    `created_by` INT UNSIGNED NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
    INDEX `idx_sales_date` (`issue_date`),
    INDEX `idx_payment_status` (`payment_status`)
);

-- Sales invoice lines
CREATE TABLE `sales_invoice_lines` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `invoice_id` INT UNSIGNED NOT NULL,
    `line_type` ENUM('product', 'service', 'package', 'voucher') NOT NULL,
    `item_id` INT UNSIGNED NOT NULL,
    `description` VARCHAR(500) NOT NULL,
    `quantity` DECIMAL(8,2) NOT NULL DEFAULT 1,
    `unit_price` DECIMAL(10,2) NOT NULL,
    `discount_percent` DECIMAL(5,2) DEFAULT 0,
    `vat_rate` DECIMAL(5,2) NOT NULL,
    `line_total` DECIMAL(10,2) NOT NULL,
    `therapist_id` INT UNSIGNED NULL,
    FOREIGN KEY (`invoice_id`) REFERENCES `sales_invoices` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`therapist_id`) REFERENCES `users` (`id`)
);

-- Separate numbering sequences
ALTER TABLE `config` 
ADD COLUMN `sales_invoice_number_format` VARCHAR(100) DEFAULT 'SALE-{YEAR}-{NUMBER:5}',
ADD COLUMN `receipt_number_format` VARCHAR(100) DEFAULT 'REC-{YEAR}-{NUMBER:5}',
ADD COLUMN `voucher_number_format` VARCHAR(100) DEFAULT 'VOUCHER-{YEAR}-{NUMBER:4}';
```

**Tests for Task 4.1.1:**
```php
// Test 1: Sales invoice creation
public function testSalesInvoiceCreation() {
    $invoice = SalesInvoice::create([
        'invoice_number' => 'SALE-2025-00001',
        'client_id' => 1,
        'issue_date' => now(),
        'subtotal' => 100.00,
        'total_vat' => 17.00,
        'total_amount' => 117.00
    ]);
    
    $this->assertDatabaseHas('sales_invoices', [
        'invoice_number' => 'SALE-2025-00001'
    ]);
}

// Test 2: Separate numbering from practitioner invoices
public function testSeparateNumbering() {
    $practitionerInvoice = Invoice::generateNumber('rental');
    $salesInvoice = SalesInvoice::generateNumber('invoice');
    
    $this->assertStringContainsString('FIT', $practitionerInvoice);
    $this->assertStringContainsString('SALE', $salesInvoice);
}
```

## Module 4.2: Product Catalog

### Task 4.2.1: Create Catalog Tables
```sql
-- Product/Service catalog
CREATE TABLE `catalog_items` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(50) NOT NULL UNIQUE,
    `name` VARCHAR(255) NOT NULL,
    `category_id` INT UNSIGNED NOT NULL,
    `item_type` ENUM('product', 'service', 'package') NOT NULL,
    `description` TEXT,
    `unit_price` DECIMAL(10,2) NOT NULL,
    `vat_rate_id` INT UNSIGNED NOT NULL,
    `is_stockable` BOOLEAN DEFAULT FALSE,
    `current_stock` INT DEFAULT 0,
    `low_stock_alert` INT DEFAULT 5,
    `quick_sale_button` BOOLEAN DEFAULT FALSE,
    `button_color` VARCHAR(7) DEFAULT '#007bff',
    `button_order` INT DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`category_id`) REFERENCES `catalog_categories` (`id`),
    FOREIGN KEY (`vat_rate_id`) REFERENCES `vat_rates` (`id`),
    INDEX `idx_quick_sale` (`quick_sale_button`, `button_order`)
);

-- Catalog categories
CREATE TABLE `catalog_categories` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `parent_id` INT UNSIGNED NULL,
    `icon` VARCHAR(50) DEFAULT 'fa-box',
    `sort_order` INT DEFAULT 0,
    FOREIGN KEY (`parent_id`) REFERENCES `catalog_categories` (`id`)
);

-- Stock movements
CREATE TABLE `stock_movements` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `item_id` INT UNSIGNED NOT NULL,
    `movement_type` ENUM('sale', 'adjustment', 'return', 'purchase') NOT NULL,
    `quantity` INT NOT NULL,
    `reference_type` VARCHAR(50) NULL,
    `reference_id` INT UNSIGNED NULL,
    `notes` TEXT,
    `created_by` INT UNSIGNED NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`item_id`) REFERENCES `catalog_items` (`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
    INDEX `idx_item_date` (`item_id`, `created_at`)
);
```

**Tests for Task 4.2.1:**
```php
// Test 1: Product creation with stock
public function testProductCreationWithStock() {
    $product = CatalogItem::create([
        'code' => 'MOIL001',
        'name' => 'Huile de Massage 100ml',
        'category_id' => 1,
        'item_type' => 'product',
        'unit_price' => 25.00,
        'vat_rate_id' => 1,
        'is_stockable' => true,
        'current_stock' => 50,
        'low_stock_alert' => 10
    ]);
    
    $this->assertTrue($product->is_stockable);
    $this->assertEquals(50, $product->current_stock);
}

// Test 2: Quick sale button configuration
public function testQuickSaleButtons() {
    CatalogItem::factory()->count(10)->create(['quick_sale_button' => true]);
    
    $quickItems = CatalogItem::getQuickSaleItems();
    
    $this->assertCount(10, $quickItems);
    $this->assertTrue($quickItems->first()->quick_sale_button);
}

// Test 3: Stock movement tracking
public function testStockMovement() {
    $product = CatalogItem::factory()->create(['current_stock' => 50]);
    
    $movement = StockMovement::create([
        'item_id' => $product->id,
        'movement_type' => 'sale',
        'quantity' => -5,
        'reference_type' => 'sales_invoice',
        'reference_id' => 1
    ]);
    
    $product->refresh();
    $this->assertEquals(45, $product->current_stock);
}

// Test 4: Low stock alert
public function testLowStockAlert() {
    $product = CatalogItem::factory()->create([
        'current_stock' => 8,
        'low_stock_alert' => 10
    ]);
    
    $lowStockItems = CatalogItem::getLowStockItems();
    
    $this->assertTrue($lowStockItems->contains($product));
}
```

## Module 4.3: Payment System

### Task 4.3.1: Create Payment Tables (FROM OLD PHASE 4 + Enhanced)
```sql
-- Payment records with multi-payment support
CREATE TABLE `sales_payments` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `invoice_id` INT UNSIGNED NOT NULL,
    `payment_date` DATETIME NOT NULL,
    `payment_method` ENUM('cash', 'card', 'transfer', 'voucher', 'insurance', 'other') NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `reference` VARCHAR(255) NULL,
    `voucher_id` INT UNSIGNED NULL,
    `notes` TEXT,
    `recorded_by` INT UNSIGNED NOT NULL,
    FOREIGN KEY (`invoice_id`) REFERENCES `sales_invoices` (`id`),
    FOREIGN KEY (`recorded_by`) REFERENCES `users` (`id`),
    INDEX `idx_payment_date` (`payment_date`)
);

-- Daily cash reconciliation
CREATE TABLE `cash_reconciliations` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `reconciliation_date` DATE NOT NULL UNIQUE,
    `opening_balance` DECIMAL(10,2) NOT NULL,
    `cash_sales` DECIMAL(10,2) NOT NULL,
    `cash_payments` DECIMAL(10,2) NOT NULL,
    `expected_closing` DECIMAL(10,2) NOT NULL,
    `actual_closing` DECIMAL(10,2) NOT NULL,
    `difference` DECIMAL(10,2) NOT NULL,
    `notes` TEXT,
    `reconciled_by` INT UNSIGNED NOT NULL,
    `reconciled_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`reconciled_by`) REFERENCES `users` (`id`)
);
```

**Tests for Task 4.3.1:**
```php
// Test 1: Multi-payment support
public function testMultiPaymentSupport() {
    $invoice = SalesInvoice::factory()->create(['total_amount' => 100]);
    
    // Pay with multiple methods
    $payment1 = $invoice->addPayment('cash', 50);
    $payment2 = $invoice->addPayment('card', 30);
    $payment3 = $invoice->addPayment('voucher', 20, ['voucher_id' => 1]);
    
    $this->assertEquals(100, $invoice->amount_paid);
    $this->assertEquals('paid', $invoice->payment_status);
}

// Test 2: Partial payment handling
public function testPartialPayment() {
    $invoice = SalesInvoice::factory()->create(['total_amount' => 500]);
    
    $invoice->addPayment('cash', 200);
    
    $this->assertEquals(200, $invoice->amount_paid);
    $this->assertEquals('partial', $invoice->payment_status);
    $this->assertEquals(300, $invoice->getBalance());
}

// Test 3: Daily cash reconciliation
public function testCashReconciliation() {
    // Create cash sales
    SalesPayment::factory()->count(5)->create([
        'payment_method' => 'cash',
        'payment_date' => today(),
        'amount' => 50
    ]);
    
    $reconciliation = CashReconciliation::createForDate(today(), [
        'opening_balance' => 100,
        'actual_closing' => 340
    ]);
    
    $this->assertEquals(250, $reconciliation->cash_sales);
    $this->assertEquals(350, $reconciliation->expected_closing);
    $this->assertEquals(-10, $reconciliation->difference);
}
```

## Module 4.4: Package & Voucher System

### Task 4.4.1: Create Package Tables
```sql
-- Service packages
CREATE TABLE `service_packages` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `total_sessions` INT NOT NULL,
    `valid_days` INT DEFAULT 365,
    `price` DECIMAL(10,2) NOT NULL,
    `services_included` JSON,
    `is_active` BOOLEAN DEFAULT TRUE,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Package purchases
CREATE TABLE `package_purchases` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `package_id` INT UNSIGNED NOT NULL,
    `client_id` INT UNSIGNED NOT NULL,
    `invoice_id` INT UNSIGNED NOT NULL,
    `purchase_date` DATE NOT NULL,
    `expiry_date` DATE NOT NULL,
    `sessions_total` INT NOT NULL,
    `sessions_used` INT DEFAULT 0,
    `status` ENUM('active', 'completed', 'expired', 'cancelled') DEFAULT 'active',
    FOREIGN KEY (`package_id`) REFERENCES `service_packages` (`id`),
    FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`),
    FOREIGN KEY (`invoice_id`) REFERENCES `sales_invoices` (`id`)
);

-- Package usage
CREATE TABLE `package_usage` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `purchase_id` INT UNSIGNED NOT NULL,
    `usage_date` DATETIME NOT NULL,
    `service_id` INT UNSIGNED NOT NULL,
    `therapist_id` INT UNSIGNED NULL,
    `invoice_id` INT UNSIGNED NULL,
    `notes` TEXT,
    FOREIGN KEY (`purchase_id`) REFERENCES `package_purchases` (`id`),
    FOREIGN KEY (`service_id`) REFERENCES `catalog_items` (`id`)
);
```

**Tests for Task 4.4.1:**
```php
// Test 1: Package creation and purchase
public function testPackagePurchase() {
    $package = ServicePackage::create([
        'name' => '10 Massages',
        'total_sessions' => 10,
        'valid_days' => 365,
        'price' => 700,
        'services_included' => json_encode([1, 2]) // Massage service IDs
    ]);
    
    $purchase = $package->purchase(1, 'SALE-2025-00001');
    
    $this->assertEquals(10, $purchase->sessions_total);
    $this->assertEquals(0, $purchase->sessions_used);
    $this->assertEquals('active', $purchase->status);
}

// Test 2: Package usage tracking
public function testPackageUsage() {
    $purchase = PackagePurchase::factory()->create(['sessions_total' => 10]);
    
    $usage = $purchase->useSession(1, 2); // service_id: 1, therapist_id: 2
    
    $purchase->refresh();
    $this->assertEquals(1, $purchase->sessions_used);
    $this->assertEquals(9, $purchase->getRemainingSessions());
}

// Test 3: Package expiry
public function testPackageExpiry() {
    $purchase = PackagePurchase::factory()->create([
        'expiry_date' => now()->subDay()
    ]);
    
    $this->assertTrue($purchase->isExpired());
    $this->assertEquals('expired', $purchase->checkAndUpdateStatus());
}
```

### Task 4.4.2: Create Voucher Tables
```sql
-- Gift vouchers
CREATE TABLE `vouchers` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `voucher_code` VARCHAR(20) NOT NULL UNIQUE,
    `voucher_type` ENUM('amount', 'service') NOT NULL,
    `amount` DECIMAL(10,2) NULL,
    `service_id` INT UNSIGNED NULL,
    `purchaser_id` INT UNSIGNED NOT NULL,
    `recipient_name` VARCHAR(255) NULL,
    `purchase_date` DATE NOT NULL,
    `expiry_date` DATE NOT NULL,
    `original_value` DECIMAL(10,2) NOT NULL,
    `remaining_value` DECIMAL(10,2) NOT NULL,
    `status` ENUM('active', 'used', 'partial', 'expired') DEFAULT 'active',
    `purchase_invoice_id` INT UNSIGNED NOT NULL,
    FOREIGN KEY (`purchaser_id`) REFERENCES `clients` (`id`),
    FOREIGN KEY (`purchase_invoice_id`) REFERENCES `sales_invoices` (`id`),
    INDEX `idx_voucher_code` (`voucher_code`),
    INDEX `idx_expiry` (`expiry_date`)
);

-- Voucher redemptions
CREATE TABLE `voucher_redemptions` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `voucher_id` INT UNSIGNED NOT NULL,
    `redemption_date` DATETIME NOT NULL,
    `amount_used` DECIMAL(10,2) NOT NULL,
    `invoice_id` INT UNSIGNED NOT NULL,
    `redeemed_by` INT UNSIGNED NOT NULL,
    FOREIGN KEY (`voucher_id`) REFERENCES `vouchers` (`id`),
    FOREIGN KEY (`invoice_id`) REFERENCES `sales_invoices` (`id`)
);
```

**Tests for Task 4.4.2:**
```php
// Test 1: Voucher generation
public function testVoucherGeneration() {
    $voucher = VoucherService::generate([
        'type' => 'amount',
        'amount' => 100,
        'purchaser_id' => 1,
        'recipient_name' => 'John Doe'
    ]);
    
    $this->assertStringStartsWith('FIT', $voucher->voucher_code);
    $this->assertEquals(100, $voucher->original_value);
    $this->assertEquals(100, $voucher->remaining_value);
}

// Test 2: Voucher redemption
public function testVoucherRedemption() {
    $voucher = Voucher::factory()->create([
        'original_value' => 100,
        'remaining_value' => 100
    ]);
    
    $redemption = VoucherService::redeem($voucher->voucher_code, 60);
    
    $voucher->refresh();
    $this->assertEquals(40, $voucher->remaining_value);
    $this->assertEquals('partial', $voucher->status);
}

// Test 3: Voucher validation
public function testVoucherValidation() {
    $expiredVoucher = Voucher::factory()->create([
        'expiry_date' => now()->subDay()
    ]);
    
    $this->expectException(VoucherException::class);
    VoucherService::redeem($expiredVoucher->voucher_code, 50);
}
```

## Module 4.5: Bank Reconciliation (FROM OLD PHASE 4)

### Task 4.5.1: Create Bank Import Tables
```sql
-- Bank statement imports
CREATE TABLE `bank_imports` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `filename` VARCHAR(255) NOT NULL,
    `file_type` ENUM('csv', 'pdf', 'mt940', 'xlsx') NOT NULL,
    `import_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `imported_by` INT UNSIGNED NOT NULL,
    `total_transactions` INT DEFAULT 0,
    `matched_transactions` INT DEFAULT 0,
    `status` ENUM('processing', 'ready', 'completed') DEFAULT 'processing',
    FOREIGN KEY (`imported_by`) REFERENCES `users` (`id`)
);

-- Bank transaction lines
CREATE TABLE `bank_import_lines` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `import_id` INT UNSIGNED NOT NULL,
    `transaction_date` DATE NOT NULL,
    `amount` DECIMAL(10,2) NOT NULL,
    `reference` VARCHAR(255),
    `payer_name` VARCHAR(255),
    `payer_account` VARCHAR(50),
    `suggested_invoice_id` INT UNSIGNED NULL,
    `match_confidence` DECIMAL(3,2) DEFAULT 0.00,
    `matched_invoice_id` INT UNSIGNED NULL,
    `matched_by` INT UNSIGNED NULL,
    `matched_at` TIMESTAMP NULL,
    `notes` TEXT,
    FOREIGN KEY (`import_id`) REFERENCES `bank_imports` (`id`),
    FOREIGN KEY (`suggested_invoice_id`) REFERENCES `invoices` (`id`),
    FOREIGN KEY (`matched_invoice_id`) REFERENCES `invoices` (`id`)
);
```

**Tests for Task 4.5.1:**
```php
// Test 1: CSV bank import
public function testBankCSVImport() {
    $file = UploadedFile::fake()->create('bank_statement.csv');
    
    $importer = new BankImporter();
    $result = $importer->importCSV($file);
    
    $this->assertTrue($result['success']);
    $this->assertGreaterThan(0, $result['transactions_imported']);
}

// Test 2: PDF OCR import
public function testBankPDFImport() {
    $file = UploadedFile::fake()->create('bank_statement.pdf');
    
    $importer = new BankImporter();
    $result = $importer->importPDF($file);
    
    $this->assertTrue($result['success']);
    $this->assertArrayHasKey('transactions', $result);
}

// Test 3: Automatic matching
public function testBankTransactionMatching() {
    // Create invoice
    $invoice = Invoice::factory()->create([
        'invoice_number' => '2025-FIT-100',
        'total_amount' => 1500.00,
        'client_id' => 1
    ]);
    
    // Import transaction
    $transaction = BankImportLine::create([
        'import_id' => 1,
        'transaction_date' => now(),
        'amount' => 1500.00,
        'reference' => 'FIT-100',
        'payer_name' => $invoice->client->name
    ]);
    
    $matcher = new BankMatcher();
    $match = $matcher->findMatch($transaction);
    
    $this->assertEquals($invoice->id, $match['invoice_id']);
    $this->assertGreaterThan(0.8, $match['confidence']);
}
```

## Module 4.6: Automated Workflows (FROM OLD PHASE 4)

### Task 4.6.1: Create Workflow Engine Tables
```sql
-- Workflow definitions
CREATE TABLE `workflows` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(255) NOT NULL,
    `trigger_type` ENUM('scheduled', 'event', 'manual') NOT NULL,
    `trigger_config` JSON,
    `conditions` JSON,
    `actions` JSON,
    `is_active` BOOLEAN DEFAULT TRUE,
    `last_run` TIMESTAMP NULL,
    `next_run` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Workflow execution logs
CREATE TABLE `workflow_logs` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `workflow_id` INT UNSIGNED NOT NULL,
    `execution_date` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `status` ENUM('started', 'completed', 'failed') NOT NULL,
    `affected_records` INT DEFAULT 0,
    `error_message` TEXT NULL,
    `execution_details` JSON,
    FOREIGN KEY (`workflow_id`) REFERENCES `workflows` (`id`)
);

-- Payment reminders
CREATE TABLE `payment_reminders` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `invoice_id` INT UNSIGNED NOT NULL,
    `reminder_level` INT DEFAULT 1,
    `scheduled_date` DATE NOT NULL,
    `sent_date` TIMESTAMP NULL,
    `template_id` INT UNSIGNED NOT NULL,
    `status` ENUM('scheduled', 'sent', 'cancelled') DEFAULT 'scheduled',
    FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`),
    FOREIGN KEY (`template_id`) REFERENCES `email_templates` (`id`)
);
```

**Tests for Task 4.6.1:**
```php
// Test 1: Workflow creation
public function testWorkflowCreation() {
    $workflow = Workflow::create([
        'name' => 'Monthly Invoice Generation',
        'trigger_type' => 'scheduled',
        'trigger_config' => json_encode(['cron' => '0 0 1 * *']),
        'actions' => json_encode([
            ['type' => 'generate_recurring_invoices'],
            ['type' => 'send_notification']
        ])
    ]);
    
    $this->assertNotNull($workflow->next_run);
}

// Test 2: Workflow execution
public function testWorkflowExecution() {
    $workflow = Workflow::factory()->create([
        'actions' => json_encode([
            ['type' => 'update_invoice_status', 'params' => ['status' => 'overdue']]
        ])
    ]);
    
    $executor = new WorkflowExecutor();
    $result = $executor->execute($workflow);
    
    $this->assertEquals('completed', $result['status']);
    $this->assertGreaterThan(0, $result['affected_records']);
}

// Test 3: Payment reminder scheduling
public function testPaymentReminderScheduling() {
    $invoice = Invoice::factory()->create([
        'status' => 'sent',
        'due_date' => now()->subDays(5)
    ]);
    
    $reminder = PaymentReminder::scheduleFor($invoice);
    
    $this->assertEquals(1, $reminder->reminder_level);
    $this->assertEquals('scheduled', $reminder->status);
}
```

## Module 4.7: Service Management (FROM OLD PHASE 4)

### Task 4.7.1: Create Service Catalog Tables
```sql
-- Advanced service management
CREATE TABLE `services` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(50) NOT NULL UNIQUE,
    `name` VARCHAR(255) NOT NULL,
    `category_id` INT UNSIGNED NOT NULL,
    `duration_minutes` INT DEFAULT 60,
    `price` DECIMAL(10,2) NOT NULL,
    `vat_rate_id` INT UNSIGNED NOT NULL,
    `requires_therapist` BOOLEAN DEFAULT TRUE,
    `max_concurrent` INT DEFAULT 1,
    `booking_instructions` TEXT,
    `is_active` BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (`category_id`) REFERENCES `service_categories` (`id`),
    FOREIGN KEY (`vat_rate_id`) REFERENCES `vat_rates` (`id`)
);

-- Service categories
CREATE TABLE `service_categories` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT,
    `icon` VARCHAR(50) DEFAULT 'fa-heartbeat',
    `sort_order` INT DEFAULT 0
);

-- Staff service assignments
CREATE TABLE `staff_services` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `staff_id` INT UNSIGNED NOT NULL,
    `service_id` INT UNSIGNED NOT NULL,
    `is_primary` BOOLEAN DEFAULT FALSE,
    `commission_rate` DECIMAL(5,2) DEFAULT 0,
    UNIQUE KEY `staff_service` (`staff_id`, `service_id`),
    FOREIGN KEY (`staff_id`) REFERENCES `users` (`id`),
    FOREIGN KEY (`service_id`) REFERENCES `services` (`id`)
);
```

**Tests for Task 4.7.1:**
```php
// Test 1: Service creation
public function testServiceCreation() {
    $service = Service::create([
        'code' => 'MASS60',
        'name' => 'Massage Thérapeutique 60min',
        'category_id' => 1,
        'duration_minutes' => 60,
        'price' => 80.00,
        'vat_rate_id' => 1
    ]);
    
    $this->assertEquals(60, $service->duration_minutes);
}

// Test 2: Staff assignment
public function testStaffServiceAssignment() {
    $staff = User::factory()->create();
    $service = Service::factory()->create();
    
    $assignment = StaffService::create([
        'staff_id' => $staff->id,
        'service_id' => $service->id,
        'commission_rate' => 30.00
    ]);
    
    $this->assertTrue($staff->canProvide($service));
}
```

## Module 4.8: Analytics and Reporting

### Task 4.8.1: Create Analytics Tables
```sql
-- Sales analytics
CREATE VIEW `daily_sales_summary` AS
SELECT 
    DATE(issue_date) as sale_date,
    COUNT(DISTINCT id) as transaction_count,
    COUNT(DISTINCT client_id) as unique_clients,
    SUM(subtotal) as subtotal,
    SUM(total_vat) as vat_collected,
    SUM(total_amount) as gross_revenue,
    SUM(CASE WHEN payment_status = 'paid' THEN total_amount ELSE 0 END) as collected_revenue
FROM sales_invoices
GROUP BY DATE(issue_date);

-- Practitioner performance
CREATE VIEW `practitioner_performance` AS
SELECT 
    p.id as practitioner_id,
    p.name as practitioner_name,
    COUNT(DISTINCT i.id) as invoice_count,
    SUM(i.total_amount) as total_billed,
    SUM(CASE WHEN i.status = 'paid' THEN i.total_amount ELSE 0 END) as total_collected,
    AVG(DATEDIFF(payment_date, issue_date)) as avg_payment_days
FROM clients p
JOIN invoices i ON p.id = i.client_id
WHERE p.client_type = 'practitioner'
GROUP BY p.id;

-- Product performance
CREATE VIEW `product_performance` AS
SELECT 
    ci.id,
    ci.name,
    ci.category_id,
    COUNT(sil.id) as times_sold,
    SUM(sil.quantity) as total_quantity,
    SUM(sil.line_total) as total_revenue,
    AVG(sil.discount_percent) as avg_discount
FROM catalog_items ci
LEFT JOIN sales_invoice_lines sil ON ci.id = sil.item_id
GROUP BY ci.id;
```

**Tests for Task 4.8.1:**
```php
// Test 1: Daily sales summary
public function testDailySalesSummary() {
    // Create test sales
    SalesInvoice::factory()->count(5)->create([
        'issue_date' => today(),
        'total_amount' => 100
    ]);
    
    $summary = DB::table('daily_sales_summary')
        ->whereDate('sale_date', today())
        ->first();
    
    $this->assertEquals(5, $summary->transaction_count);
    $this->assertEquals(500, $summary->gross_revenue);
}

// Test 2: Practitioner performance metrics
public function testPractitionerPerformance() {
    $practitioner = Client::factory()->practitioner()->create();
    
    Invoice::factory()->count(10)->create([
        'client_id' => $practitioner->id,
        'total_amount' => 1000,
        'status' => 'paid'
    ]);
    
    $performance = DB::table('practitioner_performance')
        ->where('practitioner_id', $practitioner->id)
        ->first();
    
    $this->assertEquals(10, $performance->invoice_count);
    $this->assertEquals(10000, $performance->total_collected);
}
```

## Module 4.9: Practitioner Portal

### Task 4.9.1: Create Portal Tables
```sql
-- Practitioner portal access
CREATE TABLE `practitioner_portal_access` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `practitioner_id` INT UNSIGNED NOT NULL UNIQUE,
    `email` VARCHAR(255) NOT NULL,
    `password_hash` VARCHAR(255) NOT NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `last_login` TIMESTAMP NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (`practitioner_id`) REFERENCES `clients` (`id`)
);

-- Portal queries
CREATE TABLE `portal_queries` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `practitioner_id` INT UNSIGNED NOT NULL,
    `invoice_id` INT UNSIGNED NOT NULL,
    `query_type` ENUM('dispute', 'question', 'clarification', 'document_request') NOT NULL,
    `message` TEXT NOT NULL,
    `status` ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `resolved_at` TIMESTAMP NULL,
    `resolved_by` INT UNSIGNED NULL,
    FOREIGN KEY (`practitioner_id`) REFERENCES `clients` (`id`),
    FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`)
);

-- Portal documents
CREATE TABLE `portal_documents` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `practitioner_id` INT UNSIGNED NOT NULL,
    `document_type` ENUM('cns_statement', 'payment_proof', 'receipt', 'other') NOT NULL,
    `filename` VARCHAR(255) NOT NULL,
    `file_path` VARCHAR(500) NOT NULL,
    `file_size` INT UNSIGNED NOT NULL,
    `period_month` INT NULL,
    `period_year` INT NULL,
    `uploaded_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `verified` BOOLEAN DEFAULT FALSE,
    `verified_by` INT UNSIGNED NULL,
    FOREIGN KEY (`practitioner_id`) REFERENCES `clients` (`id`)
);
```

**Tests for Task 4.9.1:**
```php
// Test 1: Portal access creation
public function testPortalAccessCreation() {
    $practitioner = Client::factory()->practitioner()->create();
    
    $access = PractitionerPortalAccess::createFor($practitioner, [
        'email' => $practitioner->email,
        'password' => 'secure123'
    ]);
    
    $this->assertTrue(Hash::check('secure123', $access->password_hash));
}

// Test 2: Portal document upload
public function testPortalDocumentUpload() {
    $practitioner = Client::factory()->practitioner()->create();
    $file = UploadedFile::fake()->create('cns_statement.pdf');
    
    $document = PortalDocument::upload($file, [
        'practitioner_id' => $practitioner->id,
        'document_type' => 'cns_statement',
        'period_month' => 3,
        'period_year' => 2025
    ]);
    
    $this->assertDatabaseHas('portal_documents', [
        'practitioner_id' => $practitioner->id,
        'document_type' => 'cns_statement'
    ]);
}

// Test 3: Query creation
public function testPortalQueryCreation() {
    $query = PortalQuery::create([
        'practitioner_id' => 1,
        'invoice_id' => 1,
        'query_type' => 'question',
        'message' => 'Why is the secretariat fee higher this month?'
    ]);
    
    $this->assertEquals('open', $query->status);
    
    // Test notification
    $this->assertDatabaseHas('notifications', [
        'type' => 'new_portal_query'
    ]);
}
```

## Summary: Complete Implementation Checklist

### Phase 3: Practitioner Billing System
- [x] Core invoice tables with all enhancements
- [x] Configurable invoice types (from old Phase 3)
- [x] VAT management with temporal validity (from old Phase 3)
- [x] Flexible rate profiles with tiers
- [x] Template system with inheritance
- [x] Email templates with conditions
- [x] Retrocession calculations
- [x] CNS import with OCR
- [x] Monthly billing wizard
- [x] Document management (from old Phase 4)
- [x] Bank reconciliation (from old Phase 4)
- [x] Automated workflows (from old Phase 4)
- [x] Payment reminders (from old Phase 4)
- [x] Service management (from old Phase 4)

### Phase 4: Retail Sales System
- [x] Separate sales invoice system
- [x] Product/service catalog
- [x] Stock management
- [x] Multi-payment support
- [x] Package system
- [x] Voucher system
- [x] Quick POS interface
- [x] Analytics dashboards
- [x] Practitioner portal

### Critical Features Maintained
- [x] Mandatory "TVA sur frais de secrétariat" display
- [x] VAT calculation formula: Amount - (Amount / 1.17)
- [x] Draft period functionality
- [x] Cascade updates
- [x] Smart email templates
- [x] CNS integration
- [x] All 23 configuration areas supported

This comprehensive implementation includes EVERYTHING from both your old and new specifications, with detailed tests for each component.