{% extends "base-modern.twig" %}

{% block title %}{{ __('products.create') | default('Add Product') }}{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products">{{ __('products.title') | default('Products') }}</a></li>
    <li class="breadcrumb-item active">{{ __('products.create') | default('Add Product') }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow-sm">
            <form id="productForm" method="POST" action="{{ base_url }}/products">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('products.create') | default('Add New Product') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="mb-3">{{ __('products.basic_info') | default('Basic Information') }}</h6>
                            
                            <div class="mb-3">
                                <label for="code" class="form-label">
                                    {{ __('products.code') | default('Product Code') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="code" name="code" value="{{ defaultCode }}" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    {{ __('products.name') | default('Product Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category_id" class="form-label">
                                    {{ __('products.category') | default('Category') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">{{ __('common.select') | default('Select...') }}</option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}">{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="item_type" class="form-label">
                                    {{ __('products.type') | default('Item Type') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="item_type" name="item_type" required>
                                    <option value="">{{ __('common.select') | default('Select...') }}</option>
                                    <option value="product">{{ __('products.type_product') | default('Product') }}</option>
                                    <option value="service">{{ __('products.type_service') | default('Service') }}</option>
                                    <option value="package">{{ __('products.type_package') | default('Package') }}</option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    {{ __('products.description') | default('Description') }}
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                            </div>
                        </div>
                        
                        <!-- Pricing & Stock -->
                        <div class="col-md-6">
                            <h6 class="mb-3">{{ __('products.pricing_stock') | default('Pricing & Stock') }}</h6>
                            
                            <div class="mb-3">
                                <label for="unit_price" class="form-label">
                                    {{ __('products.unit_price') | default('Unit Price') }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                           step="0.01" min="0" required>
                                    <span class="input-group-text">€</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="vat_rate_id" class="form-label">
                                    {{ __('products.vat_rate') | default('VAT Rate') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="vat_rate_id" name="vat_rate_id" required>
                                    <option value="">{{ __('common.select') | default('Select...') }}</option>
                                    {% for vat in vatRates %}
                                        <option value="{{ vat.id }}">{{ vat.name }} ({{ vat.rate }} %)</option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_stockable" name="is_stockable" value="1">
                                    <label class="form-check-label" for="is_stockable">
                                        {{ __('products.is_stockable') | default('Track stock for this item') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div id="stockFields" style="display: none;">
                                <div class="mb-3">
                                    <label for="current_stock" class="form-label">
                                        {{ __('products.initial_stock') | default('Initial Stock') }}
                                    </label>
                                    <input type="number" class="form-control" id="current_stock" name="current_stock" 
                                           min="0" value="0">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="low_stock_alert" class="form-label">
                                        {{ __('products.low_stock_alert') | default('Low Stock Alert Level') }}
                                    </label>
                                    <input type="number" class="form-control" id="low_stock_alert" name="low_stock_alert" 
                                           min="0" value="5">
                                    <small class="text-muted">
                                        {{ __('products.low_stock_help') | default('Alert when stock falls below this level') }}
                                    </small>
                                </div>
                            </div>
                            
                            <h6 class="mb-3 mt-4">{{ __('products.quick_sale') | default('Quick Sale Settings') }}</h6>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="quick_sale_button" 
                                           name="quick_sale_button" value="1">
                                    <label class="form-check-label" for="quick_sale_button">
                                        {{ __('products.enable_quick_sale') | default('Show in quick sale buttons') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div id="quickSaleFields" style="display: none;">
                                <div class="mb-3">
                                    <label for="button_color" class="form-label">
                                        {{ __('products.button_color') | default('Button Color') }}
                                    </label>
                                    <input type="color" class="form-control form-control-color" id="button_color" 
                                           name="button_color" value="#007bff">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="button_order" class="form-label">
                                        {{ __('products.button_order') | default('Button Order') }}
                                    </label>
                                    <input type="number" class="form-control" id="button_order" name="button_order" 
                                           min="0" value="0">
                                    <small class="text-muted">
                                        {{ __('products.button_order_help') | default('Lower numbers appear first') }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" checked>
                                    <label class="form-check-label" for="is_active">
                                        {{ __('products.is_active') | default('Active') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {{ __('common.save') | default('Save') }}
                    </button>
                    <a href="{{ base_url }}/products" class="btn btn-secondary">
                        <i class="fas fa-times"></i> {{ __('common.cancel') | default('Cancel') }}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle stock fields
document.getElementById('is_stockable').addEventListener('change', function() {
    document.getElementById('stockFields').style.display = this.checked ? 'block' : 'none';
});

// Toggle quick sale fields
document.getElementById('quick_sale_button').addEventListener('change', function() {
    document.getElementById('quickSaleFields').style.display = this.checked ? 'block' : 'none';
});

// Form submission
document.getElementById('productForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Clear previous errors
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    
    // Submit form
    fetch(this.action, {
        method: 'POST',
        body: new FormData(this),
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect;
        } else if (data.errors) {
            // Show validation errors
            for (let field in data.errors) {
                let input = document.getElementById(field);
                if (input) {
                    input.classList.add('is-invalid');
                    let feedback = input.nextElementSibling;
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.textContent = data.errors[field];
                    }
                }
            }
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
});
</script>
{% endblock %}