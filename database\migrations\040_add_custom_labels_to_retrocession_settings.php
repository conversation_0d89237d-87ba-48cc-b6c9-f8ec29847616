<?php
// Migration to add custom label fields to user_retrocession_settings table

$sql = "
-- Add custom label columns to user_retrocession_settings table
ALTER TABLE user_retrocession_settings
    ADD COLUMN cns_label VARCHAR(255) DEFAULT 'RÉTROCESSION CNS' AFTER cns_value,
    ADD COLUMN patient_label VARCHAR(255) DEFAULT 'RÉTROCESSION PATIENTS' AFTER patient_value,
    ADD COLUMN secretary_label VARCHAR(255) DEFAULT 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL' AFTER secretary_value;

-- Update existing records with default values
UPDATE user_retrocession_settings 
SET cns_label = 'RÉTROCESSION CNS',
    patient_label = 'RÉTROCESSION PATIENTS',
    secretary_label = 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL'
WHERE cns_label IS NULL OR patient_label IS NULL OR secretary_label IS NULL;

-- Special update for <PERSON><PERSON><PERSON> He<PERSON> if needed
UPDATE user_retrocession_settings urs
JOIN users u ON urs.user_id = u.id
SET urs.cns_label = 'RÉTROCESSION'
WHERE u.first_name = '<PERSON><PERSON><PERSON>' AND u.last_name = 'Heine';
";

echo "Migration 040: Adding custom labels to retrocession settings\n";
echo $sql;
?>