{% extends "base-modern.twig" %}

{% block title %}{{ __('config.system_settings') }}{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('common.home') }}</a></li>
<li class="breadcrumb-item"><a href="{{ base_url }}/config">{{ __('config.configuration') }}</a></li>
<li class="breadcrumb-item active">{{ __('config.system_settings') }}</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.system_settings') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="submit" form="systemSettingsForm" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.system_settings_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <div class="row">
        <div class="col-md-12">
            <form id="systemSettingsForm">
                {{ csrf_field() }}
            
            <!-- General Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-gear me-2"></i>{{ __('config.general_settings') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="app_name" name="app_name" 
                                       value="{{ settings.app_name.value|default('Fit360 AdminDesk') }}" 
                                       placeholder="{{ __('config.app_name') }}" required>
                                <label for="app_name">{{ __('config.app_name') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="url" class="form-control" id="app_url" name="app_url" 
                                       value="{{ settings.app_url.value|default('http://localhost') }}" 
                                       placeholder="https://yourdomain.com" required>
                                <label for="app_url">{{ __('config.app_url') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                    </div>

                    <div class="row g-3 mt-3">
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="app_timezone" name="app_timezone" required>
                                    <option value="UTC" {% if settings.app_timezone.value|default('UTC') == 'UTC' %}selected{% endif %}>UTC</option>
                                    <option value="Europe/Luxembourg" {% if settings.app_timezone.value == 'Europe/Luxembourg' %}selected{% endif %}>Europe/Luxembourg</option>
                                    <option value="Europe/Paris" {% if settings.app_timezone.value == 'Europe/Paris' %}selected{% endif %}>Europe/Paris</option>
                                    <option value="Europe/London" {% if settings.app_timezone.value == 'Europe/London' %}selected{% endif %}>Europe/London</option>
                                    <option value="America/New_York" {% if settings.app_timezone.value == 'America/New_York' %}selected{% endif %}>America/New_York</option>
                                    <option value="America/Chicago" {% if settings.app_timezone.value == 'America/Chicago' %}selected{% endif %}>America/Chicago</option>
                                    <option value="America/Los_Angeles" {% if settings.app_timezone.value == 'America/Los_Angeles' %}selected{% endif %}>America/Los_Angeles</option>
                                    <option value="Asia/Dubai" {% if settings.app_timezone.value == 'Asia/Dubai' %}selected{% endif %}>Asia/Dubai</option>
                                    <option value="Asia/Singapore" {% if settings.app_timezone.value == 'Asia/Singapore' %}selected{% endif %}>Asia/Singapore</option>
                                    <option value="Asia/Tokyo" {% if settings.app_timezone.value == 'Asia/Tokyo' %}selected{% endif %}>Asia/Tokyo</option>
                                    <option value="Australia/Sydney" {% if settings.app_timezone.value == 'Australia/Sydney' %}selected{% endif %}>Australia/Sydney</option>
                                </select>
                                <label for="app_timezone">{{ __('config.timezone') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="app_locale" name="app_locale" required>
                                    <option value="en" {% if settings.app_locale.value|default('en') == 'en' %}selected{% endif %}>English</option>
                                    <option value="fr" {% if settings.app_locale.value == 'fr' %}selected{% endif %}>Français</option>
                                    <option value="es" {% if settings.app_locale.value == 'es' %}selected{% endif %}>Español</option>
                                    <option value="de" {% if settings.app_locale.value == 'de' %}selected{% endif %}>Deutsch</option>
                                    <option value="it" {% if settings.app_locale.value == 'it' %}selected{% endif %}>Italiano</option>
                                </select>
                                <label for="app_locale">{{ __('config.language') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="app_currency" name="app_currency" required>
                                    <option value="USD" {% if settings.app_currency.value|default('USD') == 'USD' %}selected{% endif %}>USD - US Dollar</option>
                                    <option value="EUR" {% if settings.app_currency.value == 'EUR' %}selected{% endif %}>EUR - Euro</option>
                                    <option value="GBP" {% if settings.app_currency.value == 'GBP' %}selected{% endif %}>GBP - British Pound</option>
                                    <option value="CAD" {% if settings.app_currency.value == 'CAD' %}selected{% endif %}>CAD - Canadian Dollar</option>
                                    <option value="AUD" {% if settings.app_currency.value == 'AUD' %}selected{% endif %}>AUD - Australian Dollar</option>
                                    <option value="JPY" {% if settings.app_currency.value == 'JPY' %}selected{% endif %}>JPY - Japanese Yen</option>
                                    <option value="CHF" {% if settings.app_currency.value == 'CHF' %}selected{% endif %}>CHF - Swiss Franc</option>
                                </select>
                                <label for="app_currency">{{ __('config.currency') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Format Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-calendar3 me-2"></i>{{ __('config.format_settings') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="date_format" name="date_format" required>
                                    <option value="Y-m-d" {% if settings.date_format.value|default('Y-m-d') == 'Y-m-d' %}selected{% endif %}>{{ "now"|date("Y-m-d") }}</option>
                                    <option value="d/m/Y" {% if settings.date_format.value == 'd/m/Y' %}selected{% endif %}>{{ "now"|date("d/m/Y") }}</option>
                                    <option value="m/d/Y" {% if settings.date_format.value == 'm/d/Y' %}selected{% endif %}>{{ "now"|date("m/d/Y") }}</option>
                                    <option value="d.m.Y" {% if settings.date_format.value == 'd.m.Y' %}selected{% endif %}>{{ "now"|date("d.m.Y") }}</option>
                                    <option value="d-m-Y" {% if settings.date_format.value == 'd-m-Y' %}selected{% endif %}>{{ "now"|date("d-m-Y") }}</option>
                                </select>
                                <label for="date_format">{{ __('config.date_format') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="date_format_short" name="date_format_short" required>
                                    <option value="Y-m-d" {% if settings.date_format_short.value|default('d/m') == 'Y-m-d' %}selected{% endif %}>{{ "now"|date("Y-m-d") }}</option>
                                    <option value="d/m" {% if settings.date_format_short.value|default('d/m') == 'd/m' %}selected{% endif %}>{{ "now"|date("d/m") }}</option>
                                    <option value="m/d" {% if settings.date_format_short.value == 'm/d' %}selected{% endif %}>{{ "now"|date("m/d") }}</option>
                                    <option value="d.m" {% if settings.date_format_short.value == 'd.m' %}selected{% endif %}>{{ "now"|date("d.m") }}</option>
                                    <option value="d-m" {% if settings.date_format_short.value == 'd-m' %}selected{% endif %}>{{ "now"|date("d-m") }}</option>
                                    <option value="j M" {% if settings.date_format_short.value == 'j M' %}selected{% endif %}>{{ "now"|date("j M") }}</option>
                                    <option value="M j" {% if settings.date_format_short.value == 'M j' %}selected{% endif %}>{{ "now"|date("M j") }}</option>
                                </select>
                                <label for="date_format_short">{{ __('config.date_format_short') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="time_format" name="time_format" required>
                                    <option value="H:i" {% if settings.time_format.value|default('H:i') == 'H:i' %}selected{% endif %}>24-hour (14:30)</option>
                                    <option value="h:i A" {% if settings.time_format.value == 'h:i A' %}selected{% endif %}>12-hour (02:30 PM)</option>
                                </select>
                                <label for="time_format">{{ __('config.time_format') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-earmark-text me-2"></i>{{ __('config.invoice_settings') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="invoice_number_format" 
                                       name="invoice_number_format" 
                                       value="{{ settings.invoice_number_format.value|default('INV-{YEAR}-{NUMBER}') }}" 
                                       placeholder="INV-{YEAR}-{NUMBER}" required>
                                <label for="invoice_number_format">{{ __('config.invoice_number_format') }} <span class="text-danger">*</span></label>
                                <div class="form-text">
                                    {{ __('config.available_variables') }}: {YEAR}, {MONTH}, {DAY}, {NUMBER}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="invoice_start_number" 
                                       name="invoice_start_number" min="1"
                                       value="{{ settings.invoice_start_number.value|default('1') }}" 
                                       placeholder="1" required>
                                <label for="invoice_start_number">{{ __('config.invoice_start_number') }} <span class="text-danger">*</span></label>
                                <div class="form-text">
                                    {{ __('config.next_invoice_number_hint') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Interface -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-palette me-2"></i>{{ __('config.user_interface') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="app_template" name="app_template" required>
                                    <option value="modern" selected>Modern Theme (Bootstrap 5.3)</option>
                                </select>
                                <label for="app_template">{{ __('config.admin_template') }} <span class="text-danger">*</span></label>
                                <div class="form-text">
                                    {{ __('config.modern_theme_only') | default('Only the Modern theme is currently available') }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PDF Settings -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-file-pdf me-2"></i>{{ __('config.pdf_settings') | default('PDF Settings') }}
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        <i class="bi bi-info-circle me-1"></i>
                        {{ __('config.pdf_settings_desc') | default('Configure PDF document generation settings. Minimum recommended margins: 10-15mm for most printers.') }}
                    </p>
                    
                    <!-- Margin Settings -->
                    <h6 class="mb-3">{{ __('config.pdf_margins') | default('Page Margins (in mm)') }}</h6>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="pdf_margin_top" 
                                       name="pdf_margin_top" 
                                       value="{{ settings.pdf_margin_top.value|default('20') }}"
                                       min="5" max="50" step="1" required>
                                <label for="pdf_margin_top">{{ __('config.margin_top') | default('Top Margin') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="pdf_margin_bottom" 
                                       name="pdf_margin_bottom" 
                                       value="{{ settings.pdf_margin_bottom.value|default('30') }}"
                                       min="5" max="50" step="1" required>
                                <label for="pdf_margin_bottom">{{ __('config.margin_bottom') | default('Bottom Margin') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="pdf_margin_left" 
                                       name="pdf_margin_left" 
                                       value="{{ settings.pdf_margin_left.value|default('20') }}"
                                       min="5" max="50" step="1" required>
                                <label for="pdf_margin_left">{{ __('config.margin_left') | default('Left Margin') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="pdf_margin_right" 
                                       name="pdf_margin_right" 
                                       value="{{ settings.pdf_margin_right.value|default('20') }}"
                                       min="5" max="50" step="1" required>
                                <label for="pdf_margin_right">{{ __('config.margin_right') | default('Right Margin') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Logo and Layout Settings -->
                    <h6 class="mb-3 mt-4">{{ __('config.pdf_layout') | default('Layout Settings') }}</h6>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="pdf_logo_size" 
                                       name="pdf_logo_size" 
                                       value="{{ settings.pdf_logo_size.value|default('100') }}"
                                       min="10" max="200" step="5" required>
                                <label for="pdf_logo_size">{{ __('config.logo_size') | default('Logo Size (%)') }} <span class="text-danger">*</span></label>
                                <div class="form-text">{{ __('config.logo_size_desc') | default('Percentage of original logo size (100% = original size)') }}</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="pdf_page_format" name="pdf_page_format" required>
                                    <option value="A4" {% if settings.pdf_page_format.value|default('A4') == 'A4' %}selected{% endif %}>A4 (210 × 297 mm)</option>
                                    <option value="Letter" {% if settings.pdf_page_format.value == 'Letter' %}selected{% endif %}>Letter (8.5 × 11 in)</option>
                                    <option value="Legal" {% if settings.pdf_page_format.value == 'Legal' %}selected{% endif %}>Legal (8.5 × 14 in)</option>
                                </select>
                                <label for="pdf_page_format">{{ __('config.page_format') | default('Page Format') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-floating">
                                <select class="form-select" id="pdf_page_orientation" name="pdf_page_orientation" required>
                                    <option value="P" {% if settings.pdf_page_orientation.value|default('P') == 'P' %}selected{% endif %}>{{ __('config.portrait') | default('Portrait') }}</option>
                                    <option value="L" {% if settings.pdf_page_orientation.value == 'L' %}selected{% endif %}>{{ __('config.landscape') | default('Landscape') }}</option>
                                </select>
                                <label for="pdf_page_orientation">{{ __('config.page_orientation') | default('Page Orientation') }} <span class="text-danger">*</span></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Retrocession Settings -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="bi bi-calculator me-2"></i>{{ __('config.retrocession_settings') }}
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        <i class="bi bi-info-circle me-1"></i>
                        {{ __('config.retrocession_settings_desc') }}
                    </p>
                    
                    <div class="row g-3">
                        <!-- Default Percentages -->
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="retrocession_default_cns_percent" 
                                       name="retrocession_default_cns_percent" 
                                       value="{{ settings.retrocession_default_cns_percent.value|default('20.00') }}"
                                       min="0" max="100" step="0.01" required>
                                <label for="retrocession_default_cns_percent">{{ __('config.default_cns_percent') }} <span class="text-danger">*</span></label>
                                <div class="form-text">{{ __('config.default_cns_percent_desc') }}</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="retrocession_default_patient_percent" 
                                       name="retrocession_default_patient_percent" 
                                       value="{{ settings.retrocession_default_patient_percent.value|default('20.00') }}"
                                       min="0" max="100" step="0.01" required>
                                <label for="retrocession_default_patient_percent">{{ __('config.default_patient_percent') }} <span class="text-danger">*</span></label>
                                <div class="form-text">{{ __('config.default_patient_percent_desc') }}</div>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="retrocession_default_secretary_percent" 
                                       name="retrocession_default_secretary_percent" 
                                       value="{{ settings.retrocession_default_secretary_percent.value|default('10.00') }}"
                                       min="0" max="100" step="0.01" required>
                                <label for="retrocession_default_secretary_percent">{{ __('config.default_secretary_percent') }} <span class="text-danger">*</span></label>
                                <div class="form-text">{{ __('config.default_secretary_percent_desc') }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Ceiling Settings -->
                    <div class="row g-3 mt-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch mb-3">
                                <input type="checkbox" class="form-check-input" id="retrocession_default_ceiling_enabled" 
                                       name="retrocession_default_ceiling_enabled" value="1"
                                       {% if settings.retrocession_default_ceiling_enabled.value == '1' %}checked{% endif %}
                                       onchange="toggleCeilingAmount()">
                                <label class="form-check-label" for="retrocession_default_ceiling_enabled">
                                    {{ __('config.enable_invoice_ceiling') }}
                                </label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <input type="number" class="form-control" id="retrocession_default_ceiling_amount" 
                                       name="retrocession_default_ceiling_amount" 
                                       value="{{ settings.retrocession_default_ceiling_amount.value|default('5000.00') }}"
                                       min="0" step="0.01"
                                       {% if settings.retrocession_default_ceiling_enabled.value != '1' %}disabled{% endif %}>
                                <label for="retrocession_default_ceiling_amount">{{ __('config.ceiling_amount') }} ({{ currency }})</label>
                                <div class="form-text">{{ __('config.ceiling_amount_desc') }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Calculation Options -->
                    <div class="row g-3 mt-3">
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="retrocession_calculation_order" name="retrocession_calculation_order">
                                    <option value="proportional" {% if settings.retrocession_calculation_order.value|default('proportional') == 'proportional' %}selected{% endif %}>
                                        {{ __('config.calculation_proportional') }}
                                    </option>
                                    <option value="cns_first" {% if settings.retrocession_calculation_order.value == 'cns_first' %}selected{% endif %}>
                                        {{ __('config.calculation_cns_first') }}
                                    </option>
                                    <option value="patient_first" {% if settings.retrocession_calculation_order.value == 'patient_first' %}selected{% endif %}>
                                        {{ __('config.calculation_patient_first') }}
                                    </option>
                                </select>
                                <label for="retrocession_calculation_order">{{ __('config.calculation_order') }}</label>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-floating">
                                <select class="form-select" id="retrocession_round_to" name="retrocession_round_to">
                                    <option value="0.01" {% if settings.retrocession_round_to.value == '0.01' %}selected{% endif %}>0.01</option>
                                    <option value="0.05" {% if settings.retrocession_round_to.value|default('0.05') == '0.05' %}selected{% endif %}>0.05</option>
                                    <option value="0.10" {% if settings.retrocession_round_to.value == '0.10' %}selected{% endif %}>0.10</option>
                                    <option value="0.50" {% if settings.retrocession_round_to.value == '0.50' %}selected{% endif %}>0.50</option>
                                    <option value="1.00" {% if settings.retrocession_round_to.value == '1.00' %}selected{% endif %}>1.00</option>
                                </select>
                                <label for="retrocession_round_to">{{ __('config.rounding_precision') }}</label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-lightbulb me-2"></i>
                        {{ __('config.retrocession_user_override_info') }}
                    </div>
                </div>
            </div>

            <!-- System Mode -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-shield-check me-2"></i>{{ __('config.system_mode') }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="form-check form-switch">
                        <input type="checkbox" class="form-check-input" id="maintenance_mode" 
                               name="maintenance_mode" value="1"
                               {% if settings.maintenance_mode.value == '1' %}checked{% endif %}>
                        <label class="form-check-label" for="maintenance_mode">
                            {{ __('config.maintenance_mode') }}
                        </label>
                    </div>
                    <div class="form-text">
                        {{ __('config.maintenance_mode_desc') }}
                    </div>
                </div>
            </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Toggle ceiling amount field based on checkbox
function toggleCeilingAmount() {
    const enabled = document.getElementById('retrocession_default_ceiling_enabled').checked;
    const amountField = document.getElementById('retrocession_default_ceiling_amount');
    amountField.disabled = !enabled;
    if (!enabled) {
        amountField.value = amountField.defaultValue || '5000.00';
    }
}

document.getElementById('systemSettingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Show loading on all submit buttons
    const submitBtns = document.querySelectorAll('button[type="submit"][form="systemSettingsForm"], #systemSettingsForm button[type="submit"]');
    const originalTexts = [];
    submitBtns.forEach((btn, index) => {
        originalTexts[index] = btn.innerHTML;
        btn.disabled = true;
        btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status"></span>{{ __("common.saving") }}...';
    });
    
    const formData = new FormData(this);
    formData.append('_method', 'PUT');
    
    fetch('{{ base_url }}/config/system', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Success',
                text: data.message,
                showConfirmButton: false,
                timer: 1500
            }).then(() => {
                // Simply reload the page
                window.location.reload();
            });
        } else {
            Swal.fire('Error', data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        Swal.fire('Error', 'An error occurred while saving settings', 'error');
    })
    .finally(() => {
        submitBtns.forEach((btn, index) => {
            btn.disabled = false;
            btn.innerHTML = originalTexts[index];
        });
    });
});

// Template preview functionality removed - only Modern theme available
</script>
{% endblock %}