/**
 * Mobile-Optimized Permissions Manager
 * Enhanced for touch devices and responsive layouts
 */
class MobilePermissionsManager {
    constructor(options = {}) {
        this.options = {
            apiBaseUrl: '/admin/permissions',
            csrfToken: '',
            autoSave: false,
            mobileOptimized: true,
            touchThreshold: 50,
            ...options
        };
        
        this.currentGroupId = null;
        this.isDirty = false;
        this.touchStartX = 0;
        this.touchStartY = 0;
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupMobileEnhancements();
        this.initializeSwipeGestures();
        
        // Check if we're on mobile
        this.isMobile = window.innerWidth < 992;
        
        if (this.isMobile) {
            this.renderMobileView();
        }
    }
    
    setupEventListeners() {
        // Form submission
        const form = document.getElementById('permissionsForm');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
            form.addEventListener('change', () => this.markDirty());
        }
        
        // Permission toggles
        document.querySelectorAll('.permission-toggle').forEach(toggle => {
            toggle.addEventListener('change', (e) => this.togglePermissionColumn(e));
        });
        
        // Search functionality
        const searchInput = document.getElementById('moduleSearch');
        if (searchInput) {
            searchInput.addEventListener('input', () => this.filterModules());
        }
        
        // Window resize
        window.addEventListener('resize', () => this.handleResize());
    }
    
    setupMobileEnhancements() {
        if (!this.isMobile) return;
        
        // Enhance touch targets
        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            const wrapper = document.createElement('label');
            wrapper.classList.add('touch-wrapper');
            checkbox.parentNode.insertBefore(wrapper, checkbox);
            wrapper.appendChild(checkbox);
        });
        
        // Add haptic feedback for supported devices
        if ('vibrate' in navigator) {
            document.querySelectorAll('.form-check-input').forEach(input => {
                input.addEventListener('change', () => {
                    navigator.vibrate(10);
                });
            });
        }
    }
    
    initializeSwipeGestures() {
        let touchStartX = 0;
        let touchEndX = 0;
        
        document.addEventListener('touchstart', (e) => {
            touchStartX = e.changedTouches[0].screenX;
        }, { passive: true });
        
        document.addEventListener('touchend', (e) => {
            touchEndX = e.changedTouches[0].screenX;
            this.handleSwipe(touchStartX, touchEndX);
        }, { passive: true });
    }
    
    handleSwipe(startX, endX) {
        const diff = startX - endX;
        const threshold = this.options.touchThreshold;
        
        if (Math.abs(diff) > threshold) {
            if (diff > 0) {
                // Swipe left - next group
                this.navigateToNextGroup();
            } else {
                // Swipe right - previous group
                this.navigateToPreviousGroup();
            }
        }
    }
    
    renderMobileView() {
        const container = document.getElementById('permissionsBody');
        if (!container) return;
        
        // Clear existing content
        container.innerHTML = '';
        
        // Create mobile-optimized accordion
        const accordion = document.createElement('div');
        accordion.className = 'accordion';
        accordion.id = 'permissionsAccordion';
        
        this.options.modules.forEach((module, index) => {
            const accordionItem = this.createMobileModuleItem(module, index);
            accordion.appendChild(accordionItem);
        });
        
        // Replace table with accordion on mobile
        const tableContainer = document.querySelector('.table-responsive');
        if (tableContainer) {
            tableContainer.innerHTML = '';
            tableContainer.appendChild(accordion);
        }
    }
    
    createMobileModuleItem(module, index) {
        const item = document.createElement('div');
        item.className = 'accordion-item module-item';
        item.dataset.module = module.code;
        
        // Header
        const header = document.createElement('h2');
        header.className = 'accordion-header';
        header.id = `heading${index}`;
        
        const button = document.createElement('button');
        button.className = 'accordion-button collapsed';
        button.type = 'button';
        button.setAttribute('data-bs-toggle', 'collapse');
        button.setAttribute('data-bs-target', `#collapse${index}`);
        button.setAttribute('aria-expanded', 'false');
        button.setAttribute('aria-controls', `collapse${index}`);
        
        button.innerHTML = `
            <div class="d-flex align-items-center w-100">
                <i class="fas ${module.icon} me-2"></i>
                <span class="fw-semibold">${module.name}</span>
                ${module.description ? `<small class="text-muted ms-2">${module.description}</small>` : ''}
            </div>
        `;
        
        header.appendChild(button);
        item.appendChild(header);
        
        // Body
        const collapse = document.createElement('div');
        collapse.id = `collapse${index}`;
        collapse.className = 'accordion-collapse collapse';
        collapse.setAttribute('aria-labelledby', `heading${index}`);
        collapse.setAttribute('data-bs-parent', '#permissionsAccordion');
        
        const body = document.createElement('div');
        body.className = 'accordion-body';
        
        // Permission grid
        const grid = document.createElement('div');
        grid.className = 'permission-grid';
        
        module.permissions.forEach(perm => {
            const permItem = this.createPermissionItem(module.code, perm);
            grid.appendChild(permItem);
        });
        
        body.appendChild(grid);
        
        // Special permissions
        if (module.special && module.special.length > 0) {
            const specialSection = this.createSpecialPermissionsSection(module.code, module.special);
            body.appendChild(specialSection);
        }
        
        // Submodules
        if (module.submodules) {
            const submoduleSection = this.createSubmodulesSection(module.submodules);
            body.appendChild(submoduleSection);
        }
        
        collapse.appendChild(body);
        item.appendChild(collapse);
        
        return item;
    }
    
    createPermissionItem(moduleCode, permission) {
        const item = document.createElement('div');
        item.className = 'permission-item';
        
        const label = document.createElement('label');
        label.className = 'permission-label';
        label.setAttribute('for', `perm_${moduleCode}_${permission}`);
        
        // Create switch wrapper
        const switchWrapper = document.createElement('span');
        switchWrapper.className = 'permission-switch';
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = `permissions[${moduleCode}][${permission}]`;
        checkbox.id = `perm_${moduleCode}_${permission}`;
        
        const slider = document.createElement('span');
        slider.className = 'slider';
        
        switchWrapper.appendChild(checkbox);
        switchWrapper.appendChild(slider);
        
        const icon = this.getPermissionIcon(permission);
        const text = this.getPermissionText(permission);
        
        label.appendChild(switchWrapper);
        label.innerHTML += `
            <i class="fas ${icon}"></i>
            <span>${text}</span>
        `;
        
        item.appendChild(label);
        return item;
    }
    
    createSpecialPermissionsSection(moduleCode, specialPerms) {
        const section = document.createElement('div');
        section.className = 'special-permissions mt-3';
        
        const title = document.createElement('h6');
        title.className = 'text-muted mb-2';
        title.textContent = 'Special Permissions';
        section.appendChild(title);
        
        const grid = document.createElement('div');
        grid.className = 'row g-2';
        
        specialPerms.forEach(perm => {
            const col = document.createElement('div');
            col.className = 'col-12';
            
            const formCheck = document.createElement('div');
            formCheck.className = 'form-check';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input';
            checkbox.name = `permissions[${moduleCode}][special][${perm}]`;
            checkbox.id = `perm_${moduleCode}_special_${perm}`;
            
            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.setAttribute('for', checkbox.id);
            label.textContent = this.formatSpecialPermission(perm);
            
            formCheck.appendChild(checkbox);
            formCheck.appendChild(label);
            col.appendChild(formCheck);
            grid.appendChild(col);
        });
        
        section.appendChild(grid);
        return section;
    }
    
    createSubmodulesSection(submodules) {
        const section = document.createElement('div');
        section.className = 'submodules-section mt-3';
        
        const title = document.createElement('h6');
        title.className = 'text-muted mb-2';
        title.textContent = 'Submodules';
        section.appendChild(title);
        
        Object.values(submodules).forEach(submodule => {
            const submoduleDiv = document.createElement('div');
            submoduleDiv.className = 'submodule-item border rounded p-2 mb-2';
            
            const header = document.createElement('div');
            header.className = 'd-flex align-items-center mb-2';
            header.innerHTML = `
                <i class="fas ${submodule.icon} me-2"></i>
                <span class="fw-semibold">${submodule.name}</span>
            `;
            
            submoduleDiv.appendChild(header);
            
            const permGrid = document.createElement('div');
            permGrid.className = 'permission-grid small';
            
            submodule.permissions.forEach(perm => {
                const permItem = this.createPermissionItem(submodule.code, perm);
                permItem.classList.add('small');
                permGrid.appendChild(permItem);
            });
            
            submoduleDiv.appendChild(permGrid);
            section.appendChild(submoduleDiv);
        });
        
        return section;
    }
    
    getPermissionIcon(permission) {
        const icons = {
            'view': 'fa-eye',
            'create': 'fa-plus',
            'edit': 'fa-edit',
            'delete': 'fa-trash'
        };
        return icons[permission] || 'fa-check';
    }
    
    getPermissionText(permission) {
        return this.options.translations[permission] || permission.charAt(0).toUpperCase() + permission.slice(1);
    }
    
    formatSpecialPermission(permission) {
        return permission.split('_').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }
    
    togglePermissionColumn(e) {
        const permission = e.target.dataset.permission;
        const isChecked = e.target.checked;
        
        document.querySelectorAll(`input[name*="[${permission}]"]`).forEach(input => {
            input.checked = isChecked;
        });
        
        this.markDirty();
    }
    
    filterModules() {
        const searchTerm = document.getElementById('moduleSearch').value.toLowerCase();
        
        document.querySelectorAll('.module-item').forEach(item => {
            const moduleName = item.textContent.toLowerCase();
            item.style.display = moduleName.includes(searchTerm) ? '' : 'none';
        });
    }
    
    navigateToNextGroup() {
        const activeItem = document.querySelector('.list-group-item.active');
        if (activeItem && activeItem.nextElementSibling) {
            activeItem.nextElementSibling.click();
        }
    }
    
    navigateToPreviousGroup() {
        const activeItem = document.querySelector('.list-group-item.active');
        if (activeItem && activeItem.previousElementSibling) {
            activeItem.previousElementSibling.click();
        }
    }
    
    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth < 992;
        
        if (wasMobile !== this.isMobile) {
            // Reload permissions view when switching between mobile/desktop
            if (this.currentGroupId) {
                this.loadGroupPermissions(this.currentGroupId);
            }
        }
    }
    
    markDirty() {
        this.isDirty = true;
        document.querySelector('.fab-button')?.classList.add('pulse');
    }
    
    async handleFormSubmit(e) {
        e.preventDefault();
        
        const form = e.target;
        const formData = new FormData(form);
        
        try {
            const response = await fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-Token': this.options.csrfToken
                }
            });
            
            if (response.ok) {
                this.showNotification('success', this.options.translations.saved);
                this.isDirty = false;
                document.querySelector('.fab-button')?.classList.remove('pulse');
            } else {
                throw new Error('Save failed');
            }
        } catch (error) {
            this.showNotification('error', this.options.translations.error);
        }
    }
    
    showNotification(type, message) {
        // Use native mobile notifications if available
        if ('Notification' in window && Notification.permission === 'granted' && this.isMobile) {
            new Notification('Permissions Manager', {
                body: message,
                icon: '/favicon.ico',
                vibrate: type === 'error' ? [200, 100, 200] : [100]
            });
        } else {
            // Fallback to toast notification
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            
            document.body.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
    }
    
    async loadGroupPermissions(groupId) {
        this.currentGroupId = groupId;
        
        try {
            const response = await fetch(`${this.options.apiBaseUrl}/group/${groupId}/permissions`);
            const data = await response.json();
            
            if (this.isMobile) {
                this.renderMobilePermissions(data);
            } else {
                this.renderDesktopPermissions(data);
            }
        } catch (error) {
            console.error('Failed to load permissions:', error);
            this.showNotification('error', 'Failed to load permissions');
        }
    }
    
    renderMobilePermissions(data) {
        // Update mobile view with permission data
        data.permissions.forEach(perm => {
            const checkbox = document.querySelector(`input[name="permissions[${perm.module}][${perm.action}]"]`);
            if (checkbox) {
                checkbox.checked = perm.granted;
            }
        });
    }
    
    renderDesktopPermissions(data) {
        // Update desktop table view
        const tbody = document.getElementById('permissionsBody');
        if (!tbody) return;
        
        tbody.innerHTML = '';
        
        this.options.modules.forEach(module => {
            const row = this.createPermissionRow(module, data.permissions);
            tbody.appendChild(row);
        });
    }
    
    createPermissionRow(module, permissions) {
        const row = document.createElement('tr');
        row.className = 'module-row';
        
        // Module name cell
        const moduleCell = document.createElement('td');
        moduleCell.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas ${module.icon} me-2"></i>
                <div>
                    <span class="fw-semibold">${module.name}</span>
                    ${module.description ? `<br><small class="text-muted">${module.description}</small>` : ''}
                </div>
            </div>
        `;
        row.appendChild(moduleCell);
        
        // Permission cells
        ['view', 'create', 'edit', 'delete'].forEach(perm => {
            const cell = document.createElement('td');
            cell.className = 'text-center';
            
            if (module.permissions.includes(perm)) {
                // Create switch wrapper
                const switchWrapper = document.createElement('label');
                switchWrapper.className = 'permission-switch';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = `permissions[${module.code}][${perm}]`;
                
                const slider = document.createElement('span');
                slider.className = 'slider';
                
                switchWrapper.appendChild(checkbox);
                switchWrapper.appendChild(slider);
                
                const permission = permissions.find(p => 
                    p.module === module.code && p.action === perm
                );
                
                if (permission) {
                    checkbox.checked = permission.granted;
                }
                
                cell.appendChild(switchWrapper);
            }
            
            row.appendChild(cell);
        });
        
        // Special permissions cell
        const specialCell = document.createElement('td');
        specialCell.className = 'text-center';
        
        if (module.special && module.special.length > 0) {
            const dropdown = this.createSpecialPermissionsDropdown(module);
            specialCell.appendChild(dropdown);
        }
        
        row.appendChild(specialCell);
        
        return row;
    }
    
    createSpecialPermissionsDropdown(module) {
        const wrapper = document.createElement('div');
        wrapper.className = 'dropdown';
        
        const button = document.createElement('button');
        button.className = 'btn btn-sm btn-outline-secondary dropdown-toggle';
        button.type = 'button';
        button.setAttribute('data-bs-toggle', 'dropdown');
        button.innerHTML = '<i class="fas fa-cog"></i>';
        
        const menu = document.createElement('ul');
        menu.className = 'dropdown-menu';
        
        module.special.forEach(perm => {
            const li = document.createElement('li');
            li.className = 'dropdown-item-text';
            
            const formCheck = document.createElement('div');
            formCheck.className = 'form-check';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input';
            checkbox.name = `permissions[${module.code}][special][${perm}]`;
            checkbox.id = `special_${module.code}_${perm}`;
            
            const label = document.createElement('label');
            label.className = 'form-check-label';
            label.setAttribute('for', checkbox.id);
            label.textContent = this.formatSpecialPermission(perm);
            
            formCheck.appendChild(checkbox);
            formCheck.appendChild(label);
            li.appendChild(formCheck);
            menu.appendChild(li);
        });
        
        wrapper.appendChild(button);
        wrapper.appendChild(menu);
        
        return wrapper;
    }
}

// Initialize on DOM ready
if (typeof window.permissionsManagerOptions !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        window.permissionsManager = new MobilePermissionsManager(window.permissionsManagerOptions);
    });
}