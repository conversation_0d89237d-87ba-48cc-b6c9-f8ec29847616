<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Core\Request;
use Flight;
use PDO;

class ErrorLogController extends Controller
{
    /**
     * Display error logs
     */
    public function index(Request $request)
    {
        $this->requirePermission('admin.errors.view');
        
        $page = (int)($request->get['page'] ?? 1);
        $perPage = 20;
        $offset = ($page - 1) * $perPage;
        
        // Get filters
        $environment = $request->get['environment'] ?? '';
        $resolved = $request->get['resolved'] ?? '';
        $search = $request->get['search'] ?? '';
        $dateFrom = $request->get['date_from'] ?? '';
        $dateTo = $request->get['date_to'] ?? '';
        
        // Build query
        $conditions = ['1=1'];
        $params = [];
        
        if ($environment) {
            $conditions[] = 'environment = :environment';
            $params['environment'] = $environment;
        }
        
        if ($resolved !== '') {
            $conditions[] = 'resolved = :resolved';
            $params['resolved'] = (int)$resolved;
        }
        
        if ($search) {
            $conditions[] = '(message LIKE :search OR request_uri LIKE :search_uri OR error_id LIKE :search_id)';
            $params['search'] = '%' . $search . '%';
            $params['search_uri'] = '%' . $search . '%';
            $params['search_id'] = '%' . $search . '%';
        }
        
        if ($dateFrom) {
            $conditions[] = 'timestamp >= :date_from';
            $params['date_from'] = $dateFrom . ' 00:00:00';
        }
        
        if ($dateTo) {
            $conditions[] = 'timestamp <= :date_to';
            $params['date_to'] = $dateTo . ' 23:59:59';
        }
        
        $whereClause = implode(' AND ', $conditions);
        
        // Get total count
        $countQuery = "SELECT COUNT(*) FROM error_logs WHERE $whereClause";
        $stmt = Flight::db()->prepare($countQuery);
        $stmt->execute($params);
        $totalItems = $stmt->fetchColumn();
        
        // Get error logs
        $query = "
            SELECT 
                el.*,
                u.username as user_name
            FROM error_logs el
            LEFT JOIN users u ON el.user_id = u.id
            WHERE $whereClause
            ORDER BY el.timestamp DESC
            LIMIT :limit OFFSET :offset
        ";
        
        $stmt = Flight::db()->prepare($query);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        
        $errors = $stmt->fetchAll();
        
        // Get environments for filter
        $envQuery = "SELECT DISTINCT environment FROM error_logs ORDER BY environment";
        $environments = Flight::db()->query($envQuery)->fetchAll(PDO::FETCH_COLUMN);
        
        // Calculate pagination
        $totalPages = ceil($totalItems / $perPage);
        
        $this->render('admin/error-logs', [
            'errors' => $errors,
            'environments' => $environments,
            'filters' => [
                'environment' => $environment,
                'resolved' => $resolved,
                'search' => $search,
                'date_from' => $dateFrom,
                'date_to' => $dateTo
            ],
            'pagination' => [
                'current_page' => $page,
                'total_pages' => $totalPages,
                'total_items' => $totalItems,
                'per_page' => $perPage
            ]
        ]);
    }
    
    /**
     * View error details
     */
    public function view($id)
    {
        $this->requirePermission('admin.errors.view');
        
        $query = "
            SELECT 
                el.*,
                u.username as user_name
            FROM error_logs el
            LEFT JOIN users u ON el.user_id = u.id
            WHERE el.id = :id
        ";
        
        $stmt = Flight::db()->prepare($query);
        $stmt->execute(['id' => $id]);
        $error = $stmt->fetch();
        
        if (!$error) {
            Flight::flash('error', 'Error log not found.');
            Flight::redirect('/admin/errors');
            return;
        }
        
        // Parse JSON fields
        if ($error['request_headers']) {
            $error['request_headers'] = json_decode($error['request_headers'], true);
        }
        if ($error['request_params']) {
            $error['request_params'] = json_decode($error['request_params'], true);
        }
        if ($error['session_data']) {
            $error['session_data'] = json_decode($error['session_data'], true);
        }
        
        $this->render('admin/error-log-view', [
            'error' => $error
        ]);
    }
    
    /**
     * Mark error as resolved
     */
    public function resolve($id)
    {
        $this->requirePermission('admin.errors.resolve');
        
        $notes = $_POST['notes'] ?? '';
        
        $stmt = Flight::db()->prepare("
            UPDATE error_logs 
            SET resolved = 1, notes = :notes 
            WHERE id = :id
        ");
        
        $stmt->execute([
            'id' => $id,
            'notes' => $notes
        ]);
        
        Flight::flash('success', 'Error marked as resolved.');
        Flight::redirect('/admin/errors/' . $id);
    }
    
    /**
     * Delete old error logs
     */
    public function cleanup()
    {
        $this->requirePermission('admin.errors.delete');
        
        $days = (int)($_POST['days'] ?? 30);
        
        $stmt = Flight::db()->prepare("
            DELETE FROM error_logs 
            WHERE timestamp < DATE_SUB(NOW(), INTERVAL :days DAY)
        ");
        
        $stmt->execute(['days' => $days]);
        $deleted = $stmt->rowCount();
        
        Flight::flash('success', "Deleted $deleted error logs older than $days days.");
        Flight::redirect('/admin/errors');
    }
    
    /**
     * Export error logs
     */
    public function export()
    {
        $this->requirePermission('admin.errors.export');
        
        $format = $_GET['format'] ?? 'csv';
        
        // Get filters from query string
        $conditions = $this->buildExportConditions();
        
        $query = "
            SELECT 
                el.*,
                u.username as user_name
            FROM error_logs el
            LEFT JOIN users u ON el.user_id = u.id
            WHERE {$conditions['where']}
            ORDER BY el.timestamp DESC
        ";
        
        $stmt = Flight::db()->prepare($query);
        $stmt->execute($conditions['params']);
        $errors = $stmt->fetchAll();
        
        if ($format === 'csv') {
            $this->exportCSV($errors);
        } else {
            $this->exportJSON($errors);
        }
    }
    
    private function buildExportConditions()
    {
        $conditions = ['1=1'];
        $params = [];
        
        if (!empty($_GET['environment'])) {
            $conditions[] = 'environment = :environment';
            $params['environment'] = $_GET['environment'];
        }
        
        if (isset($_GET['resolved']) && $_GET['resolved'] !== '') {
            $conditions[] = 'resolved = :resolved';
            $params['resolved'] = (int)$_GET['resolved'];
        }
        
        if (!empty($_GET['date_from'])) {
            $conditions[] = 'timestamp >= :date_from';
            $params['date_from'] = $_GET['date_from'] . ' 00:00:00';
        }
        
        if (!empty($_GET['date_to'])) {
            $conditions[] = 'timestamp <= :date_to';
            $params['date_to'] = $_GET['date_to'] . ' 23:59:59';
        }
        
        return [
            'where' => implode(' AND ', $conditions),
            'params' => $params
        ];
    }
    
    private function exportCSV($errors)
    {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="error_logs_' . date('Y-m-d') . '.csv"');
        
        $output = fopen('php://output', 'w');
        
        // Headers
        fputcsv($output, [
            'ID', 'Error ID', 'Timestamp', 'Environment', 'Message', 
            'File', 'Line', 'Class', 'Request URI', 'User', 
            'Execution Time', 'Memory Peak', 'Resolved'
        ]);
        
        // Data
        foreach ($errors as $error) {
            fputcsv($output, [
                $error['id'],
                $error['error_id'],
                $error['timestamp'],
                $error['environment'],
                $error['message'],
                $error['file'],
                $error['line'],
                $error['class'],
                $error['request_uri'],
                $error['user_name'] ?? 'Guest',
                $error['execution_time'],
                $error['memory_peak'],
                $error['resolved'] ? 'Yes' : 'No'
            ]);
        }
        
        fclose($output);
        exit;
    }
    
    private function exportJSON($errors)
    {
        header('Content-Type: application/json');
        header('Content-Disposition: attachment; filename="error_logs_' . date('Y-m-d') . '.json"');
        
        echo json_encode($errors, JSON_PRETTY_PRINT);
        exit;
    }
}