<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== ADDING USERS TO COACH GROUP ===\n\n";
    
    // Users to add
    $usersToAdd = [
        ['first_name' => 'Malaurie', 'last_name' => '<PERSON><PERSON><PERSON>'],
        ['first_name' => '<PERSON>', 'last_name' => '<PERSON><PERSON><PERSON>'],
        ['first_name' => '<PERSON>', 'last_name' => '<PERSON><PERSON>']
    ];
    
    // First, find the Coach group
    $stmt = $pdo->prepare("SELECT id, name FROM user_groups WHERE name LIKE '%Coach%' OR name LIKE '%coach%'");
    $stmt->execute();
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($groups) == 0) {
        echo "❌ No Coach group found!\n";
        echo "Available groups:\n";
        $stmt = $pdo->query("SELECT id, name FROM user_groups ORDER BY name");
        $allGroups = $stmt->fetchAll(PDO::FETCH_ASSOC);
        foreach ($allGroups as $g) {
            echo "  - {$g['name']} (ID: {$g['id']})\n";
        }
        exit(1);
    }
    
    $coachGroup = $groups[0];
    echo "✓ Found Coach group: {$coachGroup['name']} (ID: {$coachGroup['id']})\n\n";
    
    // For each user, find them and add to group
    foreach ($usersToAdd as $userData) {
        echo "Processing: {$userData['first_name']} {$userData['last_name']}\n";
        
        // Find user by name
        $stmt = $pdo->prepare("
            SELECT id, username, email 
            FROM users 
            WHERE first_name = :first_name AND last_name = :last_name
        ");
        $stmt->execute([
            ':first_name' => $userData['first_name'],
            ':last_name' => $userData['last_name']
        ]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            echo "  ❌ User not found in database\n";
            
            // Try to find similar users
            $stmt = $pdo->prepare("
                SELECT id, username, first_name, last_name 
                FROM users 
                WHERE first_name LIKE :first_name OR last_name LIKE :last_name
                LIMIT 5
            ");
            $stmt->execute([
                ':first_name' => '%' . $userData['first_name'] . '%',
                ':last_name' => '%' . $userData['last_name'] . '%'
            ]);
            $similar = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($similar) > 0) {
                echo "  Similar users found:\n";
                foreach ($similar as $s) {
                    echo "    - {$s['first_name']} {$s['last_name']} (username: {$s['username']}, ID: {$s['id']})\n";
                }
            }
            echo "\n";
            continue;
        }
        
        echo "  ✓ Found user: {$user['username']} (ID: {$user['id']}, Email: {$user['email']})\n";
        
        // Check if already in group
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as count 
            FROM user_group_members 
            WHERE user_id = :user_id AND group_id = :group_id
        ");
        $stmt->execute([
            ':user_id' => $user['id'],
            ':group_id' => $coachGroup['id']
        ]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "  ℹ️  Already in Coach group\n\n";
            continue;
        }
        
        // Add to group
        $stmt = $pdo->prepare("
            INSERT INTO user_group_members (user_id, group_id, created_at) 
            VALUES (:user_id, :group_id, NOW())
        ");
        $stmt->execute([
            ':user_id' => $user['id'],
            ':group_id' => $coachGroup['id']
        ]);
        
        echo "  ✅ Added to Coach group\n\n";
    }
    
    // Show summary
    echo "\n=== SUMMARY ===\n\n";
    echo "Coach group members:\n";
    
    $stmt = $pdo->prepare("
        SELECT u.id, u.username, u.first_name, u.last_name, u.email
        FROM users u
        JOIN user_group_members ugm ON u.id = ugm.user_id
        WHERE ugm.group_id = :group_id
        ORDER BY u.first_name, u.last_name
    ");
    $stmt->execute([':group_id' => $coachGroup['id']]);
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($members as $member) {
        echo "- {$member['first_name']} {$member['last_name']} ({$member['username']}) - {$member['email']}\n";
    }
    
    echo "\nTotal members in Coach group: " . count($members) . "\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}