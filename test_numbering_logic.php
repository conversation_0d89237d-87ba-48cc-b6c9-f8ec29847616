<?php
/**
 * Test the invoice numbering logic with sample data
 * This can be run through a browser or direct PHP execution
 */

// Sample data based on the table HTML provided
$existingInvoices = [
    'FAC-RET30-2025-0193' => 'sent',
    'FAC-RET30-2025-0192' => 'sent',
    'FAC-RET30-2025-0191' => 'sent',
    'FAC-RET30-2025-0190' => 'sent',
    'FAC-RET30-2025-0189' => 'sent',
    'FAC-LOY-2025-0194' => 'sent',
    'FAC-LOY-2025-0188' => 'sent',
    'FAC-LOY-2025-0187' => 'sent',
    'FAC-LOY-2025-0186' => 'sent',
];

function getNextGlobalInvoiceNumber($existingInvoices, $year = null) {
    $year = $year ?? date('Y'); // Use current year if not specified
    $maxNumber = 0;
    $pattern = "FAC-%-{$year}-";
    
    foreach ($existingInvoices as $invoiceNumber => $status) {
        // Only consider sent invoices (not drafts)
        if ($status !== 'draft' && strpos($invoiceNumber, $pattern) !== false) {
            // Extract the last 4 digits
            $lastFourDigits = substr($invoiceNumber, -4);
            $number = intval($lastFourDigits);
            
            if ($number > $maxNumber) {
                $maxNumber = $number;
            }
        }
    }
    
    return $maxNumber + 1;
}

// Test the logic
echo "<h2>Invoice Numbering Test</h2>\n";
echo "<h3>Existing Invoices:</h3>\n";
echo "<ul>\n";
foreach ($existingInvoices as $number => $status) {
    echo "<li>{$number} - {$status}</li>\n";
}
echo "</ul>\n";

$currentYear = date('Y');
$nextNumber = getNextGlobalInvoiceNumber($existingInvoices, $currentYear);
echo "<h3>Next Global Sequence Number: {$nextNumber} (for year {$currentYear})</h3>\n";

// Test what different invoice types would get
$invoiceTypes = [
    'LOY' => 'Loyer',
    'RET30' => 'Rétrocession 30%',
    'RET25' => 'Rétrocession 25%',
    'GEN' => 'Général'
];

echo "<h3>Next Invoice Numbers by Type:</h3>\n";
echo "<ul>\n";
foreach ($invoiceTypes as $prefix => $name) {
    $suggestedNumber = "FAC-{$prefix}-{$currentYear}-" . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    echo "<li>{$name}: {$suggestedNumber}</li>\n";
}
echo "</ul>\n";

// Verify the highest existing number
$highestExisting = 0;
$highestInvoice = '';
foreach ($existingInvoices as $number => $status) {
    if ($status !== 'draft') {
        $lastFour = intval(substr($number, -4));
        if ($lastFour > $highestExisting) {
            $highestExisting = $lastFour;
            $highestInvoice = $number;
        }
    }
}

echo "<h3>Verification:</h3>\n";
echo "<p>Highest existing number: {$highestExisting} (from {$highestInvoice})</p>\n";
echo "<p>Next number should be: " . ($highestExisting + 1) . "</p>\n";
echo "<p>Our function returned: {$nextNumber}</p>\n";
echo "<p>Match: " . ($nextNumber === $highestExisting + 1 ? 'YES' : 'NO') . "</p>\n";
?>