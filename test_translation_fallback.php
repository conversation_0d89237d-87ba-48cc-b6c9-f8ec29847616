<?php
/**
 * Test script for the enhanced translation helper
 * Run: php test_translation_fallback.php
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Helpers\Language;

echo "=== Testing Enhanced Translation Helper ===\n\n";

// Test 1: Existing translation
echo "Test 1: Existing translation\n";
$result = __('users.username');
echo "Result: $result\n\n";

// Test 2: Missing translation (should fallback to English)
echo "Test 2: Missing translation in French (fallback to English)\n";
Language::setLanguage('fr');
$result = __('test.missing_in_french');
echo "Result: $result\n\n";

// Test 3: Missing in both languages (should return formatted key)
echo "Test 3: Missing in all languages\n";
$result = __('test.completely_missing_key');
echo "Result: $result\n\n";

// Test 4: Invalid key format
echo "Test 4: Invalid key format\n";
$result = __('invalidkey');
echo "Result: $result\n\n";

// Test 5: Translation with parameters
echo "Test 5: Translation with parameters\n";
$result = __('messages.welcome', ['name' => 'John']);
echo "Result: $result\n\n";

// Test 6: Check missing translations
echo "Test 6: Missing translations report\n";
$missing = Language::getMissingTranslations();
echo "Missing translations:\n";
print_r($missing);
echo "\n";

// Test 7: Cache performance
echo "Test 7: Cache performance\n";
$start = microtime(true);
for ($i = 0; $i < 100; $i++) {
    __('users.username');
}
$time1 = microtime(true) - $start;
echo "Time for 100 translations (with cache): " . number_format($time1 * 1000, 2) . "ms\n";

// Clear cache and test again
Language::clearCache();
$start = microtime(true);
for ($i = 0; $i < 100; $i++) {
    __('users.username');
}
$time2 = microtime(true) - $start;
echo "Time for 100 translations (without cache): " . number_format($time2 * 1000, 2) . "ms\n";
echo "Cache speedup: " . number_format($time2 / $time1, 2) . "x\n\n";

// Test 8: Export missing translations
echo "Test 8: Export missing translations\n";
$export = Language::exportMissingTranslations();
echo "Exportable missing translations:\n";
print_r($export);

echo "\n=== Test Complete ===\n";
echo "Check storage/logs/missing_translations.log for the log entries.\n";