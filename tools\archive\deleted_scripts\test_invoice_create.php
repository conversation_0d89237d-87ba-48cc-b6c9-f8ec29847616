<?php
/**
 * Simple Invoice Creation Test
 * This creates a basic invoice to test the system
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Models\Invoice;
use App\Models\Client;

// Start session if not started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Set default user for testing
}

$db = Flight::db();
$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $invoiceModel = new Invoice();
        
        // Prepare invoice data
        $invoiceData = [
            'client_id' => $_POST['client_id'],
            'document_type_id' => $_POST['document_type_id'],
            'invoice_type_id' => $_POST['invoice_type_id'] ?: null,
            'invoice_number' => $_POST['invoice_number'],
            'issue_date' => $_POST['issue_date'],
            'due_date' => $_POST['due_date'],
            'status' => 'draft',
            'payment_term_id' => 1, // Default payment term
            'created_by' => $_SESSION['user_id'],
            'items' => [
                [
                    'description' => $_POST['item_description'],
                    'quantity' => $_POST['item_quantity'],
                    'unit_price' => $_POST['item_price'],
                    'vat_rate_id' => $_POST['item_vat_rate_id']
                ]
            ]
        ];
        
        // Create invoice
        $result = $invoiceModel->createInvoice($invoiceData);
        
        if ($result && isset($result['id'])) {
            $message = "Invoice created successfully! ID: " . $result['id'];
            $message .= " <a href='/fit/public/invoices/" . $result['id'] . "' class='btn btn-sm btn-primary'>View Invoice</a>";
        } else {
            $error = "Failed to create invoice";
        }
        
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}

// Get data for form
$clients = (new Client())->getAllActive();
$stmt = $db->query("SELECT * FROM document_types WHERE is_active = 1");
$docTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt = $db->query("SELECT * FROM config_invoice_types WHERE is_active = 1");
$invTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1");
$vatRates = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Generate invoice number
$invoiceNumber = '';
if (!empty($docTypes)) {
    $invoiceModel = new Invoice();
    $invoiceNumber = $invoiceModel->generateDocumentNumber($docTypes[0]['id']);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Invoice Creation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h1>Simple Invoice Creation Test</h1>
    
    <?php if ($message): ?>
        <div class="alert alert-success"><?php echo $message; ?></div>
    <?php endif; ?>
    
    <?php if ($error): ?>
        <div class="alert alert-danger"><?php echo $error; ?></div>
    <?php endif; ?>
    
    <form method="POST" class="mt-4">
        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
        
        <div class="row">
            <div class="col-md-6">
                <h3>Invoice Details</h3>
                
                <div class="mb-3">
                    <label class="form-label">Client *</label>
                    <select name="client_id" class="form-select" required>
                        <option value="">Select Client</option>
                        <?php foreach ($clients as $client): ?>
                            <option value="<?php echo $client['id']; ?>">
                                <?php echo htmlspecialchars($client['first_name'] . ' ' . $client['last_name'] . ' (' . $client['client_number'] . ')'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Document Type *</label>
                    <select name="document_type_id" class="form-select" required>
                        <?php foreach ($docTypes as $type): 
                            $name = json_decode($type['name'], true);
                            $displayName = is_array($name) ? ($name['en'] ?? $name['fr'] ?? $type['code']) : $type['name'];
                        ?>
                            <option value="<?php echo $type['id']; ?>">
                                <?php echo htmlspecialchars($displayName); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Invoice Category (Optional)</label>
                    <select name="invoice_type_id" class="form-select">
                        <option value="">None</option>
                        <?php foreach ($invTypes as $type): 
                            $name = json_decode($type['name'], true);
                            $displayName = is_array($name) ? ($name['en'] ?? $name['fr'] ?? $type['code']) : $type['name'];
                        ?>
                            <option value="<?php echo $type['id']; ?>">
                                <?php echo htmlspecialchars($displayName); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Invoice Number *</label>
                    <input type="text" name="invoice_number" class="form-control" 
                           value="<?php echo htmlspecialchars($invoiceNumber); ?>" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Issue Date *</label>
                    <input type="date" name="issue_date" class="form-control" 
                           value="<?php echo date('Y-m-d'); ?>" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Due Date *</label>
                    <input type="date" name="due_date" class="form-control" 
                           value="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" required>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>Invoice Item</h3>
                
                <div class="mb-3">
                    <label class="form-label">Description *</label>
                    <input type="text" name="item_description" class="form-control" 
                           value="Test Service" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Quantity *</label>
                    <input type="number" name="item_quantity" class="form-control" 
                           value="1" min="1" step="0.01" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Unit Price *</label>
                    <input type="number" name="item_price" class="form-control" 
                           value="100" min="0" step="0.01" required>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">VAT Rate *</label>
                    <select name="item_vat_rate_id" class="form-select" required>
                        <?php foreach ($vatRates as $rate): ?>
                            <option value="<?php echo $rate['id']; ?>" 
                                    <?php echo $rate['is_default'] ? 'selected' : ''; ?>>
                                <?php echo $rate['rate']; ?>% - <?php echo htmlspecialchars($rate['code']); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="alert alert-info">
                    <strong>Total:</strong> 
                    <span id="total">Will be calculated on submission</span>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button type="submit" class="btn btn-primary">Create Invoice</button>
            <a href="/fit/public/invoices" class="btn btn-secondary">Back to Invoices</a>
        </div>
    </form>
    
    <div class="mt-5">
        <h4>Debug Information</h4>
        <ul>
            <li>User ID: <?php echo $_SESSION['user_id'] ?? 'Not set'; ?></li>
            <li>CSRF Token: <?php echo $_SESSION['csrf_token'] ?? 'Not set'; ?></li>
            <li>Clients Available: <?php echo count($clients); ?></li>
            <li>Document Types: <?php echo count($docTypes); ?></li>
            <li>Invoice Categories: <?php echo count($invTypes); ?></li>
            <li>VAT Rates: <?php echo count($vatRates); ?></li>
        </ul>
    </div>
</div>
</body>
</html>