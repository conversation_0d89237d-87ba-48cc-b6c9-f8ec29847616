<?php
/**
 * Complete fix for retrocession VAT display and JavaScript errors
 */

echo "<pre>";
echo "=== Complete Fix for Retrocession VAT Display ===\n\n";

// Read create-modern.twig
$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($createFile);

// Backup the original file
$backupFile = $createFile . '.backup.' . date('YmdHis');
file_put_contents($backupFile, $content);
echo "✓ Created backup: " . basename($backupFile) . "\n";

// 1. Fix displayBillableInfo function - properly scope variables
$displayBillableInfoPattern = '/function displayBillableInfo\(billableType, billableId\) \{[^}]*\n[^}]*console\.log[^}]*\n[^}]*\n[^}]*if \(!billableId\) \{/s';

$displayBillableInfoReplacement = 'function displayBillableInfo(billableType, billableId) {
    console.log(\'🔍 displayBillableInfo called:\', billableType, billableId);
    
    if (!billableId) {';

$content = preg_replace($displayBillableInfoPattern, $displayBillableInfoReplacement, $content, 1);

// 2. Update the VAT display section properly
$vatSectionPattern = '/\/\/ VAT info column.*?html \+= \'<\/div>\';.*?html \+= \'<\/div>\';/s';

$vatSectionReplacement = '// VAT info column
            html += \'<div class="col-md-6">\';
            
            // Check if this is a retrocession invoice
            const invoiceTypeEl = document.getElementById(\'invoice_type_id\');
            const selectedOpt = invoiceTypeEl ? invoiceTypeEl.options[invoiceTypeEl.selectedIndex] : null;
            const typePrefix = selectedOpt ? selectedOpt.getAttribute(\'data-prefix\') : \'\';
            const isRetroInvoice = window.location.search.includes(\'type=retrocession\') || 
                                  (typePrefix && typePrefix.startsWith(\'RET\'));
            
            // VAT number
            if (!isRetroInvoice && userData.vat_number) {
                html += \'<div class="mb-2">\';
                html += \'<strong>{{ __("users.vat_number") }}:</strong> \' + userData.vat_number;
                html += \'</div>\';
            }
            
            // Intracommunity VAT status
            if (!isRetroInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {
                const vatNumber = userData.vat_intercommunautaire ? userData.vat_intercommunautaire.trim() : \'\';
                const isLuxembourgVat = vatNumber.toUpperCase().startsWith(\'LU\');
                
                if (isLuxembourgVat) {
                    // Luxembourg VAT - standard VAT applies
                    html += \'<div class="alert alert-info py-2 px-3 mb-0">\';
                    html += \'<i class="bi bi-info-circle me-2"></i>\';
                    html += \'<strong>{{ __("users.luxembourg_vat") | default("TVA Luxembourg") }}</strong><br>\';
                    html += \'<small>\' + vatNumber + \'</small>\';
                    html += \'</div>\';
                } else {
                    // Non-Luxembourg EU VAT - intracommunity rules apply
                    html += \'<div class="alert alert-success py-2 px-3 mb-0">\';
                    html += \'<i class="bi bi-check-circle me-2"></i>\';
                    html += \'<strong>{{ __("users.intracommunity_vat_applicable") }}</strong><br>\';
                    html += \'<small>\' + vatNumber + \'</small>\';
                    html += \'</div>\';
                }
            } else if (!isRetroInvoice && userData.vat_number) {
                // Has VAT number but not intracommunity
                html += \'<div class="text-muted small">\';
                html += \'<i class="bi bi-info-circle me-1"></i>\';
                html += \'{{ __("users.standard_vat_applies") }}\';
                html += \'</div>\';
            }
            
            html += \'</div>\';
            html += \'</div>\';';

$content = preg_replace($vatSectionPattern, $vatSectionReplacement, $content, 1);

// 3. Remove any duplicate variable declarations by using unique names
$content = str_replace('const invoiceTypeSelect = document.getElementById(\'invoice_type_id\');', 'const invTypeSelect = document.getElementById(\'invoice_type_id\');', $content);
$content = str_replace('const selectedOption = invoiceTypeSelect', 'const selectedOption = invTypeSelect', $content);

// Save the fixed file
file_put_contents($createFile, $content);
echo "✓ Fixed create-modern.twig\n\n";

// 4. Now fix show-modern.twig if it hasn't been fixed yet
$showFile = __DIR__ . '/app/views/invoices/show-modern.twig';
if (file_exists($showFile)) {
    $showContent = file_get_contents($showFile);
    
    // Check if already fixed
    if (strpos($showContent, 'isRetrocessionInvoice') === false) {
        // Add retrocession check before VAT display
        $showContent = str_replace(
            '{# VAT Information #}',
            '{# VAT Information - Hide for retrocession invoices #}
                                    {% set invoiceTypeCode = invoice.invoice_type.code|default(\'\') %}
                                    {% set isRetrocessionInvoice = (invoiceTypeCode starts with \'RET\') or (invoice.invoice_type.id in [2, 15]) %}',
            $showContent
        );
        
        // Update VAT display conditions
        $showContent = str_replace(
            '{% if invoice.user.vat_intercommunautaire %}',
            '{% if not isRetrocessionInvoice and invoice.user.vat_intercommunautaire %}',
            $showContent
        );
        
        $showContent = str_replace(
            '{% elseif invoice.user.vat_number %}',
            '{% elseif not isRetrocessionInvoice and invoice.user.vat_number %}',
            $showContent
        );
        
        file_put_contents($showFile, $showContent);
        echo "✓ Fixed show-modern.twig\n";
    } else {
        echo "✓ show-modern.twig already fixed\n";
    }
}

echo "\n=== Summary ===\n";
echo "1. Fixed JavaScript variable redeclaration errors\n";
echo "2. Fixed VAT display logic for retrocession invoices\n";
echo "3. Added proper null checks\n";
echo "4. Created backup of original file\n";

echo "\n✅ All fixes applied successfully!\n";
echo "\nNow when creating a retrocession invoice:\n";
echo "- No JavaScript errors\n";
echo "- No VAT information displayed for practitioners\n";
echo "- Clean interface for retrocession invoices\n";

echo "</pre>";