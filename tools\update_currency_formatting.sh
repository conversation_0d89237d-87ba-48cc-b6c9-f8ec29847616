#!/bin/bash

# Script to update currency and percentage formatting across Twig templates
# Adds space before € and % symbols

echo "Updating currency and percentage formatting in Twig templates..."

# Base directory for views
VIEWS_DIR="/mnt/d/wamp64/www/fit/app/views"

# Counter for changes
CHANGES=0

# Function to update files
update_formatting() {
    local file=$1
    local temp_file="${file}.tmp"
    
    # Check if file exists
    if [ ! -f "$file" ]; then
        echo "File not found: $file"
        return
    fi
    
    # Create backup
    cp "$file" "${file}.bak"
    
    # Update € symbols (add space before €)
    # Pattern: }}€ -> }} €
    sed 's/}}\u20ac/}} \u20ac/g' "$file" > "$temp_file"
    mv "$temp_file" "$file"
    
    # Pattern: </span>€ -> </span> €
    sed 's/<\/span>\u20ac/<\/span> \u20ac/g' "$file" > "$temp_file"
    mv "$temp_file" "$file"
    
    # Pattern: + '€' -> + ' €' (JavaScript)
    sed "s/+ '\u20ac'/+ ' \u20ac'/g" "$file" > "$temp_file"
    mv "$temp_file" "$file"
    
    # Update % symbols (add space before %)
    # Pattern: }}% -> }} %
    sed 's/}}%/}} %/g' "$file" > "$temp_file"
    mv "$temp_file" "$file"
    
    # Check if changes were made
    if ! diff -q "$file" "${file}.bak" > /dev/null; then
        echo "✓ Updated: $file"
        ((CHANGES++))
        rm "${file}.bak"
    else
        echo "  No changes needed: $file"
        rm "${file}.bak"
    fi
}

# List of files to update
FILES=(
    "$VIEWS_DIR/dashboard-modern.twig"
    "$VIEWS_DIR/invoices/edit-modern.twig"
    "$VIEWS_DIR/invoices/generate-monthly-modern.twig"
    "$VIEWS_DIR/invoices/index-modern.twig"
    "$VIEWS_DIR/invoices/show-modern.twig"
    "$VIEWS_DIR/pos/receipt-modern.twig"
    "$VIEWS_DIR/products/show-modern.twig"
    "$VIEWS_DIR/retrocession/data-entry-modern.twig"
    "$VIEWS_DIR/users/financial-obligations-modern.twig"
    "$VIEWS_DIR/users/form-modern.twig"
    "$VIEWS_DIR/config/vat-rates-modern.twig"
    "$VIEWS_DIR/invoices/show-modern-backup.twig"
    "$VIEWS_DIR/invoices/show-modern-improved.twig"
    "$VIEWS_DIR/products/create-modern.twig"
    "$VIEWS_DIR/products/edit-modern.twig"
    "$VIEWS_DIR/products/index-modern.twig"
    "$VIEWS_DIR/retrocession/index-modern.twig"
    "$VIEWS_DIR/retrocession/rate-profiles-modern.twig"
    "$VIEWS_DIR/translations/index-modern.twig"
    "$VIEWS_DIR/vouchers/index-modern.twig"
)

# Update each file
for file in "${FILES[@]}"; do
    update_formatting "$file"
done

echo ""
echo "Summary: Updated $CHANGES files"
echo ""
echo "Note: Files with €{{ pattern (currency before value) were not updated."
echo "These might need manual review for consistency."