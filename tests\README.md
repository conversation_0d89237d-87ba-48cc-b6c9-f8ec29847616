# FIT360 Test Suite Documentation

## Overview

This test suite covers both Phase 3 (Core Billing & Invoice System) and Phase 4 (Sales & Inventory System) functionality.

## Test Structure

```
tests/
├── Phase3/                     # Core billing system tests
│   ├── InvoiceModelTest.php    # Invoice CRUD and calculations
│   ├── VatRateTest.php         # VAT rate management
│   ├── PaymentTermsTest.php    # Payment terms configuration
│   ├── DocumentTypesTest.php   # Document types and numbering
│   └── run-phase3-tests.php    # Phase 3 test runner
├── Phase4/                     # Sales & inventory tests
│   ├── CatalogItemTest.php     # Product catalog functionality
│   ├── CatalogCategoryTest.php # Category hierarchy management
│   ├── StockManagementTest.php # Inventory and stock control
│   └── run-phase4-tests.php    # Phase 4 test runner
└── run-all-tests.php           # Master test runner
```

## Running Tests

### Prerequisites

1. Ensure PHPUnit is installed:
   ```bash
   composer require --dev phpunit/phpunit
   ```

2. Database setup:
   - Tests use the configured database from `.env`
   - Tests create and clean up their own test data
   - Test data uses 'TEST-' prefix for easy identification

### Running All Tests

```bash
# From project root
php tests/run-all-tests.php

# Or using composer (if configured)
composer test
```

### Running Phase-Specific Tests

```bash
# Run only Phase 3 tests
php tests/Phase3/run-phase3-tests.php

# Run only Phase 4 tests
php tests/Phase4/run-phase4-tests.php
```

### Running Individual Test Classes

```bash
# Using PHPUnit directly
vendor/bin/phpunit tests/Phase3/InvoiceModelTest.php
vendor/bin/phpunit tests/Phase4/CatalogItemTest.php

# Or specific test methods
vendor/bin/phpunit tests/Phase3/InvoiceModelTest.php --filter testCreateInvoice
```

## Test Coverage

### Phase 3: Core Billing System

1. **Invoice Model Tests**
   - Invoice creation and validation
   - Document number generation
   - Status transitions (draft → sent → paid)
   - Payment tracking and calculations
   - Credit note handling
   - Due date calculations
   - Invoice item management

2. **VAT Rate Tests**
   - VAT rate CRUD operations
   - Multi-language support
   - Default rate handling
   - VAT calculations
   - Rate status management

3. **Payment Terms Tests**
   - Payment term configuration
   - Due date calculations
   - Default terms
   - Discount calculations
   - Multi-language descriptions

4. **Document Types Tests**
   - Document type management
   - Number sequence generation
   - Counter types (global, yearly, monthly)
   - Custom number formats
   - Credit note configuration

### Phase 4: Sales & Inventory System

1. **Catalog Item Tests**
   - Product CRUD operations
   - Price calculations with VAT
   - Stock management
   - Stock status tracking
   - Quick sale items
   - Product search
   - Code generation
   - Low stock alerts

2. **Category Management Tests**
   - Category hierarchy
   - Parent-child relationships
   - Breadcrumb generation
   - Multi-level navigation
   - Category items
   - Tree structure

3. **Stock Management Tests**
   - Stock movements
   - Movement types (purchase, sale, return, etc.)
   - Stock adjustments
   - Movement history
   - Stock balance calculations
   - Low stock detection
   - Reference tracking

## Writing New Tests

### Test Naming Convention

- Test classes: `{Feature}Test.php`
- Test methods: `test{Action}{Expected}`
- Test data: Use 'TEST-' prefix for all test records

### Test Structure

```php
class MyFeatureTest extends TestCase
{
    public static function setUpBeforeClass(): void
    {
        // One-time setup
    }
    
    public function setUp(): void
    {
        // Setup before each test
    }
    
    public function testFeatureAction()
    {
        // Arrange
        $data = [...];
        
        // Act
        $result = MyModel::create($data);
        
        // Assert
        $this->assertNotNull($result);
    }
    
    public function tearDown(): void
    {
        // Cleanup after each test
    }
    
    public static function tearDownAfterClass(): void
    {
        // One-time cleanup
    }
}
```

### Best Practices

1. **Isolation**: Each test should be independent
2. **Cleanup**: Always clean up test data
3. **Naming**: Use descriptive test names
4. **Assertions**: Use specific assertions
5. **Coverage**: Test both success and failure cases

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check `.env` database credentials
   - Ensure database server is running
   - Verify database exists

2. **Test Data Conflicts**
   - Run cleanup manually: `DELETE FROM table WHERE code LIKE 'TEST-%'`
   - Check for foreign key constraints

3. **Missing Dependencies**
   - Run `composer install`
   - Check autoload: `composer dump-autoload`

### Debug Mode

Enable verbose output:
```bash
vendor/bin/phpunit tests/Phase3/InvoiceModelTest.php -v
```

## Continuous Integration

The test suite is designed to be CI/CD friendly:

```yaml
# Example GitHub Actions workflow
- name: Run tests
  run: |
    cp .env.example .env
    composer install
    php tests/run-all-tests.php
```

## Maintenance

- Review and update tests when adding new features
- Keep test data minimal and focused
- Regular cleanup of old test data
- Monitor test execution time