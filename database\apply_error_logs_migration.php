<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

// Database connection
$host = $_ENV['DB_HOST'];
$username = $_ENV['DB_USER'];
$password = $_ENV['DB_PASS'];
$database = $_ENV['DB_NAME'];

try {
    $mysqli = new mysqli($host, $username, $password, $database);
    
    if ($mysqli->connect_error) {
        throw new Exception("Connection failed: " . $mysqli->connect_error);
    }
    
    echo "Connected to database successfully.\n";
    
    // Check if error_logs table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'error_logs'");
    if ($result->num_rows > 0) {
        echo "✓ error_logs table already exists\n";
        
        // Show table structure
        echo "\nTable structure:\n";
        $result = $mysqli->query("DESCRIBE error_logs");
        while ($row = $result->fetch_assoc()) {
            echo sprintf("  - %s %s %s %s\n", 
                $row['Field'], 
                $row['Type'], 
                $row['Null'] === 'NO' ? 'NOT NULL' : 'NULL',
                $row['Key'] === 'PRI' ? 'PRIMARY KEY' : ($row['Key'] === 'MUL' ? 'INDEX' : '')
            );
        }
    } else {
        echo "× error_logs table does NOT exist\n";
        echo "Creating error_logs table...\n";
        
        // Read and execute the migration
        $migrationFile = __DIR__ . '/migrations/109_create_error_logs_table.sql';
        if (!file_exists($migrationFile)) {
            throw new Exception("Migration file not found: $migrationFile");
        }
        
        $sql = file_get_contents($migrationFile);
        
        // Execute the migration
        if ($mysqli->query($sql) === TRUE) {
            echo "✓ error_logs table created successfully\n";
            
            // Mark migration as applied
            $stmt = $mysqli->prepare("INSERT INTO migrations (migration, applied_at) VALUES (?, NOW())");
            $migrationName = '109_create_error_logs_table.sql';
            $stmt->bind_param('s', $migrationName);
            
            if ($stmt->execute()) {
                echo "✓ Migration marked as applied\n";
            } else {
                echo "Warning: Could not mark migration as applied: " . $stmt->error . "\n";
            }
            $stmt->close();
        } else {
            throw new Exception("Error creating table: " . $mysqli->error);
        }
    }
    
    // Test error logging
    echo "\nTesting error logging...\n";
    
    // Create a test error entry
    $testErrorId = 'TEST_' . uniqid();
    $stmt = $mysqli->prepare("
        INSERT INTO error_logs (
            error_id, timestamp, environment, message, 
            code, file, line, class, trace,
            request_method, request_uri, user_agent, ip_address
        ) VALUES (
            ?, NOW(), 'test', 'Test error log entry',
            500, ?, 100, 'TestClass', 'Test trace',
            'GET', '/test', 'Test Agent', '127.0.0.1'
        )
    ");
    
    $testFile = __FILE__;
    $stmt->bind_param('ss', $testErrorId, $testFile);
    
    if ($stmt->execute()) {
        echo "✓ Test error log entry created successfully (ID: $testErrorId)\n";
        
        // Verify the entry
        $result = $mysqli->query("SELECT * FROM error_logs WHERE error_id = '$testErrorId'");
        if ($result->num_rows > 0) {
            echo "✓ Test entry verified in database\n";
            
            // Clean up test entry
            $mysqli->query("DELETE FROM error_logs WHERE error_id = '$testErrorId'");
            echo "✓ Test entry cleaned up\n";
        }
    } else {
        echo "× Could not create test entry: " . $stmt->error . "\n";
    }
    $stmt->close();
    
    // Show final status
    echo "\n=== Final Status ===\n";
    $result = $mysqli->query("SELECT COUNT(*) as count FROM error_logs");
    $row = $result->fetch_assoc();
    echo "Total error logs in table: " . $row['count'] . "\n";
    
    $mysqli->close();
    echo "\n✓ All operations completed successfully!\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}