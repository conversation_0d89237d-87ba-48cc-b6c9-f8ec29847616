{% extends "base-modern.twig" %}

{% block title %}{{ __('config.document_types') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.document_types') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#typeModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_document_type') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.document_types_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Debug Info -->
    <div class="alert alert-warning" id="debug-info">
        <strong>Debug Info:</strong>
        <p>Time: {{ "now"|date("Y-m-d H:i:s") }}</p>
        <p>Invoice Types Count: {{ invoiceTypes|length|default(0) }}</p>
        <p>Invoice Types Variable: {{ invoiceTypes ? 'Set' : 'Not Set' }}</p>
        <p>Invoice Types is Array: {{ invoiceTypes is iterable ? 'Yes' : 'No' }}</p>
        {% if invoiceTypes|length > 0 %}
            <p>First Type Code: {{ invoiceTypes[0].code|default('N/A') }}</p>
            <p>First Type Name: {{ invoiceTypes[0].name|default('N/A') }}</p>
        {% endif %}
        <button type="button" class="btn-close float-end" onclick="document.getElementById('debug-info').style.display='none'"></button>
    </div>
    
    <!-- Invoice Types -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ __('config.current_document_types') }}</h6>
        </div>
        <div class="card-body">
            <div class="row" id="typesContainer">
                {% for type in invoiceTypes %}
                <div class="col-lg-4 col-md-6 mb-4" data-id="{{ type.id }}">
                    <div class="card h-100 border-0 shadow-sm">
                        <div class="card-header" style="background-color: {{ type.color }}; color: white;">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">
                                    <i class="{{ type.icon }} me-2"></i>{{ type.name }}
                                </h5>
                                <span class="badge bg-white text-dark">{{ type.code }}</span>
                            </div>
                        </div>
                        <div class="card-body">
                            <dl class="row mb-0">
                                <dt class="col-6">{{ __('config.prefix') }}:</dt>
                                <dd class="col-6">{{ type.prefix }}</dd>
                                
                                <dt class="col-6">{{ __('config.format') }}:</dt>
                                <dd class="col-6">
                                    <code>{{ type.format }}</code>
                                </dd>
                                
                                <dt class="col-6">{{ __('config.next_number') }}:</dt>
                                <dd class="col-6">
                                    <span class="badge bg-primary">{{ type.next_number }}</span>
                                </dd>
                                
                                <dt class="col-6">{{ __('common.status') }}:</dt>
                                <dd class="col-6">
                                    {% if type.is_active %}
                                        <span class="badge bg-success">{{ __('common.active') }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                    {% endif %}
                                </dd>
                            </dl>
                            
                            {% if type.description %}
                            <hr>
                            <p class="mb-0 text-muted small">{{ type.description }}</p>
                            {% endif %}
                        </div>
                        <div class="card-footer bg-light border-0">
                            <div class="d-flex justify-content-between">
                                <button class="btn btn-sm btn-outline-primary" onclick="editType({{ type.id }})">
                                    <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteType({{ type.id }})"
                                        {{ type.has_invoices|default(false) ? 'disabled' : '' }}>
                                    <i class="bi bi-trash me-1"></i>{{ __('common.delete') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="col-12">
                    <div class="text-center py-5 text-muted">
                        <i class="bi bi-file-earmark-text fs-1 mb-3"></i>
                        <p>{{ __('config.no_document_types_found') }}</p>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Configuration Help -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h6 class="mb-0"><i class="bi bi-lightbulb me-2"></i>{{ __('config.format_help') }}</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>{{ __('config.available_variables') }}:</strong></p>
                    <ul class="mb-0 small">
                        <li><code>{prefix}</code> - {{ __('config.type_prefix') }}</li>
                        <li><code>{year}</code> - {{ __('config.current_year_4_digits') }}</li>
                        <li><code>{yy}</code> - {{ __('config.current_year_2_digits') }}</li>
                        <li><code>{month}</code> - {{ __('config.current_month_2_digits') }}</li>
                        <li><code>{day}</code> - {{ __('config.current_day_2_digits') }}</li>
                        <li><code>{number}</code> - {{ __('config.sequential_number') }}</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('config.examples') }}</h6>
                </div>
                <div class="card-body">
                    <p class="mb-2"><strong>{{ __('config.format_examples') }}:</strong></p>
                    <ul class="mb-0 small">
                        <li><code>{prefix}-{year}-{number:5}</code> → INV-2024-00001</li>
                        <li><code>{prefix}/{yy}{month}/{number:4}</code> → INV/2412/0001</li>
                        <li><code>{year}{month}{day}-{number:3}</code> → 20241221-001</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invoice Type Modal -->
<div class="modal fade" id="typeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/config/invoice-types" id="typeForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="_method" id="form_method" value="POST">
                <input type="hidden" name="type_id" id="type_id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="typeModalTitle">{{ __('config.add_document_type') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="type_name" class="form-label">{{ __('common.name') }} *</label>
                            <input type="text" class="form-control" id="type_name" name="name" required>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="type_code" class="form-label">{{ __('common.code') }} *</label>
                            <input type="text" class="form-control" id="type_code" name="code" 
                                   maxlength="10" required pattern="[A-Z0-9_]+">
                            <small class="text-muted">{{ __('config.code_hint') }}</small>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="type_prefix" class="form-label">{{ __('config.prefix') }} *</label>
                            <input type="text" class="form-control" id="type_prefix" name="prefix" 
                                   maxlength="10" required>
                        </div>
                        
                        <div class="col-md-8">
                            <label for="type_format" class="form-label">{{ __('config.number_format') }} *</label>
                            <input type="text" class="form-control" id="type_format" name="format" 
                                   value="{prefix}-{year}-{number:5}" required>
                            <small class="text-muted">{{ __('config.format_hint') }}</small>
                        </div>
                        
                        <div class="col-md-4">
                            <label for="type_next_number" class="form-label">{{ __('config.next_number') }}</label>
                            <input type="number" class="form-control" id="type_next_number" name="next_number" 
                                   value="1" min="1">
                        </div>
                        
                        <div class="col-md-3">
                            <label for="type_color" class="form-label">{{ __('common.color') }} *</label>
                            <input type="color" class="form-control form-control-color" id="type_color" 
                                   name="color" value="#6366f1" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="type_icon" class="form-label">{{ __('common.icon') }}</label>
                            <div class="input-group">
                                <span class="input-group-text" id="icon-preview">
                                    <i class="bi bi-file-text" id="selected-icon"></i>
                                </span>
                                <select class="form-select" id="type_icon" name="icon">
                                    <option value="bi bi-file-text" data-icon="bi bi-file-text">{{ __('config.invoice_standard') }}</option>
                                    <option value="bi bi-receipt" data-icon="bi bi-receipt">{{ __('config.receipt') }}</option>
                                    <option value="bi bi-file-earmark-medical" data-icon="bi bi-file-earmark-medical">{{ __('config.medical_invoice') }}</option>
                                    <option value="bi bi-file-earmark-check" data-icon="bi bi-file-earmark-check">{{ __('config.validated_invoice') }}</option>
                                    <option value="bi bi-file-earmark-x" data-icon="bi bi-file-earmark-x">{{ __('config.cancelled_invoice') }}</option>
                                    <option value="bi bi-file-earmark-arrow-down" data-icon="bi bi-file-earmark-arrow-down">{{ __('config.credit_note') }}</option>
                                    <option value="bi bi-file-earmark-arrow-up" data-icon="bi bi-file-earmark-arrow-up">{{ __('config.debit_note') }}</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <label for="type_status" class="form-label">{{ __('common.status') }}</label>
                            <select class="form-select" id="type_status" name="is_active">
                                <option value="1">{{ __('common.active') }}</option>
                                <option value="0">{{ __('common.inactive') }}</option>
                            </select>
                        </div>
                        
                        <div class="col-md-12">
                            <label for="type_description" class="form-label">{{ __('common.description') }}</label>
                            <textarea class="form-control" id="type_description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="col-md-12">
                            <label class="form-label">{{ __('config.preview') }}:</label>
                            <div class="alert alert-light">
                                <strong id="formatPreview">INV-2024-00001</strong>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Invoice types data
const invoiceTypes = {{ invoiceTypes|default([])|json_encode|raw }};

// Update format preview
function updatePreview() {
    const format = document.getElementById('type_format').value;
    const prefix = document.getElementById('type_prefix').value || 'INV';
    const nextNumber = document.getElementById('type_next_number').value || '1';
    
    let preview = format;
    preview = preview.replace('{prefix}', prefix);
    preview = preview.replace('{year}', new Date().getFullYear());
    preview = preview.replace('{yy}', new Date().getFullYear().toString().substr(-2));
    preview = preview.replace('{month}', ('0' + (new Date().getMonth() + 1)).slice(-2));
    preview = preview.replace('{day}', ('0' + new Date().getDate()).slice(-2));
    
    // Handle number with padding
    const numberMatch = preview.match(/\{number:(\d+)\}/);
    if (numberMatch) {
        const padding = parseInt(numberMatch[1]);
        const paddedNumber = nextNumber.toString().padStart(padding, '0');
        preview = preview.replace(numberMatch[0], paddedNumber);
    } else {
        preview = preview.replace('{number}', nextNumber);
    }
    
    document.getElementById('formatPreview').textContent = preview;
}

// Event listeners for preview update
document.getElementById('type_format').addEventListener('input', updatePreview);
document.getElementById('type_prefix').addEventListener('input', updatePreview);
document.getElementById('type_next_number').addEventListener('input', updatePreview);

// Icon preview update
document.getElementById('type_icon').addEventListener('change', function() {
    const selectedIcon = this.value;
    document.getElementById('selected-icon').className = selectedIcon;
});

function editType(id) {
    const type = invoiceTypes.find(t => t.id === id);
    if (!type) return;
    
    document.getElementById('type_id').value = id;
    document.getElementById('type_name').value = type.name;
    document.getElementById('type_code').value = type.code;
    document.getElementById('type_prefix').value = type.prefix;
    document.getElementById('type_format').value = type.format;
    document.getElementById('type_next_number').value = type.next_number;
    document.getElementById('type_color').value = type.color;
    document.getElementById('type_icon').value = type.icon || 'bi bi-file-text';
    document.getElementById('type_status').value = type.is_active ? '1' : '0';
    document.getElementById('type_description').value = type.description || '';
    
    // Update icon preview
    document.getElementById('selected-icon').className = type.icon || 'bi bi-file-text';
    
    updatePreview();
    
    // Update modal for editing
    document.getElementById('typeModalTitle').textContent = '{{ __("config.edit_document_type") }}';
    document.getElementById('typeForm').action = '{{ base_url }}/config/invoice-types/' + id;
    document.getElementById('form_method').value = 'PUT';
    
    new bootstrap.Modal(document.getElementById('typeModal')).show();
}

function deleteType(id) {
    if (confirm('{{ __("config.delete_document_type_confirm") }}')) {
        fetch('{{ base_url }}/config/invoice-types/' + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
                '_method': 'DELETE',
                'csrf_token': '{{ csrf_token }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                toastr.success(data.message);
                // Remove the card from the page
                const card = document.querySelector(`[data-id="${id}"]`);
                if (card) {
                    card.remove();
                }
                // Check if no types left
                if (document.getElementById('typesContainer').children.length === 0) {
                    location.reload();
                }
            } else {
                toastr.error(data.message || 'Une erreur est survenue');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            toastr.error('Une erreur est survenue lors de la suppression');
        });
    }
}

// Reset modal when closed
document.getElementById('typeModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('typeForm').reset();
    document.getElementById('type_id').value = '';
    document.getElementById('form_method').value = 'POST';
    document.getElementById('typeModalTitle').textContent = '{{ __("config.add_document_type") }}';
    document.getElementById('typeForm').action = '{{ base_url }}/config/invoice-types';
    updatePreview();
});

// Initialize preview on load
updatePreview();
</script>
{% endblock %}

{% block scripts %}
<script>
// Invoice type form handler using our working pattern
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing invoice types form handler');
    
    const typeForm = document.getElementById('typeForm');
    if (!typeForm) {
        console.error('Invoice type form not found!');
        return;
    }
    
    // Override form submission completely
    typeForm.onsubmit = null;
    typeForm.addEventListener('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        console.log('Form submission intercepted');
        
        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        // Disable button immediately
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}...';
        
        // Use XMLHttpRequest to avoid browser extension conflicts
        const xhr = new XMLHttpRequest();
        xhr.open('POST', this.action, true);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        
        xhr.onload = function() {
            try {
                const data = JSON.parse(xhr.responseText);
                console.log('Response received:', data);
                
                if (data.success) {
                    // Show success message
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message);
                    }
                    
                    // Close modal
                    bootstrap.Modal.getInstance(document.getElementById('typeModal')).hide();
                    
                    // Reload page immediately to show updated data
                    window.location.reload();
                } else {
                    // Show error
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message || '{{ __("common.error_occurred") }}');
                    }
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = originalText;
                }
            } catch (e) {
                console.error('Parse error:', e);
                toastr.error('{{ __("common.error_occurred") }}');
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }
        };
        
        xhr.onerror = function() {
            console.error('XHR Error');
            toastr.error('{{ __("common.error_occurred") }}');
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        };
        
        // Send the request
        xhr.send(formData);
        
        return false;
    }, true); // Use capture phase
});
</script>
{% endblock %}