<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Helpers\MoneyHelper;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== LOC Invoice Rounding Fix ===\n\n";
    
    // Get all LOC invoices
    $stmt = $pdo->prepare("
        SELECT i.*, it.code as invoice_type_code, it.prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
        WHERE i.invoice_number LIKE '%LOC%'
           OR it.code = 'location'
           OR it.prefix LIKE '%LOC%'
        ORDER BY i.id DESC
    ");
    $stmt->execute();
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($invoices) . " LOC invoices\n\n";
    
    $fixedCount = 0;
    
    foreach ($invoices as $invoice) {
        $invoiceId = $invoice['id'];
        $currentTotal = floatval($invoice['total']);
        
        // Get invoice items
        $stmt = $pdo->prepare("
            SELECT * FROM invoice_items 
            WHERE invoice_id = ? 
            ORDER BY id
        ");
        $stmt->execute([$invoiceId]);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($items)) {
            continue; // Skip if no items
        }
        
        // Recalculate using TTC method
        $newSubtotal = 0;
        $newVat = 0;
        $newTotal = 0;
        
        foreach ($items as $item) {
            $quantity = floatval($item['quantity']);
            $unitPrice = floatval($item['unit_price']);
            $vatRate = floatval($item['vat_rate']);
            
            // Calculate TTC per unit
            $ttcPerUnit = $unitPrice * (1 + $vatRate / 100);
            $ttcPerUnit = MoneyHelper::round($ttcPerUnit);
            
            // Check if this should be a round number
            $roundedTTC = round($ttcPerUnit);
            if (abs($roundedTTC - $ttcPerUnit) < 0.10 && $roundedTTC % 5 == 0) {
                $ttcPerUnit = $roundedTTC;
            }
            
            // Calculate line total
            $lineTTC = $quantity * $ttcPerUnit;
            $ttcCalc = MoneyHelper::calculateFromTTC($lineTTC, $vatRate);
            
            $newSubtotal += $ttcCalc['net'];
            $newVat += $ttcCalc['vat'];
            $newTotal += $ttcCalc['total'];
            
            // Update invoice item if needed
            $itemNet = MoneyHelper::round($quantity * $unitPrice);
            if (abs($itemNet - $ttcCalc['net']) > 0.01 || 
                abs($item['vat_amount'] - $ttcCalc['vat']) > 0.01 ||
                abs($item['total_amount'] - $ttcCalc['total']) > 0.01) {
                
                $stmt = $pdo->prepare("
                    UPDATE invoice_items 
                    SET vat_amount = ?, 
                        total_amount = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$ttcCalc['vat'], $ttcCalc['total'], $item['id']]);
            }
        }
        
        // Check if invoice totals need updating
        $difference = abs($currentTotal - $newTotal);
        
        if ($difference > 0.01) {
            echo "Invoice #{$invoice['invoice_number']} (ID: $invoiceId)\n";
            echo "  Current total: €" . number_format($currentTotal, 2) . "\n";
            echo "  New total: €" . number_format($newTotal, 2) . "\n";
            echo "  Difference: €" . number_format($newTotal - $currentTotal, 2) . "\n";
            
            // Update invoice totals
            $stmt = $pdo->prepare("
                UPDATE invoices 
                SET subtotal = ?,
                    vat_amount = ?,
                    total = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$newSubtotal, $newVat, $newTotal, $invoiceId]);
            
            echo "  ✓ Fixed!\n\n";
            $fixedCount++;
        }
    }
    
    echo "Summary: Fixed $fixedCount invoices\n";
    
    // Special check for invoice 268
    if (isset($_GET['check']) || (isset($argv[1]) && $argv[1] == 'check')) {
        echo "\n=== Checking Invoice 268 ===\n";
        
        $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 268");
        $stmt->execute();
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($invoice) {
            echo "Invoice #" . $invoice['invoice_number'] . "\n";
            echo "Total: €" . number_format($invoice['total'], 2) . "\n";
            echo "Expected: €210.00\n";
            
            if (abs($invoice['total'] - 210.00) < 0.01) {
                echo "✓ Invoice 268 is correctly showing €210.00\n";
            } else {
                echo "⚠ Invoice 268 still needs fixing\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}