<?php
/**
 * Fix Dashboard Error - Comprehensive Diagnostic and Fix Tool
 */

// Enable full error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output
echo "<h1>Dashboard Error Fix Tool</h1>";
echo "<pre>";

// Step 1: Define APP_PATH
define('APP_PATH', dirname(__DIR__));
echo "Step 1: APP_PATH defined as: " . APP_PATH . "\n\n";

// Step 2: Load vendor autoload
echo "Step 2: Loading vendor autoload...\n";
if (!file_exists(APP_PATH . '/vendor/autoload.php')) {
    die('ERROR: Vendor autoload not found. Run composer install first.');
}
require APP_PATH . '/vendor/autoload.php';
echo "✓ Vendor autoload loaded\n\n";

// Step 3: Load .env
echo "Step 3: Loading environment variables...\n";
if (file_exists(APP_PATH . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(APP_PATH);
    $dotenv->load();
    echo "✓ .env file loaded\n";
    echo "  APP_DEBUG: " . ($_ENV['APP_DEBUG'] ?? 'not set') . "\n";
    echo "  APP_ENV: " . ($_ENV['APP_ENV'] ?? 'not set') . "\n\n";
} else {
    echo "⚠ .env file not found\n\n";
}

// Step 4: Start session
echo "Step 4: Starting session...\n";
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
echo "✓ Session started (ID: " . session_id() . ")\n\n";

// Step 5: Try to load bootstrap with error catching
echo "Step 5: Loading bootstrap...\n";
$bootstrap_error = null;
try {
    // Capture any output from bootstrap
    ob_start();
    require APP_PATH . '/app/config/bootstrap.php';
    $bootstrap_output = ob_get_clean();
    
    if ($bootstrap_output) {
        echo "Bootstrap output:\n" . $bootstrap_output . "\n";
    }
    echo "✓ Bootstrap loaded successfully\n\n";
} catch (Exception $e) {
    ob_end_clean();
    $bootstrap_error = $e;
    echo "✗ Bootstrap error: " . $e->getMessage() . "\n";
    echo "  File: " . $e->getFile() . "\n";
    echo "  Line: " . $e->getLine() . "\n\n";
}

// Step 6: Test database connection
echo "Step 6: Testing database connection...\n";
if (!$bootstrap_error) {
    try {
        $db = Flight::db();
        if ($db) {
            $stmt = $db->query("SELECT VERSION() as version");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "✓ Database connected (MySQL " . $result['version'] . ")\n";
            
            // Check users table
            $stmt = $db->query("SHOW TABLES LIKE 'users'");
            if ($stmt->fetch()) {
                echo "✓ Users table exists\n";
                
                // Check for admin user
                $stmt = $db->query("SELECT id, name, email FROM users WHERE role = 'admin' LIMIT 1");
                $admin = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($admin) {
                    echo "✓ Admin user found: " . $admin['name'] . " (" . $admin['email'] . ")\n";
                } else {
                    echo "⚠ No admin user found\n";
                }
            } else {
                echo "✗ Users table not found\n";
            }
        }
    } catch (Exception $e) {
        echo "✗ Database error: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Step 7: Check authentication
echo "Step 7: Checking authentication...\n";
if (isset($_SESSION['user'])) {
    echo "✓ User logged in: " . $_SESSION['user']['name'] . "\n";
} else {
    echo "✗ User not logged in\n";
    echo "  Creating test session...\n";
    
    // Create a test session
    $_SESSION['user'] = [
        'id' => 1,
        'name' => 'Admin User',
        'email' => '<EMAIL>',
        'role' => 'admin',
        'language' => 'fr'
    ];
    $_SESSION['user_id'] = 1;
    $_SESSION['user_language'] = 'fr';
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    
    echo "✓ Test session created\n";
}
echo "\n";

// Step 8: Test route loading
echo "Step 8: Testing route system...\n";
if (!$bootstrap_error) {
    try {
        // Check if home route exists
        $routes = Flight::router()->getRoutes();
        $home_route_found = false;
        foreach ($routes as $route) {
            if ($route->pattern == '/' || $route->pattern == '*') {
                $home_route_found = true;
                break;
            }
        }
        
        if ($home_route_found) {
            echo "✓ Home route registered\n";
        } else {
            echo "✗ Home route not found\n";
        }
        
        echo "  Total routes: " . count($routes) . "\n";
    } catch (Exception $e) {
        echo "✗ Route error: " . $e->getMessage() . "\n";
    }
    echo "\n";
}

// Step 9: Test controller loading
echo "Step 9: Testing controller system...\n";
if (!$bootstrap_error) {
    try {
        if (class_exists('\App\Controllers\DashboardController')) {
            echo "✓ DashboardController class exists\n";
            
            // Try to instantiate
            $controller = new \App\Controllers\DashboardController();
            echo "✓ DashboardController instantiated\n";
            
            // Check if DashboardService exists
            if (class_exists('\App\Services\DashboardService')) {
                echo "✓ DashboardService class exists\n";
            } else {
                echo "✗ DashboardService class not found\n";
            }
        } else {
            echo "✗ DashboardController class not found\n";
        }
    } catch (Exception $e) {
        echo "✗ Controller error: " . $e->getMessage() . "\n";
    }
}

echo "</pre>";

// Step 10: Summary and recommendations
echo "<hr>";
echo "<h2>Summary & Recommendations:</h2>";
echo "<ol>";

if ($bootstrap_error) {
    echo "<li style='color: red;'>Critical: Bootstrap failed to load. This must be fixed first.</li>";
    echo "<li>Error details: " . htmlspecialchars($bootstrap_error->getMessage()) . "</li>";
} else {
    echo "<li style='color: green;'>Bootstrap loaded successfully</li>";
}

if (!isset($_SESSION['user'])) {
    echo "<li style='color: orange;'>A test session has been created. Try accessing the dashboard now.</li>";
}

echo "<li><strong>Actions to try:</strong>";
echo "<ul>";
echo "<li><a href='/fit/public/'>Try Dashboard Now</a></li>";
echo "<li><a href='/fit/public/login'>Go to Login Page</a></li>";
echo "<li><a href='test_dashboard_error.php'>Run Extended Test</a></li>";
echo "<li><a href='debug_dashboard.php'>Run Debug Dashboard</a></li>";
echo "</ul></li>";

echo "</ol>";

// Add a manual override button
echo "<hr>";
echo "<h3>Manual Override Options:</h3>";
echo "<form method='post'>";
echo "<button type='submit' name='clear_cache' value='1'>Clear Twig Cache</button> ";
echo "<button type='submit' name='reset_session' value='1'>Reset Session</button> ";
echo "<button type='submit' name='disable_debug' value='1'>Toggle Debug Mode</button>";
echo "</form>";

// Handle manual overrides
if (isset($_POST['clear_cache'])) {
    $cache_dir = APP_PATH . '/storage/cache/twig';
    if (is_dir($cache_dir)) {
        $files = glob($cache_dir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }
        echo "<p style='color: green;'>Twig cache cleared!</p>";
    }
}

if (isset($_POST['reset_session'])) {
    session_destroy();
    session_start();
    echo "<p style='color: green;'>Session reset! Please <a href='fix_dashboard_error.php'>reload this page</a>.</p>";
}

if (isset($_POST['disable_debug'])) {
    // This is just for display, actual .env modification would need file writing
    $current_debug = $_ENV['APP_DEBUG'] ?? 'false';
    $new_debug = ($current_debug === 'true') ? 'false' : 'true';
    echo "<p style='color: blue;'>To change debug mode, edit .env file and set APP_DEBUG=" . $new_debug . "</p>";
}