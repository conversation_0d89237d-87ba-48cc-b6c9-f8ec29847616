<?php

namespace Tests\Phase3;

use PDO;
use Exception;

class Task_3_1_2_ConfigurableInvoiceTypesTest
{
    private $db;
    private $passed = 0;
    private $failed = 0;
    
    public function setUp(): void
    {
        try {
            // Database connection
            $this->db = new PDO('mysql:host=localhost;dbname=fitapp', 'root', 'test1234');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (Exception $e) {
            die("Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    private function assertEquals($expected, $actual, $message = '')
    {
        if ($expected == $actual) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: $expected, Got: $actual)");
        }
    }
    
    private function assertTrue($condition, $message = '')
    {
        if ($condition) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: true, Got: false)");
        }
    }
    
    private function assertContains($needle, $haystack, $message = '')
    {
        if (in_array($needle, $haystack)) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ('$needle' not found in array)");
        }
    }
    
    private function assertGreaterThan($expected, $actual, $message = '')
    {
        if ($actual > $expected) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ($actual not greater than $expected)");
        }
    }
    
    /**
     * Test 1: Check config_invoice_types table structure
     */
    public function testInvoiceTypesTableStructure()
    {
        $stmt = $this->db->query("SHOW TABLES LIKE 'config_invoice_types'");
        $this->assertEquals(1, $stmt->rowCount(), "config_invoice_types table should exist");
        
        // Check required columns
        $stmt = $this->db->query("SHOW COLUMNS FROM config_invoice_types");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = ['code', 'name', 'description', 'color', 'icon', 
                          'default_vat_rate', 'number_format', 'is_active', 'is_system'];
        
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist");
        }
        
        echo "✓ config_invoice_types table structure is correct\n";
    }
    
    /**
     * Test 2: Check invoice_sequences table
     */
    public function testInvoiceSequencesTable()
    {
        $stmt = $this->db->query("SHOW TABLES LIKE 'invoice_sequences'");
        $this->assertEquals(1, $stmt->rowCount(), "invoice_sequences table should exist");
        
        // Check columns
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_sequences");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = ['id', 'invoice_type_id', 'year', 'last_number'];
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in invoice_sequences");
        }
        
        // Check unique constraint
        $stmt = $this->db->query("SHOW INDEX FROM invoice_sequences WHERE Key_name = 'type_year'");
        $this->assertGreaterThan(0, $stmt->rowCount(), "Unique constraint 'type_year' should exist");
        
        echo "✓ invoice_sequences table structure is correct\n";
    }
    
    /**
     * Test 3: Check default invoice types
     */
    public function testDefaultInvoiceTypes()
    {
        $stmt = $this->db->query("SELECT code, is_system FROM config_invoice_types WHERE is_system = 1");
        $systemTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $expectedTypes = ['INV', 'CN', 'VCH', 'RET'];
        $foundTypes = array_column($systemTypes, 'code');
        
        foreach ($expectedTypes as $type) {
            $this->assertContains($type, $foundTypes, "System invoice type '$type' should exist");
        }
        
        echo "✓ All default invoice types are present\n";
        
        // Check specific properties
        $stmt = $this->db->prepare("SELECT * FROM config_invoice_types WHERE code = ?");
        $stmt->execute(['INV']);
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('#007bff', $invoice['color'], "Invoice color should be blue");
        $this->assertEquals('fas fa-file-invoice', $invoice['icon'], "Invoice icon should be correct");
        $this->assertTrue(strpos($invoice['number_format'], '{YEAR}') !== false, "Number format should contain {YEAR}");
        
        echo "✓ Invoice type properties are correct\n";
    }
    
    /**
     * Test 4: Test invoice type creation
     */
    public function testInvoiceTypeCreation()
    {
        // Create a custom invoice type
        $sql = "INSERT INTO config_invoice_types (code, name, description, color, icon, number_format, is_system) 
                VALUES (:code, :name, :description, :color, :icon, :number_format, :is_system)";
        
        $stmt = $this->db->prepare($sql);
        $result = $stmt->execute([
            ':code' => 'TST',
            ':name' => JSON_encode(['en' => 'Test Type', 'fr' => 'Type Test']),
            ':description' => JSON_encode(['en' => 'Test invoice type', 'fr' => 'Type de facture test']),
            ':color' => '#ff0000',
            ':icon' => 'fas fa-test',
            ':number_format' => '{YEAR}-TST-{NUMBER:4}',
            ':is_system' => 0
        ]);
        
        $this->assertTrue($result, "Custom invoice type should be created");
        
        // Verify creation
        $stmt = $this->db->prepare("SELECT * FROM config_invoice_types WHERE code = ?");
        $stmt->execute(['TST']);
        $type = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals('TST', $type['code']);
        $this->assertEquals(0, $type['is_system']);
        
        echo "✓ Custom invoice type created successfully\n";
        
        // Clean up
        $this->db->exec("DELETE FROM config_invoice_types WHERE code = 'TST'");
    }
    
    /**
     * Test 5: Test number generation per type
     */
    public function testNumberGenerationPerType()
    {
        // Get INV type
        $stmt = $this->db->prepare("SELECT id FROM config_invoice_types WHERE code = 'INV'");
        $stmt->execute();
        $type = $stmt->fetch(PDO::FETCH_ASSOC);
        $typeId = $type['id'];
        
        // Insert sequence for current year
        $year = date('Y');
        $sql = "INSERT INTO invoice_sequences (invoice_type_id, year, last_number) 
                VALUES (:type_id, :year, :last_number)
                ON DUPLICATE KEY UPDATE last_number = :last_number";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':type_id' => $typeId,
            ':year' => $year,
            ':last_number' => 10
        ]);
        
        // Simulate getting next number
        $stmt = $this->db->prepare("SELECT last_number FROM invoice_sequences WHERE invoice_type_id = ? AND year = ?");
        $stmt->execute([$typeId, $year]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals(10, $sequence['last_number']);
        
        // Update to next number
        $stmt = $this->db->prepare("UPDATE invoice_sequences SET last_number = last_number + 1 WHERE invoice_type_id = ? AND year = ?");
        $stmt->execute([$typeId, $year]);
        
        // Check increment
        $stmt = $this->db->prepare("SELECT last_number FROM invoice_sequences WHERE invoice_type_id = ? AND year = ?");
        $stmt->execute([$typeId, $year]);
        $sequence = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $this->assertEquals(11, $sequence['last_number']);
        
        echo "✓ Number generation per type works correctly\n";
    }
    
    /**
     * Test 6: Test year reset functionality
     */
    public function testYearResetSequence()
    {
        // Get INV type
        $stmt = $this->db->prepare("SELECT id FROM config_invoice_types WHERE code = 'INV'");
        $stmt->execute();
        $type = $stmt->fetch(PDO::FETCH_ASSOC);
        $typeId = $type['id'];
        
        // Set last number for 2024
        $sql = "INSERT INTO invoice_sequences (invoice_type_id, year, last_number) 
                VALUES (:type_id, :year, :last_number)
                ON DUPLICATE KEY UPDATE last_number = :last_number";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            ':type_id' => $typeId,
            ':year' => 2024,
            ':last_number' => 999
        ]);
        
        // Check 2024 sequence
        $stmt = $this->db->prepare("SELECT last_number FROM invoice_sequences WHERE invoice_type_id = ? AND year = ?");
        $stmt->execute([$typeId, 2024]);
        $seq2024 = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertEquals(999, $seq2024['last_number']);
        
        // Check 2025 sequence (should not exist or be different)
        $stmt = $this->db->prepare("SELECT last_number FROM invoice_sequences WHERE invoice_type_id = ? AND year = ?");
        $stmt->execute([$typeId, 2025]);
        $seq2025 = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($seq2025) {
            $this->assertTrue($seq2025['last_number'] != 999, "2025 sequence should be different from 2024");
        } else {
            $this->assertTrue(true, "2025 sequence doesn't exist yet (will start at 1)");
        }
        
        echo "✓ Year reset functionality works correctly\n";
    }
    
    /**
     * Test 7: Test number format patterns
     */
    public function testNumberFormatPatterns()
    {
        $stmt = $this->db->query("SELECT code, number_format FROM config_invoice_types");
        $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($types as $type) {
            // Check that number format contains required placeholders
            $format = $type['number_format'];
            
            if ($type['code'] == 'INV') {
                $this->assertTrue(strpos($format, '{YEAR}') !== false, "INV format should contain {YEAR}");
                $this->assertTrue(strpos($format, '{NUMBER') !== false, "INV format should contain {NUMBER}");
            }
            
            if ($type['code'] == 'CN') {
                $this->assertTrue(strpos($format, 'CN') !== false, "Credit note format should contain CN");
            }
            
            echo "✓ Number format for {$type['code']}: {$format}\n";
        }
    }
    
    /**
     * Run all tests and provide summary
     */
    public function runAllTests()
    {
        echo "\n=== Task 3.1.2: Configurable Invoice Types Tests ===\n\n";
        
        $tests = [
            'testInvoiceTypesTableStructure' => 'Checking invoice types table structure',
            'testInvoiceSequencesTable' => 'Checking invoice sequences table',
            'testDefaultInvoiceTypes' => 'Checking default invoice types',
            'testInvoiceTypeCreation' => 'Testing custom invoice type creation',
            'testNumberGenerationPerType' => 'Testing number generation per type',
            'testYearResetSequence' => 'Testing year reset functionality',
            'testNumberFormatPatterns' => 'Testing number format patterns'
        ];
        
        foreach ($tests as $method => $description) {
            echo "\n$description...\n";
            try {
                $this->$method();
            } catch (Exception $e) {
                echo "❌ FAILED: " . $e->getMessage() . "\n";
                continue;
            }
        }
        
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "Test Results: {$this->passed} passed, {$this->failed} failed\n";
        
        if ($this->failed === 0) {
            echo "\n✅ ALL TESTS PASSED for Task 3.1.2\n";
            echo "\nKey features verified:\n";
            echo "- Invoice types table with all required columns\n";
            echo "- Invoice sequences table with per-year tracking\n";
            echo "- Default system types (INV, CN, VCH, RET)\n";
            echo "- Custom invoice type creation\n";
            echo "- Number generation with year reset\n";
            echo "- Configurable number formats\n";
        } else {
            echo "\n❌ SOME TESTS FAILED\n";
            echo "Please check the error messages above and:\n";
            echo "1. Ensure migration 015_enhance_invoices_phase3.sql has been run\n";
            echo "2. Check that all ALTER TABLE statements succeeded\n";
            echo "3. Verify JSON columns are properly supported\n";
        }
        
        return $this->failed === 0;
    }
}

// Run tests if executed directly
if (php_sapi_name() === 'cli' && basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    $test = new Task_3_1_2_ConfigurableInvoiceTypesTest();
    $test->setUp();
    $test->runAllTests();
}