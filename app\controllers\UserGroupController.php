<?php

namespace App\Controllers;

use Flight;
use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Models\UserGroup;
use App\Models\Permission;
use App\Models\User;

class UserGroupController extends Controller
{
    /**
     * Display user groups list
     */
    public function index(Request $request, Response $response)
    {
        try {
            $groups = UserGroup::getAllWithCounts();
        } catch (\Exception $e) {
            $groups = [];
        }
        
        // Check for template preference
        $template = $this->getTemplate();
        $viewName = 'users/groups-modern';
        
        return $this->render($viewName, [
            'title' => __('users.group_management'),
            'groups' => $groups
        ]);
    }
    
    /**
     * Show create group form
     */
    public function create(Request $request, Response $response)
    {
        // Get all permissions for the form
        $permissions = $this->getPermissionsTree();
        
        $template = $this->getTemplate();
        $viewName = 'users/group-form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.create_group'),
            'permissions' => $permissions,
            'group' => null
        ]);
    }
    
    /**
     * Store new group
     */
    public function store(Request $request, Response $response)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get sanitized input data
            $data = [
                'name' => $this->input('name'),
                'description' => $this->input('description', ''),
                'color' => $this->input('color', '#0066CC'),
                'icon' => $this->input('icon', 'users'),
                'is_active' => $this->input('is_active', 0, FILTER_VALIDATE_BOOLEAN),
                'permissions' => $this->input('permissions', [], FILTER_DEFAULT)
            ];
            
            // Validate
            if (empty($data['name'])) {
                throw new \Exception(__('users.group_name_required'));
            }
            
            // Create group
            $groupId = UserGroup::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'color' => $data['color'] ?? '#0066CC',
                'icon' => $data['icon'] ?? 'users',
                'is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            // Assign permissions
            if (!empty($data['permissions']) && is_array($data['permissions'])) {
                UserGroup::syncPermissions($groupId, $data['permissions']);
            }
            
            // Check if request is AJAX
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => __('users.group_created'),
                    'data' => ['id' => $groupId]
                ]);
                exit;
            } else {
                // Normal form submission - redirect
                Flight::flash('success', __('users.group_created'));
                $this->redirect(Flight::get('flight.base_url') . '/users/groups');
                return;
            }
        } catch (\Exception $e) {
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                Flight::flash('error', $e->getMessage());
                $this->redirect(Flight::get('flight.base_url') . '/users/groups/create');
                return;
            }
        }
    }
    
    /**
     * Show edit form
     */
    public function edit(Request $request, Response $response, $id)
    {
        $group = UserGroup::getWithPermissions($id);
        
        if (!$group) {
            $this->redirect(Flight::get('flight.base_url') . '/users/groups');
            return;
        }
        
        $permissions = $this->getPermissionsTree();
        $groupPermissionIds = array_column($group['permissions'], 'id');
        
        $template = $this->getTemplate();
        $viewName = 'users/group-form-modern';
        
        return $this->render($viewName, [
            'title' => __('users.edit_group'),
            'group' => $group,
            'permissions' => $permissions,
            'groupPermissionIds' => $groupPermissionIds
        ]);
    }
    
    /**
     * Update group
     */
    public function update(Request $request, Response $response, $id)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            // Get sanitized input data
            $data = [
                'name' => $this->input('name'),
                'description' => $this->input('description', ''),
                'color' => $this->input('color', '#0066CC'),
                'icon' => $this->input('icon', 'users'),
                'is_active' => $this->input('is_active', 0, FILTER_VALIDATE_BOOLEAN),
                'permissions' => $this->input('permissions', [], FILTER_DEFAULT)
            ];
            
            // Update group
            UserGroup::update($id, [
                'name' => $data['name'],
                'description' => $data['description'] ?? '',
                'color' => $data['color'] ?? '#0066CC',
                'icon' => $data['icon'] ?? 'users',
                'is_active' => isset($data['is_active']) ? 1 : 0
            ]);
            
            // Update permissions
            $permissions = is_array($data['permissions']) ? $data['permissions'] : [];
            UserGroup::syncPermissions($id, $permissions);
            
            // Check if request is AJAX
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => __('users.group_updated')
                ]);
                exit;
            } else {
                // Normal form submission - redirect
                Flight::flash('success', __('users.group_updated'));
                $this->redirect(Flight::get('flight.base_url') . '/users/groups');
                return;
            }
        } catch (\Exception $e) {
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                Flight::flash('error', $e->getMessage());
                $this->redirect(Flight::get('flight.base_url') . '/users/groups/create');
                return;
            }
        }
    }
    
    /**
     * Delete group
     */
    public function delete(Request $request, Response $response, $id)
    {
        // Check if request is AJAX
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
        }
        
        try {
            UserGroup::delete($id);
            
            if ($isAjax) {
                echo json_encode([
                    'success' => true,
                    'message' => __('users.group_deleted')
                ]);
                exit;
            } else {
                // Normal form submission - redirect
                Flight::flash('success', __('users.group_deleted'));
                $this->redirect(Flight::get('flight.base_url') . '/users/groups');
                return;
            }
        } catch (\Exception $e) {
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                Flight::flash('error', $e->getMessage());
                $this->redirect(Flight::get('flight.base_url') . '/users/groups/create');
                return;
            }
        }
    }
    
    /**
     * Manage group members
     */
    public function members(Request $request, Response $response, $id)
    {
        $group = UserGroup::find($id);
        if (!$group) {
            $this->redirect(Flight::get('flight.base_url') . '/users/groups');
            return;
        }
        
        $members = UserGroup::getMembers($id);
        
        // Get all users for adding
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT u.* 
            FROM users u
            WHERE u.id NOT IN (
                SELECT user_id FROM user_group_members WHERE group_id = :group_id
            )
            ORDER BY u.first_name, u.last_name
        ");
        $stmt->execute(['group_id' => $id]);
        $availableUsers = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $template = $this->getTemplate();
        $viewName = 'users/group-members-modern';
        
        return $this->render($viewName, [
            'title' => __('users.group_members', ['name' => $group['name']]),
            'group' => $group,
            'members' => $members,
            'availableUsers' => $availableUsers
        ]);
    }
    
    /**
     * Add member to group
     */
    public function addMember(Request $request, Response $response, $groupId)
    {
        header('Content-Type: application/json');
        
        try {
            // Check CSRF token
            $this->checkCsrfToken();
            
            $userId = $this->input('user_id', null, FILTER_VALIDATE_INT);
            
            if (!$userId) {
                throw new \Exception(__('users.user_id_required'));
            }
            
            UserGroup::addMember($groupId, $userId, $_SESSION['user_id']);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.member_added')
            ]);
            exit;
        } catch (\Exception $e) {
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                Flight::flash('error', $e->getMessage());
                $this->redirect(Flight::get('flight.base_url') . '/users/groups/create');
                return;
            }
        }
    }
    
    /**
     * Remove member from group
     */
    public function removeMember(Request $request, Response $response, $groupId, $userId)
    {
        header('Content-Type: application/json');
        
        try {
            UserGroup::removeMember($groupId, $userId);
            
            echo json_encode([
                'success' => true,
                'message' => __('users.member_removed')
            ]);
            exit;
        } catch (\Exception $e) {
            $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                      strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
            
            if ($isAjax) {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => $e->getMessage()
                ]);
                exit;
            } else {
                Flight::flash('error', $e->getMessage());
                $this->redirect(Flight::get('flight.base_url') . '/users/groups/create');
                return;
            }
        }
    }
    
    /**
     * Get permissions organized by category
     */
    private function getPermissionsTree()
    {
        $db = Flight::db();
        $stmt = $db->query("
            SELECT * FROM permissions 
            ORDER BY category, sort_order, name
        ");
        $permissions = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        // Organize by category
        $tree = [];
        foreach ($permissions as $permission) {
            $category = $permission['category'];
            if (!isset($tree[$category])) {
                $tree[$category] = [
                    'name' => ucfirst($category),
                    'permissions' => []
                ];
            }
            $tree[$category]['permissions'][] = $permission;
        }
        
        return $tree;
    }
}