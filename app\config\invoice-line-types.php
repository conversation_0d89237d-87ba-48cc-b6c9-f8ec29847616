<?php

/**
 * Invoice Line Types Configuration
 * 
 * Define available line types for invoice templates
 * Key: database value
 * Value: translation key
 */

return [
    'rent' => 'config.line_type_rent',
    'charges' => 'config.line_type_charges', 
    'secretariat' => 'config.line_type_secretariat',
    'service' => 'config.line_type_service',
    'cns_part' => 'config.line_type_cns_part',
    'patient_part' => 'config.line_type_patient_part',
    'hourly_rental' => 'config.line_type_hourly_rental',
    
    // Add new line types below
    // 'maintenance' => 'config.line_type_maintenance',
    // 'utilities' => 'config.line_type_utilities',
    // 'consulting' => 'config.line_type_consulting',
];