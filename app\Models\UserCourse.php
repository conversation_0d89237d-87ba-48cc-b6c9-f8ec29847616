<?php

namespace App\Models;

use App\Core\Model;
use PDO;
use Flight;
use Exception;

class UserCourse extends Model
{
    protected $table = 'user_courses';
    protected $fillable = [
        'user_id', 'course_name', 'course_description', 
        'hourly_rate', 'vat_rate', 'display_order', 'is_active'
    ];
    /**
     * Get all courses for a user
     */
    public static function getByUserId($userId)
    {
        $db = Flight::db();
        // Debug logging (disabled - enable if needed)
        // error_log("=== UserCourse::getByUserId Debug ===");
        // error_log("Fetching courses for user ID: " . $userId);
        
        $stmt = $db->prepare("
            SELECT * FROM user_courses 
            WHERE user_id = :user_id 
            ORDER BY display_order ASC, course_name ASC
        ");
        $stmt->execute(['user_id' => $userId]);
        $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // error_log("Found " . count($courses) . " courses");
        // error_log("Course IDs: " . implode(', ', array_column($courses, 'id')));
        
        return $courses;
    }
    
    /**
     * Get active courses for a user
     */
    public static function getActiveByUserId($userId)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT * FROM user_courses 
            WHERE user_id = :user_id AND is_active = 1
            ORDER BY display_order ASC, course_name ASC
        ");
        $stmt->execute(['user_id' => $userId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Find a course by ID
     */
    public static function find($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM user_courses WHERE id = :id");
        $stmt->execute(['id' => $id]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // Convert to object for property access
            return (object) $result;
        }
        return null;
    }
    
    /**
     * Create a new course for a user
     */
    public static function createCourse($data)
    {
        $db = Flight::db();
        
        // Get the next display order
        $stmt = $db->prepare("SELECT MAX(display_order) as max_order FROM user_courses WHERE user_id = :user_id");
        $stmt->execute(['user_id' => $data['user_id']]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $maxOrder = $result['max_order'] ?? -1;
        
        $data['display_order'] = $maxOrder + 1;
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $stmt = $db->prepare("
            INSERT INTO user_courses (user_id, course_name, hourly_rate, vat_rate, is_active, display_order, created_at, updated_at)
            VALUES (:user_id, :course_name, :hourly_rate, :vat_rate, :is_active, :display_order, :created_at, :updated_at)
        ");
        
        if ($stmt->execute($data)) {
            $data['id'] = $db->lastInsertId();
            return $data;
        }
        
        // Get the actual error message
        $errorInfo = $stmt->errorInfo();
        error_log("UserCourse::createCourse SQL Error: " . json_encode($errorInfo));
        
        // Check for duplicate entry error
        if ($errorInfo[1] == 1062) {
            throw new Exception('Un cours avec ce nom existe déjà pour cet utilisateur');
        }
        
        throw new Exception('Failed to create course: ' . $errorInfo[2]);
    }
    
    /**
     * Update course
     */
    public static function updateCourse($id, $data)
    {
        $db = Flight::db();
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $stmt = $db->prepare("
            UPDATE user_courses 
            SET course_name = :course_name,
                hourly_rate = :hourly_rate,
                vat_rate = :vat_rate,
                is_active = :is_active,
                updated_at = :updated_at
            WHERE id = :id
        ");
        
        $data['id'] = $id;
        return $stmt->execute($data);
    }
    
    /**
     * Delete a course (renamed to avoid conflict with parent)
     */
    public static function deleteCourse($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("DELETE FROM user_courses WHERE id = :id");
        return $stmt->execute(['id' => $id]);
    }
    
    /**
     * Update display order for multiple courses
     */
    public static function updateDisplayOrder($userId, $orderData)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            foreach ($orderData as $order => $courseId) {
                $stmt = $db->prepare("
                    UPDATE user_courses 
                    SET display_order = :order, updated_at = NOW()
                    WHERE id = :id AND user_id = :user_id
                ");
                $stmt->execute([
                    ':order' => $order,
                    ':id' => $courseId,
                    ':user_id' => $userId
                ]);
            }
            
            $db->commit();
            return true;
        } catch (Exception $e) {
            $db->rollBack();
            error_log('Error updating course display order: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Calculate TTC price from HT price and VAT rate
     */
    public static function calculateTTC($priceHT, $vatRate)
    {
        return $priceHT * (1 + $vatRate / 100);
    }
    
    /**
     * Calculate HT price from TTC price and VAT rate
     */
    public static function calculateHT($priceTTC, $vatRate)
    {
        return $priceTTC / (1 + $vatRate / 100);
    }
}