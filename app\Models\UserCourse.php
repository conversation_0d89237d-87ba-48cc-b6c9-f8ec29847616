<?php

namespace App\Models;

use App\Core\Model;
use Flight;
use PDO;
use Exception;

class UserCourse extends Model
{
    protected $table = 'user_courses';
    
    protected $fillable = [
        'user_id',
        'course_name',
        'hourly_rate',
        'vat_rate',
        'is_active',
        'display_order'
    ];
    
    protected $casts = [
        'hourly_rate' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'display_order' => 'integer'
    ];
    
    /**
     * Get all courses for a specific user
     */
    public static function getCoursesForUser($userId, $activeOnly = true)
    {
        $db = Flight::db();
        
        $sql = "
            SELECT uc.*, u.is_intracommunity, u.vat_number
            FROM user_courses uc
            JOIN users u ON uc.user_id = u.id
            WHERE uc.user_id = ?
        ";
        
        if ($activeOnly) {
            $sql .= " AND uc.is_active = 1";
        }
        
        $sql .= " ORDER BY uc.display_order ASC, uc.course_name ASC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get courses formatted for invoice creation
     */
    public static function getCoursesForInvoicing($userId)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                uc.id,
                uc.course_name,
                uc.hourly_rate,
                CASE 
                    WHEN u.is_intracommunity = 1 THEN 0.00 
                    ELSE uc.vat_rate 
                END as effective_vat_rate,
                uc.vat_rate as base_vat_rate,
                u.is_intracommunity,
                u.vat_number
            FROM user_courses uc
            JOIN users u ON uc.user_id = u.id
            WHERE uc.user_id = ? 
              AND uc.is_active = 1
            ORDER BY uc.display_order ASC, uc.course_name ASC
        ");
        
        $stmt->execute([$userId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Create a new course for a user
     */
    public static function createCourse($data)
    {
        $db = Flight::db();
        
        // Get user's intracommunity status
        $stmt = $db->prepare("SELECT is_intracommunity FROM users WHERE id = ?");
        $stmt->execute([$data['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Set VAT rate based on intracommunity status
        if ($user && $user['is_intracommunity']) {
            $data['vat_rate'] = 0.00;
        } else if (!isset($data['vat_rate'])) {
            $data['vat_rate'] = 17.00;
        }
        
        // Set display order if not provided
        if (!isset($data['display_order'])) {
            $stmt = $db->prepare("SELECT COALESCE(MAX(display_order), 0) + 1 FROM user_courses WHERE user_id = ?");
            $stmt->execute([$data['user_id']]);
            $data['display_order'] = $stmt->fetchColumn();
        }
        
        $stmt = $db->prepare("
            INSERT INTO user_courses (
                user_id, course_name, hourly_rate, vat_rate, 
                is_active, display_order, created_at, updated_at
            ) VALUES (
                :user_id, :course_name, :hourly_rate, :vat_rate,
                :is_active, :display_order, NOW(), NOW()
            )
        ");
        
        return $stmt->execute([
            ':user_id' => $data['user_id'],
            ':course_name' => $data['course_name'],
            ':hourly_rate' => $data['hourly_rate'],
            ':vat_rate' => $data['vat_rate'],
            ':is_active' => $data['is_active'] ?? true,
            ':display_order' => $data['display_order']
        ]);
    }
    
    /**
     * Update a course
     */
    public static function updateCourse($id, $data)
    {
        $db = Flight::db();
        
        // Get course and user info
        $stmt = $db->prepare("
            SELECT uc.*, u.is_intracommunity 
            FROM user_courses uc
            JOIN users u ON uc.user_id = u.id
            WHERE uc.id = ?
        ");
        $stmt->execute([$id]);
        $course = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$course) {
            throw new Exception("Course not found");
        }
        
        // Adjust VAT rate based on intracommunity status
        if ($course['is_intracommunity'] && isset($data['vat_rate'])) {
            $data['vat_rate'] = 0.00;
        }
        
        $updateFields = [];
        $updateValues = [];
        
        foreach (['course_name', 'hourly_rate', 'vat_rate', 'is_active', 'display_order'] as $field) {
            if (isset($data[$field])) {
                $updateFields[] = "{$field} = ?";
                $updateValues[] = $data[$field];
            }
        }
        
        if (empty($updateFields)) {
            return true;
        }
        
        $updateFields[] = "updated_at = NOW()";
        $updateValues[] = $id;
        
        $sql = "UPDATE user_courses SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $db->prepare($sql);
        
        return $stmt->execute($updateValues);
    }
    
    /**
     * Delete a course
     */
    public static function deleteCourse($id)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("DELETE FROM user_courses WHERE id = ?");
        return $stmt->execute([$id]);
    }
    
    /**
     * Update VAT rates for all courses of a user (when intracommunity status changes)
     */
    public static function updateVATRatesForUser($userId, $isIntracommunity)
    {
        $db = Flight::db();
        
        $newVatRate = $isIntracommunity ? 0.00 : 17.00;
        
        $stmt = $db->prepare("
            UPDATE user_courses 
            SET vat_rate = ?, updated_at = NOW() 
            WHERE user_id = ?
        ");
        
        return $stmt->execute([$newVatRate, $userId]);
    }
    
    /**
     * Reorder courses for a user
     */
    public static function reorderCourses($userId, $courseOrders)
    {
        $db = Flight::db();
        
        try {
            $db->beginTransaction();
            
            foreach ($courseOrders as $courseId => $order) {
                $stmt = $db->prepare("
                    UPDATE user_courses 
                    SET display_order = ?, updated_at = NOW() 
                    WHERE id = ? AND user_id = ?
                ");
                $stmt->execute([$order, $courseId, $userId]);
            }
            
            $db->commit();
            return true;
            
        } catch (Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get course by ID
     */
    public static function getCourseById($id)
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT uc.*, u.is_intracommunity, u.vat_number
            FROM user_courses uc
            JOIN users u ON uc.user_id = u.id
            WHERE uc.id = ?
        ");
        
        $stmt->execute([$id]);
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Calculate totals for invoice creation
     */
    public static function calculateInvoiceTotal($courseId, $hours)
    {
        $course = self::getCourseById($courseId);
        
        if (!$course) {
            throw new Exception("Course not found");
        }
        
        $subtotal = $course['hourly_rate'] * $hours;
        $vatRate = $course['is_intracommunity'] ? 0.00 : $course['vat_rate'];
        $vatAmount = $subtotal * ($vatRate / 100);
        $total = $subtotal + $vatAmount;
        
        return [
            'course' => $course,
            'hours' => $hours,
            'subtotal' => round($subtotal, 2),
            'vat_rate' => $vatRate,
            'vat_amount' => round($vatAmount, 2),
            'total' => round($total, 2)
        ];
    }
    
    /**
     * Get users who have active courses (for coach selection)
     */
    public static function getUsersWithCourses()
    {
        $db = Flight::db();
        
        $stmt = $db->prepare("
            SELECT DISTINCT 
                u.id,
                u.first_name,
                u.last_name,
                u.vat_number,
                u.is_intracommunity,
                COUNT(uc.id) as course_count
            FROM users u
            JOIN user_courses uc ON u.id = uc.user_id
            WHERE uc.is_active = 1
            GROUP BY u.id, u.first_name, u.last_name, u.vat_number, u.is_intracommunity
            ORDER BY u.last_name ASC, u.first_name ASC
        ");
        
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}