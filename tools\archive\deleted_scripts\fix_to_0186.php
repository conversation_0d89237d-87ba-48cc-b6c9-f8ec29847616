<?php
// Fix invoice number to FAC-LOY-2025-0186

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Simple .env loader
function loadEnv($path) {
    if (!file_exists($path)) {
        throw new Exception(".env file not found at: $path");
    }
    
    $lines = file($path, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        if (strpos($line, '=') === false) continue;
        
        list($key, $value) = explode('=', $line, 2);
        $key = trim($key);
        $value = trim($value);
        
        // Remove quotes if present
        if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
            (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
            $value = substr($value, 1, -1);
        }
        
        putenv("$key=$value");
        $_ENV[$key] = $value;
    }
}

try {
    // Load .env file
    loadEnv(__DIR__ . '/../.env');
    
    // Get database config from env
    $host = $_ENV['DB_HOST'] ?? 'localhost';
    $dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $username = $_ENV['DB_USERNAME'] ?? 'root';
    $password = $_ENV['DB_PASSWORD'] ?? '';
    
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Fix Invoice Number to FAC-LOY-2025-0186</h2>";
    
    // Find invoice FAC-2025-0187
    $stmt = $db->prepare("
        SELECT i.*, it.prefix as type_prefix
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.type_id = it.id
        WHERE i.invoice_number = 'FAC-2025-0187'
    ");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "<p style='color: red;'>Invoice FAC-2025-0187 not found!</p>";
        exit;
    }
    
    echo "<h3>Current Invoice Details</h3>";
    echo "<table border='1' cellpadding='5'>";
    echo "<tr><th>Field</th><th>Value</th></tr>";
    echo "<tr><td>Invoice ID</td><td>" . $invoice['id'] . "</td></tr>";
    echo "<tr><td>Current Number</td><td><strong>" . $invoice['invoice_number'] . "</strong></td></tr>";
    echo "<tr><td>Type ID</td><td>" . ($invoice['type_id'] ?? 'NULL') . "</td></tr>";
    echo "<tr><td>Type Prefix</td><td><strong>" . ($invoice['type_prefix'] ?? 'None') . "</strong></td></tr>";
    echo "<tr><td>Status</td><td>" . $invoice['status'] . "</td></tr>";
    echo "</table>";
    
    // Check if 0186 already exists
    $stmt = $db->prepare("SELECT id FROM invoices WHERE invoice_number = 'FAC-LOY-2025-0186'");
    $stmt->execute();
    if ($stmt->fetch()) {
        echo "<p style='color: red;'>❌ Invoice FAC-LOY-2025-0186 already exists! Cannot update.</p>";
        exit;
    }
    
    // Begin transaction
    $db->beginTransaction();
    
    try {
        // 1. Update invoice number to FAC-LOY-2025-0186
        $newNumber = 'FAC-LOY-2025-0186';
        $stmt = $db->prepare("UPDATE invoices SET invoice_number = ? WHERE id = ?");
        $stmt->execute([$newNumber, $invoice['id']]);
        echo "<h3 style='color: green;'>✓ Updated invoice number to: " . $newNumber . "</h3>";
        
        // 2. Reset sequence to 185 (so next invoice will be 0186 if needed)
        $stmt = $db->prepare("
            UPDATE document_sequences 
            SET last_number = 185 
            WHERE document_type_id = 1 AND year = 2025
        ");
        $stmt->execute();
        echo "<p>✓ Reset sequence to 185 (next invoice will be 0186)</p>";
        
        // 3. Add 0187 to deleted pool since we're not using it
        $stmt = $db->prepare("
            INSERT IGNORE INTO deleted_invoice_numbers (
                document_type_id, invoice_number, year, sequence_number, deleted_by
            ) VALUES 
            (1, 'FAC-2025-0187', 2025, 187, 1),
            (1, 'FAC-LOY-2025-0187', 2025, 187, 1)
        ");
        $stmt->execute();
        echo "<p>✓ Added 0187 to deleted numbers pool</p>";
        
        // 4. Remove 0186 from deleted pool if it's there
        $stmt = $db->prepare("
            DELETE FROM deleted_invoice_numbers 
            WHERE invoice_number IN ('FAC-2025-0186', 'FAC-LOY-2025-0186')
        ");
        $stmt->execute();
        echo "<p>✓ Removed 0186 from deleted pool (now in use)</p>";
        
        $db->commit();
        
        echo "<h2 style='color: green; background: #e8f5e9; padding: 20px; border-radius: 5px;'>";
        echo "✅ SUCCESS!<br>";
        echo "Invoice updated to: FAC-LOY-2025-0186<br>";
        echo "Sequence reset to: 185";
        echo "</h2>";
        
        echo "<br>";
        echo "<a href='/fit/public/invoices/" . $invoice['id'] . "' style='font-size: 16px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;'>View Updated Invoice</a>";
        echo "<a href='check_invoice_sequences.php' style='font-size: 16px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>Check Sequences</a>";
        
    } catch (Exception $e) {
        $db->rollBack();
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    
} catch (Exception $e) {
    echo "<strong>Error:</strong> " . $e->getMessage();
}
?>