<?php

namespace Tests\Phase3;

use PHPUnit\Framework\TestCase;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\InvoicePayment;
use App\Models\Client;
use App\Models\ConfigPaymentTerm;
use App\Helpers\MoneyHelper;
use Flight;

class InvoiceModelTest extends TestCase
{
    protected static $db;
    protected $invoice;
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        try {
            // First delete any payments that might reference our test clients
            self::$db->exec("DELETE FROM payments WHERE client_id IN (SELECT id FROM clients WHERE client_number LIKE 'TEST-%')");
        } catch (\Exception $e) {
            // Table might not exist
        }
        
        self::$db->exec("DELETE FROM invoice_payments WHERE invoice_id IN (SELECT id FROM invoices WHERE invoice_number LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM invoice_items WHERE invoice_id IN (SELECT id FROM invoices WHERE invoice_number LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM invoices WHERE invoice_number LIKE 'TEST-%'");
        self::$db->exec("DELETE FROM clients WHERE client_number LIKE 'TEST-%'");
        
        // Ensure we have at least one VAT rate for testing
        $stmt = self::$db->query("SELECT id FROM config_vat_rates WHERE is_active = 1 LIMIT 1");
        if (!$stmt->fetch()) {
            // Insert a default VAT rate if none exists
            self::$db->exec("INSERT INTO config_vat_rates (code, rate, description, is_default, is_active) VALUES ('TEST17', 17.00, 'Test VAT 17%', 1, 1)");
        }
    }
    
    public function setUp(): void
    {
        $this->invoice = new Invoice();
    }
    
    public function testCreateInvoice()
    {
        // Create a test client first
        $client = Client::create([
            'client_number' => 'TEST-001',
            'first_name' => 'Test',
            'last_name' => 'Client',
            'email' => '<EMAIL>',
            'is_active' => 1
        ]);
        
        // Create invoice
        $invoiceData = [
            'invoice_number' => 'TEST-INV-001',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => $client->id,
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'status' => Invoice::STATUS_DRAFT,
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00,
            'notes' => 'Test invoice',
            'created_by' => 1
        ];
        
        $invoice = Invoice::create($invoiceData);
        
        $this->assertNotNull($invoice);
        $this->assertNotNull($invoice->id);
        $this->assertEquals('TEST-INV-001', $invoice->invoice_number);
        $this->assertEquals(117.00, $invoice->total);
        $this->assertEquals(Invoice::STATUS_DRAFT, $invoice->status);
        
        return $invoice;
    }
    
    public function testAddInvoiceItems()
    {
        // Get a valid VAT rate ID for testing
        $stmt = self::$db->query("SELECT id FROM config_vat_rates WHERE is_active = 1 LIMIT 1");
        $vatRate = $stmt->fetch(\PDO::FETCH_ASSOC);
        $vatRateId = $vatRate ? $vatRate['id'] : null;
        
        // Create a test invoice first
        $client = Client::create([
            'client_number' => 'TEST-ITEMS-001',
            'first_name' => 'Test Items',
            'last_name' => 'Client',
            'email' => '<EMAIL>',
            'is_active' => 1
        ]);
        
        $invoice = Invoice::create([
            'invoice_number' => 'TEST-INV-ITEMS',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => $client->id,
            'issue_date' => date('Y-m-d'),
            'status' => Invoice::STATUS_DRAFT,
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00
        ]);
        
        $item1 = InvoiceItem::create([
            'invoice_id' => $invoice->id,
            'description' => 'Test Service 1',
            'quantity' => 2,
            'unit_price' => 25.00,
            'vat_rate_id' => $vatRateId,
            'vat_rate' => 17,
            'vat_amount' => 8.50,
            'total_amount' => 58.50
        ]);
        
        $item2 = InvoiceItem::create([
            'invoice_id' => $invoice->id,
            'description' => 'Test Service 2',
            'quantity' => 1,
            'unit_price' => 50.00,
            'vat_rate_id' => $vatRateId,
            'vat_rate' => 17,
            'vat_amount' => 8.50,
            'total_amount' => 58.50
        ]);
        
        $this->assertNotNull($item1->id);
        $this->assertNotNull($item2->id);
        
        // Test getting items
        $items = InvoiceItem::where('invoice_id', '=', $invoice->id)->get();
        $this->assertCount(2, $items);
    }
    
    public function testInvoiceCalculations()
    {
        // Create test invoice with items
        $client = Client::create([
            'client_number' => 'TEST-CALC-001',
            'first_name' => 'Test Calc',
            'last_name' => 'Client',
            'email' => '<EMAIL>',
            'is_active' => 1
        ]);
        
        $invoice = Invoice::create([
            'invoice_number' => 'TEST-INV-CALC',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => $client->id,
            'issue_date' => date('Y-m-d'),
            'status' => Invoice::STATUS_DRAFT,
            'subtotal' => 0,
            'vat_amount' => 0,
            'total' => 0
        ]);
        
        // Create items with proper calculation using MoneyHelper
        $item1 = new InvoiceItem();
        $item1->invoice_id = $invoice->id;
        $item1->description = 'Item 1';
        $item1->quantity = 2;
        $item1->unit_price = 25.00;
        $item1->vat_rate = 17;
        $item1->calculateTotals();
        $item1->save();
        
        $item2 = new InvoiceItem();
        $item2->invoice_id = $invoice->id;
        $item2->description = 'Item 2';
        $item2->quantity = 1;
        $item2->unit_price = 50.00;
        $item2->vat_rate = 17;
        $item2->calculateTotals();
        $item2->save();
        
        // Test recalculating totals
        $invoice->recalculateTotals();
        
        // With MoneyHelper rounding:
        // Item 1: 2 * 25.00 = 50.00, VAT = 8.50, Total = 58.50
        // Item 2: 1 * 50.00 = 50.00, VAT = 8.50, Total = 58.50
        // Total: 100.00 + 17.00 = 117.00
        $this->assertEquals(100.00, $invoice->subtotal);
        $this->assertEquals(17.00, $invoice->vat_amount);
        $this->assertEquals(117.00, $invoice->total);
    }
    
    public function testGenerateDocumentNumber()
    {
        // Test document number generation
        $docTypeId = 1; // Assuming standard invoice type
        
        $number1 = $this->invoice->generateDocumentNumber($docTypeId);
        $this->assertNotEmpty($number1);
        $this->assertStringContainsString(date('Y'), $number1);
        
        // Generate another number - should be sequential
        $invoice2 = new Invoice();
        $number2 = $invoice2->generateDocumentNumber($docTypeId);
        $this->assertNotEmpty($number2);
        $this->assertNotEquals($number1, $number2);
    }
    
    public function testInvoiceStatusTransitions()
    {
        $invoice = Invoice::create([
            'invoice_number' => 'TEST-STATUS-001',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => 1,
            'issue_date' => date('Y-m-d'),
            'status' => Invoice::STATUS_DRAFT,
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00
        ]);
        
        // Test status transitions
        $this->assertEquals(Invoice::STATUS_DRAFT, $invoice->status);
        
        // Send invoice
        $invoice->status = Invoice::STATUS_SENT;
        $invoice->save();
        $this->assertEquals(Invoice::STATUS_SENT, $invoice->status);
        
        // Mark as paid
        $invoice->status = Invoice::STATUS_PAID;
        $invoice->save();
        $this->assertEquals(Invoice::STATUS_PAID, $invoice->status);
    }
    
    public function testInvoicePayments()
    {
        $invoice = Invoice::create([
            'invoice_number' => 'TEST-PAY-001',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => 1,
            'issue_date' => date('Y-m-d'),
            'status' => Invoice::STATUS_SENT,
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00
        ]);
        
        // Add partial payment
        $payment1 = InvoicePayment::create([
            'invoice_id' => $invoice->id,
            'payment_date' => date('Y-m-d'),
            'amount' => 50.00,
            'payment_method' => 'bank_transfer',
            'reference' => 'TEST-REF-001'
        ]);
        
        $this->assertNotNull($payment1->id);
        
        // Update invoice paid amount
        $invoice->updatePaidAmount();
        $this->assertEquals(50.00, $invoice->paid_amount);
        $this->assertEquals(Invoice::STATUS_PARTIAL, $invoice->status);
        
        // Add remaining payment
        $payment2 = InvoicePayment::create([
            'invoice_id' => $invoice->id,
            'payment_date' => date('Y-m-d'),
            'amount' => 67.00,
            'payment_method' => 'bank_transfer',
            'reference' => 'TEST-REF-002'
        ]);
        
        // Update invoice paid amount
        $invoice->updatePaidAmount();
        $this->assertEquals(117.00, $invoice->paid_amount);
        $this->assertEquals(Invoice::STATUS_PAID, $invoice->status);
    }
    
    public function testInvoiceDueDates()
    {
        // Create a payment term first
        $paymentTerm = ConfigPaymentTerm::create([
            'code' => 'TEST-30DAYS',
            'name' => json_encode(['en' => '30 Days']),
            'days' => 30,
            'is_active' => 1
        ]);
        
        $invoice = new Invoice();
        
        // Test with payment term ID
        $invoice->issue_date = '2024-01-01';
        $invoice->payment_term_id = $paymentTerm->id;
        
        $dueDate = $invoice->calculateDueDate();
        $this->assertEquals('2024-01-31', $dueDate);
        
        // Test overdue status
        $overdueInvoice = Invoice::create([
            'invoice_number' => 'TEST-OVERDUE-001',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => 1,
            'issue_date' => date('Y-m-d', strtotime('-60 days')),
            'due_date' => date('Y-m-d', strtotime('-30 days')),
            'status' => Invoice::STATUS_SENT,
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00
        ]);
        
        $this->assertTrue($overdueInvoice->isOverdue());
    }
    
    public function testCreditNotes()
    {
        // Create original invoice
        $originalInvoice = Invoice::create([
            'invoice_number' => 'TEST-ORIG-001',
            'document_type_id' => 1,
            'billable_type' => 'client',
            'billable_id' => 1,
            'issue_date' => date('Y-m-d'),
            'status' => Invoice::STATUS_PAID,
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00
        ]);
        
        // Create credit note
        $creditNote = Invoice::create([
            'invoice_number' => 'TEST-CN-001',
            'document_type_id' => 2, // Assuming credit note type
            'billable_type' => 'client',
            'billable_id' => 1,
            'issue_date' => date('Y-m-d'),
            'status' => Invoice::STATUS_SENT,
            'subtotal' => -50.00,
            'vat_amount' => -8.50,
            'total' => -58.50,
            'reference_invoice_id' => $originalInvoice->id
        ]);
        
        $this->assertEquals(-58.50, $creditNote->total);
        $this->assertEquals($originalInvoice->id, $creditNote->reference_invoice_id);
    }
    
    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        try {
            // First delete any payments that might reference our test clients
            self::$db->exec("DELETE FROM payments WHERE client_id IN (SELECT id FROM clients WHERE client_number LIKE 'TEST-%')");
        } catch (\Exception $e) {
            // Table might not exist
        }
        
        self::$db->exec("DELETE FROM invoice_payments WHERE invoice_id IN (SELECT id FROM invoices WHERE invoice_number LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM invoice_items WHERE invoice_id IN (SELECT id FROM invoices WHERE invoice_number LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM invoices WHERE invoice_number LIKE 'TEST-%'");
        self::$db->exec("DELETE FROM clients WHERE client_number LIKE 'TEST-%'");
        self::$db->exec("DELETE FROM config_payment_terms WHERE code LIKE 'TEST-%'");
        self::$db->exec("DELETE FROM config_vat_rates WHERE code LIKE 'TEST%'");
    }
}