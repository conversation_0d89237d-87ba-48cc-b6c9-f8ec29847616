<?php
/**
 * Form Validation Helper Functions
 * Provides comprehensive validation for forms throughout the application
 */

/**
 * Validate required field
 * @param mixed $value
 * @param string $fieldName
 * @return array|null Error array or null if valid
 */
function validateRequired($value, $fieldName) {
    if (empty($value) || (is_string($value) && trim($value) === '')) {
        return ['field' => $fieldName, 'message' => __('validation.required', ['field' => $fieldName])];
    }
    return null;
}

/**
 * Validate email format
 * @param string $email
 * @return array|null Error array or null if valid
 */
function validateEmail($email) {
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return ['field' => 'email', 'message' => __('validation.email')];
    }
    // Check for valid domain
    $domain = substr(strrchr($email, "@"), 1);
    if (!checkdnsrr($domain, "MX") && !checkdnsrr($domain, "A")) {
        return ['field' => 'email', 'message' => __('validation.email_domain')];
    }
    return null;
}

/**
 * Validate number format
 * @param mixed $value
 * @param string $fieldName
 * @param array $options ['min' => null, 'max' => null, 'decimal' => false]
 * @return array|null Error array or null if valid
 */
function validateNumber($value, $fieldName, $options = []) {
    $min = $options['min'] ?? null;
    $max = $options['max'] ?? null;
    $decimal = $options['decimal'] ?? false;
    
    if ($decimal) {
        if (!is_numeric($value)) {
            return ['field' => $fieldName, 'message' => __('validation.numeric', ['field' => $fieldName])];
        }
        $value = floatval($value);
    } else {
        if (!ctype_digit(strval($value)) && !is_int($value)) {
            return ['field' => $fieldName, 'message' => __('validation.integer', ['field' => $fieldName])];
        }
        $value = intval($value);
    }
    
    if ($min !== null && $value < $min) {
        return ['field' => $fieldName, 'message' => __('validation.min_value', ['field' => $fieldName, 'min' => $min])];
    }
    
    if ($max !== null && $value > $max) {
        return ['field' => $fieldName, 'message' => __('validation.max_value', ['field' => $fieldName, 'max' => $max])];
    }
    
    return null;
}

/**
 * Validate price format
 * @param mixed $price
 * @param string $fieldName
 * @return array|null Error array or null if valid
 */
function validatePrice($price, $fieldName = 'price') {
    // Remove currency symbols and spaces
    $price = preg_replace('/[^0-9.,\-]/', '', $price);
    // Replace comma with dot for decimal
    $price = str_replace(',', '.', $price);
    
    if (!is_numeric($price)) {
        return ['field' => $fieldName, 'message' => __('validation.price_format', ['field' => $fieldName])];
    }
    
    if (floatval($price) < 0) {
        return ['field' => $fieldName, 'message' => __('validation.price_positive', ['field' => $fieldName])];
    }
    
    return null;
}

/**
 * Validate date format
 * @param string $date
 * @param string $format Default 'Y-m-d'
 * @param string $fieldName
 * @return array|null Error array or null if valid
 */
function validateDate($date, $format = 'Y-m-d', $fieldName = 'date') {
    $d = DateTime::createFromFormat($format, $date);
    if (!$d || $d->format($format) !== $date) {
        return ['field' => $fieldName, 'message' => __('validation.date_format', ['field' => $fieldName, 'format' => $format])];
    }
    return null;
}

/**
 * Validate string length
 * @param string $value
 * @param string $fieldName
 * @param array $options ['min' => null, 'max' => null]
 * @return array|null Error array or null if valid
 */
function validateLength($value, $fieldName, $options = []) {
    $min = $options['min'] ?? null;
    $max = $options['max'] ?? null;
    $length = mb_strlen($value);
    
    if ($min !== null && $length < $min) {
        return ['field' => $fieldName, 'message' => __('validation.min_length', ['field' => $fieldName, 'min' => $min])];
    }
    
    if ($max !== null && $length > $max) {
        return ['field' => $fieldName, 'message' => __('validation.max_length', ['field' => $fieldName, 'max' => $max])];
    }
    
    return null;
}

/**
 * Validate phone number
 * @param string $phone
 * @return array|null Error array or null if valid
 */
function validatePhone($phone) {
    // Remove spaces, dashes, parentheses
    $phone = preg_replace('/[\s\-\(\)]/', '', $phone);
    
    // Check if it contains only numbers and optional + at the beginning
    if (!preg_match('/^\+?\d{7,15}$/', $phone)) {
        return ['field' => 'phone', 'message' => __('validation.phone_format')];
    }
    
    return null;
}

/**
 * Validate URL format
 * @param string $url
 * @param string $fieldName
 * @return array|null Error array or null if valid
 */
function validateUrl($url, $fieldName = 'url') {
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return ['field' => $fieldName, 'message' => __('validation.url_format', ['field' => $fieldName])];
    }
    return null;
}

/**
 * Sanitize input to prevent XSS
 * @param string $input
 * @return string
 */
function sanitizeInput($input) {
    if (is_string($input)) {
        return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    return $input;
}

/**
 * Sanitize array of inputs
 * @param array $inputs
 * @return array
 */
function sanitizeArray($inputs) {
    $sanitized = [];
    foreach ($inputs as $key => $value) {
        if (is_array($value)) {
            $sanitized[$key] = sanitizeArray($value);
        } else {
            $sanitized[$key] = sanitizeInput($value);
        }
    }
    return $sanitized;
}

/**
 * Validate form data against rules
 * @param array $data The form data to validate
 * @param array $rules Validation rules
 * @return array ['valid' => bool, 'errors' => array]
 */
function validateForm($data, $rules) {
    $errors = [];
    
    foreach ($rules as $field => $fieldRules) {
        $value = $data[$field] ?? null;
        
        foreach ($fieldRules as $rule => $params) {
            $error = null;
            
            switch ($rule) {
                case 'required':
                    if ($params === true) {
                        $error = validateRequired($value, $field);
                    }
                    break;
                    
                case 'email':
                    if ($params === true && !empty($value)) {
                        $error = validateEmail($value);
                    }
                    break;
                    
                case 'number':
                    if (!empty($value)) {
                        $error = validateNumber($value, $field, $params);
                    }
                    break;
                    
                case 'price':
                    if (!empty($value)) {
                        $error = validatePrice($value, $field);
                    }
                    break;
                    
                case 'date':
                    if (!empty($value)) {
                        $format = is_array($params) ? ($params['format'] ?? 'Y-m-d') : 'Y-m-d';
                        $error = validateDate($value, $format, $field);
                    }
                    break;
                    
                case 'length':
                    if (!empty($value)) {
                        $error = validateLength($value, $field, $params);
                    }
                    break;
                    
                case 'phone':
                    if ($params === true && !empty($value)) {
                        $error = validatePhone($value);
                    }
                    break;
                    
                case 'url':
                    if ($params === true && !empty($value)) {
                        $error = validateUrl($value, $field);
                    }
                    break;
            }
            
            if ($error !== null) {
                $errors[] = $error;
                break; // Stop checking other rules for this field
            }
        }
    }
    
    return [
        'valid' => empty($errors),
        'errors' => $errors
    ];
}

/**
 * Get validation rules for invoice form
 * @return array
 */
function getInvoiceValidationRules() {
    return [
        'invoice_type_id' => ['required' => true],
        'client_name' => ['required' => true, 'length' => ['min' => 2, 'max' => 255]],
        'client_email' => ['email' => true],
        'client_phone' => ['phone' => true],
        'invoice_date' => ['required' => true, 'date' => true],
        'due_date' => ['date' => true],
        'items' => ['required' => true],
        'subtotal' => ['required' => true, 'price' => true],
        'total' => ['required' => true, 'price' => true]
    ];
}

/**
 * Get validation rules for user form
 * @return array
 */
function getUserValidationRules($isEdit = false) {
    $rules = [
        'first_name' => ['required' => true, 'length' => ['min' => 2, 'max' => 50]],
        'last_name' => ['required' => true, 'length' => ['min' => 2, 'max' => 50]],
        'email' => ['required' => true, 'email' => true],
        'username' => ['required' => true, 'length' => ['min' => 3, 'max' => 50]],
        'phone' => ['phone' => true],
        'company' => ['length' => ['max' => 100]]
    ];
    
    if (!$isEdit) {
        $rules['password'] = ['required' => true, 'length' => ['min' => 6]];
    }
    
    return $rules;
}

/**
 * Get validation rules for product form
 * @return array
 */
function getProductValidationRules() {
    return [
        'name' => ['required' => true, 'length' => ['min' => 2, 'max' => 255]],
        'category_id' => ['required' => true],
        'price' => ['required' => true, 'price' => true],
        'cost' => ['price' => true],
        'stock' => ['number' => ['min' => 0, 'decimal' => false]],
        'sku' => ['length' => ['max' => 50]],
        'barcode' => ['length' => ['max' => 50]]
    ];
}

/**
 * Get validation rules for login form
 * @return array
 */
function getLoginValidationRules() {
    return [
        'username' => ['required' => true],
        'password' => ['required' => true]
    ];
}

/**
 * Format validation errors for display
 * @param array $errors
 * @return string HTML formatted errors
 */
function formatValidationErrors($errors) {
    if (empty($errors)) {
        return '';
    }
    
    $html = '<div class="alert alert-danger alert-dismissible fade show" role="alert">';
    $html .= '<strong>' . __('validation.error_title') . '</strong>';
    $html .= '<ul class="mb-0 mt-2">';
    
    foreach ($errors as $error) {
        $html .= '<li>' . htmlspecialchars($error['message']) . '</li>';
    }
    
    $html .= '</ul>';
    $html .= '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>';
    $html .= '</div>';
    
    return $html;
}

/**
 * Add validation class to form field
 * @param string $field
 * @param array $errors
 * @return string
 */
function getValidationClass($field, $errors) {
    foreach ($errors as $error) {
        if ($error['field'] === $field) {
            return 'is-invalid';
        }
    }
    return '';
}

/**
 * Get field error message
 * @param string $field
 * @param array $errors
 * @return string
 */
function getFieldError($field, $errors) {
    foreach ($errors as $error) {
        if ($error['field'] === $field) {
            return '<div class="invalid-feedback">' . htmlspecialchars($error['message']) . '</div>';
        }
    }
    return '';
}