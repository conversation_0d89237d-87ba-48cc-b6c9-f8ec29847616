<?php

return [
    // General email terms
    'subject' => 'Subject',
    'body' => 'Body',
    'body_html' => 'HTML Body',
    'body_text' => 'Plain Text Body',
    'recipient' => 'Recipient',
    'sender' => 'Sender',
    'template' => 'Template',
    'templates' => 'Templates',
    'variables' => 'Variables',
    'available_variables' => 'Available Variables',
    
    // Email types
    'types' => [
        'invoice' => 'Invoice Email',
        'quote' => 'Quote Email',
        'payment_reminder' => 'Payment Reminder',
        'general' => 'General Email',
        'notification' => 'Notification',
    ],
    
    // Email automation
    'automation' => [
        'title' => 'Email Automation Settings',
        'description' => 'Configure automatic email sending for invoices and payment reminders',
        'master_control' => 'Master Control',
        'enable_automatic_sending' => 'Enable Automatic Email Sending',
        'master_description' => 'When enabled, emails will be sent automatically based on the settings below',
        
        // Invoice settings
        'invoice_settings' => 'Invoice Type Settings',
        'invoice_type' => 'Invoice Type',
        'auto_send' => 'Auto Send',
        'delay' => 'Delay',
        'custom_hours' => 'Hours',
        'email_template' => 'Email Template',
        'select_template' => 'Select template...',
        'enable_for' => 'Enable for',
        
        // Delay options
        'immediate' => 'Immediate',
        '1_hour' => '1 Hour',
        '24_hours' => '24 Hours',
        'custom' => 'Custom',
        
        // Payment reminders
        'payment_reminders' => 'Payment Reminders',
        'enable_payment_reminders' => 'Enable Automatic Payment Reminders',
        'reminders_description' => 'Send automatic reminders for overdue invoices',
        'reminder' => 'Reminder',
        'reminder_level' => 'Reminder Level',
        'days_after_due' => 'Days After Due Date',
        'add_reminder' => 'Add Reminder',
        'max_reminders' => 'Maximum Reminders',
        'max_reminders_help' => 'Stop sending reminders after this many attempts',
        
        // Test section
        'test_section' => 'Test Email',
        'select_template_test' => 'Select Template to Test',
        'test_email_address' => 'Test Email Address',
        'enter_email' => 'Enter email address',
        'send_test' => 'Send Test',
        'preview' => 'Preview',
        'template_preview' => 'Template Preview',
        
        // Messages
        'select_template_first' => 'Please select a template first',
        'enter_email_first' => 'Please enter an email address',
    ],
    
    // Email status
    'status' => [
        'sent' => 'Sent',
        'pending' => 'Pending',
        'failed' => 'Failed',
        'queued' => 'Queued',
    ],
];