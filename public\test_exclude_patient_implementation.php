<?php
/**
 * Test script for exclude patient line implementation
 */

require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment variables FIRST
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

// Now require bootstrap which will use the env vars
require_once dirname(__DIR__) . '/app/config/bootstrap.php';

echo "<pre>";
echo "=== Testing Exclude Patient Line Implementation ===\n\n";

try {
    $db = Flight::db();
    
    // 1. Check if migration was successful
    echo "1. Checking database migration...\n";
    $stmt = $db->prepare("SHOW COLUMNS FROM users LIKE 'exclude_patient_line_default'");
    $stmt->execute();
    $column = $stmt->fetch();
    
    if ($column) {
        echo "✅ Column 'exclude_patient_line_default' exists in users table\n";
        echo "   Type: " . $column['Type'] . "\n";
        echo "   Default: " . $column['Default'] . "\n\n";
    } else {
        echo "❌ Column 'exclude_patient_line_default' NOT found in users table\n";
        echo "   Please run the migration: http://localhost/fit/public/run_migration_081.php\n\n";
    }
    
    // 2. Check Kiné group
    echo "2. Checking Kiné group...\n";
    $stmt = $db->prepare("SELECT * FROM user_groups WHERE name = 'Kiné'");
    $stmt->execute();
    $kineGroup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($kineGroup) {
        echo "✅ Kiné group found:\n";
        echo "   ID: " . $kineGroup['id'] . "\n";
        echo "   Name: " . $kineGroup['name'] . "\n";
        echo "   Color: " . $kineGroup['color'] . "\n\n";
    } else {
        echo "❌ Kiné group NOT found\n\n";
    }
    
    // 3. Check Rémi Heine
    echo "3. Checking Rémi Heine user...\n";
    $stmt = $db->prepare("
        SELECT u.*, GROUP_CONCAT(g.name) as group_names 
        FROM users u
        LEFT JOIN user_group_members ugm ON u.id = ugm.user_id
        LEFT JOIN user_groups g ON ugm.group_id = g.id
        WHERE u.first_name = 'Rémi' AND u.last_name = 'Heine'
        GROUP BY u.id
    ");
    $stmt->execute();
    $remi = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($remi) {
        echo "✅ Rémi Heine found:\n";
        echo "   ID: " . $remi['id'] . "\n";
        echo "   Username: " . $remi['username'] . "\n";
        echo "   Email: " . $remi['email'] . "\n";
        echo "   Groups: " . ($remi['group_names'] ?: 'None') . "\n";
        echo "   Exclude Patient Line Default: " . ($remi['exclude_patient_line_default'] ? 'Yes' : 'No') . "\n\n";
        
        // Check if in Kiné group
        if ($kineGroup && strpos($remi['group_names'], 'Kiné') !== false) {
            echo "   ✅ Is member of Kiné group\n\n";
        } else {
            echo "   ⚠️ NOT member of Kiné group\n\n";
        }
    } else {
        echo "❌ Rémi Heine NOT found\n\n";
    }
    
    // 4. Test setting the exclude_patient_line_default
    if ($remi) {
        echo "4. Testing the exclude_patient_line_default setting...\n";
        
        // Set it to true
        $stmt = $db->prepare("UPDATE users SET exclude_patient_line_default = 1 WHERE id = ?");
        $stmt->execute([$remi['id']]);
        echo "✅ Set exclude_patient_line_default to 1 for Rémi Heine\n\n";
        
        // Verify
        $stmt = $db->prepare("SELECT exclude_patient_line_default FROM users WHERE id = ?");
        $stmt->execute([$remi['id']]);
        $result = $stmt->fetch();
        echo "   Verification: exclude_patient_line_default = " . $result['exclude_patient_line_default'] . "\n\n";
    }
    
    // 5. Check retrocession data entry table
    echo "5. Checking retrocession_data_entry table...\n";
    $stmt = $db->prepare("SHOW COLUMNS FROM retrocession_data_entry LIKE 'exclude_patient_line'");
    $stmt->execute();
    $column = $stmt->fetch();
    
    if ($column) {
        echo "✅ Column 'exclude_patient_line' exists in retrocession_data_entry table\n\n";
    } else {
        echo "❌ Column 'exclude_patient_line' NOT found in retrocession_data_entry table\n";
        echo "   Please run the migration: add_exclude_patient_line_to_retrocession.sql\n\n";
    }
    
    echo "=== Implementation Test Complete ===\n\n";
    
    echo "Next steps to test:\n";
    echo "1. Edit Rémi Heine: http://localhost/fit/public/users/" . ($remi['id'] ?? '?') . "/edit\n";
    echo "   - Add him to the Kiné group if not already\n";
    echo "   - Check the 'Exclude patient line by default' checkbox\n\n";
    
    echo "2. Create a retrocession invoice: http://localhost/fit/public/invoices/create?type=retrocession_25\n";
    echo "   - Select Rémi Heine as the user\n";
    echo "   - The 'Exclude patient line' checkbox should be pre-checked\n\n";
    
    echo "3. Enter retrocession data: http://localhost/fit/public/retrocession\n";
    echo "   - Find Rémi Heine and click 'Enter Data'\n";
    echo "   - The 'Exclude patient line' checkbox should be pre-checked\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "</pre>";