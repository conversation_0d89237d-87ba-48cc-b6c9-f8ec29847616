<?php

/**
 * Menu Configuration
 * Define all menu items and their visibility settings
 */

return [
    // Patients Menu
    [
        'id' => 'patients',
        'title' => 'patients.title',
        'icon' => 'fas fa-user-injured',
        'url' => '/patients',
        'route' => 'patients',
        'enabled' => true,
        'permission' => 'patients.view',
        'badge' => null
    ],
    
    // Clients Menu
    [
        'id' => 'clients',
        'title' => 'clients.title',
        'icon' => 'bi bi-building',
        'url' => '/clients',
        'route' => 'clients',
        'enabled' => true,
        'permission' => 'clients.view',
        'badge' => null
    ],
    
    // Invoices Menu
    [
        'id' => 'invoices',
        'title' => 'invoices.invoices',
        'icon' => 'bi bi-file-earmark-text',
        'url' => '/invoices',
        'route' => 'invoices',
        'enabled' => true,
        'permission' => 'invoices.view',
        'badge' => ['color' => 'warning', 'count' => 12] // Dynamic count
    ],
    
    // Users Menu with Submenu
    [
        'id' => 'users',
        'title' => 'users.users',
        'icon' => 'bi bi-people',
        'url' => '#',
        'route' => 'users',
        'enabled' => true,
        'permission' => 'users.view',
        'submenu' => [
            [
                'id' => 'users-list',
                'title' => 'users.user_list',
                'url' => '/users',
                'enabled' => true,
                'permission' => 'users.view'
            ],
            [
                'id' => 'user-groups',
                'title' => 'users.user_groups',
                'url' => '/users/groups',
                'enabled' => true,
                'permission' => 'users.manage_groups'
            ]
        ]
    ],
    
    // Configuration Menu (no submenu)
    [
        'id' => 'config',
        'title' => 'config.configuration',
        'icon' => 'bi bi-gear',
        'url' => '/config',
        'route' => 'config',
        'enabled' => true,
        'permission' => 'config.view',
        'badge' => null
    ],
    
    // Translations Menu
    [
        'id' => 'translations',
        'title' => 'translations.translations',
        'icon' => 'bi bi-translate',
        'url' => '/translations',
        'route' => 'translations',
        'enabled' => false, // Disabled by default
        'permission' => 'translations.manage',
        'admin_only' => true
    ],
    
    // Reports Menu
    [
        'id' => 'reports',
        'title' => 'reports.reports',
        'icon' => 'bi bi-graph-up',
        'url' => '/reports',
        'route' => 'reports',
        'enabled' => true,
        'permission' => 'reports.view'
    ],
    
    // Billing Menu (disabled by default)
    [
        'id' => 'billing',
        'title' => 'common.billing',
        'icon' => 'bi bi-cash-coin',
        'url' => '#',
        'route' => 'billing',
        'enabled' => false,
        'permission' => 'billing.view',
        'submenu' => [
            [
                'id' => 'billing-wizard',
                'title' => 'invoices.billing_wizard',
                'url' => '/billing-wizard',
                'enabled' => false,
                'permission' => 'billing.wizard'
            ],
            [
                'id' => 'retrocession',
                'title' => 'invoices.retrocession',
                'url' => '/retrocession',
                'enabled' => false,
                'permission' => 'billing.retrocession'
            ],
            [
                'id' => 'vouchers',
                'title' => 'invoices.vouchers',
                'url' => '/vouchers',
                'enabled' => false,
                'permission' => 'billing.vouchers'
            ]
        ]
    ]
];