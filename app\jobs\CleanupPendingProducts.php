<?php

namespace App\Jobs;

use App\Models\Database;
use PDO;

/**
 * Cleanup Pending Products Job
 * 
 * This job removes abandoned product creation attempts that are older than 24 hours.
 * It should be run daily via cron or a scheduled task.
 */
class CleanupPendingProducts
{
    protected $db;
    protected $logFile;
    
    public function __construct()
    {
        $this->db = Database::getInstance()->getConnection();
        $this->logFile = __DIR__ . '/../../storage/logs/pending_products_cleanup.log';
    }
    
    /**
     * Execute the cleanup job
     */
    public function execute()
    {
        $this->log('Starting pending products cleanup job');
        
        try {
            // Begin transaction
            $this->db->beginTransaction();
            
            // Find pending products older than 24 hours
            $stmt = $this->db->prepare("
                SELECT COUNT(*) as count 
                FROM catalog_pending_products 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
            ");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $pendingCount = $result['count'];
            
            if ($pendingCount > 0) {
                $this->log("Found {$pendingCount} abandoned product creation attempts");
                
                // Delete old pending products
                $deleteStmt = $this->db->prepare("
                    DELETE FROM catalog_pending_products 
                    WHERE created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ");
                $deleteStmt->execute();
                
                $this->log("Deleted {$pendingCount} abandoned products");
                
                // Log details of deleted products for audit
                $this->logDeletedProducts();
            } else {
                $this->log('No abandoned products found');
            }
            
            // Cleanup old rate limit entries
            $this->cleanupRateLimits();
            
            // Cleanup old activity logs (older than 90 days)
            $this->cleanupActivityLogs();
            
            // Commit transaction
            $this->db->commit();
            
            $this->log('Cleanup job completed successfully');
            
            return true;
            
        } catch (\Exception $e) {
            $this->db->rollBack();
            $this->log('ERROR: ' . $e->getMessage(), 'error');
            return false;
        }
    }
    
    /**
     * Cleanup expired rate limit entries
     */
    protected function cleanupRateLimits()
    {
        try {
            // Rate limits are stored in session, but we'll clean up any database entries if they exist
            $stmt = $this->db->prepare("
                DELETE FROM rate_limits 
                WHERE created_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute();
            
            $deletedCount = $stmt->rowCount();
            if ($deletedCount > 0) {
                $this->log("Cleaned up {$deletedCount} expired rate limit entries");
            }
        } catch (\Exception $e) {
            // Table might not exist, ignore error
            $this->log('Rate limits cleanup skipped: ' . $e->getMessage(), 'warning');
        }
    }
    
    /**
     * Cleanup old activity logs
     */
    protected function cleanupActivityLogs()
    {
        try {
            // Keep only last 90 days of activity logs for product creation
            $stmt = $this->db->prepare("
                DELETE FROM activity_logs 
                WHERE action = 'product.quick_create' 
                AND created_at < DATE_SUB(NOW(), INTERVAL 90 DAY)
            ");
            $stmt->execute();
            
            $deletedCount = $stmt->rowCount();
            if ($deletedCount > 0) {
                $this->log("Cleaned up {$deletedCount} old activity log entries");
            }
        } catch (\Exception $e) {
            $this->log('Activity logs cleanup error: ' . $e->getMessage(), 'warning');
        }
    }
    
    /**
     * Log details of deleted products for audit purposes
     */
    protected function logDeletedProducts()
    {
        // This would normally log to a separate audit table
        // For now, we'll just note it in the log file
        $this->log('Product deletion audit logged');
    }
    
    /**
     * Log message to file
     */
    protected function log($message, $level = 'info')
    {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Write to log file
        file_put_contents($this->logFile, $logMessage, FILE_APPEND | LOCK_EX);
        
        // Also output to console if running from CLI
        if (php_sapi_name() === 'cli') {
            echo $logMessage;
        }
    }
    
    /**
     * Get job statistics
     */
    public function getStatistics()
    {
        try {
            $stats = [];
            
            // Pending products count
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN created_at < DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as expired,
                    COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN 1 END) as active
                FROM catalog_pending_products
            ");
            $stmt->execute();
            $stats['pending_products'] = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Recent cleanup activity
            $stmt = $this->db->prepare("
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as products_created
                FROM catalog_items
                WHERE created_source = 'inline_creation'
                AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            ");
            $stmt->execute();
            $stats['recent_creations'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            return $stats;
            
        } catch (\Exception $e) {
            $this->log('Error getting statistics: ' . $e->getMessage(), 'error');
            return null;
        }
    }
}

// Execute if run directly from CLI
if (php_sapi_name() === 'cli' && basename(__FILE__) == basename($argv[0])) {
    require_once __DIR__ . '/../../app/config/bootstrap.php';
    
    $job = new CleanupPendingProducts();
    
    // Check for command line arguments
    if (isset($argv[1]) && $argv[1] === 'stats') {
        // Show statistics
        $stats = $job->getStatistics();
        if ($stats) {
            echo "\n=== Pending Products Statistics ===\n";
            echo "Total pending: " . $stats['pending_products']['total'] . "\n";
            echo "Expired (>24h): " . $stats['pending_products']['expired'] . "\n";
            echo "Active (<24h): " . $stats['pending_products']['active'] . "\n";
            echo "\n=== Recent Inline Creations (Last 7 days) ===\n";
            foreach ($stats['recent_creations'] as $day) {
                echo $day['date'] . ": " . $day['products_created'] . " products\n";
            }
        }
    } else {
        // Run cleanup
        $success = $job->execute();
        exit($success ? 0 : 1);
    }
}