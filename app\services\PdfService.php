<?php

namespace App\Services;

use Flight;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\Config;
use App\Models\Voucher;
use App\Helpers\MoneyHelper;
use Exception;
use TCPDF;

class PdfService
{
    use PdfServiceImproved;
    private $db;
    private $tcpdf;
    
    public function __construct()
    {
        $this->db = Flight::db();
        
        // Load TCPDF config if needed
        $tcpdfConfig = __DIR__ . '/../../vendor/tecnickcom/tcpdf/config/tcpdf_config.php';
        if (file_exists($tcpdfConfig) && !defined('PDF_PAGE_ORIENTATION')) {
            require_once $tcpdfConfig;
        }
        
        // Define constants if not already defined
        if (!defined('PDF_PAGE_ORIENTATION')) {
            define('PDF_PAGE_ORIENTATION', 'P');
        }
        if (!defined('PDF_UNIT')) {
            define('PDF_UNIT', 'mm');
        }
        if (!defined('PDF_PAGE_FORMAT')) {
            define('PDF_PAGE_FORMAT', 'A4');
        }
    }
    
    /**
     * Generate invoice PDF
     */
    public function generateInvoicePdf($invoiceData)
    {
        try {
            // Ensure invoice_type is set
            if (empty($invoiceData['invoice_type'])) {
                $invoiceData['invoice_type'] = 'invoice'; // Default type
            }
            
            // Initialize TCPDF with A4 format
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
            
            // Set document information
            $pdf->SetCreator('FIT Billing System');
            $pdf->SetAuthor($this->getConfig('company_name'));
            $pdf->SetTitle('Invoice ' . $invoiceData['invoice_number']);
            
            // Remove default header/footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);
            
            // Set margins to 2.5 cm (25 mm) on all sides
            $pdf->SetMargins(25, 25, 25);
            $pdf->SetAutoPageBreak(TRUE, 25);
            
            // Set font
            $pdf->SetFont('helvetica', '', 10);
            
            // Add page
            $pdf->AddPage();
            
            // Add company logo if available
            $logoPath = Config::getValue('company_logo');
            if ($logoPath && file_exists(PUBLIC_PATH . '/' . $logoPath)) {
                // Add logo at top left with 25mm margins
                $pdf->Image(PUBLIC_PATH . '/' . $logoPath, 25, 25, 40, 0, '', '', '', true, 300, '', false, false, 0, false, false, false);
                // Move cursor below logo
                $pdf->SetY(50);
            }
            
            // For now, skip template system as it's not properly configured
            $template = null;
            
            // Render invoice content
            // Check if we have the improved method available
            if (method_exists($this, 'renderInvoiceHtmlImproved')) {
                $html = $this->renderInvoiceHtmlImproved($invoiceData);
            } else {
                $html = $this->renderInvoiceHtml($invoiceData, $template);
            }
            
            // Output the HTML content
            $pdf->writeHTML($html, true, false, true, false, '');
            
            // Add watermark if needed (but not for paid invoices)
            if ($invoiceData['status'] === 'cancelled') {
                $this->addWatermark($pdf, 'CANCELLED');
            } elseif ($invoiceData['status'] === 'draft') {
                $this->addWatermark($pdf, 'DRAFT');
            }
            
            // Output PDF as string
            return $pdf->Output('', 'S');
            
        } catch (Exception $e) {
            throw new Exception('PDF generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate voucher PDF
     */
    public function generateVoucherPdf($voucherId)
    {
        try {
            $voucher = new Voucher();
            $voucherData = $voucher->getById($voucherId);
            
            if (!$voucherData) {
                throw new Exception('Voucher not found');
            }
            
            // Get client details
            $client = new Client();
            $clientData = $client->getById($voucherData['client_id']);
            
            // Initialize TCPDF with A4 format
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
            
            // Set document information
            $pdf->SetCreator('FIT Billing System');
            $pdf->SetAuthor($this->getConfig('company_name'));
            $pdf->SetTitle('Voucher ' . $voucherData['voucher_number']);
            
            // Remove default header/footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);
            
            // Set margins for A4 (left, top, right)
            $pdf->SetMargins(25, 25, 25);
            $pdf->SetAutoPageBreak(TRUE, 30);
            
            // Add page
            $pdf->AddPage();
            
            // Render voucher
            $html = $this->renderVoucherHtml($voucherData, $clientData);
            
            // Output the HTML content
            $pdf->writeHTML($html, true, false, true, false, '');
            
            // Output PDF as string
            return $pdf->Output('', 'S');
            
        } catch (Exception $e) {
            throw new Exception('Voucher PDF generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate monthly statement PDF
     */
    public function generateStatementPdf($clientId, $month, $year)
    {
        try {
            // Get client details
            $client = new Client();
            $clientData = $client->getById($clientId);
            
            if (!$clientData) {
                throw new Exception('Client not found');
            }
            
            // Get invoices for the period
            $invoices = $this->getClientInvoicesForPeriod($clientId, $month, $year);
            
            // Initialize TCPDF with A4 format
            $pdf = new TCPDF('P', 'mm', 'A4', true, 'UTF-8', false);
            
            // Set document information
            $pdf->SetCreator('FIT Billing System');
            $pdf->SetAuthor($this->getConfig('company_name'));
            $pdf->SetTitle('Statement - ' . $clientData['name'] . ' - ' . $month . '/' . $year);
            
            // Remove default header/footer
            $pdf->setPrintHeader(false);
            $pdf->setPrintFooter(false);
            
            // Set margins for A4 (left, top, right)
            $pdf->SetMargins(25, 25, 25);
            $pdf->SetAutoPageBreak(TRUE, 30);
            
            // Add page
            $pdf->AddPage();
            
            // Render statement
            $html = $this->renderStatementHtml($clientData, $invoices, $month, $year);
            
            // Output the HTML content
            $pdf->writeHTML($html, true, false, true, false, '');
            
            // Output PDF as string
            return $pdf->Output('', 'S');
            
        } catch (Exception $e) {
            throw new Exception('Statement PDF generation failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Render invoice HTML
     */
    private function renderInvoiceHtml($invoiceData, $template)
    {
        $companyLogo = $this->getCompanyLogo();
        $companyInfo = $this->getCompanyInfo();
        
        $html = '<style>' . $this->getInvoiceCss() . '</style>';
        
        // Header - matching print version
        $html .= '<div style="margin-bottom: 40px;">
            <table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 30px;">
                <tr>
                    <td width="50%" style="font-size: 10pt; line-height: 1.5;">';
        
        if ($companyLogo) {
            $html .= '<img src="' . $companyLogo . '" style="max-height: 60px; margin-bottom: 10px;"><br>';
        } else {
            $html .= '<div style="font-size: 18pt; font-weight: bold; margin-bottom: 5px;">' . $companyInfo['name'] . '</div>';
        }
        
        if (!empty($companyInfo['address'])) {
            $html .= $companyInfo['address'] . '<br>';
        }
        if (!empty($companyInfo['phone'])) {
            $html .= 'Tél: ' . $companyInfo['phone'] . '<br>';
        }
        if (!empty($companyInfo['email'])) {
            $html .= 'Email: ' . $companyInfo['email'];
        }
        
        $html .= '</td>
                <td width="50%" style="text-align: right;">
                    <h1>FACTURE</h1>
                    <div style="font-size: 14pt; margin: 5px 0;">' . $invoiceData['invoice_number'] . '</div>
                    <div style="font-size: 10pt; color: #666;">Date: ' . date('d/m/Y', strtotime($invoiceData['issue_date'])) . '</div>';
        
        // Add status
        if ($invoiceData['status'] === 'paid') {
            $html .= '<div class="status status-paid" style="margin-top: 10px;">PAYÉ</div>';
        } elseif ($invoiceData['status'] === 'draft') {
            $html .= '<div class="status status-draft" style="margin-top: 10px;">BROUILLON</div>';
        } elseif ($invoiceData['status'] === 'cancelled') {
            $html .= '<div class="status status-cancelled" style="margin-top: 10px;">ANNULÉ</div>';
        }
        
        $html .= '</td>
            </tr>
        </table>
        </div>';
        
        // Addresses - matching print version
        $html .= '<table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 30px;">
            <tr>
                <td width="45%" valign="top">
                    <h3>Adresse de facturation</h3>
                    <div style="font-size: 10pt; line-height: 1.5;">
                        <strong>' . $this->getClientName($invoiceData['client']) . '</strong><br>';
        
        if (!empty($invoiceData['client']['company_name'])) {
            $html .= $invoiceData['client']['company_name'] . '<br>';
        }
        
        $html .= nl2br($this->getClientAddress($invoiceData['client']));
        
        $html .= '</div>
                </td>
                <td width="10%"></td>
                <td width="45%" valign="top">
                    <h3>Informations</h3>
                    <div style="font-size: 10pt; line-height: 1.5;">
                        <strong>Date d\'échéance:</strong> ' . date('d/m/Y', strtotime($invoiceData['due_date'])) . '<br>';
        
        if (!empty($invoiceData['payment_terms'])) {
            $html .= '<strong>Conditions:</strong> ' . $invoiceData['payment_terms'] . '<br>';
        }
        if (!empty($invoiceData['reference'])) {
            $html .= '<strong>Référence:</strong> ' . $invoiceData['reference'] . '<br>';
        }
        
        $html .= '</div>
                </td>
            </tr>
        </table>';
        
        // Items table - matching print version
        $html .= '<table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 30px;">
            <thead>
                <tr>
                    <th style="width: 50%;">Description</th>
                    <th style="width: 10%; text-align: center;">Qté</th>
                    <th style="width: 15%; text-align: right;">Prix unit.</th>
                    <th style="width: 10%; text-align: center;">TVA</th>
                    <th style="width: 15%; text-align: right;">Total HT</th>
                </tr>
            </thead>
            <tbody>';
        
        foreach ($invoiceData['lines'] as $line) {
            $lineTotal = $line['quantity'] * $line['unit_price'];
            $html .= '<tr>
                <td>' . $line['description'] . '</td>
                <td style="text-align: center;">' . number_format($line['quantity'], 2, ',', '.') . '</td>
                <td style="text-align: right;">' . number_format($line['unit_price'], 2, ',', '.') . ' €</td>
                <td style="text-align: center;">' . $line['vat_rate'] . ' %</td>
                <td style="text-align: right;">' . number_format($lineTotal, 2, ',', '.') . ' €</td>
            </tr>';
        }
        
        $html .= '</tbody></table>';
        
        // Totals - matching print version
        $html .= '<table width="100%" cellpadding="0" cellspacing="0" style="margin-bottom: 40px;">
            <tr>
                <td width="60%"></td>
                <td width="40%">
                    <table width="100%" cellpadding="0" cellspacing="0">
                        <tr>
                            <td style="padding: 5px 10px;">Sous-total HT:</td>
                            <td style="padding: 5px 10px; text-align: right;">' . number_format($invoiceData['subtotal'], 2, ',', '.') . ' €</td>
                        </tr>
                        <tr>
                            <td style="padding: 5px 10px;">TVA:</td>
                            <td style="padding: 5px 10px; text-align: right;">' . number_format($invoiceData['vat_amount'], 2, ',', '.') . ' €</td>
                        </tr>
                        <tr style="border-top: 2px solid #333;">
                            <td style="padding: 5px 10px; font-weight: bold; font-size: 14pt;">Total TTC:</td>
                            <td style="padding: 5px 10px; text-align: right; font-weight: bold; font-size: 14pt;">' . number_format($invoiceData['total'], 2, ',', '.') . ' €</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>';
        
        // Payment info - matching print version
        if (!empty($invoiceData['payment_terms'])) {
            $html .= '<div style="background-color: #f9f9f9; padding: 15px; margin-bottom: 20px;">
                <h4>Conditions de paiement</h4>
                <p style="margin: 0; font-size: 10pt;">' . htmlspecialchars($invoiceData['payment_terms']) . '</p>
            </div>';
        }
        
        if (!empty($invoiceData['notes'])) {
            $html .= '<div style="background-color: #f9f9f9; padding: 15px; margin-bottom: 20px;">
                <h4>Notes</h4>
                <p style="margin: 0; font-size: 10pt;">' . nl2br($invoiceData['notes']) . '</p>
            </div>';
        }
        
        // Footer - matching print version
        $html .= '<div style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; font-size: 10pt; color: #666;">
            <p>' . $companyInfo['name'];
        
        if (!empty($companyInfo['phone'])) {
            $html .= ' - ' . $companyInfo['phone'];
        }
        if (!empty($companyInfo['email'])) {
            $html .= ' - ' . $companyInfo['email'];
        }
        
        $html .= '</p>';
        
        if (!empty($invoiceData['footer_text'])) {
            $html .= '<p style="margin-top: 10px; font-size: 9pt;">' . $invoiceData['footer_text'] . '</p>';
        }
        
        $html .= '</div>';
        
        return $html;
    }
    
    /**
     * Render voucher HTML
     */
    private function renderVoucherHtml($voucherData, $clientData)
    {
        $companyInfo = $this->getCompanyInfo();
        
        $html = '<style>' . $this->getVoucherCss() . '</style>';
        
        // Header
        $html .= '<div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2c3e50; margin: 0;">BON CADEAU</h1>
            <p style="font-size: 18px; margin: 10px 0;">' . $voucherData['voucher_number'] . '</p>
        </div>';
        
        // Voucher details
        $html .= '<div style="border: 2px solid #3498db; border-radius: 10px; padding: 20px; margin-bottom: 20px;">
            <table width="100%" cellpadding="5">
                <tr>
                    <td width="50%">
                        <strong>Montant:</strong><br>
                        <span style="font-size: 24px; color: #3498db;">' . number_format($voucherData['amount'], 2, ',', '.') . ' €</span>
                    </td>
                    <td width="50%" style="text-align: right;">
                        <strong>Valide jusqu\'au:</strong><br>
                        <span style="font-size: 18px;">' . date('d/m/Y', strtotime($voucherData['expiry_date'])) . '</span>
                    </td>
                </tr>
            </table>
        </div>';
        
        // Beneficiary info
        if (!empty($voucherData['beneficiary_name'])) {
            $html .= '<div style="margin-bottom: 20px;">
                <strong>Bénéficiaire:</strong> ' . $voucherData['beneficiary_name'] . '
            </div>';
        }
        
        // Usage info
        $html .= '<div style="margin-bottom: 20px;">
            <strong>Solde disponible:</strong> ' . number_format($voucherData['remaining_amount'], 2, ',', '.') . ' €
        </div>';
        
        // Terms
        $html .= '<div style="margin-top: 30px; padding: 15px; background-color: #f5f5f5; font-size: 12px;">
            <h4>Conditions d\'utilisation:</h4>
            <ul>
                <li>Ce bon est valable jusqu\'à la date d\'expiration indiquée</li>
                <li>Non remboursable et non échangeable contre de l\'argent</li>
                <li>Utilisable en une ou plusieurs fois</li>
                <li>Présenter ce bon lors du paiement</li>
            </ul>
        </div>';
        
        return $html;
    }
    
    /**
     * Render statement HTML
     */
    private function renderStatementHtml($clientData, $invoices, $month, $year)
    {
        $companyInfo = $this->getCompanyInfo();
        $monthName = $this->getMonthName($month);
        
        $html = '<style>' . $this->getStatementCss() . '</style>';
        
        // Header
        $html .= '<h1 style="text-align: center;">RELEVÉ DE COMPTE</h1>
            <p style="text-align: center; font-size: 16px;">' . $monthName . ' ' . $year . '</p>';
        
        // Client info
        $html .= '<table width="100%" cellpadding="5" style="margin-top: 30px;">
            <tr>
                <td width="50%">
                    <strong>' . $clientData['name'] . '</strong><br>
                    ' . nl2br($clientData['address']) . '<br>
                    ' . $clientData['email'] . '
                </td>
                <td width="50%" style="text-align: right;">
                    <strong>N° Client:</strong> ' . $clientData['client_number'] . '<br>
                    <strong>Date:</strong> ' . date('d/m/Y') . '
                </td>
            </tr>
        </table>';
        
        // Invoice list
        $html .= '<table width="100%" cellpadding="5" style="margin-top: 30px; border-collapse: collapse;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px;">Date</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">N° Facture</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">Description</th>
                    <th style="border: 1px solid #ddd; padding: 8px; text-align: right;">Montant</th>
                    <th style="border: 1px solid #ddd; padding: 8px;">Statut</th>
                </tr>
            </thead>
            <tbody>';
        
        $totalAmount = 0;
        $totalPaid = 0;
        $totalDue = 0;
        
        foreach ($invoices as $invoice) {
            $html .= '<tr>
                <td style="border: 1px solid #ddd; padding: 8px;">' . date('d/m/Y', strtotime($invoice['issue_date'])) . '</td>
                <td style="border: 1px solid #ddd; padding: 8px;">' . $invoice['invoice_number'] . '</td>
                <td style="border: 1px solid #ddd; padding: 8px;">' . $this->getInvoiceTypeName($invoice['invoice_type']) . '</td>
                <td style="border: 1px solid #ddd; padding: 8px; text-align: right;">' . number_format($invoice['total'], 2, ',', '.') . ' €</td>
                <td style="border: 1px solid #ddd; padding: 8px;">' . $this->getStatusBadge($invoice['status']) . '</td>
            </tr>';
            
            $totalAmount += $invoice['total'];
            if ($invoice['status'] === 'paid') {
                $totalPaid += $invoice['total'];
            } else {
                $totalDue += $invoice['total'];
            }
        }
        
        $html .= '</tbody></table>';
        
        // Summary
        $html .= '<table width="40%" cellpadding="5" style="margin-top: 30px; margin-left: auto;">
            <tr>
                <td><strong>Total facturé:</strong></td>
                <td style="text-align: right;">' . number_format($totalAmount, 2, ',', '.') . ' €</td>
            </tr>
            <tr>
                <td><strong>Total payé:</strong></td>
                <td style="text-align: right;">' . number_format($totalPaid, 2, ',', '.') . ' €</td>
            </tr>
            <tr style="background-color: #f5f5f5;">
                <td><strong>Solde dû:</strong></td>
                <td style="text-align: right;"><strong>' . number_format($totalDue, 2, ',', '.') . '€</strong></td>
            </tr>
        </table>';
        
        return $html;
    }
    
    /**
     * Add watermark to PDF
     */
    private function addWatermark($pdf, $text)
    {
        // Get current page dimensions
        $pageWidth = $pdf->getPageWidth();
        $pageHeight = $pdf->getPageHeight();
        
        // Start transformation
        $pdf->StartTransform();
        
        // Set alpha for transparency
        $pdf->SetAlpha(0.3);
        
        // Set font
        $pdf->SetFont('helvetica', 'B', 50);
        
        // Set text color
        $pdf->SetTextColor(200, 200, 200);
        
        // Rotate text
        $pdf->Rotate(45, $pageWidth/2, $pageHeight/2);
        
        // Place text
        $pdf->Text($pageWidth/2 - 50, $pageHeight/2, $text);
        
        // Reset alpha
        $pdf->SetAlpha(1);
        
        // Stop transformation
        $pdf->StopTransform();
    }
    
    /**
     * Get invoice CSS
     */
    private function getInvoiceCss()
    {
        return '
            body { 
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
                font-size: 10pt; 
                color: #2c3e50;
                line-height: 1.6;
            }
            h1 { 
                font-size: 28px; 
                margin: 0;
                font-weight: 300;
                letter-spacing: -0.5px;
            }
            h3 { 
                font-size: 14px; 
                margin: 15px 0 10px 0;
                color: #34495e;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            h4 { 
                font-size: 13px; 
                margin: 10px 0;
                color: #34495e;
                font-weight: 600;
            }
            p { 
                margin: 5px 0; 
                color: #7f8c8d;
            }
            table { width: 100%; }
            th { 
                font-weight: 600;
                text-transform: uppercase;
                font-size: 10px;
                letter-spacing: 0.5px;
            }
            .invoice-badge {
                display: inline-block;
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 11px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }
            .badge-paid {
                background-color: #27ae60;
                color: white;
            }
            .badge-draft {
                background-color: #95a5a6;
                color: white;
            }
            .badge-sent {
                background-color: #3498db;
                color: white;
            }
            .badge-cancelled {
                background-color: #e74c3c;
                color: white;
            }
            .info-label {
                color: #95a5a6;
                font-size: 10px;
                text-transform: uppercase;
                letter-spacing: 0.5px;
                margin-bottom: 2px;
            }
            .info-value {
                color: #2c3e50;
                font-size: 12px;
                font-weight: 500;
            }
        ';
    }
    
    /**
     * Get voucher CSS
     */
    private function getVoucherCss()
    {
        return '
            body { font-family: helvetica, sans-serif; font-size: 14px; color: #333; }
            h1 { font-size: 28px; margin: 0; }
            h4 { font-size: 14px; margin: 10px 0; }
        ';
    }
    
    /**
     * Get statement CSS
     */
    private function getStatementCss()
    {
        return '
            body { font-family: helvetica, sans-serif; font-size: 12px; color: #333; }
            h1 { font-size: 24px; margin: 0; }
            table { width: 100%; }
            th { font-weight: bold; text-align: left; }
        ';
    }
    
    /**
     * Get invoice template
     */
    private function getInvoiceTemplate($invoiceType)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM invoice_templates 
            WHERE invoice_type = :invoice_type 
            AND is_active = TRUE 
            ORDER BY is_default DESC 
            LIMIT 1
        ");
        
        $stmt->execute([':invoice_type' => $invoiceType]);
        $template = $stmt->fetch();
        
        if (!$template) {
            // Get default template
            $stmt = $this->db->prepare("
                SELECT * FROM invoice_templates 
                WHERE is_default = TRUE 
                AND is_active = TRUE 
                LIMIT 1
            ");
            $stmt->execute();
            $template = $stmt->fetch();
        }
        
        return $template;
    }
    
    /**
     * Get company logo
     */
    private function getCompanyLogo()
    {
        $logoPath = __DIR__ . '/../../public/assets/images/logo.png';
        if (file_exists($logoPath)) {
            return 'data:image/png;base64,' . base64_encode(file_get_contents($logoPath));
        }
        return null;
    }
    
    /**
     * Get company info
     */
    private function getCompanyInfo()
    {
        return [
            'name' => $this->getConfig('company_name'),
            'address' => $this->getConfig('company_address'),
            'phone' => $this->getConfig('company_phone'),
            'email' => $this->getConfig('company_email'),
            'vat_number' => $this->getConfig('company_vat_number')
        ];
    }
    
    /**
     * Get client invoices for period
     */
    private function getClientInvoicesForPeriod($clientId, $month, $year)
    {
        $stmt = $this->db->prepare("
            SELECT * FROM invoices 
            WHERE client_id = :client_id 
            AND MONTH(issue_date) = :month 
            AND YEAR(issue_date) = :year 
            ORDER BY issue_date
        ");
        
        $stmt->execute([
            ':client_id' => $clientId,
            ':month' => $month,
            ':year' => $year
        ]);
        
        return $stmt->fetchAll();
    }
    
    /**
     * Get invoice type name
     */
    private function getInvoiceTypeName($type)
    {
        $types = [
            'service' => 'Facture de service',
            'product' => 'Facture de produit',
            'rental' => 'Facture de location',
            'retrocession_30' => 'Facture de rétrocession (30%)',
            'retrocession_25' => 'Facture de rétrocession (25%)',
            'mixed' => 'Facture mixte'
        ];
        
        return $types[$type] ?? 'Facture';
    }
    
    /**
     * Get status badge
     */
    private function getStatusBadge($status)
    {
        $badges = [
            'draft' => '<span style="color: #666;">Brouillon</span>',
            'sent' => '<span style="color: #3498db;">Envoyée</span>',
            'paid' => '<span style="color: #27ae60;">Payée</span>',
            'unpaid' => '<span style="color: #e74c3c;">Impayée</span>',
            'cancelled' => '<span style="color: #95a5a6;">Annulée</span>'
        ];
        
        return $badges[$status] ?? $status;
    }
    
    /**
     * Get month name
     */
    private function getMonthName($month)
    {
        $months = [
            1 => 'Janvier', 2 => 'Février', 3 => 'Mars',
            4 => 'Avril', 5 => 'Mai', 6 => 'Juin',
            7 => 'Juillet', 8 => 'Août', 9 => 'Septembre',
            10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
        ];
        
        return $months[$month] ?? '';
    }
    
    /**
     * Get config value
     */
    private function getConfig($key)
    {
        $stmt = $this->db->prepare("SELECT value FROM config WHERE `key` = ?");
        $stmt->execute([$key]);
        return $stmt->fetch(\PDO::FETCH_COLUMN) ?: '';
    }
    
    /**
     * Get client name from client data
     */
    private function getClientName($client)
    {
        if (!$client) return '';
        
        if (!empty($client['company_name'])) {
            return $client['company_name'];
        }
        
        return trim(($client['first_name'] ?? '') . ' ' . ($client['last_name'] ?? ''));
    }
    
    /**
     * Get client address from client data
     */
    private function getClientAddress($client)
    {
        if (!$client) return '';
        
        $address = '';
        // Handle both 'address' and 'address_line1' field names
        if (!empty($client['address'])) {
            $address .= $client['address'] . "\n";
        } elseif (!empty($client['address_line1'])) {
            $address .= $client['address_line1'] . "\n";
            if (!empty($client['address_line2'])) {
                $address .= $client['address_line2'] . "\n";
            }
        }
        if (!empty($client['postal_code']) || !empty($client['city'])) {
            $address .= trim($client['postal_code'] . ' ' . $client['city']) . "\n";
        }
        if (!empty($client['country'])) {
            $address .= $client['country'];
        }
        
        // Fallback to simple address field if exists
        if (empty($address) && !empty($client['address'])) {
            $address = $client['address'];
        }
        
        return trim($address);
    }
}