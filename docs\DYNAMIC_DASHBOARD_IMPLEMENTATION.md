# Dynamic Dashboard Implementation Guide

## Overview

The Fit360 AdminDesk dashboard has been transformed from a static, hardcoded implementation to a fully dynamic, real-time system with API endpoints, activity logging, and auto-refresh capabilities.

## Key Components

### 1. Backend Infrastructure

#### DashboardController (`/app/controllers/DashboardController.php`)
- Main dashboard view method: `index()`
- API endpoints:
  - `/api/dashboard/stats` - Dashboard statistics
  - `/api/dashboard/revenue-chart` - Revenue chart data with period filtering
  - `/api/dashboard/invoice-status-chart` - Invoice status breakdown
  - `/api/dashboard/recent-activities` - Recent activity logs
  - `/api/dashboard/recent-invoices` - Recent invoices with pagination

#### Activity Logging System
- **Database Table**: `activity_logs` (migration 106)
- **Model**: `/app/models/ActivityLog.php`
- **Features**:
  - Tracks all user actions on entities
  - Stores changed fields with old/new values
  - IP address and user agent tracking
  - Relative time formatting in French

### 2. Frontend Implementation

#### Dashboard.js (`/public/js/dashboard.js`)
- **DashboardManager** module with:
  - Dynamic statistics loading
  - Real-time chart updates
  - Auto-refresh functionality (60s default)
  - Period filtering for charts
  - Activity timeline with animations
  - Error handling and loading states

#### Updated Views
- `dashboard-modern.twig` - Enhanced with dynamic containers
- `dashboard-fresh.twig` - Compatible with new system

### 3. Activity Logging Integration

Controllers updated with activity logging:
- **InvoiceController**: Create, update, delete, send, payment recording
- **ClientController**: Create, update, delete operations

## Installation & Setup

### 1. Run Database Migration

```bash
cd /mnt/c/wamp64/www/fit/database
php migrate.php
```

This creates the `activity_logs` table.

### 2. Update Routes

The routes have been updated to use the new DashboardController:
```php
// Home route now uses DashboardController
Flight::route('/', [new \App\Controllers\DashboardController(), 'index']);
```

### 3. Test the Implementation

Access the test page at:
```
http://localhost/fit/public/test-dynamic-dashboard.php
```

## API Endpoints

### Get Dashboard Stats
```
GET /api/dashboard/stats
```
Returns:
```json
{
  "total_clients": 150,
  "active_clients": 120,
  "total_patients": 500,
  "total_invoices": 1200,
  "unpaid_invoices": 45,
  "paid_invoices": 1100,
  "sent_invoices": 55,
  "overdue_invoices": 10,
  "total_revenue": 125000.50,
  "outstanding_amount": 5500.00,
  "monthly_revenue": 15000.00
}
```

### Get Revenue Chart Data
```
GET /api/dashboard/revenue-chart?period=last_7_days
```
Periods: `last_7_days`, `this_month`, `last_month`, `last_3_months`, `last_6_months`, `last_12_months`, `this_year`, `last_year`

Returns:
```json
{
  "labels": ["2025-01-24", "2025-01-25", ...],
  "data": [1200.50, 1500.00, ...],
  "period": "last_7_days"
}
```

### Get Invoice Status Chart
```
GET /api/dashboard/invoice-status-chart
```
Returns:
```json
{
  "labels": ["Paid", "Sent", "Overdue", "Draft"],
  "data": [1100, 55, 10, 35],
  "colors": ["#10b981", "#06b6d4", "#ef4444", "#6b7280"]
}
```

### Get Recent Activities
```
GET /api/dashboard/recent-activities?limit=10
```
Returns formatted activities with user info, icons, and relative times.

### Get Recent Invoices
```
GET /api/dashboard/recent-invoices?limit=5&page=1
```
Returns recent invoices with pagination info.

## Features

### Auto-Refresh
- Toggle switch in dashboard header
- Configurable interval (default 60 seconds)
- Saves preference to localStorage
- Visual indicator when active

### Period Filtering
- Revenue chart supports multiple time periods
- Click buttons to switch between week/month/year views
- Smooth chart animations on data change

### Activity Logging
- Automatic logging of all CRUD operations
- Tracks field changes with old/new values
- Human-readable descriptions in French
- Icon and color coding by action type

### Mobile Responsiveness
- All components are mobile-friendly
- Touch-optimized controls
- Responsive chart sizing
- Optimized API calls for mobile

## Usage Examples

### Manual Activity Logging
```php
use App\Models\ActivityLog;

// Log a custom activity
ActivityLog::log(
    'invoice',           // entity_type
    $invoiceId,          // entity_id
    'custom_action',     // action
    'Description here',  // description
    ['key' => 'value']   // optional changes data
);
```

### JavaScript Dashboard Control
```javascript
// Initialize dashboard (auto-runs on page load)
DashboardManager.init();

// Manual refresh
DashboardManager.refreshDashboard();

// Toggle auto-refresh
DashboardManager.toggleAutoRefresh(true);

// Update specific component
DashboardManager.updateRevenueChart('last_month');
```

## Troubleshooting

### Activity Logs Not Appearing
1. Check if migration was run: `SHOW TABLES LIKE 'activity_logs'`
2. Verify ActivityLog model is imported in controllers
3. Check for PHP errors in error logs

### Charts Not Updating
1. Check browser console for JavaScript errors
2. Verify API endpoints are accessible
3. Check for CSRF token issues

### Auto-Refresh Not Working
1. Check localStorage permissions
2. Verify interval is set correctly
3. Look for JavaScript errors

## Future Enhancements

1. **Custom Dashboards**: Allow users to customize widget layout
2. **Real-time Updates**: WebSocket integration for instant updates
3. **Export Options**: Export dashboard data to PDF/Excel
4. **More Chart Types**: Add pie charts, bar charts, etc.
5. **Performance Metrics**: Page load times, query performance
6. **Notification System**: Alert users of important activities

## Security Considerations

1. All API endpoints require authentication
2. Activity logs track IP addresses for audit trails
3. CSRF protection on all state-changing operations
4. Rate limiting should be implemented for API endpoints
5. Sensitive data is never exposed in activity logs

## Performance Optimization

1. Database indexes on frequently queried columns
2. Caching of dashboard stats (consider Redis)
3. Lazy loading of chart data
4. Pagination for large datasets
5. Debounced auto-refresh to prevent overload