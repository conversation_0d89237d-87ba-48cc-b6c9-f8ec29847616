{% extends "base-modern.twig" %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col">
                <h2 class="page-title">{{ __('invoices.generate_monthly') }}</h2>
                <div class="text-muted mt-1">{{ __('invoices.generate_monthly_desc') }}</div>
            </div>
            <div class="col-auto ms-auto">
                <a href="{{ base_url }}/invoices" class="btn btn-secondary">
                    <i class="ti ti-arrow-left"></i> {{ __('common.back') }}
                </a>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('invoices.generation_settings') }}</h3>
                </div>
                <div class="card-body">
                    <form id="generateForm">
                        <div class="mb-3">
                            <label class="form-label">{{ __('invoices.select_month') }}</label>
                            <input type="month" class="form-control" id="monthInput" name="month" value="{{ selectedMonth }}" max="{{ currentMonth }}">
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{{ __('invoices.select_users') }}</label>
                            <select class="form-select" id="userSelect" name="user_ids[]" multiple>
                                <option value="">{{ __('invoices.all_users_with_obligations') }}</option>
                                {% for user in usersWithObligations %}
                                    <option value="{{ user.id }}">{{ user.display_name }}</option>
                                {% endfor %}
                            </select>
                            <small class="text-muted">{{ __('invoices.leave_empty_for_all') }}</small>
                        </div>
                        
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-primary" id="previewBtn">
                                <i class="ti ti-eye"></i> {{ __('invoices.preview') }}
                            </button>
                            <button type="submit" class="btn btn-success" id="generateBtn" disabled>
                                <i class="ti ti-file-plus"></i> {{ __('common.generate') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ __('invoices.preview') }}</h3>
                </div>
                <div class="card-body" id="previewContainer">
                    <div class="text-center text-muted py-5">
                        <i class="ti ti-file-search fs-1 mb-3"></i>
                        <p>{{ __('invoices.click_preview_to_see') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const monthInput = document.getElementById('monthInput');
    const userSelect = document.getElementById('userSelect');
    const previewBtn = document.getElementById('previewBtn');
    const generateBtn = document.getElementById('generateBtn');
    const previewContainer = document.getElementById('previewContainer');
    const generateForm = document.getElementById('generateForm');
    
    // Initialize Select2 for user selection
    if (typeof $ !== 'undefined' && $.fn.select2) {
        $('#userSelect').select2({
            placeholder: "{{ __('invoices.select_users_placeholder') }}",
            allowClear: true
        });
    }
    
    // Preview button click
    previewBtn.addEventListener('click', function() {
        const month = monthInput.value;
        if (!month) {
            toastr.error("{{ __('invoices.please_select_month') }}");
            return;
        }
        
        previewBtn.disabled = true;
        previewBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.loading") }}';
        
        const selectedUsers = $('#userSelect').val() || [];
        const params = new URLSearchParams();
        params.append('month', month);
        
        // Handle array of user IDs properly
        if (selectedUsers && selectedUsers.length > 0) {
            selectedUsers.forEach(userId => {
                params.append('user_ids[]', userId);
            });
        }
        
        console.log('Preview params:', params.toString());
        
        fetch('{{ base_url }}/invoices/preview-monthly?' + params)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Preview response:', data);
                if (data.success) {
                    displayPreviews(data.previews);
                    generateBtn.disabled = false;
                } else {
                    toastr.error(data.message || "{{ __('common.error_occurred') }}");
                    console.error('Preview error:', data);
                }
            })
            .catch(error => {
                toastr.error("{{ __('common.error_occurred') }}: " + error.message);
                console.error('Fetch error:', error);
            })
            .finally(() => {
                previewBtn.disabled = false;
                previewBtn.innerHTML = '<i class="ti ti-eye"></i> {{ __("invoices.preview") }}';
            });
    });
    
    // Generate form submit
    generateForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!confirm("{{ __('invoices.confirm_generate_monthly') }}")) {
            return;
        }
        
        const formData = new FormData(generateForm);
        
        // Debug form data
        console.log('Form data being sent:');
        for (let [key, value] of formData.entries()) {
            console.log(key + ': ' + value);
        }
        
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.generating") }}';
        
        const generateUrl = '{{ base_url }}/invoices/generate-monthly';
        console.log('POST URL:', generateUrl);
        
        fetch(generateUrl, {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Generate response status:', response.status);
            console.log('Generate response URL:', response.url);
            if (!response.ok) {
                throw new Error('Network response was not ok: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            console.log('Generate response data:', data);
            
            // Log any generation errors
            if (data.results && data.results.errors && data.results.errors.length > 0) {
                console.error('Generation errors:', data.results.errors);
                data.results.errors.forEach(err => {
                    console.error('Error detail:', err);
                    toastr.error('Error for ' + err.user + ': ' + err.error, '', {
                        timeOut: 10000,
                        extendedTimeOut: 5000
                    });
                });
                
                // Also show in an alert for better visibility
                const errorMessages = data.results.errors.map(err => 
                    `User: ${err.user}\nError: ${err.error}`
                ).join('\n\n');
                
                alert('Invoice Generation Errors:\n\n' + errorMessages);
            }
            
            if (data.success) {
                toastr.success(data.message);
                
                // Debug the redirect
                const redirectUrl = data.redirect || '{{ base_url }}/invoices';
                console.log('Redirect URL from response:', data.redirect);
                console.log('Final redirect URL:', redirectUrl);
                console.log('Base URL:', '{{ base_url }}');
                console.log('Debug info:', data.debug);
                
                // Show debug info instead of auto-redirect
                toastr.info('Would redirect to: ' + redirectUrl);
                
                // Add manual redirect button
                setTimeout(() => {
                    if (confirm('Redirect to invoices page?\n' + redirectUrl)) {
                        window.location.href = redirectUrl;
                    }
                }, 2000);
            } else {
                toastr.error(data.message || "{{ __('common.error_occurred') }}");
                generateBtn.disabled = false;
            }
        })
        .catch(error => {
            toastr.error("{{ __('common.error_occurred') }}: " + error.message);
            console.error('Generate error:', error);
            generateBtn.disabled = false;
        })
        .finally(() => {
            generateBtn.innerHTML = '<i class="ti ti-file-plus"></i> {{ __("common.generate") }}';
        });
    });
    
    // Display preview results
    function displayPreviews(previews) {
        if (previews.length === 0) {
            previewContainer.innerHTML = `
                <div class="text-center text-muted py-5">
                    <i class="ti ti-file-off fs-1 mb-3"></i>
                    <p>{{ __('invoices.no_users_with_obligations') }}</p>
                </div>
            `;
            return;
        }
        
        let html = '<div class="table-responsive"><table class="table table-hover">';
        html += '<thead><tr>';
        html += '<th>{{ __("users.user") }}</th>';
        html += '<th>{{ __("invoices.period") }}</th>';
        html += '<th>{{ __("common.items") }}</th>';
        html += '<th class="text-end">{{ __("invoices.total") }}</th>';
        html += '</tr></thead><tbody>';
        
        let grandTotal = 0;
        
        previews.forEach(preview => {
            html += '<tr>';
            html += '<td>' + preview.user_name + '</td>';
            html += '<td>' + preview.period + '</td>';
            html += '<td>';
            preview.items.forEach(item => {
                html += '<div class="small">' + item.description + ': €' + parseFloat(item.unit_price).toFixed(2);
                if (item.vat_rate > 0) {
                    html += ' (TVA ' + item.vat_rate + '%)';
                }
                html += '</div>';
            });
            html += '</td>';
            html += '<td class="text-end"><strong>€' + parseFloat(preview.total).toFixed(2) + '</strong></td>';
            html += '</tr>';
            
            grandTotal += parseFloat(preview.total);
        });
        
        html += '</tbody>';
        html += '<tfoot><tr class="table-active">';
        html += '<td colspan="3" class="text-end"><strong>{{ __("invoices.grand_total") }}</strong></td>';
        html += '<td class="text-end"><strong>€' + grandTotal.toFixed(2) + '</strong></td>';
        html += '</tr></tfoot>';
        html += '</table></div>';
        
        html += '<div class="alert alert-info mt-3">';
        html += '<i class="ti ti-info-circle me-2"></i>';
        html += '{{ __("invoices.monthly_generation_info") }}';
        html += '</div>';
        
        previewContainer.innerHTML = html;
    }
});
</script>
{% endblock %}