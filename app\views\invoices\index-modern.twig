{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.invoices') }}{% endblock %}

{% block table_helper %}
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ base_url }}/css/table-helper.css">
<link rel="stylesheet" href="{{ base_url }}/css/table-dropdown-fix.css">
<link rel="stylesheet" href="{{ base_url }}/css/invoice-dropdown-override.css">
<style>
/* Dynamic search styles */
#search {
    padding-right: 2.5rem;
}

.table tr {
    transition: opacity 0.2s ease;
}

.table tr[style*="display: none"] {
    opacity: 0;
}

#search-results-count {
    position: absolute;
    bottom: -20px;
    left: 0;
    font-size: 0.875rem;
}

.highlight {
    background-color: #fff3cd;
    padding: 0 2px;
    border-radius: 2px;
}

/* TableHelper sort indicators styling */
.sort-indicator {
    font-size: 0.875rem;
}

th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
}

th[data-sortable="true"]:hover {
    background-color: rgba(0, 0, 0, 0.02);
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.invoices') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/invoices/bulk-generation" class="btn btn-success">
                <i class="bi bi-calendar-check me-2"></i>{{ __('invoices.bulk_generation') | default('Génération en masse') }}
            </a>
            <a href="{{ base_url }}/invoices/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('invoices.create_invoice') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('invoices.total_invoices') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_invoices|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-file-text text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('invoices.total_revenue') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ statistics.total_revenue|number_format(2, ',', '.') }} €
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-euro text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('invoices.unpaid_invoices') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.unpaid_invoices|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-exclamation-circle text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-danger h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-danger text-uppercase mb-1">
                                {{ __('invoices.outstanding_amount') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ statistics.outstanding_amount|number_format(2, ',', '.') }} €
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-cash-stack text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/invoices">
                <div class="row g-3 mb-3">
                    <div class="col-md-4">
                        <label for="status" class="form-label">{{ __('common.status') }}</label>
                        <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                            <option value="">{{ __('invoices.filter_all') }}</option>
                            <option value="draft" {{ filters.status == 'draft' ? 'selected' : '' }}>{{ __('invoices.filter_draft') }}</option>
                            <option value="sent" {{ filters.status == 'sent' ? 'selected' : '' }}>{{ __('invoices.status.sent') }}</option>
                            <option value="paid" {{ filters.status == 'paid' ? 'selected' : '' }}>{{ __('invoices.filter_paid') }}</option>
                            <option value="partial" {{ filters.status == 'partial' ? 'selected' : '' }}>{{ __('invoices.status.partial') }}</option>
                            <option value="overdue" {{ filters.status == 'overdue' ? 'selected' : '' }}>{{ __('invoices.filter_overdue') }}</option>
                            <option value="cancelled" {{ filters.status == 'cancelled' ? 'selected' : '' }}>{{ __('invoices.status.cancelled') }}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="date_from" class="form-label">{{ __('common.from') }}</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="{{ filters.date_from }}" onchange="this.form.submit()">
                    </div>
                    
                    <div class="col-md-4">
                        <label for="date_to" class="form-label">{{ __('common.to') }}</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="{{ filters.date_to }}" onchange="this.form.submit()">
                    </div>
                </div>
                
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="input-group">
                            <input type="text" class="form-control" id="search" name="search" value="{{ filters.search }}" 
                                   placeholder="{{ __('invoices.search_placeholder') }}">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ base_url }}/invoices?reset_filters=1" class="btn btn-secondary" onclick="InvoiceFilters.clear();">
                                <i class="bi bi-x-circle me-2"></i>{{ __('common.reset_filters') }}
                            </a>
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                    <i class="bi bi-download me-2"></i>{{ __('common.export') }}
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="exportInvoices('csv')">CSV</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportInvoices('excel')">Excel</a></li>
                                    <li><a class="dropdown-item" href="#" onclick="exportInvoices('pdf')">PDF</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Invoices Table -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="invoicesTable">
                    <thead>
                        <tr>
                            <th width="30" class="no-sort">
                                <input type="checkbox" class="form-check-input" id="selectAll">
                            </th>
                            <th data-column-type="text">{{ __('invoices.invoice_number') }}</th>
                            <th data-column-type="text">{{ __('invoices.document_type') }}</th>
                            <th data-column-type="text">{{ __('invoices.invoice_type') }}</th>
                            <th data-column-type="text">{{ __('clients.client') }}/{{ __('patients.patient') }}</th>
                            <th data-column-type="date">{{ __('invoices.issue_date') }}</th>
                            <th data-column-type="date">{{ __('invoices.due_date') }}</th>
                            <th class="text-end" data-column-type="number">{{ __('invoices.amount') }}</th>
                            <th data-column-type="text">{{ __('common.status') }}</th>
                            <th class="no-sort">{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for invoice in invoices %}
                        <tr>
                            <td>
                                <input type="checkbox" class="form-check-input invoice-checkbox" value="{{ invoice.id }}">
                            </td>
                            <td data-sort="{{ invoice.invoice_number }}">
                                <a href="{{ base_url }}/invoices/{{ invoice.id }}" class="text-decoration-none">
                                    {{ invoice.invoice_number }}
                                </a>
                            </td>
                            <td data-sort="{{ invoice.doc_type_display_name|default('unknown') }}">
                                {% if invoice.doc_type_color %}
                                    <span class="badge" style="background-color: {{ invoice.doc_type_color }};">
                                        <i class="{{ invoice.doc_type_icon }} me-1"></i>
                                        {{ invoice.doc_type_display_name }}
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.unknown') }}</span>
                                {% endif %}
                            </td>
                            <td data-sort="{{ invoice.type_name|default('') }}">
                                {% if invoice.type_color %}
                                    <span class="badge" style="background-color: {{ invoice.type_color }};">
                                        {{ invoice.type_name }}
                                    </span>
                                {% else %}
                                    {{ invoice.type_name }}
                                {% endif %}
                            </td>
                            <td data-sort="{{ invoice.patient_name|default(invoice.client_name) }}">
                                {% if invoice.patient_id %}
                                    <i class="bi bi-person text-primary"></i> {{ invoice.patient_name }}
                                {% else %}
                                    <i class="bi bi-building text-info"></i> {{ invoice.client_name }}
                                {% endif %}
                            </td>
                            <td data-sort="{{ invoice.issue_date }}T00:00:00">{{ invoice.issue_date|date('d/m/Y') }}</td>
                            <td data-sort="{{ invoice.due_date and invoice.due_date != '0000-00-00' ? invoice.due_date ~ 'T00:00:00' : '9999-12-31T00:00:00' }}">
                                {% if invoice.due_date and invoice.due_date != '0000-00-00' and invoice.due_date|date('Y') > 0 %}
                                    {{ invoice.due_date|date('d/m/Y') }}
                                    {% if invoice.is_overdue %}
                                        <i class="bi bi-exclamation-circle text-danger ms-1" title="{{ __('invoices.overdue') }}"></i>
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            <td class="text-end" data-sort="{{ invoice.total }}">{{ invoice.total|number_format(2, ',', '.') }} €</td>
                            <td data-sort="{{ invoice.status }}">
                                {% if invoice.status == 'draft' %}
                                    <span class="badge bg-secondary">{{ __('invoices.status.draft') }}</span>
                                {% elseif invoice.status == 'sent' %}
                                    <span class="badge bg-info">{{ __('invoices.status.sent') }}</span>
                                {% elseif invoice.status == 'paid' %}
                                    <span class="badge bg-success">{{ __('invoices.status.paid') }}</span>
                                {% elseif invoice.status == 'partial' %}
                                    <span class="badge bg-warning">{{ __('invoices.status.partial') }}</span>
                                {% elseif invoice.status == 'overdue' %}
                                    <span class="badge bg-danger">{{ __('invoices.status.overdue') }}</span>
                                {% elseif invoice.status == 'cancelled' %}
                                    <span class="badge bg-dark">{{ __('invoices.status.cancelled') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ base_url }}/invoices/{{ invoice.id }}" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.view') }}">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    {% if invoice.status == 'draft' %}
                                    <a href="{{ base_url }}/invoices/{{ invoice.id }}/edit" 
                                       class="btn btn-outline-secondary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    {% endif %}
                                    <a href="{{ base_url }}/invoices/{{ invoice.id }}/print" 
                                       class="btn btn-outline-info" 
                                       target="_blank"
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.print') }}">
                                        <i class="bi bi-printer"></i>
                                    </a>
                                    <a href="{{ base_url }}/invoices/{{ invoice.id }}/download" 
                                       class="btn btn-outline-success" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('invoices.download_pdf') }}">
                                        <i class="bi bi-download"></i>
                                    </a>
                                    {% if invoice.status != 'paid' and invoice.status != 'cancelled' %}
                                    <button type="button" 
                                            class="btn btn-outline-primary" 
                                            onclick="sendInvoice({{ invoice.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('invoices.send_invoice') }}">
                                        <i class="bi bi-envelope"></i>
                                    </button>
                                    {% endif %}
                                    {% if invoice.status == 'sent' or invoice.status == 'partial' %}
                                    <button type="button" 
                                            class="btn btn-outline-warning" 
                                            onclick="recordPayment({{ invoice.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('invoices.record_payment') }}">
                                        <i class="bi bi-cash"></i>
                                    </button>
                                    {% endif %}
                                    {% if invoice.status == 'draft' %}
                                    <a href="{{ base_url }}/invoices/create?duplicate_id={{ invoice.id }}" 
                                       class="btn btn-outline-info" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.duplicate')|default('Dupliquer') }}">
                                        <i class="bi bi-copy"></i>
                                    </a>
                                    {% endif %}
                                    {% if (invoice.status == 'draft' and (isAdmin or isManager)) or (invoice.status != 'draft' and isAdmin) %}
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="deleteInvoice({{ invoice.id }}, '{{ invoice.status }}')"
                                            data-bs-toggle="tooltip" 
                                            title="{{ isAdmin and invoice.status != 'draft' ? __('common.admin_delete') : __('common.delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-4 text-muted">
                                {{ __('invoices.no_invoices_found') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Bulk Actions -->
            {% if invoices|length > 0 %}
            <div class="d-flex justify-content-between align-items-center mt-3">
                <div class="dropdown">
                    <button class="btn btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" disabled id="bulkActionsBtn">
                        {{ __('common.bulk_actions') }}
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item bulk-action-item" href="#" data-action="send">
                            <i class="bi bi-envelope me-2"></i>{{ __('invoices.send_selected') }}
                        </a></li>
                        <li><a class="dropdown-item bulk-action-item" href="#" data-action="mark-sent">
                            <i class="bi bi-check-circle me-2"></i>{{ __('invoices.mark_selected_as_sent') }}
                        </a></li>
                        <li><a class="dropdown-item bulk-action-item" href="#" data-action="download-pdf">
                            <i class="bi bi-file-earmark-pdf me-2"></i>{{ __('invoices.download_selected_pdfs') }}
                        </a></li>
                        <li><a class="dropdown-item bulk-action-item" href="#" data-action="export">
                            <i class="bi bi-download me-2"></i>{{ __('invoices.export_selected') }}
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item bulk-action-item text-danger" href="#" data-action="delete">
                            <i class="bi bi-trash me-2"></i>{{ __('invoices.delete_selected') }}
                        </a></li>
                    </ul>
                </div>
                
                <!-- Pagination -->
                {{ include('_partials/pagination.twig', {
                    'current_page': current_page,
                    'total_pages': total_pages,
                    'base_url': base_url ~ '/invoices'
                }) }}
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Export invoices with current filters
function exportInvoices(format) {
    const form = document.getElementById('filterForm');
    const formData = new FormData(form);
    const params = new URLSearchParams();
    
    // Add format
    params.append('format', format);
    
    // Add all filter values
    for (let [key, value] of formData.entries()) {
        if (value && value !== '') {
            params.append(key, value);
        }
    }
    
    // Redirect to export URL with parameters
    window.location.href = '{{ base_url }}/invoices/export?' + params.toString();
}

// Dynamic Search Implementation
function setupDynamicSearch() {
    const searchInput = document.getElementById('search');
    const tableBody = document.querySelector('.table tbody');
    const allRows = tableBody ? Array.from(tableBody.querySelectorAll('tr')) : [];
    
    if (!searchInput || allRows.length === 0) {
        console.log('Search input or table rows not found');
        return;
    }
    
    console.log('Setting up dynamic search for', allRows.length, 'rows');
    
    // Store original display state of rows
    allRows.forEach(row => {
        row.dataset.originalDisplay = row.style.display || '';
    });
    
    // Load saved search from localStorage if no server-side search
    if (!searchInput.value && localStorage.getItem('invoice_search')) {
        searchInput.value = localStorage.getItem('invoice_search');
    }
    
    // Create debounced search function
    let searchTimeout;
    const performSearch = () => {
        const searchTerm = searchInput.value.toLowerCase().trim();
        console.log('Searching for:', searchTerm);
        
        // Save to localStorage
        if (searchTerm) {
            localStorage.setItem('invoice_search', searchInput.value);
        } else {
            localStorage.removeItem('invoice_search');
        }
        
        if (searchTerm === '') {
            // Show all rows
            allRows.forEach(row => {
                row.style.display = row.dataset.originalDisplay;
            });
            updateResultsCount(allRows.length);
            return;
        }
        
        let visibleCount = 0;
        
        allRows.forEach(row => {
            // Get searchable text from specific columns
            const invoiceNumber = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
            const clientName = row.querySelector('td:nth-child(5)')?.textContent.toLowerCase() || '';
            const amount = row.querySelector('td:nth-child(8)')?.textContent.toLowerCase() || '';
            const status = row.querySelector('td:nth-child(9)')?.textContent.toLowerCase() || '';
            
            // Check if any field contains the search term
            if (invoiceNumber.includes(searchTerm) || 
                clientName.includes(searchTerm) || 
                amount.includes(searchTerm) ||
                status.includes(searchTerm)) {
                row.style.display = row.dataset.originalDisplay;
                visibleCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        updateResultsCount(visibleCount);
    };
    
    // Add results counter
    function updateResultsCount(count) {
        let counterEl = document.getElementById('search-results-count');
        if (!counterEl) {
            counterEl = document.createElement('small');
            counterEl.id = 'search-results-count';
            counterEl.className = 'text-muted ms-2';
            searchInput.parentElement.appendChild(counterEl);
        }
        
        if (searchInput.value.trim() !== '') {
            counterEl.textContent = `(${count} résultat${count !== 1 ? 's' : ''})`;
        } else {
            counterEl.textContent = '';
        }
    }
    
    // Add event listeners
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(performSearch, 300); // 300ms debounce
    });
    
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            this.value = '';
            performSearch();
        }
    });
    
    // Clear search button
    const clearBtn = document.createElement('button');
    clearBtn.type = 'button';
    clearBtn.className = 'btn btn-sm position-absolute end-0 top-50 translate-middle-y me-5';
    clearBtn.style.display = 'none';
    clearBtn.style.zIndex = '10';
    clearBtn.innerHTML = '<i class="bi bi-x-circle"></i>';
    clearBtn.onclick = function() {
        searchInput.value = '';
        localStorage.removeItem('invoice_search');
        performSearch();
        this.style.display = 'none';
    };
    
    searchInput.parentElement.style.position = 'relative';
    searchInput.parentElement.appendChild(clearBtn);
    
    // Show/hide clear button
    searchInput.addEventListener('input', function() {
        clearBtn.style.display = this.value ? 'block' : 'none';
    });
    
    // Perform initial search if there's a value
    if (searchInput.value) {
        performSearch();
    }
}

// Filter management with localStorage
const InvoiceFilters = {
    storageKey: 'invoice_filters',
    
    // Save filters to localStorage
    save: function(filters) {
        localStorage.setItem(this.storageKey, JSON.stringify(filters));
    },
    
    // Load filters from localStorage
    load: function() {
        const stored = localStorage.getItem(this.storageKey);
        return stored ? JSON.parse(stored) : {};
    },
    
    // Clear all filters
    clear: function() {
        localStorage.removeItem(this.storageKey);
        localStorage.removeItem('invoice_search');
    },
    
    // Apply filters to form
    applyToForm: function() {
        const urlParams = new URLSearchParams(window.location.search);
        
        // If resetting filters, clear and return
        if (urlParams.get('reset_filters') === '1') {
            this.clear();
            return;
        }
        
        const filters = this.load();
        
        // Only apply stored filters if no URL parameters exist
        if (urlParams.toString() === '') {
            if (filters.status) document.getElementById('status').value = filters.status;
            if (filters.date_from) document.getElementById('date_from').value = filters.date_from;
            if (filters.date_to) document.getElementById('date_to').value = filters.date_to;
            
            // If we have stored filters but no URL params, redirect with filters
            if (Object.keys(filters).length > 0) {
                const newUrl = new URL(window.location.href);
                for (const [key, value] of Object.entries(filters)) {
                    if (value) newUrl.searchParams.set(key, value);
                }
                window.location.href = newUrl.toString();
                return;
            }
        }
        
        // Save current form values to localStorage
        this.saveFromForm();
    },
    
    // Save current form values
    saveFromForm: function() {
        const filters = {
            status: document.getElementById('status').value,
            date_from: document.getElementById('date_from').value,
            date_to: document.getElementById('date_to').value
        };
        
        // Only save non-empty values
        const cleanFilters = {};
        for (const [key, value] of Object.entries(filters)) {
            if (value) cleanFilters[key] = value;
        }
        
        this.save(cleanFilters);
    }
};

// Wait for page to be fully loaded
window.addEventListener('load', function() {
    console.log('Setting up invoice filters...');
    
    // Apply stored filters
    InvoiceFilters.applyToForm();
    
    // Don't setup dynamic search as TableHelper will handle it
    // setupDynamicSearch();
    
    // Method 1: Direct event listener on dropdown
    const statusDropdown = document.getElementById('status');
    if (statusDropdown) {
        console.log('Status dropdown found, adding change listener');
        
        statusDropdown.addEventListener('change', function() {
            console.log('Status changed to:', this.value);
            
            // Save to localStorage
            InvoiceFilters.saveFromForm();
            
            // Find the parent form
            const form = this.closest('form');
            if (form) {
                console.log('Submitting form...');
                // Show loading
                const overlay = document.getElementById('loading-overlay');
                if (overlay) {
                    overlay.style.display = 'flex';
                }
                // Submit form
                form.submit();
            } else {
                // Fallback: Direct navigation
                console.log('Form not found, using direct navigation');
                const currentUrl = new URL(window.location.href);
                if (this.value) {
                    currentUrl.searchParams.set('status', this.value);
                } else {
                    currentUrl.searchParams.delete('status');
                }
                window.location.href = currentUrl.toString();
            }
        });
        
        // Also add for date inputs
        ['date_from', 'date_to'].forEach(id => {
            const input = document.getElementById(id);
            if (input) {
                input.addEventListener('change', function() {
                    // Save to localStorage
                    InvoiceFilters.saveFromForm();
                    
                    const form = this.closest('form');
                    if (form) {
                        form.submit();
                    }
                });
            }
        });
    } else {
        console.error('Status dropdown not found!');
    }
});

// Backup: Also try on DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    // Double-check and add listener if not already added
    setTimeout(function() {
        const statusDropdown = document.getElementById('status');
        if (statusDropdown && !statusDropdown.hasAttribute('data-listener-added')) {
            console.log('Adding backup listener to status dropdown');
            statusDropdown.setAttribute('data-listener-added', 'true');
            
            statusDropdown.onchange = function() {
                console.log('Status changed (backup handler):', this.value);
                const form = this.closest('form');
                if (form) {
                    form.submit();
                }
            };
        }
    }, 500);
});

// This event handler is now consolidated in the main DOMContentLoaded listener above

function updateBulkActions() {
    const selected = document.querySelectorAll('.invoice-checkbox:checked').length;
    const bulkBtn = document.getElementById('bulkActionsBtn');
    if (bulkBtn) {
        bulkBtn.disabled = selected === 0;
    }
}

// Ensure updateBulkActions is available globally
window.updateBulkActions = updateBulkActions;

function getSelectedInvoices() {
    console.log('getSelectedInvoices called');
    const checkboxes = document.querySelectorAll('.invoice-checkbox:checked');
    console.log('Found checkboxes:', checkboxes);
    const ids = Array.from(checkboxes).map(cb => cb.value);
    console.log('Extracted IDs:', ids);
    return ids;
}

// Ensure getSelectedInvoices is available globally
window.getSelectedInvoices = getSelectedInvoices;

function sendInvoice(id) {
    if (confirm('{{ __("invoices.send_confirm") }}')) {
        // Create a form and submit it as POST
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/invoices/' + id + '/send';
        
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = '{{ csrf_token }}';
        form.appendChild(csrfInput);
        
        // Ensure email is sent
        const sendEmailInput = document.createElement('input');
        sendEmailInput.type = 'hidden';
        sendEmailInput.name = 'send_email';
        sendEmailInput.value = 'true';
        form.appendChild(sendEmailInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function recordPayment(id) {
    // Open payment modal or redirect to payment page
    window.location.href = '{{ base_url }}/invoices/' + id + '/payment';
}

function deleteInvoice(id, status) {
    console.log('deleteInvoice called with id:', id, 'status:', status);
    const isAdmin = {{ isAdmin ? 'true' : 'false' }};
    const isSent = status && status !== 'draft';
    console.log('isAdmin:', isAdmin, 'isSent:', isSent);
    
    if (isSent && isAdmin) {
        // Show stronger warning for admins deleting sent invoices
        if (confirm('{{ __("invoices.admin_delete_sent_confirm") | default("WARNING: You are about to delete a sent invoice. This action cannot be undone and may affect accounting records. Are you sure?") }}')) {
            window.location.href = '{{ base_url }}/invoices/' + id + '/delete';
        }
    } else {
        // Normal confirmation for draft invoices
        console.log('Showing confirmation for draft invoice');
        if (confirm('{{ __("common.are_you_sure") }}')) {
            console.log('User confirmed, redirecting to:', '{{ base_url }}/invoices/' + id + '/delete');
            window.location.href = '{{ base_url }}/invoices/' + id + '/delete';
        } else {
            console.log('User cancelled deletion');
        }
    }
}

function bulkAction(action) {
    console.log('=== bulkAction called ===');
    console.log('Action:', action);
    
    const selected = getSelectedInvoices();
    console.log('Selected invoices:', selected);
    console.log('Number of selected invoices:', selected.length);
    
    if (selected.length === 0) {
        console.log('No invoices selected, returning');
        alert('{{ __("invoices.no_invoices_selected") | default("Please select at least one invoice") }}');
        return;
    }
    
    if (action === 'delete' && !confirm('{{ __("invoices.bulk_delete_confirm") }}')) {
        console.log('Delete cancelled by user');
        return;
    }
    
    console.log('Creating form for submission');
    // Submit bulk action
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ base_url }}/invoices/bulk-' + action;
    console.log('Form action URL:', form.action);
    
    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrf_token';
    csrfInput.value = '{{ csrf_token }}';
    form.appendChild(csrfInput);
    console.log('CSRF token added');
    
    selected.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'invoice_ids[]';
        input.value = id;
        form.appendChild(input);
    });
    console.log('Invoice IDs added to form');
    
    document.body.appendChild(form);
    console.log('Form appended to body, submitting...');
    form.submit();
}

// Ensure all functions are available globally IMMEDIATELY
window.bulkAction = bulkAction;
window.sendInvoice = sendInvoice;
window.recordPayment = recordPayment;
window.deleteInvoice = deleteInvoice;

// Initialize Bootstrap tooltips and setup event handlers
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== DOMContentLoaded - Initializing invoice page ===');
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Verify functions are available
    console.log('Function availability check:');
    console.log('- bulkAction:', typeof window.bulkAction);
    console.log('- getSelectedInvoices:', typeof window.getSelectedInvoices);
    console.log('- updateBulkActions:', typeof window.updateBulkActions);
    
    // Initialize bulk actions button state
    updateBulkActions();
    
    // Setup checkbox event handlers
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            console.log('Select all checkbox changed:', this.checked);
            const checkboxes = document.querySelectorAll('.invoice-checkbox');
            checkboxes.forEach(cb => cb.checked = this.checked);
            updateBulkActions();
        });
    }
    
    // Setup individual checkbox handlers using event delegation
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('invoice-checkbox')) {
            console.log('Invoice checkbox changed');
            updateBulkActions();
            
            // Update select all checkbox state
            const allCheckboxes = document.querySelectorAll('.invoice-checkbox');
            const checkedBoxes = document.querySelectorAll('.invoice-checkbox:checked');
            const selectAll = document.getElementById('selectAll');
            if (selectAll) {
                selectAll.checked = allCheckboxes.length > 0 && allCheckboxes.length === checkedBoxes.length;
                selectAll.indeterminate = checkedBoxes.length > 0 && checkedBoxes.length < allCheckboxes.length;
            }
        }
    });
    
    // Setup bulk action dropdown click handlers
    document.addEventListener('click', function(e) {
        // Check if clicked element is a bulk action item
        if (e.target.closest('.bulk-action-item')) {
            e.preventDefault();
            e.stopPropagation();
            
            const actionItem = e.target.closest('.bulk-action-item');
            const action = actionItem.getAttribute('data-action');
            
            console.log('Bulk action clicked:', action);
            
            // Call the bulk action function
            if (window.bulkAction && typeof window.bulkAction === 'function') {
                window.bulkAction(action);
            } else {
                console.error('bulkAction function not available');
            }
        }
    });
    
    // Verify dropdown items exist
    setTimeout(() => {
        const dropdownItems = document.querySelectorAll('.bulk-action-item');
        console.log('Bulk action dropdown items found:', dropdownItems.length);
        dropdownItems.forEach(item => {
            console.log('Dropdown item:', item.textContent.trim(), 'data-action:', item.getAttribute('data-action'));
        });
    }, 500);
});

// Initialize TableHelper for sorting when script is loaded
window.addEventListener('load', function() {
    // Check if TableHelper is available
    if (typeof TableHelper === 'undefined') {
        console.error('TableHelper not loaded');
        return;
    }
    
    // First, disable the existing dynamic search to avoid conflicts
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.removeAttribute('data-dynamic-search');
        // Clear any existing event listeners by cloning and replacing
        const newSearchInput = searchInput.cloneNode(true);
        searchInput.parentNode.replaceChild(newSearchInput, searchInput);
    }
    
    // Dynamically detect column configuration
    const table = document.getElementById('invoicesTable');
    const headers = table.querySelectorAll('thead th');
    const searchColumns = [];
    let invoiceNumberColumn = 1; // Default
    
    // Build search columns array based on actual table structure
    headers.forEach((header, index) => {
        if (!header.classList.contains('no-sort') && index > 0) {
            searchColumns.push(index);
            
            // Try to identify invoice number column
            const headerText = header.textContent.trim().toLowerCase();
            if (headerText.includes('number') || headerText.includes('numéro') || headerText.includes('n°')) {
                invoiceNumberColumn = index;
            }
        }
    });
    
    console.log('Detected searchable columns:', searchColumns);
    console.log('Invoice number column:', invoiceNumberColumn);
    
    // Initialize TableHelper with dynamic configuration
    window.invoiceTableHelper = new TableHelper({
        tableId: 'invoicesTable',
        searchInputId: 'search',
        searchColumns: searchColumns, // Dynamically detected columns
        storageKey: 'invoice_table_state',
        sortable: true,
        reorderable: false,
        defaultSort: { column: invoiceNumberColumn, direction: 'desc' }, // Sort by invoice number
        bulkActions: [], // Disable TableHelper bulk actions as we have custom ones
        exportFormats: [], // Disable TableHelper export as we have custom export
        translations: {
            search: '{{ __("common.search") }}',
            noResults: '{{ __("common.no_results") }}',
            results: '{{ __("common.results") }}',
            sortAsc: '{{ __("common.sort_asc")|default("Sort ascending") }}',
            sortDesc: '{{ __("common.sort_desc")|default("Sort descending") }}'
        }
    });
    
    // TableHelper is now initialized
    console.log('TableHelper initialized for invoices table');
    
    // Override the sort comparison to handle dates properly
    if (window.invoiceTableHelper && window.invoiceTableHelper.sortTable) {
        const originalSortTable = window.invoiceTableHelper.sortTable;
        window.invoiceTableHelper.sortTable = function(column, direction, isMultiSort) {
            console.log(`Sorting column ${column}, direction: ${direction || 'toggle'}`);
            
            // Call original method
            originalSortTable.call(this, column, direction, isMultiSort);
            
            // Log what happened
            const tbody = this.table.querySelector('tbody');
            const firstRow = tbody.querySelector('tr');
            if (firstRow) {
                const cell = firstRow.cells[column];
                if (cell) {
                    console.log(`First cell in sorted column: "${cell.textContent.trim()}", data-sort: "${cell.getAttribute('data-sort')}"`);
                }
            }
        };
    }
    
    // Enhanced debugging for column detection
    setTimeout(function() {
        const actualHeaders = document.querySelectorAll('#invoicesTable thead th');
        console.log('\n=== Actual Table Structure ===');
        actualHeaders.forEach((header, index) => {
            const columnType = header.getAttribute('data-column-type');
            const sortable = header.getAttribute('data-sortable');
            console.log(`Column ${index}: "${header.textContent.trim()}" - Type: ${columnType}, Sortable: ${sortable}, No-sort: ${header.classList.contains('no-sort')}`);
        });
        
        // Check actual cell structure
        const firstRow = table.querySelector('tbody tr');
        if (firstRow && !firstRow.querySelector('td[colspan]')) {
            console.log('\n=== First Row Data ===');
            Array.from(firstRow.cells).forEach((cell, index) => {
                const dataSortValue = cell.getAttribute('data-sort');
                console.log(`Cell ${index}: "${cell.textContent.trim().substring(0, 20)}..." - data-sort: "${dataSortValue}"`);
            });
        }
        
        // Verify date columns
        const dateHeaders = document.querySelectorAll('#invoicesTable th[data-column-type="date"]');
        console.log('\n=== Date Columns ===');
        dateHeaders.forEach((header) => {
            const index = Array.from(actualHeaders).indexOf(header);
            console.log(`Date column at index ${index}: "${header.textContent.trim()}"`);
        });
    }, 500);
});

// Fix date sorting with a simpler approach
setTimeout(function() {
    // Ensure checkbox functionality still works after TableHelper initialization
    const selectAll = document.getElementById('selectAll');
    if (selectAll) {
        updateBulkActions();
    }
    
    // Create a custom date-aware sorting function
    if (window.invoiceTableHelper) {
        // Find which columns contain dates by checking the first data row
        const dateColumns = new Set();
        const table = document.getElementById('invoicesTable');
        const firstDataRow = table.querySelector('tbody tr:not(.empty-state)');
        
        if (firstDataRow) {
            Array.from(firstDataRow.cells).forEach((cell, index) => {
                const sortValue = cell.getAttribute('data-sort');
                if (sortValue && /^\d{4}-\d{2}-\d{2}/.test(sortValue)) {
                    dateColumns.add(index);
                    console.log(`Column ${index} identified as date column`);
                }
            });
        }
        
        // Override the sort to handle dates correctly
        const originalSortTable = window.invoiceTableHelper.sortTable;
        window.invoiceTableHelper.sortTable = function(column, direction, isMultiSort) {
            console.log(`Sorting column ${column} (is date column: ${dateColumns.has(column)})`);
            
            // If it's a date column, we need to ensure proper date comparison
            if (dateColumns.has(column)) {
                const tbody = this.table.querySelector('tbody');
                const rows = Array.from(tbody.querySelectorAll('tr')).filter(row => 
                    !row.querySelector('td[colspan]') // Exclude empty state rows
                );
                
                // Determine sort direction
                if (!isMultiSort) {
                    this.sortOrder = [];
                }
                
                const existingSortIndex = this.sortOrder.findIndex(s => s.column === column);
                if (existingSortIndex >= 0) {
                    const currentDirection = this.sortOrder[existingSortIndex].direction;
                    direction = direction || (currentDirection === 'asc' ? 'desc' : 'asc');
                    this.sortOrder[existingSortIndex].direction = direction;
                } else {
                    direction = direction || 'asc';
                    this.sortOrder.push({ column, direction });
                }
                
                // Sort rows by date
                rows.sort((a, b) => {
                    const aCell = a.cells[column];
                    const bCell = b.cells[column];
                    
                    const aDateStr = aCell ? aCell.getAttribute('data-sort') : '';
                    const bDateStr = bCell ? bCell.getAttribute('data-sort') : '';
                    
                    // Parse dates
                    const aDate = new Date(aDateStr.replace('T00:00:00', ''));
                    const bDate = new Date(bDateStr.replace('T00:00:00', ''));
                    
                    // Handle invalid dates
                    if (isNaN(aDate)) return direction === 'asc' ? 1 : -1;
                    if (isNaN(bDate)) return direction === 'asc' ? -1 : 1;
                    
                    // Compare dates
                    const diff = aDate.getTime() - bDate.getTime();
                    return direction === 'asc' ? diff : -diff;
                });
                
                // Re-append sorted rows
                rows.forEach(row => tbody.appendChild(row));
                
                // Update sort indicators
                this.updateMultiSortIndicators();
                
                // Save state
                this.saveState();
                
                console.log('Date column sorted successfully');
                return;
            }
            
            // For non-date columns, use original sorting
            originalSortTable.call(this, column, direction, isMultiSort);
        };
        
        console.log('Date sorting enhancement applied');
    }
}, 200);
</script>

<script src="{{ base_url }}/js/invoice-dropdown-fix.js"></script>

{% endblock %}