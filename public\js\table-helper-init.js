/**
 * Table Helper Auto-initialization
 * Automatically initializes TableHelper for all tables that use the table-helper macro
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to initialize table helper
    function initializeTableHelpers() {
        // Check if TableHelper class is available
        if (typeof TableHelper === 'undefined') {
            // Try again in 100ms
            setTimeout(initializeTableHelpers, 100);
            return;
        }
        
        // Check if we have table configuration
        if (window.tableHelperConfig && !window.tableHelper) {
            // Check if the table should be skipped
            const table = document.getElementById(window.tableHelperConfig.tableId);
            if (table && (table.hasAttribute('data-no-table-helper') || table.hasAttribute('data-no-init') || table.classList.contains('no-table-helper'))) {
                console.log('Skipping TableHelper for table:', window.tableHelperConfig.tableId);
                return;
            }
            
            try {
                // Initialize the table helper
                window.tableHelper = new TableHelper(window.tableHelperConfig);
                console.log('TableHelper initialized successfully for', window.tableHelperConfig.tableId);
                
                // Dispatch custom event to signal initialization complete
                document.dispatchEvent(new CustomEvent('tableHelperReady', { 
                    detail: { tableHelper: window.tableHelper } 
                }));
            } catch (error) {
                console.error('Error initializing table helper:', error);
            }
        }
        
        // Also check for multiple table configurations (for pages with multiple tables)
        if (window.tableHelperConfigs && Array.isArray(window.tableHelperConfigs)) {
            window.tableHelpers = window.tableHelpers || {};
            
            window.tableHelperConfigs.forEach(config => {
                if (!window.tableHelpers[config.tableId]) {
                    // Check if the table should be skipped
                    const table = document.getElementById(config.tableId);
                    if (table && (table.hasAttribute('data-no-table-helper') || table.hasAttribute('data-no-init') || table.classList.contains('no-table-helper'))) {
                        console.log('Skipping TableHelper for table:', config.tableId);
                        return;
                    }
                    
                    try {
                        window.tableHelpers[config.tableId] = new TableHelper(config);
                        console.log('TableHelper initialized for table:', config.tableId);
                    } catch (error) {
                        console.error('Error initializing table helper for', config.tableId, ':', error);
                    }
                }
            });
        }
    }
    
    // Start initialization process
    initializeTableHelpers();
});

/**
 * Helper function to get table helper instance
 * @param {string} tableId - Optional table ID for multiple tables
 * @returns {TableHelper|null} The table helper instance
 */
window.getTableHelper = function(tableId) {
    if (tableId && window.tableHelpers && window.tableHelpers[tableId]) {
        return window.tableHelpers[tableId];
    }
    return window.tableHelper || null;
};

/**
 * Helper function to debug table state
 * @param {string} storageKey - The storage key to debug
 */
window.debugTableState = function(storageKey) {
    const key = storageKey || 'table_filters';
    const state = localStorage.getItem(key + '_state');
    const filters = localStorage.getItem(key);
    
    console.group('=== Table State Debug ===');
    console.log('Storage Key:', key);
    
    if (state) {
        try {
            const parsedState = JSON.parse(state);
            console.log('Saved State:', parsedState);
            if (parsedState.sort) {
                console.log('Sort Column:', parsedState.sort.column);
                console.log('Sort Direction:', parsedState.sort.direction);
            }
            if (parsedState.sortOrder) {
                console.log('Multi-column Sort:', parsedState.sortOrder);
            }
        } catch (e) {
            console.error('Invalid state JSON:', state);
        }
    } else {
        console.log('No saved state found');
    }
    
    if (filters) {
        try {
            const parsedFilters = JSON.parse(filters);
            console.log('Saved Filters:', parsedFilters);
        } catch (e) {
            console.error('Invalid filters JSON:', filters);
        }
    } else {
        console.log('No saved filters found');
    }
    
    console.groupEnd();
};

/**
 * Helper function to clear all table states
 * @param {string} prefix - Optional prefix to match storage keys
 */
window.clearAllTableStates = function(prefix = '') {
    const keysToRemove = [];
    
    // Find all relevant keys
    for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('_state') || key.includes('_filters')) && key.includes(prefix)) {
            keysToRemove.push(key);
        }
    }
    
    // Remove the keys
    keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log('Removed:', key);
    });
    
    console.log(`Cleared ${keysToRemove.length} table state entries`);
};