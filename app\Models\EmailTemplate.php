<?php

namespace App\Models;

use App\Core\Model;

class EmailTemplate extends Model
{
    protected $table = 'email_templates';
    
    protected $fillable = [
        'name',
        'code',
        'subject',
        'body',
        'body_html',
        'body_text',
        'variables',
        'invoice_type',
        'email_type',
        'conditions',
        'priority',
        'is_active',
        'parent_template_id'
    ];
    
    protected $casts = [
        'variables' => 'array',
        'conditions' => 'array',
        'is_active' => 'boolean',
        'priority' => 'integer',
        'parent_template_id' => 'integer'
    ];
    
    /**
     * Get template by code
     */
    public static function getByCode($code)
    {
        return self::where('code', $code)->where('is_active', true)->first();
    }
    
    
    /**
     * Get available variables for this template
     */
    public function getAvailableVariables()
    {
        return $this->variables ?: [];
    }
    
    /**
     * Parse conditions JSON into array
     */
    public function getConditionsArray()
    {
        return $this->conditions ?: [];
    }
    
    /**
     * Get human-readable priority label
     */
    public function getPriorityLabel()
    {
        $priorities = [
            1 => 'Lowest',
            2 => 'Low',
            3 => 'Normal',
            4 => 'High',
            5 => 'Highest'
        ];
        
        return $priorities[$this->priority] ?? 'Normal';
    }
    
    /**
     * Get formatted invoice type label
     */
    public function getInvoiceTypeLabel()
    {
        if (!$this->invoice_type) {
            return 'All';
        }
        
        $types = [
            'standard' => 'Standard Invoice',
            'proforma' => 'Proforma Invoice',
            'credit' => 'Credit Note',
            'debit' => 'Debit Note',
            'recurring' => 'Recurring Invoice'
        ];
        
        return $types[$this->invoice_type] ?? ucfirst($this->invoice_type);
    }
    
    /**
     * Get parent template relationship
     */
    public function parentTemplate()
    {
        return $this->belongsTo(EmailTemplate::class, 'parent_template_id');
    }
    
    /**
     * Get child templates relationship
     */
    public function childTemplates()
    {
        return $this->hasMany(EmailTemplate::class, 'parent_template_id');
    }
    
    /**
     * Check if template has a parent
     */
    public function hasParent()
    {
        return !is_null($this->parent_template_id);
    }
    
    /**
     * Get email type label
     */
    public function getEmailTypeLabel()
    {
        $types = [
            'invoice' => 'Invoice Email',
            'reminder' => 'Payment Reminder',
            'notification' => 'General Notification',
            'welcome' => 'Welcome Email',
            'confirmation' => 'Confirmation Email'
        ];
        
        return $types[$this->email_type] ?? ucfirst($this->email_type ?? 'invoice');
    }
    
    /**
     * Override parse method to handle HTML and text versions
     */
    public function parse($variables = [])
    {
        $subject = $this->subject;
        $body = $this->body;
        $bodyHtml = $this->body_html ?: $this->body;
        $bodyText = $this->body_text ?: strip_tags($this->body);
        
        foreach ($variables as $key => $value) {
            $subject = str_replace('{' . $key . '}', $value, $subject);
            $body = str_replace('{' . $key . '}', $value, $body);
            $bodyHtml = str_replace('{' . $key . '}', $value, $bodyHtml);
            $bodyText = str_replace('{' . $key . '}', $value, $bodyText);
        }
        
        return [
            'subject' => $subject,
            'body' => $body,
            'body_html' => $bodyHtml,
            'body_text' => $bodyText
        ];
    }
    
    /**
     * Get template by code and invoice type
     */
    public static function getByCodeAndType($code, $invoiceType = null)
    {
        $query = self::where('code', $code)->where('is_active', true);
        
        if ($invoiceType) {
            $query->where(function($q) use ($invoiceType) {
                $q->where('invoice_type', $invoiceType)
                  ->orWhereNull('invoice_type');
            })->orderByRaw('invoice_type IS NULL ASC');
        }
        
        return $query->orderBy('priority', 'desc')->first();
    }
    
    /**
     * Check if template meets conditions
     */
    public function meetsConditions($context = [])
    {
        $conditions = $this->getConditionsArray();
        
        if (empty($conditions)) {
            return true;
        }
        
        foreach ($conditions as $condition) {
            $field = $condition['field'] ?? null;
            $operator = $condition['operator'] ?? '=';
            $value = $condition['value'] ?? null;
            
            if (!$field || !isset($context[$field])) {
                continue;
            }
            
            $contextValue = $context[$field];
            
            switch ($operator) {
                case '=':
                case '==':
                    if ($contextValue != $value) return false;
                    break;
                case '!=':
                case '<>':
                    if ($contextValue == $value) return false;
                    break;
                case '>':
                    if ($contextValue <= $value) return false;
                    break;
                case '>=':
                    if ($contextValue < $value) return false;
                    break;
                case '<':
                    if ($contextValue >= $value) return false;
                    break;
                case '<=':
                    if ($contextValue > $value) return false;
                    break;
                case 'contains':
                    if (stripos($contextValue, $value) === false) return false;
                    break;
                case 'not_contains':
                    if (stripos($contextValue, $value) !== false) return false;
                    break;
            }
        }
        
        return true;
    }
}