<?php
/**
 * Application Routes
 */

use App\Helpers\Language;

// Middleware to set language from user preferences
Flight::before('start', function(&$params, &$output){
    // Check if user is logged in and has language preference
    if (isset($_SESSION['user']) && isset($_SESSION['user']['language'])) {
        Language::setLanguage($_SESSION['user']['language']);
        $_SESSION['user_language'] = $_SESSION['user']['language'];
    } elseif (isset($_SESSION['user_language'])) {
        Language::setLanguage($_SESSION['user_language']);
    }
    
    // Check for language switch in URL
    if (isset($_GET['lang']) && in_array($_GET['lang'], ['fr', 'en', 'de'])) {
        Language::setLanguage($_GET['lang']);
        $_SESSION['user_language'] = $_GET['lang'];
        
        // Update user preference if logged in
        if (isset($_SESSION['user']['id'])) {
            try {
                $db = Flight::db();
                $stmt = $db->prepare("UPDATE users SET language = ? WHERE id = ?");
                $stmt->execute([$_GET['lang'], $_SESSION['user']['id']]);
                $_SESSION['user']['language'] = $_GET['lang'];
            } catch (Exception $e) {
                // Ignore errors
            }
        }
    }
});

// Home route
Flight::route('/', function(){
    // Create a basic controller to use template method
    $controller = new class extends \App\Core\Controller {
        public function dashboard() {
            // Get stats for dashboard
            $stats = [];
            try {
                $db = Flight::db();
                
                // Get total clients
                $stmt = $db->query("SELECT COUNT(*) as total, SUM(is_active) as active FROM clients");
                $clientStats = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_clients'] = $clientStats['total'] ?? 0;
                $stats['active_clients'] = $clientStats['active'] ?? 0;
                
                // Get total patients
                $stmt = $db->query("SELECT COUNT(*) as total FROM patients");
                $patientStats = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_patients'] = $patientStats['total'] ?? 0;
                
                // Get invoice stats with detailed status
                $stmt = $db->query("
                    SELECT 
                        COUNT(*) as total,
                        SUM(CASE WHEN status = 'unpaid' THEN 1 ELSE 0 END) as unpaid,
                        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                        SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid,
                        SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                        SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END) as overdue
                    FROM invoices
                ");
                $invoiceStats = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_invoices'] = $invoiceStats['total'] ?? 0;
                $stats['unpaid_invoices'] = $invoiceStats['unpaid'] ?? 0;
                $stats['pending_invoices'] = $invoiceStats['pending'] ?? 0;
                $stats['paid_invoices'] = $invoiceStats['paid'] ?? 0;
                $stats['sent_invoices'] = $invoiceStats['sent'] ?? 0;
                $stats['overdue_invoices'] = $invoiceStats['overdue'] ?? 0;
                
                // Get revenue stats
                $stmt = $db->query("SELECT SUM(total_amount) as revenue, SUM(CASE WHEN status = 'unpaid' THEN total_amount ELSE 0 END) as outstanding FROM invoices");
                $revenueStats = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['total_revenue'] = $revenueStats['revenue'] ?? 0;
                $stats['outstanding_amount'] = $revenueStats['outstanding'] ?? 0;
                
                // Get monthly revenue (current month)
                $stmt = $db->query("
                    SELECT SUM(total_amount) as monthly_revenue 
                    FROM invoices 
                    WHERE MONTH(invoice_date) = MONTH(CURRENT_DATE()) 
                    AND YEAR(invoice_date) = YEAR(CURRENT_DATE())
                    AND status = 'paid'
                ");
                $monthlyStats = $stmt->fetch(PDO::FETCH_ASSOC);
                $stats['monthly_revenue'] = $monthlyStats['monthly_revenue'] ?? 0;
            } catch (Exception $e) {
                // Default stats if tables don't exist
                $stats = [
                    'total_clients' => 0,
                    'active_clients' => 0,
                    'total_invoices' => 0,
                    'unpaid_invoices' => 0,
                    'total_revenue' => 0,
                    'outstanding_amount' => 0,
                    'monthly_revenue' => 0
                ];
            }
            
            // Use fresh dashboard to avoid template issues
            $viewName = 'dashboard-fresh';
            
            // Get currency from configuration
            $currency = '$'; // Default currency
            try {
                $stmt = $db->query("SELECT value FROM config_settings WHERE `key` = 'currency' LIMIT 1");
                $currencySetting = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($currencySetting) {
                    $currency = $currencySetting['value'];
                }
            } catch (Exception $e) {
                // Use default currency if config not found
            }
            
            return $this->render($viewName, [
                'title' => 'Dashboard',
                'stats' => $stats,
                'recent_invoices' => [],
                'recent_activities' => [],
                'currency' => $currency,
                'config' => [
                    'currency_symbol' => $currency,
                    'currency' => $currency,
                    'date_format' => 'd/m/Y',
                    'time_format' => 'H:i'
                ]
            ]);
        }
    };
    
    $controller->dashboard();
});

// Health check
Flight::route('/health', function(){
    try {
        // Test database connection
        $db = Flight::db();
        $db->query('SELECT 1');
        
        Flight::json([
            'status' => 'ok',
            'time' => date('Y-m-d H:i:s'),
            'database' => 'connected'
        ]);
    } catch (Exception $e) {
        Flight::json([
            'status' => 'error',
            'message' => 'Database connection failed'
        ], 500);
    }
});

// API Routes Group
Flight::group('/api', function(){
    // API middleware would go here
    
    // VAT rates endpoint
    Flight::route('GET /vat-rates', [new App\Controllers\ApiController(), 'getVatRates']);
    
    // Search billable entities
    Flight::route('GET /search-billable', [new App\Controllers\ApiController(), 'searchBillable']);
    
    // Search catalog items
    Flight::route('GET /catalog/search', [new App\Controllers\ApiController(), 'searchCatalog']);
});

// Include module routes
$modules = ['auth', 'users', 'invoices', 'payments', 'config', 'patients', 'clients', 'products', 'pos'];
foreach ($modules as $module) {
    $routeFile = __DIR__ . "/../modules/{$module}/routes.php";
    if (file_exists($routeFile)) {
        require $routeFile;
    }
}