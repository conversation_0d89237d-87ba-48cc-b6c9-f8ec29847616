<?php
/**
 * Application Routes
 */

use App\Helpers\Language;
use App\Middleware\PermissionMiddleware;

// Middleware to set language from user preferences
Flight::before('start', function(&$params, &$output){
    // Default language if none set
    $defaultLanguage = 'fr';
    
    // Check if user is logged in and has language preference
    if (isset($_SESSION['user']) && isset($_SESSION['user']['language'])) {
        Language::setLanguage($_SESSION['user']['language']);
        $_SESSION['user_language'] = $_SESSION['user']['language'];
    } elseif (isset($_SESSION['user_language'])) {
        Language::setLanguage($_SESSION['user_language']);
    } else {
        // Set default language if no preference exists
        Language::setLanguage($defaultLanguage);
        $_SESSION['user_language'] = $defaultLanguage;
    }
    
    // Check for language switch in URL
    if (isset($_GET['lang']) && in_array($_GET['lang'], ['fr', 'en', 'de'])) {
        Language::setLanguage($_GET['lang']);
        $_SESSION['user_language'] = $_GET['lang'];
        
        // Update user preference if logged in
        if (isset($_SESSION['user']['id'])) {
            try {
                $db = Flight::db();
                $stmt = $db->prepare("UPDATE users SET language = ? WHERE id = ?");
                $stmt->execute([$_GET['lang'], $_SESSION['user']['id']]);
                $_SESSION['user']['language'] = $_GET['lang'];
            } catch (Exception $e) {
                // Ignore errors
            }
        }
    }
});

// Home route with enhanced error handling
Flight::route('/', function() {
    try {
        $controller = new \App\Controllers\DashboardController();
        
        // Start output buffering to capture the echo from render()
        ob_start();
        
        // Call index which will echo the output
        $controller->index();
        
        // Get the output
        $output = ob_get_clean();
        
        // Send it through Flight's response system
        echo $output;
        
    } catch (Exception $e) {
        // Clean any output buffers
        while (ob_get_level() > 0) {
            ob_end_clean();
        }
        
        // Log the error
        error_log('Dashboard Route Error: ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        
        // Re-throw to let Flight's error handler display it
        throw $e;
    } catch (Error $e) {
        // Catch PHP 7+ Error types (like TypeError)
        while (ob_get_level() > 0) {
            ob_end_clean();
        }
        
        error_log('Dashboard Route Error (Error): ' . $e->getMessage() . ' in ' . $e->getFile() . ':' . $e->getLine());
        
        // Convert to Exception for Flight's error handler
        throw new Exception($e->getMessage(), $e->getCode(), $e);
    }
});

// Health check
Flight::route('/health', function(){
    try {
        // Test database connection
        $db = Flight::db();
        $db->query('SELECT 1');
        
        Flight::json([
            'status' => 'ok',
            'time' => date('Y-m-d H:i:s'),
            'database' => 'connected'
        ]);
    } catch (Exception $e) {
        Flight::json([
            'status' => 'error',
            'message' => 'Database connection failed'
        ], 500);
    }
});

// API Routes Group
Flight::group('/api', function(){
    // API middleware would go here
    
    // VAT rates endpoint
    Flight::route('GET /vat-rates', [new App\Controllers\ApiController(), 'getVatRates']);
    
    // Search billable entities
    Flight::route('GET /search-billable', [new App\Controllers\ApiController(), 'searchBillable']);
    
    // Search catalog items
    Flight::route('GET /catalog/search', [new App\Controllers\ApiController(), 'searchCatalog']);
    
    // Invoice templates
    Flight::route('GET /invoice-templates', [new App\Controllers\ApiController(), 'getInvoiceTemplates']);
    Flight::route('GET /invoice-templates/@id:[0-9]+/details', [new App\Controllers\ApiController(), 'getTemplateDetails']);
    
    // Dashboard API endpoints
    Flight::route('GET /dashboard/stats', [new \App\Controllers\DashboardController(), 'getStats']);
    Flight::route('GET /dashboard/revenue-chart', [new \App\Controllers\DashboardController(), 'getRevenueChart']);
    Flight::route('GET /dashboard/invoice-status-chart', [new \App\Controllers\DashboardController(), 'getInvoiceStatusChart']);
    Flight::route('GET /dashboard/recent-activities', [new \App\Controllers\DashboardController(), 'getRecentActivities']);
    Flight::route('GET /dashboard/recent-invoices', [new \App\Controllers\DashboardController(), 'getRecentInvoices']);
});

// Include module routes with permission configurations
$modules = ['auth', 'users', 'invoices', 'payments', 'config', 'patients', 'clients', 'products', 'pos', 'packages', 'sales', 'admin'];

// Module permission mappings - defines which routes require permissions
// Routes not listed here will be accessible to all authenticated users
$modulePermissions = [
    'users' => [
        // User management
        'GET /users' => 'users.view',
        'GET /users/create' => 'users.create',
        'POST /users' => 'users.create',
        'GET /users/@id/edit' => 'users.update',
        'POST /users/@id' => 'users.update',
        'DELETE /users/@id' => 'users.delete',
        'POST /users/@id/toggle-active' => 'users.update',
        'POST /users/@id/reset-password' => 'users.reset_password',
        
        // User Groups
        'GET /users/groups' => 'user_groups.view',
        'GET /users/groups/create' => 'user_groups.create',
        'POST /users/groups' => 'user_groups.create',
        'GET /users/groups/@id/edit' => 'user_groups.update',
        'POST /users/groups/@id' => 'user_groups.update',
        'DELETE /users/groups/@id' => 'user_groups.delete',
        'POST /users/groups/@id/members' => 'user_groups.manage_members',
        'DELETE /users/groups/@groupId/members/@userId' => 'user_groups.manage_members',
    ],
    
    'config' => [
        // All config routes require admin permissions
        'GET /config' => 'config.view',
        'GET /config/*' => 'config.manage',
        'POST /config/*' => 'config.manage',
        'PUT /config/*' => 'config.manage',
        'DELETE /config/*' => 'config.manage',
        
        // Translation management
        'GET /translations' => 'translations.view',
        'POST /translations/*' => 'translations.manage',
    ],
    
    'invoices' => [
        'GET /invoices' => 'invoices.view',
        'GET /invoices/create' => 'invoices.create',
        'POST /invoices' => 'invoices.create',
        'GET /invoices/@id/edit' => 'invoices.update',
        'POST /invoices/@id' => 'invoices.update',
        'DELETE /invoices/@id' => 'invoices.delete',
        'POST /invoices/@id/send' => 'invoices.send',
        'POST /invoices/@id/void' => 'invoices.void',
    ],
    
    'payments' => [
        'GET /payments' => 'payments.view',
        'POST /payments' => 'payments.create',
        'DELETE /payments/@id' => 'payments.delete',
    ],
    
    'patients' => [
        'GET /patients' => 'patients.view',
        'GET /patients/create' => 'patients.create',
        'POST /patients' => 'patients.create',
        'GET /patients/@id/edit' => 'patients.update',
        'POST /patients/@id' => 'patients.update',
        'DELETE /patients/@id' => 'patients.delete',
    ],
    
    'clients' => [
        'GET /clients' => 'clients.view',
        'GET /clients/create' => 'clients.create',
        'POST /clients' => 'clients.create',
        'GET /clients/@id/edit' => 'clients.update',
        'POST /clients/@id' => 'clients.update',
        'DELETE /clients/@id' => 'clients.delete',
    ],
    
    'products' => [
        'GET /products' => 'products.view',
        'GET /products/create' => 'products.create',
        'POST /products' => 'products.create',
        'GET /products/@id/edit' => 'products.update',
        'POST /products/@id' => 'products.update',
        'DELETE /products/@id' => 'products.delete',
    ],
    
    'admin' => [
        // Permission management
        'GET /admin/permissions' => 'permissions.view',
        'GET /admin/permissions/*' => 'permissions.manage',
        'POST /admin/permissions/*' => 'permissions.manage',
        
        // Admin dashboard and settings
        'GET /admin' => 'config.view',
        'GET /admin/menu-config' => 'config.manage',
        'POST /admin/menu-config/*' => 'config.manage',
    ],
];

// Apply permission middleware before loading module routes
Flight::before('start', function(&$params, &$output) use ($modulePermissions) {
    $requestUrl = parse_url(Flight::request()->url, PHP_URL_PATH);
    $basePath = Flight::get('flight.base_url');
    
    if (strpos($requestUrl, $basePath) === 0) {
        $currentPath = substr($requestUrl, strlen($basePath));
    } else {
        $currentPath = $requestUrl;
    }
    
    // Skip permission check for public routes and API endpoints (handled separately)
    $publicRoutes = ['/login', '/logout', '/health', '/api/*'];
    foreach ($publicRoutes as $publicRoute) {
        if ($publicRoute === $currentPath || 
            (strpos($publicRoute, '*') !== false && strpos($currentPath, rtrim($publicRoute, '*')) === 0)) {
            return true;
        }
    }
    
    // Skip if user not authenticated
    if (!isset($_SESSION['user_id'])) {
        return true;
    }
    
    // Check permissions for current route
    $method = Flight::request()->method;
    $routePattern = $method . ' ' . $currentPath;
    
    foreach ($modulePermissions as $module => $permissions) {
        foreach ($permissions as $pattern => $permission) {
            // Check if current route matches pattern
            if (matchRoute($pattern, $routePattern)) {
                // Check permission
                $permissionService = \App\Services\PermissionService::getInstance();
                if (!$permissionService->hasPermission($permission)) {
                    $_SESSION['flash']['error'] = "You don't have permission to access this page.";
                    Flight::redirect('/dashboard');
                    exit;
                }
                break 2;
            }
        }
    }
    
    return true;
});

// Helper function to match routes with patterns
if (!function_exists('matchRoute')) {
    function matchRoute($pattern, $route) {
        // Handle wildcards
        if (strpos($pattern, '*') !== false) {
            $pattern = str_replace('*', '.*', $pattern);
            return preg_match('#^' . $pattern . '$#', $route);
        }
        
        // Handle parameters like @id
        $pattern = preg_replace('/@\w+/', '[^/]+', $pattern);
        return preg_match('#^' . $pattern . '$#', $route);
    }
}

// Include module routes
foreach ($modules as $module) {
    $routeFile = __DIR__ . "/../modules/{$module}/routes.php";
    if (file_exists($routeFile)) {
        require $routeFile;
    }
}