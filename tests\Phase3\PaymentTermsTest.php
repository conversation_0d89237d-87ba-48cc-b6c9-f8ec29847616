<?php

namespace Tests\Phase3;

use PHPUnit\Framework\TestCase;
use App\Models\ConfigPaymentTerm;
use Flight;

class PaymentTermsTest extends TestCase
{
    protected static $db;
    protected $testPaymentTerms = [];
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        self::$db->exec("DELETE FROM config_payment_terms WHERE code LIKE 'TEST-%'");
    }
    
    public function testCreatePaymentTerm()
    {
        $paymentTerm = ConfigPaymentTerm::create([
            'code' => 'TEST-NET30',
            'name' => json_encode(['en' => 'Net 30 Days', 'fr' => 'Net 30 Jours']),
            'days' => 30,
            'is_default' => 0,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->assertNotNull($paymentTerm);
        $this->assertNotNull($paymentTerm->id);
        $this->assertEquals('TEST-NET30', $paymentTerm->code);
        $this->assertEquals(30, $paymentTerm->days);
        
        $this->testPaymentTerms[] = $paymentTerm->id;
        
        return $paymentTerm;
    }
    
    public function testCalculateDueDate()
    {
        // Create test payment term
        $paymentTerm = ConfigPaymentTerm::create([
            'code' => 'TEST-CALC-DUE',
            'name' => json_encode(['en' => 'Net 30 Days', 'fr' => 'Net 30 Jours']),
            'days' => 30,
            'is_default' => 0,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testPaymentTerms[] = $paymentTerm->id;
        
        $issueDate = '2024-01-15';
        $expectedDueDate = '2024-02-14'; // 30 days later
        
        $calculatedDueDate = $paymentTerm->calculateDueDate($issueDate);
        $this->assertEquals($expectedDueDate, $calculatedDueDate);
        
        // Test with different date
        $issueDate2 = '2024-02-01';
        $expectedDueDate2 = '2024-03-02'; // 30 days later (leap year)
        
        $calculatedDueDate2 = $paymentTerm->calculateDueDate($issueDate2);
        $this->assertEquals($expectedDueDate2, $calculatedDueDate2);
    }
    
    public function testImmediatePayment()
    {
        $immediatePayment = ConfigPaymentTerm::create([
            'code' => 'TEST-IMMEDIATE',
            'name' => json_encode(['en' => 'Due on Receipt', 'fr' => 'Dû à réception']),
            'days' => 0,
            'is_default' => 0,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testPaymentTerms[] = $immediatePayment->id;
        
        $issueDate = '2024-01-15';
        $dueDate = $immediatePayment->calculateDueDate($issueDate);
        
        // Due date should be same as issue date for immediate payment
        $this->assertEquals($issueDate, $dueDate);
    }
    
    public function testDefaultPaymentTerm()
    {
        // First, unset any existing default payment terms
        self::$db->exec("UPDATE config_payment_terms SET is_default = 0 WHERE is_default = 1");
        
        $defaultTerm = ConfigPaymentTerm::create([
            'code' => 'TEST-DEFAULT',
            'name' => json_encode(['en' => 'Default Terms', 'fr' => 'Conditions par défaut']),
            'days' => 15,
            'is_default' => 1,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testPaymentTerms[] = $defaultTerm->id;
        
        // Test getting default payment term
        $foundDefault = ConfigPaymentTerm::where('is_default', '=', 1)
                                        ->where('is_active', '=', 1)
                                        ->first();
        
        $this->assertNotNull($foundDefault);
        $this->assertEquals($defaultTerm->id, $foundDefault->id);
        
        // Test that only one default exists
        $defaultCount = ConfigPaymentTerm::where('is_default', '=', 1)->count();
        $this->assertEquals(1, $defaultCount);
    }
    
    public function testMultiplePaymentTerms()
    {
        $terms = [
            ['code' => 'TEST-NET7', 'name' => 'Net 7 Days', 'days' => 7],
            ['code' => 'TEST-NET14', 'name' => 'Net 14 Days', 'days' => 14],
            ['code' => 'TEST-NET45', 'name' => 'Net 45 Days', 'days' => 45],
            ['code' => 'TEST-NET60', 'name' => 'Net 60 Days', 'days' => 60],
            ['code' => 'TEST-NET90', 'name' => 'Net 90 Days', 'days' => 90]
        ];
        
        foreach ($terms as $termData) {
            $paymentTerm = ConfigPaymentTerm::create([
                'code' => $termData['code'],
                'name' => json_encode(['en' => $termData['name']]),
                'days' => $termData['days'],
                'is_active' => 1,
                'created_by' => 1
            ]);
            
            $this->testPaymentTerms[] = $paymentTerm->id;
        }
        
        // Test retrieving all active terms sorted by days
        $activeTerms = ConfigPaymentTerm::where('is_active', '=', 1)
                                       ->where('code', 'LIKE', 'TEST-%')
                                       ->orderBy('days', 'ASC')
                                       ->get();
        
        $this->assertGreaterThanOrEqual(5, count($activeTerms));
        
        // Verify terms are sorted correctly
        $previousDays = -1;
        foreach ($activeTerms as $term) {
            $this->assertGreaterThanOrEqual($previousDays, $term->days);
            $previousDays = $term->days;
        }
    }
    
    public function testPaymentTermWithDescription()
    {
        $termWithDesc = ConfigPaymentTerm::create([
            'code' => 'TEST-2-10-NET30',
            'name' => json_encode(['en' => '2/10 Net 30', 'fr' => '2/10 Net 30']),
            'description' => json_encode([
                'en' => '2% discount if paid within 10 days, otherwise net 30 days',
                'fr' => '2% de réduction si payé dans les 10 jours, sinon net 30 jours'
            ]),
            'days' => 30,
            'discount_days' => 10,
            'discount_percentage' => 2.00,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testPaymentTerms[] = $termWithDesc->id;
        
        // Test localized description
        $englishDesc = $termWithDesc->getLocalizedDescription('en');
        $this->assertStringContainsString('2% discount', $englishDesc);
        
        $frenchDesc = $termWithDesc->getLocalizedDescription('fr');
        $this->assertStringContainsString('2% de réduction', $frenchDesc);
        
        // Test discount calculation
        $invoiceAmount = 1000.00;
        $discountAmount = $invoiceAmount * ($termWithDesc->discount_percentage / 100);
        $this->assertEquals(20.00, $discountAmount);
    }
    
    public function testPaymentTermStatus()
    {
        $paymentTerm = ConfigPaymentTerm::create([
            'code' => 'TEST-STATUS',
            'name' => json_encode(['en' => 'Status Test']),
            'days' => 21,
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testPaymentTerms[] = $paymentTerm->id;
        
        // Test active status
        $this->assertTrue($paymentTerm->is_active);
        
        // Deactivate
        $paymentTerm->is_active = 0;
        $paymentTerm->save();
        
        // Verify deactivated
        $deactivated = ConfigPaymentTerm::find($paymentTerm->id);
        $this->assertFalse($deactivated->is_active);
    }
    
    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        self::$db->exec("DELETE FROM config_payment_terms WHERE code LIKE 'TEST-%'");
    }
}