<?php

return [
    // Module Names
    'invoices' => 'Factures',
    'retrocession' => 'Rétrocession',
    'recurring_invoices' => 'Factures Récurrentes',
    'clients' => 'Clients',
    'products' => 'Produits',
    'reports' => 'Rapports',
    'configuration' => 'Configuration',
    'users' => 'Utilisateurs',
    'stock' => 'Stock',
    'documents' => 'Documents',
    'payments' => 'Paiements',
    'categories' => 'Catégories',
    'permissions' => 'Permissions',
    'patients' => 'Membres',
    'courses' => 'Cours',
    'pos' => 'Point de Vente',
    'packages' => 'Forfaits',
    'sales' => 'Ventes',
    'admin' => 'Administration',
    'auth' => 'Authentification',
    
    // Module Descriptions
    'invoices_desc' => 'Gérer les factures clients et la facturation des abonnements, entraînements personnels et services',
    'retrocession_desc' => 'Gérer les paiements de commissions et le partage des revenus avec les entraîneurs et partenaires',
    'recurring_invoices_desc' => 'Configurer et gérer la facturation automatique des abonnements mensuels et souscriptions',
    'clients_desc' => 'Maintenir les profils complets des membres, informations de contact et historique fitness',
    'products_desc' => 'Gérer les services fitness, séances d\'entraînement, suppléments et inventaire d\'équipement',
    'reports_desc' => 'Générer des analyses d\'affaires complètes, rapports de revenus et statistiques des membres',
    'configuration_desc' => 'Configurer les paramètres système, règles d\'affaires et paramètres du centre de fitness',
    'users_desc' => 'Gérer les comptes du personnel, entraîneurs et permissions d\'accès au système',
    'stock_desc' => 'Suivre les niveaux d\'inventaire des suppléments, marchandises et équipements',
    'documents_desc' => 'Stocker et gérer les contrats, décharges, formulaires médicaux et documents des membres',
    'payments_desc' => 'Traiter les paiements des membres, suivre les transactions et gérer les méthodes de paiement',
    'categories_desc' => 'Organiser les services, produits et abonnements en groupes logiques',
    'permissions_desc' => 'Contrôler les droits d\'accès et définir les rôles utilisateur pour le personnel et les entraîneurs',
    'patients_desc' => 'Gérer les profils santé des membres, objectifs fitness et suivi de progression',
    'courses_desc' => 'Planifier et gérer les cours collectifs, ateliers et programmes d\'entraînement',
    'pos_desc' => 'Interface de vente rapide pour suppléments, marchandises et services sans rendez-vous',
    'packages_desc' => 'Créer et gérer les forfaits d\'abonnement, packages d\'entraînement et offres promotionnelles',
    'sales_desc' => 'Suivre toutes les transactions de vente, flux de revenus et performance commerciale',
    'admin_desc' => 'Outils d\'administration système pour la gestion de base de données et configurations avancées',
    'auth_desc' => 'Système de connexion sécurisé et gestion de l\'authentification pour tous les utilisateurs',
];