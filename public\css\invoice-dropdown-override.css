/**
 * Invoice Dropdown Override
 * Forces dropdown menus to display all items
 */

/* Remove all height restrictions */
.dropdown-menu,
.dropdown-menu.show,
.table .dropdown-menu,
.table .dropdown-menu.show {
    max-height: none !important;
    height: auto !important;
    overflow: visible !important;
}

/* Ensure all dropdown items are visible */
.dropdown-menu li,
.dropdown-menu .dropdown-item,
.dropdown-menu .dropdown-divider {
    display: block !important;
    visibility: visible !important;
    height: auto !important;
    max-height: none !important;
    overflow: visible !important;
}

/* Override any container restrictions */
.table-responsive,
.card,
.card-body {
    overflow: visible !important;
}

/* Force dropdown to use static positioning to avoid clipping */
.table td:last-child .dropdown {
    position: static !important;
}

.table td:last-child .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
    margin-top: 0.25rem !important;
    z-index: 9999 !important;
}

/* Ensure dropdown toggle button doesn't restrict height */
.dropdown-toggle {
    overflow: visible !important;
}

/* Bootstrap 5 specific fixes */
.dropdown-menu[data-bs-popper] {
    max-height: none !important;
    overflow: visible !important;
}

/* Fix for hidden items */
.dropdown-menu > li {
    display: list-item !important;
}

.dropdown-menu a.dropdown-item {
    display: block !important;
    width: 100% !important;
    padding: 0.25rem 1rem !important;
    clear: both !important;
    font-weight: 400 !important;
    text-align: inherit !important;
    white-space: nowrap !important;
    background-color: transparent !important;
    border: 0 !important;
}

/* Ensure dividers are visible */
.dropdown-divider {
    height: 0 !important;
    margin: 0.5rem 0 !important;
    overflow: hidden !important;
    border-top: 1px solid rgba(0,0,0,.15) !important;
    display: block !important;
}