<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Duplicate Patient Lines Fix</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .code {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #e9ecef;
            white-space: pre-wrap;
        }
        .button {
            background-color: #007bff;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .button.danger {
            background-color: #dc3545;
        }
        .button.danger:hover {
            background-color: #c82333;
        }
        .button.success {
            background-color: #28a745;
        }
        .button.success:hover {
            background-color: #1e7e34;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin: 8px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .checklist li::before {
            content: "□ ";
            font-weight: bold;
            color: #007bff;
        }
        .checklist li.completed::before {
            content: "✅ ";
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Test Duplicate Patient Lines Fix</h1>
        
        <div class="success">
            <strong>✅ Fix Applied:</strong> The duplicate patient lines issue has been resolved with the following improvements:
            <ul>
                <li>Checkbox change handler now properly removes ALL existing patient lines before adding new ones</li>
                <li>Added cleanup function to remove duplicates on page load</li>
                <li>Implemented proper form field re-indexing to ensure sequential numbering</li>
                <li>Enhanced console logging for better debugging</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🚨 Issue Identified</h3>
            <div class="error">
                <strong>Problem:</strong> The HTML you provided shows 4 duplicate patient lines, all with the same <code>items[1]</code> field naming, which would cause form submission issues.
            </div>
            
            <div class="warning">
                <strong>Root Cause:</strong> The checkbox change handler was not properly checking for existing patient lines and was creating duplicates instead of managing a single line.
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 Fixes Applied</h3>
            
            <div class="info">
                <strong>1. Enhanced Checkbox Change Handler:</strong>
                <div class="code">// New logic for removing patient lines
if (this.checked) {
    // Remove using data-line-type attribute (more reliable)
    const patientLines = itemsBody.querySelectorAll('[data-line-type="patient"]');
    patientLines.forEach(line => line.remove());
    
    // Re-index all lines after removal
    reindexInvoiceLines();
}</div>
            </div>
            
            <div class="info">
                <strong>2. Added Cleanup Function:</strong>
                <div class="code">function cleanupDuplicatePatientLines() {
    const patientLines = itemsBody.querySelectorAll('[data-line-type="patient"]');
    
    if (patientLines.length > 1) {
        // Keep only the first patient line, remove the rest
        for (let i = 1; i < patientLines.length; i++) {
            patientLines[i].remove();
        }
        
        // Re-index all remaining lines
        reindexInvoiceLines();
    }
}</div>
            </div>
            
            <div class="info">
                <strong>3. Form Field Re-indexing:</strong>
                <div class="code">function reindexInvoiceLines() {
    const allLines = itemsBody.querySelectorAll('.invoice-item');
    allLines.forEach((line, index) => {
        const inputs = line.querySelectorAll('input, select');
        inputs.forEach(input => {
            if (input.name && input.name.includes('items[')) {
                input.name = input.name.replace(/items\[\d+\]/, `items[${index}]`);
            }
        });
    });
}</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Testing Instructions</h3>
            
            <div class="warning">
                <strong>Current State:</strong> The page you showed has duplicate patient lines. The fix will clean these up automatically.
            </div>
            
            <ul class="checklist">
                <li>Open the retrocession invoice creation page</li>
                <li>Check browser console for cleanup messages: <code>"Found X patient lines"</code></li>
                <li>Verify only 1 patient line remains after cleanup</li>
                <li>Check form field names are sequential: <code>items[0], items[1], items[2]</code></li>
                <li>Toggle exclude patient line checkbox ON → Patient line should disappear</li>
                <li>Check form field names are now: <code>items[0], items[1]</code> (CNS, Secretary)</li>
                <li>Toggle exclude patient line checkbox OFF → Patient line should reappear</li>
                <li>Check form field names are back to: <code>items[0], items[1], items[2]</code></li>
                <li>Verify no duplicate patient lines are created during toggle</li>
                <li>Test form submission works correctly</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>📊 Browser Console Logs to Watch</h3>
            <div class="info">
                <strong>Expected Console Messages:</strong>
                <div class="code">✅ On page load:
"Found 4 patient lines"                    // Initially detects duplicates
"Cleaning up duplicate patient lines..."   // Cleanup starts
"Removing duplicate patient line 1"        // Each duplicate removed
"Removing duplicate patient line 2"
"Removing duplicate patient line 3"
"Re-indexed 3 invoice lines"              // Final re-indexing
"Cleanup complete"

✅ When checking exclude checkbox:
"Removing patient line (exclude checked)"  // Patient line removed
"Re-indexed 2 invoice lines"              // Only CNS + Secretary left

✅ When unchecking exclude checkbox:
"Removing existing patient line"           // Any existing removed first
"Added patient line back to retrocession invoice"
"Re-indexed 3 invoice lines"              // All 3 lines present again</div>
            </div>
        </div>

        <div class="test-section">
            <h3>🎯 Expected Results</h3>
            
            <div class="success">
                <strong>After Fix:</strong>
                <ul>
                    <li>✅ Only 1 patient line exists at any time</li>
                    <li>✅ Form fields are properly indexed (0, 1, 2)</li>
                    <li>✅ Checkbox toggle works smoothly without duplicates</li>
                    <li>✅ Form submission will work correctly</li>
                    <li>✅ Console shows proper cleanup and re-indexing messages</li>
                </ul>
            </div>
            
            <div class="error">
                <strong>Before Fix (Your HTML):</strong>
                <ul>
                    <li>❌ 4 duplicate patient lines visible</li>
                    <li>❌ All using same field name <code>items[1]</code></li>
                    <li>❌ Form submission would fail or send duplicate data</li>
                    <li>❌ Checkbox toggle would create more duplicates</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🚀 Test the Fix</h3>
            <div class="info">
                <strong>Quick Test Link:</strong><br>
                <a href="http://localhost/fit/public/invoices/create?type=retrocession_25" class="button success" target="_blank">
                    Open Retrocession Invoice (Fixed)
                </a>
            </div>
            
            <div class="info">
                <strong>Additional Testing:</strong><br>
                <a href="http://localhost/fit/public/users/18/edit" class="button" target="_blank">Edit Rémi Heine Settings</a>
                <a href="http://localhost/fit/public/invoices/create?type=retrocession_30" class="button" target="_blank">Test Retrocession 30%</a>
            </div>
        </div>

        <div class="success">
            <strong>✅ Fix Complete!</strong><br>
            The duplicate patient lines issue has been resolved. The page will now properly manage patient lines without creating duplicates, and form submission will work correctly.
        </div>
    </div>
</body>
</html>