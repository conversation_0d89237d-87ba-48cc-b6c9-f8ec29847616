{% extends 'base-modern.twig' %}

{% block title %}{{ __('permissions.management') }} - {{ parent() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-shield-alt mr-2"></i>
                        {{ __('permissions.management') }}
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-tool" data-toggle="modal" data-target="#templateModal">
                            <i class="fas fa-file-import"></i> {{ __('permissions.load_template') }}
                        </button>
                        <button type="button" class="btn btn-tool" onclick="window.permissionsManager && window.permissionsManager.showAuditLog()">
                            <i class="fas fa-history"></i> {{ __('permissions.audit_log') }}
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- User Groups Panel -->
                        <div class="col-md-3">
                            <div class="card card-outline card-primary">
                                <div class="card-header">
                                    <h5 class="mb-0">{{ __('permissions.user_groups') }}</h5>
                                </div>
                                <div class="card-body p-0">
                                    <div class="list-group list-group-flush" id="groupList">
                                        {% for group in groups %}
                                        <a href="#" class="list-group-item list-group-item-action {% if loop.first %}active{% endif %}" 
                                           data-group-id="{{ group.id }}" 
                                           onclick="window.loadGroupPermissions({{ group.id }}); return false;">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>
                                                    <i class="fas fa-users mr-2"></i>
                                                    {{ group.name }}
                                                </span>
                                                <span class="badge badge-primary">{{ group.user_count|default(0) }}</span>
                                            </div>
                                            {% if group.description %}
                                            <small class="text-muted">{{ group.description }}</small>
                                            {% endif %}
                                        </a>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button class="btn btn-sm btn-primary btn-block" onclick="window.createNewGroup()">
                                        <i class="fas fa-plus"></i> {{ __('permissions.add_group') }}
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Permissions Matrix -->
                        <div class="col-md-9">
                            <form id="permissionsForm" method="POST" action="{{ url('/permissions/update') }}">
                                {{ csrf_field() }}
                                <input type="hidden" name="group_id" id="currentGroupId" value="{{ groups[0].id ?? '' }}">
                                
                                <div class="card card-outline card-success">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            {{ __('permissions.permissions_for') }} 
                                            <span id="currentGroupName" class="font-weight-bold">{{ groups[0].name ?? '' }}</span>
                                        </h5>
                                        <div class="card-tools">
                                            <div class="input-group input-group-sm" style="width: 250px;">
                                                <input type="text" class="form-control" placeholder="{{ __('permissions.search_modules') }}" 
                                                       id="moduleSearch" onkeyup="window.filterModules()">
                                                <div class="input-group-append">
                                                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover table-striped" id="permissionsTable">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 30%">{{ __('permissions.module') }}</th>
                                                        <th class="text-center" style="width: 14%">
                                                            <label class="mb-0">
                                                                <input type="checkbox" class="permission-toggle" data-permission="view">
                                                                {{ __('permissions.view') }}
                                                            </label>
                                                        </th>
                                                        <th class="text-center" style="width: 14%">
                                                            <label class="mb-0">
                                                                <input type="checkbox" class="permission-toggle" data-permission="create">
                                                                {{ __('permissions.create') }}
                                                            </label>
                                                        </th>
                                                        <th class="text-center" style="width: 14%">
                                                            <label class="mb-0">
                                                                <input type="checkbox" class="permission-toggle" data-permission="edit">
                                                                {{ __('permissions.edit') }}
                                                            </label>
                                                        </th>
                                                        <th class="text-center" style="width: 14%">
                                                            <label class="mb-0">
                                                                <input type="checkbox" class="permission-toggle" data-permission="delete">
                                                                {{ __('permissions.delete') }}
                                                            </label>
                                                        </th>
                                                        <th class="text-center" style="width: 14%">{{ __('permissions.special') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="permissionsBody">
                                                    <!-- Permissions will be loaded dynamically -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <div class="row">
                                            <div class="col-6">
                                                <button type="button" class="btn btn-default" onclick="window.permissionsManager && window.permissionsManager.copyFromGroup()">
                                                    <i class="fas fa-copy"></i> {{ __('permissions.copy_from') }}
                                                </button>
                                                <button type="button" class="btn btn-warning" onclick="window.resetToDefault()">
                                                    <i class="fas fa-undo"></i> {{ __('permissions.reset_default') }}
                                                </button>
                                            </div>
                                            <div class="col-6 text-right">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="fas fa-save"></i> {{ __('permissions.save_changes') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Template Modal -->
<div class="modal fade" id="templateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('permissions.load_template') }}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="list-group">
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('admin')">
                        <h6 class="mb-1">{{ __('permissions.template_admin') }}</h6>
                        <p class="mb-1">{{ __('permissions.template_admin_desc') }}</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('manager')">
                        <h6 class="mb-1">{{ __('permissions.template_manager') }}</h6>
                        <p class="mb-1">{{ __('permissions.template_manager_desc') }}</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('staff')">
                        <h6 class="mb-1">{{ __('permissions.template_staff') }}</h6>
                        <p class="mb-1">{{ __('permissions.template_staff_desc') }}</p>
                    </a>
                    <a href="#" class="list-group-item list-group-item-action" onclick="window.permissionsManager && window.permissionsManager.loadTemplate('readonly')">
                        <h6 class="mb-1">{{ __('permissions.template_readonly') }}</h6>
                        <p class="mb-1">{{ __('permissions.template_readonly_desc') }}</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<!-- Include Permissions Manager Script -->
<script src="{{ base_url }}/js/permissions-manager.js?v={{ 'now'|date('YmdHis') }}&fix=5"></script>
<!-- Include Permission Switches Script -->
<script src="{{ base_url }}/js/permission-switches.js?v={{ 'now'|date('YmdHis') }}"></script>

<script>
// Permission modules configuration
const permissionModules = [
    {
        code: 'invoices',
        name: '{{ __("modules.invoices") }}',
        icon: 'fa-file-invoice',
        description: '{{ __("modules.invoices_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['bulk_generate', 'send_email', 'export', 'void'],
        submodules: {
            'invoices.retrocession': {
                code: 'invoices.retrocession',
                name: '{{ __("modules.retrocession") }}',
                icon: 'fa-hand-holding-usd',
                permissions: ['view', 'create', 'edit', 'delete']
            },
            'invoices.recurring': {
                code: 'invoices.recurring',
                name: '{{ __("modules.recurring_invoices") }}',
                icon: 'fa-sync',
                permissions: ['view', 'create', 'edit', 'delete'],
                special: ['manage_schedules']
            }
        }
    },
    {
        code: 'clients',
        name: '{{ __("modules.clients") }}',
        icon: 'fa-users',
        description: '{{ __("modules.clients_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['export', 'import', 'merge']
    },
    {
        code: 'products',
        name: '{{ __("modules.products") }}',
        icon: 'fa-box',
        description: '{{ __("modules.products_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['manage_stock', 'import', 'bulk_update']
    },
    {
        code: 'reports',
        name: '{{ __("modules.reports") }}',
        icon: 'fa-chart-bar',
        description: '{{ __("modules.reports_desc") }}',
        permissions: ['view'],
        special: ['view_financial', 'view_inventory', 'view_analytics', 'export', 'schedule']
    },
    {
        code: 'config',
        name: '{{ __("modules.configuration") }}',
        icon: 'fa-cog',
        description: '{{ __("modules.config_desc") }}',
        permissions: ['view', 'edit'],
        special: ['manage_company', 'manage_system', 'manage_templates', 'manage_integrations']
    },
    {
        code: 'users',
        name: '{{ __("modules.users") }}',
        icon: 'fa-user-cog',
        description: '{{ __("modules.users_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['manage_groups', 'manage_permissions', 'reset_passwords', 'view_activity']
    },
    {
        code: 'stock',
        name: '{{ __("modules.stock") }}',
        icon: 'fa-warehouse',
        description: '{{ __("modules.stock_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['adjust_inventory', 'transfer', 'audit']
    },
    {
        code: 'documents',
        name: '{{ __("modules.documents") }}',
        icon: 'fa-file-alt',
        description: '{{ __("modules.documents_desc") }}',
        permissions: ['view', 'create', 'edit', 'delete'],
        special: ['share', 'version_control', 'bulk_upload']
    }
];

// Initialize permissions manager with translations
const permissionsManagerOptions = {
    modules: permissionModules,
    apiBaseUrl: '{{ url("/admin/permissions") }}',
    csrfToken: '{{ csrf_token }}',
    autoSave: false,
    translations: {
        saving: '{{ __("permissions.saving") }}',
        saved: '{{ __("permissions.saved_successfully") }}',
        error: '{{ __("permissions.save_failed") }}',
        confirmReset: '{{ __("permissions.confirm_reset") }}',
        confirmCopy: '{{ __("permissions.confirm_copy") }}',
        loading: '{{ __("common.loading") }}',
        noResults: '{{ __("common.no_results") }}',
        searchPlaceholder: '{{ __("permissions.search_modules") }}',
        selectAll: '{{ __("permissions.select_all") }}',
        deselectAll: '{{ __("permissions.deselect_all") }}'
    }
};

// Initialize permissions manager with options from this view
// This will override the default initialization in permissions-manager.js
document.addEventListener('DOMContentLoaded', function() {
    // Check if PermissionsManager class is available
    if (typeof PermissionsManager !== 'undefined' && typeof permissionsManagerOptions !== 'undefined') {
        // Initialize with custom options from this view
        window.permissionsManager = new PermissionsManager(permissionsManagerOptions);
        
        // Load initial group if not already loaded
        if (window.permissionsManager && !window.permissionsManager.currentGroupId) {
            const firstGroup = document.querySelector('#groupList .list-group-item');
            if (firstGroup) {
                const groupId = firstGroup.dataset.groupId;
                window.loadGroupPermissions(groupId);
            }
        }
    }
});

// Load permissions for a specific group - moved to global scope
window.loadGroupPermissions = function(groupId) {
    if (!window.permissionsManager) {
        console.error('PermissionsManager not initialized');
        return false;
    }
    
    // Update active group in UI
    document.querySelectorAll('#groupList .list-group-item').forEach(item => {
        item.classList.remove('active');
    });
    
    const currentItem = document.querySelector(`[data-group-id="${groupId}"]`);
    if (currentItem) {
        currentItem.classList.add('active');
        
        // Update current group name
        const groupName = currentItem.querySelector('span').textContent.trim();
        document.getElementById('currentGroupName').textContent = groupName;
        document.getElementById('currentGroupId').value = groupId;
    }
    
    // Load permissions using the permissions manager
    window.permissionsManager.loadGroupPermissions(groupId);
    
    return false; // Prevent default link behavior
};

// Reset permissions to default
window.resetToDefault = function() {
    if (!window.permissionsManager) {
        console.error('PermissionsManager not initialized');
        return;
    }
    
    if (confirm('{{ __("permissions.confirm_reset") }}')) {
        window.permissionsManager.resetToDefault();
    }
};

// Create new group
window.createNewGroup = function() {
    if (window.permissionsManager) {
        window.permissionsManager.createNewGroup();
    } else {
        alert('PermissionsManager not initialized');
    }
};

// Filter modules based on search
window.filterModules = function() {
    if (window.permissionsManager) {
        window.permissionsManager.filterModules();
    } else {
        // Fallback implementation
        const searchTerm = document.getElementById('moduleSearch').value.toLowerCase();
        
        document.querySelectorAll('.module-card').forEach(card => {
            const moduleName = card.querySelector('.module-header').textContent.toLowerCase();
            const moduleDesc = card.querySelector('.text-muted')?.textContent.toLowerCase() || '';
            
            if (moduleName.includes(searchTerm) || moduleDesc.includes(searchTerm)) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });
    }
};

// Handle dirty state indicator
const dirtyIndicator = document.getElementById('dirtyIndicator');
if (dirtyIndicator) {
    // Listen for form changes
    document.getElementById('permissionsForm')?.addEventListener('change', () => {
        dirtyIndicator.classList.add('show');
    });
    
    // Hide after save
    document.getElementById('permissionsForm')?.addEventListener('submit', () => {
        setTimeout(() => {
            dirtyIndicator.classList.remove('show');
        }, 2000);
    });
}

// Mobile responsive handling
function handleMobileView() {
    const isMobile = window.innerWidth < 768;
    
    if (isMobile) {
        // Convert table headers to icons on mobile
        document.querySelectorAll('.permission-toggle').forEach(toggle => {
            const label = toggle.nextElementSibling;
            if (label) {
                const icon = label.querySelector('i');
                if (icon) {
                    icon.classList.remove('d-md-none');
                }
                const text = label.querySelector('span');
                if (text) {
                    text.classList.add('d-none');
                }
            }
        });
    }
}

// Initialize mobile view
handleMobileView();
window.addEventListener('resize', handleMobileView);

// Enhance touch interactions
if ('ontouchstart' in window) {
    document.body.classList.add('touch-device');
}
</script>
{% endblock %}

{% block styles %}
{{ parent() }}
<link rel="stylesheet" href="{{ base_url }}/css/permission-switches.css?v={{ 'now'|date('YmdHis') }}">
{% endblock %}