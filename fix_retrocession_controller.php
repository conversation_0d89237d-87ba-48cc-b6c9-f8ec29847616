<?php
/**
 * Fix RetrocessionController to handle missing exclude_patient_line column
 */

require_once __DIR__ . '/vendor/autoload.php';

// Read the controller file
$controllerFile = __DIR__ . '/app/controllers/RetrocessionController.php';
$content = file_get_contents($controllerFile);

// Remove the exclude_patient_line from the SELECT query temporarily
$oldQuery = "rde.status as entry_status,
                rde.exclude_patient_line,
                rde.invoice_id,";

$newQuery = "rde.status as entry_status,
                -- rde.exclude_patient_line, -- Column will be added after migration
                rde.invoice_id,";

// Check if we need to update
if (strpos($content, $oldQuery) !== false) {
    $content = str_replace($oldQuery, $newQuery, $content);
    file_put_contents($controllerFile, $content);
    echo "✓ Temporarily commented out exclude_patient_line in RetrocessionController\n";
    echo "  This prevents the error while you run the migration.\n";
} else {
    echo "⚠ Query pattern not found or already fixed.\n";
}

echo "\nNext steps:\n";
echo "1. Run the migration: php run_exclude_patient_migration.php\n";
echo "2. After migration succeeds, run: php restore_retrocession_controller.php\n";