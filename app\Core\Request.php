<?php

namespace App\Core;

use Flight;

class Request
{
    protected $data;
    
    public function __construct()
    {
        $this->data = Flight::request()->data->getData();
        
        // Merge with parsed JSON body if content type is JSON
        if (strpos(Flight::request()->type, 'json') !== false) {
            $jsonData = json_decode(Flight::request()->body, true);
            if ($jsonData) {
                $this->data = array_merge($this->data, $jsonData);
            }
        }
    }
    
    /**
     * Get all request data
     */
    public function all()
    {
        return $this->data;
    }
    
    /**
     * Get specific field
     */
    public function get($key, $default = null)
    {
        return $this->data[$key] ?? $default;
    }
    
    /**
     * Check if field exists
     */
    public function has($key)
    {
        return isset($this->data[$key]);
    }
    
    /**
     * Validate request data
     */
    public function validate($rules)
    {
        $errors = [];
        $validated = [];
        
        foreach ($rules as $field => $rule) {
            $fieldRules = explode('|', $rule);
            $value = $this->get($field);
            
            foreach ($fieldRules as $fieldRule) {
                // Required validation
                if ($fieldRule === 'required' && ($value === null || $value === '')) {
                    $errors[$field][] = "The $field field is required.";
                    continue;
                }
                
                // Skip other validations if field is nullable and empty
                if ($fieldRule === 'nullable' && ($value === null || $value === '')) {
                    continue 2;
                }
                
                // String validation
                if ($fieldRule === 'string' && !is_string($value)) {
                    $errors[$field][] = "The $field field must be a string.";
                }
                
                // Numeric validation
                if ($fieldRule === 'numeric' && $value !== null && !is_numeric($value)) {
                    $errors[$field][] = "The $field field must be numeric.";
                }
                
                // Email validation
                if ($fieldRule === 'email' && $value !== null && !filter_var($value, FILTER_VALIDATE_EMAIL)) {
                    $errors[$field][] = "The $field field must be a valid email address.";
                }
                
                // URL validation
                if ($fieldRule === 'url' && $value !== null && !filter_var($value, FILTER_VALIDATE_URL)) {
                    $errors[$field][] = "The $field field must be a valid URL.";
                }
                
                // Boolean validation
                if ($fieldRule === 'boolean') {
                    $value = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
                }
                
                // Integer validation
                if ($fieldRule === 'integer' && $value !== null) {
                    if (!is_numeric($value) || (int)$value != $value) {
                        $errors[$field][] = "The $field field must be an integer.";
                    } else {
                        $value = (int)$value;
                    }
                }
                
                // Max length validation
                if (strpos($fieldRule, 'max:') === 0) {
                    $max = (int) str_replace('max:', '', $fieldRule);
                    if (is_string($value) && strlen($value) > $max) {
                        $errors[$field][] = "The $field field must not exceed $max characters.";
                    } elseif (is_numeric($value) && $value > $max) {
                        $errors[$field][] = "The $field field must not be greater than $max.";
                    }
                }
                
                // Min validation
                if (strpos($fieldRule, 'min:') === 0) {
                    $min = (int) str_replace('min:', '', $fieldRule);
                    if (is_numeric($value) && $value < $min) {
                        $errors[$field][] = "The $field field must be at least $min.";
                    }
                }
                
                // Unique validation (simplified - would need DB check in real implementation)
                if (strpos($fieldRule, 'unique:') === 0) {
                    // This would require database validation
                    // For now, we'll skip this validation
                }
            }
            
            if (!isset($errors[$field]) && $value !== null) {
                $validated[$field] = $value;
            }
        }
        
        if (!empty($errors)) {
            throw new ValidationException($errors);
        }
        
        return $validated;
    }
}