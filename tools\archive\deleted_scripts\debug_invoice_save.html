<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Debug Invoice Save</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Debug Invoice Save</h1>
        
        <div class="alert alert-info">
            <h5>Instructions to debug invoice save:</h5>
            <ol>
                <li>Open the invoice creation page: <a href="/fit/public/invoices/create" target="_blank">/fit/public/invoices/create</a></li>
                <li>Open browser Developer Console (F12)</li>
                <li>Go to the Console tab</li>
                <li>Paste this code in the console:</li>
            </ol>
        </div>
        
        <div class="card">
            <div class="card-body">
                <pre><code>// Override form submission to debug
document.getElementById('invoiceForm').addEventListener('submit', function(e) {
    console.log('Form submission triggered');
    
    // Check validation
    if (!this.checkValidity()) {
        e.preventDefault();
        e.stopPropagation();
        console.error('Form validation failed!');
        
        // Find invalid fields
        const invalidFields = this.querySelectorAll(':invalid');
        console.log('Invalid fields:', invalidFields.length);
        invalidFields.forEach(field => {
            console.log('- Invalid field:', field.name, field.validationMessage);
        });
    } else {
        console.log('Form validation passed');
        
        // Log form data
        const formData = new FormData(this);
        console.log('Form data being submitted:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}:`, value);
        }
    }
    
    this.classList.add('was-validated');
});

// Also log any AJAX errors
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e);
});

console.log('Debug code loaded! Now try to save an invoice.');
</code></pre>
            </div>
        </div>
        
        <div class="mt-4">
            <h3>Common Issues:</h3>
            <ul>
                <li><strong>Required fields missing:</strong> Check if all fields marked with * are filled</li>
                <li><strong>Date format issues:</strong> Dates should be in YYYY-MM-DD format</li>
                <li><strong>Number format issues:</strong> Use dots for decimals (e.g., 100.00)</li>
                <li><strong>No client selected:</strong> Make sure to select a client from the dropdown</li>
                <li><strong>No items added:</strong> Add at least one line item to the invoice</li>
            </ul>
        </div>
        
        <div class="mt-4">
            <h3>Alternative: Direct Test</h3>
            <p>If the form still doesn't work, try this minimal test form:</p>
            <form method="POST" action="/fit/public/invoices" class="border p-3">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="action" value="save">
                
                <div class="mb-3">
                    <label>Document Type ID:</label>
                    <input type="number" name="document_type_id" value="1" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label>Client ID:</label>
                    <input type="number" name="client_id" value="2" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label>Issue Date:</label>
                    <input type="date" name="issue_date" value="2025-07-08" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label>Due Date:</label>
                    <input type="date" name="due_date" value="2025-08-07" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label>Subtotal:</label>
                    <input type="number" name="subtotal" value="100" step="0.01" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label>VAT Amount:</label>
                    <input type="number" name="vat_amount" value="17" step="0.01" class="form-control" required>
                </div>
                
                <div class="mb-3">
                    <label>Total:</label>
                    <input type="number" name="total" value="117" step="0.01" class="form-control" required>
                </div>
                
                <button type="submit" class="btn btn-primary">Save Test Invoice</button>
            </form>
        </div>
    </div>
</body>
</html>