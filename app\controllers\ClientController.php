<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\Client;
use App\Models\CustomField;
use Flight;

class ClientController extends Controller
{
    /**
     * List all clients with pagination
     */
    public function index()
    {
        $page = intval($_GET['page'] ?? 1);
        $perPage = intval($_GET['per_page'] ?? 20);
        
        $filters = [
            'search' => $this->input('search'),
            'client_type' => $this->input('type'), // Changed to match form field name
            'is_active' => $this->input('status'), // Changed to match form field name
            'type' => $this->input('type'), // Added for view template
            'status' => $this->input('status') // Added for view template
        ];
        
        $clientsData = Client::getAllPaginated($page, $perPage, $filters);
        $statistics = Client::getStatistics();
        
        // Get visible fields for list view
        $visibleFields = CustomField::getVisibleFields('clients', 'list');
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'clients/index-modern';
        
        $this->render($viewFile, [
            'clients' => $clientsData['data'],
            'pagination' => [
                'total' => $clientsData['total'],
                'page' => $clientsData['page'],
                'per_page' => $clientsData['per_page'],
                'total_pages' => $clientsData['total_pages']
            ],
            'filters' => $filters,
            'client_types' => Client::getClientTypes(),
            'statistics' => $statistics,
            'visible_fields' => $visibleFields,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Show create client form
     */
    public function create()
    {
        // Get visible fields for form view
        $visibleFields = CustomField::getVisibleFields('clients', 'form');
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'clients/create-modern';
        
        $this->render($viewFile, [
            'client_number' => Client::generateClientNumber(),
            'client_types' => Client::getClientTypes(),
            'countries' => $this->getCountriesList(),
            'visible_fields' => $visibleFields,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Store new client
     */
    public function store()
    {
        // Debug logging
        error_log('ClientController::store() called');
        error_log('POST data: ' . json_encode($_POST));
        error_log('Request method: ' . $_SERVER['REQUEST_METHOD']);
        
        if (!$this->validateCsrfToken()) {
            error_log('CSRF validation failed');
            Flight::flash('error', __('common.csrf_error'));
            Flight::redirect('/clients/create');
            return;
        }
        
        $validation = $this->validateClientData();
        if ($validation !== true) {
            error_log('Validation failed: ' . $validation);
            Flight::flash('error', $validation);
            Flight::redirect('/clients/create');
            return;
        }
        
        error_log('Validation passed, proceeding to create client');
        
        try {
            $data = [
                'client_number' => $this->input('client_number'),
                'client_type' => $this->input('client_type'),
                'email' => $this->input('email', null, FILTER_SANITIZE_EMAIL),
                'phone' => $this->input('phone'),
                'mobile' => $this->input('mobile'),
                'fax' => $this->input('fax'),
                'website' => $this->input('website'),
                'address_line1' => $this->input('address_line1'),
                'address_line2' => $this->input('address_line2'),
                'city' => $this->input('city'),
                'postal_code' => $this->input('postal_code'),
                'country' => $this->input('country'),
                'vat_number' => $this->input('vat_number'),
                'tax_exempt' => $this->input('tax_exempt') ? 1 : 0,
                'payment_terms' => intval($this->input('payment_terms', 30)),
                'credit_limit' => floatval($this->input('credit_limit', 0)),
                'discount_percentage' => floatval($this->input('discount_percentage', 0)),
                'notes' => $this->input('notes'),
                'tags' => $this->input('tags'),
                'is_active' => intval($this->input('status', 1)),
                'created_by' => $_SESSION['user_id']
            ];
            
            // Add type-specific fields
            if ($data['client_type'] === 'company') {
                $data['company_name'] = $this->input('company_name');
                $data['company_registration'] = $this->input('company_registration');
                $data['contact_person'] = $this->input('contact_person');
            } else {
                $data['first_name'] = $this->input('first_name');
                $data['last_name'] = $this->input('last_name');
                $birthDate = $this->input('birth_date');
                $data['birth_date'] = (!empty($birthDate) && $birthDate !== '0000-00-00') ? $birthDate : null;
                $data['gender'] = $this->input('gender');
            }
            
            $client = new Client();
            error_log('Calling createNew with data: ' . json_encode($data));
            $clientId = $client->createNew($data);
            error_log('createNew returned: ' . var_export($clientId, true));
            
            if ($clientId) {
                // Update billable entity
                $client->id = $clientId;
                foreach ($data as $key => $value) {
                    $client->$key = $value;
                }
                $client->updateBillableEntity();
                
                // Save custom field values
                $customFields = CustomField::getAllFieldsForModule('clients');
                $customValues = [];
                foreach ($customFields as $field) {
                    if ($field['field_source'] === 'custom') {
                        $value = $this->input('custom_' . $field['field_name']);
                        if ($value !== null) {
                            $customValues[$field['field_name']] = $value;
                        }
                    }
                }
                if (!empty($customValues)) {
                    CustomField::saveCustomFieldValues('clients', $clientId, $customValues);
                }
                
                Flight::flash('success', __('clients.created_successfully'));
                Flight::redirect('/clients/' . $clientId);
            } else {
                Flight::flash('error', __('common.error_occurred'));
                Flight::redirect('/clients/create');
            }
        } catch (\Exception $e) {
            // Log the actual error for debugging
            error_log('Client creation error: ' . $e->getMessage());
            error_log('Stack trace: ' . $e->getTraceAsString());
            
            Flight::flash('error', __('common.error_occurred') . ' - ' . $e->getMessage());
            Flight::redirect('/clients/create');
        }
    }

    /**
     * Show client details
     */
    public function show($id)
    {
        $client = Client::getWithDetails($id);
        
        if (!$client) {
            Flight::notFound();
            return;
        }
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'clients/show-modern';
        
        $this->render($viewFile, [
            'client' => $client,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Show edit client form
     */
    public function edit($id)
    {
        $client = Client::findById($id);
        
        if (!$client) {
            Flight::notFound();
            return;
        }
        
        // Get visible fields for form view
        $visibleFields = CustomField::getVisibleFields('clients', 'form');
        
        // Get custom field values
        $client['custom_fields'] = CustomField::getCustomFieldValues('clients', $id);
        
        // Determine template to use
        $template = $this->getTemplate();
        $viewFile = 'clients/edit-modern';
        
        $this->render($viewFile, [
            'client' => $client,
            'client_types' => Client::getClientTypes(),
            'countries' => $this->getCountriesList(),
            'visible_fields' => $visibleFields,
            'csrf_token' => $this->generateCsrfToken()
        ]);
    }

    /**
     * Update client
     */
    public function update($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::flash('error', __('common.csrf_error'));
            Flight::redirect('/clients/' . $id . '/edit');
            return;
        }
        
        $client = Client::findById($id);
        if (!$client) {
            Flight::notFound();
            return;
        }
        
        $validation = $this->validateClientData($id);
        if ($validation !== true) {
            Flight::flash('error', $validation);
            Flight::redirect('/clients/' . $id . '/edit');
            return;
        }
        
        try {
            $data = [
                'email' => $this->input('email', null, FILTER_SANITIZE_EMAIL),
                'phone' => $this->input('phone'),
                'mobile' => $this->input('mobile'),
                'fax' => $this->input('fax'),
                'website' => $this->input('website'),
                'address_line1' => $this->input('address_line1'),
                'address_line2' => $this->input('address_line2'),
                'city' => $this->input('city'),
                'postal_code' => $this->input('postal_code'),
                'country' => $this->input('country'),
                'vat_number' => $this->input('vat_number'),
                'tax_exempt' => $this->input('tax_exempt') ? 1 : 0,
                'payment_terms' => intval($this->input('payment_terms', 30)),
                'credit_limit' => floatval($this->input('credit_limit', 0)),
                'discount_percentage' => floatval($this->input('discount_percentage', 0)),
                'notes' => $this->input('notes'),
                'tags' => $this->input('tags'),
                'is_active' => $this->input('is_active') ? 1 : 0,
                'updated_by' => $_SESSION['user_id'] ?? null
            ];
            
            // Update type-specific fields
            if ($client['client_type'] === 'company') {
                $data['company_name'] = $this->input('company_name');
                $data['company_registration'] = $this->input('company_registration');
                $data['contact_person'] = $this->input('contact_person');
                // Don't set name - it's a generated column
            } else {
                $data['first_name'] = $this->input('first_name');
                $data['last_name'] = $this->input('last_name');
                $birthDate = $this->input('birth_date');
                $data['birth_date'] = (!empty($birthDate) && $birthDate !== '0000-00-00') ? $birthDate : null;
                $data['gender'] = $this->input('gender');
                // Don't set name - it's a generated column
            }
            
            if (Client::updateById($id, $data)) {
                // Update billable entity
                $clientData = Client::findById($id);
                if ($clientData) {
                    $client = new Client();
                    foreach ($clientData as $key => $value) {
                        $client->$key = $value;
                    }
                    $client->updateBillableEntity();
                }
                
                // Save custom field values
                $customFields = CustomField::getAllFieldsForModule('clients');
                $customValues = [];
                foreach ($customFields as $field) {
                    if ($field['field_source'] === 'custom') {
                        $value = $this->input('custom_' . $field['field_name']);
                        if ($value !== null) {
                            $customValues[$field['field_name']] = $value;
                        }
                    }
                }
                if (!empty($customValues)) {
                    CustomField::saveCustomFieldValues('clients', $id, $customValues);
                }
                
                Flight::flash('success', __('clients.updated_successfully'));
                Flight::redirect('/clients/' . $id);
            } else {
                Flight::flash('error', __('common.error_occurred'));
                Flight::redirect('/clients/' . $id . '/edit');
            }
        } catch (\Exception $e) {
            Flight::flash('error', __('common.error_occurred'));
            Flight::redirect('/clients/' . $id . '/edit');
        }
    }

    /**
     * Delete client
     */
    public function destroy($id)
    {
        if (!$this->validateCsrfToken()) {
            Flight::json(['error' => __('common.csrf_error')], 403);
            return;
        }
        
        $client = Client::findById($id);
        if (!$client) {
            Flight::json(['error' => __('common.not_found')], 404);
            return;
        }
        
        try {
            $clientModel = new Client();
            if ($clientModel->delete($id)) {
                Flight::json(['success' => true, 'message' => __('clients.deleted_successfully')]);
            } else {
                Flight::json(['error' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['error' => __('common.error_occurred')], 500);
        }
    }

    /**
     * Search clients for autocomplete
     */
    public function search()
    {
        $query = $this->input('q');
        if (strlen($query) < 2) {
            Flight::json([]);
            return;
        }
        
        try {
            $clients = Client::search($query, 20);
            Flight::json($clients);
        } catch (\Exception $e) {
            Flight::json([]);
        }
    }
    
    /**
     * Quick add client via AJAX (for invoice form)
     */
    public function quickAdd()
    {
        // Check if request is AJAX
        if (!Flight::request()->ajax) {
            Flight::json(['success' => false, 'message' => 'Invalid request'], 400);
            return;
        }
        
        // Validate basic data
        $clientType = $this->input('client_type', 'individual');
        $data = [
            'client_type' => $clientType,
            'email' => $this->input('email'),
            'phone' => $this->input('phone'),
            'address' => $this->input('address'),
            'city' => $this->input('city'),
            'postal_code' => $this->input('postal_code'),
            'country' => $this->input('country', 'Luxembourg'),
            'vat_number' => $this->input('vat_number'),
            'vat_rate' => $this->input('vat_rate', 17),
            'is_active' => true,
            'created_by' => $_SESSION['user_id'] ?? 1
        ];
        
        // Add type-specific fields
        if ($clientType === 'company') {
            $data['company_name'] = $this->input('company_name');
            if (empty($data['company_name'])) {
                Flight::json(['success' => false, 'message' => __('clients.company_name_required')], 400);
                return;
            }
        } else {
            $data['first_name'] = $this->input('first_name');
            $data['last_name'] = $this->input('last_name');
            if (empty($data['first_name']) || empty($data['last_name'])) {
                Flight::json(['success' => false, 'message' => __('clients.name_required')], 400);
                return;
            }
        }
        
        // Validate email
        if (empty($data['email']) || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            Flight::json(['success' => false, 'message' => __('clients.invalid_email')], 400);
            return;
        }
        
        // Check for duplicate email
        $existing = Client::findByEmail($data['email']);
        if ($existing) {
            Flight::json(['success' => false, 'message' => __('clients.email_already_exists')], 400);
            return;
        }
        
        try {
            // Generate client number
            $clientModel = new Client();
            $data['client_number'] = $clientModel->generateClientNumber();
            
            // Create client
            $id = Client::create($data);
            
            if ($id) {
                $client = Client::findById($id);
                
                // Format response
                $response = [
                    'success' => true,
                    'message' => __('clients.created_successfully'),
                    'client' => [
                        'id' => $client['id'],
                        'client_number' => $client['client_number'],
                        'name' => $clientType === 'company' 
                            ? $client['company_name'] 
                            : $client['first_name'] . ' ' . $client['last_name'],
                        'email' => $client['email'],
                        'phone' => $client['phone'],
                        'client_type' => $client['client_type'],
                        'vat_rate' => $client['vat_rate']
                    ]
                ];
                
                Flight::json($response);
            } else {
                Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            error_log('Quick add client error: ' . $e->getMessage());
            Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
        }
    }

    /**
     * Validate client data
     */
    private function validateClientData($id = null): mixed
    {
        $errors = [];
        $clientType = $this->input('client_type');
        
        if ($clientType === 'company') {
            if (empty($this->input('company_name'))) {
                $errors[] = __('clients.company_name_required');
            }
        } else {
            if (empty($this->input('first_name'))) {
                $errors[] = __('clients.first_name_required');
            }
            
            if (empty($this->input('last_name'))) {
                $errors[] = __('clients.last_name_required');
            }
        }
        
        $email = $this->input('email');
        if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors[] = __('common.invalid_email');
        }
        
        $website = $this->input('website');
        if (!empty($website) && !filter_var($website, FILTER_VALIDATE_URL)) {
            $errors[] = __('clients.invalid_website');
        }
        
        if (!empty($errors)) {
            return implode('<br>', $errors);
        }
        
        return true;
    }

    /**
     * Get countries list
     */
    private function getCountriesList(): array
    {
        return [
            'LU' => 'Luxembourg',
            'FR' => 'France',
            'BE' => 'Belgium',
            'DE' => 'Germany',
            'NL' => 'Netherlands',
            'IT' => 'Italy',
            'ES' => 'Spain',
            'PT' => 'Portugal',
            'GB' => 'United Kingdom',
            'CH' => 'Switzerland',
            'AT' => 'Austria',
            'PL' => 'Poland',
            'CZ' => 'Czech Republic',
            'RO' => 'Romania',
            'HU' => 'Hungary',
            'GR' => 'Greece',
            'SE' => 'Sweden',
            'DK' => 'Denmark',
            'NO' => 'Norway',
            'FI' => 'Finland',
            'IE' => 'Ireland'
        ];
    }
    
    /**
     * Delete client (AJAX)
     */
    public function delete($id)
    {
        // Ensure session is started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            Flight::json(['success' => false, 'message' => __('common.csrf_error')], 403);
            return;
        }
        
        $client = Client::findById($id);
        if (!$client) {
            Flight::json(['success' => false, 'message' => __('common.not_found')], 404);
            return;
        }
        
        try {
            // Check if client has invoices
            $db = Flight::db();
            $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE billable_type = 'client' AND billable_id = :id");
            $stmt->execute(['id' => $id]);
            $invoiceCount = $stmt->fetchColumn();
            
            if ($invoiceCount > 0) {
                Flight::json(['success' => false, 'message' => __('clients.cannot_delete_has_invoices')], 400);
                return;
            }
            
            // Delete client
            $clientModel = new Client();
            if ($clientModel->delete($id)) {
                Flight::json(['success' => true, 'message' => __('common.deleted_successfully')]);
            } else {
                Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
        }
    }
    
    /**
     * Toggle client status (AJAX)
     */
    public function toggleStatus($id)
    {
        // Get JSON data
        $data = json_decode(Flight::request()->getBody(), true);
        
        $client = Client::findById($id);
        if (!$client) {
            Flight::json(['success' => false, 'message' => __('common.not_found')], 404);
            return;
        }
        
        try {
            // Update status
            $newStatus = $data['status'] ?? ($client['is_active'] == 1 ? 0 : 1);
            $updateData = [
                'is_active' => $newStatus,
                'updated_by' => $_SESSION['user']['id'] ?? null
            ];
            
            if (Client::updateById($id, $updateData)) {
                $message = $newStatus == 1 
                    ? __('clients.client_activated') 
                    : __('clients.client_deactivated');
                    
                Flight::json([
                    'success' => true, 
                    'message' => $message,
                    'status' => $newStatus
                ]);
            } else {
                Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
            }
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
        }
    }
    
    /**
     * Bulk delete clients (AJAX)
     */
    public function bulkDelete()
    {
        // Validate CSRF token
        if (!$this->validateCsrfToken()) {
            Flight::json(['success' => false, 'message' => __('common.csrf_error')], 403);
            return;
        }
        
        $ids = $_POST['ids'] ?? [];
        if (empty($ids) || !is_array($ids)) {
            Flight::json(['success' => false, 'message' => __('common.no_items_selected')], 400);
            return;
        }
        
        try {
            $db = Flight::db();
            $deletedCount = 0;
            $skippedCount = 0;
            
            foreach ($ids as $id) {
                // Check if client has invoices
                $stmt = $db->prepare("SELECT COUNT(*) FROM invoices WHERE billable_type = 'client' AND billable_id = :id");
                $stmt->execute(['id' => $id]);
                $invoiceCount = $stmt->fetchColumn();
                
                if ($invoiceCount > 0) {
                    $skippedCount++;
                    continue;
                }
                
                // Delete client
                $client = Client::findById($id);
                if ($client && $client->delete()) {
                    $deletedCount++;
                }
            }
            
            $message = sprintf(__('clients.bulk_delete_result'), $deletedCount);
            if ($skippedCount > 0) {
                $message .= ' ' . sprintf(__('clients.bulk_delete_skipped'), $skippedCount);
            }
            
            Flight::json(['success' => true, 'message' => $message]);
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => __('common.error_occurred')], 500);
        }
    }
}