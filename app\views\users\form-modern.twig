{% extends "base-modern.twig" %}

{% block title %}{{ user.id ? __('users.edit_user') : __('users.add_user') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ user.id ? __('users.edit_user') : __('users.add_user') }}</h1>
        <a href="{{ base_url }}/users" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
        </a>
    </div>

    {% if user.id %}
    <!-- User Information Bar -->
    <div class="card mb-4 shadow-sm">
        <div class="card-body py-3">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-sm-3">
                            <small class="text-muted">{{ __('users.user_id') }}:</small>
                            <div class="fw-bold">{{ user.id }}</div>
                        </div>
                        <div class="col-sm-3">
                            <small class="text-muted">{{ __('common.created_at') }}:</small>
                            <div class="fw-bold">{{ user.created_at|date('d/m/Y') }}</div>
                        </div>
                        <div class="col-sm-3">
                            <small class="text-muted">{{ __('users.last_login') }}:</small>
                            <div class="fw-bold">
                                {% if user.last_login_at %}
                                    {{ user.last_login_at|date('d/m/Y') }}
                                {% else %}
                                    <span class="text-muted">{{ __('users.never') }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <small class="text-muted">{{ __('common.status') }}:</small>
                            <div>
                                {% if user.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <small class="text-muted d-block">{{ __('users.full_name') }}</small>
                    <h5 class="mb-0">{{ user.first_name }} {{ user.last_name }}</h5>
                    <small class="text-muted">{{ user.email }}</small>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <div class="col-12">
            <form method="POST" action="{{ user.id ? base_url ~ '/users/' ~ user.id : base_url ~ '/users' }}" 
                  enctype="multipart/form-data" class="needs-validation" novalidate id="userEditForm" data-validate="user">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="active_tab" id="active_tab" value="personal">
                <!-- Hidden submit button to catch Enter key -->
                <button type="submit" style="position: absolute; left: -9999px; width: 1px; height: 1px;" tabindex="-1" aria-hidden="true"></button>
                
                <!-- Validation Errors Display -->
                {% if session.validation_errors is defined and session.validation_errors is not empty %}
                <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                    <strong>{{ __('validation.error_title') }}</strong>
                    <ul class="mb-0 mt-2">
                        {% for error in session.validation_errors %}
                            <li>{{ error.message }}</li>
                        {% endfor %}
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endif %}
                
                <!-- Form Actions -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            {% if user.id %}
                            <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                <i class="bi bi-trash me-2"></i>Supprimer
                            </button>
                            {% else %}
                            <div></div>
                            {% endif %}
                            <div class="d-flex gap-2">
                                <a href="{{ base_url }}/users" class="btn btn-secondary">
                                    <i class="bi bi-x-circle me-2"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-circle me-2"></i>Enregistrer les modifications
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs nav-fill mb-4" id="userFormTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="personal-tab" data-bs-toggle="tab" data-bs-target="#personal" type="button" role="tab" aria-controls="personal" aria-selected="true">
                            <i class="bi bi-person-circle me-2"></i>
                            <span class="d-none d-sm-inline">{{ __('users.personal_information') }}</span>
                            <span class="d-sm-none">{{ __('users.personal') }}</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="contact-tab" data-bs-toggle="tab" data-bs-target="#contact" type="button" role="tab" aria-controls="contact" aria-selected="false">
                            <i class="bi bi-geo-alt me-2"></i>
                            <span class="d-none d-sm-inline">{{ __('users.contact_and_address') }}</span>
                            <span class="d-sm-none">{{ __('users.contact') }}</span>
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab" aria-controls="security" aria-selected="false">
                            <i class="bi bi-shield-lock me-2"></i>
                            <span class="d-none d-sm-inline">{{ __('users.access_and_security') }}</span>
                            <span class="d-sm-none">{{ __('users.security') }}</span>
                        </button>
                    </li>
                    {% if user.id and (is_practitioner or financial_obligations or can_edit_financial) %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="financial-tab" data-bs-toggle="tab" data-bs-target="#financial" type="button" role="tab" aria-controls="financial" aria-selected="false">
                            <i class="bi bi-cash-stack me-2"></i>
                            <span class="d-none d-sm-inline">{{ __('users.financial') }}</span>
                            <span class="d-sm-none">{{ __('users.financial') }}</span>
                        </button>
                    </li>
                    {% endif %}
                    {% if user.id and is_coach %}
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="courses-tab" data-bs-toggle="tab" data-bs-target="#courses" type="button" role="tab" aria-controls="courses" aria-selected="false">
                            <i class="bi bi-book me-2"></i>
                            <span class="d-none d-sm-inline">{{ __('courses.courses') }}</span>
                            <span class="d-sm-none">{{ __('courses.courses') }}</span>
                        </button>
                    </li>
                    {% endif %}
                </ul>
                
                <!-- Tab Content -->
                <div class="tab-content" id="userFormTabContent">
                    <!-- Personal Info Tab -->
                    <div class="tab-pane fade show active" id="personal" role="tabpanel" aria-labelledby="personal-tab" tabindex="0">
                        <!-- Basic Information -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-person-fill me-2"></i>{{ __('common.basic_information') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="first_name" class="form-label">{{ __('users.first_name') }} *</label>
                                        <input type="text" class="form-control" id="first_name" name="first_name" 
                                               value="{{ user.first_name }}" required>
                                        <div class="invalid-feedback">
                                            {{ __('validation.required') }}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="last_name" class="form-label">{{ __('users.last_name') }} *</label>
                                        <input type="text" class="form-control" id="last_name" name="last_name" 
                                               value="{{ user.last_name }}" required>
                                        <div class="invalid-feedback">
                                            {{ __('validation.required') }}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="username" class="form-label">{{ __('users.username') }} *</label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               value="{{ user.username }}" required>
                                        <div class="invalid-feedback">
                                            {{ __('validation.required') }}
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="email" class="form-label">{{ __('common.email') }} *</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="{{ user.email }}" required>
                                        <div class="invalid-feedback">
                                            {{ __('validation.email') }}
                                        </div>
                                    </div>
                                    
                                    {% if not user.id %}
                                    <div class="col-md-6">
                                        <label for="password" class="form-label">{{ __('users.password') }} *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password" name="password" required>
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">
                                            {{ __('validation.required') }}
                                        </div>
                                        <small class="text-muted">{{ __('users.password_requirements') }}</small>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="password_confirmation" class="form-label">{{ __('users.confirm_password') }} *</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="password_confirmation" 
                                                   name="password_confirmation" required>
                                            <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('password_confirmation')">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback">
                                            {{ __('validation.password_match') }}
                                        </div>
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Profile Picture -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-camera-fill me-2"></i>{{ __('users.profile_picture') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row align-items-center">
                                    <div class="col-auto">
                                        {% if user.avatar %}
                                            <img src="{{ base_url ~ '/uploads/avatars/' ~ user.avatar }}" 
                                                 class="rounded-circle" style="width: 100px; height: 100px;" 
                                                 alt="{{ user.first_name ~ ' ' ~ user.last_name }}" id="avatarPreview">
                                        {% else %}
                                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" 
                                                 style="width: 100px; height: 100px;" id="avatarPreview">
                                                <i class="bi bi-person-fill fs-1"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col">
                                        <div class="mb-2">
                                            <input type="file" class="form-control" id="avatar" name="avatar" accept="image/*">
                                            <small class="text-muted">{{ __('users.avatar_hint') }}</small>
                                        </div>
                                        {% if user.avatar %}
                                        <div class="form-check">
                                            <input type="checkbox" class="form-check-input" id="remove_avatar" name="remove_avatar">
                                            <label class="form-check-label" for="remove_avatar">
                                                {{ __('users.remove_avatar') }}
                                            </label>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Contact & Address Tab -->
                    <div class="tab-pane fade" id="contact" role="tabpanel" aria-labelledby="contact-tab" tabindex="0">
                        <!-- Address Information -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-geo-alt-fill me-2"></i>{{ __('users.address_information') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label for="address" class="form-label">{{ __('common.address') }}</label>
                                        <input type="text" class="form-control" id="address" name="address" 
                                               value="{{ user.address|default('15, am Pëtz') }}">
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="postal_code" class="form-label">{{ __('common.postal_code') }}</label>
                                        <input type="text" class="form-control" id="postal_code" name="postal_code" 
                                               value="{{ user.postal_code|default('L-9579') }}">
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="city" class="form-label">{{ __('common.city') }}</label>
                                        <input type="text" class="form-control" id="city" name="city" 
                                               value="{{ user.city|default('Weidingen') }}">
                                    </div>
                                    
                                    <div class="col-md-4">
                                        <label for="country" class="form-label">{{ __('common.country') }}</label>
                                        <select class="form-select" id="country" name="country">
                                            <option value="LU" {{ (user.country|default('LU')) == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                            <option value="FR" {{ user.country == 'FR' ? 'selected' : '' }}>France</option>
                                            <option value="BE" {{ user.country == 'BE' ? 'selected' : '' }}>Belgique</option>
                                            <option value="DE" {{ user.country == 'DE' ? 'selected' : '' }}>Allemagne</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Tax Information -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-receipt-cutoff me-2"></i>{{ __('users.tax_information') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <label for="vat_intercommunautaire" class="form-label">{{ __('users.vat_intercommunautaire') }}</label>
                                        <input type="text" class="form-control" id="vat_intercommunautaire" name="vat_intercommunautaire" 
                                               value="{{ user.vat_intercommunautaire }}"
                                               placeholder="LU12345678">
                                        <small class="text-muted">{{ __('users.vat_intercommunautaire_hint') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Access & Security Tab -->
                    <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab" tabindex="0">
                        <!-- Access Control -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-shield-lock-fill me-2"></i>{{ __('users.access_control') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="is_active" class="form-label">{{ __('common.status') }}</label>
                                        <select class="form-select" id="is_active" name="is_active">
                                            <option value="1" {{ user.is_active ? 'selected' : '' }}>{{ __('common.active') }}</option>
                                            <option value="0" {{ not user.is_active ? 'selected' : '' }}>{{ __('common.inactive') }}</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <label class="form-label">{{ __('users.user_groups') }}</label>
                                        <div class="row">
                                            {% for group in groups %}
                                            <div class="col-md-4 mb-2">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" 
                                                           id="group_{{ group.id }}" name="groups[]" 
                                                           value="{{ group.id }}"
                                                           {{ group.id in userGroups ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="group_{{ group.id }}">
                                                        <span class="badge" style="background-color: {{ group.color }};">
                                                            <i class="{{ group.icon }} me-1"></i>{{ group.name }}
                                                        </span>
                                                    </label>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        <small class="text-muted">{{ __('users.help_groups') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                                
                        <!-- Additional Settings -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="bi bi-gear-fill me-2"></i>{{ __('users.additional_settings') }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label for="language" class="form-label">{{ __('users.preferred_language') }}</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="fr" {{ user.language == 'fr' ? 'selected' : '' }}>Français</option>
                                            <option value="en" {{ user.language == 'en' ? 'selected' : '' }}>English</option>
                                            <option value="de" {{ user.language == 'de' ? 'selected' : '' }}>Deutsch</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <label for="timezone" class="form-label">{{ __('users.timezone') }}</label>
                                        <select class="form-select" id="timezone" name="timezone">
                                            <option value="Europe/Luxembourg" {{ user.timezone == 'Europe/Luxembourg' ? 'selected' : '' }}>
                                                Europe/Luxembourg
                                            </option>
                                            <option value="Europe/Paris" {{ user.timezone == 'Europe/Paris' ? 'selected' : '' }}>
                                                Europe/Paris
                                            </option>
                                            <option value="Europe/Brussels" {{ user.timezone == 'Europe/Brussels' ? 'selected' : '' }}>
                                                Europe/Brussels
                                            </option>
                                            <option value="Europe/Berlin" {{ user.timezone == 'Europe/Berlin' ? 'selected' : '' }}>
                                                Europe/Berlin
                                            </option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-12">
                                        <label for="notes" class="form-label">{{ __('common.notes') }}</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="3">{{ user.notes }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Financial Tab (Conditional) -->
                    {% if user.id and (is_practitioner or financial_obligations or can_edit_financial) %}
                    <div class="tab-pane fade" id="financial" role="tabpanel" aria-labelledby="financial-tab" tabindex="0">
                        <!-- Unified Retrocession Settings and Monthly Amounts (for Kiné group members) -->
                        {% if is_practitioner %}
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-calculator me-2"></i>{{ __('users.retrocession_settings') }} & {{ __('users.monthly_retrocession_amounts') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Retrocession Settings Section -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="bi bi-gear me-2"></i>{{ __('users.retrocession_parameters') }}
                                    </h6>
                                    <div class="d-flex justify-content-end mb-3">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="toggleRetrocessionSettings()">
                                            <i class="bi bi-plus-circle me-1"></i>{{ __('users.add_retrocession_settings') }}
                                        </button>
                                    </div>
                                <!-- Current Settings Display -->
                                <div id="currentRetrocessionSettings" {% if not retrocession_settings %}style="display: none;"{% endif %}>
                                    {% if retrocession_settings %}
                                        <div class="alert alert-success">
                                            <h6 class="alert-heading">{{ __('users.current_active_settings') }}</h6>
                                            <p class="mb-1">
                                                <strong>{{ __('config.cns') }}:</strong> 
                                                {% if retrocession_settings.cns_type == 'percentage' %}
                                                    {{ retrocession_settings.cns_value }} %
                                                {% else %}
                                                    {{ retrocession_settings.cns_value }} {{ currency }}
                                                {% endif %}
                                            </p>
                                            <p class="mb-1">
                                                <strong>{{ __('config.patient') }}:</strong> 
                                                {% if retrocession_settings.patient_type == 'percentage' %}
                                                    {{ retrocession_settings.patient_value }} %
                                                {% else %}
                                                    {{ retrocession_settings.patient_value }} {{ currency }}
                                                {% endif %}
                                            </p>
                                            <p class="mb-1">
                                                <strong>{{ __('config.secretary_fees') }}:</strong> 
                                                {% if retrocession_settings.secretary_type == 'percentage' %}
                                                    {{ retrocession_settings.secretary_value }} %
                                                {% else %}
                                                    {{ retrocession_settings.secretary_value }} {{ currency }}
                                                {% endif %}
                                            </p>
                                            {% if retrocession_settings.ceiling_enabled %}
                                            <p class="mb-0">
                                                <strong>{{ __('config.ceiling') }}:</strong> {{ retrocession_settings.ceiling_amount }} {{ currency }}
                                            </p>
                                            {% endif %}
                                            <hr>
                                            <small class="text-muted">
                                                {{ __('users.valid_from') }}: {{ retrocession_settings.valid_from|date('d/m/Y') }}
                                                {% if retrocession_settings.valid_to %}
                                                    - {{ __('users.valid_to') }}: {{ retrocession_settings.valid_to|date('d/m/Y') }}
                                                {% endif %}
                                            </small>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle me-2"></i>
                                            {{ __('users.no_specific_retrocession_settings') }}
                                        </div>
                                    {% endif %}
                                    
                                    <!-- View History Button -->
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="viewRetrocessionHistory({{ user.id }})">
                                        <i class="bi bi-clock-history me-1"></i>{{ __('users.view_history') }}
                                    </button>
                                </div>
                                
                                <!-- Retrocession History Section (Hidden by default) -->
                                <div id="retrocessionHistory" style="display: none;" class="mt-4">
                                    <div class="card">
                                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="bi bi-clock-history me-2"></i>{{ __('users.retrocession_history') }}
                                            </h6>
                                            <button type="button" class="btn-close" onclick="hideRetrocessionHistory()"></button>
                                        </div>
                                        <div class="card-body">
                                            <div id="historyLoading" class="text-center py-3">
                                                <div class="spinner-border spinner-border-sm" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <span class="ms-2">{{ __('common.loading') }}...</span>
                                            </div>
                                            <div id="historyContent" style="display: none;">
                                                <!-- History content will be loaded here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- New Settings Form (Hidden by default) -->
                                <div id="newRetrocessionSettings" {% if retrocession_settings %}style="display: none;"{% endif %}>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">{{ __('users.valid_from') }} *</label>
                                            <input type="date" class="form-control" name="retrocession[valid_from]" 
                                                   min="{{ 'now'|date('Y-m-d') }}" value="{{ 'now'|date('Y-m-d') }}">
                                        </div>
                                        <div class="col-md-6">
                                            <label class="form-label">{{ __('users.valid_to') }}</label>
                                            <input type="date" class="form-control" name="retrocession[valid_to]">
                                            <small class="text-muted">{{ __('users.leave_empty_for_indefinite') }}</small>
                                        </div>
                                        
                                        <!-- CNS Settings -->
                                        <div class="col-12">
                                            <h6 class="text-primary">{{ __('config.cns_settings') }}</h6>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="retrocession[cns_type]" 
                                                       id="cns_percentage" value="percentage" checked onchange="toggleValueType('cns')">
                                                <label class="form-check-label" for="cns_percentage">{{ __('common.percentage') }}</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="retrocession[cns_type]" 
                                                       id="cns_fixed" value="fixed_amount" onchange="toggleValueType('cns')">
                                                <label class="form-check-label" for="cns_fixed">{{ __('common.fixed_amount') }}</label>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="retrocession[cns_value]" 
                                                       id="cns_value" step="0.01" min="0" value="{{ retrocession_settings.cns_value|default(20) }}">
                                                <span class="input-group-text" id="cns_suffix">%</span>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <label class="form-label">{{ __('users.custom_line_label') }}</label>
                                            <input type="text" class="form-control" name="retrocession[cns_label]" 
                                                   placeholder="RÉTROCESSION CNS" value="{{ retrocession_settings.cns_label|default('RÉTROCESSION CNS') }}">
                                            <small class="text-muted">{{ __('users.custom_label_help') }}</small>
                                        </div>
                                        
                                        <!-- Patient Settings -->
                                        <div class="col-12">
                                            <h6 class="text-primary">{{ __('config.patient_settings') }}</h6>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="retrocession[patient_type]" 
                                                       id="patient_percentage" value="percentage" checked onchange="toggleValueType('patient')">
                                                <label class="form-check-label" for="patient_percentage">{{ __('common.percentage') }}</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="retrocession[patient_type]" 
                                                       id="patient_fixed" value="fixed_amount" onchange="toggleValueType('patient')">
                                                <label class="form-check-label" for="patient_fixed">{{ __('common.fixed_amount') }}</label>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="retrocession[patient_value]" 
                                                       id="patient_value" step="0.01" min="0" value="{{ retrocession_settings.patient_value|default(20) }}">
                                                <span class="input-group-text" id="patient_suffix">%</span>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <label class="form-label">{{ __('users.custom_line_label') }}</label>
                                            <input type="text" class="form-control" name="retrocession[patient_label]" 
                                                   placeholder="RÉTROCESSION PATIENTS" value="{{ retrocession_settings.patient_label|default('RÉTROCESSION PATIENTS') }}">
                                            <small class="text-muted">{{ __('users.custom_label_help') }}</small>
                                        </div>
                                        
                                        <!-- Secretary Fees Settings -->
                                        <div class="col-12">
                                            <h6 class="text-primary">{{ __('config.secretary_fees_settings') }}</h6>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="retrocession[secretary_type]" 
                                                       id="secretary_percentage" value="percentage" checked onchange="toggleValueType('secretary')">
                                                <label class="form-check-label" for="secretary_percentage">{{ __('common.percentage') }}</label>
                                            </div>
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input" type="radio" name="retrocession[secretary_type]" 
                                                       id="secretary_fixed" value="fixed_amount" onchange="toggleValueType('secretary')">
                                                <label class="form-check-label" for="secretary_fixed">{{ __('common.fixed_amount') }}</label>
                                            </div>
                                        </div>
                                        <div class="col-md-8">
                                            <div class="input-group">
                                                <input type="number" class="form-control" name="retrocession[secretary_value]" 
                                                       id="secretary_value" step="0.01" min="0" value="{{ retrocession_settings.secretary_value|default(10) }}">
                                                <span class="input-group-text" id="secretary_suffix">%</span>
                                            </div>
                                        </div>
                                        <div class="col-12 mt-2">
                                            <label class="form-label">{{ __('users.custom_line_label') }}</label>
                                            <input type="text" class="form-control" name="retrocession[secretary_label]" 
                                                   placeholder="FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL" value="{{ retrocession_settings.secretary_label|default('FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL') }}">
                                            <small class="text-muted">{{ __('users.custom_label_help') }}</small>
                                        </div>
                                        
                                        <!-- Ceiling Settings -->
                                        <div class="col-12">
                                            <hr>
                                            <div class="form-check form-switch mb-3">
                                                <input type="checkbox" class="form-check-input" id="ceiling_enabled" 
                                                       name="retrocession[ceiling_enabled]" value="1" onchange="toggleCeiling()">
                                                <label class="form-check-label" for="ceiling_enabled">
                                                    {{ __('config.enable_invoice_ceiling') }}
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6" id="ceiling_amount_group" style="display: none;">
                                            <label class="form-label">{{ __('config.ceiling_amount') }} ({{ currency }})</label>
                                            <input type="number" class="form-control" name="retrocession[ceiling_amount]" 
                                                   step="0.01" min="0" value="5000.00">
                                        </div>
                                        
                                        <!-- Notes -->
                                        <div class="col-12">
                                            <label class="form-label">{{ __('common.notes') }}</label>
                                            <textarea class="form-control" name="retrocession[notes]" rows="2"></textarea>
                                        </div>
                                    </div>
                                </div>
                                </div>
                                
                                <!-- Divider between settings and monthly amounts -->
                                <hr class="my-4">
                                
                                <!-- Monthly Retrocession Amounts Section -->
                                <div class="mb-4">
                                    <h6 class="text-success mb-3">
                                        <i class="bi bi-calendar-month me-2"></i>{{ __('users.monthly_retrocession_amounts') }}
                                    </h6>
                                    
                                    <!-- Year selector and actions in a compact layout -->
                                    <div class="bg-light border rounded p-3 mb-3">
                                        <div class="row align-items-center">
                                            <div class="col-auto">
                                                <div class="d-flex align-items-center">
                                                    <span class="text-muted small me-2">{{ __('common.year') }}:</span>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeRetrocessionYear(-1)" title="{{ __('common.previous_year') }}">
                                                            <i class="bi bi-chevron-left"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary btn-sm px-4" disabled style="font-weight: 600;">
                                                            <span id="retrocessionYearDisplay">{{ "now"|date("Y") }}</span>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeRetrocessionYear(1)" title="{{ __('common.next_year') }}">
                                                            <i class="bi bi-chevron-right"></i>
                                                        </button>
                                                    </div>
                                                    <input type="hidden" id="retrocessionYear" name="retrocession_year" value="{{ "now"|date("Y") }}">
                                                </div>
                                            </div>
                                            <div class="col">
                                                <div class="d-flex justify-content-end gap-2 flex-wrap">
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleAllMonthsBtn" onclick="toggleAllMonths()">
                                                        <i class="bi bi-arrows-expand me-1"></i>{{ __('common.show_all') | default('Afficher tout') }}
                                                    </button>
                                                    
                                                    <!-- Month/Year selector for invoice generation -->
                                                    <div class="d-flex align-items-center gap-1">
                                                        <select id="retInvoiceMonth" class="form-select form-select-sm" style="width: auto;">
                                                            {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                                            {% set current_month = "now"|date("n") %}
                                                            {% set selected_month = current_month == 1 ? 12 : current_month - 1 %}
                                                            {% for i in 1..12 %}
                                                                <option value="{{ i }}" {% if i == selected_month %}selected{% endif %}>{{ months[i-1] }}</option>
                                                            {% endfor %}
                                                        </select>
                                                        <select id="retInvoiceYear" class="form-select form-select-sm" style="width: auto;">
                                                            {% set current_year = "now"|date("Y") %}
                                                            {% set selected_year = current_month == 1 ? current_year - 1 : current_year %}
                                                            {% for year in (current_year - 2)..(current_year + 1) %}
                                                                <option value="{{ year }}" {% if year == selected_year %}selected{% endif %}>{{ year }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                    
                                                    <button type="button" class="btn btn-sm btn-success" onclick="generateRetrocessionInvoice({{ user.id }})">
                                                        <i class="bi bi-file-earmark-plus me-1"></i>{{ __('users.generate_invoice') }}
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-primary" onclick="toggleMonthlyAmounts()">
                                                        <i class="bi bi-pencil-square me-1"></i>{{ __('common.edit') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div id="monthlyAmountsView">
                                    <!-- Current monthly amounts display -->
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('common.month') }}</th>
                                                    <th>{{ __('retrocession.cns_amount') }}</th>
                                                    <th>{{ __('retrocession.patient_amount') }}</th>
                                                    <th>{{ __('common.total') }}</th>
                                                    <th>{{ __('common.status') }}</th>
                                                    <th>{{ __('common.invoice') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                                {% set current_month = "now"|date("n")|number_format(0, '', '') %}
                                                {% set next_month = ((current_month % 12) + 1)|number_format(0, '', '') %}
                                                {% for i in 1..12 %}
                                                    {% set month_data = monthly_amounts[i] %}
                                                    {% set invoice_data = monthly_invoices[i] %}
                                                    {% set is_visible_month = (i == current_month or i == next_month) %}
                                                    <tr id="month-row-{{ i }}" class="{% if invoice_data %}table-success{% endif %} {% if not is_visible_month %}month-row-hidden d-none{% endif %}" data-month="{{ i }}">
                                                        <td>{{ months[i-1] }}</td>
                                                        <td class="text-end">{{ month_data.cns_amount|default(0)|number_format(2, ',', '.') }} €</td>
                                                        <td class="text-end">{{ month_data.patient_amount|default(0)|number_format(2, ',', '.') }} €</td>
                                                        <td class="text-end"><strong>{{ (month_data.cns_amount|default(0) + month_data.patient_amount|default(0))|number_format(2, ',', '.') }} €</strong></td>
                                                        <td class="text-center">
                                                            {% if month_data.is_active %}
                                                                <span class="badge bg-success">{{ __('common.active') }}</span>
                                                            {% else %}
                                                                <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="text-center invoice-status-{{ i }}">
                                                            {% if invoice_data %}
                                                                <a href="{{ base_url }}/invoices/{{ invoice_data.id }}" class="text-success" target="_blank" title="{{ __('common.view') }} {{ __('common.invoice') }}">
                                                                    <i class="bi bi-check-circle-fill me-1"></i>
                                                                    {{ invoice_data.invoice_number }}
                                                                </a>
                                                            {% else %}
                                                                -
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="alert alert-info mt-3">
                                        <i class="bi bi-info-circle me-2"></i>
                                        {{ __('users.monthly_amounts_help') }}
                                    </div>
                                </div>
                                
                                <!-- Edit monthly amounts form (hidden by default) -->
                                <div id="monthlyAmountsEdit" style="display: none;">
                                    <div class="row mb-3">
                                        <div class="col-12">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="applyToAllMonths()">
                                                <i class="bi bi-copy me-1"></i>{{ __('users.apply_to_all_months') }}
                                            </button>
                                        </div>
                                    </div>
                                    
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th width="20%">{{ __('common.month') }}</th>
                                                    <th width="25%">{{ __('retrocession.cns_amount') }} (€)</th>
                                                    <th width="25%">{{ __('retrocession.patient_amount') }} (€)</th>
                                                    <th width="20%">{{ __('common.active') }}</th>
                                                    <th width="10%">{{ __('common.actions') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                                {% for i in 1..12 %}
                                                    {% set month_data = monthly_amounts[i] %}
                                                    <tr>
                                                        <td>{{ months[i-1] }}</td>
                                                        <td>
                                                            <input type="number" class="form-control form-control-sm monthly-cns" 
                                                                   name="monthly_amounts[{{ i }}][cns_amount]" 
                                                                   step="0.01" min="0" 
                                                                   value="{{ month_data.cns_amount|default(0) }}"
                                                                   data-month="{{ i }}">
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control form-control-sm monthly-patient" 
                                                                   name="monthly_amounts[{{ i }}][patient_amount]" 
                                                                   step="0.01" min="0" 
                                                                   value="{{ month_data.patient_amount|default(0) }}"
                                                                   data-month="{{ i }}">
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="form-check">
                                                                <input type="checkbox" class="form-check-input monthly-active" 
                                                                       name="monthly_amounts[{{ i }}][is_active]" 
                                                                       id="month_active_{{ i }}" value="1"
                                                                       {% if month_data.is_active|default(true) %}checked{% endif %}>
                                                                <label class="form-check-label" for="month_active_{{ i }}"></label>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-link p-0" 
                                                                    onclick="copyFromPreviousMonth({{ i }})" 
                                                                    title="{{ __('users.copy_from_previous') }}">
                                                                <i class="bi bi-arrow-up-circle"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <label class="form-label">{{ __('common.notes') }}</label>
                                            <textarea class="form-control" name="monthly_amounts_notes" rows="2" 
                                                      placeholder="{{ __('users.monthly_amounts_notes_placeholder') }}"></textarea>
                                        </div>
                                    </div>
                                    
                                    <div class="row mt-3">
                                        <div class="col-12">
                                            <button type="button" class="btn btn-secondary" onclick="cancelMonthlyAmounts()">
                                                {{ __('common.cancel') }}
                                            </button>
                                            <button type="button" class="btn btn-primary" onclick="saveMonthlyAmounts()">
                                                <i class="bi bi-check-circle me-1"></i>{{ __('common.save') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <!-- Financial Obligations -->
                        {% if financial_obligations or can_edit_financial %}
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h5 class="mb-0">
                                    <i class="bi bi-cash-coin me-2"></i>{{ __('users.financial_obligations') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- Year selector and actions in a compact layout -->
                                <div class="bg-light border rounded p-3 mb-3">
                                    <div class="row align-items-center">
                                        <div class="col-auto">
                                            <div class="d-flex align-items-center">
                                                <span class="text-muted small me-2">{{ __('common.year') }}:</span>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeObligationsYear(-1)" title="{{ __('common.previous_year') }}">
                                                        <i class="bi bi-chevron-left"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm px-4" disabled style="font-weight: 600;">
                                                        <span id="obligationsYearDisplay">{{ "now"|date("Y") }}</span>
                                                    </button>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" onclick="changeObligationsYear(1)" title="{{ __('common.next_year') }}">
                                                        <i class="bi bi-chevron-right"></i>
                                                    </button>
                                                </div>
                                                <input type="hidden" id="obligationsYear" name="obligations_year" value="{{ "now"|date("Y") }}">
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="d-flex justify-content-end gap-2 flex-wrap">
                                                <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleAllObligationsBtn" onclick="toggleAllObligations()" data-expanded="false">
                                                    <i class="bi bi-arrows-expand me-1"></i>{{ __('common.show_all') | default('Afficher tout') }}
                                                </button>
                                                
                                                <!-- Month selector for Loyer invoice generation -->
                                                <div class="d-flex align-items-center gap-1">
                                                    <select id="loyInvoiceMonth" class="form-select form-select-sm" style="width: auto;">
                                                        {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                                        {% set current_month = "now"|date("n") %}
                                                        {% for i in 1..12 %}
                                                            <option value="{{ i }}" {% if i == current_month %}selected{% endif %}>{{ months[i-1] }}</option>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                                
                                                <button type="button" class="btn btn-sm btn-success" onclick="generateLoyerInvoiceWithMonth({{ user.id }})">
                                                    <i class="bi bi-file-earmark-plus me-1"></i>{{ __('users.generate_loyer_invoice') }}
                                                </button>
                                                {% if can_edit_financial %}
                                                <button type="button" class="btn btn-sm btn-primary" onclick="toggleFinancialObligations()">
                                                    <i class="bi bi-pencil me-1"></i>{{ __('common.edit') }}
                                                </button>
                                                {% else %}
                                                <span class="text-muted small">
                                                    <i class="bi bi-lock me-1"></i>Modification réservée aux administrateurs
                                                </span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- View Mode -->
                                <div id="financialObligationsView">
                                    {% if financial_obligations %}
                                    <div class="row g-3">
                                        <div class="col-md-3">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="text-muted mb-2">{{ __('users.rent_amount') }}</h6>
                                                    <h4 class="mb-0">{{ financial_obligations.rent_amount|number_format(2, ',', '.') }}€</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="text-muted mb-2">{{ __('users.charges_amount') }}</h6>
                                                    <h4 class="mb-0">{{ financial_obligations.charges_amount|number_format(2, ',', '.') }}€</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="text-muted mb-2">{{ __('users.total_tvac') }}</h6>
                                                    <h4 class="mb-0 text-primary">{{ financial_obligations.total_tvac|number_format(2, ',', '.') }}€</h4>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="card bg-light">
                                                <div class="card-body">
                                                    <h6 class="text-muted mb-2">{{ __('users.effective_date') }}</h6>
                                                    <h4 class="mb-0">{{ financial_obligations.effective_date|date('d/m/Y') }}</h4>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    {% if financial_obligations.secretary_tvac_17 > 0 or financial_obligations.secretary_htva > 0 or financial_obligations.tva_17 > 0 %}
                                    <hr>
                                    <h6 class="text-muted mb-3">{{ __('users.additional_charges') }}</h6>
                                    <div class="row g-3">
                                        {% if financial_obligations.secretary_tvac_17 > 0 %}
                                        <div class="col-md-4">
                                            <small class="text-muted d-block">{{ __('users.secretary_tvac_17') }}</small>
                                            <strong>{{ financial_obligations.secretary_tvac_17|number_format(2, ',', '.') }}€</strong>
                                        </div>
                                        {% endif %}
                                        {% if financial_obligations.secretary_htva > 0 %}
                                        <div class="col-md-4">
                                            <small class="text-muted d-block">{{ __('users.secretary_htva') }}</small>
                                            <strong>{{ financial_obligations.secretary_htva|number_format(2, ',', '.') }}€</strong>
                                        </div>
                                        {% endif %}
                                        {% if financial_obligations.tva_17 > 0 %}
                                        <div class="col-md-4">
                                            <small class="text-muted d-block">{{ __('users.tva_17') }}</small>
                                            <strong>{{ financial_obligations.tva_17|number_format(2, ',', '.') }}€</strong>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                    
                                    {% if financial_obligations.notes %}
                                    <hr>
                                    <h6 class="text-muted">{{ __('common.notes') }}</h6>
                                    <p class="mb-0">{{ financial_obligations.notes }}</p>
                                    {% endif %}
                                    {% else %}
                                    <div class="alert alert-info mb-0">
                                        <i class="bi bi-info-circle me-2"></i>
                                        {{ __('users.no_financial_obligations') }}
                                    </div>
                                    {% endif %}
                                    
                                    <!-- Monthly Loyer Invoice Status -->
                                    <hr class="my-4">
                                    <h6 class="text-muted mb-3">
                                        <i class="bi bi-calendar-check me-2"></i>{{ __('users.monthly_loyer_status') }}
                                    </h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('common.month') }}</th>
                                                    <th class="text-center">{{ __('common.total') }} TVAC</th>
                                                    <th class="text-center">{{ __('common.status') }}</th>
                                                    <th class="text-center">{{ __('common.invoice') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                                {% set loyer_year = obligationsYear|default("now"|date("Y")) %}
                                                {% set current_month = "now"|date("n")|number_format(0, '', '') %}
                                                {% set next_month = ((current_month % 12) + 1)|number_format(0, '', '') %}
                                                {% for i in 1..12 %}
                                                    {% set invoice_data = monthly_loyer_invoices[i] %}
                                                    {% set is_visible_month = (i == current_month or i == next_month) %}
                                                    <tr id="loyer-month-row-{{ i }}" class="{% if invoice_data %}table-success{% endif %} {% if not is_visible_month %}loyer-month-row-hidden d-none{% endif %}" data-month="{{ i }}">
                                                        <td>{{ months[i-1] }} {{ loyer_year }}</td>
                                                        <td class="text-center">
                                                            {% if financial_obligations %}
                                                                <strong>{{ financial_obligations.total_tvac|number_format(2, ',', '.') }}€</strong>
                                                            {% else %}
                                                                <span class="text-muted">-</span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="text-center">
                                                            {% if financial_obligations %}
                                                                <span class="badge bg-success">{{ __('common.active') }}</span>
                                                            {% else %}
                                                                <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="text-center loyer-invoice-status-{{ i }}">
                                                            {% if invoice_data %}
                                                                <a href="{{ base_url }}/invoices/{{ invoice_data.invoice_id }}" class="btn btn-sm btn-success" title="{{ __('common.view') }}">
                                                                    <i class="bi bi-check-circle"></i> {{ invoice_data.invoice_number }}
                                                                </a>
                                                            {% else %}
                                                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                        onclick="generateLoyerInvoice({{ user.id }}, {{ i }}, {{ loyer_year }})"
                                                                        {% if not financial_obligations %}disabled title="Aucune obligation financière configurée"{% endif %}>
                                                                    <i class="bi bi-file-earmark-plus"></i>
                                                                </button>
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <!-- Edit Mode (hidden by default) -->
                                {% if can_edit_financial %}
                                <div id="financialObligationsEdit" style="display: none;">
                                    <div class="row g-3">
                                            <div class="col-md-6">
                                                <label for="rent_amount" class="form-label">{{ __('users.rent_amount') }} ({{ __('users.loyer') }}) *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    <input type="number" class="form-control" id="rent_amount" name="rent_amount" 
                                                           step="0.01" min="0" required
                                                           value="{{ financial_obligations.rent_amount|default(0) }}">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="charges_amount" class="form-label">{{ __('users.charges_amount') }} *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    <input type="number" class="form-control" id="charges_amount" name="charges_amount" 
                                                           step="0.01" min="0" required
                                                           value="{{ financial_obligations.charges_amount|default(0) }}">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <label for="secretary_tvac_17" class="form-label">{{ __('users.secretary_tvac_17') }}</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    <input type="number" class="form-control" id="secretary_tvac_17" name="secretary_tvac_17" 
                                                           step="0.01" min="0"
                                                           value="{{ financial_obligations.secretary_tvac_17|default(0) }}">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <label for="secretary_htva" class="form-label">{{ __('users.secretary_htva') }}</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    <input type="number" class="form-control" id="secretary_htva" name="secretary_htva" 
                                                           step="0.01" min="0"
                                                           value="{{ financial_obligations.secretary_htva|default(0) }}">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-4">
                                                <label for="tva_17" class="form-label">{{ __('users.tva_17') }} (17%)</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    <input type="number" class="form-control" id="tva_17" name="tva_17" 
                                                           step="0.01" min="0"
                                                           value="{{ financial_obligations.tva_17|default(0) }}">
                                                </div>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label for="effective_date" class="form-label">{{ __('users.effective_date') }} *</label>
                                                <input type="date" class="form-control" id="effective_date" name="effective_date" 
                                                       required value="{{ 'now'|date('Y-m-d') }}">
                                                <small class="text-muted">{{ __('users.effective_date_hint') }}</small>
                                            </div>
                                            
                                            <div class="col-md-6">
                                                <label class="form-label">{{ __('users.total_tvac') }}</label>
                                                <div class="form-control-plaintext">
                                                    <h4 class="mb-0 text-primary"><span id="totalAmount">0,00</span>€</h4>
                                                </div>
                                            </div>
                                            
                                            <div class="col-12">
                                                <label for="notes" class="form-label">{{ __('common.notes') }}</label>
                                                <textarea class="form-control" id="notes" name="notes" rows="2">{{ financial_obligations.notes|default('') }}</textarea>
                                            </div>
                                            
                                            <div class="col-12">
                                                <hr>
                                                <button type="button" class="btn btn-secondary" onclick="cancelFinancialObligations()">
                                                    {{ __('common.cancel') }}
                                                </button>
                                                <button type="button" class="btn btn-primary" onclick="saveFinancialObligations()">
                                                    <i class="bi bi-save me-2"></i>{{ __('common.save') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Financial Obligations History -->
                        {% if obligations_history and obligations_history|length > 0 %}
                        <div class="card shadow-sm mb-4" id="obligationsHistorySection" style="display: none;">
                            <div class="card-header bg-secondary text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-clock-history me-2"></i>{{ __('users.financial_obligations_history') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('users.effective_date') }}</th>
                                                <th>{{ __('users.end_date') }}</th>
                                                <th class="text-end">{{ __('users.rent_amount') }}</th>
                                                <th class="text-end">{{ __('users.charges_amount') }}</th>
                                                <th class="text-end">{{ __('users.total_tvac') }}</th>
                                                <th>{{ __('common.notes') }}</th>
                                                <th>{{ __('common.created_by') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for obligation in obligations_history %}
                                            <tr {% if not obligation.end_date %}class="table-success"{% endif %}>
                                                <td>{{ obligation.effective_date|date('d/m/Y') }}</td>
                                                <td>
                                                    {% if obligation.end_date %}
                                                        {{ obligation.end_date|date('d/m/Y') }}
                                                    {% else %}
                                                        <span class="badge bg-success">{{ __('common.active') }}</span>
                                                    {% endif %}
                                                </td>
                                                <td class="text-end font-monospace">{{ obligation.rent_amount|number_format(2, ',', '.') }}€</td>
                                                <td class="text-end font-monospace">{{ obligation.charges_amount|number_format(2, ',', '.') }}€</td>
                                                <td class="text-end font-monospace"><strong>{{ obligation.total_tvac|number_format(2, ',', '.') }}€</strong></td>
                                                <td>{{ obligation.notes|default('-') }}</td>
                                                <td>
                                                    {% if obligation.created_at %}
                                                        <small class="text-muted">{{ obligation.created_at|date('d/m/Y H:i') }}</small>
                                                    {% else %}
                                                        -
                                                    {% endif %}
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- Courses Tab (Conditional for Coaches) -->
                    {% if user.id and is_coach %}
                    <div class="tab-pane fade" id="courses" role="tabpanel" aria-labelledby="courses-tab" tabindex="0">
                        <!-- Courses Management -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-success text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">
                                        <i class="bi bi-book me-2"></i>{{ __('courses.courses_management') }}
                                    </h5>
                                    <button type="button" class="btn btn-sm btn-light" onclick="addNewCourse()">
                                        <i class="bi bi-plus-circle me-1"></i>{{ __('courses.add_course') }}
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="courses-container">
                                    {% if courses is not empty %}
                                        <div class="table-responsive">
                                            <table class="table table-sm" id="courses-table">
                                                <thead>
                                                    <tr>
                                                        <th width="30"></th>
                                                        <th>{{ __('courses.name') }}</th>
                                                        <th>{{ __('courses.hourly_rate_ttc') }}</th>
                                                        <th>{{ __('courses.vat_rate') }}</th>
                                                        <th>{{ __('common.status') }}</th>
                                                        <th width="100">{{ __('common.actions') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="courses-list">
                                                    {% for course in courses %}
                                                    <tr data-course-id="{{ course.id }}" class="course-row">
                                                        <td class="text-center">
                                                            <i class="bi bi-grip-vertical drag-handle" style="cursor: move;"></i>
                                                        </td>
                                                        <td>{{ course.course_name }}</td>
                                                        <td>€ {{ course.hourly_rate|number_format(2, ',', '.') }}</td>
                                                        <td>{{ course.vat_rate }}%</td>
                                                        <td>
                                                            {% if course.is_active %}
                                                                <span class="badge bg-success">{{ __('common.active') }}</span>
                                                            {% else %}
                                                                <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                                    onclick="editCourse({{ course.id }}, '{{ course.course_name|e('js') }}', {{ course.hourly_rate }}, {{ course.vat_rate }}, {{ course.is_active ? 'true' : 'false' }})">
                                                                <i class="bi bi-pencil"></i>
                                                            </button>
                                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="deleteCourse({{ course.id }})">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    {% else %}
                                        <div class="alert alert-info mb-0" id="no-courses-alert">
                                            <i class="bi bi-info-circle me-2"></i>
                                            {{ __('courses.no_courses_yet') }}
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- Course Form (Hidden by default) -->
                                <div id="course-form" style="display: none;" class="mt-3">
                                    <hr>
                                    <h6 id="course-form-title">{{ __('courses.add_new_course') }}</h6>
                                    <form id="course-edit-form">
                                        <input type="hidden" id="course-id">
                                        <div class="row g-3">
                                            <div class="col-md-6">
                                                <label class="form-label">{{ __('courses.course_name') }} *</label>
                                                <input type="text" class="form-control" id="course-name" required>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">{{ __('courses.hourly_rate_ttc') }} *</label>
                                                <div class="input-group">
                                                    <span class="input-group-text">€</span>
                                                    <input type="number" class="form-control" id="course-rate" step="0.01" min="0" required>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <label class="form-label">{{ __('courses.vat_rate') }} (%)</label>
                                                <input type="number" class="form-control" id="course-vat" value="16" step="0.01" min="0" max="100">
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="course-active" checked>
                                                    <label class="form-check-label" for="course-active">
                                                        {{ __('courses.is_active') }}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <button type="button" class="btn btn-secondary btn-sm" onclick="cancelCourseForm()">
                                                    {{ __('common.cancel') }}
                                                </button>
                                                <button type="submit" class="btn btn-success btn-sm" onclick="submitCourseForm(event)">
                                                    <i class="bi bi-check-circle me-1"></i>
                                                    <span id="course-save-btn-text">{{ __('courses.add_course') }}</span>
                                                </button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Monthly Course Counts -->
                        <div class="card shadow-sm mb-4">
                            <div class="card-header bg-info text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-calendar-month me-2"></i>{{ __('courses.monthly_course_counts') }}
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-end mb-3">
                                    <button type="button" class="btn btn-sm btn-info me-2" id="toggleAllCourseMonthsBtn" onclick="toggleAllCourseMonths()">
                                        <i class="bi bi-arrows-expand me-1"></i>{{ __('common.show_all') }}
                                    </button>
                                    
                                    <!-- Month/Year selector for Course invoice generation -->
                                    <div class="d-flex align-items-center gap-1 me-2">
                                        <select id="locInvoiceMonth" class="form-select form-select-sm" style="width: auto;">
                                            {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                            {% set current_month = "now"|date("n") %}
                                            {% for i in 1..12 %}
                                                <option value="{{ i }}" {% if i == current_month %}selected{% endif %}>{{ months[i-1] }}</option>
                                            {% endfor %}
                                        </select>
                                        <select id="locInvoiceYear" class="form-select form-select-sm" style="width: auto;">
                                            {% set current_year = "now"|date("Y") %}
                                            {% for year in (current_year - 2)..(current_year + 1) %}
                                                <option value="{{ year }}" {% if year == current_year %}selected{% endif %}>{{ year }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <button type="button" class="btn btn-sm btn-success me-2" onclick="generateCourseInvoice({{ user.id }})">
                                        <i class="bi bi-file-earmark-plus me-1"></i>{{ __('courses.generate_invoice') }}
                                    </button>
                                    <button type="button" class="btn btn-sm btn-light" onclick="toggleMonthlyCourses()">
                                        <i class="bi bi-pencil-square me-1"></i>{{ __('common.edit') }}
                                    </button>
                                </div>
                                
                                <div id="monthlyCoursesView">
                                    {% if courses is not empty %}
                                    <div class="table-responsive">
                                        <table class="table table-sm table-bordered">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('common.month') }}</th>
                                                    {% for course in courses %}
                                                        {% if course.is_active %}
                                                        <th class="text-center" title="{{ course.course_name }}">
                                                            {{ course.course_name|slice(0, 15) }}{% if course.course_name|length > 15 %}...{% endif %}
                                                        </th>
                                                        {% endif %}
                                                    {% endfor %}
                                                    <th class="text-center">{{ __('common.total') }}</th>
                                                    <th class="text-center">{{ __('common.invoice') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% set months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'] %}
                                                {% set current_month = "now"|date("n")|number_format(0, '', '') %}
                                                {% set next_month = ((current_month % 12) + 1)|number_format(0, '', '') %}
                                                {% set current_year = "now"|date("Y") %}
                                                {% for i in 1..12 %}
                                                    {% set is_visible_month = (i == current_month or i == next_month) %}
                                                    <tr id="course-month-row-{{ i }}" class="{% if not is_visible_month %}course-month-hidden d-none{% endif %}" data-month="{{ i }}">
                                                        <td>{{ months[i-1] }} {{ current_year }}</td>
                                                        {% set month_total = 0 %}
                                                        {% for course in courses %}
                                                            {% if course.is_active %}
                                                            {% set count_data = monthly_course_counts[i][course.id] %}
                                                            <td class="text-center">
                                                                {{ count_data.course_count|default(0) }}
                                                                {% if count_data.course_count > 0 %}
                                                                    <br><small class="text-muted">{{ (count_data.course_count * course.hourly_rate)|number_format(2, ',', '.') }}€</small>
                                                                {% endif %}
                                                            </td>
                                                            {% set month_total = month_total + (count_data.course_count|default(0) * course.hourly_rate) %}
                                                            {% endif %}
                                                        {% endfor %}
                                                        <td class="text-center"><strong>{{ month_total|number_format(2, ',', '.') }}€</strong></td>
                                                        <td class="text-center course-invoice-status-{{ i }}">
                                                            {% set invoice_data = monthly_course_invoices[i] %}
                                                            {% if invoice_data %}
                                                                <a href="{{ base_url }}/invoices/{{ invoice_data.id }}" class="text-success" target="_blank">
                                                                    <i class="bi bi-check-circle-fill me-1"></i>
                                                                    {{ invoice_data.invoice_number }}
                                                                </a>
                                                            {% else %}
                                                                -
                                                            {% endif %}
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    {% else %}
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle me-2"></i>
                                        {{ __('courses.please_add_courses_first') }}
                                    </div>
                                    {% endif %}
                                </div>
                                
                                <div id="monthlyCoursesEdit" style="display: none;">
                                    {% if courses is not empty %}
                                    <form id="monthlyCoursesForm">
                                        <div class="table-responsive">
                                            <table class="table table-sm table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th>{{ __('common.month') }}</th>
                                                        {% for course in courses %}
                                                            {% if course.is_active %}
                                                            <th class="text-center" title="{{ course.course_name }} - {{ course.hourly_rate }}€/h">
                                                                {{ course.course_name|slice(0, 15) }}{% if course.course_name|length > 15 %}...{% endif %}
                                                                <br><small class="text-muted">{{ course.hourly_rate }}€/h</small>
                                                            </th>
                                                            {% endif %}
                                                        {% endfor %}
                                                        <th class="text-center">{{ __('common.total') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for i in 1..12 %}
                                                    <tr>
                                                        <td>{{ months[i-1] }} {{ current_year }}</td>
                                                        {% set month_total = 0 %}
                                                        {% for course in courses %}
                                                            {% if course.is_active %}
                                                            {% set count_data = monthly_course_counts[i][course.id] %}
                                                            <td class="text-center">
                                                                <input type="number" 
                                                                       class="form-control form-control-sm text-center course-count-input" 
                                                                       name="course_counts[{{ i }}][{{ course.id }}]" 
                                                                       value="{{ count_data.course_count|default(0) }}"
                                                                       min="0"
                                                                       data-month="{{ i }}"
                                                                       data-course-id="{{ course.id }}"
                                                                       data-rate="{{ course.hourly_rate }}"
                                                                       onchange="updateCourseMonthTotal({{ i }})">
                                                            </td>
                                                            {% endif %}
                                                        {% endfor %}
                                                        <td class="text-center">
                                                            <strong id="course-month-total-{{ i }}">0,00€</strong>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="mt-3">
                                            <button type="button" class="btn btn-secondary" onclick="cancelMonthlyCourses()">
                                                {{ __('common.cancel') }}
                                            </button>
                                            <button type="button" class="btn btn-success" onclick="saveMonthlyCourses()">
                                                <i class="bi bi-save me-2"></i>{{ __('common.save') }}
                                            </button>
                                        </div>
                                    </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Form (hidden) -->
{% if user.id %}
<form id="deleteForm" method="POST" action="{{ base_url }}/users/{{ user.id }}" style="display: none;">
    <input type="hidden" name="_method" value="DELETE">
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
</form>
{% endif %}

<script>
// Tab persistence functionality
document.addEventListener('DOMContentLoaded', function() {
    console.log('=== Page Load Debug Info ===');
    console.log('Current user viewing user ID: {{ user.id }}');
    console.log('Can edit financial: {{ can_edit_financial ? "true" : "false" }}');
    console.log('Is Coach: {{ is_coach ? "true" : "false" }}');
    console.log('Is Practitioner: {{ is_practitioner ? "true" : "false" }}');
    console.log('Client ID: {{ user.client_id }}');
    
    // Debug courses data
    console.log('=== Courses Data ===');
    {% if courses is defined %}
        console.log('Courses variable is defined');
        console.log('Number of courses: {{ courses|length }}');
        console.log('Courses data:', {{ courses|json_encode|raw }});
    {% else %}
        console.log('Courses variable is NOT defined');
    {% endif %}
    
    // Debug course tab visibility
    const coursesTab = document.getElementById('courses-tab');
    console.log('Courses tab element exists:', coursesTab !== null);
    if (coursesTab) {
        console.log('Courses tab display style:', window.getComputedStyle(coursesTab).display);
    }
    
    // Initialize edit mode flag
    isEditingFinancialObligations = false;
    
    // Get active tab from URL hash, URL parameter, or session
    let activeTab = 'financial';
    
    // Check URL hash first (e.g., #contact)
    if (window.location.hash) {
        activeTab = window.location.hash.substring(1);
    } else {
        // Check URL parameter (e.g., ?tab=contact)
        const urlParams = new URLSearchParams(window.location.search);
        const tabParam = urlParams.get('tab');
        if (tabParam) {
            activeTab = tabParam;
        } else {
            // Check if there's a flash message with tab info
            const flashTab = '{{ session.flash.active_tab | default('') }}';
            if (flashTab) {
                activeTab = flashTab;
            }
        }
    }
    
    // Activate the tab if it exists
    const tabButton = document.querySelector(`#${activeTab}-tab`);
    console.log(`Looking for tab button: #${activeTab}-tab`, tabButton !== null);
    
    if (tabButton) {
        const tab = new bootstrap.Tab(tabButton);
        tab.show();
        document.getElementById('active_tab').value = activeTab;
        
        // Debug: Check if we're on courses tab after activation
        if (activeTab === 'courses') {
            console.log('=== Courses Tab Activated ===');
            setTimeout(() => {
                const coursesContainer = document.getElementById('courses-container');
                console.log('Courses container exists:', coursesContainer !== null);
                if (coursesContainer) {
                    console.log('Courses container HTML preview:', coursesContainer.innerHTML.substring(0, 200) + '...');
                    const courseRows = coursesContainer.querySelectorAll('.course-row');
                    console.log('Number of course rows found:', courseRows.length);
                }
                const noCoursesAlert = document.getElementById('no-courses-alert');
                console.log('No courses alert visible:', noCoursesAlert !== null && window.getComputedStyle(noCoursesAlert).display !== 'none');
            }, 100); // Small delay to ensure tab is fully shown
        }
    } else if (activeTab === 'financial') {
        // If financial tab doesn't exist, fall back to personal
        activeTab = 'personal';
        const personalTab = document.querySelector('#personal-tab');
        if (personalTab) {
            const tab = new bootstrap.Tab(personalTab);
            tab.show();
            document.getElementById('active_tab').value = activeTab;
        }
    }
    
    // Track tab changes
    const tabButtons = document.querySelectorAll('#userFormTabs button[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const tabId = e.target.getAttribute('aria-controls');
            document.getElementById('active_tab').value = tabId;
            // Update URL hash without scrolling
            history.replaceState(null, null, '#' + tabId);
        });
    });
});

// Avatar preview
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('avatarPreview');
            if (preview.tagName === 'IMG') {
                preview.src = e.target.result;
            } else {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'rounded-circle';
                img.style.width = '100px';
                img.style.height = '100px';
                img.id = 'avatarPreview';
                preview.parentNode.replaceChild(img, preview);
            }
        };
        reader.readAsDataURL(file);
    }
});

// Password toggle
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('bi-eye');
        button.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('bi-eye-slash');
        button.classList.add('bi-eye');
    }
}

function confirmDelete() {
    if (confirm('{{ __("users.delete_confirm") }}')) {
        document.getElementById('deleteForm').submit();
    }
}

// Retrocession Settings Functions
function toggleRetrocessionSettings() {
    const currentDiv = document.getElementById('currentRetrocessionSettings');
    const newDiv = document.getElementById('newRetrocessionSettings');
    
    if (newDiv.style.display === 'none') {
        // Populate form with current values if they exist
        {% if retrocession_settings %}
        // CNS settings
        document.querySelector('input[name="retrocession[cns_type]"][value="{{ retrocession_settings.cns_type }}"]').checked = true;
        document.getElementById('cns_value').value = {{ retrocession_settings.cns_value }};
        document.querySelector('input[name="retrocession[cns_label]"]').value = '{{ retrocession_settings.cns_label|e('js') }}';
        toggleValueType('cns');
        
        // Patient settings
        document.querySelector('input[name="retrocession[patient_type]"][value="{{ retrocession_settings.patient_type }}"]').checked = true;
        document.getElementById('patient_value').value = {{ retrocession_settings.patient_value }};
        document.querySelector('input[name="retrocession[patient_label]"]').value = '{{ retrocession_settings.patient_label|e('js') }}';
        toggleValueType('patient');
        
        // Secretary settings
        document.querySelector('input[name="retrocession[secretary_type]"][value="{{ retrocession_settings.secretary_type }}"]').checked = true;
        document.getElementById('secretary_value').value = {{ retrocession_settings.secretary_value }};
        document.querySelector('input[name="retrocession[secretary_label]"]').value = '{{ retrocession_settings.secretary_label|e('js') }}';
        toggleValueType('secretary');
        {% endif %}
        
        currentDiv.style.display = 'none';
        newDiv.style.display = 'block';
    } else {
        currentDiv.style.display = 'block';
        newDiv.style.display = 'none';
    }
}

function cancelRetrocessionSettings() {
    const currentDiv = document.getElementById('currentRetrocessionSettings');
    const newDiv = document.getElementById('newRetrocessionSettings');
    
    currentDiv.style.display = 'block';
    newDiv.style.display = 'none';
    
    // Reset form
    document.querySelectorAll('#newRetrocessionSettings input').forEach(input => {
        if (input.type === 'radio' && input.value === 'percentage') {
            input.checked = true;
        } else if (input.type === 'number') {
            input.value = input.defaultValue || '';
        }
    });
}

function validateRetrocessionSettings() {
    // Get values
    const cnsValue = parseFloat(document.getElementById('cns_value').value) || 0;
    const patientValue = parseFloat(document.getElementById('patient_value').value) || 0;
    const secretaryValue = parseFloat(document.getElementById('secretary_value').value) || 0;
    
    const cnsType = document.querySelector('input[name="retrocession[cns_type]"]:checked').value;
    const patientType = document.querySelector('input[name="retrocession[patient_type]"]:checked').value;
    const secretaryType = document.querySelector('input[name="retrocession[secretary_type]"]:checked').value;
    
    // Check if all are percentages
    if (cnsType === 'percentage' && patientType === 'percentage' && secretaryType === 'percentage') {
        const total = cnsValue + patientValue + secretaryValue;
        if (total > 100) {
            if (!confirm('{{ __("users.total_percentage_exceeds_100") }}. {{ __("users.continue_anyway") }}?')) {
                return;
            }
        }
    }
    
    // Gather all retrocession data
    const retrocessionData = {
        retrocession: {
            valid_from: document.querySelector('input[name="retrocession[valid_from]"]').value,
            valid_to: document.querySelector('input[name="retrocession[valid_to]"]').value || null,
            cns_type: cnsType,
            cns_value: cnsValue,
            cns_label: document.querySelector('input[name="retrocession[cns_label]"]').value,
            patient_type: patientType,
            patient_value: patientValue,
            patient_label: document.querySelector('input[name="retrocession[patient_label]"]').value,
            secretary_type: secretaryType,
            secretary_value: secretaryValue,
            secretary_label: document.querySelector('input[name="retrocession[secretary_label]"]').value,
            ceiling_enabled: document.getElementById('ceiling_enabled').checked ? 1 : 0,
            ceiling_amount: document.querySelector('input[name="retrocession[ceiling_amount]"]').value || 0,
            notes: document.querySelector('textarea[name="retrocession[notes]"]').value || ''
        }
    };
    
    // Make AJAX request to save retrocession settings
    fetch(`{{ base_url }}/users/{{ user.id }}/retrocession-settings`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': '{{ csrf_token }}'
        },
        body: JSON.stringify(retrocessionData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert('{{ __("common.saved_successfully") }}');
            
            // Reload the page to show updated settings and preserve tab
            const activeTab = document.getElementById('active_tab').value || 'financial';
            window.location.href = window.location.pathname + '#' + activeTab;
            window.location.reload();
        } else {
            alert(data.message || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error saving retrocession settings:', error);
        alert('{{ __("common.error_occurred") }}');
    });
}

function toggleValueType(type) {
    const radioChecked = document.querySelector(`input[name="retrocession[${type}_type]"]:checked`).value;
    const suffix = document.getElementById(`${type}_suffix`);
    
    if (radioChecked === 'percentage') {
        suffix.textContent = '%';
    } else {
        suffix.textContent = '€';
    }
}

function toggleCeiling() {
    const enabled = document.getElementById('ceiling_enabled').checked;
    const amountGroup = document.getElementById('ceiling_amount_group');
    
    if (enabled) {
        amountGroup.style.display = 'block';
    } else {
        amountGroup.style.display = 'none';
    }
}

function viewRetrocessionHistory(userId) {
    const historyDiv = document.getElementById('retrocessionHistory');
    const loadingDiv = document.getElementById('historyLoading');
    const contentDiv = document.getElementById('historyContent');
    
    // Toggle visibility
    if (historyDiv.style.display === 'none') {
        historyDiv.style.display = 'block';
        loadingDiv.style.display = 'block';
        contentDiv.style.display = 'none';
        
        // Fetch history data
        fetch(`{{ base_url }}/users/${userId}/retrocession-history`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.history) {
                displayRetrocessionHistory(data.history);
            } else {
                contentDiv.innerHTML = '<div class="alert alert-info mb-0"><i class="bi bi-info-circle me-2"></i>{{ __("users.no_retrocession_history") }}</div>';
            }
            loadingDiv.style.display = 'none';
            contentDiv.style.display = 'block';
        })
        .catch(error => {
            console.error('Error fetching history:', error);
            contentDiv.innerHTML = '<div class="alert alert-danger mb-0"><i class="bi bi-exclamation-triangle me-2"></i>{{ __("common.error_occurred") }}</div>';
            loadingDiv.style.display = 'none';
            contentDiv.style.display = 'block';
        });
    } else {
        historyDiv.style.display = 'none';
    }
}

function hideRetrocessionHistory() {
    document.getElementById('retrocessionHistory').style.display = 'none';
}

function displayRetrocessionHistory(history) {
    const contentDiv = document.getElementById('historyContent');
    
    if (history.length === 0) {
        contentDiv.innerHTML = '<div class="alert alert-info mb-0"><i class="bi bi-info-circle me-2"></i>{{ __("users.no_retrocession_history") }}</div>';
        return;
    }
    
    let html = `
        <div class="table-responsive">
            <table class="table table-sm table-striped">
                <thead>
                    <tr>
                        <th>{{ __('users.valid_from') }}</th>
                        <th>{{ __('users.valid_to') }}</th>
                        <th>{{ __('config.cns') }}</th>
                        <th>{{ __('config.patient') }}</th>
                        <th>{{ __('config.secretary_fees') }}</th>
                        <th>{{ __('common.status') }}</th>
                        <th>{{ __('common.created_by') }}</th>
                        <th>{{ __('common.created_at') }}</th>
                    </tr>
                </thead>
                <tbody>
    `;
    
    // Sort history: active records first, then by date (newest first)
    history.sort((a, b) => {
        // First sort by active status (active = 1 on top)
        const aActive = a.is_active === 1 || a.is_active === '1' ? 1 : 0;
        const bActive = b.is_active === 1 || b.is_active === '1' ? 1 : 0;
        
        if (aActive !== bActive) {
            return bActive - aActive; // Active records first
        }
        
        // Then sort by date (newest first)
        return new Date(b.valid_from) - new Date(a.valid_from);
    });
    
    history.forEach((record, index) => {
        const isActive = record.is_active === 1 || record.is_active === '1';
        const rowClass = isActive ? 'table-success' : '';
        
        // Format values
        const cnsDisplay = record.cns_type === 'percentage' 
            ? `${parseFloat(record.cns_value).toFixed(2)}%` 
            : `${parseFloat(record.cns_value).toFixed(2)}€`;
            
        const patientDisplay = record.patient_type === 'percentage' 
            ? `${parseFloat(record.patient_value).toFixed(2)}%` 
            : `${parseFloat(record.patient_value).toFixed(2)}€`;
            
        const secretaryDisplay = record.secretary_type === 'percentage' 
            ? `${parseFloat(record.secretary_value).toFixed(2)}%` 
            : `${parseFloat(record.secretary_value).toFixed(2)}€`;
        
        // Format dates
        const validFrom = new Date(record.valid_from).toLocaleDateString('fr-FR');
        const validTo = record.valid_to ? new Date(record.valid_to).toLocaleDateString('fr-FR') : '{{ __("common.indefinite") }}';
        const createdAt = new Date(record.created_at).toLocaleDateString('fr-FR');
        
        // Check for invalid date range
        const invalidDateRange = record.valid_to && new Date(record.valid_to) < new Date(record.valid_from);
        
        html += `
            <tr class="${rowClass}">
                <td>${validFrom}</td>
                <td class="${invalidDateRange ? 'text-danger' : ''}">${validTo}</td>
                <td>${cnsDisplay}</td>
                <td>${patientDisplay}</td>
                <td>${secretaryDisplay}</td>
                <td>
                    ${isActive 
                        ? '<span class="badge bg-success">{{ __("common.active") }}</span>' 
                        : '<span class="badge bg-secondary">{{ __("common.inactive") }}</span>'}
                </td>
                <td>${record.created_by_username || '-'}</td>
                <td>${createdAt}</td>
            </tr>
        `;
    });
    
    html += `
                </tbody>
            </table>
        </div>
        <div class="alert alert-info mt-3 mb-0">
            <small><i class="bi bi-info-circle me-1"></i>{{ __('users.retrocession_history_help') }}</small>
        </div>
    `;
    
    contentDiv.innerHTML = html;
}

// Monthly Amounts Functions
function toggleMonthlyAmounts() {
    const viewDiv = document.getElementById('monthlyAmountsView');
    const editDiv = document.getElementById('monthlyAmountsEdit');
    
    if (editDiv.style.display === 'none') {
        viewDiv.style.display = 'none';
        editDiv.style.display = 'block';
    } else {
        viewDiv.style.display = 'block';
        editDiv.style.display = 'none';
    }
}

function cancelMonthlyAmounts() {
    toggleMonthlyAmounts();
}

// Toggle all months visibility
function toggleAllMonths() {
    const hiddenRows = document.querySelectorAll('.month-row-hidden');
    const btn = document.getElementById('toggleAllMonthsBtn');
    const isExpanded = btn.getAttribute('data-expanded') === 'true';
    
    hiddenRows.forEach(row => {
        if (isExpanded) {
            row.classList.add('d-none');
        } else {
            row.classList.remove('d-none');
        }
    });
    
    if (isExpanded) {
        btn.innerHTML = '<i class="bi bi-arrows-expand me-1"></i>{{ __('common.show_all') | default('Afficher tout') }}';
        btn.setAttribute('data-expanded', 'false');
    } else {
        btn.innerHTML = '<i class="bi bi-arrows-collapse me-1"></i>{{ __('common.show_less') | default('Réduire') }}';
        btn.setAttribute('data-expanded', 'true');
    }
}

function saveMonthlyAmounts() {
    const form = document.getElementById('userEditForm');
    const formData = new FormData(form);
    
    // Add action to save monthly amounts
    formData.append('action', 'save_monthly_amounts');
    
    // Add the current year
    const year = document.getElementById('retrocessionYear').value;
    formData.append('year', year);
    
    fetch(`{{ base_url }}/users/{{ user.id }}/monthly-amounts`, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('{{ __("common.saved_successfully") }}');
            loadRetrocessionYear(year);
            toggleMonthlyAmounts();
        } else {
            alert(data.message || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
}

// Year navigation functions
let currentRetrocessionYear = parseInt('{{ "now"|date("Y") }}');

function changeRetrocessionYear(direction) {
    currentRetrocessionYear += direction;
    
    // Update display and hidden input
    const yearDisplay = document.getElementById('retrocessionYearDisplay');
    yearDisplay.textContent = currentRetrocessionYear;
    document.getElementById('retrocessionYear').value = currentRetrocessionYear;
    
    // Add animation effect
    yearDisplay.style.opacity = '0.5';
    setTimeout(() => {
        yearDisplay.style.opacity = '1';
    }, 100);
    
    // Load data for the new year
    loadRetrocessionYear(currentRetrocessionYear);
}

function loadRetrocessionYear(year) {
    // Show loading indicator
    const viewDiv = document.getElementById('monthlyAmountsView');
    const editDiv = document.getElementById('monthlyAmountsEdit');
    
    viewDiv.innerHTML = '<div class="text-center p-3"><i class="bi bi-hourglass-split"></i> Loading...</div>';
    
    // Fetch data for the selected year
    fetch(`{{ base_url }}/users/{{ user.id }}/monthly-amounts?year=${year}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateMonthlyAmountsDisplay(data.monthly_amounts, data.monthly_invoices, year);
            updateMonthlyAmountsForm(data.monthly_amounts);
        } else {
            viewDiv.innerHTML = '<div class="alert alert-danger">Error loading data</div>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        viewDiv.innerHTML = '<div class="alert alert-danger">Error loading data</div>';
    });
}

function updateMonthlyAmountsDisplay(monthlyAmounts, monthlyInvoices, year) {
    const months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const currentMonth = new Date().getMonth() + 1;
    const currentYear = new Date().getFullYear();
    
    let html = `
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead>
                    <tr>
                        <th>{{ __('common.month') }}</th>
                        <th>{{ __('retrocession.cns_amount') }}</th>
                        <th>{{ __('retrocession.patient_amount') }}</th>
                        <th>{{ __('common.total') }}</th>
                        <th>{{ __('common.status') }}</th>
                        <th>{{ __('common.invoice') }}</th>
                    </tr>
                </thead>
                <tbody>`;
    
    for (let i = 1; i <= 12; i++) {
        const monthData = monthlyAmounts[i] || {};
        const invoiceData = monthlyInvoices[i] || null;
        const isVisibleMonth = (year === currentYear && (i === currentMonth || i === (currentMonth % 12) + 1));
        const cnsAmount = parseFloat(monthData.cns_amount || 0);
        const patientAmount = parseFloat(monthData.patient_amount || 0);
        const total = cnsAmount + patientAmount;
        
        html += `
            <tr id="month-row-${i}" class="${invoiceData ? 'table-success' : ''} ${!isVisibleMonth ? 'month-row-hidden d-none' : ''}" data-month="${i}">
                <td>${months[i-1]}</td>
                <td class="text-end">${cnsAmount.toFixed(2).replace('.', ',')} €</td>
                <td class="text-end">${patientAmount.toFixed(2).replace('.', ',')} €</td>
                <td class="text-end"><strong>${total.toFixed(2).replace('.', ',')} €</strong></td>
                <td class="text-center">
                    ${monthData.is_active ? 
                        '<span class="badge bg-success">{{ __("common.active") }}</span>' : 
                        '<span class="badge bg-secondary">{{ __("common.inactive") }}</span>'}
                </td>
                <td class="text-center invoice-status-${i}">
                    ${invoiceData ? 
                        `<a href="{{ base_url }}/invoices/${invoiceData.id}" class="text-success" target="_blank">
                            <i class="bi bi-check-circle-fill me-1"></i>
                            ${invoiceData.invoice_number}
                        </a>` : 
                        '-'}
                </td>
            </tr>`;
    }
    
    html += `
                </tbody>
            </table>
        </div>
        <div class="alert alert-info mt-3">
            <i class="bi bi-info-circle me-2"></i>
            {{ __('users.monthly_amounts_help') }}
        </div>`;
    
    document.getElementById('monthlyAmountsView').innerHTML = html;
}

function updateMonthlyAmountsForm(monthlyAmounts) {
    // Update form inputs with data for selected year
    for (let i = 1; i <= 12; i++) {
        const monthData = monthlyAmounts[i] || {};
        
        const cnsInput = document.querySelector(`input[name="monthly_amounts[${i}][cns_amount]"]`);
        const patientInput = document.querySelector(`input[name="monthly_amounts[${i}][patient_amount]"]`);
        const activeCheckbox = document.querySelector(`input[name="monthly_amounts[${i}][is_active]"]`);
        
        if (cnsInput) cnsInput.value = monthData.cns_amount || 0;
        if (patientInput) patientInput.value = monthData.patient_amount || 0;
        if (activeCheckbox) activeCheckbox.checked = monthData.is_active !== false;
    }
}

function applyToAllMonths() {
    if (!confirm('{{ __("users.apply_first_month_to_all_confirm") }}')) {
        return;
    }
    
    // Get January values
    const cnsAmount = document.querySelector('input[name="monthly_amounts[1][cns_amount]"]').value;
    const patientAmount = document.querySelector('input[name="monthly_amounts[1][patient_amount]"]').value;
    const isActive = document.querySelector('input[name="monthly_amounts[1][is_active]"]').checked;
    
    // Apply to all other months
    for (let i = 2; i <= 12; i++) {
        document.querySelector(`input[name="monthly_amounts[${i}][cns_amount]"]`).value = cnsAmount;
        document.querySelector(`input[name="monthly_amounts[${i}][patient_amount]"]`).value = patientAmount;
        document.querySelector(`input[name="monthly_amounts[${i}][is_active]"]`).checked = isActive;
    }
}

function copyFromPreviousMonth(month) {
    if (month === 1) return;
    
    const prevMonth = month - 1;
    
    // Get previous month values
    const cnsAmount = document.querySelector(`input[name="monthly_amounts[${prevMonth}][cns_amount]"]`).value;
    const patientAmount = document.querySelector(`input[name="monthly_amounts[${prevMonth}][patient_amount]"]`).value;
    const isActive = document.querySelector(`input[name="monthly_amounts[${prevMonth}][is_active]"]`).checked;
    
    // Apply to current month
    document.querySelector(`input[name="monthly_amounts[${month}][cns_amount]"]`).value = cnsAmount;
    document.querySelector(`input[name="monthly_amounts[${month}][patient_amount]"]`).value = patientAmount;
    document.querySelector(`input[name="monthly_amounts[${month}][is_active]"]`).checked = isActive;
}

function generateRetrocessionInvoice(userId) {
    // Get selected month and year from dropdowns
    const selectedMonth = parseInt(document.getElementById('retInvoiceMonth').value);
    const selectedYear = parseInt(document.getElementById('retInvoiceYear').value);
    
    // Validate not future month
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    if (selectedYear > currentYear || (selectedYear === currentYear && selectedMonth > currentMonth)) {
        alert('{{ __("users.cannot_generate_future_invoice") | default("Vous ne pouvez pas générer une facture pour un mois futur") }}');
        return;
    }
    
    const monthNames = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const monthName = monthNames[selectedMonth - 1];
    
    if (confirm(`{{ __("users.generate_invoice_confirm") }} ${monthName} ${selectedYear}?`)) {
        // Make POST request
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '{{ base_url }}/users/' + userId + '/generate-retrocession', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        xhr.setRequestHeader('X-CSRF-Token', '{{ csrf_token }}');
        
        xhr.onload = function() {
            try {
                const response = JSON.parse(xhr.responseText);
                if (response.success) {
                    alert(response.message || '{{ __("users.invoice_generated_successfully") }}');
                    // Reload the page to show the updated invoice
                    location.reload();
                } else {
                    alert('{{ __("common.error") }}: ' + (response.message || '{{ __("users.invoice_generation_failed") }}'));
                }
            } catch (e) {
                console.error('JSON parse error:', e);
                console.error('Response text:', xhr.responseText);
                console.error('Response status:', xhr.status);
                console.error('Response headers:', xhr.getAllResponseHeaders());
                // Show first 200 chars of response in alert for debugging
                const preview = xhr.responseText.substring(0, 200);
                alert('{{ __("common.error") }}: Invalid JSON response. Check console for details. Response preview: ' + preview);
            }
        };
        
        xhr.onerror = function() {
            alert('{{ __("common.error") }}: Network error');
        };
        
        // Send the data as JSON with selected month/year
        xhr.send(JSON.stringify({
            month: selectedMonth,
            year: selectedYear
        }));
    }
}

// Course Functions
function showCourseForm() {
    document.getElementById('course-form').style.display = 'block';
    document.getElementById('course-form-title').textContent = '{{ __("courses.add_new_course") }}';
    document.getElementById('course-save-btn-text').textContent = '{{ __("courses.add_course") }}';
    document.getElementById('course-id').value = '';
    document.getElementById('course-name').value = '';
    document.getElementById('course-rate').value = '';
    document.getElementById('course-vat').value = '16';
    document.getElementById('course-active').checked = true;
}

function editCourse(id, name, rate, vat, isActive) {
    document.getElementById('course-form').style.display = 'block';
    document.getElementById('course-form-title').textContent = '{{ __("courses.edit_course") }}';
    document.getElementById('course-save-btn-text').textContent = '{{ __("common.save_changes") }}';
    document.getElementById('course-id').value = id;
    document.getElementById('course-name').value = name;
    document.getElementById('course-rate').value = rate;
    document.getElementById('course-vat').value = vat;
    document.getElementById('course-active').checked = isActive;
}

function cancelCourseForm() {
    document.getElementById('course-form').style.display = 'none';
    document.getElementById('course-edit-form').reset();
}

function deleteCourse(id) {
    if (!confirm('{{ __("courses.delete_confirm") }}')) {
        return;
    }
    
    fetch(`{{ base_url }}/coaches/${id}/courses`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-Token': '{{ csrf_token }}',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
}

// Course form submission
document.getElementById('course-edit-form')?.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const courseId = document.getElementById('course-id').value;
    const formData = new FormData(this);
    
    const url = courseId 
        ? `{{ base_url }}/coaches/${courseId}/courses`
        : `{{ base_url }}/coaches/{{ user.id }}/courses`;
    
    const method = courseId ? 'PUT' : 'POST';
    
    if (method === 'PUT') {
        formData.append('_method', 'PUT');
    }
    
    fetch(url, {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-Token': '{{ csrf_token }}',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.message || '{{ __("common.error_occurred") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
});
</script>

<style>
/* Financial Tab Styling */
#financial .card-header h6 {
    font-weight: 600;
    margin-bottom: 0;
}

#financial hr.my-4 {
    border-color: #dee2e6;
    opacity: 0.5;
}

/* Section Headers */
.text-primary i, .text-success i {
    opacity: 0.8;
}

/* Monthly Amounts Table */
#monthlyAmountsView .table-success {
    background-color: rgba(25, 135, 84, 0.1);
}

#monthlyAmountsView .table-success a {
    font-weight: 600;
}

/* Year Navigation Styling */
.btn-group .btn-light {
    border-color: #dee2e6;
    transition: all 0.2s ease;
}

.btn-group .btn-light:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.btn-group .btn-light:active {
    transform: scale(0.95);
}

#retrocessionYearDisplay, #obligationsYearDisplay {
    transition: opacity 0.2s ease;
}

/* Action Buttons Gap */
.gap-2 {
    gap: 0.5rem!important;
}

/* Month/Year selector styling */
.form-select.form-select-sm {
    padding: 0.25rem 2rem 0.25rem 0.5rem;
    font-size: 0.875rem;
    min-width: 100px;
}

.d-flex.gap-1 {
    gap: 0.25rem!important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-wrap: wrap;
    }
    
    .d-flex.gap-2 .btn {
        flex: 1;
        min-width: 120px;
    }
}

/* Retrocession Settings Alert */
#currentRetrocessionSettings .alert {
    border-left: 4px solid;
}

/* Financial Obligations Card */
.card-header.bg-warning {
    background-color: #ffc107 !important;
    border-color: #ffc107;
}

/* Tab Styling */
#userFormTabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 2rem;
}

#userFormTabs .nav-link {
    color: #6c757d;
    border: none;
    border-bottom: 3px solid transparent;
    padding: .75rem 1rem;
    transition: all 0.3s ease;
}

#userFormTabs .nav-link:hover {
    color: #495057;
    border-bottom-color: #dee2e6;
}

#userFormTabs .nav-link.active {
    color: #0d6efd;
    background-color: transparent;
    border-color: transparent transparent #0d6efd;
    font-weight: 600;
}

/* Make tabs responsive */
@media (max-width: 768px) {
    #userFormTabs {
        flex-direction: column;
    }
    
    #userFormTabs .nav-link {
        text-align: left;
        padding: 1rem;
        border-bottom: 1px solid #dee2e6;
        border-left: 3px solid transparent;
    }
    
    #userFormTabs .nav-link.active {
        border-bottom: 1px solid #dee2e6;
        border-left-color: #0d6efd;
        background-color: #f8f9fa;
    }
}

/* Sticky form actions */
.card.mt-4 {
    position: sticky;
    bottom: 0;
    z-index: 100;
    box-shadow: 0 -2px 10px rgba(0,0,0,.1);
}

/* Tab content animations */
.tab-pane {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Financial obligations card styling */
.financial-card .card {
    transition: all 0.3s ease;
}
.financial-card .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>

<script>
// Financial Obligations Functions
function toggleFinancialObligations() {
    const viewDiv = document.getElementById('financialObligationsView');
    const editDiv = document.getElementById('financialObligationsEdit');
    
    // Check if edit div exists (user has permission)
    if (!editDiv) {
        alert('Vous n\'avez pas la permission de modifier les obligations financières. Seuls les administrateurs et managers peuvent effectuer cette action.');
        return;
    }
    
    if (editDiv.style.display === 'none') {
        // Entering edit mode
        isEditingFinancialObligations = true;
        viewDiv.style.display = 'none';
        editDiv.style.display = 'block';
        
        // Add event listeners for total calculation
        const rentInput = document.getElementById('rent_amount');
        const chargesInput = document.getElementById('charges_amount');
        const secretaryInput = document.getElementById('secretary_tvac_17');
        
        if (rentInput) rentInput.addEventListener('input', calculateFinancialTotal);
        if (chargesInput) chargesInput.addEventListener('input', calculateFinancialTotal);
        if (secretaryInput) secretaryInput.addEventListener('input', calculateFinancialTotal);
        
        // Calculate total on load
        calculateFinancialTotal();
    } else {
        // Exiting edit mode
        isEditingFinancialObligations = false;
        viewDiv.style.display = 'block';
        editDiv.style.display = 'none';
    }
}

function cancelFinancialObligations() {
    isEditingFinancialObligations = false;
    toggleFinancialObligations();
}

// Financial Obligations Year Navigation
let currentObligationsYear = parseInt('{{ "now"|date("Y") }}');

function changeObligationsYear(direction) {
    currentObligationsYear += direction;
    
    // Update display and hidden input
    const yearDisplay = document.getElementById('obligationsYearDisplay');
    yearDisplay.textContent = currentObligationsYear;
    document.getElementById('obligationsYear').value = currentObligationsYear;
    
    // Add animation effect
    yearDisplay.style.opacity = '0.5';
    setTimeout(() => {
        yearDisplay.style.opacity = '1';
    }, 100);
    
    // Load data for the new year
    loadObligationsYear(currentObligationsYear);
}

function loadObligationsYear(year) {
    // Show loading indicator
    const viewDiv = document.getElementById('financialObligationsView');
    viewDiv.style.opacity = '0.5';
    
    // Fetch obligations for the selected year
    fetch(`{{ base_url }}/users/{{ user.id }}/financial-obligations-year?year=${year}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-Token': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update the view with new data
            updateFinancialObligationsView(data.obligations, data.history);
        } else {
            console.error('Failed to load obligations:', data.message);
        }
        viewDiv.style.opacity = '1';
    })
    .catch(error => {
        console.error('Error loading obligations:', error);
        viewDiv.style.opacity = '1';
    });
}

function updateFinancialObligationsView(current, history) {
    const viewDiv = document.getElementById('financialObligationsView');
    
    if (!current) {
        viewDiv.innerHTML = `
            <div class="alert alert-info mb-0">
                <i class="bi bi-info-circle me-2"></i>
                {{ __('users.no_financial_obligations') }}
            </div>`;
        return;
    }
    
    // Build the HTML for current obligations
    let html = `
        <div class="row g-3">
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">{{ __('users.rent_amount') }}</h6>
                        <h4 class="mb-0">${formatMoney(current.rent_amount)}€</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">{{ __('users.charges_amount') }}</h6>
                        <h4 class="mb-0">${formatMoney(current.charges_amount)}€</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">{{ __('users.total_tvac') }}</h6>
                        <h4 class="mb-0 text-primary">${formatMoney(current.total_tvac)}€</h4>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="text-muted mb-2">{{ __('users.effective_date') }}</h6>
                        <h4 class="mb-0">${formatDate(current.effective_date)}</h4>
                    </div>
                </div>
            </div>
        </div>`;
    
    // Add loyer invoice table if obligations exist
    if (current) {
        const currentMonth = new Date().getMonth() + 1;
        const nextMonth = (currentMonth % 12) + 1;
        const year = currentObligationsYear;
        const months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
        
        html += `
            <hr class="my-4">
            <h6 class="text-muted mb-3">
                <i class="bi bi-calendar-check me-2"></i>{{ __('users.monthly_loyer_status') }}
            </h6>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>{{ __('common.month') }}</th>
                            <th class="text-center">{{ __('common.total') }} TVAC</th>
                            <th class="text-center">{{ __('common.status') }}</th>
                            <th class="text-center">{{ __('common.invoice') }}</th>
                        </tr>
                    </thead>
                    <tbody>`;
        
        for (let i = 1; i <= 12; i++) {
            const isVisibleMonth = (i === currentMonth || i === nextMonth);
            const monthClass = isVisibleMonth ? '' : 'loyer-month-row-hidden d-none';
            
            html += `
                <tr id="loyer-month-row-${i}" class="${monthClass}" data-month="${i}">
                    <td>${months[i-1]} ${year}</td>
                    <td class="text-center">
                        <strong>${formatMoney(current.total_tvac)}€</strong>
                    </td>
                    <td class="text-center">
                        <span class="badge bg-success">{{ __('common.active') }}</span>
                    </td>
                    <td class="text-center loyer-invoice-status-${i}">
                        <button type="button" class="btn btn-sm btn-outline-primary" 
                                onclick="generateLoyerInvoice({{ user.id }}, ${i}, ${year})">
                            <i class="bi bi-file-earmark-plus"></i>
                        </button>
                    </td>
                </tr>`;
        }
        
        html += `
                    </tbody>
                </table>
            </div>`;
    }
    
    viewDiv.innerHTML = html;
}

function toggleAllObligations() {
    const btn = document.getElementById('toggleAllObligationsBtn');
    const expanded = btn.getAttribute('data-expanded') === 'true';
    
    let itemsToggled = 0;
    
    if (expanded) {
        // Collapse to show only current year
        btn.setAttribute('data-expanded', 'false');
        btn.innerHTML = '<i class="bi bi-arrows-expand me-1"></i>Afficher tout';
        
        // Hide history section if it exists
        const historySection = document.getElementById('obligationsHistorySection');
        if (historySection) {
            historySection.style.display = 'none';
            itemsToggled++;
        }
        
        // Hide non-current loyer months
        const currentMonth = new Date().getMonth() + 1;
        const nextMonth = (currentMonth % 12) + 1;
        
        document.querySelectorAll('[id^="loyer-month-row-"]').forEach(row => {
            const month = parseInt(row.getAttribute('data-month'));
            if (month !== currentMonth && month !== nextMonth) {
                row.classList.add('d-none');
                itemsToggled++;
            }
        });
        
        if (itemsToggled === 0) {
            // No items were hidden
            console.log('Aucun élément supplémentaire à masquer');
        }
        
    } else {
        // Expand to show all history
        btn.setAttribute('data-expanded', 'true');
        btn.innerHTML = '<i class="bi bi-arrows-collapse me-1"></i>Afficher moins';
        
        // Show history section if it exists
        const historySection = document.getElementById('obligationsHistorySection');
        if (historySection) {
            historySection.style.display = 'block';
            itemsToggled++;
        }
        
        // Show all loyer months
        document.querySelectorAll('[id^="loyer-month-row-"]').forEach(row => {
            if (row.classList.contains('d-none')) {
                row.classList.remove('d-none');
                itemsToggled++;
            }
        });
        
        if (itemsToggled === 0) {
            // No items were shown - all months are already visible
            console.log('Tous les éléments sont déjà visibles');
            // Optionally show a tooltip or brief message
            showToast('Tous les mois sont déjà affichés', 'info');
        }
    }
}

// Helper function to format money
function formatMoney(amount) {
    return parseFloat(amount).toFixed(2).replace('.', ',').replace(/\B(?=(\d{3})+(?!\d))/g, '.');
}

// Helper function to format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

function calculateFinancialTotal() {
    // Check if elements exist before trying to access them
    const rentInput = document.getElementById('rent_amount');
    const chargesInput = document.getElementById('charges_amount');
    const secretaryInput = document.getElementById('secretary_tvac_17');
    const totalElement = document.getElementById('totalAmount');
    
    // If any required element is missing, exit gracefully
    if (!rentInput || !chargesInput || !secretaryInput || !totalElement) {
        console.log('Financial form elements not found - user may not have edit permission');
        return;
    }
    
    const rent = parseFloat(rentInput.value) || 0;
    const charges = parseFloat(chargesInput.value) || 0;
    const secretaryTvac = parseFloat(secretaryInput.value) || 0;
    // Note: secretary_htva and tva_17 are NOT included in the total
    
    const total = rent + charges + secretaryTvac;
    // Format with comma as decimal separator
    totalElement.textContent = total.toFixed(2).replace('.', ',');
}

// Track if we're in edit mode
let isEditingFinancialObligations = false;

function saveFinancialObligations() {
    console.log('saveFinancialObligations called');
    console.trace('Call stack trace');
    
    // Guard: only save if we're actually in edit mode
    if (!isEditingFinancialObligations) {
        console.log('Not in edit mode, ignoring save request');
        return;
    }
    
    // Since we're inside the main form, collect the financial obligation fields manually
    const formData = new FormData();
    
    // Add CSRF token
    formData.append('csrf_token', '{{ csrf_token }}');
    
    // Collect financial obligation fields
    formData.append('rent_amount', document.getElementById('rent_amount').value);
    formData.append('charges_amount', document.getElementById('charges_amount').value);
    formData.append('secretary_tvac_17', document.getElementById('secretary_tvac_17').value);
    formData.append('secretary_htva', document.getElementById('secretary_htva').value);
    formData.append('tva_17', document.getElementById('tva_17').value);
    formData.append('effective_date', document.getElementById('effective_date').value);
    formData.append('notes', document.getElementById('notes').value);
    
    // Make the request
    fetch('{{ base_url }}/users/{{ user.id }}/financial-obligations', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            isEditingFinancialObligations = false; // Reset flag before reload
            location.reload();
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    })
    .finally(() => {
        // Ensure flag is reset even on error
        isEditingFinancialObligations = false;
    });
}

// Monthly Course Functions
function toggleMonthlyCourses() {
    const viewDiv = document.getElementById('monthlyCoursesView');
    const editDiv = document.getElementById('monthlyCoursesEdit');
    
    if (editDiv.style.display === 'none') {
        viewDiv.style.display = 'none';
        editDiv.style.display = 'block';
        // Calculate totals for each month
        for (let i = 1; i <= 12; i++) {
            updateCourseMonthTotal(i);
        }
    } else {
        viewDiv.style.display = 'block';
        editDiv.style.display = 'none';
    }
}

function cancelMonthlyCourses() {
    toggleMonthlyCourses();
}

function updateCourseMonthTotal(month) {
    let total = 0;
    const inputs = document.querySelectorAll(`input[data-month="${month}"]`);
    
    inputs.forEach(input => {
        const count = parseInt(input.value) || 0;
        const rate = parseFloat(input.getAttribute('data-rate')) || 0;
        total += count * rate;
    });
    
    document.getElementById(`course-month-total-${month}`).textContent = total.toFixed(2).replace('.', ',') + '€';
}

function toggleAllCourseMonths() {
    const hiddenRows = document.querySelectorAll('.course-month-hidden');
    const btn = document.getElementById('toggleAllCourseMonthsBtn');
    const isExpanded = btn.getAttribute('data-expanded') === 'true';
    
    hiddenRows.forEach(row => {
        if (isExpanded) {
            row.classList.add('d-none');
        } else {
            row.classList.remove('d-none');
        }
    });
    
    if (isExpanded) {
        btn.innerHTML = '<i class="bi bi-arrows-expand me-1"></i>{{ __('common.show_all') }}';
        btn.setAttribute('data-expanded', 'false');
    } else {
        btn.innerHTML = '<i class="bi bi-arrows-collapse me-1"></i>{{ __('common.show_less') }}';
        btn.setAttribute('data-expanded', 'true');
    }
}

function saveMonthlyCourses() {
    const form = document.getElementById('userEditForm');
    const formData = new FormData(form);
    
    // Get only course count inputs
    const courseInputs = document.querySelectorAll('.course-count-input');
    const courseCounts = {};
    
    courseInputs.forEach(input => {
        const month = input.getAttribute('data-month');
        const courseId = input.getAttribute('data-course-id');
        const count = input.value || 0;
        
        if (!courseCounts[month]) {
            courseCounts[month] = {};
        }
        courseCounts[month][courseId] = count;
    });
    
    // Add course counts to form data
    formData.append('course_counts', JSON.stringify(courseCounts));
    formData.append('current_year', new Date().getFullYear());
    
    fetch('{{ base_url }}/users/{{ user.id }}/monthly-course-counts', {
        method: 'POST',
        headers: {
            'X-CSRF-Token': '{{ csrf_token }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('{{ __('common.saved_successfully') }}', 'success');
            toggleMonthlyCourses();
            // Reload to update the view
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message || '{{ __('common.error') }}', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('{{ __('common.error_occurred') }}', 'danger');
    });
}

function generateCourseInvoice(userId) {
    // Get selected month and year from dropdowns
    const selectedMonth = parseInt(document.getElementById('locInvoiceMonth').value);
    const selectedYear = parseInt(document.getElementById('locInvoiceYear').value);
    
    // Validate not future month
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    if (selectedYear > currentYear || (selectedYear === currentYear && selectedMonth > currentMonth)) {
        alert('{{ __("users.cannot_generate_future_invoice") | default("Vous ne pouvez pas générer une facture pour un mois futur") }}');
        return;
    }
    
    const months = ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const monthName = months[selectedMonth];
    
    if (confirm(`{{ __('courses.generate_invoice_confirm') }} ${monthName} ${selectedYear} ?`)) {
        fetch(`{{ base_url }}/users/${userId}/generate-course-invoice`, {
            method: 'POST',
            headers: {
                'X-CSRF-Token': '{{ csrf_token }}',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                month: selectedMonth,
                year: selectedYear
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('{{ __('invoices.invoice_generated_successfully') }}', 'success');
                // Update the invoice status cell
                const statusCell = document.querySelector(`.course-invoice-status-${selectedMonth}`);
                if (statusCell && data.invoice) {
                    statusCell.innerHTML = `<a href="{{ base_url }}/invoices/${data.invoice.id}" class="text-success" target="_blank">
                        <i class="bi bi-check-circle-fill me-1"></i>
                        ${data.invoice.invoice_number}
                    </a>`;
                }
                // Reload after a short delay to update the view
                setTimeout(() => location.reload(), 1500);
            } else {
                showToast(data.message || '{{ __('common.error') }}', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('{{ __('common.error_occurred') }}', 'danger');
        });
    }
}

// Add/Edit Course Functions
function addNewCourse() {
    document.getElementById('course-form').style.display = 'block';
    document.getElementById('course-form-title').textContent = '{{ __('courses.add_new_course') }}';
    document.getElementById('course-save-btn-text').textContent = '{{ __('courses.add_course') }}';
    document.getElementById('course-id').value = '';
    document.getElementById('course-name').value = '';
    document.getElementById('course-rate').value = '';
    document.getElementById('course-vat').value = '16';
    document.getElementById('course-active').checked = true;
    document.getElementById('course-name').focus();
}

// Backup submit function in case event listener doesn't work
function submitCourseForm(event) {
    if (event) {
        event.preventDefault();
    }
    
    console.log('=== submitCourseForm called directly ===');
    
    const courseId = document.getElementById('course-id').value;
    const isEdit = courseId !== '';
    const baseUrl = '{{ base_url }}';
    const url = isEdit 
        ? `${baseUrl}/users/{{ user.id }}/courses/${courseId}`
        : `${baseUrl}/users/{{ user.id }}/courses`;
    
    const formData = new FormData();
    formData.append('course_name', document.getElementById('course-name').value);
    formData.append('hourly_rate', document.getElementById('course-rate').value);
    formData.append('vat_rate', document.getElementById('course-vat').value);
    formData.append('is_active', document.getElementById('course-active').checked ? '1' : '0');
    
    console.log('Submitting to:', url);
    console.log('Form data:', Object.fromEntries(formData));
    
    fetch(url, {
        method: isEdit ? 'PUT' : 'POST',
        headers: {
            'X-CSRF-Token': '{{ csrf_token }}'
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response:', data);
        if (data.success) {
            showToast(isEdit ? '{{ __('courses.course_updated') }}' : '{{ __('courses.course_added') }}', 'success');
            setTimeout(() => {
                window.location.hash = 'courses';
                location.reload();
            }, 1000);
        } else {
            showToast(data.message || '{{ __('common.error') }}', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('{{ __('common.error_occurred') }}', 'danger');
    });
    
    return false;
}

function editCourse(id, name, rate, vat, isActive) {
    document.getElementById('course-form').style.display = 'block';
    document.getElementById('course-form-title').textContent = '{{ __('courses.edit_course') }}';
    document.getElementById('course-save-btn-text').textContent = '{{ __('common.save') }}';
    document.getElementById('course-id').value = id;
    document.getElementById('course-name').value = name;
    document.getElementById('course-rate').value = rate;
    document.getElementById('course-vat').value = vat;
    document.getElementById('course-active').checked = isActive;
    document.getElementById('course-name').focus();
}

function cancelCourseForm() {
    document.getElementById('course-form').style.display = 'none';
}

function deleteCourse(id) {
    if (!confirm('{{ __('courses.confirm_delete_course') }}')) {
        return;
    }
    
    fetch(`{{ base_url }}/users/{{ user.id }}/courses/${id}`, {
        method: 'DELETE',
        headers: {
            'X-CSRF-Token': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('{{ __('courses.course_deleted') }}', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message || '{{ __('common.error') }}', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('{{ __('common.error_occurred') }}', 'danger');
    });
}

// Handle course form submission
// Commented out as we're using onclick handler instead
/*
document.addEventListener('DOMContentLoaded', function() {
    const courseForm = document.getElementById('course-edit-form');
    console.log('Course form element found:', courseForm !== null);
    
    if (courseForm) {
        courseForm.addEventListener('submit', function(e) {
            e.preventDefault();
            console.log('=== Course Form Submission Debug ===');
    
    try {
        const courseId = document.getElementById('course-id').value;
        const isEdit = courseId !== '';
        console.log('Is Edit:', isEdit);
        console.log('Course ID:', courseId);
        
        // Use absolute path to avoid URL duplication issues
        const baseUrl = '{{ base_url }}';
        const url = isEdit 
            ? `${baseUrl}/users/{{ user.id }}/courses/${courseId}`
            : `${baseUrl}/users/{{ user.id }}/courses`;
        console.log('Request URL:', url);
        console.log('CSRF Token:', '{{ csrf_token }}');
        
        const formData = new FormData();
        const courseName = document.getElementById('course-name').value;
        const hourlyRate = document.getElementById('course-rate').value;
        const vatRate = document.getElementById('course-vat').value;
        const isActive = document.getElementById('course-active').checked;
        
        console.log('Raw form values:');
        console.log('  course_name:', courseName);
        console.log('  hourly_rate:', hourlyRate);
        console.log('  vat_rate:', vatRate);
        console.log('  is_active:', isActive);
        
        formData.append('course_name', courseName);
        formData.append('hourly_rate', hourlyRate);
        formData.append('vat_rate', vatRate);
        formData.append('is_active', isActive ? '1' : '0');
        
        console.log('Form Data:');
        for (let [key, value] of formData.entries()) {
            console.log(`  ${key}: ${value}`);
        }
        
        console.log('Starting fetch request...');
        fetch(url, {
            method: isEdit ? 'PUT' : 'POST',
            headers: {
                'X-CSRF-Token': '{{ csrf_token }}'
            },
            body: formData
        })
        .then(response => {
            console.log('Response received:', response);
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            if (!response.ok) {
                console.error('HTTP error! status:', response.status);
            }
            
            return response.text().then(text => {
                console.log('Raw response text:', text);
                try {
                    return JSON.parse(text);
                } catch (e) {
                    console.error('Failed to parse JSON:', e);
                    console.error('Response was:', text);
                    throw new Error('Invalid JSON response: ' + text);
                }
            });
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                showToast(isEdit ? '{{ __('courses.course_updated') }}' : '{{ __('courses.course_added') }}', 'success');
                console.log('Course saved successfully, reloading page...');
                setTimeout(() => {
                    // Reload to the correct URL with the courses tab
                    const currentPath = window.location.pathname;
                    const baseUrl = '{{ base_url }}';
                    
                    console.log('Current path:', currentPath);
                    console.log('Base URL:', baseUrl);
                    
                    // Check if URL already contains duplicate base_url and fix it
                    if (currentPath.includes(baseUrl + baseUrl)) {
                        console.log('Fixing duplicate URL');
                        window.location.href = currentPath.replace(baseUrl + baseUrl, baseUrl) + '#courses';
                    } else {
                        console.log('Setting hash and reloading');
                        window.location.hash = 'courses';
                        location.reload();
                    }
                }, 1000);
            } else {
                console.error('Server returned error:', data.message);
                showToast(data.message || '{{ __('common.error') }}', 'danger');
            }
        })
        .catch(error => {
            console.error('Fetch error:', error);
            console.error('Error details:', {
                message: error.message,
                stack: error.stack
            });
            showToast('{{ __('common.error_occurred') }}', 'danger');
        });
    } catch (error) {
        console.error('Form submission error:', error);
        console.error('Error details:', {
            message: error.message,
            stack: error.stack
        });
        showToast('An error occurred while submitting the form', 'danger');
    }
        });
    } else {
        console.error('Course form not found! Check if #course-edit-form exists in the DOM');
    }
});
*/

// Generate next month's Loyer invoice (rent is billed in advance)
function generateCurrentMonthLoyerInvoice(userId) {
    const currentDate = new Date();
    let month = currentDate.getMonth() + 2; // +2 because getMonth() is 0-based, and we want next month
    let year = currentDate.getFullYear();
    
    // Handle year rollover
    if (month > 12) {
        month = 1;
        year++;
    }
    
    generateLoyerInvoice(userId, month, year);
}

// Generate Loyer invoice for specific month
function generateLoyerInvoice(userId, month, year) {
    const months = ['', 'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin', 'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'];
    const monthName = months[month] || month;
    
    if (!confirm(`{{ __("invoices.confirm_generate_loyer") | default("Êtes-vous sûr de vouloir générer la facture de loyer pour") }} ${monthName} ${year}?`)) {
        return;
    }
    
    const button = $(`button[onclick*="generateLoyerInvoice(${userId}, ${month}, ${year})"]`);
    const originalText = button.html();
    button.prop('disabled', true).html('<i class="spinner-border spinner-border-sm me-1"></i> {{ __("common.loading") }}...');
    
    const formData = new FormData();
    formData.append('csrf_token', '{{ csrf_token }}');
    formData.append('month', month);
    formData.append('year', year);
    
    fetch(`{{ base_url }}/users/${userId}/generate-loyer-invoice`, {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            return response.text().then(text => {
                throw new Error(`HTTP error! status: ${response.status}, response: ${text}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Update the UI to show invoice was generated
            const monthRow = button.closest('tr');
            monthRow.addClass('table-success');
            
            // Update status column
            monthRow.find('td:nth-child(3)').html(
                '<span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>{{ __("invoices.generated") }}</span>'
            );
            
            // Update action column with view invoice link
            const actionHtml = `
                <a href="{{ base_url }}/invoices/${data.invoice.id}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-eye me-1"></i>${data.invoice.invoice_number}
                </a>
            `;
            monthRow.find('td:last-child').html(actionHtml);
            
            // Show success message
            showToast(data.message || '{{ __("invoices.loyer_invoice_generated_successfully") }}', 'success');
            
            // Update totals if needed
            setTimeout(() => {
                location.reload(); // Reload to get updated data
            }, 2000);
        } else {
            showToast(data.message || '{{ __("common.error_occurred") }}', 'danger');
            button.prop('disabled', false).html(originalText);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast(error.message || '{{ __("common.error_occurred") }}', 'danger');
        button.prop('disabled', false).html(originalText);
    });
}

// Generate Loyer invoice with selected month
function generateLoyerInvoiceWithMonth(userId) {
    // Get selected month from dropdown
    const selectedMonth = parseInt(document.getElementById('loyInvoiceMonth').value);
    const selectedYear = parseInt(document.getElementById('obligationsYearDisplay').textContent);
    
    // Validate not future month
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;
    
    if (selectedYear > currentYear || (selectedYear === currentYear && selectedMonth > currentMonth)) {
        alert('{{ __("users.cannot_generate_future_invoice") | default("Vous ne pouvez pas générer une facture pour un mois futur") }}');
        return;
    }
    
    generateLoyerInvoice(userId, selectedMonth, selectedYear);
}

// Debug function to check courses (can be called from console)
window.debugCourses = function() {
    console.log('=== Manual Debug Check ===');
    
    // Check if courses tab exists
    const coursesTab = document.getElementById('courses-tab');
    console.log('Courses tab exists:', coursesTab !== null);
    if (coursesTab) {
        console.log('Courses tab parent element:', coursesTab.parentElement);
        console.log('Courses tab classes:', coursesTab.className);
        console.log('Courses tab style display:', coursesTab.style.display);
    }
    
    // Check courses data
    console.log('Is coach (from template):', {{ is_coach ? 'true' : 'false' }});
    console.log('User ID:', {{ user.id }});
    console.log('User has client_id:', {{ user.client_id ? 'true' : 'false' }});
    
    // Check courses container
    const coursesContainer = document.getElementById('courses-container');
    if (coursesContainer) {
        const courseTable = coursesContainer.querySelector('#courses-table');
        const courseRows = coursesContainer.querySelectorAll('.course-row');
        console.log('Course table exists:', courseTable !== null);
        console.log('Number of course rows:', courseRows.length);
        
        if (courseRows.length > 0) {
            courseRows.forEach((row, index) => {
                console.log(`Course ${index + 1}:`, {
                    id: row.getAttribute('data-course-id'),
                    name: row.querySelector('td:nth-child(2)').textContent.trim()
                });
            });
        }
    }
    
    // Check if courses variable is available
    console.log('Courses from template:', {{ courses|json_encode|raw }});
    
    // Force show courses tab for testing
    if (coursesTab && coursesTab.parentElement.style.display === 'none') {
        console.log('Forcing courses tab to be visible...');
        coursesTab.parentElement.style.display = '';
    }
};

console.log('Debug function available: call window.debugCourses() in console');
</script>
{% endblock %}