<?php
/**
 * Mobile Helper Functions
 * Utility functions for mobile responsiveness and device detection
 */

/**
 * Check if the current request is from a mobile device
 * 
 * @return bool
 */
function is_mobile() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Common mobile user agent patterns
    $mobilePatterns = [
        '/Android/i',
        '/webOS/i',
        '/iPhone/i',
        '/iPad/i',
        '/iPod/i',
        '/BlackBerry/i',
        '/Windows Phone/i',
        '/Mobile/i',
        '/Tablet/i'
    ];
    
    foreach ($mobilePatterns as $pattern) {
        if (preg_match($pattern, $userAgent)) {
            return true;
        }
    }
    
    // Check for mobile-specific headers
    if (isset($_SERVER['HTTP_X_WAP_PROFILE']) || isset($_SERVER['HTTP_PROFILE'])) {
        return true;
    }
    
    // Check Accept header for mobile content types
    $accept = $_SERVER['HTTP_ACCEPT'] ?? '';
    if (strpos(strtolower($accept), 'application/vnd.wap.xhtml+xml') !== false) {
        return true;
    }
    
    return false;
}

/**
 * Get device type (mobile, tablet, desktop)
 * 
 * @return string
 */
function get_device_type() {
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // Check for tablet first (tablets often have mobile in UA too)
    $tabletPatterns = ['/iPad/i', '/Tablet/i', '/Kindle/i', '/Playbook/i'];
    foreach ($tabletPatterns as $pattern) {
        if (preg_match($pattern, $userAgent)) {
            return 'tablet';
        }
    }
    
    // Check for mobile
    if (is_mobile()) {
        return 'mobile';
    }
    
    return 'desktop';
}

/**
 * Get appropriate view file based on device type
 * 
 * @param string $baseView Base view name (e.g., 'invoices/index')
 * @return string View file to use
 */
function get_responsive_view($baseView) {
    $device = get_device_type();
    
    // Try device-specific view first
    $deviceView = $baseView . '-' . $device;
    $viewPath = APP_PATH . '/views/' . $deviceView . '.twig';
    
    if (file_exists($viewPath)) {
        return $deviceView;
    }
    
    // Try mobile view for tablets too
    if ($device === 'tablet') {
        $mobileView = $baseView . '-mobile';
        $mobileViewPath = APP_PATH . '/views/' . $mobileView . '.twig';
        
        if (file_exists($mobileViewPath)) {
            return $mobileView;
        }
    }
    
    // Fall back to base view
    return $baseView;
}

/**
 * Generate responsive image attributes
 * 
 * @param string $src Image source
 * @param array $sizes Responsive sizes configuration
 * @return string HTML attributes
 */
function responsive_image_attrs($src, $sizes = []) {
    $attrs = ['src="' . htmlspecialchars($src) . '"'];
    
    // Default sizes if not provided
    if (empty($sizes)) {
        $sizes = [
            '(max-width: 576px) 100vw',
            '(max-width: 768px) 50vw',
            '33vw'
        ];
    }
    
    $attrs[] = 'sizes="' . implode(', ', $sizes) . '"';
    
    // Add loading lazy for better performance
    $attrs[] = 'loading="lazy"';
    
    return implode(' ', $attrs);
}

/**
 * Get touch-friendly class names
 * 
 * @param string $baseClass Base CSS class
 * @return string CSS classes including touch variants
 */
function touch_class($baseClass) {
    $classes = [$baseClass];
    
    if (is_mobile()) {
        $classes[] = $baseClass . '-touch';
        $classes[] = 'touch-target';
    }
    
    return implode(' ', $classes);
}

/**
 * Generate mobile-friendly pagination
 * 
 * @param int $currentPage Current page number
 * @param int $totalPages Total number of pages
 * @param string $baseUrl Base URL for pagination links
 * @return array Pagination data for template
 */
function mobile_pagination($currentPage, $totalPages, $baseUrl) {
    $pagination = [
        'current' => $currentPage,
        'total' => $totalPages,
        'prev' => null,
        'next' => null,
        'pages' => []
    ];
    
    // Previous page
    if ($currentPage > 1) {
        $pagination['prev'] = $baseUrl . '?page=' . ($currentPage - 1);
    }
    
    // Next page
    if ($currentPage < $totalPages) {
        $pagination['next'] = $baseUrl . '?page=' . ($currentPage + 1);
    }
    
    // For mobile, show fewer page numbers
    $device = get_device_type();
    $maxVisible = ($device === 'mobile') ? 3 : 7;
    
    $start = max(1, $currentPage - floor($maxVisible / 2));
    $end = min($totalPages, $start + $maxVisible - 1);
    
    // Adjust start if we're near the end
    if ($end - $start < $maxVisible - 1) {
        $start = max(1, $end - $maxVisible + 1);
    }
    
    for ($i = $start; $i <= $end; $i++) {
        $pagination['pages'][] = [
            'number' => $i,
            'url' => $baseUrl . '?page=' . $i,
            'active' => $i === $currentPage
        ];
    }
    
    // Add ellipsis indicators
    $pagination['show_start_ellipsis'] = $start > 1;
    $pagination['show_end_ellipsis'] = $end < $totalPages;
    
    return $pagination;
}

/**
 * Format date/time for mobile display
 * 
 * @param string|DateTime $datetime Date/time to format
 * @param bool $relative Use relative time for recent dates
 * @return string Formatted date/time
 */
function mobile_datetime($datetime, $relative = true) {
    if (is_string($datetime)) {
        $datetime = new DateTime($datetime);
    }
    
    $now = new DateTime();
    $diff = $now->diff($datetime);
    
    // Use relative time for recent dates on mobile
    if ($relative && is_mobile() && $diff->days < 7) {
        if ($diff->days === 0) {
            if ($diff->h === 0) {
                if ($diff->i === 0) {
                    return 'Just now';
                }
                return $diff->i . ' min ago';
            }
            return $diff->h . ' hr ago';
        } elseif ($diff->days === 1) {
            return 'Yesterday';
        } else {
            return $diff->days . ' days ago';
        }
    }
    
    // Use shorter format on mobile
    if (is_mobile()) {
        return $datetime->format('M j');
    }
    
    return $datetime->format('M j, Y');
}

/**
 * Get optimized table class for responsive display
 * 
 * @param string $baseClass Base table class
 * @return string Optimized table classes
 */
function responsive_table_class($baseClass = 'table') {
    $classes = [$baseClass];
    
    if (is_mobile()) {
        $classes[] = 'table-mobile-card';
        $classes[] = 'table-mobile-responsive';
    } else {
        $classes[] = 'table-hover';
        $classes[] = 'table-striped';
    }
    
    return implode(' ', $classes);
}

/**
 * Check if feature should be enabled on mobile
 * 
 * @param string $feature Feature name
 * @return bool
 */
function mobile_feature_enabled($feature) {
    // Features to disable on mobile for better performance
    $disabledOnMobile = [
        'animations_heavy',
        'parallax_scrolling',
        'hover_previews',
        'auto_refresh',
        'background_sync'
    ];
    
    if (is_mobile() && in_array($feature, $disabledOnMobile)) {
        return false;
    }
    
    // Features to enable only on mobile
    $mobileOnly = [
        'swipe_navigation',
        'pull_to_refresh',
        'touch_gestures',
        'haptic_feedback',
        'offline_mode'
    ];
    
    if (!is_mobile() && in_array($feature, $mobileOnly)) {
        return false;
    }
    
    return true;
}

/**
 * Generate viewport-specific CSS classes
 * 
 * @return string CSS classes for current viewport
 */
function viewport_classes() {
    $classes = [];
    
    $device = get_device_type();
    $classes[] = 'device-' . $device;
    
    if (is_mobile()) {
        $classes[] = 'touch-enabled';
        
        // Detect specific mobile features
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        if (preg_match('/iPhone|iPad|iPod/i', $userAgent)) {
            $classes[] = 'ios-device';
        } elseif (preg_match('/Android/i', $userAgent)) {
            $classes[] = 'android-device';
        }
    }
    
    // Add viewport width class
    if (isset($_COOKIE['viewport_width'])) {
        $width = intval($_COOKIE['viewport_width']);
        
        if ($width < 576) {
            $classes[] = 'viewport-xs';
        } elseif ($width < 768) {
            $classes[] = 'viewport-sm';
        } elseif ($width < 992) {
            $classes[] = 'viewport-md';
        } elseif ($width < 1200) {
            $classes[] = 'viewport-lg';
        } else {
            $classes[] = 'viewport-xl';
        }
    }
    
    return implode(' ', $classes);
}