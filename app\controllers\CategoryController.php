<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\CatalogCategory;
use Flight;

class CategoryController extends Controller
{
    /**
     * List all categories
     */
    public function index()
    {
        // Get categories with product count
        $db = CatalogCategory::db();
        $sql = "
            SELECT cc.*, COUNT(ci.id) as products_count
            FROM catalog_categories cc
            LEFT JOIN catalog_items ci ON cc.id = ci.category_id
            GROUP BY cc.id
            ORDER BY cc.sort_order ASC
        ";
        $stmt = $db->query($sql);
        $categories = [];
        
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $category = new CatalogCategory();
            $category->fill($row);
            $category->products_count = $row['products_count'];
            $categories[] = $category;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'categories/index-' . $template;
        
        $this->render($viewName, [
            'categories' => $categories,
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ]);
        
        // Clear session messages after rendering
        unset($_SESSION['success']);
        unset($_SESSION['error']);
    }
    
    /**
     * Show create form
     */
    public function create()
    {
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'categories/create-' . $template;
        
        $this->render($viewName, [
            'errors' => $_SESSION['errors'] ?? [],
            'old' => $_SESSION['old'] ?? []
        ]);
        
        // Clear session data after rendering
        unset($_SESSION['errors']);
        unset($_SESSION['old']);
    }
    
    /**
     * Store new category
     */
    public function store()
    {
        $data = $_POST;
        
        // Validate
        $errors = $this->validateCategory($data);
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['old'] = $data;
            Flight::redirect('/fit/public/products/categories/create');
            return;
        }
        
        try {
            // Get next sort order
            $db = CatalogCategory::db();
            $stmt = $db->query("SELECT MAX(sort_order) as max_order FROM catalog_categories");
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $nextOrder = ($result['max_order'] ?? 0) + 1;
            
            // Prepare create data
            $createData = [
                'name' => $data['name'],
                'slug' => $this->generateSlug($data['name']),
                'description' => $data['description'] ?? null,
                'icon' => $data['icon'] ?? null,
                'color' => $data['color'] ?? '#007bff',
                'sort_order' => $data['sort_order'] ?? $nextOrder,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];
            
            // Set parent_id to null if not provided or empty
            if (isset($data['parent_id']) && !empty($data['parent_id'])) {
                $createData['parent_id'] = $data['parent_id'];
            } else {
                $createData['parent_id'] = null;
            }
            
            // Create category
            $category = CatalogCategory::create($createData);
            
            $_SESSION['success'] = 'Category created successfully';
            Flight::redirect($this->url('/products/categories'));
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Error creating category: ' . $e->getMessage();
            Flight::redirect($this->url('/products/categories/create'));
        }
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $category = CatalogCategory::find($id);
        if (!$category) {
            Flight::halt(404, 'Category not found');
            return;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'categories/edit-' . $template;
        
        $this->render($viewName, [
            'category' => $category,
            'errors' => $_SESSION['errors'] ?? [],
            'old' => $_SESSION['old'] ?? []
        ]);
        
        // Clear session data after rendering
        unset($_SESSION['errors']);
        unset($_SESSION['old']);
    }
    
    /**
     * Update category
     */
    public function update($id)
    {
        $category = CatalogCategory::find($id);
        if (!$category) {
            Flight::halt(404, 'Category not found');
            return;
        }
        
        $data = $_POST;
        
        // Validate
        $errors = $this->validateCategory($data, $id);
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['old'] = $data;
            Flight::redirect($this->url('/products/categories/' . $id . '/edit'));
            return;
        }
        
        try {
            // Prepare update data
            $updateData = [
                'name' => $data['name'],
                'slug' => $this->generateSlug($data['name']),
                'description' => $data['description'] ?? null,
                'icon' => $data['icon'] ?? null,
                'color' => $data['color'] ?? '#007bff',
                'sort_order' => $data['sort_order'] ?? $category->sort_order,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];
            
            // Only include parent_id if it's explicitly set in the form
            if (isset($data['parent_id'])) {
                $updateData['parent_id'] = empty($data['parent_id']) ? null : $data['parent_id'];
            }
            
            // Update category
            $category->update($updateData);
            
            $_SESSION['success'] = 'Category updated successfully';
            Flight::redirect($this->url('/products/categories'));
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Error updating category: ' . $e->getMessage();
            Flight::redirect($this->url('/products/categories/' . $id . '/edit'));
        }
    }
    
    /**
     * Delete category
     */
    public function destroy($id)
    {
        $category = CatalogCategory::find($id);
        if (!$category) {
            Flight::json(['success' => false, 'message' => 'Category not found'], 404);
            return;
        }
        
        // Check if category has products
        $db = CatalogCategory::db();
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM catalog_items WHERE category_id = :id");
        $stmt->execute(['id' => $id]);
        $hasProducts = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
        
        if ($hasProducts) {
            Flight::json(['success' => false, 'message' => 'Cannot delete category with products'], 400);
            return;
        }
        
        try {
            $category->delete();
            Flight::json(['success' => true, 'message' => 'Category deleted successfully']);
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error deleting category'], 500);
        }
    }
    
    /**
     * Reorder categories
     */
    public function reorder()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['order']) || !is_array($data['order'])) {
            Flight::json(['success' => false, 'message' => 'Invalid order data'], 400);
            return;
        }
        
        $db = CatalogCategory::db();
        $db->beginTransaction();
        
        try {
            foreach ($data['order'] as $index => $categoryId) {
                $stmt = $db->prepare("UPDATE catalog_categories SET sort_order = :order WHERE id = :id");
                $stmt->execute(['order' => $index + 1, 'id' => $categoryId]);
            }
            
            $db->commit();
            Flight::json(['success' => true, 'message' => 'Categories reordered successfully']);
            
        } catch (\Exception $e) {
            $db->rollBack();
            Flight::json(['success' => false, 'message' => 'Error reordering categories'], 500);
        }
    }
    
    /**
     * Validate category data
     */
    private function validateCategory($data, $id = null)
    {
        $errors = [];
        
        if (empty($data['name'])) {
            $errors['name'] = 'Category name is required';
        } else {
            // Check uniqueness
            $db = CatalogCategory::db();
            $sql = "SELECT COUNT(*) as count FROM catalog_categories WHERE name = :name";
            $params = ['name' => $data['name']];
            
            if ($id) {
                $sql .= " AND id != :id";
                $params['id'] = $id;
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            if ($stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0) {
                $errors['name'] = 'Category name already exists';
            }
        }
        
        return $errors;
    }
    
    /**
     * Generate URL-friendly slug
     */
    private function generateSlug($name)
    {
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }
}