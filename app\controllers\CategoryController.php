<?php
namespace App\Controllers;

use App\Core\Controller;
use App\Models\CatalogCategory;
use Flight;

class CategoryController extends Controller
{
    /**
     * List all categories
     */
    public function index()
    {
        // Get categories with product count
        $db = CatalogCategory::db();
        $sql = "
            SELECT cc.*, COUNT(ci.id) as products_count
            FROM catalog_categories cc
            LEFT JOIN catalog_items ci ON cc.id = ci.category_id
            WHERE cc.is_active = 1
            GROUP BY cc.id
            ORDER BY cc.sort_order ASC
        ";
        $stmt = $db->query($sql);
        $categories = [];
        
        while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
            $category = new CatalogCategory();
            $category->fill($row);
            $category->products_count = $row['products_count'];
            $categories[] = $category;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'categories/index-' . $template;
        
        $this->render($viewName, [
            'categories' => $categories,
            'success' => $_SESSION['success'] ?? null,
            'error' => $_SESSION['error'] ?? null
        ]);
        
        // Clear session messages after rendering
        unset($_SESSION['success']);
        unset($_SESSION['error']);
    }
    
    /**
     * Show create form
     */
    public function create()
    {
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'categories/create-' . $template;
        
        $this->render($viewName, [
            'errors' => $_SESSION['errors'] ?? [],
            'old' => $_SESSION['old'] ?? []
        ]);
        
        // Clear session data after rendering
        unset($_SESSION['errors']);
        unset($_SESSION['old']);
    }
    
    /**
     * Store new category
     */
    public function store()
    {
        $data = $_POST;
        
        // Validate
        $errors = $this->validateCategory($data);
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['old'] = $data;
            Flight::redirect('/fit/public/products/categories/create');
            return;
        }
        
        try {
            // Get next sort order
            $db = CatalogCategory::db();
            $stmt = $db->query("SELECT MAX(sort_order) as max_order FROM catalog_categories");
            $result = $stmt->fetch(\PDO::FETCH_ASSOC);
            $nextOrder = ($result['max_order'] ?? 0) + 1;
            
            // Prepare create data
            $createData = [
                'name' => json_encode(['fr' => $data['name'], 'en' => $data['name'], 'de' => $data['name']]),
                'slug' => $this->generateSlug($data['name']),
                'description' => $data['description'] ?? null,
                'icon' => $data['icon'] ?? null,
                'color' => $data['color'] ?? '#007bff',
                'sort_order' => $data['sort_order'] ?? $nextOrder,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];
            
            // Set parent_id to null if not provided or empty
            if (isset($data['parent_id']) && !empty($data['parent_id'])) {
                $createData['parent_id'] = $data['parent_id'];
            } else {
                $createData['parent_id'] = null;
            }
            
            // Create category
            $category = CatalogCategory::create($createData);
            
            $_SESSION['success'] = 'Category created successfully';
            Flight::redirect($this->url('/products/categories'));
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Error creating category: ' . $e->getMessage();
            Flight::redirect($this->url('/products/categories/create'));
        }
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $category = CatalogCategory::find($id);
        if (!$category) {
            Flight::halt(404, 'Category not found');
            return;
        }
        
        // Get template preference
        $template = $this->getTemplate();
        $viewName = 'categories/edit-' . $template;
        
        $this->render($viewName, [
            'category' => $category,
            'errors' => $_SESSION['errors'] ?? [],
            'old' => $_SESSION['old'] ?? []
        ]);
        
        // Clear session data after rendering
        unset($_SESSION['errors']);
        unset($_SESSION['old']);
    }
    
    /**
     * Update category
     */
    public function update($id)
    {
        $category = CatalogCategory::find($id);
        if (!$category) {
            Flight::halt(404, 'Category not found');
            return;
        }
        
        $data = $_POST;
        
        // Validate
        $errors = $this->validateCategory($data, $id);
        if (!empty($errors)) {
            $_SESSION['errors'] = $errors;
            $_SESSION['old'] = $data;
            Flight::redirect($this->url('/products/categories/' . $id . '/edit'));
            return;
        }
        
        try {
            // Prepare update data
            $updateData = [
                'name' => json_encode(['fr' => $data['name'], 'en' => $data['name'], 'de' => $data['name']]),
                'slug' => $this->generateSlug($data['name']),
                'description' => $data['description'] ?? null,
                'icon' => $data['icon'] ?? null,
                'color' => $data['color'] ?? '#007bff',
                'sort_order' => $data['sort_order'] ?? $category->sort_order,
                'is_active' => isset($data['is_active']) ? 1 : 0
            ];
            
            // Only include parent_id if it's explicitly set in the form
            if (isset($data['parent_id'])) {
                $updateData['parent_id'] = empty($data['parent_id']) ? null : $data['parent_id'];
            }
            
            // Update category
            $category->update($updateData);
            
            $_SESSION['success'] = 'Category updated successfully';
            Flight::redirect($this->url('/products/categories'));
            
        } catch (\Exception $e) {
            $_SESSION['error'] = 'Error updating category: ' . $e->getMessage();
            Flight::redirect($this->url('/products/categories/' . $id . '/edit'));
        }
    }
    
    /**
     * Delete category
     */
    public function destroy($id)
    {
        $category = CatalogCategory::find($id);
        if (!$category) {
            Flight::json(['success' => false, 'message' => 'Category not found'], 404);
            return;
        }
        
        // Check if category has products
        $db = CatalogCategory::db();
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM catalog_items WHERE category_id = :id");
        $stmt->execute(['id' => $id]);
        $hasProducts = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
        
        if ($hasProducts) {
            Flight::json(['success' => false, 'message' => 'Cannot delete category with products'], 400);
            return;
        }
        
        try {
            $category->delete();
            Flight::json(['success' => true, 'message' => 'Category deleted successfully']);
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error deleting category'], 500);
        }
    }
    
    /**
     * Reorder categories
     */
    public function reorder()
    {
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($data['order']) || !is_array($data['order'])) {
            Flight::json(['success' => false, 'message' => 'Invalid order data'], 400);
            return;
        }
        
        $db = CatalogCategory::db();
        $db->beginTransaction();
        
        try {
            foreach ($data['order'] as $index => $categoryId) {
                $stmt = $db->prepare("UPDATE catalog_categories SET sort_order = :order WHERE id = :id");
                $stmt->execute(['order' => $index + 1, 'id' => $categoryId]);
            }
            
            $db->commit();
            Flight::json(['success' => true, 'message' => 'Categories reordered successfully']);
            
        } catch (\Exception $e) {
            $db->rollBack();
            Flight::json(['success' => false, 'message' => 'Error reordering categories'], 500);
        }
    }
    
    /**
     * Validate category data
     */
    private function validateCategory($data, $id = null)
    {
        $errors = [];
        
        if (empty($data['name'])) {
            $errors['name'] = 'Category name is required';
        } else {
            // Check uniqueness
            $db = CatalogCategory::db();
            $sql = "SELECT COUNT(*) as count FROM catalog_categories WHERE name = :name";
            $params = ['name' => $data['name']];
            
            if ($id) {
                $sql .= " AND id != :id";
                $params['id'] = $id;
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            if ($stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0) {
                $errors['name'] = 'Category name already exists';
            }
        }
        
        return $errors;
    }
    
    /**
     * Generate URL-friendly slug
     */
    private function generateSlug($name)
    {
        $slug = strtolower(trim($name));
        $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        return trim($slug, '-');
    }
    
    /**
     * Bulk delete categories
     */
    public function bulkDelete()
    {
        try {
            // Get JSON data
            $data = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($data['ids']) || !is_array($data['ids']) || empty($data['ids'])) {
                Flight::json(['success' => false, 'message' => 'No categories selected'], 400);
                return;
            }
            
            $db = CatalogCategory::db();
            $db->beginTransaction();
            
            try {
                $deletedCount = 0;
                $skippedCount = 0;
                $errors = [];
                
                foreach ($data['ids'] as $id) {
                    $category = CatalogCategory::find($id);
                    if (!$category) continue;
                    
                    // Check if category has products
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM catalog_items WHERE category_id = :id");
                    $stmt->execute(['id' => $id]);
                    $hasProducts = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
                    
                    if ($hasProducts) {
                        $skippedCount++;
                        $errors[] = "Category '{$category->getDisplayName()}' has products";
                        continue;
                    }
                    
                    // Check if category has children
                    $stmt = $db->prepare("SELECT COUNT(*) as count FROM catalog_categories WHERE parent_id = :id");
                    $stmt->execute(['id' => $id]);
                    $hasChildren = $stmt->fetch(\PDO::FETCH_ASSOC)['count'] > 0;
                    
                    if ($hasChildren) {
                        $skippedCount++;
                        $errors[] = "Category '{$category->getDisplayName()}' has subcategories";
                        continue;
                    }
                    
                    // Delete the category
                    $category->delete();
                    $deletedCount++;
                }
                
                $db->commit();
                
                $message = '';
                if ($deletedCount > 0) {
                    $message .= "$deletedCount categories deleted. ";
                }
                if ($skippedCount > 0) {
                    $message .= "$skippedCount categories skipped. ";
                    if (!empty($errors)) {
                        $message .= "Reasons: " . implode(', ', array_slice($errors, 0, 3));
                        if (count($errors) > 3) {
                            $message .= " and " . (count($errors) - 3) . " more...";
                        }
                    }
                }
                
                Flight::json(['success' => true, 'message' => $message]);
                
            } catch (\Exception $e) {
                $db->rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            Flight::json(['success' => false, 'message' => 'Error deleting categories: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * Export categories
     */
    public function export()
    {
        try {
            $ids = $_POST['ids'] ?? [];
            
            if (empty($ids)) {
                Flight::halt(400, 'No categories selected');
                return;
            }
            
            // Get categories
            $placeholders = str_repeat('?,', count($ids) - 1) . '?';
            $db = CatalogCategory::db();
            $sql = "
                SELECT cc.*, COUNT(ci.id) as products_count,
                       pcc.name as parent_name
                FROM catalog_categories cc
                LEFT JOIN catalog_items ci ON cc.id = ci.category_id
                LEFT JOIN catalog_categories pcc ON cc.parent_id = pcc.id
                WHERE cc.id IN ($placeholders)
                GROUP BY cc.id
                ORDER BY cc.sort_order ASC
            ";
            $stmt = $db->prepare($sql);
            $stmt->execute($ids);
            
            // Set headers for CSV download with UTF-8 encoding
            header('Content-Type: text/csv; charset=UTF-8');
            header('Content-Disposition: attachment; filename="categories_export_' . date('Y-m-d_His') . '.csv"');
            
            // Open output stream
            $output = fopen('php://output', 'w');
            
            // Write UTF-8 BOM to ensure Excel recognizes UTF-8 encoding
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Write CSV headers
            fputcsv($output, [
                'ID',
                'Name',
                'Slug',
                'Description',
                'Parent Category',
                'Icon',
                'Color',
                'Sort Order',
                'Products Count',
                'Status'
            ]);
            
            // Write data
            while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
                $category = new CatalogCategory();
                $category->fill($row);
                
                // Parse parent name if JSON
                $parentName = '';
                if ($row['parent_name']) {
                    if (strpos($row['parent_name'], '{') === 0) {
                        $names = json_decode($row['parent_name'], true);
                        if ($names) {
                            $parentName = $names[$_SESSION['user_language'] ?? 'fr'] ?? $names['fr'] ?? $names['en'] ?? reset($names);
                        }
                    } else {
                        $parentName = $row['parent_name'];
                    }
                }
                
                fputcsv($output, [
                    $category->id,
                    $category->getDisplayName(),
                    $category->slug,
                    $category->description,
                    $parentName,
                    $category->icon,
                    $category->color,
                    $category->sort_order,
                    $row['products_count'],
                    $category->is_active ? 'Active' : 'Inactive'
                ]);
            }
            
            fclose($output);
            exit;
            
        } catch (\Exception $e) {
            Flight::halt(500, 'Error exporting categories: ' . $e->getMessage());
        }
    }
}