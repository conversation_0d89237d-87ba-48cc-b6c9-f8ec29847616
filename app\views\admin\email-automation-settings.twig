{% extends 'base-modern.twig' %}

{% block title %}{{ __('email.automation.title') }} - {{ parent() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-envelope-open-text mr-2"></i>
                        {{ __('email.automation.title') }}
                    </h3>
                    <div class="card-tools">
                        <span class="text-muted">{{ __('email.automation.description') }}</span>
                    </div>
                </div>
                <div class="card-body">
                    <form id="emailAutomationForm" method="POST" action="{{ url('/admin/email-automation/save') }}">
                        {{ csrf_field() }}
                        
                        <!-- Master Enable/Disable -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-outline card-primary">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-power-off mr-2"></i>
                                            {{ __('email.automation.master_control') }}
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-check form-switch">
                                            <input type="checkbox" class="form-check-input" id="masterEnable" 
                                                   name="settings[enabled]" value="1" 
                                                   {% if settings.enabled %}checked{% endif %}>
                                            <label class="form-check-label" for="masterEnable">
                                                <strong>{{ __('email.automation.enable_automatic_sending') }}</strong>
                                                <br>
                                                <small class="text-muted">{{ __('email.automation.master_description') }}</small>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Invoice Type Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-outline card-info">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-file-invoice mr-2"></i>
                                            {{ __('email.automation.invoice_settings') }}
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th style="width: 20%">{{ __('email.automation.invoice_type') }}</th>
                                                        <th class="text-center" style="width: 15%">{{ __('email.automation.auto_send') }}</th>
                                                        <th style="width: 20%">{{ __('email.automation.delay') }}</th>
                                                        <th style="width: 15%">{{ __('email.automation.custom_hours') }}</th>
                                                        <th style="width: 30%">{{ __('email.automation.email_template') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for type in invoiceTypes %}
                                                    {% set typeSettings = settings.invoice_types[type.id] ?? {} %}
                                                    <tr data-invoice-type="{{ type.id }}">
                                                        <td>
                                                            <span class="badge" style="background-color: {{ type.color }}">
                                                                {{ type.name }}
                                                            </span>
                                                        </td>
                                                        <td class="text-center">
                                                            <div class="form-check form-switch">
                                                                <input type="checkbox" class="form-check-input invoice-type-enable" 
                                                                       id="invoiceType{{ type.id }}Enable"
                                                                       name="settings[invoice_types][{{ type.id }}][enabled]" 
                                                                       value="1"
                                                                       {% if typeSettings.enabled %}checked{% endif %}>
                                                                <label class="form-check-label" for="invoiceType{{ type.id }}Enable">
                                                                    <span class="sr-only">{{ __('email.automation.enable_for') }} {{ type.name }}</span>
                                                                </label>
                                                            </div>
                                                        </td>
                                                        <td>
                                                            <select class="form-control form-control-sm delay-select" 
                                                                    name="settings[invoice_types][{{ type.id }}][delay]"
                                                                    data-type-id="{{ type.id }}">
                                                                {% for value, label in delayOptions %}
                                                                <option value="{{ value }}" {% if typeSettings.delay == value %}selected{% endif %}>
                                                                    {{ label }}
                                                                </option>
                                                                {% endfor %}
                                                            </select>
                                                        </td>
                                                        <td>
                                                            <input type="number" class="form-control form-control-sm custom-delay-input" 
                                                                   name="settings[invoice_types][{{ type.id }}][custom_delay]"
                                                                   value="{{ typeSettings.custom_delay ?? 0 }}"
                                                                   min="0" max="720"
                                                                   style="{% if typeSettings.delay != 'custom' %}display: none;{% endif %}"
                                                                   data-type-id="{{ type.id }}">
                                                        </td>
                                                        <td>
                                                            <select class="form-control form-control-sm template-select" 
                                                                    name="settings[invoice_types][{{ type.id }}][template_id]">
                                                                <option value="">{{ __('email.automation.select_template') }}</option>
                                                                {% for templateType, templates in emailTemplates %}
                                                                    {% if templateType == 'invoice' or templateType == 'general' %}
                                                                    <optgroup label="{{ __('email.types.' ~ templateType) }}">
                                                                        {% for template in templates %}
                                                                        <option value="{{ template.id }}" 
                                                                                {% if typeSettings.template_id == template.id %}selected{% endif %}>
                                                                            {{ template.name }}
                                                                        </option>
                                                                        {% endfor %}
                                                                    </optgroup>
                                                                    {% endif %}
                                                                {% endfor %}
                                                            </select>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Payment Reminder Settings -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-outline card-warning">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-bell mr-2"></i>
                                            {{ __('email.automation.payment_reminders') }}
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="form-group mb-3">
                                            <div class="form-check form-switch">
                                                <input type="checkbox" class="form-check-input" id="remindersEnable" 
                                                       name="settings[payment_reminders][enabled]" value="1"
                                                       {% if settings.payment_reminders.enabled %}checked{% endif %}>
                                                <label class="form-check-label" for="remindersEnable">
                                                    <strong>{{ __('email.automation.enable_payment_reminders') }}</strong>
                                                    <br>
                                                    <small class="text-muted">{{ __('email.automation.reminders_description') }}</small>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div id="reminderSettings" {% if not settings.payment_reminders.enabled %}style="display: none;"{% endif %}>
                                            <div class="table-responsive">
                                                <table class="table table-sm">
                                                    <thead>
                                                        <tr>
                                                            <th>{{ __('email.automation.reminder_level') }}</th>
                                                            <th>{{ __('email.automation.days_after_due') }}</th>
                                                            <th>{{ __('email.automation.email_template') }}</th>
                                                            <th class="text-center">{{ __('common.actions') }}</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="remindersList">
                                                        {% for reminder in settings.payment_reminders.reminders %}
                                                        <tr class="reminder-row">
                                                            <td>
                                                                <span class="badge badge-warning">
                                                                    {{ __('email.automation.reminder') }} {{ loop.index }}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <input type="number" class="form-control form-control-sm" 
                                                                       name="settings[payment_reminders][reminders][{{ loop.index0 }}][days]"
                                                                       value="{{ reminder.days }}" min="1" max="365">
                                                            </td>
                                                            <td>
                                                                <select class="form-control form-control-sm" 
                                                                        name="settings[payment_reminders][reminders][{{ loop.index0 }}][template_id]">
                                                                    <option value="">{{ __('email.automation.select_template') }}</option>
                                                                    {% for templateType, templates in emailTemplates %}
                                                                        {% if templateType == 'payment_reminder' or templateType == 'general' %}
                                                                        <optgroup label="{{ __('email.types.' ~ templateType) }}">
                                                                            {% for template in templates %}
                                                                            <option value="{{ template.id }}" 
                                                                                    {% if reminder.template_id == template.id %}selected{% endif %}>
                                                                                {{ template.name }}
                                                                            </option>
                                                                            {% endfor %}
                                                                        </optgroup>
                                                                        {% endif %}
                                                                    {% endfor %}
                                                                </select>
                                                            </td>
                                                            <td class="text-center">
                                                                <button type="button" class="btn btn-sm btn-danger remove-reminder">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </td>
                                                        </tr>
                                                        {% endfor %}
                                                    </tbody>
                                                </table>
                                            </div>
                                            
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <button type="button" class="btn btn-sm btn-success" id="addReminder">
                                                        <i class="fas fa-plus"></i> {{ __('email.automation.add_reminder') }}
                                                    </button>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label for="maxReminders">{{ __('email.automation.max_reminders') }}</label>
                                                        <input type="number" class="form-control form-control-sm" id="maxReminders"
                                                               name="settings[payment_reminders][max_reminders]"
                                                               value="{{ settings.payment_reminders.max_reminders }}"
                                                               min="1" max="10">
                                                        <small class="text-muted">{{ __('email.automation.max_reminders_help') }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Test Email Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="card card-outline card-success">
                                    <div class="card-header">
                                        <h5 class="mb-0">
                                            <i class="fas fa-vial mr-2"></i>
                                            {{ __('email.automation.test_section') }}
                                        </h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="testTemplate">{{ __('email.automation.select_template_test') }}</label>
                                                    <select class="form-control" id="testTemplate">
                                                        <option value="">{{ __('email.automation.select_template') }}</option>
                                                        {% for templateType, templates in emailTemplates %}
                                                        <optgroup label="{{ __('email.types.' ~ templateType) }}">
                                                            {% for template in templates %}
                                                            <option value="{{ template.id }}">{{ template.name }}</option>
                                                            {% endfor %}
                                                        </optgroup>
                                                        {% endfor %}
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label for="testEmail">{{ __('email.automation.test_email_address') }}</label>
                                                    <input type="email" class="form-control" id="testEmail" 
                                                           placeholder="{{ __('email.automation.enter_email') }}"
                                                           value="{{ user.email }}">
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>&nbsp;</label>
                                                    <div>
                                                        <button type="button" class="btn btn-primary" id="sendTestEmail">
                                                            <i class="fas fa-paper-plane"></i> {{ __('email.automation.send_test') }}
                                                        </button>
                                                        <button type="button" class="btn btn-info" id="previewTemplate">
                                                            <i class="fas fa-eye"></i> {{ __('email.automation.preview') }}
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card-footer">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> {{ __('common.save_changes') }}
                                    </button>
                                    <a href="{{ url('/admin') }}" class="btn btn-default">
                                        <i class="fas fa-times"></i> {{ __('common.cancel') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Preview Modal -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ __('email.automation.template_preview') }}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="previewContent">
                    <div class="text-center">
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{ __('common.close') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script src="{{ asset('js/email-automation-settings.js') }}"></script>
{% endblock %}