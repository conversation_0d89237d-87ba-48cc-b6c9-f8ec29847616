<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Dropdown Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/fit/public/css/table-dropdown-fix.css">
    <style>
        body { padding: 20px; }
        .test-info { margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1>Invoice Dropdown Menu Test</h1>
        
        <div class="alert alert-info test-info">
            <h5>Testing Simplified Dropdown Fix</h5>
            <ul>
                <li>CSS-only solution in <code>table-dropdown-fix.css</code></li>
                <li>Minimal JavaScript in <code>dropdown-fix-simple.js</code></li>
                <li>No complex repositioning that causes flickering</li>
            </ul>
        </div>
        
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Invoice #</th>
                                <th>Client</th>
                                <th>Date</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th class="text-end">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for i in 1..10 %}
                            <tr>
                                <td>INV-2024-000{{ i }}</td>
                                <td>Client {{ i }}</td>
                                <td>2024-01-{{ i|format_number(2, '0', '') }}</td>
                                <td>€{{ (1000 * i)|number_format(2, ',', ' ') }}</td>
                                <td><span class="badge bg-success">Paid</span></td>
                                <td class="text-end">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="bi bi-three-dots-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            <li><a class="dropdown-item" href="#"><i class="bi bi-eye"></i> View</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="bi bi-pencil"></i> Edit</a></li>
                                            <li><a class="dropdown-item" href="#"><i class="bi bi-download"></i> Download PDF</a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><a class="dropdown-item text-danger" href="#"><i class="bi bi-trash"></i> Delete</a></li>
                                        </ul>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5>✅ Expected Behavior</h5>
            <ul>
                <li>Dropdown menus should open smoothly without flickering</li>
                <li>Menus should stay open when hovering over them</li>
                <li>Menus should be positioned to the left of the button (dropdown-menu-end)</li>
                <li>No rapid repositioning or disappearing</li>
            </ul>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/fit/public/js/dropdown-fix-simple.js"></script>
</body>
</html>