<?php

namespace Tests;

/**
 * Simple Test Runner for Web Interface
 * Runs tests without PHPUnit dependency
 */
class SimpleTestRunner
{
    private $passed = 0;
    private $failed = 0;
    private $errors = [];
    
    public function runTestClass($className, $classFile)
    {
        try {
            // Load test bootstrap if not already loaded
            if (!class_exists('Flight')) {
                require_once __DIR__ . '/bootstrap-test.php';
            }
            
            // Include test file
            require_once $classFile;
            
            // Check if class exists
            if (!class_exists($className)) {
                throw new \Exception("Test class {$className} not found");
            }
            
            // Create instance
            $testInstance = new $className();
            
            // Run setUpBeforeClass if exists
            if (method_exists($className, 'setUpBeforeClass')) {
                $className::setUpBeforeClass();
            }
            
            // Get all test methods
            $methods = get_class_methods($testInstance);
            $testMethods = array_filter($methods, function($method) {
                return strpos($method, 'test') === 0;
            });
            
            $results = [];
            
            foreach ($testMethods as $method) {
                try {
                    // Run setUp if exists
                    if (method_exists($testInstance, 'setUp')) {
                        $testInstance->setUp();
                    }
                    
                    // Run test method
                    $testInstance->$method();
                    
                    // Run tearDown if exists
                    if (method_exists($testInstance, 'tearDown')) {
                        $testInstance->tearDown();
                    }
                    
                    $results[$method] = ['status' => 'passed', 'message' => ''];
                    $this->passed++;
                } catch (\Exception $e) {
                    $results[$method] = ['status' => 'failed', 'message' => $e->getMessage()];
                    $this->failed++;
                    $this->errors[] = "{$className}::{$method} - " . $e->getMessage();
                }
            }
            
            // Run tearDownAfterClass if exists
            if (method_exists($className, 'tearDownAfterClass')) {
                $className::tearDownAfterClass();
            }
            
            return $results;
            
        } catch (\Exception $e) {
            $this->failed++;
            $this->errors[] = "Failed to run {$className}: " . $e->getMessage();
            return ['error' => $e->getMessage()];
        }
    }
    
    public function getPassed()
    {
        return $this->passed;
    }
    
    public function getFailed()
    {
        return $this->failed;
    }
    
    public function getErrors()
    {
        return $this->errors;
    }
    
    public function reset()
    {
        $this->passed = 0;
        $this->failed = 0;
        $this->errors = [];
    }
}

/**
 * Base TestCase class for compatibility
 */
if (!class_exists('PHPUnit\\Framework\\TestCase')) {
    abstract class TestCase
    {
        protected function assertEquals($expected, $actual, $message = '')
        {
            if ($expected != $actual) {
                throw new \Exception($message ?: "Expected {$expected}, got {$actual}");
            }
        }
        
        protected function assertNotNull($value, $message = '')
        {
            if ($value === null) {
                throw new \Exception($message ?: "Expected non-null value, got null");
            }
        }
        
        protected function assertNull($value, $message = '')
        {
            if ($value !== null) {
                throw new \Exception($message ?: "Expected null, got " . print_r($value, true));
            }
        }
        
        protected function assertTrue($condition, $message = '')
        {
            if (!$condition) {
                throw new \Exception($message ?: "Expected true, got false");
            }
        }
        
        protected function assertFalse($condition, $message = '')
        {
            if ($condition) {
                throw new \Exception($message ?: "Expected false, got true");
            }
        }
        
        protected function assertCount($expected, $array, $message = '')
        {
            $actual = is_array($array) || $array instanceof \Countable ? count($array) : 0;
            if ($expected != $actual) {
                throw new \Exception($message ?: "Expected count {$expected}, got {$actual}");
            }
        }
        
        protected function assertGreaterThan($expected, $actual, $message = '')
        {
            if ($actual <= $expected) {
                throw new \Exception($message ?: "Expected {$actual} to be greater than {$expected}");
            }
        }
        
        protected function assertGreaterThanOrEqual($expected, $actual, $message = '')
        {
            if ($actual < $expected) {
                throw new \Exception($message ?: "Expected {$actual} to be greater than or equal to {$expected}");
            }
        }
        
        protected function assertLessThan($expected, $actual, $message = '')
        {
            if ($actual >= $expected) {
                throw new \Exception($message ?: "Expected {$actual} to be less than {$expected}");
            }
        }
        
        protected function assertLessThanOrEqual($expected, $actual, $message = '')
        {
            if ($actual > $expected) {
                throw new \Exception($message ?: "Expected {$actual} to be less than or equal to {$expected}");
            }
        }
        
        protected function assertContains($needle, $haystack, $message = '')
        {
            if (is_array($haystack)) {
                if (!in_array($needle, $haystack)) {
                    throw new \Exception($message ?: "Array does not contain expected value");
                }
            } else {
                if (strpos($haystack, $needle) === false) {
                    throw new \Exception($message ?: "String does not contain expected value");
                }
            }
        }
        
        protected function assertStringContainsString($needle, $haystack, $message = '')
        {
            if (strpos($haystack, $needle) === false) {
                throw new \Exception($message ?: "String '{$haystack}' does not contain '{$needle}'");
            }
        }
        
        protected function assertStringStartsWith($prefix, $string, $message = '')
        {
            if (strpos($string, $prefix) !== 0) {
                throw new \Exception($message ?: "String '{$string}' does not start with '{$prefix}'");
            }
        }
        
        protected function assertIsArray($value, $message = '')
        {
            if (!is_array($value)) {
                throw new \Exception($message ?: "Expected array, got " . gettype($value));
            }
        }
        
        protected function assertIsString($value, $message = '')
        {
            if (!is_string($value)) {
                throw new \Exception($message ?: "Expected string, got " . gettype($value));
            }
        }
        
        protected function assertNotEquals($expected, $actual, $message = '')
        {
            if ($expected == $actual) {
                throw new \Exception($message ?: "Expected values to be different, but they are equal");
            }
        }
        
        protected function assertEmpty($value, $message = '')
        {
            if (!empty($value)) {
                throw new \Exception($message ?: "Expected empty value, got " . print_r($value, true));
            }
        }
        
        protected function assertNotEmpty($value, $message = '')
        {
            if (empty($value)) {
                throw new \Exception($message ?: "Expected non-empty value");
            }
        }
    }
}

// Add namespace alias for compatibility
if (!class_exists('PHPUnit\\Framework\\TestCase')) {
    class_alias('Tests\\TestCase', 'PHPUnit\\Framework\\TestCase');
}