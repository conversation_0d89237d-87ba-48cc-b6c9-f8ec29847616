<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== RESETTING INVOICE SEQUENCE ===\n\n";

try {
    $db = Flight::db();
    
    // Show current state
    echo "1. Current sequence state:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025 
        ORDER BY updated_at DESC
    ");
    $sequences = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo sprintf("- Type: %d, Year: %d, Month: %s, Last: %d, Updated: %s\n",
            $seq['document_type_id'],
            $seq['year'],
            $seq['month'] ?? 'NULL',
            $seq['last_number'],
            $seq['updated_at']
        );
    }
    
    // Reset to 185 so next invoice will be 186
    echo "\n2. Resetting sequence to 185...\n";
    $stmt = $db->prepare("
        UPDATE document_sequences 
        SET last_number = 185, updated_at = NOW()
        WHERE document_type_id = 1 
        AND year = 2025 
        AND month IS NULL
    ");
    $result = $stmt->execute();
    
    if ($result) {
        echo "✓ Success! Sequence reset to 185\n";
        echo "  Next invoice will be: FAC-2025-0186\n";
    } else {
        echo "✗ Failed to reset sequence\n";
    }
    
    // Verify the change
    echo "\n3. New sequence state:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025 AND month IS NULL
    ");
    $seq = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($seq) {
        echo sprintf("- Last number: %d (Next will be: FAC-2025-%04d)\n", 
            $seq['last_number'],
            $seq['last_number'] + 1
        );
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}