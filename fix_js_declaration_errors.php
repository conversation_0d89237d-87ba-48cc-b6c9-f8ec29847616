<?php
/**
 * Fix JavaScript declaration errors in retrocession invoice creation
 */

echo "<pre>";
echo "=== Fixing JavaScript Declaration Errors ===\n\n";

$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($createFile);

// Create backup
$backupFile = $createFile . '.backup.' . date('YmdHis');
file_put_contents($backupFile, $content);
echo "✓ Created backup: " . basename($backupFile) . "\n\n";

// Fix 1: Find the displayBillableInfo function and fix the VAT section
echo "Fixing VAT display section...\n";

// Find the VAT info column section and fix it properly
$vatSectionStart = strpos($content, '// VAT info column');
if ($vatSectionStart !== false) {
    // Find the end of this section (double closing div)
    $searchFrom = $vatSectionStart;
    $vatSectionEnd = strpos($content, "html += '</div>';\n            html += '</div>';", $searchFrom);
    
    if ($vatSectionEnd !== false) {
        $vatSectionEnd += strlen("html += '</div>';\n            html += '</div>';");
        
        // Extract the section
        $originalSection = substr($content, $vatSectionStart, $vatSectionEnd - $vatSectionStart);
        
        // Create the fixed section
        $fixedSection = "// VAT info column
            html += '<div class=\"col-md-6\">';
            
            // Check if this is a retrocession invoice at the beginning
            const invTypeSelect = document.getElementById('invoice_type_id');
            const selectedOpt = invTypeSelect ? invTypeSelect.options[invTypeSelect.selectedIndex] : null;
            const typePrefix = selectedOpt ? selectedOpt.getAttribute('data-prefix') : '';
            const isRetrocessionInvoice = window.location.search.includes('type=retrocession') || 
                                        (typePrefix && typePrefix.startsWith('RET'));
            
            // VAT number
            if (!isRetrocessionInvoice && userData.vat_number) {
                html += '<div class=\"mb-2\">';
                html += '<strong>{{ __(\"users.vat_number\") }}:</strong> ' + userData.vat_number;
                html += '</div>';
            }
            
            // Intracommunity VAT status
            if (!isRetrocessionInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {
                const vatNumber = userData.vat_intercommunautaire ? userData.vat_intercommunautaire.trim() : '';
                const isLuxembourgVat = vatNumber.toUpperCase().startsWith('LU');
                
                if (isLuxembourgVat) {
                    // Luxembourg VAT - standard VAT applies
                    html += '<div class=\"alert alert-info py-2 px-3 mb-0\">';
                    html += '<i class=\"bi bi-info-circle me-2\"></i>';
                    html += '<strong>{{ __(\"users.luxembourg_vat\") | default(\"TVA Luxembourg\") }}</strong><br>';
                    html += '<small>' + vatNumber + '</small>';
                    html += '</div>';
                } else {
                    // Non-Luxembourg EU VAT - intracommunity rules apply
                    html += '<div class=\"alert alert-success py-2 px-3 mb-0\">';
                    html += '<i class=\"bi bi-check-circle me-2\"></i>';
                    html += '<strong>{{ __(\"users.intracommunity_vat_applicable\") }}</strong><br>';
                    html += '<small>' + vatNumber + '</small>';
                    html += '</div>';
                }
            } else if (!isRetrocessionInvoice && userData.vat_number) {
                // Has VAT number but not intracommunity
                html += '<div class=\"text-muted small\">';
                html += '<i class=\"bi bi-info-circle me-1\"></i>';
                html += '{{ __(\"users.standard_vat_applies\") }}';
                html += '</div>';
            }
            
            html += '</div>';
            html += '</div>';";
        
        // Replace the section
        $content = substr($content, 0, $vatSectionStart) . $fixedSection . substr($content, $vatSectionEnd);
        echo "✓ Fixed VAT display section\n";
    } else {
        echo "⚠ Could not find end of VAT section\n";
    }
} else {
    echo "⚠ Could not find VAT info column section\n";
}

// Fix 2: Remove any remaining isRetroInvoice references
$content = str_replace('!isRetroInvoice', '!isRetrocessionInvoice', $content);
$content = str_replace('isRetroInvoice', 'isRetrocessionInvoice', $content);
echo "✓ Fixed variable name consistency\n";

// Save the fixed content
file_put_contents($createFile, $content);

echo "\n✅ JavaScript declaration errors fixed!\n\n";
echo "The fix:\n";
echo "1. Moved isRetrocessionInvoice declaration to the beginning of the VAT section\n";
echo "2. Fixed variable name consistency (isRetroInvoice -> isRetrocessionInvoice)\n";
echo "3. Avoided duplicate const declarations by using unique names\n";
echo "4. Ensured all variables are properly scoped\n";

echo "\nNow the retrocession invoice creation should work without JavaScript errors.\n";
echo "</pre>";