/**
 * Diagnostic script for live search issues
 * Run this in browser console on the invoice create page
 */

console.log('=== LIVE SEARCH DIAGNOSTIC ===');

// 1. Check if scripts are loaded
console.log('\n1. Script Loading:');
console.log('- ProductLiveSearch class exists:', typeof ProductLiveSearch !== 'undefined');
console.log('- productLiveSearch instance exists:', typeof productLiveSearch !== 'undefined');
console.log('- InvoiceProductSelection exists:', typeof InvoiceProductSelection !== 'undefined');

// 2. Check DOM elements
console.log('\n2. DOM Elements:');
const descInputs = document.querySelectorAll('.item-description');
console.log('- Description inputs found:', descInputs.length);
console.log('- itemsBody exists:', document.getElementById('itemsBody') !== null);

// 3. Check if inputs have live search attached
console.log('\n3. Live Search Attachment:');
descInputs.forEach((input, index) => {
    console.log(`- Input ${index}:`, {
        attached: input.dataset.liveSearchAttached === 'true',
        value: input.value,
        name: input.name,
        hasEventListeners: input._liveSearchListeners ? 'Yes' : 'No'
    });
});

// 4. Try manual initialization
console.log('\n4. Manual Initialization:');
if (typeof productLiveSearch !== 'undefined') {
    console.log('- Attempting manual attachment...');
    productLiveSearch.attachToExistingInputs();
    
    // Check again
    const recheck = document.querySelectorAll('.item-description');
    let attached = 0;
    recheck.forEach(input => {
        if (input.dataset.liveSearchAttached === 'true') attached++;
    });
    console.log(`- Result: ${attached} of ${recheck.length} inputs now have live search`);
}

// 5. Test search functionality
console.log('\n5. Test Search:');
console.log('- API endpoint:', window.base_url + '/api/products/search');
console.log('- Try typing in a description field to test');

// 6. Event listener test
console.log('\n6. Testing Event Listeners:');
const testInput = descInputs[0];
if (testInput) {
    // Create a test event
    const inputEvent = new Event('input', { bubbles: true });
    testInput.value = 'test';
    console.log('- Dispatching test input event...');
    testInput.dispatchEvent(inputEvent);
    console.log('- Check if dropdown appeared');
}

console.log('\n=== END DIAGNOSTIC ===');