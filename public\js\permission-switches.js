/**
 * Permission Switches Enhancement
 * Converts checkboxes to modern toggle switches with animations
 */

class PermissionSwitches {
    constructor() {
        this.init();
    }
    
    init() {
        // Convert existing checkboxes to switches
        this.convertCheckboxesToSwitches();
        
        // Setup event listeners
        this.setupEventListeners();
        
        // Initialize indeterminate states
        this.updateIndeterminateStates();
    }
    
    convertCheckboxesToSwitches() {
        // Find all permission checkboxes that aren't already converted
        const checkboxes = document.querySelectorAll('.permissions-table input[type="checkbox"]:not(.switch-converted), .permission-checkbox:not(.switch-converted)');
        
        checkboxes.forEach(checkbox => {
            // Skip if already in a switch
            if (checkbox.closest('.permission-switch')) {
                return;
            }
            
            // Create switch wrapper
            const switchWrapper = document.createElement('label');
            switchWrapper.className = 'permission-switch';
            
            // Add size class based on context
            if (checkbox.classList.contains('permission-toggle')) {
                switchWrapper.classList.add('switch-sm');
            }
            
            // Add special class for special permissions
            if (checkbox.name && checkbox.name.includes('[special]')) {
                switchWrapper.classList.add('switch-special');
            }
            
            // Create slider
            const slider = document.createElement('span');
            slider.className = 'slider';
            
            // Wrap checkbox
            checkbox.parentNode.insertBefore(switchWrapper, checkbox);
            switchWrapper.appendChild(checkbox);
            switchWrapper.appendChild(slider);
            
            // Mark as converted
            checkbox.classList.add('switch-converted');
        });
    }
    
    setupEventListeners() {
        // Handle switch changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('.permission-switch input')) {
                this.handleSwitchChange(e.target);
            }
        });
        
        // Handle column toggles
        document.querySelectorAll('.permission-toggle').forEach(toggle => {
            toggle.addEventListener('change', (e) => {
                this.handleColumnToggle(e.target);
            });
        });
        
        // Add click feedback
        document.addEventListener('click', (e) => {
            if (e.target.matches('.permission-switch, .permission-switch *')) {
                const switchEl = e.target.closest('.permission-switch');
                this.addClickFeedback(switchEl);
            }
        });
    }
    
    handleSwitchChange(checkbox) {
        const switchWrapper = checkbox.closest('.permission-switch');
        
        // Add visual feedback
        switchWrapper.classList.add('batch-updating');
        
        // Haptic feedback on mobile
        if ('vibrate' in navigator) {
            navigator.vibrate(10);
        }
        
        // Remove feedback class after animation
        setTimeout(() => {
            switchWrapper.classList.remove('batch-updating');
        }, 300);
        
        // Update indeterminate states
        this.updateIndeterminateStates();
    }
    
    handleColumnToggle(toggle) {
        const permission = toggle.dataset.permission;
        const isChecked = toggle.checked;
        
        // Find all switches in this column
        const columnSwitches = document.querySelectorAll(`input[name*="[${permission}]"]`);
        
        // Batch update with animation
        columnSwitches.forEach((checkbox, index) => {
            setTimeout(() => {
                checkbox.checked = isChecked;
                
                const switchWrapper = checkbox.closest('.permission-switch');
                if (switchWrapper) {
                    switchWrapper.classList.add('batch-updating');
                    setTimeout(() => {
                        switchWrapper.classList.remove('batch-updating');
                    }, 300);
                }
            }, index * 50); // Stagger animation
        });
    }
    
    updateIndeterminateStates() {
        // Check each column for partial selection
        ['view', 'create', 'edit', 'delete'].forEach(permission => {
            const toggle = document.querySelector(`.permission-toggle[data-permission="${permission}"]`);
            if (!toggle) return;
            
            const columnCheckboxes = document.querySelectorAll(`input[name*="[${permission}]"]:not(.permission-toggle)`);
            const checkedCount = Array.from(columnCheckboxes).filter(cb => cb.checked).length;
            
            if (checkedCount === 0) {
                toggle.checked = false;
                toggle.indeterminate = false;
            } else if (checkedCount === columnCheckboxes.length) {
                toggle.checked = true;
                toggle.indeterminate = false;
            } else {
                toggle.indeterminate = true;
            }
        });
    }
    
    addClickFeedback(switchEl) {
        switchEl.classList.add('success');
        setTimeout(() => {
            switchEl.classList.remove('success');
        }, 500);
    }
    
    // Method to convert mobile accordion checkboxes
    convertMobilePermissions() {
        const mobileCheckboxes = document.querySelectorAll('.permission-grid .form-check-input');
        
        mobileCheckboxes.forEach(checkbox => {
            if (checkbox.classList.contains('switch-converted')) return;
            
            const wrapper = checkbox.closest('.permission-item') || checkbox.parentElement;
            const label = wrapper.querySelector('label') || wrapper;
            
            // Create switch
            const switchWrapper = document.createElement('span');
            switchWrapper.className = 'permission-switch';
            
            const slider = document.createElement('span');
            slider.className = 'slider';
            
            // Replace checkbox with switch
            checkbox.parentNode.insertBefore(switchWrapper, checkbox);
            switchWrapper.appendChild(checkbox);
            switchWrapper.appendChild(slider);
            
            checkbox.classList.add('switch-converted');
        });
    }
    
    // Utility method to get switch state
    getSwitchStates() {
        const states = {};
        
        document.querySelectorAll('.permission-switch input').forEach(input => {
            if (input.name) {
                states[input.name] = input.checked;
            }
        });
        
        return states;
    }
    
    // Method to set switch states (useful for loading saved permissions)
    setSwitchStates(states) {
        Object.entries(states).forEach(([name, checked]) => {
            const input = document.querySelector(`input[name="${name}"]`);
            if (input) {
                input.checked = checked;
                
                // Add visual feedback
                const switchWrapper = input.closest('.permission-switch');
                if (switchWrapper) {
                    switchWrapper.classList.add('loading');
                    setTimeout(() => {
                        switchWrapper.classList.remove('loading');
                    }, 1000);
                }
            }
        });
        
        this.updateIndeterminateStates();
    }
}

// Auto-initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    window.permissionSwitches = new PermissionSwitches();
    
    // Re-convert after dynamic content loads
    const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
            if (mutation.addedNodes.length) {
                window.permissionSwitches.convertCheckboxesToSwitches();
                window.permissionSwitches.convertMobilePermissions();
            }
        });
    });
    
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// Integration with existing permissions manager
if (window.permissionsManager) {
    const originalRenderRow = window.permissionsManager.createPermissionRow;
    window.permissionsManager.createPermissionRow = function(...args) {
        const row = originalRenderRow.apply(this, args);
        
        // Convert checkboxes in the new row
        setTimeout(() => {
            window.permissionSwitches.convertCheckboxesToSwitches();
        }, 0);
        
        return row;
    };
}