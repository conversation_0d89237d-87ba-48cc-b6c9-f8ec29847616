<?php
/**
 * Authentication Routes
 */

use Firebase\JWT\JWT;
use Firebase\JWT\Key;

// Login page
Flight::route('GET /login', function(){
    $view = Flight::get('view');
    
    // Get template preference from config or default
    $template = 'adminlte';
    try {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT value FROM config WHERE key = 'app_template' AND category = 'system'");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($result && $result['value']) {
            $template = $result['value'];
        }
    } catch (Exception $e) {
        // Use default
    }
    
    $viewName = 'auth/login-modern.twig';
    
    echo $view->render($viewName, [
        'title' => 'Login',
        'app_name' => Flight::get('config')['app_name'] ?? 'Fit360 AdminDesk'
    ]);
});

// Login process
Flight::route('POST /login', function(){
    // Check CSRF token
    $csrfToken = Flight::request()->data->csrf_token ?? '';
    if (empty($_SESSION['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $csrfToken)) {
        $view = Flight::get('view');
        echo $view->render('auth/login-modern.twig', [
            'title' => 'Login',
            'error' => __('auth.invalid_csrf_token'),
            'app_name' => Flight::get('config')['app_name'] ?? 'Fit360 AdminDesk'
        ]);
        return;
    }
    
    $identifier = Flight::request()->data->username ?? Flight::request()->data->email; // Can be username or email
    $password = Flight::request()->data->password;
    
    try {
        $db = Flight::db();
        // Try to find user by username or email
        $stmt = $db->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
        $stmt->execute([$identifier, $identifier]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // Update last login
            $stmt = $db->prepare("UPDATE users SET last_login_at = NOW() WHERE id = ?");
            $stmt->execute([$user['id']]);
            
            // Create session with user object
            $_SESSION['user'] = [
                'id' => $user['id'],
                'email' => $user['email'],
                'name' => $user['first_name'] . ' ' . $user['last_name'],
                'username' => $user['username'],
                'language' => $user['language'] ?? 'fr',
                'timezone' => $user['timezone'] ?? 'Europe/Luxembourg',
                'is_admin' => isset($user['is_admin']) ? (bool)$user['is_admin'] : false
            ];
            
            // Keep backward compatibility
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
            $_SESSION['username'] = $user['username'];
            
            // Load user groups from database
            try {
                $stmt = $db->prepare("
                    SELECT g.id, g.name, g.description, g.color, g.icon 
                    FROM user_groups g 
                    INNER JOIN user_group_members ugm ON g.id = ugm.group_id 
                    WHERE ugm.user_id = ? AND g.is_active = 1
                ");
                $stmt->execute([$user['id']]);
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                if (!empty($groups)) {
                    $_SESSION['user_groups'] = $groups;
                    
                    // Check if user is in Administrators group
                    foreach ($groups as $group) {
                        if (strtolower($group['name']) === 'administrators') {
                            $_SESSION['user']['is_admin'] = true;
                            break;
                        }
                    }
                } else {
                    // Fallback if no groups found
                    $_SESSION['user_groups'] = [];
                }
            } catch (Exception $e) {
                // If groups table doesn't exist, use temporary fix
                $_SESSION['user_groups'] = 'Administrators';
                error_log('Error loading user groups: ' . $e->getMessage());
            }
            
            // Set user language
            $_SESSION['user_language'] = $user['language'] ?? 'fr';
            
            // Use simple modern dashboard
            $_SESSION['use_simple_modern'] = true;
            
            // Create JWT token
            $payload = [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'exp' => time() + (60 * 60 * 24) // 24 hours
            ];
            
            $jwt = JWT::encode($payload, $_ENV['JWT_SECRET'], 'HS256');
            $_SESSION['jwt_token'] = $jwt;
            
            redirect_to('/');
        } else {
            $view = Flight::get('view');
            
            // Get template preference
            $template = 'adminlte';
            try {
                $db = Flight::db();
                $stmt = $db->prepare("SELECT value FROM config WHERE key = 'app_template' AND category = 'system'");
                $stmt->execute();
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                if ($result && $result['value']) {
                    $template = $result['value'];
                }
            } catch (Exception $e) {
                // Use default
            }
            
            $viewName = 'auth/login-modern.twig';
            
            echo $view->render($viewName, [
                'title' => 'Login',
                'error' => __('auth.invalid_credentials'),
                'app_name' => Flight::get('config')['app_name'] ?? 'Fit360 AdminDesk'
            ]);
        }
    } catch (Exception $e) {
        $view = Flight::get('view');
        $debugError = $_ENV['APP_DEBUG'] === 'true' ? $e->getMessage() : 'An error occurred. Please try again.';
        
        // Get template preference
        $template = 'adminlte';
        try {
            $db = Flight::db();
            $stmt = $db->prepare("SELECT value FROM config WHERE key = 'app_template' AND category = 'system'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($result && $result['value']) {
                $template = $result['value'];
            }
        } catch (Exception $ex) {
            // Use default
        }
        
        $viewName = 'auth/login-modern.twig';
        
        echo $view->render($viewName, [
            'title' => 'Login',
            'error' => $debugError,
            'app_name' => Flight::get('config')['app_name'] ?? 'Fit360 AdminDesk'
        ]);
    }
});

// Logout
Flight::route('GET /logout', function(){
    session_destroy();
    redirect_to('/login');
});

// Authentication middleware
Flight::before('start', function(&$params, &$output){
    $publicRoutes = ['/login', '/health', '/install.php', '/test-system.php'];
    $requestUrl = parse_url(Flight::request()->url, PHP_URL_PATH);
    
    // Get the path after base URL
    $basePath = Flight::get('flight.base_url');
    if (strpos($requestUrl, $basePath) === 0) {
        $currentPath = substr($requestUrl, strlen($basePath));
    } else {
        $currentPath = $requestUrl;
    }
    
    // Normalize path
    if (empty($currentPath)) {
        $currentPath = '/';
    }
    
    // Check if it's an actual file (like install.php)
    $isFile = strpos($currentPath, '.php') !== false;
    
    // Check if it's an API route (allow API routes for authenticated users via session)
    $isApiRoute = strpos($currentPath, '/api/') === 0;
    
    // Check if route requires authentication
    $requiresAuth = !in_array($currentPath, $publicRoutes) && !$isFile;
    
    if ($requiresAuth && !isset($_SESSION['user_id']) && !$isApiRoute) {
        // Prevent redirect loops
        if ($currentPath !== '/login') {
            redirect_to('/login');
        }
    }
    
    // For API routes, check if user is authenticated via session
    if ($isApiRoute && !isset($_SESSION['user_id'])) {
        // Check if it's an AJAX request
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
        
        if ($isAjax) {
            header('Content-Type: application/json');
            echo json_encode(['error' => 'Unauthorized', 'message' => 'Please log in']);
            exit;
        } else {
            Flight::json(['error' => 'Unauthorized'], 401);
            exit;
        }
    }
    
    return true;
});