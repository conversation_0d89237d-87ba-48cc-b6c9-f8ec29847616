{% extends "base-modern.twig" %}

{% block title %}{{ __('config.company_settings') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.company_settings') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="submit" form="companySettingsForm" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.company_settings_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <form id="companySettingsForm" method="POST" action="{{ base_url }}/config/company" enctype="multipart/form-data" class="needs-validation" novalidate>
        <input type="hidden" name="_method" value="PUT">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Basic Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-building me-2"></i>{{ __('config.company_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="company_name" class="form-label">{{ __('config.company_name') }} *</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="{{ settings.company_name.value|default('') }}" required>
                                <div class="invalid-feedback">
                                    {{ __('validation.required') }}
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <!-- VAT number moved to Legal Information section -->
                            </div>
                            
                            <div class="col-md-12">
                                <label for="company_address" class="form-label">{{ __('config.company_address') }}</label>
                                <input type="text" class="form-control" id="company_address" name="company_address" 
                                       value="{{ settings.company_address.value|default('') }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="company_postal_code" class="form-label">{{ __('common.postal_code') }}</label>
                                <input type="text" class="form-control" id="company_postal_code" name="company_postal_code" 
                                       value="{{ settings.company_postal_code.value|default('') }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="company_city" class="form-label">{{ __('common.city') }}</label>
                                <input type="text" class="form-control" id="company_city" name="company_city" 
                                       value="{{ settings.company_city.value|default('') }}">
                            </div>
                            
                            <div class="col-md-4">
                                <label for="company_country" class="form-label">{{ __('common.country') }}</label>
                                <select class="form-select" id="company_country" name="company_country">
                                    <option value="LU" {{ settings.company_country.value|default('LU') == 'LU' ? 'selected' : '' }}>Luxembourg</option>
                                    <option value="FR" {{ settings.company_country.value|default('LU') == 'FR' ? 'selected' : '' }}>France</option>
                                    <option value="BE" {{ settings.company_country.value|default('LU') == 'BE' ? 'selected' : '' }}>Belgique</option>
                                    <option value="DE" {{ settings.company_country.value|default('LU') == 'DE' ? 'selected' : '' }}>Allemagne</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-telephone me-2"></i>{{ __('patients.contact_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="company_phone" class="form-label">{{ __('config.company_phone') }}</label>
                                <input type="tel" class="form-control" id="company_phone" name="company_phone" 
                                       value="{{ settings.company_phone.value|default('') }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="company_email" class="form-label">{{ __('config.company_email') }}</label>
                                <input type="email" class="form-control" id="company_email" name="company_email" 
                                       value="{{ settings.company_email.value|default('') }}">
                                <div class="invalid-feedback">
                                    {{ __('validation.email') }}
                                </div>
                            </div>
                            
                            <div class="col-md-12">
                                <label for="company_website" class="form-label">{{ __('config.company_website') }}</label>
                                <input type="url" class="form-control" id="company_website" name="company_website" 
                                       value="{{ settings.company_website.value|default('') }}" placeholder="https://">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Banking Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-bank me-2"></i>{{ __('config.bank_details') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="bank_name" class="form-label">{{ __('config.bank_name') }}</label>
                                <input type="text" class="form-control" id="bank_name" name="company_bank" 
                                       value="{{ settings.company_bank.value|default('') }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="bank_account_holder" class="form-label">{{ __('config.account_holder') }}</label>
                                <input type="text" class="form-control" id="bank_account_holder" name="company_bank_account_holder" 
                                       value="{{ settings.company_bank_account_holder.value|default('') }}">
                            </div>
                            
                            <div class="col-md-12">
                                <label for="iban" class="form-label">{{ __('config.iban') }}</label>
                                <input type="text" class="form-control" id="iban" name="company_iban" 
                                       value="{{ settings.company_iban.value|default('') }}" placeholder="LU00 0000 0000 0000 0000">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="bic_swift" class="form-label">{{ __('config.bic_swift') }}</label>
                                <input type="text" class="form-control" id="bic_swift" name="company_bic_swift" 
                                       value="{{ settings.company_bic_swift.value|default('') }}">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="bank_reference" class="form-label">{{ __('config.bank_reference') }}</label>
                                <input type="text" class="form-control" id="bank_reference" name="company_bank_reference" 
                                       value="{{ settings.company_bank_reference.value|default('') }}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Invoice Settings -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="bi bi-file-earmark-text me-2"></i>{{ __('config.invoice_settings') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label for="invoice_footer" class="form-label">{{ __('config.invoice_footer_text') }}</label>
                                <textarea class="form-control" id="invoice_footer" name="invoice_footer" rows="3">{{ settings.invoice_footer.value|default('') }}</textarea>
                                <small class="text-muted">{{ __('config.invoice_footer_hint') }}</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="invoice_due_days" class="form-label">{{ __('config.default_due_days') }}</label>
                                <input type="number" class="form-control" id="invoice_due_days" name="invoice_due_days" 
                                       value="{{ settings.invoice_due_days.value|default(30) }}" min="0">
                                <small class="text-muted">{{ __('config.due_days_hint') }}</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="invoice_late_fee" class="form-label">{{ __('config.late_fee_percentage') }}</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="invoice_late_fee" name="invoice_late_fee" 
                                           value="{{ settings.invoice_late_fee.value|default(0) }}" min="0" step="0.01">
                                    <span class="input-group-text">%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4">
                <!-- Logo Upload -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-image me-2"></i>{{ __('config.company_logo') }}</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="mb-3">
                            {% if settings.company_logo.value %}
                                <img src="{{ base_url }}/{{ settings.company_logo.value }}" alt="Company Logo" 
                                     class="img-fluid mb-3" style="max-height: 200px;" id="logoPreview">
                            {% else %}
                                <div class="bg-light rounded p-5 mb-3" id="logoPreview">
                                    <i class="bi bi-building fs-1 text-muted"></i>
                                    <p class="text-muted mb-0">{{ __('config.no_logo_uploaded') }}</p>
                                </div>
                            {% endif %}
                        </div>
                        
                        <div class="mb-3">
                            <input type="file" class="form-control" id="company_logo" name="company_logo" accept="image/*">
                            <small class="text-muted">{{ __('config.logo_requirements') }}</small>
                        </div>
                        
                        {% if settings.company_logo.value %}
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="remove_logo" name="remove_logo">
                            <label class="form-check-label" for="remove_logo">
                                {{ __('config.remove_logo') }}
                            </label>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Legal Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>{{ __('config.legal_information') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="legal_form" class="form-label">{{ __('config.legal_form') }}</label>
                            <select class="form-select" id="legal_form" name="company_legal_form">
                                <option value="">{{ __('common.select') }}</option>
                                <option value="SA" {{ settings.company_legal_form.value|default('') == 'SA' ? 'selected' : '' }}>SA</option>
                                <option value="SARL" {{ settings.company_legal_form.value|default('') == 'SARL' ? 'selected' : '' }}>SARL</option>
                                <option value="SARLS" {{ settings.company_legal_form.value|default('') == 'SARLS' ? 'selected' : '' }}>SARL-S</option>
                                <option value="SNC" {{ settings.company_legal_form.value|default('') == 'SNC' ? 'selected' : '' }}>SNC</option>
                                <option value="SCS" {{ settings.company_legal_form.value|default('') == 'SCS' ? 'selected' : '' }}>SCS</option>
                                <option value="OTHER" {{ settings.company_legal_form.value|default('') == 'OTHER' ? 'selected' : '' }}>{{ __('common.other') }}</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="court_registry" class="form-label">{{ __('config.court_registry') }}</label>
                            <input type="text" class="form-control" id="court_registry" name="company_court_registry" 
                                   value="{{ settings.company_court_registry.value|default('') }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="vat_number" class="form-label">{{ __('config.vat_number') }}</label>
                            <input type="text" class="form-control" id="vat_number" name="company_tax_id" 
                                   value="{{ settings.company_tax_id.value|default('') }}">
                        </div>
                        
                        <div class="mb-3">
                            <label for="capital_amount" class="form-label">{{ __('config.capital_amount') }}</label>
                            <div class="input-group">
                                <span class="input-group-text">{{ currency }}</span>
                                <input type="number" class="form-control" id="capital_amount" name="company_capital_amount" 
                                       value="{{ settings.company_capital_amount.value|default('') }}" min="0" step="0.01">
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </form>
</div>

<script>
// Wait for page to load
window.addEventListener('load', function() {
    console.log('Company settings page loaded');
    
    // Logo preview
    const logoInput = document.getElementById('company_logo');
    if (logoInput) {
        logoInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('logoPreview');
            if (preview.tagName === 'IMG') {
                preview.src = e.target.result;
            } else {
                const img = document.createElement('img');
                img.src = e.target.result;
                img.className = 'img-fluid mb-3';
                img.style.maxHeight = '200px';
                img.id = 'logoPreview';
                preview.parentNode.replaceChild(img, preview);
            }
        };
        reader.readAsDataURL(file);
    }
        });
    }

    // Form submission is handled in the scripts block below

    // Format IBAN input
    const ibanInput = document.getElementById('iban');
    if (ibanInput) {
        ibanInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '');
            let formatted = value.match(/.{1,4}/g);
            if (formatted) {
                e.target.value = formatted.join(' ');
            }
        });
    }
}); // End of window.load
</script>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<script>
// Company settings form handler - using block scripts to ensure it runs after all base scripts
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Initializing company form');
    
    const companyForm = document.getElementById('companySettingsForm');
    if (!companyForm) {
        console.error('Company settings form not found!');
        return;
    }
    
    // Override form submission completely
    companyForm.onsubmit = null;
    companyForm.addEventListener('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        console.log('Form submission intercepted and stopped');
        
        // Validate form
        if (!this.checkValidity()) {
            this.classList.add('was-validated');
            return false;
        }
        
        const formData = new FormData(this);
        
        // Get all submit buttons for this form
        const submitBtns = document.querySelectorAll('button[type="submit"][form="companySettingsForm"], #companySettingsForm button[type="submit"]');
        const originalTexts = [];
        
        // Disable all buttons immediately
        submitBtns.forEach((btn, index) => {
            originalTexts[index] = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}...';
        });
        
        // Use XMLHttpRequest as fallback if fetch fails
        const xhr = new XMLHttpRequest();
        xhr.open('POST', this.action, true);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        
        xhr.onload = function() {
            try {
                const data = JSON.parse(xhr.responseText);
                console.log('Response received:', data);
                
                if (data.success) {
                    // Show success message with SweetAlert2
                    Swal.fire({
                        icon: 'success',
                        title: '{{ __("common.success") }}',
                        text: data.message,
                        showConfirmButton: false,
                        timer: 1500
                    }).then(() => {
                        // Don't reload the page, just reset buttons
                        submitBtns.forEach((btn, index) => {
                            btn.innerHTML = originalTexts[index];
                            btn.disabled = false;
                        });
                    });
                } else {
                    // Show error with SweetAlert2
                    Swal.fire('{{ __("common.error") }}', data.message || '{{ __("common.error_occurred") }}', 'error');
                    submitBtns.forEach((btn, index) => {
                        btn.disabled = false;
                        btn.innerHTML = originalTexts[index];
                    });
                }
            } catch (e) {
                console.error('Parse error:', e);
                Swal.fire('{{ __("common.error") }}', '{{ __("common.server_error") }}', 'error');
                submitBtns.forEach((btn, index) => {
                    btn.disabled = false;
                    btn.innerHTML = originalTexts[index];
                });
            }
        };
        
        xhr.onerror = function() {
            console.error('XHR Error');
            Swal.fire('{{ __("common.error") }}', '{{ __("common.connection_error") }}', 'error');
            submitBtns.forEach((btn, index) => {
                btn.disabled = false;
                btn.innerHTML = originalTexts[index];
            });
        };
        
        // Send the request
        xhr.send(formData);
        
        return false;
    }, true); // Use capture phase to ensure we catch it first
});
</script>
{% endblock %}