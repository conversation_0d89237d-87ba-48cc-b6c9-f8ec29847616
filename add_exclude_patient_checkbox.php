<?php
/**
 * Add exclude patient line checkbox to invoice creation form
 */

echo "<pre>";
echo "=== Adding Exclude Patient Line Checkbox ===\n\n";

$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($createFile);

// Create backup
$backupFile = $createFile . '.backup.' . date('YmdHis');
file_put_contents($backupFile, $content);
echo "✓ Created backup: " . basename($backupFile) . "\n\n";

// Find where to insert the checkbox - after the subject field
$searchPattern = '</div>
                    </div>
                </div>
            </div>
        </div>';

// Check if we can find this pattern
$patternPos = strpos($content, $searchPattern);
if ($patternPos === false) {
    // Try a more specific search - after the invoice subject field
    $searchPattern = '<label for="invoice_subject" class="form-label">{{ __(\'invoices.subject\')|default(\'Objet\') }}</label>
                        <input type="text" class="form-control" id="invoice_subject" name="subject" 
                               value="{{ invoice.subject|default(\'\') }}">
                    </div>';
    $patternPos = strpos($content, $searchPattern);
}

if ($patternPos !== false) {
    // Insert the exclude patient line checkbox HTML
    $insertHtml = '
                    
                    <!-- Exclude Patient Line (for retrocession invoices only) -->
                    <div class="col-md-12" id="exclude_patient_line_container" style="display: none;">
                        <div class="form-check mt-3">
                            <input type="checkbox" class="form-check-input" id="exclude_patient_line" 
                                   name="exclude_patient_line" value="1">
                            <label class="form-check-label" for="exclude_patient_line">
                                {{ __("retrocession.exclude_patient_line")|default("Exclure la ligne patient") }}
                                <small class="text-muted d-block">{{ __("retrocession.exclude_patient_line_help")|default("Si coché, la facture ne contiendra pas la ligne \"RÉTROCESSION PATIENTS\"") }}</small>
                            </label>
                        </div>
                    </div>';
    
    // Insert after the pattern
    $insertPos = $patternPos + strlen($searchPattern);
    $content = substr($content, 0, $insertPos) . $insertHtml . substr($content, $insertPos);
    
    echo "✓ Added exclude patient line checkbox HTML\n";
} else {
    echo "⚠ Could not find insertion point, trying alternative method...\n";
    
    // Alternative: Find the end of invoice details card body
    $altPattern = '<!-- Invoice Details -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>{{ __(\'invoices.invoice_details\') }}</h5>
            </div>
            <div class="card-body">';
    
    $altPos = strpos($content, $altPattern);
    if ($altPos !== false) {
        // Find the closing div of card-body
        $searchFrom = $altPos + strlen($altPattern);
        $cardBodyEnd = strpos($content, '</div>
        </div>', $searchFrom);
        
        if ($cardBodyEnd !== false) {
            $insertHtml = '
                
                <div class="row g-3 mt-3">
                    <!-- Exclude Patient Line (for retrocession invoices only) -->
                    <div class="col-md-12" id="exclude_patient_line_container" style="display: none;">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="exclude_patient_line" 
                                   name="exclude_patient_line" value="1">
                            <label class="form-check-label" for="exclude_patient_line">
                                {{ __("retrocession.exclude_patient_line")|default("Exclure la ligne patient") }}
                                <small class="text-muted d-block">{{ __("retrocession.exclude_patient_line_help")|default("Si coché, la facture ne contiendra pas la ligne \"RÉTROCESSION PATIENTS\"") }}</small>
                            </label>
                        </div>
                    </div>
                </div>
                
            ';
            
            $content = substr($content, 0, $cardBodyEnd) . $insertHtml . substr($content, $cardBodyEnd);
            echo "✓ Added exclude patient line checkbox using alternative method\n";
        }
    }
}

// Now update the invoice controller to handle the exclude_patient_line field
$controllerFile = __DIR__ . '/app/controllers/InvoiceController.php';
if (file_exists($controllerFile)) {
    $controllerContent = file_get_contents($controllerFile);
    
    // Check if it's already handling exclude_patient_line
    if (strpos($controllerContent, 'retro_exclude_patient_line') === false) {
        // Find where invoice data is prepared for retrocession
        $searchPattern = "'payment_method' => \$data['payment_method'] ?? null,";
        if (strpos($controllerContent, $searchPattern) !== false) {
            $replacement = "'payment_method' => \$data['payment_method'] ?? null,
            'retro_exclude_patient_line' => !empty(\$data['exclude_patient_line']) ? 1 : 0,";
            
            $controllerContent = str_replace($searchPattern, $replacement, $controllerContent);
            file_put_contents($controllerFile, $controllerContent);
            echo "✓ Updated InvoiceController to handle exclude_patient_line\n";
        }
    } else {
        echo "✓ InvoiceController already handles exclude_patient_line\n";
    }
}

// Save the updated content
file_put_contents($createFile, $content);

echo "\n✅ Exclude patient line checkbox added successfully!\n\n";
echo "How to use:\n";
echo "1. Go to create a retrocession invoice\n";
echo "2. Select 'Rétrocession 25%' or 'Rétrocession 30%' as invoice type\n";
echo "3. The 'Exclude patient line' checkbox will appear\n";
echo "4. Check it to remove the 'RÉTROCESSION PATIENTS 20%' line\n";
echo "5. The invoice will only show CNS and secretary fees\n";

echo "</pre>";