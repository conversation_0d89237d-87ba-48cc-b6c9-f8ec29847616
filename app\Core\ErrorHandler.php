<?php

namespace App\Core;

use Monolog\Logger;
use Exception;
use PDO;
use Flight;

class ErrorHandler
{
    private $logger;
    private $db;
    private $debug;
    private $environment;
    
    public function __construct(Logger $logger = null, PDO $db = null)
    {
        $this->logger = $logger;
        $this->db = $db;
        $this->debug = (getenv('APP_DEBUG') === 'true' || 
                       (isset($_ENV['APP_DEBUG']) && $_ENV['APP_DEBUG'] === 'true'));
        $this->environment = $_ENV['APP_ENV'] ?? 'production';
    }
    
    /**
     * Handle application errors
     */
    public function handleError($exception)
    {
        // Capture request details
        $errorData = $this->captureErrorData($exception);
        
        // Log to file
        $this->logToFile($errorData);
        
        // Log to database (if available)
        $this->logToDatabase($errorData);
        
        // Display error based on environment
        $this->displayError($errorData);
    }
    
    /**
     * Capture comprehensive error data
     */
    private function captureErrorData($exception)
    {
        $startTime = $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true);
        $executionTime = microtime(true) - $startTime;
        
        return [
            'id' => uniqid('error_'),
            'timestamp' => date('Y-m-d H:i:s'),
            'environment' => $this->environment,
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'class' => get_class($exception),
            'trace' => $exception->getTraceAsString(),
            'trace_array' => $exception->getTrace(),
            'request' => [
                'method' => $_SERVER['REQUEST_METHOD'] ?? 'CLI',
                'uri' => $_SERVER['REQUEST_URI'] ?? 'CLI',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'N/A',
                'ip' => $this->getClientIp(),
                'referer' => $_SERVER['HTTP_REFERER'] ?? null,
                'headers' => $this->getRequestHeaders(),
                'params' => [
                    'get' => $_GET,
                    'post' => $this->sanitizePostData($_POST),
                    'cookies' => $_COOKIE
                ]
            ],
            'server' => [
                'hostname' => gethostname(),
                'software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
                'php_version' => PHP_VERSION,
                'os' => PHP_OS
            ],
            'performance' => [
                'execution_time' => round($executionTime, 3),
                'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2),
                'memory_peak' => round(memory_get_peak_usage(true) / 1024 / 1024, 2),
                'included_files' => count(get_included_files())
            ],
            'session' => [
                'id' => session_id() ?: null,
                'user_id' => $_SESSION['user_id'] ?? null,
                'username' => $_SESSION['username'] ?? null
            ]
        ];
    }
    
    /**
     * Get client IP address
     */
    private function getClientIp()
    {
        $ipKeys = ['HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        foreach ($ipKeys as $key) {
            if (array_key_exists($key, $_SERVER) === true) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = explode(',', $ip)[0];
                }
                return trim($ip);
            }
        }
        return 'Unknown';
    }
    
    /**
     * Get request headers
     */
    private function getRequestHeaders()
    {
        $headers = [];
        foreach ($_SERVER as $key => $value) {
            if (substr($key, 0, 5) === 'HTTP_') {
                $header = str_replace(' ', '-', ucwords(str_replace('_', ' ', strtolower(substr($key, 5)))));
                $headers[$header] = $value;
            }
        }
        return $headers;
    }
    
    /**
     * Sanitize POST data to remove sensitive information
     */
    private function sanitizePostData($data)
    {
        $sensitiveKeys = ['password', 'pass', 'pwd', 'token', 'secret', 'api_key', 'credit_card'];
        
        array_walk_recursive($data, function(&$value, $key) use ($sensitiveKeys) {
            foreach ($sensitiveKeys as $sensitive) {
                if (stripos($key, $sensitive) !== false) {
                    $value = '***REDACTED***';
                }
            }
        });
        
        return $data;
    }
    
    /**
     * Log error to file
     */
    private function logToFile($errorData)
    {
        if ($this->logger) {
            $context = [
                'error_id' => $errorData['id'],
                'file' => $errorData['file'],
                'line' => $errorData['line'],
                'request_uri' => $errorData['request']['uri'],
                'user_id' => $errorData['session']['user_id'],
                'execution_time' => $errorData['performance']['execution_time'],
                'memory_peak' => $errorData['performance']['memory_peak']
            ];
            
            $this->logger->error($errorData['message'], $context);
        }
        
        // Also log to standard error log
        $logMessage = sprintf(
            "[%s] %s in %s:%d | URI: %s | User: %s | Time: %.3fs | Memory: %.2fMB",
            $errorData['id'],
            $errorData['message'],
            $errorData['file'],
            $errorData['line'],
            $errorData['request']['uri'],
            $errorData['session']['username'] ?? 'Guest',
            $errorData['performance']['execution_time'],
            $errorData['performance']['memory_peak']
        );
        
        error_log($logMessage);
    }
    
    /**
     * Log error to database
     */
    private function logToDatabase($errorData)
    {
        if (!$this->db) {
            return;
        }
        
        try {
            $sql = "INSERT INTO error_logs (
                error_id, timestamp, environment, message, code, file, line, 
                class, trace, request_method, request_uri, user_agent, ip_address,
                user_id, username, execution_time, memory_usage, memory_peak
            ) VALUES (
                :error_id, :timestamp, :environment, :message, :code, :file, :line,
                :class, :trace, :request_method, :request_uri, :user_agent, :ip_address,
                :user_id, :username, :execution_time, :memory_usage, :memory_peak
            )";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                'error_id' => $errorData['id'],
                'timestamp' => $errorData['timestamp'],
                'environment' => $errorData['environment'],
                'message' => $errorData['message'],
                'code' => $errorData['code'],
                'file' => $errorData['file'],
                'line' => $errorData['line'],
                'class' => $errorData['class'],
                'trace' => $errorData['trace'],
                'request_method' => $errorData['request']['method'],
                'request_uri' => $errorData['request']['uri'],
                'user_agent' => $errorData['request']['user_agent'],
                'ip_address' => $errorData['request']['ip'],
                'user_id' => $errorData['session']['user_id'],
                'username' => $errorData['session']['username'],
                'execution_time' => $errorData['performance']['execution_time'],
                'memory_usage' => $errorData['performance']['memory_usage'],
                'memory_peak' => $errorData['performance']['memory_peak']
            ]);
        } catch (Exception $e) {
            // If database logging fails, just log to file
            error_log("Failed to log error to database: " . $e->getMessage());
        }
    }
    
    /**
     * Display error based on environment
     */
    private function displayError($errorData)
    {
        // Clear any output buffers
        while (ob_get_level() > 0) {
            ob_end_clean();
        }
        
        // Set appropriate HTTP status code
        http_response_code(500);
        
        if ($this->debug || $this->environment === 'local' || $this->environment === 'development') {
            $this->displayDebugError($errorData);
        } else {
            $this->displayProductionError($errorData);
        }
    }
    
    /**
     * Display detailed debug error
     */
    private function displayDebugError($errorData)
    {
        // Try to use Twig template first
        try {
            $view = Flight::get('view');
            if ($view && $view->getLoader()->exists('errors/500-debug.twig')) {
                echo $view->render('errors/500-debug.twig', [
                    'error' => $errorData
                ]);
                return;
            }
        } catch (Exception $e) {
            // Fall back to HTML
        }
        
        // Fallback HTML error display
        include __DIR__ . '/../views/errors/500-debug.php';
    }
    
    /**
     * Display production error
     */
    private function displayProductionError($errorData)
    {
        // Try to use Twig template
        try {
            $view = Flight::get('view');
            if ($view) {
                echo $view->render('errors/500.twig', [
                    'error_id' => $errorData['id']
                ]);
                return;
            }
        } catch (Exception $e) {
            // Fall back to simple HTML
        }
        
        // Fallback simple error
        echo '<!DOCTYPE html><html><head><title>Error</title></head><body>';
        echo '<h1>Une erreur s\'est produite</h1>';
        echo '<p>Nous nous excusons pour la gêne occasionnée. L\'erreur a été enregistrée.</p>';
        echo '<p>Code d\'erreur: ' . htmlspecialchars($errorData['id']) . '</p>';
        echo '<p><a href="/">Retour à l\'accueil</a></p>';
        echo '</body></html>';
    }
    
    /**
     * Register as global error handler
     */
    public function register()
    {
        // Register Flight error handler
        Flight::map('error', [$this, 'handleError']);
        
        // Register PHP error handler
        set_error_handler([$this, 'handlePhpError']);
        
        // Register shutdown handler for fatal errors
        register_shutdown_function([$this, 'handleShutdown']);
    }
    
    /**
     * Handle PHP errors
     */
    public function handlePhpError($severity, $message, $file, $line)
    {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        throw new \ErrorException($message, 0, $severity, $file, $line);
    }
    
    /**
     * Handle fatal errors on shutdown
     */
    public function handleShutdown()
    {
        $error = error_get_last();
        
        if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
            $exception = new \ErrorException(
                $error['message'],
                0,
                $error['type'],
                $error['file'],
                $error['line']
            );
            
            $this->handleError($exception);
        }
    }
}