# Phase 5: Product/Service Catalog System

## Task 5.1: Catalog Database Schema
### Subtask 5.1.1: Create Product/Service Tables
**Database Schema:**
```sql
CREATE TABLE `catalog_categories` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `parent_id` INT UNSIGNED NULL,
    `name` VARCHAR(100) NOT NULL,
    `description` TEXT,
    `is_active` BOOLEAN DEFAULT TRUE,
    `sort_order` INT DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`parent_id`) REFERENCES `catalog_categories` (`id`) ON DELETE CASCADE,
    INDEX `idx_parent` (`parent_id`),
    INDEX `idx_active` (`is_active`)
);

CREATE TABLE `catalog_items` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `category_id` INT UNSIGNED NULL,
    `code` VARCHAR(50) UNIQUE,
    `name` VARCHAR(200) NOT NULL,
    `description` TEXT,
    `type` ENUM('product', 'service') NOT NULL,
    `unit_price` DECIMAL(10,2) NOT NULL,
    `vat_rate` DECIMAL(5,2) DEFAULT 17.00,
    `unit_of_measure` VARCHAR(50) DEFAULT 'unit',
    `is_active` BOOLEAN DEFAULT TRUE,
    `tags` JSON,
    `metadata` JSON,
    `created_by` INT UNSIGNED,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`category_id`) REFERENCES `catalog_categories` (`id`) ON DELETE SET NULL,
    FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
    INDEX `idx_category` (`category_id`),
    INDEX `idx_type` (`type`),
    INDEX `idx_active` (`is_active`),
    FULLTEXT INDEX `idx_search` (`name`, `description`, `code`)
);

CREATE TABLE `catalog_item_favorites` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL,
    `item_id` INT UNSIGNED NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `user_item` (`user_id`, `item_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`item_id`) REFERENCES `catalog_items` (`id`) ON DELETE CASCADE
);

CREATE TABLE `catalog_price_history` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `item_id` INT UNSIGNED NOT NULL,
    `old_price` DECIMAL(10,2) NOT NULL,
    `new_price` DECIMAL(10,2) NOT NULL,
    `changed_by` INT UNSIGNED NOT NULL,
    `changed_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `reason` TEXT,
    FOREIGN KEY (`item_id`) REFERENCES `catalog_items` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`changed_by`) REFERENCES `users` (`id`),
    INDEX `idx_item_date` (`item_id`, `changed_at`)
);
```

**Test Cases:**
- [ ] Tables created successfully with all constraints
- [ ] Full-text search index works properly
- [ ] JSON fields store and retrieve data correctly
- [ ] Price history tracks changes accurately

## Task 5.2: Inline Smart Search Implementation
### Subtask 5.2.1: Search API Development
**Files to Create:**
- Create `/app/controllers/CatalogSearchController.php`
- Create `/app/models/CatalogItem.php`

**Features to Implement:**
```php
class CatalogSearchController {
    public function search() {
        $query = Flight::request()->query->q;
        $type = Flight::request()->query->type;
        $limit = Flight::request()->query->limit ?: 10;
        
        $results = $this->catalogItem->searchItems([
            'query' => $query,
            'type' => $type,
            'limit' => $limit,
            'include_favorites' => true
        ]);
        
        Flight::json($results);
    }
    
    public function quickAdd() {
        // Handle creation of new items on-the-fly
        $data = Flight::request()->data;
        $item = $this->catalogItem->quickCreate($data);
        Flight::json($item);
    }
}
```

**Test Cases:**
- [ ] Search returns relevant results quickly
- [ ] Favorites appear first in results
- [ ] Type filtering works correctly
- [ ] Quick add creates items properly

### Subtask 5.2.2: Invoice Form Integration
**JavaScript Implementation:**
```javascript
class CatalogSearch {
    constructor(inputElement, options = {}) {
        this.input = inputElement;
        this.options = {
            minChars: 2,
            delay: 300,
            onSelect: null,
            allowQuickAdd: true,
            ...options
        };
        this.init();
    }
    
    init() {
        this.createDropdown();
        this.bindEvents();
        this.setupDebounce();
    }
    
    async search(query) {
        const response = await fetch(`/api/catalog/search?q=${query}`);
        const results = await response.json();
        
        if (results.length === 0 && this.options.allowQuickAdd) {
            this.showQuickAdd(query);
        } else {
            this.showResults(results);
        }
    }
    
    showResults(results) {
        // Display search results with highlighting
        // Show favorites with star icon
        // Include price and VAT info
    }
    
    showQuickAdd(query) {
        // Show option to create new item
        // Pre-fill with search query
        // Allow setting price and VAT
    }
}
```

**Test Cases:**
- [ ] Autocomplete appears after minimum characters
- [ ] Debouncing prevents excessive API calls
- [ ] Selection populates invoice line correctly
- [ ] Quick add integrates seamlessly

## Task 5.3: Catalog Management Interface
### Subtask 5.3.1: Catalog CRUD Interface
**Files to Create:**
- Create `/app/controllers/CatalogController.php`
- Create `/app/views/catalog/index.twig` (all templates)
- Create `/app/views/catalog/form.twig` (all templates)

**Features to Implement:**
- Hierarchical category management
- Bulk import/export functionality
- Price change history viewing
- Tag-based filtering
- Advanced search with filters
- Favorite items management

**Test Cases:**
- [ ] Category hierarchy displays correctly
- [ ] Items can be moved between categories
- [ ] Bulk operations work properly
- [ ] Export includes all relevant data

### Subtask 5.3.2: Price Management Features
**Features to Implement:**
```php
class PriceManagementService {
    public function bulkPriceUpdate($criteria, $adjustment) {
        // Update prices by percentage or fixed amount
        // Log all changes with reason
        // Optional approval workflow
    }
    
    public function importPriceList($file) {
        // CSV/Excel import with validation
        // Preview changes before applying
        // Rollback capability
    }
    
    public function schedulePriceChange($itemId, $newPrice, $effectiveDate) {
        // Schedule future price changes
        // Send notifications when applied
    }
}
```

**Test Cases:**
- [ ] Bulk updates calculate correctly
- [ ] Price history maintains integrity
- [ ] Scheduled changes apply on time
- [ ] Import validates data properly