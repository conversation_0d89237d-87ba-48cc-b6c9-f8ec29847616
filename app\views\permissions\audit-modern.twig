{% extends 'base-modern.twig' %}

{% block title %}{{ __('permissions.audit_log') }} - {{ parent() }}{% endblock %}

{% block styles %}
{{ parent() }}
<style>
/* Audit Log Styles */
.audit-container {
    min-height: calc(100vh - 200px);
}

.audit-filters {
    background-color: var(--bs-gray-100);
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.audit-entry {
    border-left: 3px solid transparent;
    transition: all 0.2s ease;
}

.audit-entry:hover {
    background-color: var(--bs-gray-50);
    border-left-color: var(--bs-primary);
}

.audit-entry.permission-granted {
    border-left-color: var(--bs-success);
}

.audit-entry.permission-revoked {
    border-left-color: var(--bs-danger);
}

.audit-entry.group-modified {
    border-left-color: var(--bs-warning);
}

.audit-entry.template-applied {
    border-left-color: var(--bs-info);
}

.audit-timeline {
    position: relative;
    padding-left: 2rem;
}

.audit-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--bs-gray-300);
}

.audit-timeline-item {
    position: relative;
    padding-bottom: 2rem;
}

.audit-timeline-item::before {
    content: '';
    position: absolute;
    left: -1.25rem;
    top: 0.5rem;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: var(--bs-primary);
    border: 2px solid white;
    box-shadow: 0 0 0 3px var(--bs-gray-300);
}

.audit-details {
    background-color: var(--bs-gray-50);
    border-radius: 0.25rem;
    padding: 1rem;
    margin-top: 0.5rem;
    display: none;
}

.audit-details.show {
    display: block;
}

.permission-change {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0.125rem;
}

.permission-added {
    background-color: var(--bs-success-bg-subtle);
    color: var(--bs-success);
}

.permission-removed {
    background-color: var(--bs-danger-bg-subtle);
    color: var(--bs-danger);
}

.audit-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    background-color: var(--bs-primary);
    color: white;
}

/* Mobile Styles */
@media (max-width: 768px) {
    .audit-filters {
        padding: 1rem;
    }
    
    .audit-filters .row > div {
        margin-bottom: 0.5rem;
    }
    
    .audit-timeline {
        padding-left: 1.5rem;
    }
    
    .audit-entry {
        font-size: 0.875rem;
    }
    
    .audit-details {
        font-size: 0.813rem;
    }
}

/* Loading Animation */
.audit-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 300px;
}

.audit-loading .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Export Options */
.export-dropdown .dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.export-dropdown .dropdown-item i {
    width: 1.25rem;
}

/* Pagination */
.audit-pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid audit-container">
    <div class="row">
        <div class="col-12">
            <!-- Page Header -->
            <div class="page-header mb-4">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="page-pretitle">
                            <a href="{{ url('/permissions') }}" class="text-muted">
                                <i class="fas fa-arrow-left me-2"></i>
                                {{ __('permissions.back_to_permissions') }}
                            </a>
                        </div>
                        <h2 class="page-title mt-2">
                            <i class="fas fa-history me-2"></i>
                            {{ __('permissions.audit_log') }}
                        </h2>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary" onclick="refreshAuditLog()">
                                <i class="fas fa-sync-alt me-2"></i>
                                <span class="d-none d-md-inline">{{ __('common.refresh') }}</span>
                            </button>
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-secondary dropdown-toggle" 
                                        data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-download me-2"></i>
                                    <span class="d-none d-md-inline">{{ __('common.export') }}</span>
                                </button>
                                <ul class="dropdown-menu export-dropdown">
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="exportAuditLog('csv')">
                                            <i class="fas fa-file-csv"></i>
                                            {{ __('common.export_csv') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="exportAuditLog('excel')">
                                            <i class="fas fa-file-excel"></i>
                                            {{ __('common.export_excel') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="exportAuditLog('pdf')">
                                            <i class="fas fa-file-pdf"></i>
                                            {{ __('common.export_pdf') }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters Card -->
            <div class="audit-filters">
                <form id="auditFilterForm">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="filterDateRange" class="form-label">{{ __('permissions.date_range') }}</label>
                            <select class="form-select" id="filterDateRange" onchange="applyFilters()">
                                <option value="today">{{ __('common.today') }}</option>
                                <option value="week" selected>{{ __('common.last_7_days') }}</option>
                                <option value="month">{{ __('common.last_30_days') }}</option>
                                <option value="custom">{{ __('common.custom_range') }}</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filterUser" class="form-label">{{ __('permissions.user') }}</label>
                            <select class="form-select" id="filterUser" onchange="applyFilters()">
                                <option value="">{{ __('common.all_users') }}</option>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filterGroup" class="form-label">{{ __('permissions.group') }}</label>
                            <select class="form-select" id="filterGroup" onchange="applyFilters()">
                                <option value="">{{ __('common.all_groups') }}</option>
                                {% for group in groups %}
                                <option value="{{ group.id }}">{{ group.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="filterAction" class="form-label">{{ __('permissions.action_type') }}</label>
                            <select class="form-select" id="filterAction" onchange="applyFilters()">
                                <option value="">{{ __('common.all_actions') }}</option>
                                <option value="permissions_updated">{{ __('permissions.permissions_updated') }}</option>
                                <option value="group_created">{{ __('permissions.group_created') }}</option>
                                <option value="group_deleted">{{ __('permissions.group_deleted') }}</option>
                                <option value="template_applied">{{ __('permissions.template_applied') }}</option>
                                <option value="permissions_copied">{{ __('permissions.permissions_copied') }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-3 d-none" id="customDateRange">
                        <div class="col-md-3">
                            <label for="filterStartDate" class="form-label">{{ __('common.start_date') }}</label>
                            <input type="date" class="form-control" id="filterStartDate" onchange="applyFilters()">
                        </div>
                        <div class="col-md-3">
                            <label for="filterEndDate" class="form-label">{{ __('common.end_date') }}</label>
                            <input type="date" class="form-control" id="filterEndDate" onchange="applyFilters()">
                        </div>
                    </div>
                </form>
            </div>

            <!-- Audit Log Card -->
            <div class="card shadow-sm">
                <div class="card-body">
                    <div id="auditLogContent">
                        <!-- Loading State -->
                        <div class="audit-loading">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">{{ __('common.loading') }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pagination -->
            <div class="audit-pagination" id="auditPagination">
                <!-- Pagination will be rendered here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ parent() }}
<script>
// Audit Log Manager
class AuditLogManager {
    constructor() {
        this.currentPage = 1;
        this.perPage = 20;
        this.totalPages = 1;
        this.filters = {
            dateRange: 'week',
            user: '',
            group: '',
            action: '',
            startDate: '',
            endDate: ''
        };
        
        this.init();
    }
    
    init() {
        this.loadAuditLog();
        this.bindEvents();
    }
    
    bindEvents() {
        // Custom date range toggle
        document.getElementById('filterDateRange')?.addEventListener('change', (e) => {
            const customRange = document.getElementById('customDateRange');
            if (e.target.value === 'custom') {
                customRange?.classList.remove('d-none');
            } else {
                customRange?.classList.add('d-none');
            }
        });
        
        // Expand/collapse audit details
        document.addEventListener('click', (e) => {
            if (e.target.closest('.audit-entry')) {
                const entry = e.target.closest('.audit-entry');
                const details = entry.querySelector('.audit-details');
                if (details) {
                    details.classList.toggle('show');
                }
            }
        });
    }
    
    async loadAuditLog() {
        const content = document.getElementById('auditLogContent');
        if (!content) return;
        
        // Show loading
        content.innerHTML = `
            <div class="audit-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">{{ __('common.loading') }}</span>
                </div>
            </div>
        `;
        
        try {
            const params = new URLSearchParams({
                page: this.currentPage,
                per_page: this.perPage,
                ...this.filters
            });
            
            const response = await fetch(`{{ base_url }}/api/permissions/audit?${params}`);
            const data = await response.json();
            
            this.totalPages = data.total_pages || 1;
            this.renderAuditLog(data.entries || []);
            this.renderPagination();
            
        } catch (error) {
            console.error('Error loading audit log:', error);
            content.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ __('common.error_loading_data') }}
                </div>
            `;
        }
    }
    
    renderAuditLog(entries) {
        const content = document.getElementById('auditLogContent');
        if (!content) return;
        
        if (entries.length === 0) {
            content.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                    <p class="text-muted">{{ __('permissions.no_audit_entries') }}</p>
                </div>
            `;
            return;
        }
        
        // Group entries by date
        const groupedEntries = this.groupEntriesByDate(entries);
        
        let html = '<div class="audit-timeline">';
        
        Object.entries(groupedEntries).forEach(([date, dateEntries]) => {
            html += `
                <div class="mb-4">
                    <h6 class="text-muted mb-3">${this.formatDate(date)}</h6>
                    ${dateEntries.map(entry => this.renderAuditEntry(entry)).join('')}
                </div>
            `;
        });
        
        html += '</div>';
        content.innerHTML = html;
    }
    
    renderAuditEntry(entry) {
        const actionClass = this.getActionClass(entry.action);
        const actionIcon = this.getActionIcon(entry.action);
        const userInitials = this.getUserInitials(entry.user_name);
        
        return `
            <div class="audit-entry ${actionClass} p-3 mb-3 border rounded cursor-pointer">
                <div class="d-flex align-items-start">
                    <div class="audit-user-avatar me-3" title="${entry.user_name}">
                        ${userInitials}
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">
                                    <i class="${actionIcon} me-2"></i>
                                    ${this.getActionTitle(entry.action)}
                                </h6>
                                <p class="mb-0 text-muted">
                                    <strong>${entry.user_name}</strong>
                                    ${this.getActionDescription(entry)}
                                </p>
                            </div>
                            <small class="text-muted">
                                ${this.formatTime(entry.created_at)}
                            </small>
                        </div>
                        
                        ${entry.details ? this.renderAuditDetails(entry) : ''}
                    </div>
                </div>
            </div>
        `;
    }
    
    renderAuditDetails(entry) {
        if (!entry.details) return '';
        
        let detailsHtml = '<div class="audit-details mt-3">';
        
        // Permission changes
        if (entry.details.permissions_added || entry.details.permissions_removed) {
            detailsHtml += '<div class="mb-2"><strong>{{ __('permissions.permission_changes') }}:</strong></div>';
            
            if (entry.details.permissions_added) {
                detailsHtml += '<div class="mb-2">';
                entry.details.permissions_added.forEach(perm => {
                    detailsHtml += `<span class="permission-change permission-added">+ ${perm}</span>`;
                });
                detailsHtml += '</div>';
            }
            
            if (entry.details.permissions_removed) {
                detailsHtml += '<div class="mb-2">';
                entry.details.permissions_removed.forEach(perm => {
                    detailsHtml += `<span class="permission-change permission-removed">- ${perm}</span>`;
                });
                detailsHtml += '</div>';
            }
        }
        
        // Other details
        if (entry.details.affected_modules) {
            detailsHtml += `
                <div class="mt-2">
                    <strong>{{ __('permissions.affected_modules') }}:</strong>
                    ${entry.details.affected_modules.join(', ')}
                </div>
            `;
        }
        
        if (entry.details.template_name) {
            detailsHtml += `
                <div class="mt-2">
                    <strong>{{ __('permissions.template') }}:</strong>
                    ${entry.details.template_name}
                </div>
            `;
        }
        
        if (entry.details.source_group) {
            detailsHtml += `
                <div class="mt-2">
                    <strong>{{ __('permissions.copied_from') }}:</strong>
                    ${entry.details.source_group}
                </div>
            `;
        }
        
        if (entry.ip_address) {
            detailsHtml += `
                <div class="mt-2 text-muted small">
                    <strong>{{ __('common.ip_address') }}:</strong> ${entry.ip_address}
                </div>
            `;
        }
        
        detailsHtml += '</div>';
        return detailsHtml;
    }
    
    groupEntriesByDate(entries) {
        const grouped = {};
        
        entries.forEach(entry => {
            const date = new Date(entry.created_at).toDateString();
            if (!grouped[date]) {
                grouped[date] = [];
            }
            grouped[date].push(entry);
        });
        
        return grouped;
    }
    
    getActionClass(action) {
        const classes = {
            'permissions_updated': 'permission-granted',
            'permissions_revoked': 'permission-revoked',
            'group_created': 'group-modified',
            'group_deleted': 'permission-revoked',
            'template_applied': 'template-applied',
            'permissions_copied': 'group-modified'
        };
        return classes[action] || '';
    }
    
    getActionIcon(action) {
        const icons = {
            'permissions_updated': 'fas fa-check-circle text-success',
            'permissions_revoked': 'fas fa-times-circle text-danger',
            'group_created': 'fas fa-plus-circle text-primary',
            'group_deleted': 'fas fa-trash text-danger',
            'template_applied': 'fas fa-file-import text-info',
            'permissions_copied': 'fas fa-copy text-warning'
        };
        return icons[action] || 'fas fa-circle';
    }
    
    getActionTitle(action) {
        const titles = {
            'permissions_updated': '{{ __('permissions.permissions_updated') }}',
            'permissions_revoked': '{{ __('permissions.permissions_revoked') }}',
            'group_created': '{{ __('permissions.group_created') }}',
            'group_deleted': '{{ __('permissions.group_deleted') }}',
            'template_applied': '{{ __('permissions.template_applied') }}',
            'permissions_copied': '{{ __('permissions.permissions_copied') }}'
        };
        return titles[action] || action;
    }
    
    getActionDescription(entry) {
        const group = entry.details?.group_name || entry.group_name || '';
        
        switch (entry.action) {
            case 'permissions_updated':
                return `{{ __('permissions.updated_permissions_for') }} <strong>${group}</strong>`;
            case 'permissions_revoked':
                return `{{ __('permissions.revoked_permissions_for') }} <strong>${group}</strong>`;
            case 'group_created':
                return `{{ __('permissions.created_new_group') }} <strong>${group}</strong>`;
            case 'group_deleted':
                return `{{ __('permissions.deleted_group') }} <strong>${group}</strong>`;
            case 'template_applied':
                return `{{ __('permissions.applied_template_to') }} <strong>${group}</strong>`;
            case 'permissions_copied':
                return `{{ __('permissions.copied_permissions_to') }} <strong>${group}</strong>`;
            default:
                return entry.description || '';
        }
    }
    
    getUserInitials(name) {
        if (!name) return '?';
        return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
    }
    
    formatDate(dateString) {
        const date = new Date(dateString);
        const today = new Date();
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        
        if (date.toDateString() === today.toDateString()) {
            return '{{ __('common.today') }}';
        } else if (date.toDateString() === yesterday.toDateString()) {
            return '{{ __('common.yesterday') }}';
        } else {
            return date.toLocaleDateString('{{ app.language }}', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
    }
    
    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString('{{ app.language }}', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }
    
    renderPagination() {
        const pagination = document.getElementById('auditPagination');
        if (!pagination || this.totalPages <= 1) {
            if (pagination) pagination.innerHTML = '';
            return;
        }
        
        let html = '<nav><ul class="pagination">';
        
        // Previous button
        html += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="auditLogManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;
        
        // Page numbers
        const startPage = Math.max(1, this.currentPage - 2);
        const endPage = Math.min(this.totalPages, startPage + 4);
        
        for (let i = startPage; i <= endPage; i++) {
            html += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="auditLogManager.goToPage(${i})">${i}</a>
                </li>
            `;
        }
        
        // Next button
        html += `
            <li class="page-item ${this.currentPage === this.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="auditLogManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;
        
        html += '</ul></nav>';
        pagination.innerHTML = html;
    }
    
    goToPage(page) {
        if (page < 1 || page > this.totalPages) return;
        this.currentPage = page;
        this.loadAuditLog();
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
    
    applyFilters() {
        this.filters = {
            dateRange: document.getElementById('filterDateRange')?.value || 'week',
            user: document.getElementById('filterUser')?.value || '',
            group: document.getElementById('filterGroup')?.value || '',
            action: document.getElementById('filterAction')?.value || '',
            startDate: document.getElementById('filterStartDate')?.value || '',
            endDate: document.getElementById('filterEndDate')?.value || ''
        };
        
        this.currentPage = 1;
        this.loadAuditLog();
    }
    
    async exportAuditLog(format) {
        const params = new URLSearchParams({
            format: format,
            ...this.filters
        });
        
        window.location.href = `{{ base_url }}/api/permissions/audit/export?${params}`;
    }
    
    refresh() {
        this.loadAuditLog();
    }
}

// Initialize audit log manager
let auditLogManager;
document.addEventListener('DOMContentLoaded', () => {
    auditLogManager = new AuditLogManager();
});

// Global functions for inline event handlers
function applyFilters() {
    auditLogManager?.applyFilters();
}

function refreshAuditLog() {
    auditLogManager?.refresh();
}

function exportAuditLog(format) {
    auditLogManager?.exportAuditLog(format);
}
</script>
{% endblock %}