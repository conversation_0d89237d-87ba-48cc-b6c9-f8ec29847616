/**
 * Mobile Enhancements for Fit360 AdminDesk
 * Provides mobile-specific functionality and interactions
 */

(function() {
    'use strict';

    // Check if device is mobile
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent) || window.innerWidth <= 768;

    // Mobile enhancement features
    const MobileEnhancements = {
        // Initialize all mobile features
        init: function() {
            if (!isMobile) return;

            this.initMobileMenu();
            this.initSwipeGestures();
            this.initPullToRefresh();
            this.initTableResponsive();
            this.initFormEnhancements();
            this.initBottomNavigation();
            this.initFloatingActionButton();
            this.initMobileModals();
            this.optimizeImages();
        },

        // Mobile menu toggle functionality
        initMobileMenu: function() {
            // Create mobile menu toggle if it doesn't exist
            if (!document.querySelector('.mobile-menu-toggle')) {
                const menuToggle = document.createElement('button');
                menuToggle.className = 'mobile-menu-toggle';
                menuToggle.innerHTML = '<i class="fas fa-bars"></i>';
                menuToggle.setAttribute('aria-label', 'Toggle menu');
                document.body.appendChild(menuToggle);
            }

            // Create sidebar overlay if it doesn't exist
            if (!document.querySelector('.sidebar-overlay')) {
                const overlay = document.createElement('div');
                overlay.className = 'sidebar-overlay';
                document.body.appendChild(overlay);
            }

            const menuToggle = document.querySelector('.mobile-menu-toggle');
            const sidebar = document.querySelector('.sidebar, .main-sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            if (menuToggle && sidebar) {
                menuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    overlay.classList.toggle('show');
                });

                overlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    overlay.classList.remove('show');
                });
            }
        },

        // Swipe gesture support
        initSwipeGestures: function() {
            let touchStartX = 0;
            let touchEndX = 0;
            const sidebar = document.querySelector('.sidebar, .main-sidebar');
            const overlay = document.querySelector('.sidebar-overlay');

            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            }, { passive: true });

            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            }, { passive: true });

            function handleSwipe() {
                const swipeThreshold = 50;
                const diff = touchEndX - touchStartX;

                if (Math.abs(diff) > swipeThreshold) {
                    if (diff > 0 && touchStartX < 20) {
                        // Swipe right from left edge - open sidebar
                        if (sidebar) {
                            sidebar.classList.add('show');
                            overlay.classList.add('show');
                        }
                    } else if (diff < 0 && sidebar && sidebar.classList.contains('show')) {
                        // Swipe left - close sidebar
                        sidebar.classList.remove('show');
                        overlay.classList.remove('show');
                    }
                }
            }
        },

        // Pull-to-refresh functionality
        initPullToRefresh: function() {
            let touchStartY = 0;
            let touchEndY = 0;
            const pullThreshold = 80;
            let isPulling = false;

            // Create pull-to-refresh indicator
            const indicator = document.createElement('div');
            indicator.className = 'pull-to-refresh';
            indicator.innerHTML = '<i class="fas fa-sync"></i>';
            document.body.appendChild(indicator);

            document.addEventListener('touchstart', function(e) {
                if (window.scrollY === 0) {
                    touchStartY = e.touches[0].clientY;
                    isPulling = true;
                }
            }, { passive: true });

            document.addEventListener('touchmove', function(e) {
                if (!isPulling) return;
                
                touchEndY = e.touches[0].clientY;
                const pullDistance = touchEndY - touchStartY;

                if (pullDistance > 0 && pullDistance < pullThreshold * 2) {
                    indicator.style.top = Math.min(pullDistance - 60, 20) + 'px';
                    indicator.style.opacity = Math.min(pullDistance / pullThreshold, 1);
                }
            }, { passive: true });

            document.addEventListener('touchend', function() {
                if (!isPulling) return;

                const pullDistance = touchEndY - touchStartY;
                
                if (pullDistance > pullThreshold) {
                    indicator.classList.add('show');
                    indicator.innerHTML = '<i class="fas fa-sync fa-spin"></i>';
                    
                    // Refresh the page after a short delay
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    indicator.style.top = '-60px';
                    indicator.style.opacity = '0';
                }

                isPulling = false;
                touchStartY = 0;
                touchEndY = 0;
            });
        },

        // Enhanced table responsiveness
        initTableResponsive: function() {
            const tables = document.querySelectorAll('table:not(.table-mobile-card)');
            
            tables.forEach(function(table) {
                // Wrap table in responsive container
                if (!table.closest('.table-mobile-responsive')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'table-mobile-responsive';
                    table.parentNode.insertBefore(wrapper, table);
                    wrapper.appendChild(table);
                }

                // Add data-label attributes for mobile card view
                const headers = table.querySelectorAll('thead th');
                const rows = table.querySelectorAll('tbody tr');
                
                rows.forEach(function(row) {
                    const cells = row.querySelectorAll('td');
                    cells.forEach(function(cell, index) {
                        if (headers[index]) {
                            cell.setAttribute('data-label', headers[index].textContent);
                        }
                    });
                });

                // Add mobile card class option
                if (table.classList.contains('mobile-cards')) {
                    table.classList.add('table-mobile-card');
                }
            });
        },

        // Form enhancements for mobile
        initFormEnhancements: function() {
            // Auto-resize textareas
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(function(textarea) {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            });

            // Add floating label support
            const formFloating = document.querySelectorAll('.form-floating');
            formFloating.forEach(function(container) {
                const input = container.querySelector('input, select, textarea');
                if (input && input.value) {
                    container.classList.add('has-value');
                }
                
                if (input) {
                    input.addEventListener('change', function() {
                        if (this.value) {
                            container.classList.add('has-value');
                        } else {
                            container.classList.remove('has-value');
                        }
                    });
                }
            });

            // Improve number inputs with stepper buttons
            const numberInputs = document.querySelectorAll('input[type="number"]');
            numberInputs.forEach(function(input) {
                if (!input.parentElement.classList.contains('number-input-wrapper')) {
                    const wrapper = document.createElement('div');
                    wrapper.className = 'number-input-wrapper';
                    input.parentNode.insertBefore(wrapper, input);
                    wrapper.appendChild(input);

                    const decreaseBtn = document.createElement('button');
                    decreaseBtn.type = 'button';
                    decreaseBtn.className = 'number-decrease';
                    decreaseBtn.innerHTML = '-';
                    
                    const increaseBtn = document.createElement('button');
                    increaseBtn.type = 'button';
                    increaseBtn.className = 'number-increase';
                    increaseBtn.innerHTML = '+';

                    wrapper.appendChild(decreaseBtn);
                    wrapper.appendChild(increaseBtn);

                    decreaseBtn.addEventListener('click', function() {
                        const min = input.min || -Infinity;
                        const step = input.step || 1;
                        const newValue = parseFloat(input.value || 0) - parseFloat(step);
                        if (newValue >= min) {
                            input.value = newValue;
                            input.dispatchEvent(new Event('change'));
                        }
                    });

                    increaseBtn.addEventListener('click', function() {
                        const max = input.max || Infinity;
                        const step = input.step || 1;
                        const newValue = parseFloat(input.value || 0) + parseFloat(step);
                        if (newValue <= max) {
                            input.value = newValue;
                            input.dispatchEvent(new Event('change'));
                        }
                    });
                }
            });
        },

        // Bottom navigation setup
        initBottomNavigation: function() {
            // Create bottom navigation if it doesn't exist
            if (!document.querySelector('.bottom-nav')) {
                const bottomNav = document.createElement('nav');
                bottomNav.className = 'bottom-nav';
                bottomNav.innerHTML = `
                    <a href="/" class="bottom-nav-item active">
                        <i class="fas fa-home"></i>
                        <span>Home</span>
                    </a>
                    <a href="/invoices" class="bottom-nav-item">
                        <i class="fas fa-file-invoice"></i>
                        <span>Invoices</span>
                    </a>
                    <a href="/clients" class="bottom-nav-item">
                        <i class="fas fa-users"></i>
                        <span>Clients</span>
                    </a>
                    <a href="/retrocession" class="bottom-nav-item">
                        <i class="fas fa-calculator"></i>
                        <span>Retrocession</span>
                    </a>
                `;
                document.body.appendChild(bottomNav);

                // Add padding to main content
                const mainContent = document.querySelector('.content-wrapper, .main-content, main');
                if (mainContent) {
                    mainContent.style.paddingBottom = '5rem';
                }
            }

            // Highlight active nav item based on current URL
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.bottom-nav-item');
            navItems.forEach(function(item) {
                if (currentPath.includes(item.getAttribute('href'))) {
                    navItems.forEach(nav => nav.classList.remove('active'));
                    item.classList.add('active');
                }
            });
        },

        // Floating action button
        initFloatingActionButton: function() {
            // Check if there's a primary action on the page
            const primaryAction = document.querySelector('[data-mobile-fab]');
            if (primaryAction) {
                const fab = document.createElement('button');
                fab.className = 'fab';
                fab.innerHTML = primaryAction.getAttribute('data-mobile-fab-icon') || '<i class="fas fa-plus"></i>';
                fab.setAttribute('aria-label', primaryAction.getAttribute('data-mobile-fab-label') || 'Primary action');
                
                fab.addEventListener('click', function() {
                    if (primaryAction.tagName === 'A') {
                        window.location.href = primaryAction.href;
                    } else {
                        primaryAction.click();
                    }
                });

                document.body.appendChild(fab);
            }
        },

        // Mobile-optimized modals
        initMobileModals: function() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(function(modal) {
                // Add mobile class to modals
                modal.classList.add('mobile-optimized');

                // Handle modal scrolling
                modal.addEventListener('shown.bs.modal', function() {
                    document.body.style.position = 'fixed';
                    document.body.style.width = '100%';
                });

                modal.addEventListener('hidden.bs.modal', function() {
                    document.body.style.position = '';
                    document.body.style.width = '';
                });
            });
        },

        // Optimize images for mobile
        optimizeImages: function() {
            // Lazy loading for images
            const images = document.querySelectorAll('img[data-src]');
            const imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.add('loaded');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));

            // Add loading placeholder
            images.forEach(function(img) {
                if (!img.classList.contains('lazy-load')) {
                    img.classList.add('lazy-load');
                }
            });
        }
    };

    // Initialize on DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            MobileEnhancements.init();
        });
    } else {
        MobileEnhancements.init();
    }

    // Re-initialize on dynamic content load
    window.MobileEnhancements = MobileEnhancements;

})();