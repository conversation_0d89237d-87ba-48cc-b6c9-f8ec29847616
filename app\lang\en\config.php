<?php

return array (
  'add_custom_field' => 'Add Custom Field',
  'add_translation' => 'Add Translation',
  'advanced_settings' => 'Advanced Settings',
  'all_available_fields' => 'All available fields',
  'all_changes_saved' => 'All changes saved',
  'all_fields' => 'All Fields',
  'all_groups' => 'All Groups',
  'available_fields' => 'Available Fields',
  'available_languages' => 'Available Languages',
  'available_variables' => 'Available variables',
  'backup_completed' => 'Backup completed successfully',
  'backup_enabled' => 'Backups Enabled',
  'backup_failed' => 'Backup failed',
  'backup_frequency' => 'Backup Frequency',
  'bank_accounts_payment_gateways' => 'Bank accounts, payment gateways, terms',
  'bank_details' => 'Bank Details',
  'bank_name' => 'Bank Name',
  'basic_company_details' => 'Basic company details, logo, contact information',
  'basic_settings' => 'Basic Settings',
  'bic_swift' => 'BIC/SWIFT',
  'cannot_delete_default_field' => 'Cannot delete a default field',
  'category' => 'Category',
  'clear_cache' => 'Clear Cache',
  'client_number_format' => 'Client Number Format',
  'client_number_prefix' => 'Client number prefix',
  'client_number_prefix_hint' => 'Prefix for client numbers (e.g., CLT)',
  'client_numbers' => 'Client Numbers',
  'company_address' => 'Company Address',
  'company_email' => 'Company Email',
  'company_information' => 'Company Information',
  'company_logo' => 'Company Logo',
  'company_name' => 'Company Name',
  'company_phone' => 'Company Phone',
  'company_settings' => 'Company Settings',
  'company_settings_description' => 'Manage your company information, logo and contact details',
  'company_website' => 'Company Website',
  'config.not_translated' => 'Not translated',
  'configuration' => 'Configuration',
  'configuration_areas' => 'Configuration Areas',
  'configuration_management' => 'Configuration Management',
  'configurations' => 'configurations',
  'configure' => 'Configure',
  'configure_invoice_categories' => 'Configure invoice categories',
  'configure_number_formats' => 'Configure number formats for different modules',
  'configure_numbering' => 'Configure numbering',
  'configure_system_settings' => 'Configure system settings like timezone, currency, and formats.',
  'core' => 'Core',
  'counter_reset' => 'Counter reset successfully',
  'create_backup' => 'Create Backup',
  'currency' => 'Currency',
  'currency_symbol' => 'Currency Symbol',
  'custom' => 'Custom',
  'customize_email_messages' => 'Customize email messages',
  'dash' => 'Dash (-)',
  'date_format' => 'Date Format',
  'date_format_short' => 'Short Date Format',
  'decimal_separator' => 'Decimal Separator',
  'default' => 'Default',
  'default_language' => 'Default Language',
  'default_timezone' => 'Default Timezone',
  'default_value' => 'Default Value',
  'default_vat_rate' => 'Default VAT Rate',
  'define_invoice_types' => 'Define different invoice types with custom prefixes and colors.',
  'delete_translation' => 'Delete Translation',
  'delete_translation_confirm' => 'Are you sure you want to delete this translation?',
  'diagnostic' => 'Diagnostic',
  'drag_to_reorder' => 'Drag to reorder',
  'duplicate_keys_found' => 'Duplicate Keys Found',
  'edit_translation' => 'Edit Translation',
  'effective_from' => 'Effective From',
  'effective_to' => 'Effective To',
  'email_settings' => 'Email Settings',
  'email_template' => 'Email Template',
  'email_templates' => 'Email Templates',
  'error_saving_number_formats' => 'An error occurred while saving number format settings',
  'export' => 'Export',
  'export_all' => 'Export All',
  'export_translations' => 'Export Translations',
  'field_already_exists' => 'This field name already exists',
  'field_created' => 'Field created successfully',
  'field_deleted' => 'Field deleted successfully',
  'field_label' => 'Field Label',
  'field_management' => 'Field Management',
  'field_name' => 'Field Name',
  'field_name_hint' => 'Lowercase letters, numbers and underscores only',
  'field_options' => 'Field Options',
  'field_options_hint' => 'One option per line for dropdowns',
  'field_type' => 'Field Type',
  'field_updated' => 'Field updated successfully',
  'file_upload_error' => 'File upload error',
  'financial' => 'Financial',
  'fix_action' => 'Fix Action',
  'fix_missing_description' => 'Choose how to fix missing translations',
  'fix_missing_translations' => 'Fix Missing Translations',
  'form_view_fields' => 'Form View Fields',
  'form_view_settings' => 'Form View Settings',
  'format' => 'Format',
  'format_help' => 'Use {number} for the sequential number, {year} for year, {month} for month',
  'from_email' => 'From Email',
  'from_name' => 'From Name',
  'general_settings' => 'General Settings',
  'group' => 'Group',
  'group_hint' => 'e.g. common, users, config',
  'help_text' => 'Help Text',
  'iban' => 'IBAN',
  'import' => 'Import',
  'import_add_new' => 'Add new only',
  'import_error' => 'Import error',
  'import_file_hint' => 'JSON file with structure {group: {key: value}}',
  'import_merge' => 'Merge (update existing, add new)',
  'import_replace' => 'Replace (delete all existing)',
  'import_success' => 'Import successful: :keys_imported keys imported (:keys_added added, :keys_updated updated)',
  'import_translations' => 'Import Translations',
  'import_type' => 'Import Type',
  'import_type_hint' => 'Choose how to handle existing translations',
  'inconsistent' => 'Inconsistent',
  'interface_translations' => 'Interface translations, document languages',
  'invalid_configuration' => 'Invalid configuration',
  'invalid_field_name' => 'Invalid field name',
  'invalid_file_type' => 'Invalid file type',
  'invalid_format' => 'Invalid format',
  'invalid_json_format' => 'Invalid JSON format',
  'invoice_code' => 'Invoice Code',
  'invoice_color' => 'Color',
  'invoice_configuration' => 'Invoice Configuration',
  'invoice_format' => 'Numbering Format',
  'invoice_icon' => 'Icon',
  'invoice_number_format' => 'Invoice Number Format',
  'invoice_number_prefix' => 'Invoice number prefix',
  'invoice_number_prefix_hint' => 'Prefix for invoice numbers (e.g., INV)',
  'invoice_numbers' => 'Invoice Numbers',
  'invoice_prefix' => 'Invoice Prefix',
  'invoice_settings' => 'Invoice Settings',
  'invoice_type' => 'Document Type',
  'invoice_types' => 'Document Types',
  'add_invoice_type' => 'Add Document Type',
  'edit_invoice_type' => 'Edit Document Type',
  'current_invoice_types' => 'Current Document Types',
  'no_invoice_types_found' => 'No document types found',
  'delete_invoice_type_confirm' => 'Are you sure you want to delete this document type?',
  'invoice_types_management' => 'Document Types Management',
  'document_type' => 'Document Type',
  'document_types' => 'Document Types',
  'add_document_type' => 'Add Document Type',
  'edit_document_type' => 'Edit Document Type',
  'current_document_types' => 'Current Document Types',
  'no_document_types_found' => 'No document types found',
  'delete_document_type_confirm' => 'Are you sure you want to delete this document type?',
  'document_types_management' => 'Document Types Management',
  'format_help' => 'Format Help',
  'available_variables' => 'Available Variables',
  'type_prefix' => 'Type Prefix',
  'current_year_4_digits' => 'Current Year (4 digits)',
  'current_year_2_digits' => 'Current Year (2 digits)',
  'current_month_2_digits' => 'Current Month (2 digits)',
  'current_day_2_digits' => 'Current Day (2 digits)',
  'sequential_number' => 'Sequential Number',
  'examples' => 'Examples',
  'format_examples' => 'Format Examples',
  'code_hint' => 'Uppercase letters and underscores only',
  'format_hint' => 'Use variables in curly braces',
  'preview' => 'Preview',
  'next_number' => 'Next Number',
  'invoice_standard' => 'Standard Invoice',
  'receipt' => 'Receipt',
  'medical_invoice' => 'Medical Invoice',
  'validated_invoice' => 'Validated Invoice',
  'cancelled_invoice' => 'Cancelled Invoice',
  'credit_note' => 'Credit Note',
  'debit_note' => 'Debit Note',
  'is_required' => 'Required Field',
  'is_searchable' => 'Searchable',
  'is_sortable' => 'Sortable',
  'key' => 'Key',
  'key_hint' => 'e.g. welcome_message, button_save',
  'language' => 'Language',
  'languages_to_display' => 'Languages to Display',
  'list_view_fields' => 'List View Fields',
  'list_view_settings' => 'List View Settings',
  'maintenance_mode' => 'Maintenance Mode',
  'manage' => 'Manage',
  'manage_module_fields' => 'Manage visible fields and add custom fields for each module',
  'manage_tax_rates' => 'Manage tax rates',
  'manage_translations' => 'Manage Translations',
  'manage_vat_rates' => 'Manage VAT rates for invoicing and set default rates.',
  'manage_visible_fields_and_add_custom_fields' => 'Manage visible fields and add custom fields for each module',
  'missing' => 'Missing',
  'missing_translations' => 'Missing translations',
  'missing_translations_fixed' => '%d missing translations fixed for %s in group %s',
  'module' => 'Module',
  'monthly_reset' => 'Monthly Reset',
  'multi_language_support' => 'Multi-language Support',
  'multilingual_view' => 'Multilingual View',
  'never_reset' => 'Never Reset',
  'next_invoice_number' => 'Next Invoice Number',
  'next_number' => 'Next Number',
  'no_changes_to_save' => 'No changes to save',
  'no_file_uploaded' => 'No file uploaded',
  'none' => 'None',
  'not_translated' => 'Not translated',
  'number_format' => 'Number Format',
  'number_format_configuration' => 'Number Format Configuration',
  'number_format_saved' => 'Number format saved successfully',
  'number_format_settings_saved' => 'Number format settings saved successfully',
  'number_format_updated' => 'Number format updated successfully',
  'number_formats' => 'Number Formats',
  'number_formats_prefixes' => 'Number formats, prefixes, templates',
  'number_formats_title' => 'Number Formats Configuration',
  'number_length' => 'Number Length',
  'optional' => 'Optional',
  'optional_field' => 'Optional Field',
  'padding' => 'Padding',
  'padding_help' => 'Number of digits to pad with zeros (e.g., 5 → 00001)',
  'patient_number_format' => 'Patient Number Format',
  'patient_number_prefix' => 'Patient number prefix',
  'patient_number_prefix_hint' => 'Prefix for patient numbers (e.g., PAT)',
  'patient_numbers' => 'Patient Numbers',
  'payment_methods' => 'Payment Methods',
  'payment_settings' => 'Payment Settings',
  'placeholder' => 'Placeholder',
  'prefix' => 'Prefix',
  'preview' => 'Preview',
  'quick_actions' => 'Quick Actions',
  'recommended' => 'Recommended',
  'reference' => 'Reference',
  'registration_number' => 'Registration Number',
  'reply_to' => 'Reply To',
  'required_field' => 'Required Field',
  'reset_counter' => 'Reset Counter',
  'restore_defaults' => 'Restore Defaults',
  'save_all_changes' => 'Save All Changes',
  'save_number_format_settings' => 'Save Number Format Settings',
  'save_settings' => 'Save Settings',
  'search_translations' => 'Search translations',
  'select_at_least_two_languages' => 'Please select at least two languages',
  'select_module' => 'Select Module',
  'send_test_email' => 'Send Test Email',
  'separator' => 'Separator',
  'separator_dash' => 'Dash (-)',
  'separator_none' => 'None',
  'separator_slash' => 'Slash (/)',
  'separator_underscore' => 'Underscore (_)',
  'settings' => 'Settings',
  'settings_saved' => 'Settings saved successfully',
  'settings_updated' => 'Settings updated successfully',
  'showing_missing_only' => 'Showing missing translations only',
  'single_language_view' => 'Single Language View',
  'slash' => 'Slash (/)',
  'smtp_encryption' => 'SMTP Encryption',
  'smtp_host' => 'SMTP Host',
  'smtp_password' => 'SMTP Password',
  'smtp_port' => 'SMTP Port',
  'smtp_username' => 'SMTP Username',
  'source' => 'Source',
  'suffix' => 'Suffix',
  'sync_complete' => 'Sync complete',
  'sync_confirm' => 'Do you want to sync translations from files?',
  'sync_from_files' => 'Sync from Files',
  'sync_success' => 'Sync successful: :totalKeys keys synced from :totalFiles files',
  'sync_to_files' => 'Sync to Files',
  'sync_to_files_confirm' => 'Are you sure you want to sync translations to files?',
  'sync_translations' => 'Sync Translations',
  'synchronized' => 'Synchronized',
  'system' => 'System',
  'system_email' => 'System Email',
  'system_name' => 'System Name',
  'system_settings' => 'System Settings',
  'target_language' => 'Target Language',
  'tax_settings' => 'Tax Settings',
  'test_connection' => 'Test Connection',
  'test_email_failed' => 'Failed to send test email',
  'test_email_sent' => 'Test email sent successfully',
  'thousand_separator' => 'Thousand Separator',
  'time_format' => 'Time Format',
  'total_translations' => 'Total Translations',
  'translation_added' => 'Translation added',
  'translation_count_by_group' => 'Translation Count by Group',
  'translation_created' => 'Translation created successfully',
  'translation_deleted' => 'Translation deleted',
  'translation_diagnostic' => 'Translation Diagnostic',
  'translation_editor' => 'Translation Editor',
  'translation_file' => 'Translation File',
  'translation_filters' => 'Translation Filters',
  'translation_groups' => 'Translation Groups',
  'translation_key_exists' => 'This translation key already exists',
  'translation_updated' => 'Translation updated',
  'translations' => 'Translations',
  'translations_description' => 'Manage system translations and customize interface texts',
  'translations_management' => 'Translations Management',
  'translations_updated' => 'Translations updated',
  'type_checkbox' => 'Checkbox',
  'type_date' => 'Date',
  'type_datetime' => 'Date & Time',
  'type_email' => 'Email',
  'type_number' => 'Number',
  'type_phone' => 'Phone',
  'type_select' => 'Dropdown',
  'type_text' => 'Text',
  'type_textarea' => 'Textarea',
  'type_url' => 'URL',
  'underscore' => 'Underscore (_)',
  'unsaved_changes' => 'Unsaved Changes',
  'value' => 'Value',
  'value_hint' => 'Translation text',
  'vat_number' => 'VAT Number',
  'vat_percentage' => 'VAT Percentage',
  'vat_rate' => 'VAT Rate',
  'vat_rates' => 'VAT Rates',
  'view_mode' => 'View Mode',
  'visibility_saved' => 'Visibility saved successfully',
  'visible_fields' => 'Visible Fields',
  'visible_in_form' => 'Visible in Form',
  'visible_in_list' => 'Visible in List',
  'yearly_reset' => 'Yearly Reset',
  'zero_padding_length' => 'Zero-padding length',
  'system_settings_description' => 'Configure system settings, date and time formats, currency',
  'vat_rates_description' => 'Manage VAT rates and applicable taxes',
  'invoice_types_description' => 'Configure different document types for invoices, receipts, credit notes, and other financial documents',
  'document_types_description' => 'Configure different document types for invoices, receipts, credit notes, and other financial documents',
  'field_manager_description' => 'Customize fields and their visibility',
  'email_templates_description' => 'Manage email templates for automated communications',
  'number_formats_description' => 'Configure automatic numbering formats',
  'payment_methods_description' => 'Manage accepted payment methods',
  'rate_profiles' => 'Rate Profiles',
  'rate_profiles_description' => 'Manage pricing profiles and rate schedules',
  'system_status' => 'System Status',
  'active_vat_rates' => 'Active VAT Rates',
  'rates' => 'Rates',
  'types' => 'Types',
  'templates' => 'Templates',
  'custom_fields' => 'Custom Fields',
  'fields' => 'Fields',
  'app_name' => 'Application Name',
  'app_url' => 'Application URL',
  'timezone' => 'Timezone',
  'format_settings' => 'Format Settings',
  'invoice_start_number' => 'Invoice Starting Number',
  'next_invoice_number_hint' => 'The next invoice number will be generated automatically',
  'user_interface' => 'User Interface',
  'admin_template' => 'Admin Template',
  'choose_admin_template' => 'Choose admin template',
  'system_mode' => 'System Mode',
  'maintenance_mode_desc' => 'Enable maintenance mode to prevent public access',
  'account_holder' => 'Account Holder',
  'bank_reference' => 'Bank Reference',
  'invoice_footer_text' => 'Invoice Footer Text',
  'invoice_footer_hint' => 'This text will appear at the bottom of all invoices',
  'default_due_days' => 'Default Payment Terms',
  'due_days_hint' => 'Number of days before payment is due',
  'late_fee_percentage' => 'Late Fee Percentage',
  'no_logo_uploaded' => 'No logo uploaded',
  'logo_requirements' => 'Accepted formats: JPG, PNG, GIF. Maximum size: 2MB. Recommended dimensions: 200x100px',
  'legal_information' => 'Legal Information',
  'legal_form' => 'Legal Form',
  'court_registry' => 'Court Registry',
  'capital_amount' => 'Share Capital',
  'vat_rates_management' => 'VAT Rates Management',
  'add_vat_rate' => 'Add VAT Rate',
  'current_vat_rates' => 'Current VAT Rates',
  'no_vat_rates_found' => 'No VAT rates found',
  'export_import' => 'Export/Import',
  'vat_rate_tip_1' => 'Only one VAT rate can be set as default',
  'vat_rate_tip_2' => 'Effective dates allow you to schedule rate changes',
  'vat_rate_tip_3' => 'Inactive rates will not be available when creating invoices',
  'edit_vat_rate' => 'Edit VAT Rate',
  'set_as_default' => 'Set as Default',
  'import_vat_rates' => 'Import VAT Rates',
  'csv_format_hint' => 'CSV format with columns: name, rate, description',
  'import_format_info' => 'CSV file must contain columns: name, rate, description (optional)',
  'delete_vat_rate_confirm' => 'Are you sure you want to delete this VAT rate?',
  'vat_rate_created' => 'VAT rate created successfully',
  'vat_rate_updated' => 'VAT rate updated successfully',
  'vat_rate_deleted' => 'VAT rate deleted successfully',
  'vat_rate_set_default' => 'VAT rate set as default successfully',
  'default_vat_updated' => 'Default VAT rate updated successfully',
  'name_required' => 'Name is required',
  'code_required' => 'Code is required',
  'rate_range' => 'Rate must be between 0 and 100',
  'vat_rate_insert_failed' => 'Failed to insert VAT rate',
  'vat_rate_create_failed' => 'Failed to create VAT rate',
  'vat_rate_code_exists' => 'A VAT rate with this code already exists. Please use a different code.',
  'set_default_failed' => 'Failed to set default rate: :error',
  'company_settings_updated' => 'Company settings updated successfully',
  'company_name_required' => 'Company name is required',
  'valid_email_required' => 'A valid email address is required',
  'invalid_file_type' => 'Invalid file type. Only JPG, PNG, GIF and WebP files are allowed',
  'upload_failed' => 'File upload failed',
  'invoice_type_created' => 'Document type created successfully',
  'invoice_type_updated' => 'Document type updated successfully',
  'invoice_type_deleted' => 'Document type deleted successfully',
  'invoice_type_code_exists' => 'A document type with this code already exists',
  'invoice_type_create_failed' => 'Failed to create document type',
  'invoice_type_update_failed' => 'Failed to update document type',
  'document_type_created' => 'Document type created successfully',
  'document_type_updated' => 'Document type updated successfully',
  'document_type_deleted' => 'Document type deleted successfully',
  'document_type_code_exists' => 'A document type with this code already exists',
  'document_type_create_failed' => 'Failed to create document type',
  'document_type_update_failed' => 'Failed to update document type',
  'number_digits' => 'Number of digits',
  'digits_hint' => 'Number of digits to display (e.g., 6 → 000001)',
  'prefix_hint' => 'Text to appear before the number',
  'suffix_hint' => 'Text to appear after the number',
  'never' => 'Never',
  'yearly' => 'Yearly',
  'monthly' => 'Monthly',
  'example' => 'Example',
  'quote_number_format' => 'Quote Number Format',
  'current_numbers' => 'Current Numbers',
  'last_invoice_number' => 'Last Invoice Number',
  'last_patient_number' => 'Last Patient Number',
  'last_quote_number' => 'Last Quote Number',
  'date_variables' => 'Date Variables',
  'date_variables_description' => 'Use these variables in your format string',
  'year_4_digits' => 'Year (4 digits)',
  'year_2_digits' => 'Year (2 digits)',
  'month_2_digits' => 'Month (2 digits)',
  'month_1_digit' => 'Month (1 digit)',
  'day_2_digits' => 'Day (2 digits)',
  'day_1_digit' => 'Day (1 digit)',
  'reset_counters' => 'Reset Counters',
  'reset_counters_warning' => 'Warning: This will reset all numbering sequences to start from 1',
  'reset_all_counters' => 'Reset All Counters',
  'reset_counters_confirm' => 'Are you sure you want to reset all counters? This action cannot be undone.',
  'auto_generate_patient_number' => 'Automatically generate patient numbers',
  'number_formats_configuration' => 'Number Formats Configuration',
  'number_format_saved_successfully' => 'Number format settings saved successfully',
  'server_error' => 'Server error occurred',
  'connection_error' => 'Connection error occurred',
  'client_number_format' => 'Client Number Format',
  'auto_generate_client_number' => 'Automatically generate client numbers',
  'last_client_number' => 'Last Client Number',
  'template_variables' => 'Template Variables',
  'available_templates' => 'Available Templates',
  'content' => 'Content',
  'email_subject' => 'Email Subject',
  'email_body' => 'Email Body',
  'email_footer' => 'Email Footer',
  'subject_variables_hint' => 'You can use variables like {patient_name}, {invoice_number}, etc.',
  'body_html_hint' => 'HTML is allowed. Use variables in curly braces.',
  'template_language' => 'Template Language',
  'template_type' => 'Template Type',
  'template_active' => 'Template Active',
  'include_company_logo' => 'Include Company Logo',
  'attach_invoice_pdf' => 'Attach Invoice PDF',
  'restore_default' => 'Restore Default',
  'restore_default_confirm' => 'Are you sure you want to restore the default template? Your changes will be lost.',
  'select_template_to_edit' => 'Select a template to edit',
  'send_test_email' => 'Send Test Email',
  'enter_test_email' => 'Enter email address for testing',
  'invoice_variables' => 'Invoice Variables',
  'patient_variables' => 'Patient Variables',
  'company_variables' => 'Company Variables',
  'subject' => 'Subject',
  'var_invoice_number' => 'Invoice Number',
  'var_invoice_date' => 'Invoice Date',
  'var_due_date' => 'Due Date',
  'var_total_amount' => 'Total Amount',
  'var_subtotal' => 'Subtotal',
  'var_vat_amount' => 'VAT Amount',
  'var_payment_link' => 'Payment Link',
  'var_invoice_link' => 'Invoice Link',
  'var_patient_name' => 'Patient Name',
  'var_patient_email' => 'Patient Email',
  'var_patient_phone' => 'Patient Phone',
  'var_patient_address' => 'Patient Address',
  'var_patient_city' => 'Patient City',
  'var_patient_country' => 'Patient Country',
  'var_company_name' => 'Company Name',
  'var_company_email' => 'Company Email',
  'var_company_phone' => 'Company Phone',
  'var_company_address' => 'Company Address',
  'var_company_website' => 'Company Website',
  'var_company_vat' => 'Company VAT Number',
  'add_payment_method' => 'Add Payment Method',
  'edit_payment_method' => 'Edit Payment Method',
  'no_payment_methods_found' => 'No payment methods found',
  'payment_method_code_hint' => 'Uppercase letters and underscores only (e.g., CASH, BANK_TRANSFER)',
  'delete_payment_method_confirm' => 'Are you sure you want to delete this payment method?',
  'common_payment_methods' => 'Common Payment Methods',
  'cash' => 'Cash',
  'credit_card' => 'Credit Card',
  'bank_transfer' => 'Bank Transfer',
  'check' => 'Check',
  'payment_method_code_exists' => 'A payment method with this code already exists',
  'payment_method_create_failed' => 'Failed to create payment method',
  'payment_method_created' => 'Payment method created successfully',
  'payment_method_update_failed' => 'Failed to update payment method',
  'payment_method_updated' => 'Payment method updated successfully',
  'payment_method_deleted' => 'Payment method deleted successfully',
  'payment_method_in_use' => 'This payment method is in use and cannot be deleted',
  'payment_method_activated' => 'Payment method activated',
  'payment_method_deactivated' => 'Payment method deactivated',
  'current_active_payment_methods' => 'Currently Active Payment Methods',
  'inactive_payment_methods' => 'Inactive Payment Methods',
  'no_active_payment_methods' => 'No active payment methods',
  'payment_terms' => 'Payment Terms',
  'payment_terms_description' => 'Configure payment terms and due date options for invoices',
  'add_payment_term' => 'Add Payment Term',
  'edit_payment_term' => 'Edit Payment Term',
  'payment_days' => 'Payment Days',
  'immediate_payment' => 'Immediate Payment',
  'payment_term_name_hint' => 'e.g., Net 30, Due on Receipt',
  'payment_term_code_hint' => 'Unique identifier (letters, numbers, underscores)',
  'payment_days_hint' => 'Number of days from invoice date (0 for immediate)',
  'payment_term_description_hint' => 'Additional details about this payment term',
  'set_as_default' => 'Set as Default',
  'default_payment_term_hint' => 'This payment term will be selected by default for new invoices',
  'payment_term_code_exists' => 'A payment term with this code already exists',
  'payment_term_created' => 'Payment term created successfully',
  'payment_term_create_failed' => 'Failed to create payment term',
  'payment_term_updated' => 'Payment term updated successfully',
  'payment_term_update_failed' => 'Failed to update payment term',
  'payment_term_deleted' => 'Payment term deleted successfully',
  'payment_term_delete_failed' => 'Failed to delete payment term',
  'payment_term_not_found' => 'Payment term not found',
  'payment_term_in_use' => 'This payment term is in use and cannot be deleted',
  'current_active_payment_terms' => 'Currently Active Payment Terms',
  'inactive_payment_terms' => 'Inactive Payment Terms',
  'no_active_payment_terms' => 'No active payment terms configured',
  'confirm_delete_payment_term' => 'Are you sure you want to delete this payment term',
  'confirm_status_change' => 'Are you sure you want to change the status?',
  'click_to_activate' => 'Click to activate',
  'click_to_deactivate' => 'Click to deactivate',
  'modern_theme_only' => 'Only the Modern theme is currently available',
  'table_columns' => 'Table Columns',
  'table_columns_description' => 'Configure column order and visibility for data tables',
  'invoice_items_columns' => 'Invoice Items Columns',
  'invoice_items_columns_description' => 'Configure invoice item columns based on document and invoice type',
  'default_template' => 'Default Template',
  'default_template_description' => 'This configuration will be used when no specific configuration is defined for a document type',
  'table_column_config' => 'Table Column Configuration',
  'column_configuration' => 'Column Configuration',
  'select_table' => 'Select Table',
  'column_name' => 'Column Name',
  'visible' => 'Visible',
  'position' => 'Position',
  'save_column_order' => 'Save Column Order',
  'column_order_saved' => 'Column order saved successfully',
  'drag_to_reorder' => 'Drag to reorder columns',
  'invoices_table' => 'Invoices Table',
  'users_table' => 'Users Table',
  'clients_table' => 'Clients Table',
  'patients_table' => 'Patients Table',
  
  // Color Schemes
  'color_schemes' => 'Color Schemes',
  'color_schemes_description' => 'Manage and customize the color schemes of your application. Choose from professional themes designed for financial dashboards.',
  'color_scheme' => 'Color Scheme',
  'create_color_scheme' => 'Create Color Scheme',
  'edit_color_scheme' => 'Edit Color Scheme',
  'color_scheme_created' => 'Color scheme created successfully',
  'color_scheme_updated' => 'Color scheme updated successfully',
  'color_scheme_deleted' => 'Color scheme deleted successfully',
  'color_scheme_activated' => 'Color scheme activated successfully',
  'color_scheme_duplicated' => 'Color scheme duplicated successfully',
  
  // Validation
  'color_scheme_code_exists' => 'A color scheme with this code already exists',
  'color_scheme_not_found' => 'Color scheme not found',
  'cannot_edit_system_scheme' => 'System color schemes cannot be edited directly. Please duplicate it first.',
  'cannot_delete_system_scheme' => 'System color schemes cannot be deleted',
  'cannot_delete_default_scheme' => 'The default color scheme cannot be deleted',
  'cannot_delete_active_scheme' => 'Cannot delete the currently active color scheme',
  'missing_required_color' => 'Missing required color: :color',
  
  // Form fields
  'base_scheme' => 'Base Scheme',
  'base_scheme_hint' => 'Select a color scheme to use as a starting point',
  'code_format_hint' => 'Use only lowercase letters, numbers and underscores',
  
  // Colors
  'theme_colors' => 'Theme Colors',
  'interface_colors' => 'Interface Colors',
  'primary_color' => 'Primary Color',
  'secondary_color' => 'Secondary Color',
  'success_color' => 'Success Color',
  'info_color' => 'Info Color',
  'warning_color' => 'Warning Color',
  'danger_color' => 'Danger Color',
  'light_color' => 'Light Color',
  'dark_color' => 'Dark Color',
  'sidebar_bg' => 'Sidebar Background',
  'sidebar_text' => 'Sidebar Text',
  'navbar_bg' => 'Navbar Background',
  'navbar_text' => 'Navbar Text',
  'body_bg' => 'Body Background',
  'card_bg' => 'Card Background',
  'text_primary' => 'Primary Text',
  'border_color' => 'Border Color',
  
  // Preview
  'color_scheme_preview' => 'Color Scheme Preview',
  'sample_content' => 'Sample Content',
  'preview_description' => 'This is how your application will look with the selected color scheme.',
  
  // Actions
  'activate' => 'Activate',
  'confirm_activate_scheme' => 'Are you sure you want to activate this color scheme?',
  'confirm_duplicate_scheme' => 'Are you sure you want to duplicate this color scheme?',
  'confirm_delete_scheme' => 'Are you sure you want to delete this color scheme? This action cannot be undone.',
  
  // Status
  'system' => 'System',
  'default' => 'Default',
  'active' => 'Active',
  
  // Errors
  'color_scheme_create_failed' => 'Failed to create color scheme',
  'color_scheme_update_failed' => 'Failed to update color scheme',
  'color_scheme_delete_failed' => 'Failed to delete color scheme',
  'color_scheme_activation_failed' => 'Failed to activate color scheme',
  'color_scheme_duplicate_failed' => 'Failed to duplicate color scheme',
  
  // Additional
  'basic_info' => 'Basic Information',
  'colors' => 'Colors',
);
