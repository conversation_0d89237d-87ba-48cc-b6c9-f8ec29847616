<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\Invoice;

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $invoiceNumber = $_GET['invoice'] ?? $argv[1] ?? 'FAC-RET25-2025-0198';
    
    echo "=== Deleting Invoice ===\n\n";
    
    // Get the invoice
    $stmt = $pdo->prepare("
        SELECT i.*, it.name as invoice_type_name 
        FROM invoices i
        LEFT JOIN config_invoice_types it ON i.invoice_type_id = it.id
        WHERE i.invoice_number = ?
    ");
    $stmt->execute([$invoiceNumber]);
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "Invoice '$invoiceNumber' not found.\n";
        exit;
    }
    
    echo "Found invoice:\n";
    echo "- Number: " . $invoice['invoice_number'] . "\n";
    echo "- Type: " . ($invoice['invoice_type_name'] ?? 'Standard') . "\n";
    echo "- Status: " . $invoice['status'] . "\n";
    echo "- Total: €" . number_format($invoice['total'], 2) . "\n";
    echo "- Client ID: " . $invoice['client_id'] . "\n";
    echo "- Created: " . date('d/m/Y', strtotime($invoice['created_at'])) . "\n";
    
    // Check if it's a draft
    if ($invoice['status'] !== Invoice::STATUS_DRAFT) {
        echo "\n⚠️  WARNING: This invoice is not a draft (status: {$invoice['status']}).\n";
        echo "Only draft invoices should be deleted. Non-draft invoices should be cancelled instead.\n";
        
        if (php_sapi_name() === 'cli') {
            echo "Continue anyway? (y/n): ";
            $handle = fopen("php://stdin", "r");
            $line = fgets($handle);
            if (trim($line) != 'y') {
                echo "Cancelled.\n";
                exit;
            }
            fclose($handle);
        } else {
            echo "Add ?confirm=yes to the URL to force deletion.\n";
            if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
                exit;
            }
        }
    }
    
    echo "\nDeleting invoice...\n";
    
    // Start transaction
    $pdo->beginTransaction();
    
    try {
        // Store invoice data for deleted_invoice_numbers table
        $documentTypeId = $invoice['document_type_id'];
        $invoiceTypeId = $invoice['invoice_type_id'];
        $sequenceNumber = null;
        
        // Extract sequence number from invoice number
        if (preg_match('/(\d+)$/', $invoice['invoice_number'], $matches)) {
            $sequenceNumber = intval($matches[1]);
        }
        
        // Delete payment allocations
        $stmt = $pdo->prepare("DELETE FROM payment_allocations WHERE invoice_id = ?");
        $stmt->execute([$invoice['id']]);
        $deletedAllocations = $stmt->rowCount();
        if ($deletedAllocations > 0) {
            echo "- Deleted $deletedAllocations payment allocation(s)\n";
        }
        
        // Delete invoice items
        $stmt = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
        $stmt->execute([$invoice['id']]);
        $deletedItems = $stmt->rowCount();
        if ($deletedItems > 0) {
            echo "- Deleted $deletedItems invoice item(s)\n";
        }
        
        // Delete invoice lines (legacy)
        $stmt = $pdo->prepare("DELETE FROM invoice_lines WHERE invoice_id = ?");
        $stmt->execute([$invoice['id']]);
        $deletedLines = $stmt->rowCount();
        if ($deletedLines > 0) {
            echo "- Deleted $deletedLines invoice line(s)\n";
        }
        
        // Delete the invoice
        $stmt = $pdo->prepare("DELETE FROM invoices WHERE id = ?");
        $stmt->execute([$invoice['id']]);
        echo "- Deleted invoice record\n";
        
        // Add to deleted_invoice_numbers table if sequence number was extracted
        if ($sequenceNumber && $documentTypeId) {
            // Determine year and month based on invoice number format
            $year = null;
            $month = null;
            
            if (preg_match('/(\d{4})-(\d{2})-\d+$/', $invoice['invoice_number'], $matches)) {
                $year = intval($matches[1]);
                $month = intval($matches[2]);
            } elseif (preg_match('/(\d{4})-\d+$/', $invoice['invoice_number'], $matches)) {
                $year = intval($matches[1]);
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO deleted_invoice_numbers 
                (document_type_id, invoice_type_id, invoice_number, sequence_number, year, month, deleted_at, deleted_by)
                VALUES (?, ?, ?, ?, ?, ?, NOW(), ?)
            ");
            $stmt->execute([
                $documentTypeId,
                $invoiceTypeId,
                $invoice['invoice_number'],
                $sequenceNumber,
                $year,
                $month,
                $_SESSION['user_id'] ?? 1
            ]);
            echo "- Added invoice number to reusable numbers\n";
        }
        
        // Log the deletion
        $stmt = $pdo->prepare("
            INSERT INTO activity_log (user_id, action, model_type, model_id, description, ip_address, created_at)
            VALUES (?, 'delete', 'invoice', ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $_SESSION['user_id'] ?? 1,
            $invoice['id'],
            "Deleted invoice {$invoice['invoice_number']} (status: {$invoice['status']}, total: €" . number_format($invoice['total'], 2) . ")",
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
        ]);
        echo "- Logged deletion activity\n";
        
        $pdo->commit();
        
        echo "\n✓ Successfully deleted invoice '{$invoiceNumber}'!\n";
        echo "\nThe invoice number may be reused for future invoices if configured.\n";
        
    } catch (Exception $e) {
        $pdo->rollBack();
        echo "\n❌ Error: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}