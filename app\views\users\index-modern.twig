{% extends "base-modern.twig" %}

{% block title %}{{ __('users.users') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('users.users') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/users/groups" class="btn btn-info">
                <i class="bi bi-people me-2"></i>{{ __('users.user_groups') }}
            </a>
            <a href="{{ base_url }}/users/create" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>{{ __('users.add_user') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('users.total_users') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('users.active_users') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.active_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-check text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('users.user_groups') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_groups|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-diagram-3 text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('users.online_now') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.online_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-circle-fill text-success fs-6"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 fw-bold text-primary">{{ __('users.user_list') }}</h6>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" placeholder="{{ __('common.search') }}..." id="searchInput">
                        <button class="btn btn-sm btn-outline-secondary" type="button">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body">
            <table class="table table-hover" id="usersTable">
                    <thead>
                        <tr>
                            <th>{{ __('users.user') }}</th>
                            <th>{{ __('users.username') }}</th>
                            <th>{{ __('common.email') }}</th>
                            <th>{{ __('users.groups') }}</th>
                            <th>{{ __('users.last_login') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    {% if user.avatar %}
                                        <img src="{{ user.avatar starts with base_url ? user.avatar : base_url ~ '/uploads/avatars/' ~ user.avatar }}" 
                                             class="rounded-circle me-2" style="width: 40px; height: 40px;" 
                                             alt="{{ user.name }}">
                                    {% else %}
                                        <div class="avatar avatar-sm me-2">
                                            <span class="avatar-text rounded-circle bg-primary">
                                                {{ user.name|first|upper }}
                                            </span>
                                        </div>
                                    {% endif %}
                                    <div>
                                        <div class="fw-bold">{{ user.name }}</div>
                                        <small class="text-muted">ID: {{ user.id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ user.username }}</td>
                            <td>
                                <a href="mailto:{{ user.email }}">{{ user.email }}</a>
                            </td>
                            <td>
                                {% if user.groups %}
                                    {{ user.groups }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.last_login %}
                                    {{ user.last_login|date('d/m/Y H:i') }}
                                {% else %}
                                    <span class="text-muted">{{ __('users.never') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if user.is_active %}
                                    <span class="badge bg-success">{{ __('common.active') }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ __('common.inactive') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ base_url }}/users/{{ user.id }}" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.view') }}">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ base_url }}/users/{{ user.id }}/edit" 
                                       class="btn btn-outline-secondary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    {% if user.id != session.user_id %}
                                    <button type="button" 
                                            class="btn btn-outline-{{ user.is_active ? 'warning' : 'success' }}" 
                                            onclick="toggleUserStatus({{ user.id }}, {{ user.is_active ? 'false' : 'true' }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ user.is_active ? __('users.deactivate') : __('users.activate') }}">
                                        <i class="bi bi-{{ user.is_active ? 'x-circle' : 'check-circle' }}"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-info" 
                                            onclick="resetPassword({{ user.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('users.reset_password') }}">
                                        <i class="bi bi-key"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="deleteUser({{ user.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center py-4 text-muted">
                                {{ __('users.no_users_found') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            
            <!-- Pagination -->
            {% if users|length > 0 %}
                <div class="d-flex justify-content-center mt-4">
                    {{ include('_partials/pagination.twig', {
                        'current_page': current_page,
                        'total_pages': total_pages,
                        'base_url': base_url ~ '/users'
                    }) }}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Search functionality
    $('#searchInput').on('keyup', function() {
        var value = $(this).val().toLowerCase();
        $('#usersTable tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});

function toggleUserStatus(id, activate) {
    const action = activate ? '{{ __("users.activate_confirm") }}' : '{{ __("users.deactivate_confirm") }}';
    if (confirm(action)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token }}');
        
        fetch('{{ base_url }}/users/' + id + '/toggle-active', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("common.error") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("common.error_occurred") }}');
        });
    }
}

function resetPassword(userId) {
    // Find the user row and get the user name
    let userName = 'this user';
    
    // Look for the dropdown button that was clicked and traverse to find the user name
    const dropdownBtn = event.currentTarget || event.target;
    if (dropdownBtn) {
        const row = dropdownBtn.closest('tr');
        if (row) {
            const nameElement = row.querySelector('.fw-bold');
            if (nameElement) {
                userName = nameElement.textContent.trim();
            }
        }
    }
    
    // Update modal with user info
    document.getElementById('resetUserId').value = userId;
    document.getElementById('resetUserName').textContent = userName;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function deleteUser(id) {
    if (confirm('{{ __("users.delete_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/users/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}
</script>


<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="resetPasswordForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" id="resetUserId" name="user_id" value="">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('users.reset_password_for') }} <span id="resetUserName"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('users.reset_password_warning') }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password_reset" class="form-label">{{ __('users.new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password_reset" name="new_password" required minlength="6">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('new_password_reset')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">{{ __('users.password_requirements') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password_reset" class="form-label">{{ __('users.confirm_new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password_reset" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('confirm_password_reset')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1">
                        <label class="form-check-label" for="send_email">
                            {{ __('users.send_password_email') }}
                        </label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-warning">{{ __('users.reset_password') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Password visibility toggle
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('bi-eye');
        button.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('bi-eye-slash');
        button.classList.add('bi-eye');
    }
}

// Handle reset password form submission
document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const userId = document.getElementById('resetUserId').value;
    const newPassword = document.getElementById('new_password_reset').value;
    const confirmPassword = document.getElementById('confirm_password_reset').value;
    
    if (newPassword !== confirmPassword) {
        alert('{{ __("users.password_mismatch") }}');
        return;
    }
    
    // Update form action with user ID
    this.action = '{{ base_url }}/users/' + userId + '/reset-password';
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message || '{{ __("users.password_reset_success") }}');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
            // Clear form
            document.getElementById('new_password_reset').value = '';
            document.getElementById('confirm_password_reset').value = '';
            document.getElementById('send_email').checked = false;
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
});
</script>
{% endblock %}