{% extends "base-modern.twig" %}

{% block title %}{{ __('users.users') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('users.users') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/retrocession/bulk-monthly" class="btn btn-success">
                <i class="bi bi-calculator me-2"></i>{{ __('retrocession.bulk_monthly') | default('Rétrocession mensuelle') }}
            </a>
            <a href="{{ base_url }}/users/groups" class="btn btn-info">
                <i class="bi bi-people me-2"></i>{{ __('users.user_groups') }}
            </a>
            <a href="{{ base_url }}/users/create" class="btn btn-primary">
                <i class="bi bi-person-plus me-2"></i>{{ __('users.add_user') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('users.total_users') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-people text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('users.active_users') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.active_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-person-check text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-info h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-info text-uppercase mb-1">
                                {{ __('users.user_groups') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_groups|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-diagram-3 text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('users.online_now') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.online_users|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-circle-fill text-success fs-6"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table with Tabs -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0 fw-bold text-primary">{{ __('users.user_list') }}</h6>
                </div>
                <div class="col-auto">
                    <div class="d-flex gap-2">
                        <div class="input-group">
                            <input type="text" class="form-control form-control-sm" placeholder="{{ __('common.search') }}..." id="searchInput">
                            <button class="btn btn-sm btn-outline-secondary" type="button">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="bi bi-download me-1"></i>{{ __('common.export') }}
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><a class="dropdown-item" href="#" onclick="exportUsers('csv')">CSV</a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportUsers('excel')">Excel</a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportUsers('pdf')">PDF</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card-body pb-0">
            <!-- Tab Navigation -->
            <ul class="nav nav-tabs nav-fill mb-0" id="userGroupTabs" role="tablist">
                <!-- All Users Tab (First) -->
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if not currentUserGroupId %}active{% endif %}" id="all-users-tab" data-bs-toggle="tab" data-bs-target="#all-users" type="button" role="tab" aria-controls="all-users" aria-selected="{% if not currentUserGroupId %}true{% else %}false{% endif %}">
                        <i class="bi bi-people-fill me-2"></i>
                        {{ __('users.all_users') }}
                        <span class="badge rounded-pill bg-white text-primary ms-2">
                            {{ users|length }}
                        </span>
                    </button>
                </li>
                
                <!-- Group Tabs -->
                {% for group in groups %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link group-tab {% if currentUserGroupId == group.id %}active{% endif %}" 
                            id="group-{{ group.id }}-tab" 
                            data-bs-toggle="tab" 
                            data-bs-target="#group-{{ group.id }}" 
                            type="button" 
                            role="tab" 
                            aria-controls="group-{{ group.id }}" 
                            aria-selected="{% if currentUserGroupId == group.id %}true{% else %}false{% endif %}"
                            data-group-color="{{ group.color|default('#6c757d') }}">
                        {% if group.icon %}
                            {% if 'bi' in group.icon %}
                                <i class="{{ group.icon }} me-2"></i>
                            {% else %}
                                <i class="fas fa-{{ group.icon }} me-2"></i>
                            {% endif %}
                        {% else %}
                            <i class="bi bi-people me-2"></i>
                        {% endif %}
                        {{ group.name }}
                        <span class="badge rounded-pill ms-2 tab-badge">
                            {{ usersByGroup[group.id]|length|default(0) }}
                        </span>
                    </button>
                </li>
                {% endfor %}
                
                <!-- No Group Tab (Last) -->
                {% if noGroupUsers|length > 0 %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="no-group-tab" data-bs-toggle="tab" data-bs-target="#no-group" type="button" role="tab" aria-controls="no-group" aria-selected="false">
                        <i class="bi bi-person-dash me-2"></i>
                        {{ __('users.no_group') }}
                        <span class="badge rounded-pill bg-white text-secondary ms-2">
                            {{ noGroupUsers|length }}
                        </span>
                    </button>
                </li>
                {% endif %}
            </ul>
        </div>
        
        <div class="card-body pt-0">
            <!-- Tab Content -->
            <div class="tab-content" id="userGroupTabContent">
                <!-- All Users Tab -->
                <div class="tab-pane fade {% if not currentUserGroupId %}show active{% endif %}" id="all-users" role="tabpanel" aria-labelledby="all-users-tab" tabindex="0">
                    {{ include('users/_user_table.twig', {'tabUsers': users, 'tabId': 'all'}) }}
                </div>

                <!-- Group Tabs -->
                {% for group in groups %}
                <div class="tab-pane fade {% if currentUserGroupId == group.id %}show active{% endif %}" id="group-{{ group.id }}" role="tabpanel" aria-labelledby="group-{{ group.id }}-tab" tabindex="0">
                    {% if usersByGroup[group.id] is defined %}
                        {{ include('users/_user_table.twig', {'tabUsers': usersByGroup[group.id], 'tabId': 'group-' ~ group.id}) }}
                    {% else %}
                        <div class="text-center py-5">
                            <i class="bi bi-people-fill text-muted fs-1 mb-3 d-block"></i>
                            <p class="text-muted">{{ __('users.no_users_in_group') }}</p>
                        </div>
                    {% endif %}
                </div>
                {% endfor %}

                <!-- No Group Tab -->
                {% if noGroupUsers|length > 0 %}
                <div class="tab-pane fade" id="no-group" role="tabpanel" aria-labelledby="no-group-tab" tabindex="0">
                    {{ include('users/_user_table.twig', {'tabUsers': noGroupUsers, 'tabId': 'no-group'}) }}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="resetPasswordForm" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" id="resetUserId" name="user_id" value="">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('users.reset_password_for') }} <span id="resetUserName"></span></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        {{ __('users.reset_password_warning') }}
                    </div>
                    
                    <div class="mb-3">
                        <label for="new_password_reset" class="form-label">{{ __('users.new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="new_password_reset" name="new_password" required minlength="6">
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('new_password_reset')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                        <small class="text-muted">{{ __('users.password_requirements') }}</small>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password_reset" class="form-label">{{ __('users.confirm_new_password') }} *</label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="confirm_password_reset" name="confirm_password" required>
                            <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('confirm_password_reset')">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="send_email" name="send_email" value="1">
                        <label class="form-check-label" for="send_email">
                            {{ __('users.send_password_email') }}
                        </label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-warning">{{ __('users.reset_password') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Enhanced tab styling */
#userGroupTabs {
    border-bottom: none;
    gap: 0.25rem;
}

#userGroupTabs .nav-link {
    color: #495057;
    border: none;
    border-radius: 0.5rem 0.5rem 0 0;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    background-color: #f8f9fa;
    margin-bottom: -1px;
}

#userGroupTabs .nav-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* All Users Tab */
#all-users-tab {
    background-color: #e7f3ff;
    color: #0d6efd;
}

#all-users-tab:hover {
    background-color: #cfe2ff;
}

#all-users-tab.active {
    background-color: #0d6efd;
    color: white;
}

/* No Group Tab */
#no-group-tab {
    background-color: #e9ecef;
    color: #6c757d;
}

#no-group-tab:hover {
    background-color: #dee2e6;
}

#no-group-tab.active {
    background-color: #6c757d;
    color: white;
}

/* Group tab colors - using exact colors from database */
{% for group in groups %}
{% set groupColor = group.color|default('#6c757d') %}
#group-{{ group.id }}-tab {
    background-color: {{ groupColor }}33; /* 20% opacity */
    color: {{ groupColor }};
    border-bottom: 3px solid transparent;
}

#group-{{ group.id }}-tab:hover {
    background-color: {{ groupColor }}4D; /* 30% opacity */
    border-bottom-color: {{ groupColor }}80; /* 50% opacity */
}

#group-{{ group.id }}-tab.active {
    background-color: {{ groupColor }};
    color: white;
    border-bottom-color: {{ groupColor }};
}

#group-{{ group.id }}-tab.active .tab-badge {
    background-color: white !important;
    color: {{ groupColor }} !important;
}

/* Ensure text is readable on dark backgrounds */
{% if groupColor in ['#28a745', '#dc3545', '#6f42c1', '#6610f2', '#795548', '#9c27b0'] %}
#group-{{ group.id }}-tab:not(.active) {
    color: {{ groupColor }}E6; /* Slightly darker for better contrast */
}
{% endif %}
{% endfor %}

/* Badge styling */
#userGroupTabs .nav-link .badge {
    font-size: 0.75rem;
    padding: 0.25em 0.6em;
    font-weight: 600;
}

/* Tab-specific badge colors */
{% for group in groups %}
#group-{{ group.id }}-tab:not(.active) .tab-badge {
    background-color: {{ group.color|default('#6c757d') }};
    color: white;
}
{% endfor %}

/* All Users tab badge */
#all-users-tab:not(.active) .badge {
    background-color: #0d6efd;
    color: white;
}

/* No Group tab badge */
#no-group-tab:not(.active) .badge {
    background-color: #6c757d;
    color: white;
}

/* Mobile responsive tabs */
@media (max-width: 768px) {
    #userGroupTabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    #userGroupTabs::-webkit-scrollbar {
        display: none;
    }
    
    #userGroupTabs .nav-item {
        flex-shrink: 0;
    }
    
    #userGroupTabs .nav-link {
        white-space: nowrap;
        padding: 0.5rem 1rem;
    }
    
    /* Remove nav-fill on mobile */
    #userGroupTabs.nav-fill .nav-item {
        flex: 0 0 auto;
    }
}

/* Avatar styling in table */
.avatar {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.avatar-text {
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}
</style>

<script>
// Export users with current filters
function exportUsers(format) {
    const params = new URLSearchParams();
    
    // Add format
    params.append('format', format);
    
    // Add current search value
    const searchValue = $('#searchInput').val();
    if (searchValue) {
        params.append('search', searchValue);
    }
    
    // Add current active group ID (from active tab)
    const activeTab = $('.nav-tabs .nav-link.active');
    const groupId = activeTab.data('group-id');
    if (groupId) {
        params.append('group_id', groupId);
    }
    
    // Redirect to export URL with parameters
    window.location.href = '{{ base_url }}/users/export?' + params.toString();
}

$(document).ready(function() {
    // Initialize Bootstrap tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Remember last selected tab - but only if user has no default group
    {% if not currentUserGroupId %}
    const savedTab = localStorage.getItem('userListActiveTab');
    if (savedTab) {
        const tabTrigger = document.querySelector(`#${savedTab}-tab`);
        if (tabTrigger) {
            const tab = new bootstrap.Tab(tabTrigger);
            tab.show();
        }
    }
    {% else %}
    // User has a default group, clear any saved tab preference
    localStorage.removeItem('userListActiveTab');
    {% endif %}
    
    // Save selected tab
    document.querySelectorAll('#userGroupTabs button[data-bs-toggle="tab"]').forEach(tab => {
        tab.addEventListener('shown.bs.tab', function (event) {
            const tabId = event.target.id.replace('-tab', '');
            localStorage.setItem('userListActiveTab', tabId);
        });
    });
    
    // Search functionality - works within active tab
    $('#searchInput').on('keyup', function() {
        const value = $(this).val().toLowerCase();
        const activeTabPane = $('.tab-pane.active');
        
        activeTabPane.find('tbody tr').filter(function() {
            $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
        });
    });
});

function toggleUserStatus(id, activate) {
    const action = activate ? '{{ __("users.activate_confirm") }}' : '{{ __("users.deactivate_confirm") }}';
    if (confirm(action)) {
        const formData = new FormData();
        formData.append('csrf_token', '{{ csrf_token }}');
        
        fetch('{{ base_url }}/users/' + id + '/toggle-active', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert(data.message || '{{ __("common.error") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("common.error_occurred") }}');
        });
    }
}

function resetPassword(userId) {
    // Find the user row and get the user name
    let userName = 'this user';
    
    // Look for the dropdown button that was clicked and traverse to find the user name
    const dropdownBtn = event.currentTarget || event.target;
    if (dropdownBtn) {
        const row = dropdownBtn.closest('tr');
        if (row) {
            const nameElement = row.querySelector('.fw-bold');
            if (nameElement) {
                userName = nameElement.textContent.trim();
            }
        }
    }
    
    // Update modal with user info
    document.getElementById('resetUserId').value = userId;
    document.getElementById('resetUserName').textContent = userName;
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('resetPasswordModal'));
    modal.show();
}

function deleteUser(id) {
    if (confirm('{{ __("users.delete_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/users/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Password visibility toggle
function togglePasswordVisibility(fieldId) {
    const field = document.getElementById(fieldId);
    const button = field.nextElementSibling.querySelector('i');
    
    if (field.type === 'password') {
        field.type = 'text';
        button.classList.remove('bi-eye');
        button.classList.add('bi-eye-slash');
    } else {
        field.type = 'password';
        button.classList.remove('bi-eye-slash');
        button.classList.add('bi-eye');
    }
}

// Handle reset password form submission
document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const userId = document.getElementById('resetUserId').value;
    const newPassword = document.getElementById('new_password_reset').value;
    const confirmPassword = document.getElementById('confirm_password_reset').value;
    
    if (newPassword !== confirmPassword) {
        alert('{{ __("users.password_mismatch") }}');
        return;
    }
    
    // Update form action with user ID
    this.action = '{{ base_url }}/users/' + userId + '/reset-password';
    
    const formData = new FormData(this);
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message || '{{ __("users.password_reset_success") }}');
            bootstrap.Modal.getInstance(document.getElementById('resetPasswordModal')).hide();
            // Clear form
            document.getElementById('new_password_reset').value = '';
            document.getElementById('confirm_password_reset').value = '';
            document.getElementById('send_email').checked = false;
        } else {
            alert(data.message || '{{ __("common.error") }}');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("common.error_occurred") }}');
    });
});
</script>
{% endblock %}