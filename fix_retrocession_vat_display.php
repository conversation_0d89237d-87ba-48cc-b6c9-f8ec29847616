<?php
/**
 * Fix VAT display for retrocession invoices
 */

echo "<pre>";
echo "=== Fixing VAT Display for Retrocession Invoices ===\n\n";

// 1. Update create-modern.twig
$createFile = __DIR__ . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($createFile);

// Find the section where VAT intracommunautaire is displayed
$searchPattern = "// Intracommunity VAT status
            if (userData.is_intracommunity || userData.vat_intercommunautaire) {";

$replaceWith = "// Intracommunity VAT status
            // Don't show VAT info for retrocession invoices
            const isRetrocessionInvoice = window.location.search.includes('type=retrocession') || 
                                        (typeCode && typeCode.startsWith('RET'));
            
            if (!isRetrocessionInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {";

if (strpos($content, "!isRetrocessionInvoice && (userData.is_intracommunity") === false) {
    $content = str_replace($searchPattern, $replaceWith, $content);
    
    // Also update the VAT number display section
    $vatNumberSearch = "// VAT number
            if (userData.vat_number) {";
    $vatNumberReplace = "// VAT number
            if (!isRetrocessionInvoice && userData.vat_number) {";
    
    $content = str_replace($vatNumberSearch, $vatNumberReplace, $content);
    
    // Add check at the beginning of displayBillableInfo function
    $functionSearch = "function displayBillableInfo(billableType, billableId) {
    console.log('🔍 displayBillableInfo called:', billableType, billableId);
    
    if (!billableId) {";
    
    $functionReplace = "function displayBillableInfo(billableType, billableId) {
    console.log('🔍 displayBillableInfo called:', billableType, billableId);
    
    // Check if this is a retrocession invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect ? invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex] : null;
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const isRetrocessionInvoice = window.location.search.includes('type=retrocession') || 
                                  (typeCode && typeCode.startsWith('RET'));
    
    if (!billableId) {";
    
    $content = str_replace($functionSearch, $functionReplace, $content);
    
    file_put_contents($createFile, $content);
    echo "✓ Updated create-modern.twig to hide VAT info for retrocession invoices\n";
} else {
    echo "⚠ create-modern.twig already has retrocession VAT check\n";
}

// 2. Update show-modern.twig
$showFile = __DIR__ . '/app/views/invoices/show-modern.twig';
$showContent = file_get_contents($showFile);

// Find VAT information section
$vatSectionSearch = "{# VAT Information #}
                                    {% if invoice.user.vat_intercommunautaire %}";

$vatSectionReplace = "{# VAT Information - Hide for retrocession invoices #}
                                    {% set invoiceTypeCode = invoice.invoice_type.code|default('') %}
                                    {% set isRetrocessionInvoice = (invoiceTypeCode starts with 'RET') or (invoice.invoice_type.id in [2, 15]) %}
                                    
                                    {% if not isRetrocessionInvoice and invoice.user.vat_intercommunautaire %}";

if (strpos($showContent, "not isRetrocessionInvoice and invoice.user.vat_intercommunautaire") === false) {
    $showContent = str_replace($vatSectionSearch, $vatSectionReplace, $showContent);
    
    // Also update the elseif for vat_number
    $vatNumberSearch = "{% elseif invoice.user.vat_number %}";
    $vatNumberReplace = "{% elseif not isRetrocessionInvoice and invoice.user.vat_number %}";
    
    $showContent = str_replace($vatNumberSearch, $vatNumberReplace, $showContent);
    
    file_put_contents($showFile, $showContent);
    echo "✓ Updated show-modern.twig to hide VAT info for retrocession invoices\n";
} else {
    echo "⚠ show-modern.twig already has retrocession VAT check\n";
}

// 3. Create cleanup script for default postal codes
$cleanupScript = '<?php
/**
 * Clean up default postal codes for users without actual addresses
 */

require_once __DIR__ . \'/vendor/autoload.php\';
require_once __DIR__ . \'/app/config/bootstrap.php\';

echo "<pre>";
echo "=== Cleaning Up Default Postal Codes ===\\n\\n";

try {
    $db = Flight::db();
    
    // Find users with default postal code but no VAT number
    $stmt = $db->query("
        SELECT id, username, first_name, last_name, 
               postal_code, city, address,
               vat_number, vat_intercommunautaire,
               billing_postal_code, billing_city, billing_address
        FROM users
        WHERE postal_code = \'L-9579\'
        AND (vat_number IS NULL OR vat_number = \'\')
        AND (vat_intercommunautaire IS NULL OR vat_intercommunautaire = \'\')
        AND is_practitioner = 1
    ");
    
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($users) . " practitioners with default postal code but no VAT number\\n\\n";
    
    foreach ($users as $user) {
        echo "User: " . $user[\'first_name\'] . " " . $user[\'last_name\'] . " (" . $user[\'username\'] . ")\\n";
        echo "  Current postal code: " . $user[\'postal_code\'] . "\\n";
        echo "  Current city: " . $user[\'city\'] . "\\n";
        
        // Check if they have billing address info
        if (!empty($user[\'billing_postal_code\']) && $user[\'billing_postal_code\'] != \'L-9579\') {
            echo "  Has different billing postal code: " . $user[\'billing_postal_code\'] . "\\n";
            echo "  → Keeping address info as it may be intentional\\n";
        } else {
            echo "  → Clearing default address values\\n";
            
            // Clear default values
            $updateStmt = $db->prepare("
                UPDATE users 
                SET postal_code = NULL,
                    city = NULL,
                    address = NULL
                WHERE id = :id
                AND postal_code = \'L-9579\'
                AND city = \'Weidingen\'
                AND address = \'15, am Pëtz\'
            ");
            $updateStmt->execute([\'id\' => $user[\'id\']]);
        }
        echo "\\n";
    }
    
    echo "\\n✅ Cleanup complete!\\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\\n";
}
echo "</pre>";';

file_put_contents(__DIR__ . '/cleanup_default_postal_codes.php', $cleanupScript);
echo "\n✓ Created cleanup_default_postal_codes.php script\n";

echo "\n=== Summary ===\n";
echo "1. Modified invoice creation view to hide VAT info for retrocession invoices\n";
echo "2. Modified invoice show view to hide VAT info for retrocession invoices\n";
echo "3. Created cleanup script for default postal codes\n";
echo "\nRun the cleanup script to remove default postal codes:\n";
echo "http://localhost/fit/cleanup_default_postal_codes.php\n";

echo "</pre>";