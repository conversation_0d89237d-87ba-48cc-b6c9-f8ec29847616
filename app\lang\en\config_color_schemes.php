<?php

return [
    // Color Schemes
    'color_schemes' => 'Color Schemes',
    'color_schemes_description' => 'Customize the visual appearance with professional color themes',
    'color_scheme' => 'Color Scheme',
    'create_color_scheme' => 'Create Color Scheme',
    'edit_color_scheme' => 'Edit Color Scheme',
    'color_scheme_created' => 'Color scheme created successfully',
    'color_scheme_updated' => 'Color scheme updated successfully',
    'color_scheme_deleted' => 'Color scheme deleted successfully',
    'color_scheme_activated' => 'Color scheme activated successfully',
    'color_scheme_duplicated' => 'Color scheme duplicated successfully',
    'color_schemes_description' => 'Manage and customize color schemes for your application. Choose from professional themes designed for financial dashboards.',
    
    // Validation
    'color_scheme_code_exists' => 'A color scheme with this code already exists',
    'color_scheme_not_found' => 'Color scheme not found',
    'cannot_edit_system_scheme' => 'System color schemes cannot be edited directly. Please duplicate it first.',
    'cannot_delete_system_scheme' => 'System color schemes cannot be deleted',
    'cannot_delete_default_scheme' => 'The default color scheme cannot be deleted',
    'cannot_delete_active_scheme' => 'Cannot delete the currently active color scheme',
    'missing_required_color' => 'Missing required color: :color',
    
    // Form fields
    'base_scheme' => 'Base Scheme',
    'base_scheme_hint' => 'Select a color scheme to use as the starting point',
    'code_format_hint' => 'Use lowercase letters, numbers, and underscores only',
    
    // Colors
    'theme_colors' => 'Theme Colors',
    'interface_colors' => 'Interface Colors',
    'primary_color' => 'Primary Color',
    'secondary_color' => 'Secondary Color',
    'success_color' => 'Success Color',
    'info_color' => 'Info Color',
    'warning_color' => 'Warning Color',
    'danger_color' => 'Danger Color',
    'light_color' => 'Light Color',
    'dark_color' => 'Dark Color',
    'sidebar_bg' => 'Sidebar Background',
    'sidebar_text' => 'Sidebar Text',
    'navbar_bg' => 'Navbar Background',
    'navbar_text' => 'Navbar Text',
    'body_bg' => 'Body Background',
    'card_bg' => 'Card Background',
    'text_primary' => 'Primary Text',
    'border_color' => 'Border Color',
    
    // Preview
    'color_scheme_preview' => 'Color Scheme Preview',
    'sample_content' => 'Sample Content',
    'preview_description' => 'This is how your application will look with the selected color scheme.',
    
    // Actions
    'activate' => 'Activate',
    'confirm_activate_scheme' => 'Are you sure you want to activate this color scheme?',
    'confirm_duplicate_scheme' => 'Are you sure you want to duplicate this color scheme?',
    'confirm_delete_scheme' => 'Are you sure you want to delete this color scheme? This action cannot be undone.',
    
    // Status
    'system' => 'System',
    'default' => 'Default',
    'active' => 'Active',
    
    // Errors
    'color_scheme_create_failed' => 'Failed to create color scheme',
    'color_scheme_update_failed' => 'Failed to update color scheme',
    'color_scheme_delete_failed' => 'Failed to delete color scheme',
    'color_scheme_activation_failed' => 'Failed to activate color scheme',
    'color_scheme_duplicate_failed' => 'Failed to duplicate color scheme',
];