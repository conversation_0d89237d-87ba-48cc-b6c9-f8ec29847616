{% extends "base-modern.twig" %}

{% block title %}{{ __('categories.title') | default('Product Categories') }}{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products">{{ __('products.title') | default('Products') }}</a></li>
    <li class="breadcrumb-item active">{{ __('categories.title') | default('Categories') }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        {% if success %}
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ success }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endif %}
        
        {% if error %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ error }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endif %}
        
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">{{ __('categories.title') | default('Product Categories') }}</h1>
            <a href="{{ base_url }}/products/categories/create" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>{{ __('categories.create') | default('New Category') }}
            </a>
        </div>

        <!-- Categories List -->
        <div class="card shadow-sm">
            <div class="card-body">
                {% if categories is empty %}
                    <div class="text-center py-5">
                        <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                        <p class="text-muted">{{ __('categories.no_categories') | default('No categories found') }}</p>
                        <a href="{{ base_url }}/products/categories/create" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>{{ __('categories.create_first') | default('Create First Category') }}
                        </a>
                    </div>
                {% else %}
                    <div class="table-responsive">
                        <table class="table table-hover" id="categoriesTable">
                            <thead>
                                <tr>
                                    <th width="50">{{ __('common.order') | default('Order') }}</th>
                                    <th>{{ __('categories.name') | default('Name') }}</th>
                                    <th>{{ __('categories.products_count') | default('Products') }}</th>
                                    <th>{{ __('common.status') | default('Status') }}</th>
                                    <th width="150">{{ __('common.actions') | default('Actions') }}</th>
                                </tr>
                            </thead>
                            <tbody id="sortable-categories">
                                {% for category in categories %}
                                <tr data-id="{{ category.id }}">
                                    <td>
                                        <i class="fas fa-grip-vertical text-muted handle" style="cursor: move;"></i>
                                        {{ category.sort_order }}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if category.icon %}
                                                <i class="{{ category.icon }} me-2" style="color: {{ category.color }}; font-size: 1.2rem;"></i>
                                            {% endif %}
                                            <div>
                                                <strong>{{ category.name }}</strong>
                                                {% if category.description %}
                                                    <br><small class="text-muted">{{ category.description }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ category.products_count | default(0) }}</span>
                                    </td>
                                    <td>
                                        {% if category.is_active %}
                                            <span class="badge bg-success">{{ __('common.active') | default('Active') }}</span>
                                        {% else %}
                                            <span class="badge bg-danger">{{ __('common.inactive') | default('Inactive') }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{{ base_url }}/products/categories/{{ category.id }}/edit" 
                                               class="btn btn-sm btn-outline-primary" title="{{ __('common.edit') | default('Edit') }}">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteCategory({{ category.id }})" 
                                                    title="{{ __('common.delete') | default('Delete') }}"
                                                    {% if category.products_count > 0 %}disabled{% endif %}>
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
<script>
// Initialize sortable
document.addEventListener('DOMContentLoaded', function() {
    const el = document.getElementById('sortable-categories');
    if (el) {
        new Sortable(el, {
            handle: '.handle',
            animation: 150,
            onEnd: function(evt) {
                // Get new order
                const order = [];
                el.querySelectorAll('tr').forEach(row => {
                    order.push(row.dataset.id);
                });
                
                // Send to server
                fetch('{{ base_url }}/products/categories/reorder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({ order: order })
                })
                .then(response => response.json())
                .then(data => {
                    if (!data.success) {
                        alert(data.message || 'Error reordering categories');
                        // Reload to restore original order
                        window.location.reload();
                    }
                });
            }
        });
    }
});

// Delete category
function deleteCategory(id) {
    if (!confirm('{{ __("categories.delete_confirm") | default("Are you sure you want to delete this category?") }}')) {
        return;
    }
    
    fetch(`{{ base_url }}/products/categories/${id}`, {
        method: 'DELETE',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-HTTP-Method-Override': 'DELETE'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert(data.message || 'Error deleting category');
        }
    });
}
</script>
{% endblock %}