<?php

namespace App\Controllers;

use Flight;
use App\Models\Voucher;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Config;
use Exception;

class VoucherController extends \App\Core\Controller
{
    private $voucher;
    private $client;
    private $invoice;
    
    public function __construct()
    {
        $this->voucher = new Voucher();
        $this->client = new Client();
        $this->invoice = new Invoice();
    }
    
    /**
     * List vouchers
     */
    public function index()
    {
        $filters = [
            'status' => Flight::request()->query->status,
            'client_id' => Flight::request()->query->client_id,
            'date_from' => Flight::request()->query->date_from,
            'date_to' => Flight::request()->query->date_to,
            'search' => Flight::request()->query->search
        ];
        
        $page = Flight::request()->query->page ?: 1;
        $limit = Flight::request()->query->limit ?: 20;
        
        $vouchers = $this->getVouchers($filters, $page, $limit);
        $statistics = $this->voucher->getStatistics($filters);
        $clients = $this->client->getAll(['is_active' => true]);
        
        // Get column configuration for vouchers table
        $db = Flight::db();
        $stmt = $db->prepare("SELECT value FROM config WHERE `key` = :key AND category = 'table_columns_enhanced' LIMIT 1");
        $stmt->execute(['key' => 'vouchers_columns']);
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        $columnConfig = [];
        if ($result && $result['value']) {
            $columnConfig = json_decode($result['value'], true) ?: [];
        }
        
        // Process column configuration to determine visibility
        $visibleColumns = [
            'checkbox' => true, // Always visible
            'voucher_number' => true,
            'beneficiary' => true,
            'type' => true,
            'amount' => true,
            'remaining' => true,
            'status' => true,
            'expiry_date' => true,
            'created_by' => true,
            'actions' => true // Always visible
        ];
        
        // Apply visibility settings from config
        foreach ($columnConfig as $config) {
            if (isset($config['column']) && isset($config['visible'])) {
                // Convert string '0'/'1' to boolean
                $visibleColumns[$config['column']] = ($config['visible'] == 1 || $config['visible'] === true);
            }
        }
        
        $this->render('vouchers/index', [
            'vouchers' => $vouchers['data'],
            'pagination' => $vouchers['pagination'],
            'statistics' => $statistics,
            'filters' => $filters,
            'clients' => $clients,
            'visibleColumns' => $visibleColumns,
            'statuses' => [
                Voucher::STATUS_ACTIVE => __('vouchers.status.active'),
                Voucher::STATUS_PARTIALLY_USED => __('vouchers.status.partially_used'),
                Voucher::STATUS_FULLY_USED => __('vouchers.status.fully_used'),
                Voucher::STATUS_EXPIRED => __('vouchers.status.expired'),
                Voucher::STATUS_CANCELLED => __('vouchers.status.cancelled')
            ]
        ]);
    }
    
    /**
     * Show create form
     */
    public function create()
    {
        $clientId = Flight::request()->query->client_id;
        $client = $clientId ? $this->client->getById($clientId) : null;
        
        $clients = $this->client->getAll(['is_active' => true]);
        
        $this->render('vouchers/create', [
            'client' => $client,
            'clients' => $clients,
            'voucherTypes' => [
                Voucher::TYPE_AMOUNT => __('vouchers.type.amount'),
                Voucher::TYPE_SERVICE => __('vouchers.type.service'),
                Voucher::TYPE_PERCENTAGE => __('vouchers.type.percentage')
            ],
            'defaultExpiry' => date('Y-m-d', strtotime('+1 year'))
        ]);
    }
    
    /**
     * Store new voucher
     */
    public function store()
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Validate required fields
            $this->validateVoucherData($data);
            
            // Create voucher
            $voucher = $this->voucher->createVoucher($data);
            
            // Create invoice line if part of a sale
            if (!empty($data['create_invoice'])) {
                $this->createVoucherInvoice($voucher, $data);
            }
            
            Flight::flash('success', __('vouchers.created_successfully', ['number' => $voucher['voucher_number']]));
            Flight::redirect($this->url('/vouchers/' . $voucher['id']));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/vouchers/create'));
        }
    }
    
    /**
     * Show voucher details
     */
    public function show($id)
    {
        $voucher = $this->voucher->getById($id);
        
        if (!$voucher) {
            Flight::notFound();
            return;
        }
        
        // Get usage history
        $usage = $this->voucher->getUsageHistory($id);
        
        // Get client details
        $client = $this->client->getById($voucher['client_id']);
        
        $this->render('vouchers/show', [
            'voucher' => $voucher,
            'client' => $client,
            'usage' => $usage,
            'canCancel' => !in_array($voucher['status'], [Voucher::STATUS_FULLY_USED, Voucher::STATUS_CANCELLED])
        ]);
    }
    
    /**
     * Use voucher (AJAX)
     */
    public function use()
    {
        try {
            $voucherNumber = Flight::request()->data->voucher_number;
            $amount = Flight::request()->data->amount;
            $invoiceId = Flight::request()->data->invoice_id;
            
            if (!$voucherNumber || !$amount || !$invoiceId) {
                throw new Exception(__('vouchers.missing_data'));
            }
            
            $result = $this->voucher->useVoucher($voucherNumber, $amount, $invoiceId);
            
            Flight::json([
                'success' => true,
                'message' => __('vouchers.used_successfully'),
                'amount_used' => $result['amount_used'],
                'remaining_balance' => $result['remaining_balance']
            ]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Check voucher (AJAX)
     */
    public function check()
    {
        try {
            $voucherNumber = Flight::request()->query->number;
            
            if (!$voucherNumber) {
                throw new Exception(__('vouchers.number_required'));
            }
            
            $voucher = $this->voucher->getByVoucherNumber($voucherNumber);
            
            if (!$voucher) {
                throw new Exception(__('vouchers.not_found'));
            }
            
            // Validate voucher
            $this->voucher->validateVoucher($voucher);
            
            Flight::json([
                'success' => true,
                'voucher' => [
                    'id' => $voucher['id'],
                    'number' => $voucher['voucher_number'],
                    'type' => $voucher['voucher_type'],
                    'amount' => $voucher['amount'],
                    'remaining' => $voucher['remaining_amount'],
                    'expiry_date' => $voucher['expiry_date'],
                    'beneficiary' => $voucher['beneficiary_name']
                ]
            ]);
            
        } catch (Exception $e) {
            Flight::json(['success' => false, 'message' => $e->getMessage()], 400);
        }
    }
    
    /**
     * Cancel voucher
     */
    public function cancel($id)
    {
        try {
            $reason = Flight::request()->data->reason;
            
            $this->voucher->cancelVoucher($id, $reason);
            
            Flight::flash('success', __('vouchers.cancelled_successfully'));
            Flight::redirect($this->url('/vouchers/' . $id));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/vouchers/' . $id));
        }
    }
    
    /**
     * Print voucher
     */
    public function print($id)
    {
        $voucher = $this->voucher->getById($id);
        
        if (!$voucher) {
            Flight::notFound();
            return;
        }
        
        $client = $this->client->getById($voucher['client_id']);
        
        $this->render('vouchers/print', [
            'voucher' => $voucher,
            'client' => $client,
            'layout' => false // No layout for print
        ]);
    }
    
    /**
     * Email voucher
     */
    public function email($id)
    {
        try {
            $voucher = $this->voucher->getById($id);
            
            if (!$voucher) {
                Flight::notFound();
                return;
            }
            
            $recipient = Flight::request()->data->recipient_email ?: $voucher['beneficiary_email'];
            
            if (!$recipient) {
                throw new Exception(__('vouchers.recipient_email_required'));
            }
            
            // Send email using email service
            $emailService = new \App\Services\EmailService();
            $result = $emailService->sendVoucherEmail($id, $recipient);
            
            if ($result['success']) {
                Flight::flash('success', __('vouchers.email_sent_successfully'));
            } else {
                Flight::flash('error', $result['message']);
            }
            
            Flight::redirect($this->url('/vouchers/' . $id));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/vouchers/' . $id));
        }
    }
    
    /**
     * Expiring vouchers report
     */
    public function expiring()
    {
        $days = Flight::request()->query->days ?: 30;
        
        // Update expired vouchers first
        $this->voucher->updateExpiredVouchers();
        
        // Get expiring vouchers
        $vouchers = $this->voucher->getExpiringVouchers($days);
        
        $this->render('vouchers/expiring', [
            'vouchers' => $vouchers,
            'days' => $days
        ]);
    }
    
    /**
     * Send expiry reminders
     */
    public function sendExpiryReminders()
    {
        try {
            $days = Flight::request()->data->days ?: 30;
            $voucherIds = Flight::request()->data->voucher_ids ?? [];
            
            if (empty($voucherIds)) {
                throw new Exception(__('vouchers.no_vouchers_selected'));
            }
            
            $sent = 0;
            $errors = [];
            
            $emailService = new \App\Services\EmailService();
            
            foreach ($voucherIds as $voucherId) {
                try {
                    $voucher = $this->voucher->getById($voucherId);
                    $recipient = $voucher['beneficiary_email'] ?: $this->client->getById($voucher['client_id'])['email'];
                    
                    if ($recipient) {
                        $result = $emailService->sendVoucherExpiryReminder($voucherId, $recipient);
                        if ($result['success']) {
                            $sent++;
                        } else {
                            $errors[] = $voucher['voucher_number'] . ': ' . $result['message'];
                        }
                    }
                } catch (Exception $e) {
                    $errors[] = 'Voucher #' . $voucherId . ': ' . $e->getMessage();
                }
            }
            
            if ($sent > 0) {
                Flight::flash('success', __('vouchers.reminders_sent', ['count' => $sent]));
            }
            
            if (!empty($errors)) {
                Flight::flash('error', implode('<br>', $errors));
            }
            
            Flight::redirect($this->url('/vouchers/expiring?days=' . $days));
            
        } catch (Exception $e) {
            Flight::flash('error', $e->getMessage());
            Flight::redirect($this->url('/vouchers/expiring'));
        }
    }
    
    /**
     * Voucher statistics
     */
    public function statistics()
    {
        $year = Flight::request()->query->year ?: date('Y');
        
        // Get monthly statistics
        $monthlyStats = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthlyStats[$month] = $this->voucher->getStatistics([
                'date_from' => "$year-$month-01",
                'date_to' => date('Y-m-t', strtotime("$year-$month-01"))
            ]);
        }
        
        // Get overall statistics
        $overallStats = $this->voucher->getStatistics([
            'date_from' => "$year-01-01",
            'date_to' => "$year-12-31"
        ]);
        
        $this->render('vouchers/statistics', [
            'year' => $year,
            'monthlyStats' => $monthlyStats,
            'overallStats' => $overallStats,
            'years' => range(date('Y') - 2, date('Y'))
        ]);
    }
    
    /**
     * Private helper methods
     */
    
    private function getVouchers($filters, $page, $limit)
    {
        $db = Flight::db();
        
        $where = ['1=1'];
        $params = [];
        
        if (!empty($filters['status'])) {
            $where[] = 'v.status = :status';
            $params[':status'] = $filters['status'];
        }
        
        if (!empty($filters['client_id'])) {
            $where[] = 'v.client_id = :client_id';
            $params[':client_id'] = $filters['client_id'];
        }
        
        if (!empty($filters['date_from'])) {
            $where[] = 'v.issue_date >= :date_from';
            $params[':date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $where[] = 'v.issue_date <= :date_to';
            $params[':date_to'] = $filters['date_to'];
        }
        
        if (!empty($filters['search'])) {
            $where[] = '(v.voucher_number LIKE :search OR v.beneficiary_name LIKE :search)';
            $params[':search'] = '%' . $filters['search'] . '%';
        }
        
        $whereClause = implode(' AND ', $where);
        
        // Count total
        $stmt = $db->prepare("
            SELECT COUNT(*) as total
            FROM vouchers v
            WHERE $whereClause
        ");
        $stmt->execute($params);
        $total = $stmt->fetch()['total'];
        
        // Get data
        $offset = ($page - 1) * $limit;
        $stmt = $db->prepare("
            SELECT v.*, c.name as client_name
            FROM vouchers v
            LEFT JOIN clients c ON v.client_id = c.id
            WHERE $whereClause
            ORDER BY v.created_at DESC
            LIMIT :limit OFFSET :offset
        ");
        
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $limit, \PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, \PDO::PARAM_INT);
        
        $stmt->execute();
        $data = $stmt->fetchAll();
        
        return [
            'data' => $data,
            'pagination' => [
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit)
            ]
        ];
    }
    
    private function validateVoucherData($data)
    {
        $required = ['voucher_type', 'client_id', 'amount'];
        
        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new Exception(__('validation.required', ['field' => $field]));
            }
        }
        
        if ($data['amount'] <= 0) {
            throw new Exception(__('vouchers.amount_must_be_positive'));
        }
        
        if (!empty($data['expiry_date']) && strtotime($data['expiry_date']) < strtotime('today')) {
            throw new Exception(__('vouchers.expiry_date_past'));
        }
    }
    
    private function createVoucherInvoice($voucher, $data)
    {
        // Create invoice for voucher sale
        $invoiceData = [
            'client_id' => $voucher['client_id'],
            'invoice_type' => 'service',
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d'),
            'lines' => [
                [
                    'line_type' => 'voucher',
                    'description' => __('vouchers.sale_description', ['number' => $voucher['voucher_number']]),
                    'quantity' => 1,
                    'unit_price' => $voucher['amount'],
                    'vat_rate' => $data['vat_rate'] ?? 17
                ]
            ]
        ];
        
        $invoice = $this->invoice->createInvoice($invoiceData);
        
        // Update voucher with invoice reference
        $db = Flight::db();
        $stmt = $db->prepare("
            UPDATE vouchers 
            SET purchase_invoice_id = :invoice_id 
            WHERE id = :id
        ");
        $stmt->execute([
            ':invoice_id' => $invoice['id'],
            ':id' => $voucher['id']
        ]);
        
        return $invoice;
    }
}