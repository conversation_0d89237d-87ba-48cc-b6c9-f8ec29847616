<?php
/**
 * <PERSON><PERSON>t to fix user ID 18 data that was incorrectly inserted
 * Access via: http://localhost/fit/public/fix_user_18.php
 */

require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables BEFORE bootstrap
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

// HTML header
?>
<!DOCTYPE html>
<html>
<head>
    <title>Fix User 18</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        pre { background: #f4f4f4; padding: 10px; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>Fix User ID 18 Data</h1>
    <pre>
<?php

try {
    $db = Flight::db();
    
    // First, let's see what data we have for user 18
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 18");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo "<span class='error'>User ID 18 not found.</span>\n";
        exit;
    }
    
    echo "Current data for user ID 18:\n";
    echo "username: " . htmlspecialchars($user['username']) . "\n";
    echo "email: " . htmlspecialchars($user['email']) . "\n";
    echo "first_name: " . htmlspecialchars($user['first_name']) . "\n";
    echo "last_name: " . htmlspecialchars($user['last_name']) . "\n";
    echo "address: " . htmlspecialchars($user['address']) . "\n";
    echo "postal_code: " . htmlspecialchars($user['postal_code']) . "\n";
    echo "city: " . htmlspecialchars($user['city']) . "\n";
    echo "country: " . htmlspecialchars($user['country']) . "\n";
    echo "\n";
    
    // Fix the data
    echo "<span class='success'>Fixing user data...</span>\n";
    
    // Correct mapping based on what we see:
    $correctData = [
        'username' => 'remi',  // Generate proper username
        'email' => '<EMAIL>',  // From last_name field
        'first_name' => 'Rémi',  // From email field
        'last_name' => 'Heine',  // Correct last name
        'address' => '15, am Pëtz',  // Default address
        'postal_code' => 'L-9579',  // Default postal code
        'city' => 'Weidingen',  // Default city
        'country' => 'LU',  // Default country
        'language' => 'fr',  // French
        'timezone' => 'Europe/Luxembourg',  // Luxembourg timezone
        'is_active' => 1  // Active
    ];
    
    // Update the user
    $updateFields = [];
    $updateValues = [];
    foreach ($correctData as $field => $value) {
        $updateFields[] = "$field = ?";
        $updateValues[] = $value;
    }
    $updateValues[] = 18; // Add ID for WHERE clause
    
    $updateQuery = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    
    $stmt = $db->prepare($updateQuery);
    $stmt->execute($updateValues);
    
    echo "\n<span class='success'>User ID 18 has been fixed!</span>\n\n";
    
    // Verify the fix
    $stmt = $db->prepare("SELECT * FROM users WHERE id = 18");
    $stmt->execute();
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Updated data for user ID 18:\n";
    echo "username: " . htmlspecialchars($user['username']) . "\n";
    echo "email: " . htmlspecialchars($user['email']) . "\n";
    echo "first_name: " . htmlspecialchars($user['first_name']) . "\n";
    echo "last_name: " . htmlspecialchars($user['last_name']) . "\n";
    echo "address: " . htmlspecialchars($user['address']) . "\n";
    echo "postal_code: " . htmlspecialchars($user['postal_code']) . "\n";
    echo "city: " . htmlspecialchars($user['city']) . "\n";
    echo "country: " . htmlspecialchars($user['country']) . "\n";
    
    echo "\n<span class='success'>✓ Fix completed successfully!</span>\n";
    echo "\n<a href='/fit/public/users/18/edit'>Go to Edit User</a>";
    
} catch (Exception $e) {
    echo "<span class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</span>\n";
}
?>
    </pre>
</body>
</html>