<!DOCTYPE html>
<html>
<head>
    <title>Force Refresh</title>
</head>
<body>
    <h1>Force Browser Cache Clear</h1>
    <p>Follow these steps to see your changes:</p>
    
    <h2>Method 1: Hard Refresh (Recommended)</h2>
    <ul>
        <li><strong>Windows/Linux:</strong> Press <code>Ctrl + Shift + R</code> or <code>Ctrl + F5</code></li>
        <li><strong>Mac:</strong> Press <code>Cmd + Shift + R</code></li>
    </ul>
    
    <h2>Method 2: Open Developer Tools First</h2>
    <ol>
        <li>Open Developer Tools (F12)</li>
        <li>Right-click the refresh button</li>
        <li>Select "Empty Cache and Hard Reload"</li>
    </ol>
    
    <h2>Method 3: Disable Cache in DevTools</h2>
    <ol>
        <li>Open Developer Tools (F12)</li>
        <li>Go to Network tab</li>
        <li>Check "Disable cache"</li>
        <li>Keep DevTools open while testing</li>
    </ol>
    
    <h2>Method 4: Add Version Parameter</h2>
    <p>Try accessing the page with a version parameter:</p>
    <p><a href="/fit/public/invoices/create?v=<?php echo time(); ?>">Open Invoice Create with Fresh Version</a></p>
    
    <script>
        // Also clear any service workers
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for(let registration of registrations) {
                    registration.unregister();
                }
            });
        }
    </script>
</body>
</html>