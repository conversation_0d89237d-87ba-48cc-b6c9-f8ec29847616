{% extends "base-modern.twig" %}

{% block title %}{{ __('admin.error_logs') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">{{ __('admin.error_logs') }}</h1>
        <div>
            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cleanupModal">
                <i class="bi bi-trash me-1"></i>{{ __('admin.cleanup_old_logs') }}
            </button>
            <div class="btn-group ms-2">
                <button type="button" class="btn btn-sm btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                    <i class="bi bi-download me-1"></i>{{ __('common.export') }}
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="/admin/errors/export?format=csv{{ filters|url_encode }}">CSV</a></li>
                    <li><a class="dropdown-item" href="/admin/errors/export?format=json{{ filters|url_encode }}">JSON</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" action="/admin/errors" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">{{ __('common.search') }}</label>
                    <input type="text" name="search" class="form-control" value="{{ filters.search }}" 
                           placeholder="{{ __('admin.search_error_logs') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('admin.environment') }}</label>
                    <select name="environment" class="form-select">
                        <option value="">{{ __('common.all') }}</option>
                        {% for env in environments %}
                            <option value="{{ env }}" {% if filters.environment == env %}selected{% endif %}>
                                {{ env|capitalize }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('admin.status') }}</label>
                    <select name="resolved" class="form-select">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="0" {% if filters.resolved == '0' %}selected{% endif %}>{{ __('admin.unresolved') }}</option>
                        <option value="1" {% if filters.resolved == '1' %}selected{% endif %}>{{ __('admin.resolved') }}</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('common.date_from') }}</label>
                    <input type="date" name="date_from" class="form-control" value="{{ filters.date_from }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">{{ __('common.date_to') }}</label>
                    <input type="date" name="date_to" class="form-control" value="{{ filters.date_to }}">
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Error Logs Table -->
    <div class="card">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th width="120">{{ __('admin.error_id') }}</th>
                        <th width="160">{{ __('common.timestamp') }}</th>
                        <th width="100">{{ __('admin.environment') }}</th>
                        <th>{{ __('admin.error_message') }}</th>
                        <th width="200">{{ __('admin.location') }}</th>
                        <th width="120">{{ __('common.user') }}</th>
                        <th width="100">{{ __('admin.performance') }}</th>
                        <th width="80">{{ __('admin.status') }}</th>
                        <th width="80">{{ __('common.actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for error in errors %}
                    <tr class="{% if not error.resolved %}table-danger{% endif %}">
                        <td>
                            <code class="small">{{ error.error_id|slice(0, 13) }}...</code>
                        </td>
                        <td class="small">
                            {{ error.timestamp|date('Y-m-d H:i:s') }}
                        </td>
                        <td>
                            <span class="badge bg-{% if error.environment == 'production' %}danger{% elseif error.environment == 'development' %}warning{% else %}info{% endif %}">
                                {{ error.environment }}
                            </span>
                        </td>
                        <td>
                            <div class="text-truncate" style="max-width: 400px;" title="{{ error.message }}">
                                <strong>{{ error.class|split('\\')|last }}</strong>: {{ error.message }}
                            </div>
                            <small class="text-muted">{{ error.request_uri }}</small>
                        </td>
                        <td class="small">
                            <div class="text-truncate" title="{{ error.file }}">
                                ...{{ error.file|split('/')|slice(-2)|join('/') }}:{{ error.line }}
                            </div>
                        </td>
                        <td>
                            {% if error.user_name %}
                                {{ error.user_name }}
                            {% else %}
                                <span class="text-muted">Guest</span>
                            {% endif %}
                        </td>
                        <td class="small">
                            <span title="{{ __('admin.execution_time') }}">{{ error.execution_time }}s</span><br>
                            <span title="{{ __('admin.memory_peak') }}">{{ error.memory_peak }}MB</span>
                        </td>
                        <td>
                            {% if error.resolved %}
                                <span class="badge bg-success">{{ __('admin.resolved') }}</span>
                            {% else %}
                                <span class="badge bg-danger">{{ __('admin.unresolved') }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <a href="/admin/errors/{{ error.id }}" class="btn btn-sm btn-outline-primary" title="{{ __('common.view') }}">
                                <i class="bi bi-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="9" class="text-center py-4 text-muted">
                            {{ __('admin.no_error_logs_found') }}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if pagination.total_pages > 1 %}
        <div class="card-footer">
            {% include '_partials/pagination.twig' with {
                'current_page': pagination.current_page,
                'total_pages': pagination.total_pages,
                'base_url': '/admin/errors'
            } %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Cleanup Modal -->
<div class="modal fade" id="cleanupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="/admin/errors/cleanup">
                {{ csrf_field() }}
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('admin.cleanup_old_logs') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>{{ __('admin.cleanup_logs_description') }}</p>
                    <div class="mb-3">
                        <label class="form-label">{{ __('admin.delete_logs_older_than') }}</label>
                        <div class="input-group">
                            <input type="number" name="days" class="form-control" value="30" min="1" required>
                            <span class="input-group-text">{{ __('common.days') }}</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>{{ __('common.delete') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}