<?php

namespace Tests\Phase3;

use PDO;
use Exception;

class Task_3_4_1_InvoiceTemplateTest
{
    private $db;
    private $passed = 0;
    private $failed = 0;
    
    public function setUp(): void
    {
        try {
            // Database connection
            $this->db = new PDO('mysql:host=localhost;dbname=fitapp', 'root', 'test1234');
            $this->db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch (Exception $e) {
            die("Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    private function assertEquals($expected, $actual, $message = '')
    {
        if ($expected == $actual) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: $expected, Got: $actual)");
        }
    }
    
    private function assertTrue($condition, $message = '')
    {
        if ($condition) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " (Expected: true, Got: false)");
        }
    }
    
    private function assertContains($needle, $haystack, $message = '')
    {
        if (in_array($needle, $haystack)) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ('$needle' not found in array)");
        }
    }
    
    private function assertGreaterThan($expected, $actual, $message = '')
    {
        if ($actual > $expected) {
            $this->passed++;
            return true;
        } else {
            $this->failed++;
            throw new Exception($message . " ($actual not greater than $expected)");
        }
    }
    
    /**
     * Test 1: Check template tables exist
     */
    public function testTemplateTablesExist()
    {
        $requiredTables = [
            'invoice_templates',
            'invoice_template_settings',
            'template_vat_configs',
            'template_line_items'
        ];
        
        foreach ($requiredTables as $table) {
            $stmt = $this->db->query("SHOW TABLES LIKE '$table'");
            $this->assertEquals(1, $stmt->rowCount(), "Table '$table' should exist");
        }
        
        echo "✓ All invoice template tables exist\n";
    }
    
    /**
     * Test 2: Check invoice_templates structure with inheritance
     */
    public function testInvoiceTemplatesStructure()
    {
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_templates");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = [
            'id', 'name', 'code', 'invoice_type', 'owner_type',
            'owner_id', 'parent_template_id', 'is_active', 'created_by'
        ];
        
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in invoice_templates");
        }
        
        // Check invoice_type enum
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_templates WHERE Field = 'invoice_type'");
        $typeColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertTrue(strpos($typeColumn['Type'], 'retrocession_30') !== false, "Should support retrocession_30");
        $this->assertTrue(strpos($typeColumn['Type'], 'retrocession_25') !== false, "Should support retrocession_25");
        
        // Check owner_type enum
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_templates WHERE Field = 'owner_type'");
        $ownerColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertTrue(strpos($ownerColumn['Type'], 'system') !== false, "Should support system ownership");
        $this->assertTrue(strpos($ownerColumn['Type'], 'group') !== false, "Should support group ownership");
        $this->assertTrue(strpos($ownerColumn['Type'], 'user') !== false, "Should support user ownership");
        
        echo "✓ invoice_templates structure with inheritance support is correct\n";
    }
    
    /**
     * Test 3: Check template settings with override capability
     */
    public function testTemplateSettingsStructure()
    {
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_template_settings");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        $requiredColumns = [
            'template_id', 'setting_key', 'setting_value',
            'use_parent_value', 'setting_type'
        ];
        
        foreach ($requiredColumns as $column) {
            $this->assertContains($column, $columns, "Column '$column' should exist in invoice_template_settings");
        }
        
        // Check setting_type enum
        $stmt = $this->db->query("SHOW COLUMNS FROM invoice_template_settings WHERE Field = 'setting_type'");
        $typeColumn = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->assertTrue(strpos($typeColumn['Type'], 'financial') !== false, "Should support financial settings");
        $this->assertTrue(strpos($typeColumn['Type'], 'content') !== false, "Should support content settings");
        
        // Check unique constraint
        $stmt = $this->db->query("SHOW INDEX FROM invoice_template_settings WHERE Key_name = 'template_setting'");
        $hasUniqueKey = $stmt->rowCount() > 0;
        echo "✓ Template settings with override capability " . ($hasUniqueKey ? "(with unique key) " : "") . "is correct\n";
    }
    
    /**
     * Test 4: Check system templates exist
     */
    public function testSystemTemplates()
    {
        $stmt = $this->db->query("SELECT * FROM invoice_templates WHERE owner_type = 'system'");
        $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $this->assertGreaterThan(0, count($templates), "Should have system templates");
        
        $foundTypes = [];
        echo "✓ Found system templates:\n";
        foreach ($templates as $template) {
            $foundTypes[] = $template['invoice_type'];
            echo "  - {$template['name']} ({$template['code']}) - Type: {$template['invoice_type']}\n";
        }
        
        // Check for essential template types
        $this->assertContains('retrocession_30', $foundTypes, "Should have 30% retrocession template");
        $this->assertContains('retrocession_25', $foundTypes, "Should have 25% retrocession template");
    }
    
    /**
     * Test 5: Test template inheritance
     */
    public function testTemplateInheritance()
    {
        // Get a system template
        $stmt = $this->db->query("SELECT id, code FROM invoice_templates WHERE owner_type = 'system' LIMIT 1");
        $systemTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($systemTemplate) {
            // Create a user template inheriting from system
            $sql = "INSERT INTO invoice_templates 
                    (name, code, invoice_type, owner_type, owner_id, parent_template_id, is_active, created_by) 
                    VALUES 
                    (:name, :code, :invoice_type, :owner_type, :owner_id, :parent_template_id, :is_active, :created_by)";
            
            $stmt = $this->db->prepare($sql);
            $result = $stmt->execute([
                ':name' => 'Test User Template',
                ':code' => 'TEST_USER_' . time(),
                ':invoice_type' => 'rental',
                ':owner_type' => 'user',
                ':owner_id' => 1,
                ':parent_template_id' => $systemTemplate['id'],
                ':is_active' => 1,
                ':created_by' => 1
            ]);
            
            $this->assertTrue($result, "User template with inheritance should be created");
            $userTemplateId = $this->db->lastInsertId();
            
            // Add settings to test inheritance
            $sql = "INSERT INTO invoice_template_settings 
                    (template_id, setting_key, setting_value, use_parent_value, setting_type) 
                    VALUES 
                    (:template_id, :setting_key, :setting_value, :use_parent_value, :setting_type)
                    ON DUPLICATE KEY UPDATE setting_value = :setting_value";
            
            $stmt = $this->db->prepare($sql);
            
            // Add a setting that uses parent value
            $stmt->execute([
                ':template_id' => $userTemplateId,
                ':setting_key' => 'payment_terms',
                ':setting_value' => null,
                ':use_parent_value' => 1,
                ':setting_type' => 'financial'
            ]);
            
            // Add a setting that overrides parent
            $stmt->execute([
                ':template_id' => $userTemplateId,
                ':setting_key' => 'footer_text',
                ':setting_value' => 'Custom footer',
                ':use_parent_value' => 0,
                ':setting_type' => 'content'
            ]);
            
            echo "✓ Template inheritance with parent-child relationships works correctly\n";
            
            // Clean up
            $this->db->exec("DELETE FROM invoice_templates WHERE id = $userTemplateId");
        } else {
            echo "✓ Template inheritance structure verified\n";
        }
    }
    
    /**
     * Test 6: Test template VAT configurations
     */
    public function testTemplateVATConfigs()
    {
        // Get a template
        $stmt = $this->db->query("SELECT id FROM invoice_templates LIMIT 1");
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template) {
            // Get VAT rate
            $stmt = $this->db->query("SELECT id FROM config_vat_rates WHERE is_active = 1 LIMIT 1");
            $vatRate = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($vatRate) {
                // Add VAT config
                $sql = "INSERT INTO template_vat_configs 
                        (template_id, vat_rate_id, is_default, service_type) 
                        VALUES 
                        (:template_id, :vat_rate_id, :is_default, :service_type)
                        ON DUPLICATE KEY UPDATE is_default = :is_default";
                
                $stmt = $this->db->prepare($sql);
                $result = $stmt->execute([
                    ':template_id' => $template['id'],
                    ':vat_rate_id' => $vatRate['id'],
                    ':is_default' => 1,
                    ':service_type' => 'standard'
                ]);
                
                $this->assertTrue($result, "Template VAT configuration should be created");
                
                echo "✓ Template VAT configurations work correctly\n";
            }
        } else {
            echo "✓ Template VAT configuration structure verified\n";
        }
    }
    
    /**
     * Test 7: Test template settings retrieval
     */
    public function testTemplateSettingsRetrieval()
    {
        // Get a template with settings
        $stmt = $this->db->query("
            SELECT t.*, COUNT(s.id) as settings_count 
            FROM invoice_templates t
            LEFT JOIN invoice_template_settings s ON t.id = s.template_id
            GROUP BY t.id
            HAVING settings_count > 0
            LIMIT 1
        ");
        $template = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($template) {
            // Get settings
            $stmt = $this->db->prepare("
                SELECT * FROM invoice_template_settings 
                WHERE template_id = ?
            ");
            $stmt->execute([$template['id']]);
            $settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $this->assertGreaterThan(0, count($settings), "Template should have settings");
            
            echo "✓ Template settings retrieval works correctly\n";
            echo "  Found " . count($settings) . " settings for template '{$template['name']}'\n";
        } else {
            echo "✓ Template settings structure verified\n";
        }
    }
    
    /**
     * Test 8: Test template types coverage
     */
    public function testTemplateTypesCoverage()
    {
        $expectedTypes = ['rental', 'hourly', 'retrocession_30', 'retrocession_25'];
        $stmt = $this->db->query("SELECT DISTINCT invoice_type FROM invoice_templates WHERE owner_type = 'system'");
        $actualTypes = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($expectedTypes as $type) {
            $this->assertContains($type, $actualTypes, "System should have '$type' template");
        }
        
        echo "✓ All expected template types are covered\n";
    }
    
    /**
     * Run all tests and provide summary
     */
    public function runAllTests()
    {
        echo "\n=== Task 3.4.1: Invoice Template System Tests ===\n\n";
        
        $tests = [
            'testTemplateTablesExist' => 'Checking template tables existence',
            'testInvoiceTemplatesStructure' => 'Checking invoice_templates structure',
            'testTemplateSettingsStructure' => 'Checking template settings structure',
            'testSystemTemplates' => 'Checking system templates',
            'testTemplateInheritance' => 'Testing template inheritance',
            'testTemplateVATConfigs' => 'Testing template VAT configurations',
            'testTemplateSettingsRetrieval' => 'Testing template settings retrieval',
            'testTemplateTypesCoverage' => 'Testing template types coverage'
        ];
        
        foreach ($tests as $method => $description) {
            echo "\n$description...\n";
            try {
                $this->$method();
            } catch (Exception $e) {
                echo "❌ FAILED: " . $e->getMessage() . "\n";
                continue;
            }
        }
        
        echo "\n" . str_repeat('=', 50) . "\n";
        echo "Test Results: {$this->passed} passed, {$this->failed} failed\n";
        
        if ($this->failed === 0) {
            echo "\n✅ ALL TESTS PASSED for Task 3.4.1\n";
            echo "\nKey features verified:\n";
            echo "- Invoice templates with inheritance support\n";
            echo "- Template settings with override capability\n";
            echo "- System/group/user ownership levels\n";
            echo "- Template VAT configurations\n";
            echo "- All invoice types covered (rental, hourly, retrocession 30%, 25%)\n";
            echo "- Parent-child template relationships\n";
        } else {
            echo "\n❌ SOME TESTS FAILED\n";
            echo "Please check the error messages above\n";
        }
        
        return $this->failed === 0;
    }
}

// Run tests if executed directly
if (php_sapi_name() === 'cli' && basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    $test = new Task_3_4_1_InvoiceTemplateTest();
    $test->setUp();
    $test->runAllTests();
}