<!DOCTYPE html>
<html>
<head>
    <title>Test Column Renaming Feature</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test Column Renaming Feature</h2>
        
        <div class="alert alert-info">
            <h5>How to test:</h5>
            <ol>
                <li>Go to <a href="/fit/public/config/table-columns" target="_blank">Column Configuration</a></li>
                <li>Select "Table des Articles de Facture" (Invoice Items)</li>
                <li>Select any document type (e.g., "Facture")</li>
                <li>Click the pencil icon next to any column name</li>
                <li>Enter a custom name and press Enter or click the check button</li>
                <li>Save the configuration</li>
                <li>Generate an invoice PDF to see the custom column names</li>
            </ol>
        </div>
        
        <h3>Features Added:</h3>
        <ul class="list-group">
            <li class="list-group-item">
                <i class="bi bi-check-circle text-success me-2"></i>
                <strong>Inline Editing:</strong> Click the pencil icon to edit column names
            </li>
            <li class="list-group-item">
                <i class="bi bi-check-circle text-success me-2"></i>
                <strong>Visual Feedback:</strong> Custom names show with an arrow icon indicating the original name
            </li>
            <li class="list-group-item">
                <i class="bi bi-check-circle text-success me-2"></i>
                <strong>Keyboard Support:</strong> Press Enter to save, Escape to cancel
            </li>
            <li class="list-group-item">
                <i class="bi bi-check-circle text-success me-2"></i>
                <strong>Reset to Default:</strong> Clear the field to restore the original name
            </li>
            <li class="list-group-item">
                <i class="bi bi-check-circle text-success me-2"></i>
                <strong>PDF Support:</strong> Custom names appear in generated PDFs
            </li>
            <li class="list-group-item">
                <i class="bi bi-check-circle text-success me-2"></i>
                <strong>Per Document Type:</strong> Different names for different document types
            </li>
        </ul>
        
        <h3 class="mt-4">Test Scenarios:</h3>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Original Column</th>
                        <th>Test Custom Name</th>
                        <th>Expected Result</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Description</td>
                        <td>Article</td>
                        <td>Shows "Article" with arrow icon</td>
                    </tr>
                    <tr>
                        <td>Quantité</td>
                        <td>Qty</td>
                        <td>Shows "Qty" with arrow icon</td>
                    </tr>
                    <tr>
                        <td>Prix unitaire</td>
                        <td>Prix/Unit</td>
                        <td>Shows "Prix/Unit" with arrow icon</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="mt-4">
            <a href="/fit/public/config/table-columns" class="btn btn-primary">
                <i class="bi bi-gear me-2"></i>Go to Column Configuration
            </a>
            <a href="/fit/public/invoices" class="btn btn-secondary">
                <i class="bi bi-file-text me-2"></i>View Invoices
            </a>
        </div>
    </div>
</body>
</html>