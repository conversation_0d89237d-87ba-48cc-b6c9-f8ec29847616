<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING ALL INVOICES ===\n\n";

try {
    $db = Flight::db();
    
    // Count all invoices
    $stmt = $db->query("SELECT COUNT(*) as total FROM invoices");
    $result = $stmt->fetch(\PDO::FETCH_ASSOC);
    echo "Total invoices in database: " . $result['total'] . "\n\n";
    
    if ($result['total'] > 0) {
        // Get all invoices
        echo "All invoices:\n";
        $stmt = $db->query("
            SELECT id, invoice_number, status, created_at, total, 
                   client_id, user_id, document_type_id
            FROM invoices 
            ORDER BY id DESC
        ");
        $invoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        foreach ($invoices as $inv) {
            echo sprintf("ID: %d | Number: %s | Status: %s | Total: %.2f | Created: %s\n",
                $inv['id'],
                $inv['invoice_number'] ?? 'NULL',
                $inv['status'] ?? 'NULL',
                $inv['total'] ?? 0,
                $inv['created_at'] ?? 'NULL'
            );
        }
    } else {
        echo "No invoices found in the database.\n\n";
        
        // Check if there was an error during creation
        echo "Checking for recent errors in the database...\n";
        
        // Check if invoice_items table has any orphaned items
        $stmt = $db->query("SELECT COUNT(*) as total FROM invoice_items");
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        echo "Invoice items in database: " . $result['total'] . "\n";
        
        // Check document sequences to see if numbers were generated
        $stmt = $db->query("
            SELECT * FROM document_sequences 
            WHERE document_type_id IN (SELECT id FROM document_types WHERE code = 'invoice')
            ORDER BY updated_at DESC
            LIMIT 5
        ");
        $sequences = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        if (count($sequences) > 0) {
            echo "\nDocument sequences (invoice numbers generated):\n";
            foreach ($sequences as $seq) {
                echo sprintf("- Type: %d, Year: %s, Month: %s, Last: %d, Updated: %s\n",
                    $seq['document_type_id'],
                    $seq['year'] ?? 'NULL',
                    $seq['month'] ?? 'NULL',
                    $seq['last_number'],
                    $seq['updated_at']
                );
            }
            echo "\nThis suggests invoice numbers were generated but invoices weren't saved.\n";
        }
    }
    
    // Check if there's a problem with the table structure
    echo "\nChecking invoices table structure:\n";
    $stmt = $db->query("SHOW CREATE TABLE invoices");
    $result = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    // Check if all required columns exist
    $requiredColumns = ['id', 'invoice_number', 'status', 'client_id', 'user_id', 'total', 'created_at'];
    $stmt = $db->query("DESCRIBE invoices");
    $columns = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    $columnNames = array_column($columns, 'Field');
    
    echo "Table has " . count($columns) . " columns.\n";
    foreach ($requiredColumns as $required) {
        if (!in_array($required, $columnNames)) {
            echo "⚠️ Missing required column: $required\n";
        }
    }
    
    // Check for any recent database errors in logs
    echo "\nIf invoice creation is failing, check:\n";
    echo "1. Browser console for JavaScript errors\n";
    echo "2. Network tab in browser dev tools for failed AJAX requests\n";
    echo "3. PHP error logs at: " . ini_get('error_log') . "\n";
    echo "4. Application logs at: " . dirname(__DIR__) . "/storage/logs/\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}