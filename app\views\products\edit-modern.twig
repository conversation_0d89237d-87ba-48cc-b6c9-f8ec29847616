{% extends "base-modern.twig" %}

{% block title %}{{ __('products.edit') | default('Edit Product') }}{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ base_url }}/css/mobile-responsive.css">
<link rel="stylesheet" href="{{ base_url }}/css/mobile-products.css">
{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products">{{ __('products.title') | default('Products') }}</a></li>
    <li class="breadcrumb-item active">{{ __('products.edit') | default('Edit') }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-12">
        <div class="card shadow-sm">
            <form id="productForm" method="POST" action="{{ base_url }}/products/{{ product.id }}" class="product-form">
                <input type="hidden" name="_method" value="PUT">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('products.edit') | default('Edit Product') }}: {{ product.name }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="mb-3">{{ __('products.basic_info') | default('Basic Information') }}</h6>
                            
                            <div class="mb-3">
                                <label for="code" class="form-label">
                                    {{ __('products.code') | default('Product Code') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="code" name="code" value="{{ product.code }}" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    {{ __('products.name') | default('Product Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ product.name }}" required>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="category_id" class="form-label">
                                    {{ __('products.category') | default('Category') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="category_id" name="category_id" required>
                                    <option value="">{{ __('common.select') | default('Select...') }}</option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}" {% if category.id == product.category_id %}selected{% endif %}>
                                            {{ category.display_name }}
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="item_type" class="form-label">
                                    {{ __('products.type') | default('Item Type') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="item_type" name="item_type" required>
                                    <option value="">{{ __('common.select') | default('Select...') }}</option>
                                    <option value="product" {% if product.item_type == 'product' %}selected{% endif %}>
                                        {{ __('products.type_product') | default('Product') }}
                                    </option>
                                    <option value="service" {% if product.item_type == 'service' %}selected{% endif %}>
                                        {{ __('products.type_service') | default('Service') }}
                                    </option>
                                    <option value="package" {% if product.item_type == 'package' %}selected{% endif %}>
                                        {{ __('products.type_package') | default('Package') }}
                                    </option>
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    {{ __('products.description') | default('Description') }}
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3">{{ product.description }}</textarea>
                            </div>
                        </div>
                        
                        <!-- Pricing & Stock -->
                        <div class="col-md-6">
                            <h6 class="mb-3">{{ __('products.pricing_stock') | default('Pricing & Stock') }}</h6>
                            
                            <div class="mb-3">
                                <label for="unit_price" class="form-label">
                                    {{ __('products.unit_price') | default('Unit Price') }} <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="unit_price" name="unit_price" 
                                           step="0.01" min="0" value="{{ product.unit_price }}" required>
                                    <span class="input-group-text">€</span>
                                </div>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="vat_rate_id" class="form-label">
                                    {{ __('products.vat_rate') | default('VAT Rate') }} <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="vat_rate_id" name="vat_rate_id" required>
                                    <option value="">{{ __('common.select') | default('Select...') }}</option>
                                    {% for vat in vatRates %}
                                        <option value="{{ vat.id }}" {% if vat.id == product.vat_rate_id %}selected{% endif %}>
                                            {{ vat.display_name }} ({{ vat.rate }}%)
                                        </option>
                                    {% endfor %}
                                </select>
                                <div class="invalid-feedback"></div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_stockable" name="is_stockable" 
                                           value="1" {% if product.is_stockable %}checked{% endif %}>
                                    <label class="form-check-label" for="is_stockable">
                                        {{ __('products.is_stockable') | default('Track stock for this item') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div id="stockFields" style="{% if not product.is_stockable %}display: none;{% endif %}">
                                <div class="stock-fields-mobile">
                                    <div class="mb-3">
                                        <label for="current_stock" class="form-label">
                                            {{ __('products.current_stock') | default('Current Stock') }}
                                        </label>
                                        <input type="number" class="form-control" id="current_stock" name="current_stock" 
                                               min="0" value="{{ product.current_stock }}" readonly>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="low_stock_alert" class="form-label">
                                            {{ __('products.low_stock_alert') | default('Low Stock Alert') }}
                                        </label>
                                        <input type="number" class="form-control" id="low_stock_alert" name="low_stock_alert" 
                                               min="0" value="{{ product.low_stock_alert }}">
                                    </div>
                                </div>
                                <small class="text-muted">
                                    {{ __('products.stock_readonly_hint') | default('Use stock adjustment to change stock levels') }}
                                </small>
                            </div>
                            
                            <h6 class="mb-3 mt-4">{{ __('products.quick_sale') | default('Quick Sale Settings') }}</h6>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="quick_sale_button" 
                                           name="quick_sale_button" value="1" {% if product.quick_sale_button %}checked{% endif %}>
                                    <label class="form-check-label" for="quick_sale_button">
                                        {{ __('products.enable_quick_sale') | default('Show in quick sale buttons') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div id="quickSaleFields" style="{% if not product.quick_sale_button %}display: none;{% endif %}">
                                <div class="mb-3">
                                    <label for="button_color" class="form-label">
                                        {{ __('products.button_color') | default('Button Color') }}
                                    </label>
                                    <input type="color" class="form-control form-control-color" id="button_color" 
                                           name="button_color" value="{{ product.button_color | default('#007bff') }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="button_order" class="form-label">
                                        {{ __('products.button_order') | default('Button Order') }}
                                    </label>
                                    <input type="number" class="form-control" id="button_order" name="button_order" 
                                           min="0" value="{{ product.button_order }}">
                                    <small class="text-muted">
                                        {{ __('products.button_order_help') | default('Lower numbers appear first') }}
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" {% if product.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        {{ __('products.is_active') | default('Active') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-footer product-form-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> {{ __('common.save') | default('Save Changes') }}
                    </button>
                    <a href="{{ base_url }}/products/{{ product.id }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> {{ __('common.cancel') | default('Cancel') }}
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Toggle stock fields
document.getElementById('is_stockable').addEventListener('change', function() {
    document.getElementById('stockFields').style.display = this.checked ? 'block' : 'none';
});

// Toggle quick sale fields
document.getElementById('quick_sale_button').addEventListener('change', function() {
    document.getElementById('quickSaleFields').style.display = this.checked ? 'block' : 'none';
});

// Form submission
document.getElementById('productForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    // Clear previous errors
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    
    // Submit form
    // Add CSRF token if available
    const formData = new FormData(this);
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.content;
    if (csrfToken) {
        formData.append('_csrf_token', csrfToken);
    }
    
    fetch(this.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect;
        } else if (data.errors) {
            // Show validation errors
            for (let field in data.errors) {
                let input = document.getElementById(field);
                if (input) {
                    input.classList.add('is-invalid');
                    let feedback = input.nextElementSibling;
                    if (feedback && feedback.classList.contains('invalid-feedback')) {
                        feedback.textContent = data.errors[field];
                    }
                }
            }
        } else {
            alert(data.message || 'An error occurred');
        }
    })
    .catch(error => {
        alert('Error: ' + error.message);
    });
});

// Initialize mobile enhancements
document.addEventListener('DOMContentLoaded', function() {
    if (typeof MobileEnhancements !== 'undefined') {
        MobileEnhancements.init();
    }
});
</script>
<script src="{{ base_url }}/js/mobile-enhancements.js"></script>
{% endblock %}