<?php

namespace App\Core;

use ArrayAccess;
use Countable;
use IteratorAggregate;
use ArrayIterator;

class Collection implements ArrayAccess, Countable, IteratorAggregate
{
    protected $items = [];
    
    public function __construct($items = [])
    {
        $this->items = $items;
    }
    
    /**
     * Get all items
     */
    public function all()
    {
        return $this->items;
    }
    
    /**
     * Get item by key
     */
    public function get($key, $default = null)
    {
        return $this->items[$key] ?? $default;
    }
    
    /**
     * Key by a specific field
     */
    public function keyBy($field)
    {
        $keyed = [];
        foreach ($this->items as $item) {
            if (is_object($item)) {
                $keyed[$item->$field] = $item;
            } elseif (is_array($item)) {
                $keyed[$item[$field]] = $item;
            }
        }
        return new static($keyed);
    }
    
    /**
     * Count items
     */
    public function count(): int
    {
        return count($this->items);
    }
    
    /**
     * Check if collection is empty
     */
    public function isEmpty()
    {
        return empty($this->items);
    }
    
    /**
     * Get first item
     */
    public function first()
    {
        return reset($this->items) ?: null;
    }
    
    /**
     * Get last item
     */
    public function last()
    {
        return end($this->items) ?: null;
    }
    
    /**
     * Convert to array
     */
    public function toArray()
    {
        return array_map(function($item) {
            if (is_object($item) && method_exists($item, 'toArray')) {
                return $item->toArray();
            }
            return $item;
        }, $this->items);
    }
    
    /**
     * ArrayAccess implementation
     */
    public function offsetExists($offset): bool
    {
        return isset($this->items[$offset]);
    }
    
    public function offsetGet($offset): mixed
    {
        return $this->items[$offset] ?? null;
    }
    
    public function offsetSet($offset, $value): void
    {
        if (is_null($offset)) {
            $this->items[] = $value;
        } else {
            $this->items[$offset] = $value;
        }
    }
    
    public function offsetUnset($offset): void
    {
        unset($this->items[$offset]);
    }
    
    /**
     * IteratorAggregate implementation
     */
    public function getIterator(): ArrayIterator
    {
        return new ArrayIterator($this->items);
    }
    
    /**
     * Pluck a specific field from the collection
     */
    public function pluck($field, $keyField = null)
    {
        $plucked = [];
        
        foreach ($this->items as $item) {
            $value = null;
            $key = null;
            
            // Get the value
            if (is_object($item)) {
                $value = $item->$field ?? null;
                if ($keyField) {
                    $key = $item->$keyField ?? null;
                }
            } elseif (is_array($item)) {
                $value = $item[$field] ?? null;
                if ($keyField) {
                    $key = $item[$keyField] ?? null;
                }
            }
            
            // Set in result array
            if ($keyField && $key !== null) {
                $plucked[$key] = $value;
            } else {
                $plucked[] = $value;
            }
        }
        
        return new static($plucked);
    }
}