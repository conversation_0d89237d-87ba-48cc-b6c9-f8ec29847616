#!/bin/bash

# Fit360 AdminDesk - Cron Jobs Setup Script
# This script sets up the necessary cron jobs for the application

echo "Setting up Fit360 AdminDesk cron jobs..."

# Get the absolute path to the project
PROJECT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PHP_BIN=$(which php)

# Check if PHP is installed
if [ -z "$PHP_BIN" ]; then
    echo "Error: PHP not found. Please install PHP first."
    exit 1
fi

# Create cron jobs file
CRON_FILE="/tmp/fit360_cron_jobs"

# Write cron jobs
cat > "$CRON_FILE" << EOF
# Fit360 AdminDesk Cron Jobs

# Cleanup pending products - Run daily at 2:00 AM
0 2 * * * $PHP_BIN $PROJECT_PATH/app/jobs/CleanupPendingProducts.php >> $PROJECT_PATH/storage/logs/cron.log 2>&1

# Generate statistics report - Run every Monday at 3:00 AM
0 3 * * 1 $PHP_BIN $PROJECT_PATH/app/jobs/CleanupPendingProducts.php stats >> $PROJECT_PATH/storage/logs/stats.log 2>&1

# You can add more cron jobs here as needed
EOF

echo "The following cron jobs will be installed:"
echo "----------------------------------------"
cat "$CRON_FILE"
echo "----------------------------------------"

# Ask for confirmation
read -p "Do you want to install these cron jobs? (y/n) " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Install cron jobs
    crontab -l > /tmp/existing_cron 2>/dev/null || true
    cat /tmp/existing_cron "$CRON_FILE" | crontab -
    
    echo "Cron jobs installed successfully!"
    echo ""
    echo "To view installed cron jobs, run: crontab -l"
    echo "To edit cron jobs, run: crontab -e"
    echo "To remove all cron jobs, run: crontab -r"
else
    echo "Installation cancelled."
fi

# Cleanup
rm -f "$CRON_FILE"

# Create log directory if it doesn't exist
mkdir -p "$PROJECT_PATH/storage/logs"

echo ""
echo "Setup complete!"