<?php
require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get invoice 268 details
    $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = 268");
    $stmt->execute();
    $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        echo "Invoice 268 not found.\n";
        exit;
    }
    
    echo "=== Invoice 268 Analysis ===\n";
    echo "Invoice Number: " . $invoice['invoice_number'] . "\n";
    echo "Type: " . $invoice['type'] . "\n";
    echo "Total: €" . number_format($invoice['total'], 2) . " (stored as: " . $invoice['total'] . ")\n";
    echo "Subtotal: €" . number_format($invoice['subtotal'], 2) . " (stored as: " . $invoice['subtotal'] . ")\n";
    echo "VAT Amount: €" . number_format($invoice['vat_amount'], 2) . " (stored as: " . $invoice['vat_amount'] . ")\n";
    echo "Calculated Total: €" . number_format($invoice['subtotal'] + $invoice['vat_amount'], 2) . "\n";
    
    echo "\n=== Invoice Items ===\n";
    $stmt = $pdo->prepare("SELECT * FROM invoice_items WHERE invoice_id = 268 ORDER BY id");
    $stmt->execute();
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $itemsSubtotal = 0;
    $itemsVatTotal = 0;
    $itemsTotal = 0;
    
    foreach ($items as $item) {
        echo "\n- " . $item['description'] . ":\n";
        echo "  Quantity: " . $item['quantity'] . "\n";
        echo "  Unit Price: €" . number_format($item['unit_price'], 2) . " (stored as: " . $item['unit_price'] . ")\n";
        echo "  Line Total: €" . number_format($item['line_total'], 2) . " (stored as: " . $item['line_total'] . ")\n";
        echo "  VAT Rate: " . $item['vat_rate'] . "%\n";
        
        // Calculate what the VAT should be
        $calculatedVat = $item['line_total'] * ($item['vat_rate'] / 100);
        $calculatedTotal = $item['line_total'] + $calculatedVat;
        
        echo "  Calculated VAT: €" . number_format($calculatedVat, 2) . "\n";
        echo "  Calculated Total with VAT: €" . number_format($calculatedTotal, 2) . "\n";
        
        // Check if this is a TTC price (common LOC prices)
        $commonTTCPrices = [15, 30, 45, 60, 75, 90, 105, 120, 135, 150];
        $unitPriceWithVAT = $item['unit_price'] * (1 + $item['vat_rate'] / 100);
        $totalWithVAT = $item['quantity'] * $unitPriceWithVAT;
        
        foreach ($commonTTCPrices as $ttc) {
            if (abs($totalWithVAT - $ttc) < 0.10) {
                echo "  ⚠️  This appears to be a €$ttc TTC price\n";
                echo "  Expected NET price for €$ttc TTC: €" . number_format($ttc / (1 + $item['vat_rate'] / 100), 2) . "\n";
            }
        }
        
        $itemsSubtotal += $item['line_total'];
        $itemsVatTotal += $calculatedVat;
        $itemsTotal += $calculatedTotal;
    }
    
    echo "\n=== Totals Summary ===\n";
    echo "Items Subtotal: €" . number_format($itemsSubtotal, 2) . "\n";
    echo "Items VAT Total: €" . number_format($itemsVatTotal, 2) . "\n";
    echo "Items Total: €" . number_format($itemsTotal, 2) . "\n";
    echo "\n";
    echo "Invoice Subtotal: €" . number_format($invoice['subtotal'], 2) . "\n";
    echo "Invoice VAT: €" . number_format($invoice['vat_amount'], 2) . "\n";
    echo "Invoice Total: €" . number_format($invoice['total'], 2) . "\n";
    
    // Check if this looks like a LOC invoice with TTC pricing
    if (strpos($invoice['invoice_number'], 'LOC') !== false) {
        echo "\n=== LOC Invoice TTC Analysis ===\n";
        echo "This is a LOC (location) invoice.\n";
        
        // Common course quantities and their TTC totals
        $expectedTotals = [
            7 => 210.00,  // 7 x €30 TTC
            14 => 420.00, // 14 x €30 TTC
            1 => 30.00,   // 1 x €30 TTC
            2 => 60.00,   // 2 x €30 TTC
            10 => 300.00, // 10 x €30 TTC
        ];
        
        $totalQuantity = array_sum(array_column($items, 'quantity'));
        
        if (isset($expectedTotals[$totalQuantity])) {
            $expectedTotal = $expectedTotals[$totalQuantity];
            echo "Expected TTC total for $totalQuantity sessions: €" . number_format($expectedTotal, 2) . "\n";
            echo "Current invoice total: €" . number_format($invoice['total'], 2) . "\n";
            echo "Difference: €" . number_format($expectedTotal - $invoice['total'], 2) . "\n";
            
            if (abs($expectedTotal - $invoice['total']) > 0.01) {
                echo "\n⚠️  ROUNDING ISSUE DETECTED!\n";
                echo "The invoice should total €" . number_format($expectedTotal, 2) . " but shows €" . number_format($invoice['total'], 2) . "\n";
            }
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}