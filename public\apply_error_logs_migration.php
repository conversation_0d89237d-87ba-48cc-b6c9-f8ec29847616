<?php
// Start session
session_start();

// Check authentication
if (!isset($_SESSION['user_id'])) {
    die("Unauthorized access. Please login first.");
}

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin']) {
    $isAdmin = true;
} elseif (isset($_SESSION['user_groups'])) {
    // Check if user is in Administrators group
    if (is_array($_SESSION['user_groups'])) {
        foreach ($_SESSION['user_groups'] as $group) {
            if (strtolower($group['name']) === 'administrators') {
                $isAdmin = true;
                break;
            }
        }
    } elseif ($_SESSION['user_groups'] === 'Administrators') {
        $isAdmin = true;
    }
}

if (!$isAdmin) {
    die("Only administrators can run this script.");
}

// Load environment variables and bootstrap
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Apply Error Logs Migration - Fit360 AdminDesk</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 800px; margin-top: 50px; }
        .card { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .log-output { background-color: #1e1e1e; color: #d4d4d4; padding: 15px; border-radius: 5px; font-family: 'Consolas', 'Monaco', monospace; font-size: 14px; max-height: 400px; overflow-y: auto; }
        .success { color: #4ec9b0; }
        .error { color: #f48771; }
        .info { color: #569cd6; }
        .warning { color: #dcdcaa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Apply Error Logs Migration</h4>
            </div>
            <div class="card-body">
                <p>This script will create the <code>error_logs</code> table in your database to enable error logging functionality.</p>
                
                <div class="log-output">
                    <?php
                    try {
                        echo '<span class="info">[INFO] Starting migration process...</span><br>';
                        
                        // Get database connection
                        $db = Flight::db();
                        echo '<span class="success">[✓] Database connection established</span><br>';
                        
                        // Check if table already exists
                        $tableExists = false;
                        try {
                            $result = $db->query("SHOW TABLES LIKE 'error_logs'");
                            $tableExists = $result->rowCount() > 0;
                        } catch (Exception $e) {
                            // Table check failed, assume it doesn't exist
                        }
                        
                        if ($tableExists) {
                            echo '<span class="warning">[!] Table `error_logs` already exists. Skipping creation.</span><br>';
                        } else {
                            // Read migration file
                            $migrationFile = __DIR__ . '/../database/migrations/109_create_error_logs_table.sql';
                            if (!file_exists($migrationFile)) {
                                throw new Exception("Migration file not found: $migrationFile");
                            }
                            
                            echo '<span class="info">[INFO] Reading migration file...</span><br>';
                            $sql = file_get_contents($migrationFile);
                            
                            // Remove comments and split into statements
                            $sql = preg_replace('/^--.*$/m', '', $sql);
                            $statements = array_filter(array_map('trim', explode(';', $sql)));
                            
                            echo '<span class="info">[INFO] Executing migration statements...</span><br>';
                            
                            foreach ($statements as $statement) {
                                if (!empty($statement)) {
                                    try {
                                        $db->exec($statement);
                                        echo '<span class="success">[✓] Executed: ' . htmlspecialchars(substr($statement, 0, 60)) . '...</span><br>';
                                    } catch (PDOException $e) {
                                        echo '<span class="error">[✗] Failed: ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                                        // Continue with other statements
                                    }
                                }
                            }
                            
                            echo '<span class="success">[✓] Migration completed successfully!</span><br>';
                        }
                        
                        // Test error logging
                        echo '<br><span class="info">[INFO] Testing error logging...</span><br>';
                        
                        // Create a test error entry
                        $testErrorId = 'test_' . uniqid();
                        $stmt = $db->prepare("INSERT INTO error_logs 
                            (error_id, level, message, file, line, user_id, request_method, request_uri, user_agent, ip_address, created_at) 
                            VALUES 
                            (:error_id, :level, :message, :file, :line, :user_id, :request_method, :request_uri, :user_agent, :ip_address, NOW())");
                        
                        $stmt->execute([
                            'error_id' => $testErrorId,
                            'level' => 'info',
                            'message' => 'Test error log entry created by migration script',
                            'file' => __FILE__,
                            'line' => __LINE__,
                            'user_id' => $_SESSION['user_id'],
                            'request_method' => 'GET',
                            'request_uri' => $_SERVER['REQUEST_URI'],
                            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
                            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown'
                        ]);
                        
                        echo '<span class="success">[✓] Test error logged successfully (ID: ' . $testErrorId . ')</span><br>';
                        
                        // Verify the entry was created
                        $verify = $db->prepare("SELECT COUNT(*) FROM error_logs WHERE error_id = :error_id");
                        $verify->execute(['error_id' => $testErrorId]);
                        $count = $verify->fetchColumn();
                        
                        if ($count > 0) {
                            echo '<span class="success">[✓] Error logging is working correctly!</span><br>';
                            
                            // Clean up test entry
                            $db->prepare("DELETE FROM error_logs WHERE error_id = :error_id")->execute(['error_id' => $testErrorId]);
                            echo '<span class="info">[INFO] Test entry cleaned up</span><br>';
                        } else {
                            echo '<span class="error">[✗] Failed to verify error logging</span><br>';
                        }
                        
                        echo '<br><span class="success"><strong>✓ Migration completed successfully!</strong></span><br>';
                        echo '<span class="info">The error logging system is now active.</span><br>';
                        
                    } catch (Exception $e) {
                        echo '<span class="error">[ERROR] ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                        echo '<span class="error">Stack trace:</span><br>';
                        echo '<pre class="error">' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
                    }
                    ?>
                </div>
                
                <div class="mt-4">
                    <a href="/fit/public/config" class="btn btn-primary">Back to Configuration</a>
                    <a href="/fit/public" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>