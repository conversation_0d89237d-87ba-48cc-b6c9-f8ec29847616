# Claude Code Agent Setup Guide

This guide explains how to create Claude Code agents using the agent prompts in this directory.

## Understanding Claude Code Agents vs Agent Prompts

- **Agent Prompts** (in `/agents/`): Markdown files with specialized system prompts
- **Claude Code Agents**: Built-in agents created via `/agents` command with dedicated context windows

## Creating Agents from Prompts

### Step 1: Run `/agents` Command
Type `/agents` in Claude Code and select "Create new agent"

### Step 2: Configure Each Agent

#### 1. Billing Expert Agent
- **Name**: `billing-expert`
- **Description**: Invoice generation and payment processing specialist
- **System Prompt**: Copy from `billing-invoice-agent.md`
- **Tools**: Read, Write, Edit, MultiEdit, Bash, Grep

#### 2. Retrocession Specialist
- **Name**: `retrocession-specialist`
- **Description**: Healthcare practitioner billing calculations
- **System Prompt**: Copy from `retrocession-agent.md`
- **Tools**: Read, Write, <PERSON>, Ba<PERSON>, Grep

#### 3. Database Expert
- **Name**: `database-expert`
- **Description**: Migration and schema optimization specialist
- **System Prompt**: Copy from `database-migration-agent.md`
- **Tools**: <PERSON>, <PERSON>rite, <PERSON><PERSON>, Grep

#### 4. QA Engineer
- **Name**: `qa-engineer`
- **Description**: Testing and quality assurance specialist
- **System Prompt**: Copy from `testing-qa-agent.md`
- **Tools**: Read, Write, Bash, Grep, Task

#### 5. Mobile UI Expert
- **Name**: `mobile-ui-expert`
- **Description**: Mobile-first responsive design specialist
- **System Prompt**: Copy from `mobile-ui-agent.md`
- **Tools**: Read, Write, Edit, MultiEdit, Grep

#### 6. Localization Expert
- **Name**: `localization-expert`
- **Description**: Multi-language translation specialist
- **System Prompt**: Copy from `localization-agent.md`
- **Tools**: Read, Write, Edit, Grep

#### 7. Debug Master
- **Name**: `debug-master`
- **Description**: Production issue debugging specialist
- **System Prompt**: Copy from `debugger-agent.md`
- **Tools**: Read, Bash, Grep, LS, Task

#### 8. Backend Architect
- **Name**: `backend-architect`
- **Description**: PHP backend development specialist
- **System Prompt**: Copy from `backend-engineer-agent.md`
- **Tools**: Read, Write, Edit, MultiEdit, Bash, Grep

#### 9. Frontend Developer
- **Name**: `frontend-developer`
- **Description**: UI/UX and JavaScript specialist
- **System Prompt**: Copy from `frontend-engineer-agent.md`
- **Tools**: Read, Write, Edit, MultiEdit, Grep

#### 10. Research Optimizer
- **Name**: `research-optimizer`
- **Description**: Documentation research and optimization specialist
- **System Prompt**: Copy from `research-optimization-agent.md`
- **Tools**: WebSearch, WebFetch, Read, Write

## Using Created Agents

Once created, invoke agents with:

```
/agent billing-expert "Create invoice FAC-2025-0001 for client ABC"
/agent debug-master "Fix foreign key constraint error in retrocession"
/agent research-optimizer "Find latest PHP 8.3 performance features"
```

## Agent Selection Strategy

For complex tasks, use the agent-selector prompt to determine which agent to invoke:

1. Copy `agent-selector-router.md` content
2. Ask Claude to analyze your task
3. Use the recommended agent

## Benefits of Claude Code Agents

- **Dedicated Context**: Each agent has its own context window
- **Specialized Tools**: Agents only get tools they need
- **Persistent**: Created agents remain available across sessions
- **Focused**: Agents stay specialized on their domain

## Maintenance

Keep agent prompts in `/agents/` updated as your project evolves. When prompts change significantly, recreate the corresponding Claude Code agent.