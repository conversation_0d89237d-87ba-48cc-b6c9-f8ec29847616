# Inline Product Creation Feature - Implementation Plan

## Overview
This document outlines the comprehensive plan for implementing inline product creation directly from the invoice creation page in Fit360 AdminDesk. The feature allows users to create new products on-the-fly while creating DIV invoices, with automatic catalog saving.

## Feature Requirements
- Users can create new products without leaving the invoice page
- Products are saved to the catalog with one click
- Default to "Divers" category
- Mobile-first responsive design
- Secure implementation with proper validation

## 1. UI/UX Design

### Trigger Mechanism
- Show "Create new product" option in live search dropdown when no matches found
- Only appears for DIV invoice types
- 500ms delay to avoid showing while typing

### Inline Form Design
```html
<!-- When user clicks "Create new product", the invoice line transforms -->
<tr class="invoice-item product-creation-mode">
  <td colspan="6">
    <div class="product-creation-form">
      <div class="row g-2">
        <div class="col-md-6">
          <input type="text" class="form-control form-control-sm" 
                 placeholder="Product Name*" value="{from search}">
        </div>
        <div class="col-md-3">
          <input type="text" class="form-control form-control-sm" 
                 placeholder="Code (auto)">
        </div>
        <div class="col-md-3">
          <input type="number" class="form-control form-control-sm" 
                 placeholder="Price*" step="0.01">
        </div>
      </div>
      <!-- Additional fields and buttons -->
    </div>
  </td>
</tr>
```

### Mobile Adaptations
- Full-width fields on mobile
- Minimum 44px touch targets
- Stacked layout for better usability
- Background highlighting for active form

## 2. Backend API Design

### Enhanced ProductController Methods

```php
// Enhanced quick create with better validation
public function quickCreate() {
    // CSRF validation
    if (!$this->validateCsrfToken()) {
        Flight::json(['success' => false, 'message' => 'CSRF error'], 403);
        return;
    }
    
    // Permission check
    if (!$this->requirePermission('invoice.create')) {
        Flight::json(['success' => false, 'message' => 'Unauthorized'], 403);
        return;
    }
    
    // Enhanced validation
    $data = $this->validateAndSanitizeInput($_POST);
    
    // Create product with transaction
    $db = CatalogItem::db();
    $db->beginTransaction();
    
    try {
        // Generate code if not provided
        if (empty($data['code'])) {
            $data['code'] = $this->generateProductCode($data['category_id']);
        }
        
        // Create product
        $product = CatalogItem::create($data);
        
        // Log activity
        $this->logProductCreation($product, ['source' => 'inline_invoice']);
        
        $db->commit();
        
        // Return enriched response
        Flight::json([
            'success' => true,
            'product' => $this->enrichProductResponse($product)
        ]);
        
    } catch (\Exception $e) {
        $db->rollback();
        Flight::json(['success' => false, 'message' => $e->getMessage()], 500);
    }
}
```

### New API Endpoints

```php
// Check if product name already exists
public function checkProductName() {
    $name = Flight::request()->query->name;
    
    $existing = CatalogItem::where('name', 'LIKE', $name)
                          ->where('is_active', 1)
                          ->first();
    
    if ($existing) {
        Flight::json([
            'exists' => true,
            'product' => [
                'id' => $existing->id,
                'code' => $existing->code,
                'name' => $existing->name,
                'price' => $existing->unit_price
            ]
        ]);
    } else {
        Flight::json(['exists' => false]);
    }
}

// Generate unique product code
public function generateProductCode() {
    $categoryId = Flight::request()->query->category_id;
    
    // Get category prefix
    $category = CatalogCategory::find($categoryId);
    $prefix = $category ? substr(strtoupper($category->code ?? 'PROD'), 0, 4) : 'PROD';
    
    // Generate next number
    $code = CatalogItem::generateCode($prefix);
    
    Flight::json(['success' => true, 'code' => $code]);
}
```

## 3. Database Schema Enhancements

### Migration: 108_add_inline_product_creation_support.sql

```sql
-- Add miscellaneous category flag
ALTER TABLE `catalog_categories` 
ADD COLUMN `is_misc_category` BOOLEAN DEFAULT FALSE AFTER `is_featured`;

-- Add creation tracking
ALTER TABLE `catalog_items`
ADD COLUMN `creation_source` ENUM('admin', 'inline', 'import', 'api') DEFAULT 'admin',
ADD COLUMN `created_from_invoice_id` INT UNSIGNED NULL,
ADD INDEX `idx_creation_source` (`creation_source`);

-- Pending products for rollback support
CREATE TABLE `catalog_items_pending` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `temp_id` VARCHAR(36) NOT NULL UNIQUE,
    `invoice_id` INT UNSIGNED NOT NULL,
    `product_data` JSON NOT NULL,
    `status` ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'pending',
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP DEFAULT (CURRENT_TIMESTAMP + INTERVAL 24 HOUR),
    INDEX `idx_invoice_pending` (`invoice_id`),
    FOREIGN KEY (`invoice_id`) REFERENCES `invoices`(`id`) ON DELETE CASCADE
);

-- Product code sequences per category
CREATE TABLE `catalog_sequences` (
    `category_id` INT UNSIGNED NOT NULL PRIMARY KEY,
    `prefix` VARCHAR(10) NOT NULL,
    `last_number` INT UNSIGNED DEFAULT 0,
    FOREIGN KEY (`category_id`) REFERENCES `catalog_categories`(`id`)
);
```

## 4. Security Implementation

### Input Validation Methods

```php
protected function validateAndSanitizeInput($data) {
    $rules = [
        'name' => ['required', 'string', 'max:255', 'no_sql_keywords'],
        'code' => ['optional', 'alphanumeric_dash', 'max:50', 'unique:catalog_items'],
        'category_id' => ['required', 'exists:catalog_categories,id'],
        'unit_price' => ['required', 'numeric', 'min:0', 'max:999999.99'],
        'vat_rate_id' => ['required', 'exists:config_vat_rates,id'],
        'description' => ['optional', 'string', 'max:1000']
    ];
    
    $validator = new Validator($data, $rules);
    
    if (!$validator->passes()) {
        throw new ValidationException($validator->errors());
    }
    
    // Additional sanitization
    $clean = [];
    $clean['name'] = htmlspecialchars(trim($data['name']), ENT_QUOTES, 'UTF-8');
    $clean['code'] = strtoupper(preg_replace('/[^A-Za-z0-9\-_]/', '', $data['code'] ?? ''));
    $clean['category_id'] = intval($data['category_id']);
    $clean['unit_price'] = round(floatval($data['unit_price']), 2);
    $clean['vat_rate_id'] = intval($data['vat_rate_id']);
    $clean['description'] = htmlspecialchars(trim($data['description'] ?? ''), ENT_QUOTES, 'UTF-8');
    
    return $clean;
}
```

### Rate Limiting

```php
// In routes file
Flight::route('POST /api/products/quick-create', function() {
    // Rate limiting check
    $userId = $_SESSION['user_id'];
    $key = "product_create:$userId";
    
    if (!RateLimiter::attempt($key, 10, 300)) { // 10 attempts per 5 minutes
        Flight::json(['error' => 'Too many requests'], 429);
        return;
    }
    
    $controller = new ProductController();
    return $controller->quickCreate();
});
```

### Security Audit Logging

```php
protected function logProductCreation($product, $context = []) {
    SecurityAudit::create([
        'action' => 'product_quick_create',
        'user_id' => $_SESSION['user_id'],
        'ip_address' => $_SERVER['REMOTE_ADDR'],
        'user_agent' => $_SERVER['HTTP_USER_AGENT'],
        'resource_type' => 'catalog_item',
        'resource_id' => $product->id,
        'details' => json_encode([
            'product_code' => $product->code,
            'product_name' => $product->name,
            'source' => $context['source'] ?? 'unknown',
            'invoice_id' => $context['invoice_id'] ?? null
        ]),
        'created_at' => date('Y-m-d H:i:s')
    ]);
}
```

## 5. Frontend Integration

### Enhanced ProductLiveSearch.js

```javascript
class ProductLiveSearch {
    // ... existing code ...
    
    showDropdown(items, searchQuery) {
        // ... existing code ...
        
        // Add create option if no exact match
        if (!this.hasExactMatch(searchQuery, items)) {
            const createOption = this.createNewProductOption(searchQuery);
            dropdown.appendChild(createOption);
        }
    }
    
    createNewProductOption(searchQuery) {
        const div = document.createElement('div');
        div.className = 'product-search-create-new';
        div.innerHTML = `
            <i class="bi bi-plus-circle me-2"></i>
            <span>Create new product: "<strong>${this.escapeHtml(searchQuery)}</strong>"</span>
        `;
        
        div.addEventListener('click', (e) => {
            e.preventDefault();
            this.showInlineCreationForm(searchQuery);
        });
        
        return div;
    }
    
    async showInlineCreationForm(defaultName) {
        const row = this.currentInput.closest('tr');
        
        // Check if name already exists
        const checkResponse = await fetch(`/api/products/check-name?name=${encodeURIComponent(defaultName)}`);
        const checkData = await checkResponse.json();
        
        if (checkData.exists) {
            if (confirm(`Product "${checkData.product.name}" already exists. Use it?`)) {
                this.selectProduct(checkData.product);
                return;
            }
        }
        
        // Transform row to creation mode
        row.classList.add('product-creation-mode');
        const formHtml = this.buildCreationForm(defaultName);
        row.querySelector('td').colSpan = 6;
        row.querySelector('td').innerHTML = formHtml;
        
        // Initialize form
        this.initializeCreationForm(row);
    }
}
```

## 6. Implementation Tasks (Priority Order)

### High Priority
1. **Database Schema Updates** (Task #19)
   - Create migration file
   - Add is_misc_category field
   - Create pending products table
   
2. **Backend API Implementation** (Tasks #21-23)
   - Enhance quickCreate method
   - Add validation methods
   - Create API routes with middleware
   
3. **Security Implementation** (Tasks #29-30, 32)
   - Add authorization checks
   - Implement rate limiting
   - Add input sanitization

### Medium Priority
4. **Frontend UI Implementation** (Tasks #25-28)
   - Extend product-live-search.js
   - Create inline form HTML/CSS
   - Implement AJAX submission
   - Add loading states

5. **Testing & Validation** (Tasks #24, 31, 33)
   - Create security tests
   - Test duplicate detection
   - Implement audit logging

### Low Priority
6. **Polish & Documentation** (Tasks #34-36)
   - Test mobile responsiveness
   - Create cleanup jobs
   - Update documentation

## 7. Testing Strategy

### Unit Tests
- Input validation methods
- Code generation logic
- Permission checks

### Integration Tests
- API endpoint responses
- Database transactions
- Rate limiting

### Security Tests
- SQL injection attempts
- XSS prevention
- CSRF validation
- Rate limit enforcement

### UI Tests
- Mobile responsiveness
- Form validation
- Error handling
- Success flows

## 8. Rollback Strategy

If invoice creation is cancelled:
1. Pending products remain in pending state
2. Cleanup job runs after 24 hours
3. No catalog pollution

If product creation fails:
1. Transaction rollback prevents partial data
2. Clear error message to user
3. Form remains open for retry

## 9. Success Metrics

- Reduced time to create invoices with new products
- Increased catalog completeness
- No security incidents
- Positive user feedback on workflow

## 10. Future Enhancements

- Bulk product import from invoice
- Product templates for common items  
- Barcode scanning support
- AI-powered product suggestions
- Integration with supplier catalogs

---

This comprehensive plan ensures a secure, user-friendly implementation of inline product creation that enhances the invoice workflow while maintaining data integrity and system security.