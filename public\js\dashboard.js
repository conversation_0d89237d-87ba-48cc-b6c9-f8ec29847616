/**
 * Dashboard JavaScript
 * Simple, clean dashboard functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Auto-refresh dashboard data every 5 minutes
    const REFRESH_INTERVAL = 5 * 60 * 1000; // 5 minutes
    
    function refreshDashboardData() {
        // Refresh stats
        fetch('/api/dashboard/stats')
            .then(response => response.json())
            .then(data => {
                updateStats(data);
            })
            .catch(error => console.error('Error refreshing stats:', error));
    }
    
    function updateStats(stats) {
        // Update stat cards
        const statElements = {
            'total_clients': document.querySelector('[data-stat="total_clients"]'),
            'total_patients': document.querySelector('[data-stat="total_patients"]'),
            'total_invoices': document.querySelector('[data-stat="total_invoices"]'),
            'revenue_this_month': document.querySelector('[data-stat="revenue_this_month"]')
        };
        
        Object.keys(statElements).forEach(key => {
            if (statElements[key] && stats[key] !== undefined) {
                statElements[key].textContent = formatValue(key, stats[key]);
            }
        });
    }
    
    function formatValue(key, value) {
        if (key === 'revenue_this_month') {
            return '€' + parseFloat(value).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }
        return parseInt(value).toLocaleString();
    }
    
    // Set up auto-refresh if enabled
    const autoRefreshEnabled = localStorage.getItem('dashboard_auto_refresh') !== 'false';
    if (autoRefreshEnabled) {
        setInterval(refreshDashboardData, REFRESH_INTERVAL);
    }
    
    // Handle mobile responsiveness
    function handleMobileView() {
        const isMobile = window.innerWidth < 768;
        
        if (isMobile) {
            // Convert tables to cards on mobile
            const tables = document.querySelectorAll('.table-responsive table');
            tables.forEach(table => {
                table.classList.add('table-sm');
            });
        }
    }
    
    // Initial mobile check
    handleMobileView();
    
    // Re-check on window resize
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleMobileView, 250);
    });
    
    // Add click tracking for quick actions
    document.querySelectorAll('.btn, .list-group-item-action').forEach(element => {
        element.addEventListener('click', function() {
            // Track action clicks (can be extended to send analytics)
            console.log('Action clicked:', this.textContent.trim());
        });
    });
});