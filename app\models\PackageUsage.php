<?php

namespace App\Models;

use App\Core\Model;

class PackageUsage extends Model
{
    protected $table = 'package_usage';
    
    protected $fillable = [
        'purchase_id',
        'usage_date',
        'service_id',
        'therapist_id',
        'invoice_id',
        'notes'
    ];
    
    protected $casts = [
        'usage_date' => 'datetime'
    ];
    
    /**
     * Get the package purchase
     */
    public function purchase()
    {
        return $this->belongsTo(PackagePurchase::class, 'purchase_id');
    }
    
    /**
     * Get the service
     */
    public function service()
    {
        return $this->belongsTo(CatalogItem::class, 'service_id');
    }
    
    /**
     * Get the therapist
     */
    public function therapist()
    {
        return $this->belongsTo(User::class, 'therapist_id');
    }
    
    /**
     * Get the invoice (if any)
     */
    public function invoice()
    {
        return $this->belongsTo(SalesInvoice::class, 'invoice_id');
    }
    
    /**
     * Get formatted usage date
     */
    public function getFormattedUsageDateAttribute()
    {
        return $this->usage_date ? $this->usage_date->format('d/m/Y H:i') : '';
    }
    
    /**
     * Get usage description
     */
    public function getDescriptionAttribute()
    {
        $service = $this->service;
        $therapist = $this->therapist;
        
        $description = $service ? $service->name : 'Unknown Service';
        
        if ($therapist) {
            $description .= ' - ' . $therapist->name;
        }
        
        return $description;
    }
    
    /**
     * Scope for usage by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('usage_date', [$startDate, $endDate]);
    }
    
    /**
     * Scope for usage by therapist
     */
    public function scopeByTherapist($query, $therapistId)
    {
        return $query->where('therapist_id', $therapistId);
    }
    
    /**
     * Scope for usage by service
     */
    public function scopeByService($query, $serviceId)
    {
        return $query->where('service_id', $serviceId);
    }
    
    /**
     * Get usage summary for reporting
     */
    public static function getUsageSummary($startDate = null, $endDate = null)
    {
        $query = static::query();
        
        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }
        
        return $query->selectRaw('
                service_id,
                COUNT(*) as usage_count,
                COUNT(DISTINCT purchase_id) as unique_packages,
                COUNT(DISTINCT therapist_id) as unique_therapists
            ')
            ->groupBy('service_id')
            ->get();
    }
    
    /**
     * Get therapist performance summary
     */
    public static function getTherapistSummary($startDate = null, $endDate = null)
    {
        $query = static::query();
        
        if ($startDate && $endDate) {
            $query->dateRange($startDate, $endDate);
        }
        
        return $query->selectRaw('
                therapist_id,
                COUNT(*) as sessions_count,
                COUNT(DISTINCT service_id) as services_provided,
                COUNT(DISTINCT purchase_id) as packages_serviced
            ')
            ->whereNotNull('therapist_id')
            ->groupBy('therapist_id')
            ->get();
    }
}