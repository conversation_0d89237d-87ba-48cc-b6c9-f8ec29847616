<?php
/**
 * Database Column Verification Script
 * Checks for potential column mismatches in common queries
 */

require_once __DIR__ . '/../config/database.php';

echo "<h1>Database Column Verification</h1>";
echo "<pre>";

try {
    $db = Flight::db();
    
    echo "=== Checking invoice_types table ===\n";
    
    // Get actual columns from invoice_types
    $stmt = $db->query("SHOW COLUMNS FROM invoice_types");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    echo "Actual columns in invoice_types: " . implode(', ', $columns) . "\n\n";
    
    // Check if display_order exists
    if (in_array('display_order', $columns)) {
        echo "✓ display_order column exists\n";
    } else {
        echo "✗ display_order column MISSING (using 'order' instead)\n";
    }
    
    // Check if config_invoice_types exists
    echo "\n=== Checking config_invoice_types table ===\n";
    try {
        $stmt = $db->query("SHOW COLUMNS FROM config_invoice_types");
        $configColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "Actual columns in config_invoice_types: " . implode(', ', $configColumns) . "\n";
    } catch (PDOException $e) {
        echo "✗ config_invoice_types table does not exist\n";
    }
    
    // Search for potential column issues in code
    echo "\n=== Potential Column Issues in Code ===\n";
    
    $potentialIssues = [
        'invoice_types' => ['display_order', 'numbering_pattern', 'is_system', 'icon'],
        'invoices' => ['type_id', 'invoice_type_id'],
        'invoice_lines' => ['sort_order', 'display_order'],
        'users' => ['vat_intercommunautaire', 'exclude_patient_line_from_retrocession']
    ];
    
    foreach ($potentialIssues as $table => $columns) {
        echo "\nChecking $table table:\n";
        
        try {
            $stmt = $db->query("SHOW COLUMNS FROM $table");
            $actualColumns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            foreach ($columns as $column) {
                if (in_array($column, $actualColumns)) {
                    echo "  ✓ $column exists\n";
                } else {
                    echo "  ✗ $column MISSING\n";
                }
            }
        } catch (PDOException $e) {
            echo "  ✗ Table $table does not exist\n";
        }
    }
    
    // Check for queries that might fail
    echo "\n=== Testing Common Queries ===\n";
    
    $testQueries = [
        "SELECT * FROM invoice_types WHERE is_active = 1 ORDER BY `order` LIMIT 1" => "Invoice types with order",
        "SELECT * FROM invoices LIMIT 1" => "Basic invoice query",
        "SELECT * FROM invoice_lines LIMIT 1" => "Invoice lines query",
        "SELECT * FROM users WHERE id = 1" => "User query"
    ];
    
    foreach ($testQueries as $query => $description) {
        try {
            $stmt = $db->query($query);
            echo "✓ $description - OK\n";
        } catch (PDOException $e) {
            echo "✗ $description - ERROR: " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Database connection error: " . $e->getMessage() . "\n";
}

echo "</pre>";

// Summary
echo "<h2>Summary</h2>";
echo "<ul>";
echo "<li>The main issue was the ConfigController using 'display_order' instead of 'order'</li>";
echo "<li>This has been fixed in the code by changing the query</li>";
echo "<li>Migration 131 has been created to add display_order column for future compatibility</li>";
echo "<li>Consider standardizing on either invoice_types or config_invoice_types table</li>";
echo "</ul>";