<?php

namespace App\Services;

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Writer\Csv;
use PhpOffice\PhpSpreadsheet\Writer\Pdf\Tcpdf;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class ExportService
{
    private $defaultDateFormat = 'd/m/Y';
    private $defaultCurrencyFormat = '#,##0.00 "CHF"';
    
    /**
     * Export data to Excel format
     * 
     * @param array $data The data to export
     * @param array $columns Column configuration ['key' => 'Label'] or detailed config
     * @param string $filename Output filename (without extension)
     * @param array $options Additional options (sheet_name, title, filters, etc.)
     * @return void Outputs the file directly
     */
    public function exportToExcel(array $data, array $columns, string $filename, array $options = []): void
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Set sheet name
        $sheetName = $options['sheet_name'] ?? 'Export';
        $sheet->setTitle(substr($sheetName, 0, 31)); // Excel sheet name limit
        
        $currentRow = 1;
        
        // Add title if provided
        if (!empty($options['title'])) {
            $sheet->setCellValue('A' . $currentRow, $options['title']);
            $sheet->mergeCells('A' . $currentRow . ':' . $this->getColumnLetter(count($columns)) . $currentRow);
            $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true)->setSize(14);
            $sheet->getStyle('A' . $currentRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
            $currentRow += 2;
        }
        
        // Add filters/date range if provided
        if (!empty($options['filters'])) {
            foreach ($options['filters'] as $filterLabel => $filterValue) {
                $sheet->setCellValue('A' . $currentRow, $filterLabel . ':');
                $sheet->setCellValue('B' . $currentRow, $filterValue);
                $sheet->getStyle('A' . $currentRow)->getFont()->setBold(true);
                $currentRow++;
            }
            $currentRow++;
        }
        
        $headerRow = $currentRow;
        
        // Add headers
        $columnIndex = 1;
        $columnConfig = [];
        foreach ($columns as $key => $config) {
            if (is_string($config)) {
                // Simple column definition
                $columnConfig[$key] = [
                    'label' => $config,
                    'type' => 'text'
                ];
                $sheet->setCellValue($this->getColumnLetter($columnIndex) . $headerRow, $config);
            } else {
                // Detailed column definition
                $columnConfig[$key] = $config;
                $sheet->setCellValue($this->getColumnLetter($columnIndex) . $headerRow, $config['label'] ?? $key);
            }
            $columnIndex++;
        }
        
        // Style headers
        $headerRange = 'A' . $headerRow . ':' . $this->getColumnLetter(count($columns)) . $headerRow;
        $sheet->getStyle($headerRange)->applyFromArray([
            'font' => ['bold' => true, 'color' => ['rgb' => 'FFFFFF']],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => '4472C4']
            ],
            'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['rgb' => '000000']
                ]
            ]
        ]);
        
        // Add data
        $dataRow = $headerRow + 1;
        foreach ($data as $row) {
            $columnIndex = 1;
            foreach ($columnConfig as $key => $config) {
                $value = $row[$key] ?? '';
                $cellAddress = $this->getColumnLetter($columnIndex) . $dataRow;
                
                // Apply formatting based on type
                if (isset($config['type'])) {
                    switch ($config['type']) {
                        case 'number':
                        case 'integer':
                            $sheet->setCellValue($cellAddress, is_numeric($value) ? (float)$value : 0);
                            if (!empty($config['format'])) {
                                $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode($config['format']);
                            }
                            break;
                            
                        case 'currency':
                            $sheet->setCellValue($cellAddress, is_numeric($value) ? (float)$value : 0);
                            $format = $config['format'] ?? $this->defaultCurrencyFormat;
                            $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode($format);
                            break;
                            
                        case 'date':
                        case 'datetime':
                            if ($value instanceof \DateTime) {
                                $excelDate = \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($value);
                                $sheet->setCellValue($cellAddress, $excelDate);
                            } elseif (is_string($value) && !empty($value)) {
                                try {
                                    $date = new \DateTime($value);
                                    $excelDate = \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel($date);
                                    $sheet->setCellValue($cellAddress, $excelDate);
                                } catch (\Exception $e) {
                                    $sheet->setCellValue($cellAddress, $value);
                                }
                            }
                            $format = $config['format'] ?? ($config['type'] === 'datetime' ? 'd/m/Y H:i' : $this->defaultDateFormat);
                            $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode($format);
                            break;
                            
                        case 'percentage':
                            $sheet->setCellValue($cellAddress, is_numeric($value) ? (float)$value : 0);
                            $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode('0.00%');
                            break;
                            
                        default:
                            $sheet->setCellValue($cellAddress, $value);
                    }
                } else {
                    $sheet->setCellValue($cellAddress, $value);
                }
                
                $columnIndex++;
            }
            $dataRow++;
        }
        
        // Add borders to data
        $dataRange = 'A' . ($headerRow + 1) . ':' . $this->getColumnLetter(count($columns)) . ($dataRow - 1);
        if ($dataRow > $headerRow + 1) {
            $sheet->getStyle($dataRange)->applyFromArray([
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => Border::BORDER_THIN,
                        'color' => ['rgb' => 'D9D9D9']
                    ]
                ]
            ]);
        }
        
        // Auto-size columns
        foreach (range(1, count($columns)) as $col) {
            $sheet->getColumnDimension($this->getColumnLetter($col))->setAutoSize(true);
        }
        
        // Add summary/totals if provided
        if (!empty($options['totals'])) {
            $totalsRow = $dataRow + 1;
            $sheet->setCellValue('A' . $totalsRow, 'Total');
            $sheet->getStyle('A' . $totalsRow)->getFont()->setBold(true);
            
            foreach ($options['totals'] as $columnKey => $total) {
                $columnIndex = array_search($columnKey, array_keys($columnConfig)) + 1;
                if ($columnIndex > 0) {
                    $cellAddress = $this->getColumnLetter($columnIndex) . $totalsRow;
                    $sheet->setCellValue($cellAddress, $total);
                    $sheet->getStyle($cellAddress)->getFont()->setBold(true);
                    
                    // Apply same formatting as column
                    if (isset($columnConfig[$columnKey]['type'])) {
                        switch ($columnConfig[$columnKey]['type']) {
                            case 'currency':
                                $format = $columnConfig[$columnKey]['format'] ?? $this->defaultCurrencyFormat;
                                $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode($format);
                                break;
                            case 'number':
                                if (!empty($columnConfig[$columnKey]['format'])) {
                                    $sheet->getStyle($cellAddress)->getNumberFormat()->setFormatCode($columnConfig[$columnKey]['format']);
                                }
                                break;
                        }
                    }
                }
            }
        }
        
        // Output file
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }
    
    /**
     * Export data to CSV format
     * 
     * @param array $data The data to export
     * @param array $columns Column configuration
     * @param string $filename Output filename (without extension)
     * @param array $options Additional options (delimiter, enclosure, etc.)
     * @return void Outputs the file directly
     */
    public function exportToCSV(array $data, array $columns, string $filename, array $options = []): void
    {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // Add headers
        $columnIndex = 1;
        $columnKeys = [];
        foreach ($columns as $key => $config) {
            $label = is_string($config) ? $config : ($config['label'] ?? $key);
            $sheet->setCellValue($this->getColumnLetter($columnIndex) . '1', $label);
            $columnKeys[] = $key;
            $columnIndex++;
        }
        
        // Add data
        $rowIndex = 2;
        foreach ($data as $row) {
            $columnIndex = 1;
            foreach ($columnKeys as $key) {
                $value = $row[$key] ?? '';
                $sheet->setCellValue($this->getColumnLetter($columnIndex) . $rowIndex, $value);
                $columnIndex++;
            }
            $rowIndex++;
        }
        
        // Configure CSV writer
        $writer = new Csv($spreadsheet);
        $writer->setDelimiter($options['delimiter'] ?? ',');
        $writer->setEnclosure($options['enclosure'] ?? '"');
        $writer->setLineEnding($options['line_ending'] ?? "\r\n");
        $writer->setUseBOM($options['use_bom'] ?? true);
        
        // Output file
        header('Content-Type: text/csv; charset=UTF-8');
        header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
        header('Cache-Control: max-age=0');
        
        $writer->save('php://output');
        exit;
    }
    
    /**
     * Export data to PDF format using TCPDF
     * 
     * @param array $data The data to export
     * @param array $columns Column configuration
     * @param string $filename Output filename (without extension)
     * @param array $options Additional options (orientation, title, etc.)
     * @return void Outputs the file directly
     */
    public function exportToPDF(array $data, array $columns, string $filename, array $options = []): void
    {
        // For now, we'll use the existing TCPDF directly since PhpSpreadsheet's PDF export has limitations
        $pdf = new \TCPDF($options['orientation'] ?? 'L', 'mm', $options['format'] ?? 'A4', true, 'UTF-8', false);
        
        // Set document information
        $pdf->SetCreator('Fit360 AdminDesk');
        $pdf->SetAuthor($options['author'] ?? 'System');
        $pdf->SetTitle($options['title'] ?? 'Export');
        $pdf->SetSubject($options['subject'] ?? 'Data Export');
        
        // Remove default header/footer
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);
        
        // Set margins
        $pdf->SetMargins(10, 10, 10);
        
        // Add a page
        $pdf->AddPage();
        
        // Set font
        $pdf->SetFont('helvetica', '', 10);
        
        // Add title if provided
        if (!empty($options['title'])) {
            $pdf->SetFont('helvetica', 'B', 16);
            $pdf->Cell(0, 10, $options['title'], 0, 1, 'C');
            $pdf->Ln(5);
        }
        
        // Add filters if provided
        if (!empty($options['filters'])) {
            $pdf->SetFont('helvetica', '', 10);
            foreach ($options['filters'] as $filterLabel => $filterValue) {
                $pdf->Cell(40, 7, $filterLabel . ':', 0, 0, 'L');
                $pdf->Cell(0, 7, $filterValue, 0, 1, 'L');
            }
            $pdf->Ln(5);
        }
        
        // Calculate column widths
        $pageWidth = $pdf->getPageWidth() - 20; // Minus margins
        $columnCount = count($columns);
        $defaultWidth = $pageWidth / $columnCount;
        
        // Table header
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->SetFillColor(68, 114, 196);
        $pdf->SetTextColor(255, 255, 255);
        
        $columnWidths = [];
        foreach ($columns as $key => $config) {
            $label = is_string($config) ? $config : ($config['label'] ?? $key);
            $width = is_array($config) && isset($config['pdf_width']) ? $config['pdf_width'] : $defaultWidth;
            $columnWidths[] = $width;
            $pdf->Cell($width, 8, $label, 1, 0, 'C', true);
        }
        $pdf->Ln();
        
        // Table data
        $pdf->SetFont('helvetica', '', 9);
        $pdf->SetTextColor(0, 0, 0);
        $fill = false;
        
        foreach ($data as $row) {
            $pdf->SetFillColor(245, 245, 245);
            $columnIndex = 0;
            foreach ($columns as $key => $config) {
                $value = $row[$key] ?? '';
                $align = 'L';
                
                // Format value based on type
                if (is_array($config) && isset($config['type'])) {
                    switch ($config['type']) {
                        case 'currency':
                            $value = is_numeric($value) ? 'CHF ' . number_format((float)$value, 2, '.', "'") : $value;
                            $align = 'R';
                            break;
                        case 'number':
                            $value = is_numeric($value) ? number_format((float)$value, 2, '.', "'") : $value;
                            $align = 'R';
                            break;
                        case 'date':
                            if ($value instanceof \DateTime) {
                                $value = $value->format('d/m/Y');
                            } elseif (is_string($value) && !empty($value)) {
                                try {
                                    $date = new \DateTime($value);
                                    $value = $date->format('d/m/Y');
                                } catch (\Exception $e) {
                                    // Keep original value
                                }
                            }
                            $align = 'C';
                            break;
                    }
                }
                
                $pdf->Cell($columnWidths[$columnIndex], 7, $value, 1, 0, $align, $fill);
                $columnIndex++;
            }
            $pdf->Ln();
            $fill = !$fill;
        }
        
        // Add totals if provided
        if (!empty($options['totals'])) {
            $pdf->SetFont('helvetica', 'B', 10);
            $pdf->Cell($columnWidths[0], 8, 'Total', 1, 0, 'L');
            
            $columnIndex = 1;
            foreach (array_slice(array_keys($columns), 1) as $key) {
                if (isset($options['totals'][$key])) {
                    $value = $options['totals'][$key];
                    if (isset($columns[$key]['type']) && $columns[$key]['type'] === 'currency') {
                        $value = 'CHF ' . number_format((float)$value, 2, '.', "'");
                    }
                    $pdf->Cell($columnWidths[$columnIndex], 8, $value, 1, 0, 'R');
                } else {
                    $pdf->Cell($columnWidths[$columnIndex], 8, '', 1, 0, 'C');
                }
                $columnIndex++;
            }
        }
        
        // Output file
        $pdf->Output($filename . '.pdf', 'D');
        exit;
    }
    
    /**
     * Export data with multiple sheets (Excel only)
     * 
     * @param array $sheets Array of sheet configurations ['name' => '', 'data' => [], 'columns' => []]
     * @param string $filename Output filename
     * @return void
     */
    public function exportMultipleSheets(array $sheets, string $filename): void
    {
        $spreadsheet = new Spreadsheet();
        
        foreach ($sheets as $index => $sheetConfig) {
            if ($index > 0) {
                $spreadsheet->createSheet();
            }
            
            $sheet = $spreadsheet->getSheet($index);
            $sheet->setTitle(substr($sheetConfig['name'] ?? 'Sheet' . ($index + 1), 0, 31));
            
            // Add headers
            $columnIndex = 1;
            foreach ($sheetConfig['columns'] as $key => $config) {
                $label = is_string($config) ? $config : ($config['label'] ?? $key);
                $sheet->setCellValue($this->getColumnLetter($columnIndex) . '1', $label);
                $columnIndex++;
            }
            
            // Style headers
            $headerRange = 'A1:' . $this->getColumnLetter(count($sheetConfig['columns'])) . '1';
            $sheet->getStyle($headerRange)->applyFromArray([
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E8E8E8']
                ]
            ]);
            
            // Add data
            $rowIndex = 2;
            foreach ($sheetConfig['data'] as $row) {
                $columnIndex = 1;
                foreach ($sheetConfig['columns'] as $key => $config) {
                    $value = $row[$key] ?? '';
                    $sheet->setCellValue($this->getColumnLetter($columnIndex) . $rowIndex, $value);
                    $columnIndex++;
                }
                $rowIndex++;
            }
            
            // Auto-size columns
            foreach (range(1, count($sheetConfig['columns'])) as $col) {
                $sheet->getColumnDimension($this->getColumnLetter($col))->setAutoSize(true);
            }
        }
        
        // Set first sheet as active
        $spreadsheet->setActiveSheetIndex(0);
        
        // Output file
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');
        
        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    }
    
    /**
     * Log export activity
     * 
     * @param string $type Export type (excel, csv, pdf)
     * @param string $module Module name (invoices, users, products)
     * @param int $recordCount Number of records exported
     * @param array $filters Applied filters
     * @return void
     */
    public function logExport(string $type, string $module, int $recordCount, array $filters = []): void
    {
        try {
            $db = \Flight::db();
            $stmt = $db->prepare("
                INSERT INTO export_logs (
                    user_id, export_type, module, record_count, 
                    filters, exported_at, ip_address, user_agent
                ) VALUES (
                    :user_id, :export_type, :module, :record_count,
                    :filters, NOW(), :ip_address, :user_agent
                )
            ");
            
            $stmt->execute([
                ':user_id' => $_SESSION['user_id'] ?? null,
                ':export_type' => $type,
                ':module' => $module,
                ':record_count' => $recordCount,
                ':filters' => json_encode($filters),
                ':ip_address' => $_SERVER['REMOTE_ADDR'] ?? null,
                ':user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (\Exception $e) {
            // Log error but don't interrupt export
            error_log('Export logging failed: ' . $e->getMessage());
        }
    }
    
    /**
     * Convert column number to Excel column letter
     * 
     * @param int $columnNumber
     * @return string
     */
    private function getColumnLetter(int $columnNumber): string
    {
        $letter = '';
        while ($columnNumber > 0) {
            $columnNumber--;
            $letter = chr(65 + ($columnNumber % 26)) . $letter;
            $columnNumber = intval($columnNumber / 26);
        }
        return $letter;
    }
}