<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Cache Reload - Invoice Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 30px; border-radius: 10px; margin-bottom: 30px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .btn { padding: 12px 24px; margin: 8px; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .code { background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; margin: 10px 0; border: 1px solid #e9ecef; }
        .success { border-left: 5px solid #28a745; background: #f8fff9; }
        .error { border-left: 5px solid #dc3545; background: #fff5f5; }
        .warning { border-left: 5px solid #ffc107; background: #fffdf5; }
        .info { border-left: 5px solid #17a2b8; background: #f0f8ff; }
        .step { margin: 15px 0; padding: 15px; border-radius: 5px; background: #e9ecef; }
        .step-number { background: #007bff; color: white; border-radius: 50%; width: 30px; height: 30px; display: inline-flex; align-items: center; justify-content: center; margin-right: 10px; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Force Cache Reload</h1>
            <p>Ensure JavaScript fixes are properly loaded</p>
        </div>

        <div class="card error">
            <h2>⚠️ Current Issue</h2>
            <p>The JavaScript fixes have been applied but the browser is still showing the old cached version with the <strong>"redeclaration of const urlParams"</strong> error.</p>
            <p>This cache-busting solution will force the browser to load the updated template.</p>
        </div>

        <div class="card info">
            <h2>🛠️ Cache-Busting Solutions</h2>
            
            <div class="step">
                <span class="step-number">1</span>
                <strong>Hard Refresh (Recommended)</strong>
                <p>Press <strong>Ctrl+Shift+R</strong> (or Cmd+Shift+R on Mac) to force reload</p>
            </div>
            
            <div class="step">
                <span class="step-number">2</span>
                <strong>Clear Browser Cache</strong>
                <p>Press <strong>Ctrl+Shift+Delete</strong> to open cache clearing dialog</p>
            </div>
            
            <div class="step">
                <span class="step-number">3</span>
                <strong>Use Cache-Busting URLs</strong>
                <p>Click these links that include cache-busting parameters:</p>
                <div style="margin-top: 10px;">
                    <a href="/fit/public/invoices/create?_cb=<%= new Date().getTime() %>" class="btn btn-primary" target="_blank">Standard Invoice (Cache-Busted)</a>
                    <a href="/fit/public/invoices/create?type=location&_cb=<%= new Date().getTime() %>" class="btn btn-success" target="_blank">Location Invoice (Cache-Busted)</a>
                </div>
            </div>
            
            <div class="step">
                <span class="step-number">4</span>
                <strong>Private/Incognito Mode</strong>
                <p>Open in private browsing mode to bypass cache entirely</p>
            </div>
        </div>

        <div class="card warning">
            <h2>🧪 Test the Fix</h2>
            <p>After clearing cache, test these specific JavaScript functions:</p>
            <div class="code">
                // Open developer console (F12) and run:<br>
                console.log('URL Params variables:', {<br>
                &nbsp;&nbsp;urlParams: typeof urlParams,<br>
                &nbsp;&nbsp;urlParamsMain: typeof urlParamsMain,<br>
                &nbsp;&nbsp;urlParams1: typeof urlParams1,<br>
                &nbsp;&nbsp;urlParams2: typeof urlParams2,<br>
                &nbsp;&nbsp;urlParamsDebug: typeof urlParamsDebug,<br>
                &nbsp;&nbsp;urlParams3: typeof urlParams3,<br>
                &nbsp;&nbsp;urlParams4: typeof urlParams4<br>
                });
            </div>
        </div>

        <div class="card success">
            <h2>✅ Expected Results After Cache Clear</h2>
            <ul>
                <li>❌ No "redeclaration of const urlParams" error</li>
                <li>✅ Debug system loads (InvoiceDebugger available)</li>
                <li>✅ Debug and Auto Fix buttons appear</li>
                <li>✅ Console shows comprehensive logs with 🔍 prefix</li>
                <li>✅ Coach dropdown populates for location invoices</li>
                <li>✅ All URL parameter variables have unique names</li>
            </ul>
        </div>

        <div class="card">
            <h2>🔍 Quick Cache Test</h2>
            <p>Click this button to test if cache has been cleared:</p>
            <button class="btn btn-primary" onclick="testCacheStatus()">Test Cache Status</button>
            <div id="cacheTestResult" style="margin-top: 15px;"></div>
        </div>

        <div class="card info">
            <h2>📝 Manual Cache Clearing Steps</h2>
            <h3>Chrome:</h3>
            <ol>
                <li>Press F12 to open DevTools</li>
                <li>Right-click the refresh button</li>
                <li>Select "Empty Cache and Hard Reload"</li>
            </ol>
            
            <h3>Firefox:</h3>
            <ol>
                <li>Press Ctrl+Shift+Delete</li>
                <li>Select "Cache" and "Everything"</li>
                <li>Click "Clear Now"</li>
            </ol>
            
            <h3>Edge:</h3>
            <ol>
                <li>Press Ctrl+Shift+Delete</li>
                <li>Select "Cached images and files"</li>
                <li>Click "Clear now"</li>
            </ol>
        </div>

        <div class="card">
            <h2>🚀 Final Test Links</h2>
            <p>Use these cache-busting links to test the fixed invoice system:</p>
            <div>
                <a href="/fit/public/invoices/create?_nocache=true&_timestamp=<%= Date.now() %>" class="btn btn-primary" target="_blank">🔍 Test Standard Invoice</a>
                <a href="/fit/public/invoices/create?type=location&_nocache=true&_timestamp=<%= Date.now() %>" class="btn btn-success" target="_blank">🎯 Test Location Invoice</a>
                <a href="/fit/public/comprehensive-invoice-debug-summary.html" class="btn btn-warning" target="_blank">📊 Debug Summary</a>
            </div>
        </div>
    </div>

    <script>
        // Replace template-like syntax with actual values
        document.addEventListener('DOMContentLoaded', function() {
            const timestamp = Date.now();
            const links = document.querySelectorAll('a[href*="<%= "]');
            links.forEach(link => {
                link.href = link.href.replace(/<%= new Date\(\)\.getTime\(\) %>/g, timestamp);
                link.href = link.href.replace(/<%= Date\.now\(\) %>/g, timestamp);
            });
        });

        function testCacheStatus() {
            const resultDiv = document.getElementById('cacheTestResult');
            resultDiv.innerHTML = '<p>🔄 Testing cache status...</p>';
            
            // Test with current timestamp to check if cache is working
            const testUrl = `/fit/public/invoices/create?cache_test=${Date.now()}`;
            
            fetch(testUrl)
                .then(response => {
                    if (response.ok) {
                        return response.text();
                    }
                    throw new Error(`HTTP ${response.status}`);
                })
                .then(html => {
                    // Check if the fixed JavaScript is present
                    const hasUniqueUrlParams = html.includes('urlParamsMain') && 
                                             html.includes('urlParams1') && 
                                             html.includes('urlParams2') && 
                                             html.includes('urlParamsDebug');
                    
                    const hasDebugSystem = html.includes('InvoiceDebugger') && 
                                          html.includes('showDebugConsole');
                    
                    if (hasUniqueUrlParams && hasDebugSystem) {
                        resultDiv.innerHTML = `
                            <div class="success" style="padding: 10px; border-radius: 5px;">
                                <strong>✅ Cache Status: CLEARED</strong><br>
                                Fixed JavaScript is now loaded!<br>
                                <small>• All URL parameter variables have unique names</small><br>
                                <small>• Debug system is present</small>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `
                            <div class="error" style="padding: 10px; border-radius: 5px;">
                                <strong>❌ Cache Status: STILL CACHED</strong><br>
                                Old JavaScript is still being loaded.<br>
                                <small>• Unique URL params: ${hasUniqueUrlParams ? 'Present' : 'Missing'}</small><br>
                                <small>• Debug system: ${hasDebugSystem ? 'Present' : 'Missing'}</small><br>
                                <br>
                                <em>Try a hard refresh (Ctrl+Shift+R) or clear cache manually.</em>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `
                        <div class="error" style="padding: 10px; border-radius: 5px;">
                            <strong>❌ Cache Test Failed</strong><br>
                            Error: ${error.message}
                        </div>
                    `;
                });
        }
    </script>
</body>
</html>