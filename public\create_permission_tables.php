<?php
// Start session
session_start();

// Check authentication
if (!isset($_SESSION['user_id'])) {
    die("Unauthorized access. Please login first.");
}

// Check if user is admin
$isAdmin = false;
if (isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin']) {
    $isAdmin = true;
} elseif (isset($_SESSION['user_groups'])) {
    if (is_array($_SESSION['user_groups'])) {
        foreach ($_SESSION['user_groups'] as $group) {
            if (strtolower($group['name']) === 'administrators') {
                $isAdmin = true;
                break;
            }
        }
    } elseif ($_SESSION['user_groups'] === 'Administrators') {
        $isAdmin = true;
    }
}

if (!$isAdmin) {
    die("Only administrators can run this script.");
}

// Load bootstrap
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Permission Tables - Fit360 AdminDesk</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .container { max-width: 800px; margin-top: 50px; }
        .card { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); }
        .log-output { background-color: #1e1e1e; color: #d4d4d4; padding: 15px; border-radius: 5px; font-family: monospace; font-size: 14px; max-height: 400px; overflow-y: auto; }
        .success { color: #4ec9b0; }
        .error { color: #f48771; }
        .info { color: #569cd6; }
        .warning { color: #dcdcaa; }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">Create Permission Management Tables</h4>
            </div>
            <div class="card-body">
                <p>This script will create the permission management system tables in your database.</p>
                
                <div class="log-output">
                    <?php
                    try {
                        echo '<span class="info">[INFO] Starting table creation...</span><br>';
                        
                        // Get database connection
                        $db = Flight::db();
                        echo '<span class="success">[✓] Database connection established</span><br>';
                        
                        // Read migration file
                        $migrationFile = __DIR__ . '/../database/migrations/200_create_permission_management_system.sql';
                        if (!file_exists($migrationFile)) {
                            throw new Exception("Migration file not found: $migrationFile");
                        }
                        
                        echo '<span class="info">[INFO] Reading migration file...</span><br>';
                        $sql = file_get_contents($migrationFile);
                        
                        // Remove comments and split into statements
                        $sql = preg_replace('/^--.*$/m', '', $sql);
                        $sql = preg_replace('/\/\*.*?\*\//s', '', $sql);
                        
                        // Split by semicolon but not inside quotes
                        $statements = [];
                        $current = '';
                        $inQuote = false;
                        $quoteChar = '';
                        
                        for ($i = 0; $i < strlen($sql); $i++) {
                            $char = $sql[$i];
                            
                            if (!$inQuote && ($char == '"' || $char == "'" || $char == '`')) {
                                $inQuote = true;
                                $quoteChar = $char;
                            } elseif ($inQuote && $char == $quoteChar && $sql[$i-1] != '\\') {
                                $inQuote = false;
                            }
                            
                            if (!$inQuote && $char == ';') {
                                if (trim($current)) {
                                    $statements[] = trim($current);
                                }
                                $current = '';
                            } else {
                                $current .= $char;
                            }
                        }
                        
                        if (trim($current)) {
                            $statements[] = trim($current);
                        }
                        
                        echo '<span class="info">[INFO] Found ' . count($statements) . ' SQL statements to execute</span><br><br>';
                        
                        $successCount = 0;
                        $skipCount = 0;
                        $errorCount = 0;
                        
                        foreach ($statements as $index => $statement) {
                            if (empty(trim($statement))) continue;
                            
                            // Extract table name if it's a CREATE TABLE statement
                            $tableName = '';
                            if (preg_match('/CREATE\s+TABLE\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?/i', $statement, $matches)) {
                                $tableName = $matches[1];
                            }
                            
                            try {
                                $db->exec($statement);
                                $successCount++;
                                
                                if ($tableName) {
                                    echo '<span class="success">[✓] Created table: ' . $tableName . '</span><br>';
                                } else {
                                    echo '<span class="success">[✓] Executed statement ' . ($index + 1) . '</span><br>';
                                }
                            } catch (PDOException $e) {
                                if (strpos($e->getMessage(), 'already exists') !== false) {
                                    $skipCount++;
                                    echo '<span class="warning">[!] Table already exists: ' . $tableName . '</span><br>';
                                } else {
                                    $errorCount++;
                                    echo '<span class="error">[✗] Error: ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                                }
                            }
                        }
                        
                        echo '<br><span class="info">=====================================</span><br>';
                        echo '<span class="success">✓ Successfully executed: ' . $successCount . ' statements</span><br>';
                        if ($skipCount > 0) {
                            echo '<span class="warning">! Skipped: ' . $skipCount . ' (already exist)</span><br>';
                        }
                        if ($errorCount > 0) {
                            echo '<span class="error">✗ Failed: ' . $errorCount . '</span><br>';
                        }
                        echo '<span class="info">=====================================</span><br>';
                        
                        // Check if all required tables exist
                        echo '<br><span class="info">[INFO] Verifying tables...</span><br>';
                        
                        $requiredTables = [
                            'system_modules' => 'System modules registry',
                            'module_permissions' => 'Module permissions',
                            'group_permissions' => 'Group permission assignments',
                            'permission_templates' => 'Permission templates',
                            'permission_audit_log' => 'Audit trail'
                        ];
                        
                        $allExist = true;
                        foreach ($requiredTables as $table => $description) {
                            $stmt = $db->query("SHOW TABLES LIKE '$table'");
                            if ($stmt->rowCount() > 0) {
                                echo '<span class="success">[✓] Table exists: ' . $table . ' - ' . $description . '</span><br>';
                            } else {
                                echo '<span class="error">[✗] Table missing: ' . $table . ' - ' . $description . '</span><br>';
                                $allExist = false;
                            }
                        }
                        
                        if ($allExist) {
                            echo '<br><span class="success"><strong>✅ All permission tables are ready!</strong></span><br>';
                            echo '<span class="info">You can now run the permission seeder to populate initial data.</span><br>';
                        } else {
                            echo '<br><span class="error"><strong>❌ Some tables are missing. Please check the errors above.</strong></span><br>';
                        }
                        
                    } catch (Exception $e) {
                        echo '<span class="error">[ERROR] ' . htmlspecialchars($e->getMessage()) . '</span><br>';
                        echo '<pre class="error">' . htmlspecialchars($e->getTraceAsString()) . '</pre>';
                    }
                    ?>
                </div>
                
                <div class="mt-4">
                    <a href="/fit/public/run-permission-seeder.php?key=fit360-permission-setup-2024" class="btn btn-success">
                        <i class="fas fa-seedling"></i> Run Permission Seeder
                    </a>
                    <a href="/fit/public/config" class="btn btn-primary">Back to Configuration</a>
                    <a href="/fit/public" class="btn btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>