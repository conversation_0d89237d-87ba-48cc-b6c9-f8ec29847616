<?php

require_once __DIR__ . '/../../app/config/bootstrap.php';

use App\Core\Database;

try {
    $db = Database::getInstance()->getConnection();
    
    echo "Creating payment reminder email templates...\n";
    
    // Payment Reminder Level 1 - Friendly
    $stmt = $db->prepare("
        INSERT INTO email_templates (
            name, code, subject, body_html, body_text, 
            category, variables, active, created_at
        ) VALUES (
            'Payment Reminder - First Notice',
            'payment_reminder_1',
            'Friendly Reminder: Invoice {{invoice_number}} Payment Due',
            '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">
                <h2 style=\"color: #2196F3;\">Payment Reminder</h2>
                
                <p>Dear {{client_name}},</p>
                
                <p>We hope this message finds you well. This is a friendly reminder that payment for the following invoice is now overdue:</p>
                
                <div style=\"background-color: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0;\">
                    <p><strong>Invoice Number:</strong> {{invoice_number}}</p>
                    <p><strong>Amount Due:</strong> ${{invoice_amount}}</p>
                    <p><strong>Original Due Date:</strong> {{due_date}}</p>
                    <p><strong>Days Overdue:</strong> {{days_overdue}}</p>
                </div>
                
                <p>We understand that oversights happen, and we appreciate your prompt attention to this matter. You can make your payment quickly and securely by clicking the button below:</p>
                
                <p style=\"text-align: center; margin: 30px 0;\">
                    <a href=\"{{payment_link}}\" style=\"background-color: #4CAF50; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;\">Pay Now</a>
                </p>
                
                <p>You can also view your invoice details here: <a href=\"{{invoice_link}}\">{{invoice_link}}</a></p>
                
                <p>If you have already sent payment, please disregard this notice. If you have any questions or concerns about this invoice, please don''t hesitate to contact us.</p>
                
                <p>Thank you for your business!</p>
                
                <p>Best regards,<br>
                {{company_name}}<br>
                {{company_email}}<br>
                {{company_phone}}</p>
            </div>',
            'Dear {{client_name}},

This is a friendly reminder that payment for invoice {{invoice_number}} in the amount of ${{invoice_amount}} is now {{days_overdue}} days overdue.

Invoice Details:
- Invoice Number: {{invoice_number}}
- Amount Due: ${{invoice_amount}}
- Original Due Date: {{due_date}}

You can pay online at: {{payment_link}}

If you have already sent payment, please disregard this notice. If you have any questions, please contact us.

Thank you for your prompt attention to this matter.

Best regards,
{{company_name}}',
            'reminders',
            'client_name,company,invoice_number,invoice_amount,due_date,days_overdue,payment_link,invoice_link,company_name,company_email,company_phone',
            1,
            NOW()
        )
    ");
    $stmt->execute();
    echo "✓ Created first payment reminder template\n";
    
    // Payment Reminder Level 2 - Firm
    $stmt = $db->prepare("
        INSERT INTO email_templates (
            name, code, subject, body_html, body_text, 
            category, variables, active, created_at
        ) VALUES (
            'Payment Reminder - Second Notice',
            'payment_reminder_2',
            'Second Notice: Invoice {{invoice_number}} Payment Overdue',
            '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">
                <h2 style=\"color: #FF9800;\">Second Payment Reminder</h2>
                
                <p>Dear {{client_name}},</p>
                
                <p>This is our second notice regarding the overdue payment for the invoice below. We have not yet received payment, and your account is now seriously past due.</p>
                
                <div style=\"background-color: #fff3e0; padding: 20px; border-left: 4px solid #FF9800; margin: 20px 0;\">
                    <p><strong>Invoice Number:</strong> {{invoice_number}}</p>
                    <p><strong>Amount Due:</strong> ${{invoice_amount}}</p>
                    <p><strong>Original Due Date:</strong> {{due_date}}</p>
                    <p><strong>Days Overdue:</strong> {{days_overdue}}</p>
                </div>
                
                <p><strong>Please give this matter your immediate attention.</strong> To avoid any disruption to your services or additional late fees, we request that you make payment as soon as possible.</p>
                
                <p style=\"text-align: center; margin: 30px 0;\">
                    <a href=\"{{payment_link}}\" style=\"background-color: #FF9800; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;\">Make Payment Now</a>
                </p>
                
                <p>If you are experiencing difficulties making this payment, please contact us immediately so we can discuss payment arrangements. We value your business and want to work with you to resolve this matter.</p>
                
                <p>If payment has already been sent, please contact us with the payment details so we can update our records.</p>
                
                <p>We look forward to your prompt response.</p>
                
                <p>Sincerely,<br>
                {{company_name}}<br>
                Accounts Receivable Department<br>
                {{company_email}}<br>
                {{company_phone}}</p>
            </div>',
            'Dear {{client_name}},

SECOND NOTICE - PAYMENT OVERDUE

This is our second notice regarding overdue payment for invoice {{invoice_number}}.

Amount Due: ${{invoice_amount}}
Days Overdue: {{days_overdue}}
Original Due Date: {{due_date}}

Your account is seriously past due. Please give this matter your immediate attention.

Pay online now at: {{payment_link}}

If you are experiencing difficulties, please contact us immediately to discuss payment arrangements. If payment has been sent, please contact us with details.

We require your urgent response to avoid further action.

{{company_name}}
{{company_email}}
{{company_phone}}',
            'reminders',
            'client_name,company,invoice_number,invoice_amount,due_date,days_overdue,payment_link,invoice_link,company_name,company_email,company_phone',
            1,
            NOW()
        )
    ");
    $stmt->execute();
    echo "✓ Created second payment reminder template\n";
    
    // Payment Reminder Level 3 - Urgent/Final
    $stmt = $db->prepare("
        INSERT INTO email_templates (
            name, code, subject, body_html, body_text, 
            category, variables, active, created_at
        ) VALUES (
            'Payment Reminder - Final Notice',
            'payment_reminder_3',
            'FINAL NOTICE: Invoice {{invoice_number}} - Immediate Action Required',
            '<div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">
                <div style=\"background-color: #f44336; color: white; padding: 20px; text-align: center;\">
                    <h2 style=\"margin: 0;\">FINAL NOTICE</h2>
                </div>
                
                <div style=\"padding: 20px;\">
                    <p>Dear {{client_name}},</p>
                    
                    <p><strong>This is your FINAL NOTICE regarding the seriously overdue payment for the invoice detailed below.</strong></p>
                    
                    <div style=\"background-color: #ffebee; padding: 20px; border-left: 4px solid #f44336; margin: 20px 0;\">
                        <p><strong>Invoice Number:</strong> {{invoice_number}}</p>
                        <p><strong>Amount Due:</strong> <span style=\"color: #f44336; font-size: 18px; font-weight: bold;\">${{invoice_amount}}</span></p>
                        <p><strong>Original Due Date:</strong> {{due_date}}</p>
                        <p><strong>Days Overdue:</strong> <span style=\"color: #f44336; font-weight: bold;\">{{days_overdue}} DAYS</span></p>
                    </div>
                    
                    <p style=\"background-color: #fffde7; padding: 15px; border: 1px solid #f9a825;\">
                        <strong>IMMEDIATE ACTION REQUIRED:</strong> Despite our previous reminders, we have not received payment for this invoice. This account is now severely delinquent.
                    </p>
                    
                    <p><strong>Unless payment is received within 5 business days of this notice, we will have no choice but to:</strong></p>
                    <ul>
                        <li>Suspend all services immediately</li>
                        <li>Forward this account to our collections department</li>
                        <li>Report this delinquency to credit agencies</li>
                        <li>Pursue all legal remedies available</li>
                    </ul>
                    
                    <p style=\"text-align: center; margin: 30px 0;\">
                        <a href=\"{{payment_link}}\" style=\"background-color: #f44336; color: white; padding: 15px 40px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold; font-size: 18px;\">PAY NOW TO AVOID FURTHER ACTION</a>
                    </p>
                    
                    <p><strong>This is your last opportunity to resolve this matter before we take further action.</strong> If you have already made payment, please contact us IMMEDIATELY with proof of payment.</p>
                    
                    <p>To make payment arrangements or if you have any questions, contact us immediately at {{company_phone}} or {{company_email}}.</p>
                    
                    <p>We regret that this action has become necessary and hope to resolve this matter immediately.</p>
                    
                    <p>{{company_name}}<br>
                    Collections Department<br>
                    {{company_email}}<br>
                    {{company_phone}}</p>
                </div>
            </div>',
            'FINAL NOTICE - IMMEDIATE ACTION REQUIRED

Dear {{client_name}},

This is your FINAL NOTICE for overdue invoice {{invoice_number}}.

AMOUNT DUE: ${{invoice_amount}}
DAYS OVERDUE: {{days_overdue}} DAYS

Despite previous reminders, payment has not been received. This account is SEVERELY DELINQUENT.

Unless payment is received within 5 business days, we will:
- Suspend all services
- Forward to collections
- Report to credit agencies
- Pursue legal remedies

PAY NOW: {{payment_link}}

This is your last opportunity to avoid further action. If payment has been made, contact us IMMEDIATELY.

{{company_name}}
Collections Department
{{company_email}}
{{company_phone}}',
            'reminders',
            'client_name,company,invoice_number,invoice_amount,due_date,days_overdue,payment_link,invoice_link,company_name,company_email,company_phone',
            1,
            NOW()
        )
    ");
    $stmt->execute();
    echo "✓ Created final payment reminder template\n";
    
    // Update reminder settings with template IDs
    $templates = [
        'payment_reminder_1' => 1,
        'payment_reminder_2' => 2,
        'payment_reminder_3' => 3
    ];
    
    foreach ($templates as $code => $level) {
        $stmt = $db->prepare("
            UPDATE reminder_settings rs
            JOIN email_templates et ON et.code = ?
            SET rs.template_id = et.id
            WHERE rs.reminder_level = ?
        ");
        $stmt->execute([$code, $level]);
    }
    echo "✓ Updated reminder settings with template IDs\n";
    
    echo "\n✅ Payment reminder email templates created successfully!\n";
    
} catch (Exception $e) {
    echo "\n❌ Error creating payment reminder templates: " . $e->getMessage() . "\n";
    exit(1);
}