<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/..');
$dotenv->load();

$host = $_ENV['DB_HOST'] ?? '127.0.0.1';
$dbname = $_ENV['DB_DATABASE'] ?? 'fitapp';
$username = $_ENV['DB_USERNAME'] ?? 'root';
$password = $_ENV['DB_PASSWORD'] ?? 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $userId = 8; // <PERSON><PERSON>
    
    // Get Coach group ID
    $stmt = $pdo->prepare("SELECT id FROM user_groups WHERE name = 'Coach'");
    $stmt->execute();
    $coachGroup = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$coachGroup) {
        echo "<p style='color: red;'>Coach group not found!</p>";
        exit;
    }
    
    $coachGroupId = $coachGroup['id'];
    
    // Check if user is already in Coach group
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count 
        FROM user_group_assignments 
        WHERE user_id = ? AND group_id = ?
    ");
    $stmt->execute([$userId, $coachGroupId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        echo "<p>User is already in the Coach group.</p>";
    } else {
        // Add user to Coach group
        $stmt = $pdo->prepare("
            INSERT INTO user_group_assignments (user_id, group_id, assigned_at, assigned_by)
            VALUES (?, ?, NOW(), 1)
        ");
        $stmt->execute([$userId, $coachGroupId]);
        
        echo "<p style='color: green;'>Successfully added user to Coach group!</p>";
    }
    
    // Show updated group assignments
    echo "<h3>Current Group Assignments for User ID $userId:</h3>";
    
    $stmt = $pdo->prepare("
        SELECT g.name, uga.assigned_at
        FROM user_group_assignments uga
        JOIN user_groups g ON uga.group_id = g.id
        WHERE uga.user_id = ?
        ORDER BY g.name
    ");
    $stmt->execute([$userId]);
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<ul>";
    foreach ($groups as $group) {
        echo "<li>" . $group['name'] . " (assigned: " . date('d/m/Y', strtotime($group['assigned_at'])) . ")</li>";
    }
    echo "</ul>";
    
    echo "<p><a href='/fit/public/users/8/edit'>Back to Edit User</a> | ";
    echo "<a href='/fit/public/check_user_8_retrocession.php'>Check Retrocession Settings</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}