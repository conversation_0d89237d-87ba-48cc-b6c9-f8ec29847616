/**
 * Date formatter for DD/MM/YYYY format
 */
const DateFormatter = {
    /**
     * Initialize date formatting for all date inputs on the page
     */
    init: function() {
        console.log('DateFormatter: Initializing date formatting...');
        
        // Find all date inputs
        const dateInputs = document.querySelectorAll('input[type="date"]');
        console.log('DateFormatter: Found', dateInputs.length, 'date inputs');
        
        dateInputs.forEach(input => {
            this.convertToTextInput(input);
        });
        
        // Also handle dynamically added inputs
        this.observeNewInputs();
    },
    
    /**
     * Convert a date input to text input with DD/MM/YYYY format
     */
    convertToTextInput: function(input) {
        // Get current value
        const currentValue = input.value;
        console.log('DateFormatter: Converting input', input.name, 'with value:', currentValue);
        
        // Change type to text
        input.type = 'text';
        input.placeholder = 'DD/MM/YYYY';
        input.maxLength = 10;
        
        // Add data attribute to identify formatted inputs
        input.dataset.dateFormatted = 'true';
        
        // Format existing value if present
        if (currentValue) {
            const formatted = this.formatDate(currentValue);
            input.value = formatted;
            console.log('DateFormatter: Formatted', currentValue, 'to', formatted);
        }
        
        // Add input mask
        this.addInputMask(input);
        
        // Add validation
        this.addValidation(input);
        
        // Handle form submission
        this.handleFormSubmission(input);
    },
    
    /**
     * Format date from YYYY-MM-DD to DD/MM/YYYY
     */
    formatDate: function(dateString) {
        if (!dateString) return '';
        
        const parts = dateString.split('-');
        if (parts.length !== 3) return dateString;
        
        return `${parts[2]}/${parts[1]}/${parts[0]}`;
    },
    
    /**
     * Parse date from DD/MM/YYYY to YYYY-MM-DD
     */
    parseDate: function(dateString) {
        if (!dateString) return '';
        
        const parts = dateString.split('/');
        if (parts.length !== 3) return dateString;
        
        // Validate parts
        const day = parts[0].padStart(2, '0');
        const month = parts[1].padStart(2, '0');
        const year = parts[2];
        
        if (year.length !== 4) return '';
        
        return `${year}-${month}-${day}`;
    },
    
    /**
     * Add input mask for DD/MM/YYYY format
     */
    addInputMask: function(input) {
        input.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            let formattedValue = '';
            
            if (value.length >= 1) {
                formattedValue = value.substring(0, 2);
            }
            if (value.length >= 3) {
                formattedValue += '/' + value.substring(2, 4);
            }
            if (value.length >= 5) {
                formattedValue += '/' + value.substring(4, 8);
            }
            
            e.target.value = formattedValue;
        });
        
        // Handle backspace for better UX
        input.addEventListener('keydown', function(e) {
            if (e.key === 'Backspace') {
                const value = e.target.value;
                if (value.endsWith('/')) {
                    e.target.value = value.slice(0, -1);
                    e.preventDefault();
                }
            }
        });
    },
    
    /**
     * Add validation for DD/MM/YYYY format
     */
    addValidation: function(input) {
        input.addEventListener('blur', function(e) {
            const value = e.target.value;
            if (!value) return;
            
            const regex = /^(\d{2})\/(\d{2})\/(\d{4})$/;
            const match = value.match(regex);
            
            if (!match) {
                input.setCustomValidity('Please enter date in DD/MM/YYYY format');
                return;
            }
            
            const day = parseInt(match[1]);
            const month = parseInt(match[2]);
            const year = parseInt(match[3]);
            
            // Validate date components
            if (month < 1 || month > 12) {
                input.setCustomValidity('Invalid month');
                return;
            }
            
            const daysInMonth = new Date(year, month, 0).getDate();
            if (day < 1 || day > daysInMonth) {
                input.setCustomValidity('Invalid day for the given month');
                return;
            }
            
            // Clear any custom validity
            input.setCustomValidity('');
        });
    },
    
    /**
     * Handle form submission to convert dates back to YYYY-MM-DD
     */
    handleFormSubmission: function(input) {
        const form = input.closest('form');
        if (!form || form.dataset.dateFormatterAttached) return;
        
        form.dataset.dateFormatterAttached = 'true';
        
        form.addEventListener('submit', (e) => {
            // Find all formatted date inputs
            const formattedInputs = form.querySelectorAll('[data-date-formatted="true"]');
            
            formattedInputs.forEach(formattedInput => {
                // Create hidden input with original name
                const hiddenInput = document.createElement('input');
                hiddenInput.type = 'hidden';
                hiddenInput.name = formattedInput.name;
                hiddenInput.value = this.parseDate(formattedInput.value);
                
                // Temporarily change the visible input name
                formattedInput.name = formattedInput.name + '_display';
                
                // Add hidden input to form
                form.appendChild(hiddenInput);
            });
        });
    },
    
    /**
     * Observe for dynamically added date inputs
     */
    observeNewInputs: function() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // Check if it's a date input
                        if (node.tagName === 'INPUT' && node.type === 'date') {
                            this.convertToTextInput(node);
                        }
                        // Check for date inputs within added element
                        const dateInputs = node.querySelectorAll?.('input[type="date"]');
                        if (dateInputs) {
                            dateInputs.forEach(input => this.convertToTextInput(input));
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
};

// Initialize on DOM ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        // Small delay to ensure other scripts have initialized
        setTimeout(() => DateFormatter.init(), 100);
    });
} else {
    // Small delay to ensure other scripts have initialized
    setTimeout(() => DateFormatter.init(), 100);
}