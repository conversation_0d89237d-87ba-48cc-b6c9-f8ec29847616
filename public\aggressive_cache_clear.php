<?php
/**
 * Aggressive cache clear - modifies the template to force browser reload
 */

// Prevent any caching
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Pragma: no-cache");
header("Expires: 0");

// Clear PHP caches
if (function_exists('opcache_reset')) {
    opcache_reset();
}
if (function_exists('apcu_clear_cache')) {
    apcu_clear_cache();
}

// Clear Twig cache
$twigCacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($twigCacheDir)) {
    system("rm -rf " . escapeshellarg($twigCacheDir) . "/*");
}

// The main template file
$templateFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';

// Add a cache-busting comment to force recompilation
$content = file_get_contents($templateFile);
$timestamp = time();
$marker = "{# CACHE_BUST_" . $timestamp . " #}";

// Remove any existing cache bust markers
$content = preg_replace('/{# CACHE_BUST_\d+ #}/', '', $content);

// Add new marker at the beginning
$content = $marker . "\n" . $content;

// Also modify the JavaScript slightly to force reload
// Add a comment with timestamp after the problematic line 3638
$lines = explode("\n", $content);
if (isset($lines[3637])) { // Line 3638 in 1-based indexing
    $lines[3637] .= " // CB_" . $timestamp;
}
$content = implode("\n", $lines);

// Save the modified file
file_put_contents($templateFile, $content);

// Create a special version of the invoice creation page that forces reload
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Force Reload Invoice Page</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            color: #856404;
            background-color: #fff3cd;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            padding: 15px 30px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            margin: 10px;
        }
        .button:hover {
            background: #218838;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Aggressive Cache Clear Complete</h1>
        
        <div class="success">
            <p><strong>✅ All server caches cleared</strong></p>
            <p>✅ Template file modified with cache buster</p>
            <p>✅ Timestamp added: <?php echo $timestamp; ?></p>
        </div>
        
        <div class="warning">
            <h3>⚠️ Browser Cache Must Be Cleared</h3>
            <p>Your browser is still caching the old file. You MUST do one of these:</p>
            
            <ol style="text-align: left; display: inline-block;">
                <li><strong>Best Option:</strong> Open Developer Tools (F12) → Network tab → Check "Disable cache" → Keep DevTools open</li>
                <li><strong>Alternative:</strong> Use a completely different browser you haven't used before</li>
                <li><strong>Last Resort:</strong> Clear ALL browser data for this site</li>
            </ol>
        </div>
        
        <?php
        // Generate a unique URL that includes multiple cache busters
        $invoiceUrl = "/fit/public/invoices/create?_cb=" . $timestamp . "&v=" . $timestamp . "&nocache=1&t=" . microtime(true);
        ?>
        
        <p>
            <a href="<?php echo $invoiceUrl; ?>" class="button" onclick="forceReload(event)">
                Open Invoice Page
            </a>
        </p>
        
        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <p><strong>If you still see the error:</strong></p>
            <p>The JavaScript file is being cached by your browser at a very low level.</p>
            <p>Try this sequence:</p>
            <ol style="text-align: left; display: inline-block;">
                <li>Close this tab</li>
                <li>Open a new incognito/private window</li>
                <li>Go directly to: <code>http://localhost/fit/public/invoices/create?_t=<?php echo $timestamp; ?></code></li>
                <li>Immediately press <code>Ctrl+F5</code> when the page loads</li>
            </ol>
        </div>
    </div>
    
    <script>
    function forceReload(e) {
        e.preventDefault();
        const url = e.target.href;
        
        // Try to clear any service workers
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.getRegistrations().then(function(registrations) {
                for(let registration of registrations) {
                    registration.unregister();
                }
            });
        }
        
        // Clear caches if available
        if ('caches' in window) {
            caches.keys().then(function(names) {
                for (let name of names) {
                    caches.delete(name);
                }
            });
        }
        
        // Force reload with cache bypass
        window.location.href = url + '&_reload=' + Date.now();
    }
    
    // Auto-redirect after 3 seconds
    setTimeout(function() {
        document.querySelector('.button').click();
    }, 3000);
    </script>
</body>
</html>