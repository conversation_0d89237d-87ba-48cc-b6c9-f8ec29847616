<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
button { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; }
.btn-primary { background: #007bff; color: white; }
</style>';

echo "<h1>Run Invoice Type Prefix Migration</h1>";

// Check if column exists
echo '<div class="section">';
echo '<h2>1. Checking Database Structure</h2>';
try {
    $stmt = $db->query("SHOW COLUMNS FROM config_invoice_types LIKE 'prefix'");
    $column = $stmt->fetch();
    
    if ($column) {
        echo '<div class="success">✓ Prefix column already exists</div>';
        $columnExists = true;
    } else {
        echo '<div class="warning">✗ Prefix column does not exist</div>';
        $columnExists = false;
    }
} catch (Exception $e) {
    echo '<div class="error">Error checking column: ' . $e->getMessage() . '</div>';
    $columnExists = false;
}
echo '</div>';

// Run migration if requested
if (isset($_POST['run_migration'])) {
    echo '<div class="section">';
    echo '<h2>2. Running Migration</h2>';
    
    try {
        // Add column if it doesn't exist (DDL statements can't be in transactions in MySQL)
        if (!$columnExists) {
            try {
                $db->exec("ALTER TABLE config_invoice_types ADD COLUMN prefix VARCHAR(10) NULL AFTER code");
                echo '<div class="success">✓ Added prefix column</div>';
            } catch (Exception $e) {
                // Column might already exist
                echo '<div class="warning">Could not add column (may already exist): ' . $e->getMessage() . '</div>';
            }
        }
        
        // Start transaction for updates only
        $db->beginTransaction();
        
        // Update prefixes
        $updates = [
            ['code' => 'rental', 'prefix' => 'LOY'],
            ['code' => 'hourly', 'prefix' => 'HOR'],
            ['code' => 'service', 'prefix' => 'SER'],
            ['code' => 'retrocession_30', 'prefix' => 'RET30'],
            ['code' => 'retrocession_25', 'prefix' => 'RET25'],
            ['code' => 'medical', 'prefix' => 'MED'],
            ['code' => 'credit_note', 'prefix' => 'CN'],
            ['code' => 'quote', 'prefix' => 'DEV'],
            ['code' => 'receipt', 'prefix' => 'REC'],
            ['code' => 'other', 'prefix' => 'DIV']
        ];
        
        foreach ($updates as $update) {
            $stmt = $db->prepare("UPDATE config_invoice_types SET prefix = ? WHERE code = ? AND (prefix IS NULL OR prefix = '')");
            $stmt->execute([$update['prefix'], $update['code']]);
            if ($stmt->rowCount() > 0) {
                echo '<div class="success">✓ Updated ' . $update['code'] . ' prefix to ' . $update['prefix'] . '</div>';
            }
        }
        
        // Update any remaining without prefix
        $db->exec("UPDATE config_invoice_types SET prefix = UPPER(LEFT(code, 3)) WHERE prefix IS NULL OR prefix = ''");
        
        $db->commit();
        echo '<div class="success"><strong>✓ Migration completed successfully!</strong></div>';
        
    } catch (Exception $e) {
        $db->rollBack();
        echo '<div class="error">Error during migration: ' . $e->getMessage() . '</div>';
    }
    echo '</div>';
}

// Show current state
echo '<div class="section">';
echo '<h2>Current Invoice Types</h2>';
try {
    $stmt = $db->query("SELECT id, name, code, prefix FROM config_invoice_types ORDER BY id");
    $types = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo '<table style="border-collapse: collapse; width: 100%;">';
    echo '<tr><th style="border: 1px solid #ddd; padding: 8px;">ID</th>';
    echo '<th style="border: 1px solid #ddd; padding: 8px;">Name</th>';
    echo '<th style="border: 1px solid #ddd; padding: 8px;">Code</th>';
    echo '<th style="border: 1px solid #ddd; padding: 8px;">Prefix</th></tr>';
    
    foreach ($types as $type) {
        $name = json_decode($type['name'], true);
        $displayName = is_array($name) ? ($name['fr'] ?? $name['en'] ?? $type['name']) : $type['name'];
        
        echo '<tr>';
        echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $type['id'] . '</td>';
        echo '<td style="border: 1px solid #ddd; padding: 8px;">' . htmlspecialchars($displayName) . '</td>';
        echo '<td style="border: 1px solid #ddd; padding: 8px;">' . $type['code'] . '</td>';
        echo '<td style="border: 1px solid #ddd; padding: 8px;"><strong>' . ($type['prefix'] ?? 'NULL') . '</strong></td>';
        echo '</tr>';
    }
    echo '</table>';
} catch (Exception $e) {
    echo '<div class="error">Error loading invoice types: ' . $e->getMessage() . '</div>';
}
echo '</div>';

// Migration button
if (!isset($_POST['run_migration'])) {
    echo '<div class="section">';
    echo '<form method="post">';
    echo '<button type="submit" name="run_migration" value="1" class="btn-primary">Run Migration</button>';
    echo '</form>';
    echo '</div>';
}

echo '<div class="section">';
echo '<h2>Next Steps</h2>';
echo '<ol>';
echo '<li>Run the migration using the button above</li>';
echo '<li>Go to <a href="/fit/public/update_invoice_type_prefixes.php">Update Invoice Type Prefixes</a> to modify prefixes if needed</li>';
echo '<li>Test with <a href="/fit/public/test_new_invoice_numbering.php">Test New Invoice Numbering</a></li>';
echo '<li>Update existing invoices to use the new format</li>';
echo '</ol>';
echo '</div>';