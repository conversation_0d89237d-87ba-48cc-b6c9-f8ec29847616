<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CHECKING WHY SEQUENCE IS AT 191 ===\n\n";
    
    // Check current sequences
    echo "1. Current document sequences:\n";
    $stmt = $pdo->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 
        ORDER BY year DESC, month DESC
    ");
    $sequences = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo "- Year: {$seq['year']}, Month: " . ($seq['month'] ?? 'N/A') . 
             ", Last Number: {$seq['last_number']}, Updated: {$seq['updated_at']}\n";
    }
    
    // Check actual invoices
    echo "\n2. Actual invoices in database:\n";
    $stmt = $pdo->query("
        SELECT invoice_number, created_at 
        FROM invoices 
        WHERE invoice_number LIKE 'FAC-2025-%' 
        ORDER BY id DESC 
        LIMIT 10
    ");
    $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($invoices) > 0) {
        foreach ($invoices as $inv) {
            echo "- {$inv['invoice_number']} (Created: {$inv['created_at']})\n";
        }
    } else {
        echo "No invoices found with FAC-2025-* pattern\n";
    }
    
    // Check for gaps
    echo "\n3. Checking for sequence gaps:\n";
    $stmt = $pdo->query("
        SELECT last_number FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025 AND month IS NULL
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $currentLastNumber = $result ? $result['last_number'] : 0;
    
    echo "Current sequence last_number: {$currentLastNumber}\n";
    echo "Next number would be: FAC-2025-" . str_pad($currentLastNumber + 1, 4, '0', STR_PAD_LEFT) . "\n";
    
    // Check recent activity
    echo "\n4. Recent sequence updates (last 10):\n";
    $stmt = $pdo->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 
        ORDER BY updated_at DESC 
        LIMIT 10
    ");
    $updates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($updates as $update) {
        echo "- Updated: {$update['updated_at']}, Year: {$update['year']}, " .
             "Month: " . ($update['month'] ?? 'N/A') . ", Last: {$update['last_number']}\n";
    }
    
    echo "\n5. EXPLANATION:\n";
    echo "The sequence increments every time the 'generate number' endpoint is called.\n";
    echo "This happens when:\n";
    echo "- You click the generate button\n";
    echo "- The form loads and auto-generates a number\n";
    echo "- You change document type\n";
    echo "- Failed form submissions that still triggered number generation\n";
    
    echo "\n6. To fix and set it back to 185 (for next number 0186):\n";
    echo "Run the fix_invoice_sequence_186.php script again.\n";
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}