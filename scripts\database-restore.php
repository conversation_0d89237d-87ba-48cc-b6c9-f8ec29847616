<?php
/**
 * Fit360 AdminDesk - Database Restore Script
 * 
 * This script restores the MySQL database from a backup file.
 * 
 * Usage:
 * php scripts/database-restore.php <backup-file> [--force]
 */

require_once __DIR__ . '/../vendor/autoload.php';

class DatabaseRestore
{
    private $config;
    
    public function __construct()
    {
        $this->loadConfig();
    }
    
    private function loadConfig()
    {
        // Load environment variables
        $envFile = __DIR__ . '/../.env';
        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
                    list($key, $value) = explode('=', $line, 2);
                    $_ENV[trim($key)] = trim($value);
                }
            }
        }
        
        $this->config = [
            'host' => $_ENV['DB_HOST'] ?? '127.0.0.1',
            'port' => $_ENV['DB_PORT'] ?? '3306',
            'database' => $_ENV['DB_DATABASE'] ?? 'fitapp',
            'username' => $_ENV['DB_USERNAME'] ?? 'root',
            'password' => $_ENV['DB_PASSWORD'] ?? '',
        ];
    }

    private function findMysql()
    {
        // Common paths for mysql
        $possiblePaths = [
            'mysql', // System PATH
            'C:\wamp64\bin\mysql\mysql8.3.0\bin\mysql.exe', // WAMP
            'C:\xampp\mysql\bin\mysql.exe', // XAMPP
            'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe', // MySQL Server
            '/usr/bin/mysql', // Linux
            '/usr/local/bin/mysql', // macOS Homebrew
        ];

        foreach ($possiblePaths as $path) {
            if ($this->commandExists($path)) {
                return escapeshellarg($path);
            }
        }

        throw new Exception("mysql not found. Please install MySQL client tools or add them to your PATH.");
    }

    private function commandExists($command)
    {
        // Remove quotes if present
        $command = trim($command, '"\'');

        // On Windows, check if file exists directly
        if (PHP_OS_FAMILY === 'Windows') {
            return file_exists($command);
        }

        // On Unix-like systems, use which command
        $output = shell_exec("which $command 2>/dev/null");
        return !empty($output);
    }

    private function findMysqlDump()
    {
        // Common paths for mysqldump
        $possiblePaths = [
            'mysqldump', // System PATH
            'C:\wamp64\bin\mysql\mysql8.3.0\bin\mysqldump.exe', // WAMP
            'C:\xampp\mysql\bin\mysqldump.exe', // XAMPP
            'C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe', // MySQL Server
            '/usr/bin/mysqldump', // Linux
            '/usr/local/bin/mysqldump', // macOS Homebrew
        ];

        foreach ($possiblePaths as $path) {
            if ($this->commandExists($path)) {
                return escapeshellarg($path);
            }
        }

        throw new Exception("mysqldump not found. Please install MySQL client tools or add them to your PATH.");
    }

    public function listAvailableBackups()
    {
        $backupDir = 'storage/backups';
        $files = glob($backupDir . '/fitapp_backup_*.sql*');
        
        if (empty($files)) {
            echo "No backup files found in {$backupDir}\n";
            return [];
        }
        
        // Sort by modification time (newest first)
        usort($files, function($a, $b) {
            return filemtime($b) - filemtime($a);
        });
        
        echo "Available backup files:\n";
        foreach ($files as $index => $file) {
            $size = $this->formatBytes(filesize($file));
            $date = date('Y-m-d H:i:s', filemtime($file));
            echo sprintf("  %d. %s (%s) - %s\n", $index + 1, basename($file), $size, $date);
        }
        
        return $files;
    }
    
    public function restoreFromBackup($backupFile, $force = false)
    {
        if (!file_exists($backupFile)) {
            throw new Exception("Backup file not found: {$backupFile}");
        }
        
        echo "Preparing to restore database from: " . basename($backupFile) . "\n";
        echo "Database: {$this->config['database']}\n";
        echo "Host: {$this->config['host']}:{$this->config['port']}\n";
        
        if (!$force) {
            echo "\nWARNING: This will completely replace the current database!\n";
            echo "Type 'yes' to continue: ";
            $confirmation = trim(fgets(STDIN));
            if (strtolower($confirmation) !== 'yes') {
                echo "Restore cancelled.\n";
                return false;
            }
        }
        
        // Test database connection
        $this->testConnection();
        
        // Create backup of current database before restore
        if (!$force) {
            $this->createPreRestoreBackup();
        }
        
        // Perform restore
        $this->performRestore($backupFile);
        
        echo "Database restore completed successfully!\n";
        return true;
    }
    
    private function testConnection()
    {
        echo "Testing database connection...\n";
        
        $dsn = sprintf(
            'mysql:host=%s;port=%s;dbname=%s',
            $this->config['host'],
            $this->config['port'],
            $this->config['database']
        );
        
        try {
            $pdo = new PDO($dsn, $this->config['username'], $this->config['password']);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            echo "Database connection successful.\n";
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    private function createPreRestoreBackup()
    {
        echo "Creating backup of current database before restore...\n";

        // Find mysqldump executable
        $mysqldump = $this->findMysqlDump();

        $timestamp = date('Y-m-d_H-i-s');
        $filename = "fitapp_pre_restore_backup_{$timestamp}.sql";
        $filepath = "storage/backups/{$filename}";

        $command = sprintf(
            '%s --host=%s --port=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            $mysqldump,
            escapeshellarg($this->config['host']),
            escapeshellarg($this->config['port']),
            escapeshellarg($this->config['username']),
            escapeshellarg($this->config['password']),
            escapeshellarg($this->config['database']),
            escapeshellarg($filepath)
        );
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Pre-restore backup failed with return code: {$returnCode}");
        }
        
        echo "Pre-restore backup created: {$filepath}\n";
    }
    
    private function performRestore($backupFile)
    {
        echo "Restoring database...\n";

        // Find mysql executable
        $mysql = $this->findMysql();

        // Check if file is compressed
        $isCompressed = str_ends_with($backupFile, '.gz');

        if ($isCompressed) {
            // Decompress and pipe to mysql
            $command = sprintf(
                'gunzip -c %s | %s --host=%s --port=%s --user=%s --password=%s %s',
                escapeshellarg($backupFile),
                $mysql,
                escapeshellarg($this->config['host']),
                escapeshellarg($this->config['port']),
                escapeshellarg($this->config['username']),
                escapeshellarg($this->config['password']),
                escapeshellarg($this->config['database'])
            );
        } else {
            // Direct restore from SQL file
            $command = sprintf(
                '%s --host=%s --port=%s --user=%s --password=%s %s < %s',
                $mysql,
                escapeshellarg($this->config['host']),
                escapeshellarg($this->config['port']),
                escapeshellarg($this->config['username']),
                escapeshellarg($this->config['password']),
                escapeshellarg($this->config['database']),
                escapeshellarg($backupFile)
            );
        }
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new Exception("Database restore failed with return code: {$returnCode}");
        }
        
        echo "Database restore completed.\n";
    }
    
    private function formatBytes($size, $precision = 2)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        return round($size, $precision) . ' ' . $units[$i];
    }
}

// Command line interface
function showUsage()
{
    echo "Usage: php scripts/database-restore.php [backup-file] [options]\n";
    echo "Options:\n";
    echo "  --force             Skip confirmation prompts\n";
    echo "  --list              List available backup files\n";
    echo "  --help              Show this help message\n";
    echo "\n";
    echo "Examples:\n";
    echo "  php scripts/database-restore.php --list\n";
    echo "  php scripts/database-restore.php storage/backups/fitapp_backup_2024-01-15_10-30-00.sql.gz\n";
    echo "  php scripts/database-restore.php storage/backups/fitapp_backup_2024-01-15_10-30-00.sql --force\n";
}

// Parse command line arguments
$backupFile = null;
$force = false;
$listBackups = false;
$showHelp = false;

for ($i = 1; $i < $argc; $i++) {
    $arg = $argv[$i];
    
    if ($arg === '--help') {
        $showHelp = true;
    } elseif ($arg === '--list') {
        $listBackups = true;
    } elseif ($arg === '--force') {
        $force = true;
    } elseif (!str_starts_with($arg, '--')) {
        $backupFile = $arg;
    }
}

// Execute based on arguments
try {
    if ($showHelp) {
        showUsage();
        exit(0);
    }
    
    $restore = new DatabaseRestore();
    
    if ($listBackups) {
        $restore->listAvailableBackups();
        exit(0);
    }
    
    if (!$backupFile) {
        echo "Error: No backup file specified.\n\n";
        showUsage();
        exit(1);
    }
    
    $restore->restoreFromBackup($backupFile, $force);
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
