<?php

namespace App\Controllers;

use Flight;
use App\Core\Controller;
use App\Core\Request;
use App\Core\Response;
use App\Models\SystemModule;
use App\Models\GroupPermission;
use App\Models\PermissionTemplate;
use App\Models\UserGroup;

class PermissionController extends Controller
{
    private $systemModule;
    private $groupPermission;
    private $permissionTemplate;
    private $db;
    
    public function __construct()
    {
        // Check if user is authenticated
        if (!isset($_SESSION['user_id'])) {
            // For AJAX requests, return JSON error instead of redirect
            $isAjax = $this->isAjax() || 
                      (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') ||
                      (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
            
            if ($isAjax) {
                $this->json(['error' => 'Authentication required'], 401);
                exit;
            }
            $this->redirect('/login');
            exit;
        }
        
        // Check if user has permission to manage permissions
        $systemModule = new SystemModule();
        if (!$systemModule->userHasPermission($_SESSION['user_id'], 'permissions', 'manage')) {
            // For AJAX requests, return JSON error instead of redirect
            $isAjax = $this->isAjax() || 
                      (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') ||
                      (isset($_SERVER['HTTP_ACCEPT']) && strpos($_SERVER['HTTP_ACCEPT'], 'application/json') !== false);
            
            if ($isAjax) {
                $this->json(['error' => __('messages.no_permission')], 403);
                exit;
            }
            $this->flash('error', __('messages.no_permission'));
            $this->redirect('/');
            exit;
        }
        
        $this->systemModule = $systemModule;
        $this->groupPermission = new GroupPermission();
        $this->permissionTemplate = new PermissionTemplate();
        $this->db = Flight::db();
    }
    
    /**
     * Display permission management UI
     */
    public function index()
    {
        try {
            // Get all user groups
            $groups = UserGroup::getAllWithCounts();
            
            // Get modules from permissions table categories (OLD schema)
            $modules = $this->getModulesFromPermissions();
            
            // Get permission templates
            $templates = $this->permissionTemplate->getAll();
            
            // Get template preference
            $template = $this->getTemplate();
            $viewName = 'permissions/index-modern';
            
            return $this->render($viewName, [
                'title' => __('permissions.title'),
                'groups' => $groups,
                'modules' => $modules,
                'templates' => $templates,
                'activeTab' => Flight::request()->query->activeTab ?? 'permissions'
            ]);
            
        } catch (\Exception $e) {
            error_log('PermissionController::index error: ' . $e->getMessage());
            $this->flash('error', __('messages.error_loading_permissions'));
            return $this->redirect('/');
        }
    }
    
    /**
     * AJAX endpoint to get permissions for a group
     */
    public function getGroupPermissions()
    {
        try {
            // Verify CSRF token for AJAX requests
            $csrfToken = Flight::request()->headers['X-CSRF-Token'] ?? '';
            if (!$this->validateCsrfToken($csrfToken)) {
                return $this->json(['error' => __('messages.invalid_csrf_token')], 403);
            }
            
            $groupId = Flight::request()->query->group_id;
            if (!$groupId) {
                return $this->json(['error' => __('messages.group_id_required')], 400);
            }
            
            // Get permissions organized by module (category) for OLD schema
            $permissions = $this->getGroupPermissionsForOldSchema($groupId);
            
            // Get group info
            $group = UserGroup::find($groupId);
            
            return $this->json([
                'success' => true,
                'group' => $group,
                'permissions' => $permissions
            ]);
            
        } catch (\Exception $e) {
            error_log('PermissionController::getGroupPermissions error: ' . $e->getMessage());
            return $this->json(['error' => __('messages.error_loading_permissions')], 500);
        }
    }
    
    /**
     * Save permission changes
     */
    public function updatePermissions()
    {
        try {
            // Verify CSRF token
            $csrfToken = Flight::request()->data->csrf_token ?? 
                        Flight::request()->header('X-CSRF-Token') ?? '';
            if (!$this->validateCsrfToken($csrfToken)) {
                return $this->json(['error' => __('messages.invalid_csrf_token')], 403);
            }
            
            // Get JSON data from request body
            $contentType = Flight::request()->headers['Content-Type'] ?? '';
            if (strpos($contentType, 'application/json') !== false) {
                $rawData = Flight::request()->getBody();
                $data = json_decode($rawData, true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    return $this->json(['error' => 'Invalid JSON data'], 400);
                }
            } else {
                $data = $this->getRequestData();
            }
            
            $groupId = $data['group_id'] ?? null;
            $permissions = $data['permissions'] ?? [];
            
            error_log('UpdatePermissions - GroupId: ' . $groupId);
            error_log('UpdatePermissions - Permissions: ' . json_encode($permissions));
            
            if (!$groupId) {
                return $this->json(['error' => __('messages.group_id_required')], 400);
            }
            
            // For OLD schema - permissions come as {category: ['view', 'edit', ...]}
            // We need to convert this to permission IDs
            $this->db->beginTransaction();
            
            try {
                // First, remove all existing permissions for this group
                $deleteStmt = $this->db->prepare("DELETE FROM group_permissions WHERE group_id = ?");
                $deleteStmt->execute([$groupId]);
                
                // Insert new permissions
                $insertStmt = $this->db->prepare("
                    INSERT INTO group_permissions (group_id, permission_id, granted_at, granted_by)
                    SELECT ?, p.id, NOW(), ?
                    FROM permissions p
                    WHERE p.category = ? AND p.code = ?
                ");
                
                $grantedCount = 0;
                foreach ($permissions as $category => $actions) {
                    if (!is_array($actions)) continue;
                    
                    foreach ($actions as $action) {
                        // Build permission code as category.action
                        $permissionCode = $category . '.' . $action;
                        $insertStmt->execute([
                            $groupId,
                            $_SESSION['user_id'],
                            $category,
                            $permissionCode
                        ]);
                        $grantedCount += $insertStmt->rowCount();
                    }
                }
                
                $this->db->commit();
                
                error_log('UpdatePermissions - Success, granted: ' . $grantedCount);
                
                // Ensure clean JSON output
                if (ob_get_level()) {
                    ob_clean();
                }
                
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => __('messages.permissions_updated'),
                    'granted' => $grantedCount
                ]);
                exit;
                
            } catch (\Exception $e) {
                $this->db->rollBack();
                error_log('UpdatePermissions - Transaction error: ' . $e->getMessage());
                throw $e;
            }
            
        } catch (\Exception $e) {
            error_log('PermissionController::updatePermissions error: ' . $e->getMessage());
            
            // Ensure clean JSON output
            if (ob_get_level()) {
                ob_clean();
            }
            
            header('Content-Type: application/json');
            http_response_code(500);
            echo json_encode(['error' => __('messages.error_updating_permissions') . ' - ' . $e->getMessage()]);
            exit;
        }
    }
    
    /**
     * Copy permissions from one group to another
     */
    public function copyPermissions()
    {
        try {
            // Verify CSRF token
            $csrfToken = Flight::request()->data->csrf_token ?? '';
            if (!$this->validateCsrfToken($csrfToken)) {
                return $this->json(['error' => __('messages.invalid_csrf_token')], 403);
            }
            
            $data = $this->getRequestData();
            $sourceGroupId = $data['source_group_id'] ?? null;
            $targetGroupId = $data['target_group_id'] ?? null;
            
            if (!$sourceGroupId || !$targetGroupId) {
                return $this->json(['error' => __('messages.source_target_required')], 400);
            }
            
            if ($sourceGroupId == $targetGroupId) {
                return $this->json(['error' => __('messages.same_group_error')], 400);
            }
            
            // Get permissions from source group
            $sourcePermissions = $this->groupPermission->getGroupPermissions($sourceGroupId);
            
            // Format permissions for target group
            $targetPermissions = [];
            foreach ($sourcePermissions as $perm) {
                if ($perm['is_granted']) {
                    $targetPermissions[] = [
                        'module_id' => $perm['module_id'],
                        'permission_code' => $perm['permission_code'],
                        'is_granted' => true
                    ];
                }
            }
            
            // Apply to target group
            $this->groupPermission->updateMultiplePermissions($targetGroupId, $targetPermissions, $_SESSION['user_id']);
            
            return $this->json([
                'success' => true,
                'message' => __('messages.permissions_copied')
            ]);
            
        } catch (\Exception $e) {
            error_log('PermissionController::copyPermissions error: ' . $e->getMessage());
            return $this->json(['error' => __('messages.error_copying_permissions')], 500);
        }
    }
    
    /**
     * Get permission template data for AJAX
     */
    public function getTemplateData()
    {
        try {
            $templateName = Flight::request()->query->name;
            if (!$templateName) {
                return $this->json(['error' => 'Template name required'], 400);
            }
            
            // Define templates with permissions for fitness center
            $templates = [
                'admin' => [
                    'name' => 'Administrator',
                    'permissions' => [
                        'invoices' => ['view', 'create', 'edit', 'delete', 'bulk_generate', 'send_email', 'export', 'void'],
                        'clients' => ['view', 'create', 'edit', 'delete', 'export', 'import', 'merge'],
                        'products' => ['view', 'create', 'edit', 'delete', 'manage_stock', 'import', 'bulk_update'],
                        'reports' => ['view', 'view_financial', 'view_inventory', 'view_analytics', 'export', 'schedule'],
                        'config' => ['view', 'edit', 'manage_company', 'manage_system', 'manage_templates', 'manage_integrations'],
                        'users' => ['view', 'create', 'edit', 'delete', 'manage_groups', 'manage_permissions', 'reset_passwords', 'view_activity'],
                        'stock' => ['view', 'create', 'edit', 'delete', 'adjust_inventory', 'transfer', 'audit'],
                        'documents' => ['view', 'create', 'edit', 'delete', 'share', 'version_control', 'bulk_upload']
                    ]
                ],
                'manager' => [
                    'name' => 'Manager',
                    'permissions' => [
                        'invoices' => ['view', 'create', 'edit', 'bulk_generate', 'send_email', 'export'],
                        'clients' => ['view', 'create', 'edit', 'export'],
                        'products' => ['view', 'create', 'edit', 'manage_stock'],
                        'reports' => ['view', 'view_financial', 'view_inventory', 'export'],
                        'config' => ['view'],
                        'users' => ['view', 'create', 'edit', 'reset_passwords'],
                        'stock' => ['view', 'create', 'edit', 'adjust_inventory'],
                        'documents' => ['view', 'create', 'edit', 'share']
                    ]
                ],
                'staff' => [
                    'name' => 'Staff',
                    'permissions' => [
                        'invoices' => ['view', 'create'],
                        'clients' => ['view', 'create', 'edit'],
                        'products' => ['view'],
                        'reports' => ['view'],
                        'stock' => ['view'],
                        'documents' => ['view', 'create']
                    ]
                ],
                'readonly' => [
                    'name' => 'Read Only',
                    'permissions' => [
                        'invoices' => ['view'],
                        'clients' => ['view'],
                        'products' => ['view'],
                        'reports' => ['view'],
                        'stock' => ['view'],
                        'documents' => ['view']
                    ]
                ]
            ];
            
            if (!isset($templates[$templateName])) {
                return $this->json(['error' => 'Template not found'], 404);
            }
            
            return $this->json([
                'success' => true,
                'template' => $templates[$templateName]
            ]);
            
        } catch (\Exception $e) {
            error_log('PermissionController::getTemplateData error: ' . $e->getMessage());
            return $this->json(['error' => 'Error loading template'], 500);
        }
    }
    
    /**
     * Apply permission template to a group
     */
    public function loadTemplate()
    {
        try {
            // Verify CSRF token
            $csrfToken = Flight::request()->data->csrf_token ?? '';
            if (!$this->validateCsrfToken($csrfToken)) {
                return $this->json(['error' => __('messages.invalid_csrf_token')], 403);
            }
            
            $data = $this->getRequestData();
            $groupId = $data['group_id'] ?? null;
            $templateId = $data['template_id'] ?? null;
            
            if (!$groupId || !$templateId) {
                return $this->json(['error' => __('messages.group_template_required')], 400);
            }
            
            // Check if template exists
            $template = $this->permissionTemplate->getById($templateId);
            if (!$template) {
                return $this->json(['error' => __('messages.template_not_found')], 404);
            }
            
            // Apply template
            $result = $this->groupPermission->applyTemplate($groupId, $templateId, $_SESSION['user_id']);
            
            if ($result) {
                return $this->json([
                    'success' => true,
                    'message' => __('messages.template_applied')
                ]);
            } else {
                return $this->json(['error' => __('messages.error_applying_template')], 500);
            }
            
        } catch (\Exception $e) {
            error_log('PermissionController::loadTemplate error: ' . $e->getMessage());
            return $this->json(['error' => __('messages.error_applying_template')], 500);
        }
    }
    
    /**
     * View permission change history
     */
    public function auditLog()
    {
        try {
            // Build filters from query params
            $filters = [];
            if (Flight::request()->query->group_id) {
                $filters['group_id'] = (int)Flight::request()->query->group_id;
            }
            if (Flight::request()->query->module_id) {
                $filters['module_id'] = (int)Flight::request()->query->module_id;
            }
            if (Flight::request()->query->changed_by) {
                $filters['changed_by'] = (int)Flight::request()->query->changed_by;
            }
            
            $limit = (int)(Flight::request()->query->limit ?? 100);
            $limit = min($limit, 500); // Cap at 500
            
            // Get audit log
            $auditLog = $this->groupPermission->getAuditLog($filters, $limit);
            
            // If AJAX request, return JSON
            if ($this->isAjax()) {
                return $this->json([
                    'success' => true,
                    'data' => $auditLog
                ]);
            }
            
            // Otherwise render view
            $template = $this->getTemplate();
            $viewName = 'permissions/audit-log-modern';
            
            // Get groups and modules for filters
            $groups = UserGroup::getAll();
            $modules = $this->systemModule->getAllModules();
            
            return $this->render($viewName, [
                'title' => __('permissions.audit_log'),
                'auditLog' => $auditLog,
                'groups' => $groups,
                'modules' => $modules,
                'filters' => $filters
            ]);
            
        } catch (\Exception $e) {
            error_log('PermissionController::auditLog error: ' . $e->getMessage());
            
            if ($this->isAjax()) {
                return $this->json(['error' => __('messages.error_loading_audit')], 500);
            }
            
            $this->flash('error', __('messages.error_loading_audit'));
            return $this->redirect('/permissions');
        }
    }
    
    /**
     * Validate CSRF token
     */
    protected function validateCsrfToken($token = null): bool
    {
        // Use parent implementation to ensure consistency
        return parent::validateCsrfToken($token);
    }
    
    /**
     * Get modules structure from permissions table (OLD schema compatibility)
     * Builds a module-like structure from permission categories
     */
    private function getModulesFromPermissions()
    {
        $stmt = $this->db->prepare("
            SELECT DISTINCT 
                category as code,
                category as name,
                MIN(id) as id
            FROM permissions
            GROUP BY category
            ORDER BY category
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $modules = [];
        foreach ($categories as $cat) {
            // Format category name for display
            $displayName = ucfirst(str_replace('_', ' ', $cat['name']));
            
            // Get all permissions for this category
            $stmt = $this->db->prepare("
                SELECT id, code, name, description 
                FROM permissions 
                WHERE category = ?
                ORDER BY sort_order, code
            ");
            $stmt->execute([$cat['code']]);
            $permissions = $stmt->fetchAll(\PDO::FETCH_ASSOC);
            
            // Extract permission types from codes
            $permissionTypes = [];
            $specialPermissions = [];
            
            foreach ($permissions as $perm) {
                // Extract action from code (e.g., 'invoices.view' -> 'view')
                $parts = explode('.', $perm['code']);
                if (count($parts) >= 2) {
                    $action = end($parts);
                    if (in_array($action, ['view', 'create', 'edit', 'delete'])) {
                        $permissionTypes[$action] = true;
                    } else {
                        $specialPermissions[] = [
                            'code' => $action,
                            'name' => $perm['name']
                        ];
                    }
                }
            }
            
            $modules[] = [
                'id' => $cat['id'],
                'code' => $cat['code'],
                'name' => $displayName,
                'icon' => $this->getIconForCategory($cat['code']),
                'parent_id' => null,
                'children' => [],
                'permissions' => array_keys($permissionTypes),
                'special' => $specialPermissions
            ];
        }
        
        return $modules;
    }
    
    /**
     * Get icon for category (OLD schema compatibility)
     */
    private function getIconForCategory($category)
    {
        $iconMap = [
            'invoices' => 'fa-file-invoice',
            'clients' => 'fa-users',
            'products' => 'fa-box',
            'reports' => 'fa-chart-bar',
            'configuration' => 'fa-cog',
            'users' => 'fa-user-cog',
            'payments' => 'fa-credit-card',
            'categories' => 'fa-tags',
            'retrocession' => 'fa-hand-holding-usd',
            'permissions' => 'fa-shield-alt'
        ];
        
        return $iconMap[$category] ?? 'fa-folder';
    }
    
    /**
     * Get group permissions formatted for the view (OLD schema)
     */
    private function getGroupPermissionsForOldSchema($groupId)
    {
        $stmt = $this->db->prepare("
            SELECT 
                p.id,
                p.category,
                p.code,
                p.name,
                p.description,
                CASE WHEN gp.permission_id IS NOT NULL THEN 1 ELSE 0 END as is_granted
            FROM permissions p
            LEFT JOIN group_permissions gp ON 
                p.id = gp.permission_id 
                AND gp.group_id = ?
            ORDER BY p.category, p.sort_order, p.code
        ");
        $stmt->execute([$groupId]);
        $permissions = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        $organized = [];
        
        foreach ($permissions as $perm) {
            $category = $perm['category'];
            
            if (!isset($organized[$category])) {
                $organized[$category] = [
                    'module_id' => $category,
                    'module_code' => $category,
                    'module_name' => ucfirst(str_replace('_', ' ', $category)),
                    'icon' => $this->getIconForCategory($category),
                    'permissions' => []
                ];
            }
            
            // Extract action from code
            $parts = explode('.', $perm['code']);
            $action = count($parts) >= 2 ? end($parts) : 'view';
            
            $organized[$category]['permissions'][] = [
                'permission_id' => $perm['id'],
                'permission_code' => $perm['code'],
                'action' => $action,
                'permission_name' => $perm['name'],
                'description' => $perm['description'],
                'is_granted' => (bool)$perm['is_granted']
            ];
        }
        
        return array_values($organized);
    }
}