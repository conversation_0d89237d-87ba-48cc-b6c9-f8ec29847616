<!DOCTYPE html>
<html lang="{{ current_language() }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Debug - {{ error.message|slice(0, 50) }}...</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            color: #333;
        }
        .error-header {
            background: #dc3545;
            color: white;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .error-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }
        .error-header .error-id {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 5px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .error-box {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .error-box-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .error-box-content {
            padding: 20px;
        }
        .error-message {
            font-size: 18px;
            color: #dc3545;
            margin-bottom: 10px;
        }
        .error-location {
            color: #6c757d;
            font-size: 14px;
        }
        .stack-trace {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 13px;
            line-height: 1.5;
        }
        .stack-frame {
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .stack-frame:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        .stack-frame-number {
            color: #6c757d;
            margin-right: 10px;
        }
        .stack-frame-class {
            color: #0066cc;
            font-weight: 600;
        }
        .stack-frame-function {
            color: #28a745;
        }
        .stack-frame-file {
            color: #6c757d;
            font-size: 12px;
            margin-top: 3px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
        }
        .info-value {
            color: #6c757d;
            text-align: right;
            max-width: 60%;
            word-break: break-word;
        }
        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 600;
            border-radius: 4px;
            text-transform: uppercase;
        }
        .badge-danger {
            background: #dc3545;
            color: white;
        }
        .badge-warning {
            background: #ffc107;
            color: #212529;
        }
        .badge-info {
            background: #17a2b8;
            color: white;
        }
        .badge-success {
            background: #28a745;
            color: white;
        }
        .performance-metrics {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        .metric {
            flex: 1;
            min-width: 150px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 14px;
            color: #6c757d;
        }
        pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .collapsible {
            cursor: pointer;
            user-select: none;
        }
        .collapsible:hover {
            background: #e9ecef;
        }
        .collapsible::before {
            content: '▼';
            display: inline-block;
            margin-right: 5px;
            transition: transform 0.2s;
        }
        .collapsible.collapsed::before {
            transform: rotate(-90deg);
        }
        .collapse-content {
            max-height: 400px;
            overflow-y: auto;
        }
        .collapse-content.collapsed {
            display: none;
        }
    </style>
</head>
<body>
    <div class="error-header">
        <h1>Application Error (Debug Mode)</h1>
        <div class="error-id">Error ID: {{ error.id }}</div>
    </div>

    <div class="container">
        <!-- Main Error Information -->
        <div class="error-box">
            <div class="error-box-header">
                <span>Error Details</span>
                <span class="badge badge-danger">{{ error.class }}</span>
            </div>
            <div class="error-box-content">
                <div class="error-message">{{ error.message }}</div>
                <div class="error-location">
                    in <strong>{{ error.file }}</strong> at line <strong>{{ error.line }}</strong>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="error-box">
            <div class="error-box-header">Performance Metrics</div>
            <div class="error-box-content">
                <div class="performance-metrics">
                    <div class="metric">
                        <div class="metric-value">{{ error.performance.execution_time }}s</div>
                        <div class="metric-label">Execution Time</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{{ error.performance.memory_usage }}MB</div>
                        <div class="metric-label">Memory Usage</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{{ error.performance.memory_peak }}MB</div>
                        <div class="metric-label">Peak Memory</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">{{ error.performance.included_files }}</div>
                        <div class="metric-label">Included Files</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Request Information -->
        <div class="error-box">
            <div class="error-box-header collapsible" onclick="toggleCollapse(this)">
                Request Information
            </div>
            <div class="error-box-content collapse-content">
                <div class="info-grid">
                    <div>
                        <div class="info-item">
                            <span class="info-label">Method</span>
                            <span class="info-value">
                                <span class="badge badge-info">{{ error.request.method }}</span>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">URI</span>
                            <span class="info-value">{{ error.request.uri }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">IP Address</span>
                            <span class="info-value">{{ error.request.ip }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">User Agent</span>
                            <span class="info-value">{{ error.request.user_agent }}</span>
                        </div>
                        {% if error.request.referer %}
                        <div class="info-item">
                            <span class="info-label">Referer</span>
                            <span class="info-value">{{ error.request.referer }}</span>
                        </div>
                        {% endif %}
                    </div>
                    <div>
                        <div class="info-item">
                            <span class="info-label">Environment</span>
                            <span class="info-value">
                                <span class="badge badge-warning">{{ error.environment }}</span>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Timestamp</span>
                            <span class="info-value">{{ error.timestamp }}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">User</span>
                            <span class="info-value">
                                {% if error.session.username %}
                                    {{ error.session.username }} (ID: {{ error.session.user_id }})
                                {% else %}
                                    Guest
                                {% endif %}
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">Session ID</span>
                            <span class="info-value">{{ error.session.id|default('None') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stack Trace -->
        <div class="error-box">
            <div class="error-box-header collapsible" onclick="toggleCollapse(this)">
                Stack Trace
            </div>
            <div class="error-box-content collapse-content">
                <div class="stack-trace">
                    {% for frame in error.trace_array|slice(0, 10) %}
                    <div class="stack-frame">
                        <span class="stack-frame-number">#{{ loop.index0 }}</span>
                        {% if frame.class %}
                            <span class="stack-frame-class">{{ frame.class }}</span>{{ frame.type|default('') }}
                        {% endif %}
                        <span class="stack-frame-function">{{ frame.function }}()</span>
                        {% if frame.file %}
                        <div class="stack-frame-file">
                            {{ frame.file }}:{{ frame.line }}
                        </div>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <!-- Request Parameters -->
        {% if error.request.params.get or error.request.params.post %}
        <div class="error-box">
            <div class="error-box-header collapsible collapsed" onclick="toggleCollapse(this)">
                Request Parameters
            </div>
            <div class="error-box-content collapse-content collapsed">
                {% if error.request.params.get %}
                    <h4>GET Parameters</h4>
                    <pre>{{ error.request.params.get|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                {% endif %}
                
                {% if error.request.params.post %}
                    <h4>POST Parameters</h4>
                    <pre>{{ error.request.params.post|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <!-- Headers -->
        <div class="error-box">
            <div class="error-box-header collapsible collapsed" onclick="toggleCollapse(this)">
                Request Headers
            </div>
            <div class="error-box-content collapse-content collapsed">
                <pre>{{ error.request.headers|json_encode(constant('JSON_PRETTY_PRINT')) }}</pre>
            </div>
        </div>

        <!-- Server Information -->
        <div class="error-box">
            <div class="error-box-header collapsible collapsed" onclick="toggleCollapse(this)">
                Server Information
            </div>
            <div class="error-box-content collapse-content collapsed">
                <div class="info-item">
                    <span class="info-label">Hostname</span>
                    <span class="info-value">{{ error.server.hostname }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Software</span>
                    <span class="info-value">{{ error.server.software }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">PHP Version</span>
                    <span class="info-value">{{ error.server.php_version }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Operating System</span>
                    <span class="info-value">{{ error.server.os }}</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleCollapse(element) {
            element.classList.toggle('collapsed');
            const content = element.nextElementSibling;
            if (content) {
                content.classList.toggle('collapsed');
            }
        }
    </script>
</body>
</html>