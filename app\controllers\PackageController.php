<?php

namespace App\Controllers;

use App\Core\Controller;
use App\Models\ServicePackage;
use App\Models\PackagePurchase;
use App\Models\PackageUsage;
use App\Models\CatalogItem;
use App\Models\Client;
use App\Models\SalesInvoice;
use Flight;
use Exception;

class PackageController extends Controller
{
    protected $servicePackage;
    protected $packagePurchase;
    protected $packageUsage;
    
    public function __construct()
    {
        // Don't call parent constructor as it doesn't exist
        $this->servicePackage = new ServicePackage();
        $this->packagePurchase = new PackagePurchase();
        $this->packageUsage = new PackageUsage();
    }
    
    /**
     * List all packages
     */
    public function index()
    {
        $packages = $this->servicePackage->with(['purchases'])->orderBy('name')->get();
        
        // Calculate statistics for each package
        foreach ($packages as $package) {
            $package->active_purchases = $package->getActivePurchasesCount();
            $package->average_usage_rate = $package->getAverageUsageRate();
        }
        
        $this->render('packages/index', [
            'packages' => $packages,
            'pageTitle' => __('packages.title')
        ]);
    }
    
    /**
     * Show create package form
     */
    public function create()
    {
        $services = CatalogItem::where('type', 'service')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
        
        $this->render('packages/create', [
            'services' => $services,
            'pageTitle' => __('packages.create')
        ]);
    }
    
    /**
     * Store new package
     */
    public function store()
    {
        try {
            $data = Flight::request()->data->getData();
            
            // Validation
            $rules = [
                'name' => 'required|string|max:255',
                'total_sessions' => 'required|integer|min:1',
                'valid_days' => 'required|integer|min:1',
                'price' => 'required|numeric|min:0',
                'services_included' => 'required|array'
            ];
            
            $validation = $this->validate($data, $rules);
            if (!$validation['valid']) {
                $this->jsonResponse(['success' => false, 'errors' => $validation['errors']], 422);
                return;
            }
            
            // Create package
            $package = $this->servicePackage->create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'total_sessions' => $data['total_sessions'],
                'valid_days' => $data['valid_days'],
                'price' => $data['price'],
                'services_included' => json_encode($data['services_included']),
                'is_active' => $data['is_active'] ?? true
            ]);
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('packages.created_successfully'),
                'redirect' => '/packages/' . $package->id
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Show package details
     */
    public function show($id)
    {
        $package = $this->servicePackage->findOrFail($id);
        
        // Get active purchases
        $activePurchases = $this->packagePurchase
            ->where('package_id', $id)
            ->where('status', 'active')
            ->with(['client', 'invoice'])
            ->orderBy('purchase_date', 'desc')
            ->get();
        
        // Get usage statistics
        $usageStats = PackageUsage::join('package_purchases', 'package_usage.purchase_id', '=', 'package_purchases.id')
            ->where('package_purchases.package_id', $id)
            ->selectRaw('
                COUNT(*) as total_uses,
                COUNT(DISTINCT package_usage.service_id) as services_used,
                COUNT(DISTINCT package_usage.therapist_id) as therapists_involved
            ')
            ->first();
        
        $this->render('packages/show', [
            'package' => $package,
            'activePurchases' => $activePurchases,
            'usageStats' => $usageStats,
            'pageTitle' => $package->name
        ]);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $package = $this->servicePackage->findOrFail($id);
        
        $services = CatalogItem::where('type', 'service')
            ->where('is_active', true)
            ->orderBy('name')
            ->get();
        
        // Decode services_included for the form
        $package->services_included = is_string($package->services_included) 
            ? json_decode($package->services_included, true) 
            : $package->services_included;
        
        $this->render('packages/edit', [
            'package' => $package,
            'services' => $services,
            'pageTitle' => __('packages.edit')
        ]);
    }
    
    /**
     * Update package
     */
    public function update($id)
    {
        try {
            $package = $this->servicePackage->findOrFail($id);
            $data = Flight::request()->data->getData();
            
            // Validation
            $rules = [
                'name' => 'required|string|max:255',
                'total_sessions' => 'required|integer|min:1',
                'valid_days' => 'required|integer|min:1',
                'price' => 'required|numeric|min:0',
                'services_included' => 'required|array'
            ];
            
            $validation = $this->validate($data, $rules);
            if (!$validation['valid']) {
                $this->jsonResponse(['success' => false, 'errors' => $validation['errors']], 422);
                return;
            }
            
            // Update package
            $package->update([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'total_sessions' => $data['total_sessions'],
                'valid_days' => $data['valid_days'],
                'price' => $data['price'],
                'services_included' => json_encode($data['services_included']),
                'is_active' => $data['is_active'] ?? true
            ]);
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('packages.updated_successfully'),
                'redirect' => '/packages/' . $package->id
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Delete package
     */
    public function destroy($id)
    {
        try {
            $package = $this->servicePackage->findOrFail($id);
            
            // Check if package has purchases
            if ($package->purchases()->count() > 0) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('packages.cannot_delete_has_purchases')
                ], 400);
                return;
            }
            
            $package->delete();
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('packages.deleted_successfully'),
                'redirect' => '/packages'
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Show purchase form
     */
    public function purchase($id)
    {
        $package = $this->servicePackage->findOrFail($id);
        $clients = Client::where('is_active', true)->orderBy('name')->get();
        
        $this->render('packages/purchase', [
            'package' => $package,
            'clients' => $clients,
            'pageTitle' => __('packages.purchase')
        ]);
    }
    
    /**
     * Process package purchase
     */
    public function processPurchase($id)
    {
        try {
            $package = $this->servicePackage->findOrFail($id);
            $data = Flight::request()->data->getData();
            
            // Validation
            if (empty($data['client_id'])) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('packages.client_required')
                ], 422);
                return;
            }
            
            // Create sales invoice
            $invoice = SalesInvoice::create([
                'client_id' => $data['client_id'],
                'invoice_type' => 'package',
                'issue_date' => date('Y-m-d'),
                'subtotal' => $package->price,
                'total_vat' => 0, // Packages might be VAT exempt
                'total_amount' => $package->price,
                'payment_status' => 'unpaid',
                'notes' => $data['notes'] ?? null,
                'created_by' => $_SESSION['user_id'] ?? 1
            ]);
            
            // Generate invoice number
            $invoice->invoice_number = $invoice->generateNumber();
            $invoice->save();
            
            // Create invoice line
            $invoice->lines()->create([
                'line_type' => 'package',
                'item_id' => $package->id,
                'description' => $package->name,
                'quantity' => 1,
                'unit_price' => $package->price,
                'discount_percent' => 0,
                'vat_rate' => 0,
                'line_total' => $package->price
            ]);
            
            // Create package purchase
            $purchase = $package->createPurchase(
                $data['client_id'],
                $invoice->id,
                $data['purchase_date'] ?? date('Y-m-d')
            );
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('packages.purchased_successfully'),
                'redirect' => '/packages/purchases/' . $purchase->id
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * List all purchases
     */
    public function purchases()
    {
        $purchases = $this->packagePurchase
            ->with(['package', 'client', 'invoice'])
            ->orderBy('purchase_date', 'desc')
            ->paginate(20);
        
        $this->render('packages/purchases', [
            'purchases' => $purchases,
            'pageTitle' => __('packages.purchases')
        ]);
    }
    
    /**
     * Show purchase details
     */
    public function showPurchase($id)
    {
        $purchase = $this->packagePurchase->with(['package', 'client', 'invoice', 'usage.service', 'usage.therapist'])->findOrFail($id);
        
        $this->render('packages/purchase-details', [
            'purchase' => $purchase,
            'pageTitle' => __('packages.purchase_details')
        ]);
    }
    
    /**
     * Use a session from package
     */
    public function useSession($purchaseId)
    {
        try {
            $purchase = $this->packagePurchase->findOrFail($purchaseId);
            $data = Flight::request()->data->getData();
            
            // Validation
            if (empty($data['service_id'])) {
                $this->jsonResponse([
                    'success' => false,
                    'message' => __('packages.service_required')
                ], 422);
                return;
            }
            
            // Use session
            $usage = $purchase->useSession(
                $data['service_id'],
                $data['therapist_id'] ?? null,
                $data['invoice_id'] ?? null,
                $data['notes'] ?? null
            );
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('packages.session_used_successfully'),
                'remaining_sessions' => $purchase->getRemainingSessions()
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => $e->getMessage()
            ], 400);
        }
    }
    
    /**
     * Get client's active packages
     */
    public function clientPackages($clientId)
    {
        $packages = $this->packagePurchase
            ->where('client_id', $clientId)
            ->where('status', 'active')
            ->where('expiry_date', '>=', date('Y-m-d'))
            ->with('package')
            ->get();
        
        $this->jsonResponse([
            'success' => true,
            'packages' => $packages
        ]);
    }
    
    /**
     * Check expiring packages
     */
    public function expiring()
    {
        $packages = $this->packagePurchase
            ->expiringSoon(30)
            ->with(['package', 'client'])
            ->get();
        
        $this->render('packages/expiring', [
            'packages' => $packages,
            'pageTitle' => __('packages.expiring_packages')
        ]);
    }
    
    /**
     * Duplicate a package
     */
    public function duplicate($id)
    {
        try {
            $package = $this->servicePackage->findOrFail($id);
            $newPackage = $package->duplicate();
            
            $this->jsonResponse([
                'success' => true,
                'message' => __('packages.duplicated_successfully'),
                'redirect' => '/packages/' . $newPackage->id . '/edit'
            ]);
            
        } catch (Exception $e) {
            $this->jsonResponse([
                'success' => false,
                'message' => __('common.error_occurred') . ': ' . $e->getMessage()
            ], 500);
        }
    }
}