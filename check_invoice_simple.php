<?php
// Simple check for invoice FAC-RET30-2025-0189
require_once __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS'] ?? '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // First check if invoice exists
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM invoices WHERE invoice_number = 'FAC-RET30-2025-0189'");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        echo "Invoice FAC-RET30-2025-0189 not found in database.\n";
        
        // List recent RET30 invoices
        echo "\nRecent RET30 invoices:\n";
        $stmt = $pdo->prepare("SELECT invoice_number, created_at, total FROM invoices WHERE invoice_number LIKE '%RET30%' ORDER BY created_at DESC LIMIT 10");
        $stmt->execute();
        $invoices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($invoices as $inv) {
            echo "- {$inv['invoice_number']} | Created: {$inv['created_at']} | Total: €{$inv['total']}\n";
        }
    } else {
        // Get invoice details
        $stmt = $pdo->prepare("
            SELECT i.*, u.first_name, u.last_name, u.vat_intercommunautaire
            FROM invoices i
            LEFT JOIN users u ON i.user_id = u.id
            WHERE i.invoice_number = 'FAC-RET30-2025-0189'
        ");
        $stmt->execute();
        $invoice = $stmt->fetch(PDO::FETCH_ASSOC);
        
        echo "Invoice Found!\n";
        echo "Number: {$invoice['invoice_number']}\n";
        echo "User: {$invoice['first_name']} {$invoice['last_name']}\n";
        echo "VAT Intracommunautaire: " . ($invoice['vat_intercommunautaire'] ?: 'NONE') . "\n";
        echo "Subtotal: €{$invoice['subtotal']}\n";
        echo "VAT Amount: €{$invoice['vat_amount']}\n";
        echo "Total: €{$invoice['total']}\n";
        echo "Status: {$invoice['status']}\n";
        
        // Get invoice lines
        echo "\nInvoice Lines:\n";
        $stmt = $pdo->prepare("
            SELECT il.*, vr.rate 
            FROM invoice_lines il
            LEFT JOIN config_vat_rates vr ON il.vat_rate_id = vr.id
            WHERE il.invoice_id = ?
        ");
        $stmt->execute([$invoice['id']]);
        $lines = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($lines as $line) {
            $lineTotal = $line['quantity'] * $line['unit_price'];
            $lineVat = $lineTotal * ($line['rate'] / 100);
            echo "- {$line['description']}: {$line['quantity']} x €{$line['unit_price']} = €$lineTotal (VAT {$line['rate']}%: €$lineVat)\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}