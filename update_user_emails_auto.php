<?php
// Update existing user emails to @fit-360.lu format (automatic version)

// Load database configuration
$configFile = __DIR__ . '/.env';
$env = parse_ini_file($configFile);

try {
    // Connect to database
    $dsn = sprintf('mysql:host=%s;dbname=%s;charset=utf8mb4', 
        $env['DB_HOST'] ?? 'localhost',
        $env['DB_DATABASE'] ?? 'fitapp'
    );
    
    $db = new PDO($dsn, 
        $env['DB_USERNAME'] ?? 'root', 
        $env['DB_PASSWORD'] ?? ''
    );
    
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Get all users who don't have @fit-360.lu emails
    $stmt = $db->query("
        SELECT id, first_name, email 
        FROM users 
        WHERE email NOT LIKE '%@fit-360.lu'
        ORDER BY id
    ");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h2>Updating User Emails to @fit-360.lu</h2>";
    echo "<p>Found " . count($users) . " users to update</p>";
    
    // Track used emails to ensure uniqueness
    $stmt = $db->query("SELECT email FROM users WHERE email LIKE '%@fit-360.lu'");
    $usedEmails = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $updates = [];
    
    foreach ($users as $user) {
        // Clean the first name
        $cleanName = strtolower($user['first_name']);
        
        // Remove accents
        $cleanName = iconv('UTF-8', 'ASCII//TRANSLIT', $cleanName);
        
        // Remove special characters and spaces
        $cleanName = preg_replace('/[^a-z0-9]/', '', $cleanName);
        
        if (empty($cleanName)) {
            echo "<p style='color: orange;'>⚠ Skipping user ID {$user['id']} - no valid name</p>";
            continue;
        }
        
        // Generate unique email
        $baseEmail = $cleanName . '@fit-360.lu';
        $newEmail = $baseEmail;
        $counter = 1;
        
        while (in_array($newEmail, $usedEmails)) {
            $counter++;
            $newEmail = $cleanName . $counter . '@fit-360.lu';
        }
        
        $usedEmails[] = $newEmail;
        $updates[] = [
            'id' => $user['id'],
            'old_email' => $user['email'],
            'new_email' => $newEmail,
            'name' => $user['first_name']
        ];
    }
    
    // Display changes made
    echo "<h3>Email Updates:</h3>";
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>ID</th><th>Name</th><th>Old Email</th><th>New Email</th><th>Status</th></tr>";
    
    // Perform updates
    $stmt = $db->prepare("UPDATE users SET email = ? WHERE id = ?");
    $success = 0;
    $failed = 0;
    
    foreach ($updates as $update) {
        echo "<tr>";
        echo "<td>{$update['id']}</td>";
        echo "<td>{$update['name']}</td>";
        echo "<td>{$update['old_email']}</td>";
        echo "<td>{$update['new_email']}</td>";
        
        try {
            $stmt->execute([$update['new_email'], $update['id']]);
            $success++;
            echo "<td style='color: green;'>✓ Updated</td>";
        } catch (Exception $e) {
            $failed++;
            echo "<td style='color: red;'>✗ Failed: " . htmlspecialchars($e->getMessage()) . "</td>";
        }
        echo "</tr>";
    }
    
    echo "</table>";
    
    echo "<h3>Summary:</h3>";
    echo "<p style='color: green;'>✓ Successfully updated: $success users</p>";
    if ($failed > 0) {
        echo "<p style='color: red;'>✗ Failed to update: $failed users</p>";
    }
    echo "<p>Total processed: " . count($updates) . " users</p>";
    
    // Clean up temporary files
    $tempFiles = [
        __DIR__ . '/run_loyer_config.php',
        __DIR__ . '/configure_loyer_columns.sql',
        __DIR__ . '/check_invoice_types.php',
        __DIR__ . '/run_user_address_migration.php',
        __DIR__ . '/update_user_emails.php'
    ];
    
    echo "<h3>Cleanup:</h3>";
    foreach ($tempFiles as $file) {
        if (file_exists($file)) {
            if (unlink($file)) {
                echo "<p>✓ Removed temporary file: " . basename($file) . "</p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}