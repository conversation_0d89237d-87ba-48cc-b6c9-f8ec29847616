<?php

return [
    'required' => 'This field is required',
    'email' => 'Please enter a valid email address',
    'numeric' => 'This field must be a number',
    'min' => 'This field must be at least :min characters',
    'max' => 'This field must not exceed :max characters',
    'unique' => 'This value already exists',
    'confirmed' => 'The confirmation does not match',
    'date' => 'Please enter a valid date',
    'date_format' => 'Please enter a date in the format :format',
    'before' => 'This date must be before :date',
    'after' => 'This date must be after :date',
    'regex' => 'The format is invalid',
    'in' => 'The selected value is invalid',
    'between' => 'This value must be between :min and :max',
    'required_field' => ':field is required',
    'invalid_data_format' => 'Invalid data format received',
    'required_fields' => 'Please fill in all required fields',
    'fix_errors' => 'Please fix the errors below',
];