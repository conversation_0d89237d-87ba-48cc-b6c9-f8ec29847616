# Phase 8: Legacy Product/Service Catalog System (Week 13)

## Task 5.1: Catalog Database Structure
### Subtask 5.1.1: Create Catalog Tables
**Files to Create:**
- Create migration `/database/migrations/create_catalog_tables.sql`

**Code to Implement:**
```sql
-- Main catalog table for all items
CREATE TABLE `catalog_items` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `code` VARCHAR(50) NOT NULL UNIQUE,
    `name` VARCHAR(255) NOT NULL,
    `description` TEXT,
    `short_description` VARCHAR(500), -- For invoice lines
    `category_id` INT UNSIGNED NOT NULL,
    `item_type` ENUM('product', 'service', 'rent', 'charge', 'retrocession', 'other') NOT NULL,
    `invoice_context` SET('practitioner', 'sales', 'both') DEFAULT 'both',
    `unit_of_measure` VARCHAR(50) DEFAULT 'piece',
    `standard_price` DECIMAL(10,2) NOT NULL,
    `member_price` DECIMAL(10,2) NULL,
    `special_price` DECIMAL(10,2) NULL,
    `vat_rate_id` INT UNSIGNED NOT NULL,
    `accounting_code` VARCHAR(20) NULL,
    `is_active` BOOLEAN DEFAULT TRUE,
    `usage_count` INT DEFAULT 0,
    `last_used` TIMESTAMP NULL,
    `created_by` INT UNSIGNED NOT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (`category_id`) REFERENCES `catalog_categories` (`id`),
    FOREIGN KEY (`vat_rate_id`) REFERENCES `vat_rates` (`id`),
    FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
    INDEX `idx_active_type` (`is_active`, `item_type`),
    INDEX `idx_context` (`invoice_context`),
    INDEX `idx_usage` (`usage_count`, `last_used`),
    FULLTEXT INDEX `idx_search` (`name`, `description`, `code`)
);

-- Categories with hierarchy
CREATE TABLE `catalog_categories` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `name` VARCHAR(100) NOT NULL,
    `parent_id` INT UNSIGNED NULL,
    `category_type` ENUM('product', 'service', 'rent', 'charge', 'mixed') DEFAULT 'mixed',
    `icon` VARCHAR(50) DEFAULT 'fa-folder',
    `color` VARCHAR(7) DEFAULT '#6c757d',
    `sort_order` INT DEFAULT 0,
    `is_active` BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (`parent_id`) REFERENCES `catalog_categories` (`id`)
);

-- User favorites for quick access
CREATE TABLE `catalog_favorites` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL,
    `item_id` INT UNSIGNED NOT NULL,
    `added_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY `user_item` (`user_id`, `item_id`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`item_id`) REFERENCES `catalog_items` (`id`) ON DELETE CASCADE
);

-- Recent items tracking
CREATE TABLE `catalog_recent_items` (
    `id` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    `user_id` INT UNSIGNED NOT NULL,
    `item_id` INT UNSIGNED NOT NULL,
    `used_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `use_count` INT DEFAULT 1,
    INDEX `idx_user_recent` (`user_id`, `used_at`),
    FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    FOREIGN KEY (`item_id`) REFERENCES `catalog_items` (`id`) ON DELETE CASCADE
);
```

**Test Cases:**
- [ ] Catalog tables created successfully
- [ ] Foreign key constraints work
- [ ] Indexes optimize search performance
- [ ] Full-text search functions correctly
- [ ] Default categories inserted

### Subtask 5.1.2: Catalog Model Implementation
**Files to Create:**
- Create `/app/models/CatalogItem.php`
- Create `/app/models/CatalogCategory.php`
- Create `/app/models/CatalogFavorite.php`

**Code to Implement:**
```php
class CatalogItem {
    public function search($query, $context = null, $limit = 10) {
        // Full-text search with context filtering
    }
    
    public function forContext($context) {
        // Filter items by invoice context
    }
    
    public function trackUsage($userId) {
        // Track item usage for suggestions
    }
    
    public function duplicate() {
        // Create copy of item
    }
}
```

**Test Cases:**
- [ ] Search returns relevant results
- [ ] Context filtering works correctly
- [ ] Usage tracking increments properly
- [ ] Duplication preserves all data
- [ ] Recent items tracked per user

## Task 5.2: Inline Smart Search Implementation
### Subtask 5.2.1: Search Service Development
**Files to Create:**
- Create `/app/services/CatalogSearchService.php`
- Create `/app/controllers/api/CatalogApiController.php`

**Features to Implement:**
- Real-time search with minimum 2 characters
- Context-aware filtering (practitioner vs sales)
- Usage-based ranking
- User-specific suggestions (recent & favorites)
- Fuzzy matching for typos

**Test Cases:**
- [ ] Search responds in < 200ms
- [ ] Context filtering excludes irrelevant items
- [ ] Most used items appear first
- [ ] Fuzzy matching finds close matches
- [ ] API endpoints return correct JSON

### Subtask 5.2.2: JavaScript Search Component
**Files to Create:**
- Create `/public/assets/js/inline-item-search.js`
- Create search component CSS

**Features to Implement:**
- Dropdown search results
- Keyboard navigation (arrows, enter, escape)
- Click outside to close
- Loading state during search
- No results state with "create new" option

**Test Cases:**
- [ ] Search dropdown appears on focus
- [ ] Keyboard navigation works smoothly
- [ ] Selected item fills invoice line
- [ ] Escape key closes dropdown
- [ ] Loading state shows during search

### Subtask 5.2.3: Invoice Integration
**Files to Update:**
- Update `/app/views/invoices/create.twig`
- Update `/app/views/invoices/edit.twig`
- Update invoice JavaScript for all templates

**Features to Implement:**
- Replace description text field with search field
- Auto-fill price, VAT, and unit from catalog
- Quick-add favorites buttons
- Recent items suggestions
- Manual entry fallback option

**Test Cases:**
- [ ] Search field replaces description input
- [ ] Item selection fills all fields
- [ ] Favorites quick-add works
- [ ] Manual entry still possible
- [ ] Works in all template variants

## Task 5.3: Catalog Management Interface
### Subtask 5.3.1: Catalog Management Controller
**Files to Create:**
- Create `/app/controllers/CatalogController.php`
- Create catalog routes

**Features to Implement:**
- CRUD operations for items
- Bulk import/export
- Category management
- Usage statistics view
- Duplicate with modifications

**Test Cases:**
- [ ] Item CRUD operations work
- [ ] Import handles CSV/Excel files
- [ ] Export includes all data
- [ ] Categories organize properly
- [ ] Statistics display correctly

### Subtask 5.3.2: Catalog Management Views
**Files to Create:**
- Create `/app/views/catalog/index.twig`
- Create `/app/views/catalog/form.twig`
- Create `/app/views/catalog/import.twig`
- Create equivalent views for all templates

**Features to Implement:**
- Filterable item listing
- Category tabs
- Quick edit in table
- Bulk operations
- Import wizard with field mapping

**Test Cases:**
- [ ] Listing displays all items
- [ ] Filters work correctly
- [ ] Quick edit saves changes
- [ ] Bulk delete with confirmation
- [ ] Import wizard guides user

### Subtask 5.3.3: Usage Analytics
**Files to Create:**
- Create `/app/views/catalog/analytics.twig`

**Features to Display:**
- Most used items
- Items not used in X days
- Usage trends over time
- Popular combinations
- Low stock alerts (future)

**Test Cases:**
- [ ] Analytics calculate correctly
- [ ] Charts display properly
- [ ] Date range filters work
- [ ] Export analytics data
- [ ] Performance acceptable

## Task 5.4: Advanced Catalog Features
### Subtask 5.4.1: Pricing Rules Engine
**Files to Create:**
- Create pricing rules tables
- Create `/app/models/PricingRule.php`

**Features to Implement:**
- Customer-specific pricing
- Quantity discounts
- Date-based pricing
- Bundle pricing
- Promotional codes

**Test Cases:**
- [ ] Customer prices override standard
- [ ] Quantity discounts calculate
- [ ] Date ranges respected
- [ ] Bundles price correctly
- [ ] Promo codes apply

### Subtask 5.4.2: Multi-language Catalog
**Database Updates:**
- Add translation tables for catalog items

**Features to Implement:**
- Item names in multiple languages
- Description translations
- Language-specific search
- Import/export with languages

**Test Cases:**
- [ ] Translations display correctly
- [ ] Search works in all languages
- [ ] Import preserves translations
- [ ] Fallback to default language