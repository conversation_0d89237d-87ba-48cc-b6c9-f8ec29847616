{% extends "base-modern.twig" %}

{% block title %}{{ __('translations.translations') }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ __('translations.translation_management') }}</h3>
                <div class="card-tools">
                    <a href="{{ base_url }}/translations/diagnostic" class="btn btn-warning btn-sm">
                        <i class="bi bi-bug"></i> {{ __('translations.diagnostic') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Filter Section -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text" id="liveSearch" class="form-control" 
                                   placeholder="{{ __('translations.search_translations') }}..." 
                                   value="{{ search }}">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                <i class="bi bi-x-circle"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="groupFilter" class="form-select">
                            <option value="">{{ __('translations.all_groups') }}</option>
                            {% for grp in groups %}
                                <option value="{{ grp }}" {% if grp == currentGroup %}selected{% endif %}>
                                    {{ grp }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mt-2">
                            <input type="checkbox" class="form-check-input" id="showMissing" 
                                   {% if showMissing %}checked{% endif %}>
                            <label class="form-check-label" for="showMissing">
                                {{ __('translations.missing_translations') }}
                            </label>
                        </div>
                    </div>
                    <div class="col-md-2 text-end">
                        <span class="text-muted">
                            <span id="visibleCount">0</span> / <span id="totalCount">{{ allTranslations|length }}</span> {{ __('common.entries') }}
                        </span>
                    </div>
                </div>

                <!-- Statistics -->
                {% if stats %}
                <div class="alert alert-info mb-4">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="mb-0">{{ __('translations.statistics') }}</h5>
                        <div>
                            <span class="badge bg-primary fs-6">
                                <i class="bi bi-translate"></i> {{ __('translations.total_translations') }}: {{ stats.total }}
                            </span>
                        </div>
                    </div>
                    {% if stats.by_group %}
                    <div class="mt-3">
                        <strong class="d-block mb-2">{{ __('common.by_group') }}:</strong>
                        <div class="d-flex flex-wrap gap-2">
                            {% for group in stats.by_group %}
                                <span class="badge bg-secondary">
                                    <i class="bi bi-folder"></i> {{ group.group }}: {{ group.count }}
                                </span>
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Translations Table -->
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="translationsTable">
                        <thead>
                            <tr>
                                <th style="width: 120px;">{{ __('translations.group') }}</th>
                                <th style="width: 200px;">{{ __('translations.key') }}</th>
                                {% for lang in languages %}
                                <th>{{ __('translations.value') }} ({{ lang|upper }})</th>
                                {% endfor %}
                                <th style="width: 120px;">{{ __('common.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for translation in allTranslations %}
                            <tr id="translation-{{ translation.group }}-{{ translation.key|replace({'.': '_'}) }}">
                                <td>
                                    <span class="badge bg-primary">{{ translation.group }}</span>
                                </td>
                                <td>
                                    <code>{{ translation.key }}</code>
                                </td>
                                {% for lang in languages %}
                                <td class="editable-cell" data-lang="{{ lang }}" data-group="{{ translation.group }}" data-key="{{ translation.key }}">
                                    <div class="cell-content">
                                        {% if translation.values[lang] %}
                                            {{ translation.values[lang] }}
                                        {% else %}
                                            <span class="text-muted">{{ __('translations.not_translated') }}</span>
                                        {% endif %}
                                    </div>
                                    <input type="text" class="form-control form-control-sm inline-editor d-none" 
                                           value="{{ translation.values[lang]|default('') }}" 
                                           data-original="{{ translation.values[lang]|default('') }}">
                                </td>
                                {% endfor %}
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-info multilang-edit" 
                                                data-group="{{ translation.group }}" 
                                                data-key="{{ translation.key }}"
                                                title="{{ __('translations.edit_all_languages') }}">
                                            <i class="bi bi-translate"></i>
                                        </button>
                                        <button class="btn btn-danger delete-translation" 
                                                data-group="{{ translation.group }}"
                                                data-key="{{ translation.key }}"
                                                title="{{ __('common.delete') }}">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="{{ 3 + languages|length }}" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="bi bi-translate fs-1 d-block mb-2"></i>
                                        {{ __('translations.no_translations_found') }}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Add Translation Button -->
                <div class="mt-3">
                    <button class="btn btn-success" id="addTranslationBtn">
                        <i class="bi bi-plus-circle"></i> {{ __('translations.add_translation') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Multilingual Edit Modal -->
<div class="modal fade" id="multilangModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-translate"></i> {{ __('translations.edit_all_languages') }}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>{{ __('translations.group') }}:</strong> <span id="modal-group" class="badge bg-primary"></span>
                    <strong class="ms-3">{{ __('translations.key') }}:</strong> <code id="modal-key"></code>
                </div>
                
                <div id="multilang-container">
                    <!-- Language inputs will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    {{ __('common.close') }}
                </button>
                <button type="button" class="btn btn-primary" id="saveAllLanguages">
                    <i class="bi bi-save"></i> {{ __('translations.save_all_languages') }}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Add Translation Modal -->
<div class="modal fade" id="addTranslationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form id="addTranslationForm">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('translations.add_translation') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ __('translations.group') }}</label>
                                <input type="text" name="group" class="form-control" required 
                                       list="groupsList" placeholder="e.g., common, users, invoices">
                                <datalist id="groupsList">
                                    {% for grp in groups %}
                                        <option value="{{ grp }}">
                                    {% endfor %}
                                </datalist>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ __('translations.key') }}</label>
                                <input type="text" name="key" class="form-control" required 
                                       placeholder="e.g., welcome_message">
                            </div>
                        </div>
                    </div>
                    
                    <h6 class="mb-3">{{ __('translations.values_for_languages') }}</h6>
                    <div id="add-translation-languages">
                        {% for lang in languages %}
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="bi bi-translate"></i> {{ lang|upper }} - {{ __('languages.' ~ lang) }}
                            </label>
                            <textarea name="values[{{ lang }}]" class="form-control" rows="2" 
                                      {% if lang == languages[0] %}required{% endif %}></textarea>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        {{ __('common.cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> {{ __('common.save') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
.editable-cell {
    cursor: text;
    position: relative;
    padding: 0.5rem;
    transition: background-color 0.2s;
}

.editable-cell:hover {
    background-color: #f8f9fa;
}

.editable-cell.editing {
    padding: 0.25rem;
    background-color: #e9ecef;
}

.cell-content {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    min-height: 32px;
    display: flex;
    align-items: center;
}

.inline-editor {
    width: 100%;
    padding: 0.375rem 0.75rem;
    border: 2px solid #0d6efd;
    border-radius: 4px;
}

.inline-editor:focus {
    outline: none;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.table td {
    vertical-align: middle;
}

/* Highlight unsaved changes */
.has-changes {
    background-color: #fff3cd !important;
}

/* Visual feedback for saving */
.saving {
    opacity: 0.6;
    pointer-events: none;
}

.save-success {
    animation: flash-green 0.5s;
}

@keyframes flash-green {
    0% { background-color: inherit; }
    50% { background-color: #d1e7dd; }
    100% { background-color: inherit; }
}

/* Live search styles */
#clearSearch {
    display: none;
}

.text-muted {
    font-size: 0.875rem;
}

/* Smooth row transitions */
#translationsTable tbody tr {
    transition: opacity 0.3s ease;
}

#translationsTable tbody tr:not(:visible) {
    opacity: 0;
}
</style>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Available languages from server
    var availableLanguages = {{ languages|json_encode|raw }};
    var currentlyEditing = null;
    
    // Make cells editable on click
    $('.editable-cell').on('click', function(e) {
        // Don't trigger if already editing this cell
        if ($(this).hasClass('editing')) {
            return;
        }
        
        // Save any other cell being edited
        if (currentlyEditing && currentlyEditing[0] !== this) {
            saveInlineEdit(currentlyEditing);
        }
        
        var cell = $(this);
        var content = cell.find('.cell-content');
        var input = cell.find('.inline-editor');
        var lang = cell.data('lang').toUpperCase();
        var key = cell.data('group') + '.' + cell.data('key');
        
        // Switch to edit mode
        cell.addClass('editing');
        content.addClass('d-none');
        input.removeClass('d-none').focus().select();
        
        currentlyEditing = cell;
        
        // Show editing notification
        toastr.info('{{ __("translations.editing") }}: ' + key + ' (' + lang + ')', '', {
            timeOut: 1500,
            progressBar: true
        });
    });
    
    // Handle keyboard events in inline editor
    $('.inline-editor').on('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            saveInlineEdit($(this).closest('.editable-cell'));
        } else if (e.key === 'Escape') {
            cancelInlineEdit($(this).closest('.editable-cell'));
        }
    });
    
    // Save when clicking outside
    $(document).on('click', function(e) {
        if (currentlyEditing && !$(e.target).closest('.editable-cell').length) {
            saveInlineEdit(currentlyEditing);
        }
    });
    
    // Handle input changes
    $('.inline-editor').on('input', function() {
        var input = $(this);
        var cell = input.closest('.editable-cell');
        
        if (input.val() !== input.data('original')) {
            cell.addClass('has-changes');
        } else {
            cell.removeClass('has-changes');
        }
    });
    
    function saveInlineEdit(cell) {
        var input = cell.find('.inline-editor');
        var content = cell.find('.cell-content');
        var value = input.val().trim();
        var original = input.data('original') || '';
        
        // If value hasn't changed, just exit edit mode
        if (value === original) {
            exitEditMode(cell);
            toastr.info('{{ __("translations.no_changes_made") }}', '', {
                timeOut: 1500,
                progressBar: true
            });
            return;
        }
        
        // Show saving state
        cell.addClass('saving');
        
        // Save the translation
        $.ajax({
            url: '{{ base_url }}/translations/save',
            method: 'POST',
            data: {
                lang: cell.data('lang'),
                group: cell.data('group'),
                key: cell.data('key'),
                value: value,
                csrf_token: '{{ csrf_token }}'
            },
            success: function(response) {
                if (response.success) {
                    // Update the display
                    if (value) {
                        content.html(value);
                    } else {
                        content.html('<span class="text-muted">{{ __("translations.not_translated") }}</span>');
                    }
                    
                    // Update original value
                    input.data('original', value);
                    
                    // Flash success animation
                    cell.addClass('save-success');
                    setTimeout(function() {
                        cell.removeClass('save-success');
                    }, 500);
                    
                    // Exit edit mode
                    exitEditMode(cell);
                    
                    // Always show success message
                    toastr.success('{{ __("translations.translation_saved") }}', '{{ __("common.success") }}', {
                        timeOut: 2000,
                        progressBar: true
                    });
                } else {
                    toastr.error(response.message || '{{ __("common.error_occurred") }}');
                    cell.removeClass('saving');
                }
            },
            error: function(xhr) {
                var message = '{{ __("common.error_occurred") }}';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                toastr.error(message, '{{ __("common.error") }}', {
                    timeOut: 4000,
                    progressBar: true
                });
                cell.removeClass('saving');
            }
        });
    }
    
    function cancelInlineEdit(cell) {
        var input = cell.find('.inline-editor');
        var hasChanges = input.val() !== (input.data('original') || '');
        
        if (hasChanges) {
            // Show warning that changes were discarded
            toastr.warning('{{ __("translations.changes_discarded") }}', '', {
                timeOut: 2000,
                progressBar: true
            });
        }
        
        input.val(input.data('original') || '');
        exitEditMode(cell);
    }
    
    function exitEditMode(cell) {
        var content = cell.find('.cell-content');
        var input = cell.find('.inline-editor');
        
        cell.removeClass('editing saving has-changes');
        content.removeClass('d-none');
        input.addClass('d-none');
        
        if (currentlyEditing && currentlyEditing[0] === cell[0]) {
            currentlyEditing = null;
        }
    }
    
    // Multilingual edit
    $('.multilang-edit').on('click', function() {
        var group = $(this).data('group');
        var key = $(this).data('key');
        
        $('#modal-group').text(group);
        $('#modal-key').text(key);
        
        // Load all language values for this key
        $.ajax({
            url: '{{ base_url }}/translations/multilang',
            method: 'GET',
            data: {
                group: group,
                key: key
            },
            success: function(response) {
                var html = '';
                
                availableLanguages.forEach(function(lang) {
                    var value = response.translations[lang] || '';
                    var langName = lang.toUpperCase();
                    
                    html += '<div class="mb-3">';
                    html += '<label class="form-label">';
                    html += '<i class="bi bi-translate"></i> ' + langName;
                    html += '</label>';
                    html += '<textarea class="form-control multilang-input" data-lang="' + lang + '" ';
                    html += 'data-group="' + group + '" data-key="' + key + '" rows="2">' + value + '</textarea>';
                    html += '</div>';
                });
                
                $('#multilang-container').html(html);
                $('#multilangModal').modal('show');
            },
            error: function() {
                toastr.error('{{ __("common.error_occurred") }}');
            }
        });
    });
    
    // Save all languages
    $('#saveAllLanguages').on('click', function() {
        var btn = $(this);
        var translations = [];
        
        $('.multilang-input').each(function() {
            translations.push({
                lang: $(this).data('lang'),
                group: $(this).data('group'),
                key: $(this).data('key'),
                value: $(this).val()
            });
        });
        
        btn.prop('disabled', true);
        
        // Save each translation
        var savePromises = translations.map(function(trans) {
            return $.ajax({
                url: '{{ base_url }}/translations/save',
                method: 'POST',
                data: {
                    lang: trans.lang,
                    group: trans.group,
                    key: trans.key,
                    value: trans.value,
                    csrf_token: '{{ csrf_token }}'
                }
            });
        });
        
        Promise.all(savePromises)
            .then(function() {
                toastr.success('{{ __("translations.all_languages_saved") }}');
                $('#multilangModal').modal('hide');
                
                // Reload page if we're viewing the language that was edited
                var currentLang = '{{ currentLanguage }}';
                if (translations.some(t => t.lang === currentLang)) {
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                }
            })
            .catch(function() {
                toastr.error('{{ __("common.error_occurred") }}');
            })
            .finally(function() {
                btn.prop('disabled', false);
            });
    });
    
    // Live Search Functionality
    var searchTimeout;
    var $searchInput = $('#liveSearch');
    var $groupFilter = $('#groupFilter');
    var $showMissing = $('#showMissing');
    var $tbody = $('#translationsTable tbody');
    var $visibleCount = $('#visibleCount');
    var $clearSearch = $('#clearSearch');
    
    function filterTranslations() {
        var searchTerm = $searchInput.val().toLowerCase();
        var selectedGroup = $groupFilter.val();
        var showOnlyMissing = $showMissing.is(':checked');
        var visibleRows = 0;
        
        $tbody.find('tr').each(function() {
            var $row = $(this);
            var group = $row.find('.badge').text().toLowerCase();
            var key = $row.find('code').text().toLowerCase();
            var hasEmptyTranslation = false;
            
            // Check if any translation is missing
            $row.find('.editable-cell').each(function() {
                var content = $(this).find('.cell-content').text().trim();
                if (content === '{{ __("translations.not_translated") }}' || content === '') {
                    hasEmptyTranslation = true;
                }
            });
            
            // Check all values for search term
            var matchesSearch = false;
            if (searchTerm === '') {
                matchesSearch = true;
            } else {
                // Search in group and key
                if (group.includes(searchTerm) || key.includes(searchTerm)) {
                    matchesSearch = true;
                }
                
                // Search in translation values
                $row.find('.cell-content').each(function() {
                    if ($(this).text().toLowerCase().includes(searchTerm)) {
                        matchesSearch = true;
                    }
                });
            }
            
            // Apply filters
            var matchesGroup = selectedGroup === '' || group === selectedGroup;
            var matchesMissing = !showOnlyMissing || hasEmptyTranslation;
            
            if (matchesSearch && matchesGroup && matchesMissing) {
                $row.show();
                visibleRows++;
            } else {
                $row.hide();
            }
        });
        
        // Update visible count
        $visibleCount.text(visibleRows);
        
        // Show/hide clear button
        if (searchTerm) {
            $clearSearch.show();
        } else {
            $clearSearch.hide();
        }
    }
    
    // Trigger live search on keyup with debounce
    $searchInput.on('keyup input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(filterTranslations, 300);
    });
    
    // Trigger filter on group change
    $groupFilter.on('change', filterTranslations);
    
    // Trigger filter on checkbox change
    $showMissing.on('change', filterTranslations);
    
    // Clear search
    $clearSearch.on('click', function() {
        $searchInput.val('').trigger('input');
    });
    
    // Initial filter
    filterTranslations();
    
    // Delete translation (deletes for all languages)
    $('.delete-translation').on('click', function() {
        var btn = $(this);
        var group = btn.data('group');
        var key = btn.data('key');
        
        Swal.fire({
            title: '{{ __("common.are_you_sure") }}',
            text: group + '.' + key + ' ({{ __("translations.all_languages") }})',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: '{{ __("common.yes_delete") }}',
            cancelButtonText: '{{ __("common.cancel") }}'
        }).then((result) => {
            if (result.isConfirmed) {
                // Delete for all languages
                var deletePromises = availableLanguages.map(function(lang) {
                    return $.ajax({
                        url: '{{ base_url }}/translations/delete',
                        method: 'POST',
                        data: {
                            lang: lang,
                            group: group,
                            key: key,
                            csrf_token: '{{ csrf_token }}'
                        }
                    });
                });
                
                Promise.all(deletePromises)
                    .then(function() {
                        var rowId = 'translation-' + group + '-' + key.replace(/\./g, '_');
                        $('#' + rowId).fadeOut(function() {
                            $(this).remove();
                        });
                        toastr.success('{{ __("translations.translation_deleted") }}');
                    })
                    .catch(function() {
                        toastr.error('{{ __("common.error_occurred") }}');
                    });
            }
        });
    });
    
    // Add translation modal
    $('#addTranslationBtn').on('click', function() {
        $('#addTranslationModal').modal('show');
    });
    
    // Add translation form (now saves for all languages)
    $('#addTranslationForm').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var group = form.find('[name="group"]').val();
        var key = form.find('[name="key"]').val();
        var values = {};
        
        // Collect values for each language
        form.find('[name^="values["]').each(function() {
            var lang = $(this).attr('name').match(/values\[(\w+)\]/)[1];
            values[lang] = $(this).val();
        });
        
        // Save for each language
        var savePromises = [];
        
        for (var lang in values) {
            if (values[lang]) {
                savePromises.push(
                    $.ajax({
                        url: '{{ base_url }}/translations/save',
                        method: 'POST',
                        data: {
                            lang: lang,
                            group: group,
                            key: key,
                            value: values[lang],
                            csrf_token: '{{ csrf_token }}'
                        }
                    })
                );
            }
        }
        
        Promise.all(savePromises)
            .then(function() {
                $('#addTranslationModal').modal('hide');
                form[0].reset();
                toastr.success('{{ __("translations.translation_saved") }}');
                
                // Reload page to show new translation
                setTimeout(function() {
                    window.location.reload();
                }, 1000);
            })
            .catch(function() {
                toastr.error('{{ __("common.error_occurred") }}');
            });
    });
});
</script>
{% endblock %}