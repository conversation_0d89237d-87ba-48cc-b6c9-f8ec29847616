<?php

namespace App\Models;

use App\Core\Model;
use App\Helpers\MoneyHelper;

class InvoiceItem extends Model
{
    protected $table = 'invoice_items';
    
    /**
     * Fields that contain monetary values
     */
    protected $monetary = [
        'unit_price',
        'vat_amount',
        'total_amount',
        'discount_amount',
        'cns_amount',
        'patient_amount',
        'secretary_amount'
    ];
    
    protected $fillable = [
        'invoice_id',
        'description',
        'quantity',
        'unit_price',
        'vat_rate_id',
        'vat_rate',
        'vat_amount',
        'total_amount',
        'discount_percentage',
        'discount_amount',
        'item_order',
        'catalog_item_id',
        'notes',
        'sort_order' // Add for backward compatibility
    ];
    
    protected $casts = [
        'quantity' => 'decimal:2',
        'unit_price' => 'decimal:2',
        'vat_rate' => 'decimal:2',
        'vat_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'item_order' => 'integer'
    ];
    
    /**
     * Get the invoice this item belongs to
     */
    public function getInvoice()
    {
        return Invoice::find($this->invoice_id);
    }
    
    /**
     * Get the VAT rate
     */
    public function getVatRate()
    {
        if ($this->vat_rate_id) {
            return ConfigVatRate::find($this->vat_rate_id);
        }
        return null;
    }
    
    /**
     * Get the catalog item if linked
     */
    public function getCatalogItem()
    {
        if ($this->catalog_item_id) {
            return CatalogItem::find($this->catalog_item_id);
        }
        return null;
    }
    
    /**
     * Calculate line totals
     */
    public function calculateTotals()
    {
        $subtotal = MoneyHelper::round($this->quantity * $this->unit_price);
        
        // Apply discount if any
        if ($this->discount_percentage > 0) {
            $this->discount_amount = MoneyHelper::round($subtotal * ($this->discount_percentage / 100));
            $subtotal = MoneyHelper::round($subtotal - $this->discount_amount);
        }
        
        // Calculate VAT
        $this->vat_amount = MoneyHelper::round($subtotal * ($this->vat_rate / 100));
        $this->total_amount = MoneyHelper::round($subtotal + $this->vat_amount);
        
        return $this;
    }
}