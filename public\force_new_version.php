<?php
/**
 * Force a completely new version by renaming the template
 */

// Clear all caches
if (function_exists('opcache_reset')) {
    opcache_reset();
}

// The template file
$originalFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$backupFile = dirname(__DIR__) . '/app/views/invoices/create-modern.twig.backup_' . date('YmdHis');
$tempFile = dirname(__DIR__) . '/app/views/invoices/create-modern-v2.twig';

// Create backup
copy($originalFile, $backupFile);

// Copy to a new filename temporarily
copy($originalFile, $tempFile);

// Now modify the routes to use the new template
$bootstrapFile = dirname(__DIR__) . '/app/config/bootstrap.php';
$bootstrapContent = file_get_contents($bootstrapFile);

// Check if we need to update the template name in routes
$routesFile = dirname(__DIR__) . '/app/modules/invoices/routes.php';
if (file_exists($routesFile)) {
    $routesContent = file_get_contents($routesFile);
    
    // Replace create-modern with create-modern-v2
    $routesContent = str_replace('create-modern', 'create-modern-v2', $routesContent);
    file_put_contents($routesFile, $routesContent);
}

// Clear Twig cache completely
$twigCacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($twigCacheDir)) {
    system("rm -rf " . escapeshellarg($twigCacheDir) . "/*");
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Force New Version</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 40px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            padding: 15px 30px;
            background: #dc3545;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 18px;
            margin: 10px;
        }
        .button:hover {
            background: #c82333;
        }
        code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Cache Fix</h1>
        
        <div class="warning">
            <h3>⚠️ Temporary Solution Applied</h3>
            <p>The template has been copied to a new filename to force browsers to load the fresh version.</p>
            <ul>
                <li>Original: <code>create-modern.twig</code></li>
                <li>Temporary: <code>create-modern-v2.twig</code></li>
                <li>Backup created: <code><?php echo basename($backupFile); ?></code></li>
            </ul>
        </div>
        
        <div class="success">
            <h3>✅ What was done:</h3>
            <ol>
                <li>Created backup of original template</li>
                <li>Copied template to new filename</li>
                <li>Updated routes to use new template</li>
                <li>Cleared all server caches</li>
            </ol>
        </div>
        
        <p style="text-align: center; margin: 30px 0;">
            <a href="/fit/public/invoices/create?_v=<?php echo time(); ?>" class="button">
                🚀 Open Invoice Page (New Version)
            </a>
        </p>
        
        <div class="warning">
            <h3>📝 To Revert Later:</h3>
            <p>After confirming everything works:</p>
            <ol>
                <li>Delete <code>create-modern-v2.twig</code></li>
                <li>Update routes back to use <code>create-modern.twig</code></li>
                <li>Clear caches again</li>
            </ol>
        </div>
    </div>
</body>
</html>