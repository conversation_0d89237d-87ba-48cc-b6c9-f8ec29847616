<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING PDF COLUMN CONFIGURATION ===\n\n";

try {
    $db = Flight::db();
    
    // Get the latest invoice
    $stmt = $db->query("
        SELECT i.*, dt.code as doc_type_code 
        FROM invoices i
        LEFT JOIN config_document_types dt ON i.document_type_id = dt.id
        WHERE i.invoice_number = 'FAC-2025-0187'
        LIMIT 1
    ");
    $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($invoice) {
        echo "Invoice: {$invoice['invoice_number']}\n";
        echo "Document Type ID: " . ($invoice['document_type_id'] ?? 'NULL') . "\n";
        echo "Document Type Code: " . ($invoice['doc_type_code'] ?? 'NULL') . "\n\n";
        
        // Check column configuration
        if ($invoice['document_type_id']) {
            $visibleColumns = \App\Models\DocumentTypeColumnConfig::getVisibleInvoiceColumns($invoice['document_type_id']);
            echo "Visible Columns Configuration:\n";
            foreach ($visibleColumns as $col => $config) {
                echo "- $col: " . ($config['visible'] ? 'visible' : 'hidden');
                if (!empty($config['custom_name'])) {
                    echo " (custom name: {$config['custom_name']})";
                }
                echo "\n";
            }
        } else {
            echo "No document type ID - using default columns\n";
        }
        
        echo "\n";
        
        // Get invoice lines
        $stmt = $db->prepare("
            SELECT * FROM invoice_lines 
            WHERE invoice_id = ?
            ORDER BY sort_order ASC, id ASC
        ");
        $stmt->execute([$invoice['id']]);
        $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        echo "Invoice Lines (" . count($lines) . " total):\n";
        foreach ($lines as $i => $line) {
            echo sprintf("%d. ID: %d, Description: %s, Qty: %.2f, Price: %.2f€\n",
                $i + 1,
                $line['id'],
                $line['description'],
                $line['quantity'],
                $line['unit_price']
            );
        }
    } else {
        echo "Invoice not found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}