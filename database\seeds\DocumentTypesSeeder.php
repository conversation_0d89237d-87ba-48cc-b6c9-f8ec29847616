<?php

namespace App\Database\Seeds;

use Flight;

class DocumentTypesSeeder
{
    public function run()
    {
        $db = Flight::db();
        
        // Check if document types already exist
        $stmt = $db->query("SELECT COUNT(*) as count FROM config_invoice_types");
        $result = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "Document types already exist, skipping...\n";
            return;
        }
        
        $documentTypes = [
            [
                'name' => json_encode([
                    'fr' => 'Facture Standard',
                    'en' => 'Standard Invoice'
                ]),
                'description' => json_encode([
                    'fr' => 'Facture standard pour les services réguliers',
                    'en' => 'Standard invoice for regular services'
                ]),
                'code' => 'INV',
                'number_format' => '{prefix}-{year}-{number:5}',
                'icon' => 'bi bi-file-text',
                'color' => '#6366f1',
                'next_number' => 1,
                'is_active' => 1,
                'numbering_pattern' => 'sequential',
                'year_reset' => 1,
                'is_system' => 0
            ],
            [
                'name' => json_encode([
                    'fr' => 'Reçu',
                    'en' => 'Receipt'
                ]),
                'description' => json_encode([
                    'fr' => 'Reçu pour les paiements directs',
                    'en' => 'Receipt for direct payments'
                ]),
                'code' => 'REC',
                'number_format' => '{prefix}/{yy}{month}/{number:4}',
                'icon' => 'bi bi-receipt',
                'color' => '#10b981',
                'next_number' => 1,
                'is_active' => 1,
                'numbering_pattern' => 'sequential',
                'year_reset' => 0,
                'is_system' => 0
            ],
            [
                'name' => json_encode([
                    'fr' => 'Facture Médicale',
                    'en' => 'Medical Invoice'
                ]),
                'description' => json_encode([
                    'fr' => 'Facture pour les services médicaux',
                    'en' => 'Invoice for medical services'
                ]),
                'code' => 'MED',
                'number_format' => '{prefix}-{year}-{number:5}',
                'icon' => 'bi bi-file-earmark-medical',
                'color' => '#ef4444',
                'next_number' => 1,
                'is_active' => 1,
                'numbering_pattern' => 'sequential',
                'year_reset' => 1,
                'is_system' => 0
            ],
            [
                'name' => json_encode([
                    'fr' => 'Note de Crédit',
                    'en' => 'Credit Note'
                ]),
                'description' => json_encode([
                    'fr' => 'Note de crédit pour les remboursements',
                    'en' => 'Credit note for refunds'
                ]),
                'code' => 'CN',
                'number_format' => '{prefix}-{year}-{number:5}',
                'icon' => 'bi bi-file-earmark-arrow-down',
                'color' => '#f59e0b',
                'next_number' => 1,
                'is_active' => 1,
                'numbering_pattern' => 'sequential',
                'year_reset' => 1,
                'is_system' => 0
            ]
        ];
        
        // Insert document types
        $stmt = $db->prepare("
            INSERT INTO config_invoice_types 
            (name, description, code, number_format, icon, color, next_number, is_active, numbering_pattern, year_reset, is_system, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        foreach ($documentTypes as $type) {
            $stmt->execute([
                $type['name'],
                $type['description'],
                $type['code'],
                $type['number_format'],
                $type['icon'],
                $type['color'],
                $type['next_number'],
                $type['is_active'],
                $type['numbering_pattern'],
                $type['year_reset'],
                $type['is_system']
            ]);
            echo "Created document type: " . $type['code'] . "\n";
        }
        
        echo "Document types seeded successfully!\n";
    }
}