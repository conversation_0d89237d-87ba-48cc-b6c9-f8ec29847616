<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>';

echo "<h1>Invoice Sequence and Numbers Check</h1>";

// 1. Check current sequence
echo '<div class="section">';
echo '<h2>1. Current Document Sequences</h2>';
$stmt = $db->query("
    SELECT ds.*, dt.code as doc_type_code 
    FROM document_sequences ds
    JOIN document_types dt ON ds.document_type_id = dt.id
    WHERE ds.year = 2025 OR ds.year IS NULL
    ORDER BY ds.id DESC
");
$sequences = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<table>';
echo '<tr><th>ID</th><th>Doc Type</th><th>Year</th><th>Month</th><th>Last Number</th><th>Updated</th></tr>';
foreach ($sequences as $seq) {
    echo '<tr>';
    echo '<td>' . $seq['id'] . '</td>';
    echo '<td>' . $seq['doc_type_code'] . '</td>';
    echo '<td>' . ($seq['year'] ?? 'NULL') . '</td>';
    echo '<td>' . ($seq['month'] ?? 'NULL') . '</td>';
    echo '<td>' . $seq['last_number'] . '</td>';
    echo '<td>' . $seq['updated_at'] . '</td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';

// 2. Check existing invoices with FAC-2025 prefix
echo '<div class="section">';
echo '<h2>2. Existing FAC-2025 Invoices</h2>';
$stmt = $db->query("
    SELECT id, invoice_number, status, created_at 
    FROM invoices 
    WHERE invoice_number LIKE 'FAC-2025-%'
    ORDER BY id DESC
    LIMIT 10
");
$invoices = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<table>';
echo '<tr><th>ID</th><th>Invoice Number</th><th>Status</th><th>Created</th></tr>';
foreach ($invoices as $inv) {
    echo '<tr>';
    echo '<td>' . $inv['id'] . '</td>';
    echo '<td>' . $inv['invoice_number'] . '</td>';
    echo '<td>' . $inv['status'] . '</td>';
    echo '<td>' . $inv['created_at'] . '</td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';

// 3. Check if FAC-2025-0186 exists
echo '<div class="section">';
echo '<h2>3. Check FAC-2025-0186</h2>';
$stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
$stmt->execute(['FAC-2025-0186']);
$invoice186 = $stmt->fetch(\PDO::FETCH_ASSOC);

if ($invoice186) {
    echo '<div class="error">FAC-2025-0186 already exists!</div>';
    echo '<table>';
    echo '<tr><th>Field</th><th>Value</th></tr>';
    echo '<tr><td>ID</td><td>' . $invoice186['id'] . '</td></tr>';
    echo '<tr><td>Status</td><td>' . $invoice186['status'] . '</td></tr>';
    echo '<tr><td>Created</td><td>' . $invoice186['created_at'] . '</td></tr>';
    echo '</table>';
} else {
    echo '<div class="success">FAC-2025-0186 does not exist - available for use!</div>';
}
echo '</div>';

// 4. Solution
echo '<div class="section">';
echo '<h2>4. Solution</h2>';

if ($invoice186) {
    echo '<div class="warning">';
    echo '<strong>⚠️ FAC-2025-0186 already exists!</strong><br>';
    echo 'The sequence is correct (185), but invoice 0186 was already created.<br>';
    echo 'You need to either:<br>';
    echo '1. Delete or change the existing FAC-2025-0186 invoice<br>';
    echo '2. Set the sequence to 186 so the next invoice will be 0187<br>';
    echo '3. Manually change FAC-2025-0187 to a different number';
    echo '</div>';
} else {
    echo '<div class="success">';
    echo '<strong>✓ You can now change FAC-2025-0187 to FAC-2025-0186</strong><br>';
    echo '1. Go to invoice edit page<br>';
    echo '2. Change the invoice number from FAC-2025-0187 to FAC-2025-0186<br>';
    echo '3. Save the invoice';
    echo '</div>';
}

// Show what would happen on next invoice creation
$nextNumber = ($sequences[0]['last_number'] ?? 0) + 1;
echo '<div class="warning">';
echo '<strong>Next Invoice Creation:</strong><br>';
echo 'Current sequence last_number: ' . ($sequences[0]['last_number'] ?? 'unknown') . '<br>';
echo 'Next invoice will get number: FAC-2025-' . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
echo '</div>';

echo '</div>';