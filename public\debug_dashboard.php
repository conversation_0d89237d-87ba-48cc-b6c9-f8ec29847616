<?php
/**
 * Debug Dashboard Access
 * This file helps diagnose why the dashboard is not loading
 */

// Enable full error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

echo "<h1>Dashboard Debug Information</h1>";
echo "<pre>";

// 1. Check PHP version and extensions
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Loaded Extensions: " . implode(', ', get_loaded_extensions()) . "\n\n";

// 2. Check paths
echo "Current Directory: " . __DIR__ . "\n";
echo "APP_PATH would be: " . dirname(__DIR__) . "\n\n";

// 3. Check critical files
$files_to_check = [
    'vendor/autoload.php' => dirname(__DIR__) . '/vendor/autoload.php',
    '.env' => dirname(__DIR__) . '/.env',
    'app/config/bootstrap.php' => dirname(__DIR__) . '/app/config/bootstrap.php',
    'app/config/routes.php' => dirname(__DIR__) . '/app/config/routes.php',
];

echo "File Existence Check:\n";
foreach ($files_to_check as $name => $path) {
    echo "- $name: " . (file_exists($path) ? "EXISTS" : "NOT FOUND") . "\n";
}
echo "\n";

// 4. Try to load .env file
if (file_exists(dirname(__DIR__) . '/.env')) {
    echo "Loading .env file...\n";
    $env_contents = file_get_contents(dirname(__DIR__) . '/.env');
    $env_lines = explode("\n", $env_contents);
    foreach ($env_lines as $line) {
        if (strpos($line, 'DB_') === 0 || strpos($line, 'APP_') === 0) {
            // Show only APP and DB variables (hide sensitive data)
            $parts = explode('=', $line, 2);
            if (count($parts) == 2) {
                $key = $parts[0];
                $value = $parts[1];
                if (strpos($key, 'PASSWORD') !== false) {
                    $value = '***HIDDEN***';
                }
                echo "  $key = $value\n";
            }
        }
    }
    echo "\n";
}

// 5. Test database connection
echo "Testing Database Connection:\n";
try {
    // Load composer autoloader if it exists
    if (file_exists(dirname(__DIR__) . '/vendor/autoload.php')) {
        require_once dirname(__DIR__) . '/vendor/autoload.php';
        
        // Load .env file
        if (file_exists(dirname(__DIR__) . '/.env')) {
            $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
            $dotenv->load();
        }
    }
    
    $dbHost = $_ENV['DB_HOST'] ?? '127.0.0.1';
    $dbName = $_ENV['DB_DATABASE'] ?? 'fitapp';
    $dbUser = $_ENV['DB_USERNAME'] ?? 'root';
    $dbPass = $_ENV['DB_PASSWORD'] ?? '';
    
    echo "  Host: $dbHost\n";
    echo "  Database: $dbName\n";
    echo "  Username: $dbUser\n";
    echo "  Password: " . ($dbPass ? '***SET***' : '***EMPTY***') . "\n";
    
    $pdo = new PDO(
        "mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4",
        $dbUser,
        $dbPass,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
    
    echo "  Status: CONNECTION SUCCESSFUL\n";
    
    // Check if users table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    $table_exists = $stmt->fetch();
    echo "  Users table: " . ($table_exists ? "EXISTS" : "NOT FOUND") . "\n";
    
} catch (Exception $e) {
    echo "  Status: CONNECTION FAILED\n";
    echo "  Error: " . $e->getMessage() . "\n";
}

echo "\n";

// 6. Check session
echo "Session Information:\n";
session_start();
echo "  Session ID: " . session_id() . "\n";
echo "  Session Status: " . session_status() . "\n";
echo "  Session Data: " . print_r($_SESSION, true) . "\n";

echo "</pre>";

echo "<hr>";
echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>If vendor/autoload.php is missing, run: composer install</li>";
echo "<li>If database connection fails, check your .env file settings</li>";
echo "<li>If files are missing, check your project structure</li>";
echo "<li>Try accessing <a href='index.php'>index.php</a> directly</li>";
echo "</ol>";