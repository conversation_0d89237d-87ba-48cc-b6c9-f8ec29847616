{"summary": {"total_tests": 37, "passed": 10, "failed": 27, "pass_rate": 27.03, "duration": 1.43, "timestamp": "2025-07-28 09:58:41"}, "results": [{"method": "GET", "path": "/", "url": "http://localhost/fit/public/", "description": "Homepage/Dashboard", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 30.46, "error": "", "timestamp": "2025-07-28 09:58:39"}, {"method": "GET", "path": "/dashboard", "url": "http://localhost/fit/public/dashboard", "description": "Dashboard", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 30.24, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/api/dashboard/stats", "url": "http://localhost/fit/public/api/dashboard/stats", "description": "Dashboard Stats API", "http_code": 200, "status": "OK", "passed": true, "response_time": 36.16, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/api/dashboard/revenue-chart", "url": "http://localhost/fit/public/api/dashboard/revenue-chart", "description": "Revenue Chart API", "http_code": 200, "status": "OK", "passed": true, "response_time": 34.45, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/api/dashboard/invoice-status-chart", "url": "http://localhost/fit/public/api/dashboard/invoice-status-chart", "description": "Invoice Status Chart API", "http_code": 200, "status": "OK", "passed": true, "response_time": 37.5, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/login", "url": "http://localhost/fit/public/login", "description": "<PERSON><PERSON>", "http_code": 200, "status": "OK", "passed": true, "response_time": 64.63, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/logout", "url": "http://localhost/fit/public/logout", "description": "Logout", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 26.82, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config", "url": "http://localhost/fit/public/config", "description": "Configuration Dashboard", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 37.07, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/email-templates", "url": "http://localhost/fit/public/config/email-templates", "description": "Email Templates List", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 42.2, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/email-templates/create", "url": "http://localhost/fit/public/config/email-templates/create", "description": "Create <PERSON><PERSON>", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 27.19, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/email-templates/1/edit", "url": "http://localhost/fit/public/config/email-templates/1/edit", "description": "Edit <PERSON><PERSON>late", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 25.89, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/vat-rates", "url": "http://localhost/fit/public/config/vat-rates", "description": "VAT Rates", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 31, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/invoice-types", "url": "http://localhost/fit/public/config/invoice-types", "description": "Invoice Types", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 38.91, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/payment-methods", "url": "http://localhost/fit/public/config/payment-methods", "description": "Payment Methods", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 30.65, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/payment-terms", "url": "http://localhost/fit/public/config/payment-terms", "description": "Payment Terms", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 30.25, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/company", "url": "http://localhost/fit/public/config/company", "description": "Company Settings", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 26.45, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/config/system", "url": "http://localhost/fit/public/config/system", "description": "System Settings", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 34.51, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/invoices", "url": "http://localhost/fit/public/invoices", "description": "Invoices List", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 27, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/invoices/create", "url": "http://localhost/fit/public/invoices/create", "description": "Create Invoice", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 39.75, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/invoices/1", "url": "http://localhost/fit/public/invoices/1", "description": "View Invoice #1", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 24.71, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/invoices/1/edit", "url": "http://localhost/fit/public/invoices/1/edit", "description": "Edit Invoice #1", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 35.19, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/invoices/archive", "url": "http://localhost/fit/public/invoices/archive", "description": "Archived Invoices", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 26.45, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/clients", "url": "http://localhost/fit/public/clients", "description": "Clients List", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 25.59, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/clients/create", "url": "http://localhost/fit/public/clients/create", "description": "Create Client", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 28.53, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/clients/1", "url": "http://localhost/fit/public/clients/1", "description": "View Client #1", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 39.26, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/clients/1/edit", "url": "http://localhost/fit/public/clients/1/edit", "description": "Edit Client #1", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 33.46, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/api/clients/active", "url": "http://localhost/fit/public/api/clients/active", "description": "Active Clients API", "http_code": 200, "status": "OK", "passed": true, "response_time": 41.92, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/users", "url": "http://localhost/fit/public/users", "description": "Users List", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 31.81, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/users/create", "url": "http://localhost/fit/public/users/create", "description": "Create User", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 34.37, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/profile", "url": "http://localhost/fit/public/profile", "description": "User Profile", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 30.46, "error": "", "timestamp": "2025-07-28 09:58:40"}, {"method": "GET", "path": "/health", "url": "http://localhost/fit/public/health", "description": "Health Check", "http_code": 200, "status": "OK", "passed": true, "response_time": 29.04, "error": "", "timestamp": "2025-07-28 09:58:41"}, {"method": "GET", "path": "/api/vat-rates", "url": "http://localhost/fit/public/api/vat-rates", "description": "VAT Rates API", "http_code": 200, "status": "OK", "passed": true, "response_time": 36.31, "error": "", "timestamp": "2025-07-28 09:58:41"}, {"method": "GET", "path": "/api/search-billable", "url": "http://localhost/fit/public/api/search-billable", "description": "Search Billable API", "http_code": 200, "status": "OK", "passed": true, "response_time": 42.98, "error": "", "timestamp": "2025-07-28 09:58:41"}, {"method": "GET", "path": "/api/catalog/search", "url": "http://localhost/fit/public/api/catalog/search", "description": "Catalog Search API", "http_code": 200, "status": "OK", "passed": true, "response_time": 30.32, "error": "", "timestamp": "2025-07-28 09:58:41"}, {"method": "GET", "path": "/api/invoice-templates", "url": "http://localhost/fit/public/api/invoice-templates", "description": "Invoice Templates API", "http_code": 200, "status": "OK", "passed": true, "response_time": 25.53, "error": "", "timestamp": "2025-07-28 09:58:41"}, {"method": "GET", "path": "/translations", "url": "http://localhost/fit/public/translations", "description": "Translations Management", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 41.39, "error": "", "timestamp": "2025-07-28 09:58:41"}, {"method": "GET", "path": "/translations/diagnostic", "url": "http://localhost/fit/public/translations/diagnostic", "description": "Translations Diagnostic", "http_code": 303, "status": "HTTP 303", "passed": false, "response_time": 36.52, "error": "", "timestamp": "2025-07-28 09:58:41"}]}