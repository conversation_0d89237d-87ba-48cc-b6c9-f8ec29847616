<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== TESTING INVOICE CREATION ===\n\n";
    
    // First, check if we have any clients or users to bill to
    echo "1. Checking available billable entities:\n\n";
    
    // Check clients
    $stmt = $pdo->query("SELECT id, company_name, first_name, last_name FROM clients WHERE is_active = 1 LIMIT 5");
    $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo "Active clients:\n";
    if (count($clients) > 0) {
        foreach ($clients as $client) {
            $name = $client['company_name'] ?: $client['first_name'] . ' ' . $client['last_name'];
            echo "  - ID: {$client['id']}, Name: {$name}\n";
        }
    } else {
        echo "  No active clients found!\n";
    }
    
    // Check users
    echo "\nActive users:\n";
    $stmt = $pdo->query("SELECT id, username, first_name, last_name FROM users WHERE is_active = 1 LIMIT 5");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    foreach ($users as $user) {
        echo "  - ID: {$user['id']}, Name: {$user['first_name']} {$user['last_name']} ({$user['username']})\n";
    }
    
    // Check invoice structure
    echo "\n2. Checking invoices table structure:\n";
    $stmt = $pdo->query("SHOW COLUMNS FROM invoices");
    $requiredFields = ['invoice_number', 'issue_date', 'due_date', 'status', 'total'];
    $columns = [];
    while ($col = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $col['Field'];
        if (in_array($col['Field'], $requiredFields)) {
            echo "  ✓ {$col['Field']}: {$col['Type']} " . ($col['Null'] === 'YES' ? '(nullable)' : '(required)') . "\n";
        }
    }
    
    // Check for billable fields
    echo "\n3. Checking billable fields:\n";
    $billableFields = ['client_id', 'user_id', 'billable_type', 'billable_id'];
    foreach ($billableFields as $field) {
        if (in_array($field, $columns)) {
            echo "  ✓ {$field} exists\n";
        } else {
            echo "  ❌ {$field} NOT FOUND\n";
        }
    }
    
    // Try to create a test invoice
    echo "\n4. Attempting to create a test invoice:\n\n";
    
    if (count($users) > 0) {
        $testUser = $users[0];
        
        // Prepare invoice data
        $invoiceData = [
            'invoice_number' => 'TEST-' . date('Y') . '-0001',
            'document_type_id' => 1, // Invoice type
            'issue_date' => date('Y-m-d'),
            'due_date' => date('Y-m-d', strtotime('+30 days')),
            'status' => 'draft',
            'subtotal' => 100.00,
            'vat_amount' => 17.00,
            'total' => 117.00,
            'currency' => 'EUR',
            'created_by' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // Check which billing method to use
        if (in_array('billable_type', $columns)) {
            echo "Using new billable_type/billable_id system\n";
            $invoiceData['billable_type'] = 'user';
            $invoiceData['billable_id'] = $testUser['id'];
        } else {
            echo "Using old client_id/user_id system\n";
            $invoiceData['user_id'] = $testUser['id'];
        }
        
        // Build insert query
        $fields = array_keys($invoiceData);
        $placeholders = array_map(function($f) { return ':' . $f; }, $fields);
        
        $sql = "INSERT INTO invoices (" . implode(', ', $fields) . ") VALUES (" . implode(', ', $placeholders) . ")";
        
        echo "SQL: " . $sql . "\n";
        echo "Data: " . json_encode($invoiceData, JSON_PRETTY_PRINT) . "\n\n";
        
        try {
            $stmt = $pdo->prepare($sql);
            $stmt->execute($invoiceData);
            $invoiceId = $pdo->lastInsertId();
            echo "✅ SUCCESS! Created test invoice with ID: {$invoiceId}\n";
            
            // Verify it was created
            $stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ?");
            $stmt->execute([$invoiceId]);
            $created = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "\nCreated invoice details:\n";
            echo "- Invoice Number: {$created['invoice_number']}\n";
            echo "- Status: {$created['status']}\n";
            echo "- Total: {$created['total']}\n";
            
        } catch (PDOException $e) {
            echo "❌ ERROR creating invoice: " . $e->getMessage() . "\n";
            echo "Error code: " . $e->getCode() . "\n";
        }
    } else {
        echo "Cannot create test invoice - no users found!\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}