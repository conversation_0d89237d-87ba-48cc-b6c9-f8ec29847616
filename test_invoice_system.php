<?php
/**
 * Invoice System Test Script
 * Run this file to test all invoice-related functionality
 */

require_once __DIR__ . '/vendor/autoload.php';
require_once __DIR__ . '/app/config/bootstrap.php';

use App\Models\Client;
use App\Models\Invoice;
use App\Models\User;
use App\Controllers\InvoiceController;

// Color codes for output
$green = "\033[32m";
$red = "\033[31m";
$yellow = "\033[33m";
$blue = "\033[34m";
$reset = "\033[0m";

echo "\n{$blue}=== INVOICE SYSTEM TEST ==={$reset}\n\n";

// Test 1: Database Connection
echo "{$yellow}1. Testing Database Connection...{$reset}\n";
try {
    $db = Flight::db();
    if ($db) {
        echo "{$green}✓ Database connection successful{$reset}\n";
        
        // Check if we can query
        $stmt = $db->query("SELECT 1");
        if ($stmt) {
            echo "{$green}✓ Database queries working{$reset}\n";
        }
    }
} catch (Exception $e) {
    echo "{$red}✗ Database connection failed: " . $e->getMessage() . "{$reset}\n";
    exit(1);
}

// Test 2: Check Required Tables
echo "\n{$yellow}2. Checking Required Tables...{$reset}\n";
$requiredTables = [
    'clients', 'users', 'invoices', 'invoice_items', 
    'config_invoice_types', 'config_vat_rates', 'config_payment_terms',
    'document_types', 'payment_methods'
];

foreach ($requiredTables as $table) {
    try {
        $stmt = $db->query("SELECT 1 FROM $table LIMIT 1");
        echo "{$green}✓ Table '$table' exists{$reset}\n";
    } catch (Exception $e) {
        echo "{$red}✗ Table '$table' missing or inaccessible{$reset}\n";
    }
}

// Test 3: Check for Clients
echo "\n{$yellow}3. Checking for Clients...{$reset}\n";
try {
    $clientModel = new Client();
    $clients = $clientModel->getAllActive();
    $clientCount = count($clients);
    
    if ($clientCount > 0) {
        echo "{$green}✓ Found $clientCount active client(s){$reset}\n";
        echo "  First client: " . $clients[0]['first_name'] . " " . $clients[0]['last_name'] . " (" . $clients[0]['client_number'] . ")\n";
    } else {
        echo "{$red}✗ No active clients found!{$reset}\n";
        echo "{$yellow}  Creating a test client...{$reset}\n";
        
        // Create a test client
        $testClient = [
            'client_type' => 'individual',
            'first_name' => 'Test',
            'last_name' => 'Client',
            'email' => '<EMAIL>',
            'phone' => '+352 123 456',
            'address' => '123 Test Street',
            'city' => 'Luxembourg',
            'postal_code' => 'L-1234',
            'country' => 'LU',
            'is_active' => 1,
            'created_by' => 1
        ];
        
        $clientId = $clientModel->create($testClient);
        if ($clientId) {
            echo "{$green}✓ Test client created successfully (ID: $clientId){$reset}\n";
        } else {
            echo "{$red}✗ Failed to create test client{$reset}\n";
        }
    }
} catch (Exception $e) {
    echo "{$red}✗ Error checking clients: " . $e->getMessage() . "{$reset}\n";
}

// Test 4: Check for Users that can be invoiced
echo "\n{$yellow}4. Checking for Invoiceable Users...{$reset}\n";
try {
    $stmt = $db->query("SELECT * FROM users WHERE is_active = 1 AND can_be_invoiced = 1");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $userCount = count($users);
    
    if ($userCount > 0) {
        echo "{$green}✓ Found $userCount invoiceable user(s){$reset}\n";
        echo "  First user: " . $users[0]['username'] . " (" . $users[0]['email'] . ")\n";
    } else {
        echo "{$yellow}! No users marked as invoiceable{$reset}\n";
    }
} catch (Exception $e) {
    echo "{$red}✗ Error checking users: " . $e->getMessage() . "{$reset}\n";
}

// Test 5: Check Configuration
echo "\n{$yellow}5. Checking Configuration...{$reset}\n";

// Check invoice types
try {
    $stmt = $db->query("SELECT * FROM config_invoice_types WHERE is_active = 1");
    $types = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (count($types) > 0) {
        echo "{$green}✓ Found " . count($types) . " invoice type(s){$reset}\n";
    } else {
        echo "{$red}✗ No active invoice types configured{$reset}\n";
    }
} catch (Exception $e) {
    echo "{$red}✗ Error checking invoice types: " . $e->getMessage() . "{$reset}\n";
}

// Check VAT rates
try {
    $stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1");
    $rates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (count($rates) > 0) {
        echo "{$green}✓ Found " . count($rates) . " VAT rate(s){$reset}\n";
    } else {
        echo "{$red}✗ No active VAT rates configured{$reset}\n";
    }
} catch (Exception $e) {
    echo "{$red}✗ Error checking VAT rates: " . $e->getMessage() . "{$reset}\n";
}

// Check document types
try {
    $stmt = $db->query("SELECT * FROM document_types WHERE is_active = 1");
    $docTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    if (count($docTypes) > 0) {
        echo "{$green}✓ Found " . count($docTypes) . " document type(s){$reset}\n";
        foreach ($docTypes as $type) {
            echo "  - " . $type['name'] . " (" . $type['code'] . ")\n";
        }
    } else {
        echo "{$red}✗ No active document types configured{$reset}\n";
    }
} catch (Exception $e) {
    echo "{$red}✗ Error checking document types: " . $e->getMessage() . "{$reset}\n";
}

// Test 6: Test Invoice Creation
echo "\n{$yellow}6. Testing Invoice Creation...{$reset}\n";
try {
    // Get first active client
    $clientModel = new Client();
    $clients = $clientModel->getAllActive();
    
    if (empty($clients)) {
        echo "{$red}✗ Cannot test invoice creation - no clients available{$reset}\n";
    } else {
        $testClient = $clients[0];
        
        // Get configuration
        $stmt = $db->query("SELECT * FROM config_invoice_types WHERE is_active = 1 LIMIT 1");
        $invoiceType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $db->query("SELECT * FROM document_types WHERE is_active = 1 AND code = 'invoice' LIMIT 1");
        $docType = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1 AND is_default = 1 LIMIT 1");
        $vatRate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$vatRate) {
            $stmt = $db->query("SELECT * FROM config_vat_rates WHERE is_active = 1 LIMIT 1");
            $vatRate = $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        if (!$invoiceType || !$docType || !$vatRate) {
            echo "{$red}✗ Missing required configuration:{$reset}\n";
            if (!$invoiceType) echo "  - No invoice types\n";
            if (!$docType) echo "  - No document types\n";
            if (!$vatRate) echo "  - No VAT rates\n";
        } else {
            // Create test invoice data
            $invoiceModel = new Invoice();
            $invoiceNumber = $invoiceModel->generateDocumentNumber($docType['id']);
            
            $testInvoice = [
                'client_id' => $testClient['id'],
                'invoice_type_id' => $invoiceType['id'],
                'document_type_id' => $docType['id'],
                'invoice_number' => $invoiceNumber,
                'issue_date' => date('Y-m-d'),
                'due_date' => date('Y-m-d', strtotime('+30 days')),
                'status' => 'draft',
                'items' => [
                    [
                        'description' => 'Test Service',
                        'quantity' => 1,
                        'unit_price' => 100,
                        'vat_rate_id' => $vatRate['id']
                    ]
                ],
                'created_by' => 1
            ];
            
            echo "  Testing with:\n";
            echo "  - Client: " . $testClient['first_name'] . " " . $testClient['last_name'] . "\n";
            echo "  - Invoice Type: " . $invoiceType['code'] . "\n";
            echo "  - Document Type: " . $docType['name'] . "\n";
            echo "  - Invoice Number: " . $invoiceNumber . "\n";
            echo "  - VAT Rate: " . $vatRate['rate'] . "%\n";
            
            // Try to create invoice
            try {
                $result = $invoiceModel->createInvoice($testInvoice);
                if ($result && isset($result['id'])) {
                    echo "{$green}✓ Test invoice created successfully (ID: " . $result['id'] . "){$reset}\n";
                    
                    // Delete test invoice
                    $db->exec("DELETE FROM invoice_items WHERE invoice_id = " . $result['id']);
                    $db->exec("DELETE FROM invoices WHERE id = " . $result['id']);
                    echo "{$green}✓ Test invoice cleaned up{$reset}\n";
                } else {
                    echo "{$red}✗ Failed to create test invoice{$reset}\n";
                }
            } catch (Exception $e) {
                echo "{$red}✗ Invoice creation error: " . $e->getMessage() . "{$reset}\n";
            }
        }
    }
} catch (Exception $e) {
    echo "{$red}✗ Error testing invoice creation: " . $e->getMessage() . "{$reset}\n";
}

// Test 7: Check Session
echo "\n{$yellow}7. Checking Session Configuration...{$reset}\n";
if (session_status() === PHP_SESSION_ACTIVE) {
    echo "{$green}✓ Session is active{$reset}\n";
    if (isset($_SESSION['user_id'])) {
        echo "{$green}✓ User is logged in (ID: " . $_SESSION['user_id'] . "){$reset}\n";
    } else {
        echo "{$yellow}! No user logged in - this might cause permission issues{$reset}\n";
    }
} else {
    echo "{$yellow}! Session not started - running in CLI mode{$reset}\n";
}

// Test 8: Check File Permissions
echo "\n{$yellow}8. Checking File Permissions...{$reset}\n";
$directories = [
    '/storage/cache',
    '/storage/logs',
    '/public/uploads'
];

foreach ($directories as $dir) {
    $path = __DIR__ . $dir;
    if (is_dir($path)) {
        if (is_writable($path)) {
            echo "{$green}✓ Directory '$dir' is writable{$reset}\n";
        } else {
            echo "{$red}✗ Directory '$dir' is not writable{$reset}\n";
        }
    } else {
        echo "{$yellow}! Directory '$dir' does not exist{$reset}\n";
    }
}

// Summary
echo "\n{$blue}=== TEST SUMMARY ==={$reset}\n";
echo "\nIf you're having issues creating invoices, check that:\n";
echo "1. You have at least one active client in the system\n";
echo "2. All configuration tables have data (invoice types, VAT rates, document types)\n";
echo "3. You are logged in as a user with proper permissions\n";
echo "4. All required directories are writable\n";
echo "\nTo fix common issues:\n";
echo "- Create a client: Go to /clients and add a new client\n";
echo "- Check configuration: Go to /config and ensure all settings are configured\n";
echo "- Clear cache: Delete contents of /storage/cache/\n";

echo "\n{$blue}=== END OF TEST ==={$reset}\n\n";