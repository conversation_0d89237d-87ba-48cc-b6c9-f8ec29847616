# Database Backup System

This document describes the automated database backup system for the Fit360 AdminDesk application.

## Overview

The backup system provides:
- Automated daily backups via GitHub Actions
- Manual backup creation and restoration scripts
- Compressed backup files to save space
- Configurable retention policies
- Integration with GitHub repository for backup storage

## Components

### 1. Backup Script (`scripts/database-backup.php`)

The main backup script that creates MySQL database dumps with the following features:
- Reads database configuration from `.env` file
- Creates timestamped backup files
- Optional gzip compression
- Automatic cleanup of old backups based on retention policy
- Command-line interface with various options

**Usage:**
```bash
# Create a compressed backup with 7-day retention
php scripts/database-backup.php

# Create uncompressed backup with 30-day retention
php scripts/database-backup.php --no-compress --retention=30

# List existing backups
php scripts/database-backup.php --list

# Custom output directory
php scripts/database-backup.php --output-dir=custom/backup/path
```

### 2. Restore <PERSON>t (`scripts/database-restore.php`)

Restores the database from backup files with safety features:
- Lists available backup files
- Creates pre-restore backup of current database
- Handles both compressed and uncompressed backups
- Confirmation prompts to prevent accidental data loss

**Usage:**
```bash
# List available backups
php scripts/database-restore.php --list

# Restore from specific backup (with confirmation)
php scripts/database-restore.php storage/backups/fitapp_backup_2024-01-15_10-30-00.sql.gz

# Force restore without confirmation
php scripts/database-restore.php storage/backups/fitapp_backup_2024-01-15_10-30-00.sql.gz --force
```

### 3. Shell Script Wrapper (`scripts/backup.sh`)

Simple bash wrapper for the PHP backup script:
```bash
# Make executable
chmod +x scripts/backup.sh

# Run backup
./scripts/backup.sh

# Run with options
./scripts/backup.sh --no-compress --retention=14
```

### 4. GitHub Actions Workflow (`.github/workflows/database-backup.yml`)

Automated daily backups that:
- Run at 2:00 AM UTC daily
- Can be triggered manually with custom parameters
- Set up MySQL service for testing
- Restore from latest backup if available
- Create new backup and commit to repository
- Generate backup summary artifacts

## Configuration

### Environment Variables

The backup system reads database configuration from your `.env` file:
```env
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=fitapp
DB_USERNAME=root
DB_PASSWORD=your_password
```

### GitHub Secrets

For GitHub Actions to work, configure these repository secrets:
- `DB_DATABASE`: Database name (e.g., `fitapp`)
- `DB_PASSWORD`: Database password

## File Structure

```
storage/backups/
├── .gitkeep                                    # Ensures directory is tracked
├── fitapp_backup_2024-01-15_10-30-00.sql.gz  # Compressed backup
├── fitapp_backup_2024-01-14_10-30-00.sql.gz  # Previous backup
└── fitapp_pre_restore_backup_*.sql            # Pre-restore backups
```

## Backup File Naming

Backup files follow this naming convention:
- `fitapp_backup_YYYY-MM-DD_HH-MM-SS.sql` (uncompressed)
- `fitapp_backup_YYYY-MM-DD_HH-MM-SS.sql.gz` (compressed)
- `fitapp_pre_restore_backup_YYYY-MM-DD_HH-MM-SS.sql` (pre-restore backups)

## Security Considerations

1. **Sensitive Data**: Backup files contain your entire database, including sensitive information
2. **Access Control**: Ensure your GitHub repository has appropriate access controls
3. **Encryption**: Consider encrypting backup files for additional security
4. **Credentials**: Never commit database credentials to the repository

## Monitoring and Maintenance

### Checking Backup Status

1. **GitHub Actions**: Check the Actions tab in your repository for backup job status
2. **Local Backups**: Use `php scripts/database-backup.php --list` to see available backups
3. **File Sizes**: Monitor backup file sizes to detect potential issues

### Troubleshooting

**Common Issues:**

1. **Permission Errors**: Ensure the `storage/backups` directory is writable
2. **MySQL Connection**: Verify database credentials in `.env` file
3. **Disk Space**: Monitor available disk space for backup storage
4. **GitHub Actions Failures**: Check workflow logs for specific error messages

**Testing Backups:**

```bash
# Test backup creation
php scripts/database-backup.php --output-dir=test-backups

# Test restore (use a test database)
php scripts/database-restore.php test-backups/fitapp_backup_*.sql.gz --force
```

## Best Practices

1. **Regular Testing**: Periodically test backup restoration to ensure backups are valid
2. **Multiple Locations**: Consider storing backups in multiple locations for redundancy
3. **Monitoring**: Set up alerts for backup failures
4. **Documentation**: Keep this documentation updated with any changes
5. **Retention Policy**: Adjust retention periods based on your needs and storage capacity

## Manual Backup Workflow

For immediate backups outside the automated schedule:

1. **Create Backup:**
   ```bash
   php scripts/database-backup.php
   ```

2. **Verify Backup:**
   ```bash
   php scripts/database-backup.php --list
   ```

3. **Commit to Repository:**
   ```bash
   git add storage/backups/
   git commit -m "Manual database backup - $(date)"
   git push
   ```

## Recovery Procedures

### Full Database Recovery

1. **Stop Application**: Ensure no active connections to the database
2. **List Backups**: `php scripts/database-restore.php --list`
3. **Choose Backup**: Select the appropriate backup file
4. **Restore**: `php scripts/database-restore.php path/to/backup.sql.gz`
5. **Verify**: Test application functionality after restore

### Partial Recovery

For recovering specific tables or data, manually extract and execute specific parts of the SQL backup file.

## Support

For issues with the backup system:
1. Check the troubleshooting section above
2. Review GitHub Actions logs for automated backup failures
3. Verify database connectivity and permissions
4. Ensure all required dependencies are installed
