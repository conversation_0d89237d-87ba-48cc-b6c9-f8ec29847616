<?php
/**
 * Fix invoice 263 rounding issue
 */

require_once __DIR__ . '/vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

try {
    $dsn = "mysql:host={$_ENV['DB_HOST']};dbname={$_ENV['DB_NAME']};charset=utf8mb4";
    $pdo = new PDO($dsn, $_ENV['DB_USER'], $_ENV['DB_PASS'] ?? '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Fixing invoice 263 (FAC-LOC-2025-0196)...\n";
    
    // Update the invoice totals
    $stmt = $pdo->prepare("
        UPDATE invoices 
        SET vat_amount = 115.54, 
            total = 795.00 
        WHERE id = 263
    ");
    $stmt->execute();
    
    echo "Invoice updated successfully!\n";
    echo "- VAT: €115.51 → €115.54\n";
    echo "- Total: €794.97 → €795.00\n";
    
    // Clear cache if using Flight cache
    try {
        require_once __DIR__ . '/app/config/bootstrap.php';
        $cache = Flight::cache();
        $cache->delete("invoice_details_263");
        echo "Cache cleared\n";
    } catch (Exception $e) {
        echo "Could not clear cache: " . $e->getMessage() . "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}