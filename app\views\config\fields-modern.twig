{% extends "base-modern.twig" %}

{% block title %}{{ __('config.field_manager') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.field_manager') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFieldModal">
                <i class="bi bi-plus-circle me-2"></i>{{ __('config.add_field') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.field_manager_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <!-- Entity Selection -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/config/fields" class="row g-3">
                <div class="col-md-6">
                    <label for="entity" class="form-label">{{ __('config.select_entity') }}</label>
                    <select class="form-select" id="entity" name="entity" onchange="this.form.submit()">
                        <option value="patient" {{ current_entity == 'patient' ? 'selected' : '' }}>{{ __('common.patients') }}</option>
                        <option value="invoice" {{ current_entity == 'invoice' ? 'selected' : '' }}>{{ __('common.invoices') }}</option>
                        <option value="user" {{ current_entity == 'user' ? 'selected' : '' }}>{{ __('common.users') }}</option>
                        <option value="service" {{ current_entity == 'service' ? 'selected' : '' }}>{{ __('common.services') }}</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="group" class="form-label">{{ __('config.field_group') }}</label>
                    <select class="form-select" id="group" name="group" onchange="this.form.submit()">
                        <option value="">{{ __('common.all') }}</option>
                        {% for group in field_groups %}
                            <option value="{{ group.id }}" {{ current_group == group.id ? 'selected' : '' }}>{{ group.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </form>
        </div>
    </div>

    <!-- Fields Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h6 class="m-0 fw-bold text-primary">{{ __('config.custom_fields') }} - {{ current_entity|capitalize }}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="fieldsTable">
                    <thead>
                        <tr>
                            <th width="50">{{ __('common.order') }}</th>
                            <th>{{ __('config.field_name') }}</th>
                            <th>{{ __('config.field_key') }}</th>
                            <th>{{ __('config.field_type') }}</th>
                            <th>{{ __('config.field_group') }}</th>
                            <th>{{ __('config.required') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody class="sortable">
                        {% for field in fields %}
                        <tr data-id="{{ field.id }}">
                            <td class="handle">
                                <i class="bi bi-grip-vertical text-muted"></i>
                                {{ field.order }}
                            </td>
                            <td>
                                <strong>{{ field.label }}</strong>
                                {% if field.description %}
                                    <br><small class="text-muted">{{ field.description }}</small>
                                {% endif %}
                            </td>
                            <td><code>{{ field.field_key }}</code></td>
                            <td>
                                {% if field.type == 'text' %}
                                    <span class="badge bg-primary">{{ __('config.type_text') }}</span>
                                {% elseif field.type == 'number' %}
                                    <span class="badge bg-info">{{ __('config.type_number') }}</span>
                                {% elseif field.type == 'date' %}
                                    <span class="badge bg-warning">{{ __('config.type_date') }}</span>
                                {% elseif field.type == 'select' %}
                                    <span class="badge bg-success">{{ __('config.type_select') }}</span>
                                {% elseif field.type == 'checkbox' %}
                                    <span class="badge bg-secondary">{{ __('config.type_checkbox') }}</span>
                                {% elseif field.type == 'textarea' %}
                                    <span class="badge bg-dark">{{ __('config.type_textarea') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if field.group %}
                                    <span class="badge bg-light text-dark">{{ field.group.name }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if field.is_required %}
                                    <i class="bi bi-check-circle text-success"></i>
                                {% else %}
                                    <i class="bi bi-x-circle text-muted"></i>
                                {% endif %}
                            </td>
                            <td>
                                <div class="form-check form-switch">
                                    <input type="checkbox" class="form-check-input" 
                                           onchange="toggleFieldStatus({{ field.id }})" 
                                           {{ field.is_active ? 'checked' : '' }}>
                                </div>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" 
                                            class="btn btn-outline-secondary" 
                                            onclick="editField({{ field.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.edit') }}">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    {% if field.type == 'select' %}
                                    <button type="button" 
                                            class="btn btn-outline-primary" 
                                            onclick="manageOptions({{ field.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('config.manage_options') }}">
                                        <i class="bi bi-list-ul"></i>
                                    </button>
                                    {% endif %}
                                    <button type="button" 
                                            class="btn btn-outline-info" 
                                            onclick="duplicateField({{ field.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.duplicate') }}">
                                        <i class="bi bi-files"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="deleteField({{ field.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.delete') }}">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="8" class="text-center py-4 text-muted">
                                {{ __('config.no_custom_fields') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Field Modal -->
<div class="modal fade" id="addFieldModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/config/fields" id="fieldForm">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                <input type="hidden" name="field_id" id="field_id">
                
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">{{ __('config.add_field') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="field_label" class="form-label">{{ __('config.field_label') }} *</label>
                            <input type="text" class="form-control" id="field_label" name="label" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="field_key" class="form-label">{{ __('config.field_key') }} *</label>
                            <input type="text" class="form-control" id="field_key" name="field_key" required 
                                   pattern="[a-z0-9_]+" title="{{ __('config.field_key_format') }}">
                            <small class="text-muted">{{ __('config.field_key_hint') }}</small>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="field_type" class="form-label">{{ __('config.field_type') }} *</label>
                            <select class="form-select" id="field_type" name="type" required onchange="toggleFieldOptions()">
                                <option value="text">{{ __('config.type_text') }}</option>
                                <option value="number">{{ __('config.type_number') }}</option>
                                <option value="date">{{ __('config.type_date') }}</option>
                                <option value="select">{{ __('config.type_select') }}</option>
                                <option value="checkbox">{{ __('config.type_checkbox') }}</option>
                                <option value="textarea">{{ __('config.type_textarea') }}</option>
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="field_entity" class="form-label">{{ __('config.entity') }} *</label>
                            <select class="form-select" id="field_entity" name="entity" required>
                                <option value="patient" {{ current_entity == 'patient' ? 'selected' : '' }}>{{ __('common.patients') }}</option>
                                <option value="invoice" {{ current_entity == 'invoice' ? 'selected' : '' }}>{{ __('common.invoices') }}</option>
                                <option value="user" {{ current_entity == 'user' ? 'selected' : '' }}>{{ __('common.users') }}</option>
                                <option value="service" {{ current_entity == 'service' ? 'selected' : '' }}>{{ __('common.services') }}</option>
                            </select>
                        </div>
                        
                        <div class="col-md-12">
                            <label for="field_description" class="form-label">{{ __('config.field_description') }}</label>
                            <textarea class="form-control" id="field_description" name="description" rows="2"></textarea>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="field_group" class="form-label">{{ __('config.field_group') }}</label>
                            <select class="form-select" id="field_group" name="group_id">
                                <option value="">{{ __('common.none') }}</option>
                                {% for group in field_groups %}
                                    <option value="{{ group.id }}">{{ group.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="field_default" class="form-label">{{ __('config.default_value') }}</label>
                            <input type="text" class="form-control" id="field_default" name="default_value">
                        </div>
                        
                        <!-- Options for Select Field -->
                        <div class="col-12 d-none" id="selectOptions">
                            <label class="form-label">{{ __('config.field_options') }}</label>
                            <div id="optionsList">
                                <div class="input-group mb-2">
                                    <input type="text" class="form-control" name="options[]" placeholder="{{ __('config.option_value') }}">
                                    <button type="button" class="btn btn-outline-secondary" onclick="addOption()">
                                        <i class="bi bi-plus"></i>
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted">{{ __('config.options_hint') }}</small>
                        </div>
                        
                        <!-- Field Settings -->
                        <div class="col-12">
                            <h6>{{ __('config.field_settings') }}</h6>
                            <div class="row g-3">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_required" name="is_required" value="1">
                                        <label class="form-check-label" for="is_required">
                                            {{ __('config.required_field') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="is_searchable" name="is_searchable" value="1">
                                        <label class="form-check-label" for="is_searchable">
                                            {{ __('config.searchable_field') }}
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="show_in_list" name="show_in_list" value="1">
                                        <label class="form-check-label" for="show_in_list">
                                            {{ __('config.show_in_list') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('common.save') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
<script>
// Initialize Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Sortable fields
if (document.querySelector('.sortable')) {
    new Sortable(document.querySelector('.sortable'), {
        handle: '.handle',
        animation: 150,
        onEnd: function(evt) {
            const order = [];
            document.querySelectorAll('.sortable tr').forEach((row, index) => {
                if (row.dataset.id) {
                    order.push({
                        id: row.dataset.id,
                        order: index + 1
                    });
                }
            });
            
            // Save new order
            fetch('{{ base_url }}/config/fields/reorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': '{{ csrf_token }}'
                },
                body: JSON.stringify({ order: order })
            });
        }
    });
}

// Generate field key from label
document.getElementById('field_label')?.addEventListener('input', function() {
    const key = this.value.toLowerCase()
        .replace(/[^a-z0-9]+/g, '_')
        .replace(/^_+|_+$/g, '');
    document.getElementById('field_key').value = key;
});

// Toggle field options
function toggleFieldOptions() {
    const type = document.getElementById('field_type').value;
    const optionsDiv = document.getElementById('selectOptions');
    
    if (type === 'select') {
        optionsDiv.classList.remove('d-none');
    } else {
        optionsDiv.classList.add('d-none');
    }
}

// Add option field
function addOption() {
    const optionHtml = `
        <div class="input-group mb-2">
            <input type="text" class="form-control" name="options[]" placeholder="{{ __('config.option_value') }}">
            <button type="button" class="btn btn-outline-danger" onclick="removeOption(this)">
                <i class="bi bi-x"></i>
            </button>
        </div>
    `;
    document.getElementById('optionsList').insertAdjacentHTML('beforeend', optionHtml);
}

// Remove option field
function removeOption(btn) {
    btn.closest('.input-group').remove();
}

// Edit field
function editField(id) {
    // Load field data via AJAX and populate form
    fetch('{{ base_url }}/config/fields/' + id)
        .then(response => response.json())
        .then(data => {
            document.getElementById('field_id').value = data.id;
            document.getElementById('field_label').value = data.label;
            document.getElementById('field_key').value = data.field_key;
            document.getElementById('field_type').value = data.type;
            document.getElementById('field_entity').value = data.entity;
            document.getElementById('field_description').value = data.description || '';
            document.getElementById('field_group').value = data.group_id || '';
            document.getElementById('field_default').value = data.default_value || '';
            document.getElementById('is_required').checked = data.is_required;
            document.getElementById('is_searchable').checked = data.is_searchable;
            document.getElementById('show_in_list').checked = data.show_in_list;
            
            document.getElementById('modalTitle').textContent = '{{ __("config.edit_field") }}';
            document.getElementById('fieldForm').action = '{{ base_url }}/config/fields/' + id;
            
            toggleFieldOptions();
            
            const modal = new bootstrap.Modal(document.getElementById('addFieldModal'));
            modal.show();
        });
}

// Delete field
function deleteField(id) {
    if (confirm('{{ __("config.delete_field_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/config/fields/' + id;
        
        const method = document.createElement('input');
        method.type = 'hidden';
        method.name = '_method';
        method.value = 'DELETE';
        form.appendChild(method);
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Toggle field status
function toggleFieldStatus(id) {
    fetch('{{ base_url }}/config/fields/' + id + '/toggle', {
        method: 'POST',
        headers: {
            'X-CSRF-Token': '{{ csrf_token }}'
        }
    });
}

// Duplicate field
function duplicateField(id) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{{ base_url }}/config/fields/' + id + '/duplicate';
    
    const csrf = document.createElement('input');
    csrf.type = 'hidden';
    csrf.name = 'csrf_token';
    csrf.value = '{{ csrf_token }}';
    form.appendChild(csrf);
    
    document.body.appendChild(form);
    form.submit();
}

// Manage select options
function manageOptions(id) {
    window.location.href = '{{ base_url }}/config/fields/' + id + '/options';
}

// Reset modal on close
document.getElementById('addFieldModal').addEventListener('hidden.bs.modal', function() {
    document.getElementById('fieldForm').reset();
    document.getElementById('field_id').value = '';
    document.getElementById('modalTitle').textContent = '{{ __("config.add_field") }}';
    document.getElementById('fieldForm').action = '{{ base_url }}/config/fields';
    document.getElementById('selectOptions').classList.add('d-none');
});
</script>

<style>
/* Fix for dropdown positioning in tables */
.table-responsive .dropdown {
    position: static;
}

.table-responsive .dropdown-menu {
    position: absolute !important;
    transform: translate3d(0px, 0px, 0px) !important;
    will-change: transform !important;
}
</style>
{% endblock %}