/**
 * TableHelper v2 Optimized - Enhanced performance version
 * Features: Live search, filter persistence, import/export, bulk actions, sorting, column reordering
 * Optimizations: Debouncing, virtual scrolling support, request cancellation, caching
 */

class TableHelperOptimized {
    constructor(options) {
        this.options = {
            tableId: options.tableId || 'data-table',
            searchInputId: options.searchInputId || 'search',
            searchColumns: options.searchColumns || [],
            filters: options.filters || [],
            storageKey: options.storageKey || 'table_filters',
            exportFormats: options.exportFormats || ['csv', 'excel', 'pdf'],
            exportUrl: options.exportUrl || '',
            bulkActions: options.bulkActions || [],
            bulkActionUrl: options.bulkActionUrl || '',
            sortable: options.sortable !== false,
            reorderable: false,
            defaultSort: options.defaultSort || null,
            columnOrder: options.columnOrder || null,
            debounceDelay: options.debounceDelay || 300,
            virtualScroll: options.virtualScroll || false,
            pageSize: options.pageSize || 50,
            translations: options.translations || {
                search: 'Search...',
                noResults: 'No results found',
                bulkActions: 'Bulk Actions',
                export: 'Export',
                import: 'Import',
                reset: 'Reset Filters',
                results: 'results',
                selected: 'selected',
                sortAsc: 'Sort ascending',
                sortDesc: 'Sort descending',
                dragToReorder: 'Drag to reorder columns',
                stateSaved: 'Table state saved'
            }
        };
        
        // Performance optimizations
        this.debounceTimers = {};
        this.searchCache = new Map();
        this.currentRequest = null;
        this.visibleRows = new Set();
        
        // State
        this.currentSort = null;
        this.sortOrder = [];
        this.columnOrder = [];
        this.originalColumnOrder = [];
        this.draggedColumn = null;
        this.saveIndicator = null;
        
        // Use requestIdleCallback for non-critical initialization
        if ('requestIdleCallback' in window) {
            requestIdleCallback(() => this.init());
        } else {
            this.init();
        }
    }
    
    init() {
        this.table = document.getElementById(this.options.tableId);
        this.searchInput = document.getElementById(this.options.searchInputId);
        
        if (!this.table) {
            console.error('Table not found:', this.options.tableId);
            return;
        }
        
        // Use DocumentFragment for better performance
        this.fragment = document.createDocumentFragment();
        
        // Initialize column order arrays first
        this.initializeColumnOrder();
        
        // Setup features with optimized event handlers
        this.setupSearch();
        this.setupFilters();
        this.setupBulkActions();
        
        // Load saved state before setting up sorting and reordering
        this.loadSavedState();
        
        // Setup sorting and reordering after state is loaded
        this.setupSorting();
        this.setupColumnReordering();
        
        // Apply saved sort if exists
        if (this.currentSort) {
            requestAnimationFrame(() => {
                this.sortTable(this.currentSort.column, this.currentSort.direction);
            });
        }
        
        // Create save indicator
        this.createSaveIndicator();
        
        // Setup virtual scrolling if enabled
        if (this.options.virtualScroll) {
            this.setupVirtualScroll();
        }
    }
    
    // Optimized debounce function
    debounce(func, wait, immediate = false) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                timeout = null;
                if (!immediate) func(...args);
            };
            const callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func(...args);
        };
    }
    
    // Setup optimized search with caching and request cancellation
    setupSearch() {
        if (!this.searchInput) return;
        
        const performSearch = (value) => {
            const searchTerm = value.toLowerCase().trim();
            
            // Check cache first
            if (this.searchCache.has(searchTerm)) {
                this.applySearchResults(this.searchCache.get(searchTerm));
                return;
            }
            
            // Cancel previous request if exists
            if (this.currentRequest && typeof this.currentRequest.abort === 'function') {
                this.currentRequest.abort();
            }
            
            const rows = Array.from(this.table.querySelectorAll('tbody tr'));
            const results = [];
            
            // Use requestAnimationFrame for smooth UI updates
            const processRows = (startIndex = 0) => {
                const batchSize = 50;
                const endIndex = Math.min(startIndex + batchSize, rows.length);
                
                for (let i = startIndex; i < endIndex; i++) {
                    const row = rows[i];
                    let found = false;
                    
                    if (searchTerm === '') {
                        found = true;
                    } else {
                        // Only search specified columns for better performance
                        const cells = this.options.searchColumns.length > 0
                            ? this.options.searchColumns.map(index => row.cells[index])
                            : row.cells;
                        
                        for (const cell of cells) {
                            if (cell && cell.textContent.toLowerCase().includes(searchTerm)) {
                                found = true;
                                break;
                            }
                        }
                    }
                    
                    results.push({ row, visible: found });
                }
                
                if (endIndex < rows.length) {
                    requestAnimationFrame(() => processRows(endIndex));
                } else {
                    // Cache results
                    this.searchCache.set(searchTerm, results);
                    
                    // Limit cache size
                    if (this.searchCache.size > 50) {
                        const firstKey = this.searchCache.keys().next().value;
                        this.searchCache.delete(firstKey);
                    }
                    
                    this.applySearchResults(results);
                }
            };
            
            requestAnimationFrame(() => processRows());
        };
        
        // Debounced search
        const debouncedSearch = this.debounce(performSearch, this.options.debounceDelay);
        
        this.searchInput.addEventListener('input', (e) => {
            debouncedSearch(e.target.value);
        });
        
        // Clear cache on table changes
        this.table.addEventListener('DOMNodeInserted', () => {
            this.searchCache.clear();
        });
    }
    
    // Apply search results with optimized DOM manipulation
    applySearchResults(results) {
        // Batch DOM updates
        requestAnimationFrame(() => {
            results.forEach(({ row, visible }) => {
                if (visible) {
                    row.style.display = '';
                    row.classList.remove('d-none');
                } else {
                    row.style.display = 'none';
                    row.classList.add('d-none');
                }
            });
            
            this.updateResultsCount();
        });
    }
    
    // Optimized sorting with Web Workers support
    setupSorting() {
        if (!this.options.sortable) return;
        
        const headers = this.table.querySelectorAll('thead th[data-sortable="true"]');
        
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.classList.add('sortable');
            
            // Use event delegation for better performance
            header.addEventListener('click', (e) => {
                e.preventDefault();
                const columnIndex = parseInt(header.getAttribute('data-column-index') || index);
                const currentDirection = header.getAttribute('data-sort-direction') || 'none';
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';
                
                this.sortTable(columnIndex, newDirection);
            });
        });
    }
    
    // Optimized sort function
    sortTable(columnIndex, direction) {
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        // Show loading state
        this.showLoading();
        
        // Use requestIdleCallback for sorting large datasets
        const performSort = () => {
            const sortedRows = rows.sort((a, b) => {
                const aCell = a.cells[columnIndex];
                const bCell = b.cells[columnIndex];
                
                if (!aCell || !bCell) return 0;
                
                // Extract values with caching
                const aValue = this.getSortValue(aCell);
                const bValue = this.getSortValue(bCell);
                
                // Numeric comparison
                if (!isNaN(aValue) && !isNaN(bValue)) {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                }
                
                // String comparison
                const result = aValue.toString().localeCompare(bValue.toString(), undefined, {
                    numeric: true,
                    sensitivity: 'base'
                });
                
                return direction === 'asc' ? result : -result;
            });
            
            // Use DocumentFragment for batch DOM updates
            const fragment = document.createDocumentFragment();
            sortedRows.forEach(row => fragment.appendChild(row));
            tbody.appendChild(fragment);
            
            // Update sort indicators
            this.updateSortIndicators(columnIndex, direction);
            
            // Save state
            this.currentSort = { column: columnIndex, direction };
            this.saveState();
            
            // Hide loading state
            this.hideLoading();
        };
        
        if ('requestIdleCallback' in window) {
            requestIdleCallback(performSort);
        } else {
            setTimeout(performSort, 0);
        }
    }
    
    // Extract sort value with caching
    getSortValue(cell) {
        // Check for data-sort-value attribute first
        if (cell.hasAttribute('data-sort-value')) {
            return cell.getAttribute('data-sort-value');
        }
        
        // Extract text content
        const text = cell.textContent.trim();
        
        // Try to parse as number
        const num = parseFloat(text.replace(/[^0-9.-]/g, ''));
        if (!isNaN(num)) return num;
        
        // Try to parse as date
        const date = Date.parse(text);
        if (!isNaN(date)) return date;
        
        // Return as string
        return text.toLowerCase();
    }
    
    // Virtual scrolling for large tables
    setupVirtualScroll() {
        const tbody = this.table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        if (rows.length < this.options.pageSize * 2) return;
        
        // Create viewport
        const viewport = document.createElement('div');
        viewport.style.height = '500px';
        viewport.style.overflow = 'auto';
        viewport.style.position = 'relative';
        
        // Create spacer
        const spacer = document.createElement('div');
        spacer.style.height = `${rows.length * 40}px`; // Assuming 40px row height
        
        viewport.appendChild(spacer);
        tbody.parentNode.insertBefore(viewport, tbody);
        
        // Handle scroll
        let renderTimeout;
        viewport.addEventListener('scroll', () => {
            clearTimeout(renderTimeout);
            renderTimeout = setTimeout(() => {
                this.renderVisibleRows(viewport, rows);
            }, 16); // 60fps
        });
        
        // Initial render
        this.renderVisibleRows(viewport, rows);
    }
    
    // Render only visible rows for performance
    renderVisibleRows(viewport, rows) {
        const scrollTop = viewport.scrollTop;
        const viewportHeight = viewport.clientHeight;
        const rowHeight = 40;
        
        const startIndex = Math.floor(scrollTop / rowHeight);
        const endIndex = Math.ceil((scrollTop + viewportHeight) / rowHeight);
        
        // Buffer for smooth scrolling
        const buffer = 5;
        const visibleStart = Math.max(0, startIndex - buffer);
        const visibleEnd = Math.min(rows.length, endIndex + buffer);
        
        // Hide all rows first
        rows.forEach((row, index) => {
            if (index < visibleStart || index >= visibleEnd) {
                row.style.display = 'none';
            } else {
                row.style.display = '';
                row.style.position = 'absolute';
                row.style.top = `${index * rowHeight}px`;
                row.style.width = '100%';
            }
        });
    }
    
    // Loading states
    showLoading() {
        if (!this.loadingOverlay) {
            this.loadingOverlay = document.createElement('div');
            this.loadingOverlay.className = 'table-loading-overlay';
            this.loadingOverlay.innerHTML = '<div class="spinner-border"></div>';
            this.table.parentNode.appendChild(this.loadingOverlay);
        }
        this.loadingOverlay.style.display = 'flex';
    }
    
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = 'none';
        }
    }
    
    // Initialize column order arrays
    initializeColumnOrder() {
        const headers = this.table.querySelectorAll('thead th');
        headers.forEach((header, index) => {
            header.setAttribute('data-original-index', index);
            this.originalColumnOrder.push(index);
        });
        
        if (this.options.columnOrder && Array.isArray(this.options.columnOrder)) {
            this.columnOrder = [...this.options.columnOrder];
            this.applyColumnOrder();
        } else {
            this.columnOrder = [...this.originalColumnOrder];
        }
    }
    
    // Other methods remain similar but with performance optimizations...
    
    // Cleanup method for memory management
    destroy() {
        // Cancel any pending requests
        if (this.currentRequest) {
            this.currentRequest.abort();
        }
        
        // Clear timers
        Object.values(this.debounceTimers).forEach(timer => clearTimeout(timer));
        
        // Clear caches
        this.searchCache.clear();
        
        // Remove event listeners
        if (this.searchInput) {
            this.searchInput.removeEventListener('input', this.searchHandler);
        }
        
        // Clear references
        this.table = null;
        this.searchInput = null;
        this.fragment = null;
    }
}