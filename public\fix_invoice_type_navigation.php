<?php
/**
 * Fix invoice type dropdown navigation
 */

echo "<pre>";
echo "=== Fixing Invoice Type Dropdown Navigation ===\n\n";

$file = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($file);

// Create backup
$backup = $file . '.backup.' . date('YmdHis');
file_put_contents($backup, $content);
echo "✓ Created backup: " . basename($backup) . "\n\n";

// Find the invoice type change event listener and replace it with a simpler version
$searchStart = "// Handle invoice type change to show/hide CNS fields\n    document.getElementById('invoice_type_id').addEventListener('change', function() {";
$searchEnd = "    });";

// Find the positions
$startPos = strpos($content, $searchStart);
if ($startPos === false) {
    echo "❌ Could not find the invoice type change event listener\n";
    exit;
}

// Find the matching closing bracket
$openBrackets = 0;
$currentPos = $startPos + strlen($searchStart);
$endPos = false;

while ($currentPos < strlen($content)) {
    if ($content[$currentPos] === '{') {
        $openBrackets++;
    } elseif ($content[$currentPos] === '}') {
        if ($openBrackets === 0) {
            // Found the matching closing bracket
            $endPos = strpos($content, $searchEnd, $currentPos);
            if ($endPos !== false) {
                $endPos += strlen($searchEnd);
                break;
            }
        } else {
            $openBrackets--;
        }
    }
    $currentPos++;
}

if ($endPos === false) {
    echo "❌ Could not find the end of the event listener\n";
    exit;
}

// Create the new event listener
$newEventListener = "// Handle invoice type change to show/hide CNS fields
    document.getElementById('invoice_type_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        const typeId = this.value;
        const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
        
        console.log('🔍 Invoice type change - Type ID:', typeId, 'Type Code:', typeCode, 'Text:', selectedOption?.textContent);
        
        // Define type mappings
        const typeMapping = {
            // Retrocession types
            'RET25': 'retrocession_25',
            'RET30': 'retrocession_30',
            'RET': 'retrocession',
            // Location types
            'LOY': 'location',
            'LOCS': 'location',
            // By ID
            '1': 'loyer',
            '2': 'retrocession_30',
            '12': 'location',
            '15': 'retrocession_25'
        };
        
        // Determine the URL type parameter
        let urlType = null;
        
        // Check by code first
        if (typeCode && typeMapping[typeCode]) {
            urlType = typeMapping[typeCode];
        }
        // Then by ID
        else if (typeId && typeMapping[typeId]) {
            urlType = typeMapping[typeId];
        }
        // Finally by name
        else if (typeName.includes('rétrocession 25')) {
            urlType = 'retrocession_25';
        } else if (typeName.includes('rétrocession 30')) {
            urlType = 'retrocession_30';
        } else if (typeName.includes('location')) {
            urlType = 'location';
        } else if (typeName.includes('loyer')) {
            urlType = 'loyer';
        }
        
        // Get current URL type
        const currentUrl = new URL(window.location.href);
        const currentType = currentUrl.searchParams.get('type');
        
        console.log('URL type needed:', urlType, 'Current type:', currentType);
        
        // If we need a different URL type, navigate
        if (urlType && urlType !== currentType) {
            console.log('🔄 Navigating to:', urlType);
            currentUrl.searchParams.set('type', urlType);
            window.location.href = currentUrl.toString();
            return;
        }
        
        // If no special type but we have a type parameter, clear it
        if (!urlType && currentType) {
            console.log('🔄 Clearing type parameter');
            currentUrl.searchParams.delete('type');
            window.location.href = currentUrl.toString();
            return;
        }
        
        // Continue with existing logic
        handleInvoiceTypeChange();
        updateInvoiceNumber();
        toggleExcludePatientLine();
        
        // Update columns if not retrocession
        if (!typeCode || !typeCode.startsWith('RET')) {
            updateInvoiceItemColumns();
        } else {
            setTimeout(() => {
                updateInvoiceTableHeaders('RET');
            }, 50);
        }
        
        updateTemplateOptions();
    });";

// Replace the old event listener with the new one
$newContent = substr($content, 0, $startPos) . $newEventListener . substr($content, $endPos);

// Save the file
file_put_contents($file, $newContent);

echo "✅ Invoice type dropdown navigation fixed!\n\n";
echo "The dropdown will now navigate to the appropriate URL when changed:\n";
echo "- Rétrocession 25% → ?type=retrocession_25\n";
echo "- Rétrocession 30% → ?type=retrocession_30\n";
echo "- Location → ?type=location\n";
echo "- Loyer → ?type=loyer\n";
echo "\nOther types will clear the type parameter.\n";

echo "</pre>";