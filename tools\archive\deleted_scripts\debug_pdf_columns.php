<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

$invoiceId = $_GET['id'] ?? 238; // Default to invoice 238

echo "=== DEBUG PDF COLUMNS FOR INVOICE ID: $invoiceId ===\n\n";

try {
    $db = Flight::db();
    
    // Get invoice
    $stmt = $db->prepare("SELECT * FROM invoices WHERE id = ?");
    $stmt->execute([$invoiceId]);
    $invoice = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if (!$invoice) {
        die("Invoice not found");
    }
    
    echo "Invoice: {$invoice['invoice_number']}\n";
    echo "Document Type ID: " . ($invoice['document_type_id'] ?? 'NULL') . "\n\n";
    
    // Get column configuration
    $documentTypeId = $invoice['document_type_id'] ?? null;
    $visibleColumns = [];
    
    if ($documentTypeId) {
        $visibleColumns = \App\Models\DocumentTypeColumnConfig::getVisibleInvoiceColumns($documentTypeId);
    }
    
    // Default columns if no configuration
    if (empty($visibleColumns)) {
        $visibleColumns = [
            'description' => ['visible' => true, 'order' => 1],
            'quantity' => ['visible' => true, 'order' => 2],
            'unit_price' => ['visible' => true, 'order' => 3],
            'vat_rate' => ['visible' => true, 'order' => 4],
            'total' => ['visible' => true, 'order' => 5]
        ];
    }
    
    echo "Visible Columns (in order):\n";
    $orderedColumns = [];
    foreach ($visibleColumns as $col => $config) {
        if ($config['visible']) {
            $orderedColumns[] = $col;
            echo "- $col (order: " . ($config['order'] ?? 'default') . ")\n";
        }
    }
    
    // Get invoice lines
    $stmt = $db->prepare("
        SELECT * FROM invoice_lines 
        WHERE invoice_id = ?
        ORDER BY sort_order ASC, id ASC
    ");
    $stmt->execute([$invoiceId]);
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo "\nInvoice Lines:\n";
    foreach ($lines as $i => $line) {
        echo "\nLine " . ($i + 1) . ":\n";
        foreach ($orderedColumns as $col) {
            $value = '';
            switch ($col) {
                case 'description':
                    $value = $line['description'];
                    break;
                case 'quantity':
                    $value = number_format($line['quantity'], 2, ',', '.');
                    break;
                case 'unit_price':
                    $value = number_format($line['unit_price'], 2, ',', '.') . '€';
                    break;
                case 'vat_rate':
                    $value = number_format($line['vat_rate'], 2, ',', '.') . '%';
                    break;
                case 'total':
                    $total = $line['quantity'] * $line['unit_price'];
                    $totalWithVat = $total * (1 + $line['vat_rate'] / 100);
                    $value = number_format($totalWithVat, 2, ',', '.') . '€';
                    break;
            }
            echo "  $col: $value\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}