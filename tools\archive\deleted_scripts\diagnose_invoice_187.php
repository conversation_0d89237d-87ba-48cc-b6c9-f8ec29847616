<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

// CSS for better display
echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { border-collapse: collapse; width: 100%; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
</style>';

echo "<h1>Invoice FAC-2025-0187 Diagnostic Report</h1>";

// Get invoice by number
$stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
$stmt->execute(['FAC-2025-0187']);
$invoice = $stmt->fetch(\PDO::FETCH_ASSOC);

if (!$invoice) {
    echo '<div class="warning">Invoice FAC-2025-0187 not found in database!</div>';
    exit;
}

echo '<div class="section">';
echo '<h2>Invoice Details</h2>';
echo '<table>';
echo '<tr><th>Field</th><th>Value</th></tr>';
echo '<tr><td>ID</td><td>' . $invoice['id'] . '</td></tr>';
echo '<tr><td>Invoice Number</td><td>' . $invoice['invoice_number'] . '</td></tr>';
echo '<tr><td>User ID</td><td>' . ($invoice['user_id'] ?? 'NULL') . '</td></tr>';
echo '<tr><td>Client ID</td><td>' . ($invoice['client_id'] ?? 'NULL') . '</td></tr>';
echo '<tr><td>Period</td><td>' . ($invoice['period'] ?? 'NULL') . '</td></tr>';
echo '<tr><td>Subject</td><td>' . ($invoice['subject'] ?? 'NULL') . '</td></tr>';
echo '<tr><td>Subtotal</td><td>€' . number_format($invoice['subtotal'], 2) . '</td></tr>';
echo '<tr><td>VAT Amount</td><td>€' . number_format($invoice['vat_amount'], 2) . '</td></tr>';
echo '<tr><td>Total</td><td>€' . number_format($invoice['total'], 2) . '</td></tr>';
echo '</table>';
echo '</div>';

// Get invoice lines
echo '<div class="section">';
echo '<h2>Invoice Lines (from database)</h2>';
$stmt = $db->prepare("
    SELECT * FROM invoice_lines 
    WHERE invoice_id = ? 
    ORDER BY sort_order ASC, id ASC
");
$stmt->execute([$invoice['id']]);
$lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<p>Total lines found: ' . count($lines) . '</p>';

if (count($lines) > 0) {
    echo '<table>';
    echo '<tr><th>ID</th><th>Description</th><th>Qty</th><th>Unit Price</th><th>VAT Rate</th><th>Line Total</th><th>Sort Order</th></tr>';
    
    $calculatedSubtotal = 0;
    $calculatedVat = 0;
    
    foreach ($lines as $line) {
        $lineTotal = $line['quantity'] * $line['unit_price'];
        $calculatedSubtotal += $lineTotal;
        $lineVat = $lineTotal * ($line['vat_rate'] / 100);
        $calculatedVat += $lineVat;
        
        echo '<tr>';
        echo '<td>' . $line['id'] . '</td>';
        echo '<td>' . htmlspecialchars($line['description']) . '</td>';
        echo '<td>' . $line['quantity'] . '</td>';
        echo '<td>€' . number_format($line['unit_price'], 2) . '</td>';
        echo '<td>' . $line['vat_rate'] . '%</td>';
        echo '<td>€' . number_format($lineTotal, 2) . '</td>';
        echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
        echo '</tr>';
    }
    echo '</table>';
    
    // Check for duplicates
    $descriptions = [];
    $duplicates = [];
    foreach ($lines as $line) {
        if (in_array($line['description'], $descriptions)) {
            $duplicates[] = $line['description'];
        }
        $descriptions[] = $line['description'];
    }
    
    if (!empty($duplicates)) {
        echo '<div class="warning">';
        echo '<strong>⚠️ Duplicate descriptions found:</strong><br>';
        foreach (array_unique($duplicates) as $dup) {
            echo '- ' . htmlspecialchars($dup) . '<br>';
        }
        echo '</div>';
    }
}
echo '</div>';

// Test PDF query
echo '<div class="section">';
echo '<h2>PDF Generation Query Test</h2>';
echo '<p>This is the exact query used in invoice-pdf.php:</p>';
echo '<pre>';
$query = "
    SELECT 
        il.*,
        vr.name as vat_rate_name,
        vr.rate as vat_rate_value
    FROM invoice_lines il
    LEFT JOIN config_vat_rates vr ON il.vat_rate_id = vr.id
    WHERE il.invoice_id = ?
    ORDER BY il.sort_order ASC, il.id ASC
";
echo htmlspecialchars($query);
echo '</pre>';

$stmt = $db->prepare($query);
$stmt->execute([$invoice['id']]);
$pdfLines = $stmt->fetchAll(\PDO::FETCH_ASSOC);

echo '<p>Lines returned by PDF query: ' . count($pdfLines) . '</p>';
echo '<table>';
echo '<tr><th>Description</th><th>VAT Rate Name</th><th>Sort Order</th></tr>';
foreach ($pdfLines as $line) {
    echo '<tr>';
    echo '<td>' . htmlspecialchars($line['description']) . '</td>';
    echo '<td>' . ($line['vat_rate_name'] ?? 'NULL') . '</td>';
    echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
    echo '</tr>';
}
echo '</table>';
echo '</div>';

// Raw SQL check
echo '<div class="section">';
echo '<h2>Raw Database Check</h2>';
echo '<pre>';
$stmt = $db->prepare("SELECT id, description, sort_order FROM invoice_lines WHERE invoice_id = ? ORDER BY id");
$stmt->execute([$invoice['id']]);
while ($row = $stmt->fetch(\PDO::FETCH_ASSOC)) {
    echo "ID: {$row['id']}, Description: {$row['description']}, Sort Order: " . ($row['sort_order'] ?? 'NULL') . "\n";
}
echo '</pre>';
echo '</div>';

// Recommendations
echo '<div class="section">';
echo '<h2>Analysis & Recommendations</h2>';
if (count($lines) == count($pdfLines)) {
    echo '<div class="success">✓ Database query returns correct number of lines</div>';
} else {
    echo '<div class="warning">⚠️ Mismatch between database lines (' . count($lines) . ') and PDF query lines (' . count($pdfLines) . ')</div>';
}

if (!empty($duplicates)) {
    echo '<div class="warning">⚠️ There are duplicate descriptions in the database. This may cause confusion in the PDF.</div>';
}
echo '</div>';