<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== FORCE RESET INVOICE SEQUENCE ===\n\n";

try {
    $db = Flight::db();
    
    // First, show current state
    echo "1. Current state:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025
    ");
    $sequences = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($sequences as $seq) {
        echo "- ID: {$seq['id']}, Month: " . ($seq['month'] ?? 'NULL') . ", Last: {$seq['last_number']}\n";
    }
    
    // Delete all 2025 sequences
    echo "\n2. Deleting all 2025 sequences...\n";
    $stmt = $db->prepare("DELETE FROM document_sequences WHERE document_type_id = 1 AND year = 2025");
    $stmt->execute();
    echo "Deleted " . $stmt->rowCount() . " rows\n";
    
    // Insert fresh sequence at 185
    echo "\n3. Creating new sequence at 185...\n";
    $stmt = $db->prepare("
        INSERT INTO document_sequences (document_type_id, year, month, last_number, created_at, updated_at)
        VALUES (1, 2025, NULL, 185, NOW(), NOW())
    ");
    $stmt->execute();
    echo "Created new sequence\n";
    
    // Verify
    echo "\n4. Verification:\n";
    $stmt = $db->query("
        SELECT * FROM document_sequences 
        WHERE document_type_id = 1 AND year = 2025 AND month IS NULL
    ");
    $seq = $stmt->fetch(\PDO::FETCH_ASSOC);
    
    if ($seq) {
        echo "Success! Last number: {$seq['last_number']}\n";
        echo "Next invoice will be: FAC-2025-" . str_pad($seq['last_number'] + 1, 4, '0', STR_PAD_LEFT) . "\n";
    } else {
        echo "ERROR: Sequence not found!\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}