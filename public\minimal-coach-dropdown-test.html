<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Coach Dropdown Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #007bff; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; text-align: center; }
        .card { background: white; border-radius: 10px; padding: 20px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        select, input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 5px; cursor: pointer; font-weight: bold; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .success { border-left: 5px solid #28a745; background: #f8fff9; padding: 15px; margin: 10px 0; }
        .error { border-left: 5px solid #dc3545; background: #fff5f5; padding: 15px; margin: 10px 0; }
        .warning { border-left: 5px solid #ffc107; background: #fffdf5; padding: 15px; margin: 10px 0; }
        .info { border-left: 5px solid #17a2b8; background: #f0f8ff; padding: 15px; margin: 10px 0; }
        .code { background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 14px; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Minimal Coach Dropdown Test</h1>
            <p>Isolated test of coach dropdown functionality</p>
        </div>

        <div class="card">
            <h2>📋 Test Form</h2>
            <form id="testForm">
                <div class="form-group">
                    <label for="invoice_type_id">Invoice Type:</label>
                    <select id="invoice_type_id" name="invoice_type_id">
                        <option value="">Select Invoice Type</option>
                        <option value="1">Standard Invoice</option>
                        <option value="12" selected>Location Invoice</option>
                        <option value="2">Retrocession 30%</option>
                        <option value="15">Retrocession 25%</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="billable_type">Billable Type:</label>
                    <select id="billable_type" name="billable_type">
                        <option value="">Select Type</option>
                        <option value="client">Client</option>
                        <option value="user" selected>User</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="billable_id">Billable (Coach):</label>
                    <select id="billable_id" name="billable_id">
                        <option value="">Select...</option>
                    </select>
                </div>
            </form>
        </div>

        <div class="card">
            <h2>🛠️ Test Controls</h2>
            <button class="btn btn-primary" onclick="testManualPopulation()">👥 Manual Population</button>
            <button class="btn btn-success" onclick="testAutoPopulation()">⚡ Auto Population</button>
            <button class="btn btn-warning" onclick="testDataValidation()">🔍 Validate Data</button>
            <button class="btn btn-danger" onclick="clearDropdown()">🧹 Clear Dropdown</button>
        </div>

        <div class="card">
            <h2>📊 Test Results</h2>
            <div id="testResults">
                <p>Run tests to see results...</p>
            </div>
        </div>

        <div class="card">
            <h2>📝 Debug Log</h2>
            <div id="debugLog" class="code" style="max-height: 300px; overflow-y: auto;">
                <div id="logContent">Debug logs will appear here...</div>
            </div>
            <button class="btn btn-primary" onclick="clearLog()">Clear Log</button>
        </div>
    </div>

    <script>
        // Mock coaches data (same as from PHP)
        const mockCoachesData = [
            { id: 16, name: 'Isabelle Lamy', username: 'Isabelle', email: '<EMAIL>', course_name: null },
            { id: 8, name: 'Justine Deremiens', username: 'Justine', email: '<EMAIL>', course_name: null },
            { id: 14, name: 'Malaurie Zéler', username: 'Malaurie', email: '<EMAIL>', course_name: null },
            { id: 15, name: 'Nicolas Moineau', username: 'Nicolas', email: '<EMAIL>', course_name: null }
        ];

        // Debug logging function
        function debugLog(message, data = null) {
            const timestamp = new Date().toISOString();
            const logDiv = document.getElementById('logContent');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = '🔍 [' + timestamp + '] ' + message + (data ? ' | Data: ' + JSON.stringify(data) : '');
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log('🔍 [' + timestamp + '] ' + message, data || '');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM Content Loaded');
            debugLog('Mock coaches data loaded', { count: mockCoachesData.length });
            
            // Check if URL has type=location
            const urlParams = new URLSearchParams(window.location.search);
            const typeParam = urlParams.get('type');
            if (typeParam === 'location') {
                debugLog('URL type=location detected, triggering auto population');
                setTimeout(() => {
                    testAutoPopulation();
                }, 100);
            }
        });

        // Test manual population
        function testManualPopulation() {
            debugLog('=== MANUAL POPULATION TEST START ===');
            
            try {
                const billableSelect = document.getElementById('billable_id');
                const billableType = document.getElementById('billable_type');
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                
                if (!billableSelect || !billableType || !invoiceTypeSelect) {
                    throw new Error('Required form elements not found');
                }
                
                // Step 1: Set invoice type to Location
                invoiceTypeSelect.value = '12';
                debugLog('Step 1: Set invoice type to Location (12)');
                
                // Step 2: Set billable type to user
                billableType.value = 'user';
                debugLog('Step 2: Set billable type to user');
                
                // Step 3: Clear existing options
                billableSelect.innerHTML = '<option value="">Select...</option>';
                debugLog('Step 3: Cleared existing options');
                
                // Step 4: Populate with coaches
                let addedCount = 0;
                mockCoachesData.forEach(coach => {
                    const courseName = coach.course_name ? ' - ' + coach.course_name : '';
                    const option = new Option(coach.name + ' (' + coach.username + ')' + courseName, 'user_' + coach.id);
                    billableSelect.add(option);
                    addedCount++;
                    debugLog('Added coach: ' + coach.name + ' (ID: ' + coach.id + ')');
                });
                
                // Step 5: Trigger change events
                const invoiceTypeEvent = new Event('change', { bubbles: true });
                invoiceTypeSelect.dispatchEvent(invoiceTypeEvent);
                
                const billableTypeEvent = new Event('change', { bubbles: true });
                billableType.dispatchEvent(billableTypeEvent);
                
                debugLog('Step 5: Triggered change events');
                debugLog('=== MANUAL POPULATION SUCCESS ===', { addedCount });
                
                showResult('success', 'Manual population successful! Added ' + addedCount + ' coaches.');
                
            } catch (error) {
                debugLog('=== MANUAL POPULATION ERROR ===', { error: error.message });
                showResult('error', 'Manual population failed: ' + error.message);
            }
        }

        // Test auto population
        function testAutoPopulation() {
            debugLog('=== AUTO POPULATION TEST START ===');
            
            try {
                // Simulate the immediate location detection logic
                if (window.location.search.includes('type=location') || true) { // Force true for testing
                    debugLog('Location detection triggered');
                    
                    const billableSelect = document.getElementById('billable_id');
                    const billableTypeSelect = document.getElementById('billable_type');
                    const invoiceTypeSelect = document.getElementById('invoice_type_id');
                    
                    if (billableSelect && billableTypeSelect && invoiceTypeSelect) {
                        // Set invoice type to Location (ID 12)
                        invoiceTypeSelect.value = '12';
                        debugLog('Set invoice type to Location (12)');
                        
                        // Set billable type to user
                        billableTypeSelect.value = 'user';
                        debugLog('Set billable type to user');
                        
                        // Directly populate coaches
                        billableSelect.innerHTML = '<option value="">Select...</option>';
                        let populatedCount = 0;
                        
                        mockCoachesData.forEach(coach => {
                            const courseName = coach.course_name ? ' - ' + coach.course_name : '';
                            const option = new Option(coach.name + ' (' + coach.username + ')' + courseName, 'user_' + coach.id);
                            billableSelect.add(option);
                            populatedCount++;
                        });
                        
                        debugLog('AUTO POPULATION SUCCESS: ' + populatedCount + ' coaches added');
                        
                        // Trigger change events
                        const invoiceTypeEvent = new Event('change', { bubbles: true });
                        invoiceTypeSelect.dispatchEvent(invoiceTypeEvent);
                        
                        const billableTypeEvent = new Event('change', { bubbles: true });
                        billableTypeSelect.dispatchEvent(billableTypeEvent);
                        
                        showResult('success', 'Auto population successful! Added ' + populatedCount + ' coaches.');
                        
                    } else {
                        throw new Error('DOM elements not ready for auto population');
                    }
                } else {
                    debugLog('No location type detected in URL');
                    showResult('warning', 'No location type detected. Add ?type=location to URL for auto population.');
                }
                
            } catch (error) {
                debugLog('=== AUTO POPULATION ERROR ===', { error: error.message });
                showResult('error', 'Auto population failed: ' + error.message);
            }
        }

        // Test data validation
        function testDataValidation() {
            debugLog('=== DATA VALIDATION TEST START ===');
            
            const validation = {
                coachesData: {
                    exists: typeof mockCoachesData !== 'undefined',
                    isArray: Array.isArray(mockCoachesData),
                    count: mockCoachesData ? mockCoachesData.length : 0,
                    valid: mockCoachesData ? mockCoachesData.every(coach => coach.id && coach.name && coach.username) : false
                },
                formElements: {
                    invoiceTypeSelect: !!document.getElementById('invoice_type_id'),
                    billableTypeSelect: !!document.getElementById('billable_type'),
                    billableSelect: !!document.getElementById('billable_id')
                },
                dropdownState: {
                    invoiceTypeValue: document.getElementById('invoice_type_id')?.value,
                    billableTypeValue: document.getElementById('billable_type')?.value,
                    billableOptionsCount: document.getElementById('billable_id')?.options.length
                }
            };
            
            debugLog('Data validation results', validation);
            
            let resultMessage = 'Data Validation Results:\n';
            resultMessage += '• Coaches data valid: ' + (validation.coachesData.valid ? 'Yes' : 'No') + '\n';
            resultMessage += '• Form elements present: ' + (Object.values(validation.formElements).every(Boolean) ? 'Yes' : 'No') + '\n';
            resultMessage += '• Dropdown options: ' + (validation.dropdownState.billableOptionsCount || 0) + '\n';
            resultMessage += '• Invoice type: ' + (validation.dropdownState.invoiceTypeValue || 'Not set') + '\n';
            resultMessage += '• Billable type: ' + (validation.dropdownState.billableTypeValue || 'Not set');
            
            showResult('info', resultMessage);
        }

        // Clear dropdown
        function clearDropdown() {
            debugLog('Clearing dropdown');
            const billableSelect = document.getElementById('billable_id');
            if (billableSelect) {
                billableSelect.innerHTML = '<option value="">Select...</option>';
                showResult('warning', 'Dropdown cleared.');
            }
        }

        // Clear log
        function clearLog() {
            document.getElementById('logContent').innerHTML = 'Debug logs will appear here...';
        }

        // Show result
        function showResult(type, message) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = type;
            resultDiv.innerHTML = '<strong>' + type.toUpperCase() + ':</strong> ' + message.replace(/\n/g, '<br>');
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        // Add URL parameter handling
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('type') === 'location') {
            debugLog('URL parameter type=location detected');
        }
    </script>
</body>
</html>