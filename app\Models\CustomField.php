<?php

namespace App\Models;

use App\Core\Model;
use PDO;

class Custom<PERSON>ield extends Model
{
    protected $table = 'custom_fields';
    protected $fillable = [
        'module', 'field_name', 'field_label', 'field_type', 'field_options',
        'validation_rules', 'placeholder', 'default_value', 'help_text',
        'is_required', 'is_unique', 'is_searchable', 'is_sortable', 'is_exportable',
        'display_order', 'column_width', 'css_class', 'is_active',
        'created_by', 'updated_by'
    ];
    
    /**
     * Get all fields for a module (both default and custom)
     */
    public static function getAllFieldsForModule($module): array
    {
        $db = \Flight::db();
        $currentLang = $_SESSION['language'] ?? 'fr';
        
        // Get default fields
        $stmt = $db->prepare("
            SELECT 
                mdf.field_name,
                mdf.field_label,
                mdf.field_category,
                mdf.is_system,
                mdf.can_be_hidden,
                'default' as field_source,
                NULL as field_type,
                NULL as field_options,
                NULL as validation_rules,
                mdf.template_variable,
                mdf.display_order,
                NULL as id
            FROM module_default_fields mdf
            WHERE mdf.module = :module
            
            UNION ALL
            
            SELECT 
                cf.field_name,
                COALESCE(cft.label, cf.field_label) as field_label,
                'custom' as field_category,
                0 as is_system,
                1 as can_be_hidden,
                'custom' as field_source,
                cf.field_type,
                cf.field_options,
                cf.validation_rules,
                cf.template_variable,
                cf.display_order + 1000 as display_order,
                cf.id
            FROM custom_fields cf
            LEFT JOIN custom_field_translations cft ON cf.id = cft.field_id 
                AND cft.language = :language
            WHERE cf.module = :module2 AND cf.is_active = 1
            
            ORDER BY display_order
        ");
        
        $stmt->execute([
            'module' => $module, 
            'module2' => $module,
            'language' => $currentLang
        ]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get visible fields for a specific view
     */
    public static function getVisibleFields($module, $viewType = 'list', $userGroupId = null): array
    {
        $db = \Flight::db();
        $currentLang = $_SESSION['language'] ?? 'fr';
        
        $sql = "
            SELECT 
                fv.field_name,
                fv.display_order,
                COALESCE(
                    cft.label,
                    cf.field_label, 
                    mdf.field_label
                ) as field_label,
                COALESCE(cf.field_type, 'text') as field_type,
                cf.field_options,
                COALESCE(cft.placeholder, cf.placeholder) as placeholder,
                COALESCE(cft.help_text, cf.help_text) as help_text,
                cf.template_variable,
                cf.is_required,
                mdf.is_system,
                CASE 
                    WHEN cf.id IS NOT NULL THEN 'custom'
                    ELSE 'default'
                END as field_source
            FROM field_visibility fv
            LEFT JOIN custom_fields cf ON fv.field_name = cf.field_name 
                AND fv.module = cf.module AND cf.is_active = 1
            LEFT JOIN custom_field_translations cft ON cf.id = cft.field_id 
                AND cft.language = :language
            LEFT JOIN module_default_fields mdf ON fv.field_name = mdf.field_name 
                AND fv.module = mdf.module
            WHERE fv.module = :module 
            AND fv.view_type = :view_type 
            AND fv.is_visible = 1
        ";
        
        $params = [
            'module' => $module,
            'view_type' => $viewType,
            'language' => $currentLang
        ];
        
        if ($userGroupId !== null) {
            $sql .= " AND (fv.user_group_id = :group_id OR fv.user_group_id IS NULL)";
            $params['group_id'] = $userGroupId;
        } else {
            $sql .= " AND fv.user_group_id IS NULL";
        }
        
        $sql .= " ORDER BY fv.display_order";
        
        $stmt = $db->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Save field visibility settings
     */
    public static function saveFieldVisibility($module, $viewType, $fields, $userGroupId = null): bool
    {
        $db = \Flight::db();
        
        try {
            $db->beginTransaction();
            
            // Delete existing visibility settings
            $sql = "DELETE FROM field_visibility WHERE module = :module AND view_type = :view_type";
            $params = ['module' => $module, 'view_type' => $viewType];
            
            if ($userGroupId !== null) {
                $sql .= " AND user_group_id = :group_id";
                $params['group_id'] = $userGroupId;
            } else {
                $sql .= " AND user_group_id IS NULL";
            }
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            
            // Insert new visibility settings
            $stmt = $db->prepare("
                INSERT INTO field_visibility 
                (module, field_name, view_type, is_visible, display_order, user_group_id) 
                VALUES (:module, :field_name, :view_type, :is_visible, :display_order, :user_group_id)
            ");
            
            foreach ($fields as $order => $field) {
                $stmt->execute([
                    'module' => $module,
                    'field_name' => $field['field_name'],
                    'view_type' => $viewType,
                    'is_visible' => $field['is_visible'] ?? 1,
                    'display_order' => $field['display_order'] ?? ($order + 1),
                    'user_group_id' => $userGroupId
                ]);
            }
            
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }
    
    /**
     * Get custom field values for an entity
     */
    public static function getCustomFieldValues($module, $entityId): array
    {
        $db = \Flight::db();
        
        $stmt = $db->prepare("
            SELECT 
                cf.field_name,
                cfv.field_value
            FROM custom_fields cf
            LEFT JOIN custom_field_values cfv ON cf.id = cfv.field_id 
                AND cfv.module = :module 
                AND cfv.entity_id = :entity_id
            WHERE cf.module = :module2 
            AND cf.is_active = 1
        ");
        
        $stmt->execute([
            'module' => $module,
            'module2' => $module,
            'entity_id' => $entityId
        ]);
        
        $values = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $values[$row['field_name']] = $row['field_value'];
        }
        
        return $values;
    }
    
    /**
     * Save custom field values for an entity
     */
    public static function saveCustomFieldValues($module, $entityId, $values): bool
    {
        $db = \Flight::db();
        
        try {
            // Get active custom fields for this module
            $stmt = $db->prepare("
                SELECT id, field_name FROM custom_fields 
                WHERE module = :module AND is_active = 1
            ");
            $stmt->execute(['module' => $module]);
            $customFields = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            // Save values
            $stmt = $db->prepare("
                INSERT INTO custom_field_values (module, entity_id, field_id, field_value)
                VALUES (:module, :entity_id, :field_id, :field_value)
                ON DUPLICATE KEY UPDATE field_value = VALUES(field_value)
            ");
            
            foreach ($values as $fieldName => $value) {
                if (isset($customFields[$fieldName])) {
                    $stmt->execute([
                        'module' => $module,
                        'entity_id' => $entityId,
                        'field_id' => $customFields[$fieldName],
                        'field_value' => $value
                    ]);
                }
            }
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * Validate custom field data
     */
    public static function validateField($field, $value): array
    {
        $errors = [];
        
        // Required validation
        if ($field['is_required'] && empty($value)) {
            $errors[] = sprintf(__('validation.field_required'), $field['field_label']);
        }
        
        // Type-specific validation
        if (!empty($value)) {
            switch ($field['field_type']) {
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = sprintf(__('validation.invalid_email'), $field['field_label']);
                    }
                    break;
                    
                case 'number':
                    if (!is_numeric($value)) {
                        $errors[] = sprintf(__('validation.must_be_number'), $field['field_label']);
                    }
                    break;
                    
                case 'date':
                    if (!strtotime($value)) {
                        $errors[] = sprintf(__('validation.invalid_date'), $field['field_label']);
                    }
                    break;
                    
                case 'url':
                    if (!filter_var($value, FILTER_VALIDATE_URL)) {
                        $errors[] = sprintf(__('validation.invalid_url'), $field['field_label']);
                    }
                    break;
            }
        }
        
        // Custom validation rules
        if (!empty($field['validation_rules'])) {
            $rules = json_decode($field['validation_rules'], true);
            
            if (isset($rules['min_length']) && strlen($value) < $rules['min_length']) {
                $errors[] = sprintf(__('validation.min_length'), $field['field_label'], $rules['min_length']);
            }
            
            if (isset($rules['max_length']) && strlen($value) > $rules['max_length']) {
                $errors[] = sprintf(__('validation.max_length'), $field['field_label'], $rules['max_length']);
            }
            
            if (isset($rules['pattern']) && !preg_match($rules['pattern'], $value)) {
                $errors[] = sprintf(__('validation.invalid_format'), $field['field_label']);
            }
        }
        
        return $errors;
    }
}