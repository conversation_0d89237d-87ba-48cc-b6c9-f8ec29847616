<?php

/**
 * Migration: Add exclude_patient_line column to retrocession_data_entry table
 * 
 * This migration adds a boolean field to allow excluding the patient line
 * from retrocession invoices when needed.
 */

return new class {
    
    public function up($pdo) {
        // Add exclude_patient_line column to retrocession_data_entry table
        $pdo->exec("
            ALTER TABLE `retrocession_data_entry` 
            ADD COLUMN `exclude_patient_line` tinyint(1) DEFAULT 0 COMMENT 'Exclude patient line from invoice generation' 
            AFTER `override_notes`
        ");
        
        echo "Added exclude_patient_line column to retrocession_data_entry table\n";
    }
    
    public function down($pdo) {
        // Remove the column
        $pdo->exec("ALTER TABLE `retrocession_data_entry` DROP COLUMN `exclude_patient_line`");
    }
};