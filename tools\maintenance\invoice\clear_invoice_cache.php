<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
.info { background: #d1ecf1; border: 1px solid #bee5eb; padding: 10px; margin: 10px 0; }
</style>';

echo "<h1>Clear Invoice Cache</h1>";

$cache = Flight::cache();

if (isset($_GET['invoice_id'])) {
    $invoiceId = $_GET['invoice_id'];
    $cacheKey = "invoice_details_{$invoiceId}";
    
    echo '<div class="section">';
    echo '<h2>Clearing cache for Invoice ID: ' . $invoiceId . '</h2>';
    
    // Clear the specific invoice cache
    $cache->delete($cacheKey);
    
    echo '<div class="success">✓ Cache cleared for invoice ID ' . $invoiceId . '</div>';
    echo '<p><a href="invoice-pdf.php?id=' . $invoiceId . '">Test PDF for this invoice</a></p>';
    echo '</div>';
} elseif (isset($_GET['clear_all'])) {
    echo '<div class="section">';
    echo '<h2>Clearing all invoice caches</h2>';
    
    // Clear all cache (if supported by cache driver)
    try {
        $cache->clear();
        echo '<div class="success">✓ All caches cleared</div>';
    } catch (Exception $e) {
        echo '<div class="info">Note: Cache clear method not available. Caches will expire naturally.</div>';
    }
    echo '</div>';
} else {
    echo '<div class="section">';
    echo '<h2>Options:</h2>';
    echo '<form method="get">';
    echo '<label>Invoice ID: <input type="number" name="invoice_id" required></label>';
    echo '<button type="submit">Clear Invoice Cache</button>';
    echo '</form>';
    echo '<br>';
    echo '<p><a href="?clear_all=1">Clear All Caches</a></p>';
    echo '</div>';
}

echo '<div class="info">';
echo '<p><strong>Why clear cache?</strong></p>';
echo '<ul>';
echo '<li>If invoice data was updated but PDF shows old data</li>';
echo '<li>If duplicate lines appear due to cached data</li>';
echo '<li>After fixing database issues</li>';
echo '</ul>';
echo '</div>';