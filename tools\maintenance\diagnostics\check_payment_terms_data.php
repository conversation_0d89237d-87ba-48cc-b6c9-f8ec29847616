<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING PAYMENT TERMS DATA ===\n\n";

try {
    $db = Flight::db();
    
    // Get all payment terms
    $stmt = $db->query("SELECT * FROM config_payment_terms ORDER BY id");
    $terms = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    echo "Current payment terms in database:\n";
    echo str_repeat("-", 80) . "\n";
    
    foreach ($terms as $term) {
        echo "ID: {$term['id']}\n";
        echo "Code: {$term['code']}\n";
        echo "Days: {$term['days']}\n";
        echo "Name (raw): {$term['name']}\n";
        
        // Decode name
        if (!empty($term['name']) && $term['name'] !== 'null') {
            $nameData = json_decode($term['name'], true);
            if ($nameData) {
                echo "Name (decoded): " . json_encode($nameData, JSON_PRETTY_PRINT) . "\n";
            }
        }
        
        // Decode description
        if (!empty($term['description']) && $term['description'] !== 'null') {
            $descData = json_decode($term['description'], true);
            if ($descData) {
                echo "Description (decoded): " . json_encode($descData, JSON_PRETTY_PRINT) . "\n";
            }
        }
        
        echo "Is Active: {$term['is_active']}\n";
        echo "Is Default: {$term['is_default']}\n";
        echo str_repeat("-", 80) . "\n";
    }
    
    // Check for specific issues
    echo "\nISSUES FOUND:\n";
    
    // Check for Net 30 with wrong days
    $stmt = $db->prepare("SELECT * FROM config_payment_terms WHERE (code = 'net_30' OR name LIKE '%Net 30%') AND days != 30");
    $stmt->execute();
    $wrongNet30 = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($wrongNet30) > 0) {
        echo "- Found 'Net 30' payment terms with incorrect days value!\n";
        foreach ($wrongNet30 as $term) {
            echo "  ID {$term['id']}: Has {$term['days']} days instead of 30\n";
        }
    }
    
    // Check for other common issues
    $stmt = $db->prepare("SELECT * FROM config_payment_terms WHERE 
        (code = 'net_60' AND days != 60) OR 
        (code = 'net_90' AND days != 90) OR 
        (code = 'immediate' AND days != 0)");
    $stmt->execute();
    $otherWrong = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($otherWrong) > 0) {
        echo "- Found other payment terms with incorrect days values!\n";
        foreach ($otherWrong as $term) {
            echo "  ID {$term['id']} ({$term['code']}): Has {$term['days']} days\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}