<?php

return array(
  // Location/Coach invoicing
  'location_type' => 'Room rental',
  'select_course' => 'Select a course',
  'hours' => 'Hours',
  'hours_abbr' => 'h',
  'hours_worked' => 'Hours worked',
  'coach_selection' => 'Coach selection',
  'course_rate' => 'Course rate',
  'intracommunity_vat' => 'Intracommunity VAT',
  'period_month' => 'Period month',
  'period_year' => 'Period year',
  'select_coach_for_location' => 'Select the coach for location rental',
  'no_coaches_found' => 'No coaches found',
  'select_practitioner_for_retrocession' => 'Select the practitioner for retrocession calculation',
  'no_practitioners_found' => 'No practitioners found',
  'replace_existing_items_with_courses' => 'Replace existing items with coach courses?',
  'billable_information' => 'Billing Information',
);