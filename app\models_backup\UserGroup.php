<?php

namespace App\Models;

use PDO;
use Flight;
use Exception;

class UserGroup
{
    /**
     * Get all groups
     */
    public static function getAll()
    {
        $db = Flight::db();
        $stmt = $db->query("SELECT * FROM user_groups ORDER BY is_system DESC, name ASC");
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Find group by ID
     */
    public static function find($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("SELECT * FROM user_groups WHERE id = ?");
        $stmt->execute([$id]);
        return $stmt->fetch(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get all groups with member count
     */
    public static function getAllWithCounts()
    {
        $db = Flight::db();
        $sql = "SELECT ug.*, 
                COUNT(DISTINCT ugm.user_id) as member_count,
                COUNT(DISTINCT gp.permission_id) as permission_count
                FROM user_groups ug
                LEFT JOIN user_group_members ugm ON ug.id = ugm.group_id
                LEFT JOIN group_permissions gp ON ug.id = gp.group_id
                WHERE ug.is_active = 1
                GROUP BY ug.id
                ORDER BY ug.is_system DESC, ug.name ASC";
        
        $stmt = $db->query($sql);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Get group with permissions
     */
    public static function getWithPermissions($groupId)
    {
        $db = Flight::db();
        
        // Get group details
        $stmt = $db->prepare("SELECT * FROM user_groups WHERE id = ?");
        $stmt->execute([$groupId]);
        $group = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        if (!$group) {
            return null;
        }
        
        // Get permissions
        $stmt = $db->prepare("
            SELECT p.* 
            FROM permissions p
            INNER JOIN group_permissions gp ON p.id = gp.permission_id
            WHERE gp.group_id = ?
            ORDER BY p.category, p.sort_order
        ");
        $stmt->execute([$groupId]);
        $group['permissions'] = $stmt->fetchAll(\PDO::FETCH_ASSOC);
        
        return $group;
    }
    
    /**
     * Create new group
     */
    public static function create($data)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            INSERT INTO user_groups (name, description, color, icon, is_active)
            VALUES (?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $data['name'],
            $data['description'] ?? null,
            $data['color'] ?? '#0066CC',
            $data['icon'] ?? 'users',
            $data['is_active'] ?? 1
        ]);
        
        return $db->lastInsertId();
    }
    
    /**
     * Update group
     */
    public static function update($id, $data)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            UPDATE user_groups 
            SET name = ?, description = ?, color = ?, icon = ?, is_active = ?
            WHERE id = ? AND is_system = 0
        ");
        
        return $stmt->execute([
            $data['name'],
            $data['description'] ?? null,
            $data['color'] ?? '#0066CC',
            $data['icon'] ?? 'users',
            $data['is_active'] ?? 1,
            $id
        ]);
    }
    
    /**
     * Delete group (only non-system groups)
     */
    public static function delete($id)
    {
        $db = Flight::db();
        $stmt = $db->prepare("DELETE FROM user_groups WHERE id = ? AND is_system = 0");
        return $stmt->execute([$id]);
    }
    
    /**
     * Assign permissions to group
     */
    public static function syncPermissions($groupId, $permissionIds)
    {
        $db = Flight::db();
        
        // Start transaction
        $db->beginTransaction();
        
        try {
            // Remove existing permissions
            $stmt = $db->prepare("DELETE FROM group_permissions WHERE group_id = ?");
            $stmt->execute([$groupId]);
            
            // Add new permissions
            if (!empty($permissionIds)) {
                $stmt = $db->prepare("INSERT INTO group_permissions (group_id, permission_id) VALUES (?, ?)");
                foreach ($permissionIds as $permissionId) {
                    $stmt->execute([$groupId, $permissionId]);
                }
            }
            
            $db->commit();
            return true;
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get group members
     */
    public static function getMembers($groupId)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            SELECT 
                u.*, 
                ugm.joined_at,
                ugm.added_by,
                CONCAT(added_user.first_name, ' ', added_user.last_name) as added_by_name
            FROM users u
            INNER JOIN user_group_members ugm ON u.id = ugm.user_id
            LEFT JOIN users added_user ON ugm.added_by = added_user.id
            WHERE ugm.group_id = ?
            ORDER BY u.first_name, u.last_name
        ");
        $stmt->execute([$groupId]);
        return $stmt->fetchAll(\PDO::FETCH_ASSOC);
    }
    
    /**
     * Add user to group
     */
    public static function addMember($groupId, $userId, $addedBy = null)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            INSERT IGNORE INTO user_group_members (user_id, group_id, added_by)
            VALUES (?, ?, ?)
        ");
        return $stmt->execute([$userId, $groupId, $addedBy]);
    }
    
    /**
     * Remove user from group
     */
    public static function removeMember($groupId, $userId)
    {
        $db = Flight::db();
        $stmt = $db->prepare("
            DELETE FROM user_group_members 
            WHERE group_id = ? AND user_id = ?
        ");
        return $stmt->execute([$groupId, $userId]);
    }
}