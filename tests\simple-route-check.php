<?php
/**
 * Simple Route Health Check
 * Tests critical routes and reports errors
 */

// Critical routes to test
$routes = [
    ['GET', '/fit/public/', 'Dashboard'],
    ['GET', '/fit/public/login', 'Login Page'],
    ['GET', '/fit/public/config/email-templates', 'Email Templates'],
    ['GET', '/fit/public/config/email-templates/9/edit', 'Edit Email Template #9'],
    ['GET', '/fit/public/invoices', 'Invoices List'],
    ['GET', '/fit/public/clients', 'Clients List'],
    ['GET', '/fit/public/health', 'Health Check'],
];

echo "Route Health Check\n";
echo "==================\n\n";

$passed = 0;
$failed = 0;
$errors = [];

foreach ($routes as $route) {
    list($method, $path, $desc) = $route;
    $url = 'http://localhost' . $path;
    
    echo "Testing: $method $path ($desc)... ";
    
    // Simple check using file_get_contents
    $context = stream_context_create([
        'http' => [
            'method' => $method,
            'timeout' => 5,
            'follow_location' => 0,
            'ignore_errors' => true,
        ]
    ]);
    
    $startTime = microtime(true);
    $response = @file_get_contents($url, false, $context);
    $responseTime = round((microtime(true) - $startTime) * 1000, 2);
    
    // Check response headers
    $status = 'FAIL';
    $httpCode = 0;
    
    if (isset($http_response_header) && is_array($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (preg_match('/^HTTP\/\d\.\d (\d{3})/', $header, $matches)) {
                $httpCode = (int)$matches[1];
                break;
            }
        }
        
        if ($httpCode == 200) {
            $status = 'OK';
            $passed++;
            
            // Check for PHP errors in response
            if ($response && (stripos($response, 'Fatal error') !== false || 
                             stripos($response, 'Parse error') !== false ||
                             stripos($response, 'Exception') !== false)) {
                $status = 'PHP ERROR';
                $failed++;
                $passed--;
                
                // Extract error message
                if (preg_match('/(Fatal error|Parse error|Exception):([^<]+)/', $response, $errorMatch)) {
                    $errors[] = [
                        'route' => $path,
                        'error' => trim($errorMatch[0])
                    ];
                }
            }
        } elseif ($httpCode == 302 || $httpCode == 301) {
            $status = 'REDIRECT';
            $passed++;
        } else {
            $failed++;
        }
    } else {
        $failed++;
    }
    
    echo "$status (HTTP $httpCode) - {$responseTime}ms\n";
}

echo "\n";
echo "Summary\n";
echo "=======\n";
echo "Total Routes: " . count($routes) . "\n";
echo "Passed: $passed\n";
echo "Failed: $failed\n";
echo "Pass Rate: " . round(($passed / count($routes)) * 100, 2) . "%\n";

if (!empty($errors)) {
    echo "\nErrors Found:\n";
    echo "=============\n";
    foreach ($errors as $error) {
        echo "\nRoute: " . $error['route'] . "\n";
        echo "Error: " . $error['error'] . "\n";
    }
}

// Save results
$results = [
    'timestamp' => date('Y-m-d H:i:s'),
    'total' => count($routes),
    'passed' => $passed,
    'failed' => $failed,
    'errors' => $errors
];

file_put_contents(__DIR__ . '/health-check-results.json', json_encode($results, JSON_PRETTY_PRINT));
