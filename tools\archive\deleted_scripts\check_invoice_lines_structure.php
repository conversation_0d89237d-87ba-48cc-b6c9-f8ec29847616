<?php
require_once __DIR__ . '/../vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();

require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== CHECKING INVOICE_LINES TABLE STRUCTURE ===\n\n";

try {
    $db = Flight::db();
    
    // Get table structure
    echo "1. Table structure for invoice_lines:\n";
    $stmt = $db->query("DESCRIBE invoice_lines");
    $columns = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    foreach ($columns as $col) {
        echo sprintf("- %-20s %s %s %s\n", 
            $col['Field'], 
            $col['Type'],
            $col['Null'] === 'NO' ? 'NOT NULL' : 'NULL',
            $col['Default'] ? "DEFAULT {$col['Default']}" : ''
        );
    }
    
    // Check sample data
    echo "\n2. Sample data from invoice_lines:\n";
    $stmt = $db->query("
        SELECT * FROM invoice_lines 
        ORDER BY invoice_id DESC, sort_order ASC
        LIMIT 5
    ");
    $lines = $stmt->fetchAll(\PDO::FETCH_ASSOC);
    
    if (count($lines) > 0) {
        // Show column names
        $columns = array_keys($lines[0]);
        echo "Columns: " . implode(', ', $columns) . "\n\n";
        
        foreach ($lines as $line) {
            echo sprintf("Invoice %d: %s - Qty: %.2f x %.2f€ (VAT: %.2f%%)\n",
                $line['invoice_id'],
                substr($line['description'], 0, 30),
                $line['quantity'],
                $line['unit_price'],
                $line['vat_rate'] ?? 0
            );
        }
    } else {
        echo "No invoice lines found.\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}