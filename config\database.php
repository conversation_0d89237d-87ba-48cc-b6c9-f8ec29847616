<?php
/**
 * Database Configuration
 * 
 * This file provides database constants for migration scripts
 */

// Load environment variables if .env file exists
$envFile = dirname(__DIR__) . '/.env';
if (file_exists($envFile)) {
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) continue;
        
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        
        if (!isset($_ENV[$name])) {
            $_ENV[$name] = $value;
        }
    }
}

// Database configuration constants
define('DB_HOST', $_ENV['DB_HOST'] ?? '127.0.0.1');
define('DB_NAME', $_ENV['DB_DATABASE'] ?? 'fitapp');
define('DB_USER', $_ENV['DB_USERNAME'] ?? 'root');
define('DB_PASS', $_ENV['DB_PASSWORD'] ?? 'test1234');

// Optional: Define table prefix if needed
define('DB_PREFIX', $_ENV['DB_PREFIX'] ?? '');

// For debugging
if (php_sapi_name() === 'cli' || (isset($_GET['debug']) && $_GET['debug'] === '1')) {
    echo "Database Configuration:\n";
    echo "Host: " . DB_HOST . "\n";
    echo "Database: " . DB_NAME . "\n";
    echo "User: " . DB_USER . "\n";
    echo "Password: " . (empty(DB_PASS) ? '(empty)' : '(set)') . "\n\n";
}
?>