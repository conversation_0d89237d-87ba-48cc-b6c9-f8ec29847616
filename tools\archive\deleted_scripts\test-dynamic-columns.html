<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Test Dynamic Invoice Item Columns</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin-bottom: 30px; padding: 15px; border: 1px solid #ddd; background: #f9f9f9; }
        .step { margin: 10px 0; padding: 10px; background: white; border-left: 3px solid #007bff; }
        .expected { color: green; }
        .note { color: #666; font-style: italic; }
        pre { background: #f4f4f4; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Dynamic Invoice Item Columns Test</h1>
    
    <div class="test-section">
        <h2>Setup Instructions</h2>
        <div class="step">
            <strong>Step 1: Configure Column Settings</strong>
            <ol>
                <li>Go to <a href="/fit/public/config/table-columns" target="_blank">Configuration → Table Columns</a></li>
                <li>Select "Invoice Items" table</li>
                <li>Select a Document Type (e.g., "Facture")</li>
                <li>Select an Invoice Type (e.g., "Loyer")</li>
                <li>Configure which columns should be visible:
                    <ul>
                        <li>Hide "Reference" column</li>
                        <li>Show "Discount" column</li>
                        <li>Show "Subtotal" column</li>
                        <li>Rename "Description" to "Article"</li>
                    </ul>
                </li>
                <li>Save the configuration</li>
            </ol>
        </div>
        
        <div class="step">
            <strong>Step 2: Test Invoice Creation</strong>
            <ol>
                <li>Go to <a href="/fit/public/invoices/create" target="_blank">Create Invoice</a></li>
                <li>Select the same Document Type and Invoice Type as configured above</li>
                <li class="expected">Expected: The invoice items table should update to show only the configured columns in the correct order</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>What to Check</h2>
        <div class="step">
            <strong>1. Column Visibility</strong>
            <p>When you change document type or invoice type, verify that:</p>
            <ul>
                <li>Hidden columns are not displayed</li>
                <li>Visible columns are shown</li>
                <li>Required columns (Description, Total) are always visible</li>
            </ul>
        </div>
        
        <div class="step">
            <strong>2. Column Order</strong>
            <p>Check that columns appear in the order you configured</p>
        </div>
        
        <div class="step">
            <strong>3. Custom Names</strong>
            <p>If you renamed columns, the custom names should appear in the headers</p>
        </div>
        
        <div class="step">
            <strong>4. Data Preservation</strong>
            <p>When switching between configurations:</p>
            <ul>
                <li>Existing item data should be preserved</li>
                <li>Hidden column data should not be lost</li>
                <li>Calculations should still work correctly</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Console Commands for Testing</h2>
        <p>Open browser console (F12) and run these commands:</p>
        <pre>
// Check current column configuration
console.log('Current columns:', currentColumnConfig);

// Manually trigger column update
updateInvoiceItemColumns();

// Check visible columns
const visibleCols = currentColumnConfig.filter(col => col.visible);
console.log('Visible columns:', visibleCols.map(c => c.name));
        </pre>
    </div>
    
    <div class="test-section">
        <h2>Test Scenarios</h2>
        <div class="step">
            <strong>Scenario 1: Default Configuration</strong>
            <p>Create invoice without selecting document type</p>
            <p class="expected">Expected: Default columns (Description, Quantity, Unit Price, VAT Rate, Total)</p>
        </div>
        
        <div class="step">
            <strong>Scenario 2: Document Type Only</strong>
            <p>Select document type but no invoice type</p>
            <p class="expected">Expected: Columns configured for that document type</p>
        </div>
        
        <div class="step">
            <strong>Scenario 3: Full Configuration</strong>
            <p>Select both document type and invoice type</p>
            <p class="expected">Expected: Columns configured for that specific combination</p>
        </div>
        
        <div class="step">
            <strong>Scenario 4: Add Items with Different Configs</strong>
            <ol>
                <li>Add 2 items with default config</li>
                <li>Change to a config with discount column</li>
                <li>Add another item</li>
                <li>All items should now show discount column</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Troubleshooting</h2>
        <div class="step">
            <strong>If columns don't update:</strong>
            <ol>
                <li>Check browser console for errors</li>
                <li>Verify API endpoint is accessible: <code>/fit/public/api/column-config/invoice_items</code></li>
                <li>Check that column configuration was saved in table-columns page</li>
                <li>Try hard refresh (Ctrl+Shift+R)</li>
            </ol>
        </div>
        
        <div class="step">
            <strong>Common Issues:</strong>
            <ul>
                <li><strong>404 on API call:</strong> Check routes configuration</li>
                <li><strong>Columns don't match config:</strong> Clear browser cache</li>
                <li><strong>JavaScript errors:</strong> Check for conflicting scripts</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section">
        <h2>API Test</h2>
        <p>Test the column configuration API directly:</p>
        <p><a href="/fit/public/api/column-config/invoice_items" target="_blank">Default Config</a></p>
        <p><a href="/fit/public/api/column-config/invoice_items?documentTypeId=1" target="_blank">With Document Type 1</a></p>
        <p><a href="/fit/public/api/column-config/invoice_items?documentTypeId=1&invoiceTypeId=1" target="_blank">With Document Type 1 & Invoice Type 1</a></p>
        <p class="note">You should see JSON response with column configuration</p>
    </div>
</body>
</html>