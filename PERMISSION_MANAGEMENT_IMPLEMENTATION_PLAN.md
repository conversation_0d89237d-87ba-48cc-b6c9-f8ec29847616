# Permission Management System Implementation Plan

## Overview
This document outlines the implementation of a comprehensive permission management UI for Fit360 AdminDesk, allowing administrators to configure granular access rights for different user groups.

## 🎯 Goals
1. Provide a user-friendly interface for permission management
2. Enable granular control over module access (view, create, edit, delete)
3. Support dynamic permission assignment without code changes
4. Maintain backward compatibility with existing permission checks
5. Add audit trail for permission changes

## 📊 Database Schema

### 1. System Modules Table
```sql
CREATE TABLE system_modules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT DEFAULT NULL,
    icon VARCHAR(50),
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIG<PERSON> KEY (parent_id) REFERENCES system_modules(id) ON DELETE CASCADE,
    INDEX idx_code (code),
    INDEX idx_parent (parent_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 2. Module Permissions Table
```sql
CREATE TABLE module_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    module_id INT NOT NULL,
    permission_code VARCHAR(50) NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    
    UNIQUE KEY unique_module_permission (module_id, permission_code),
    FOREIGN KEY (module_id) REFERENCES system_modules(id) ON DELETE CASCADE,
    INDEX idx_permission_code (permission_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 3. Group Permissions Table
```sql
CREATE TABLE group_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    module_id INT NOT NULL,
    permission_code VARCHAR(50) NOT NULL,
    is_granted BOOLEAN DEFAULT FALSE,
    granted_by INT,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_group_module_permission (group_id, module_id, permission_code),
    FOREIGN KEY (group_id) REFERENCES user_groups(id) ON DELETE CASCADE,
    FOREIGN KEY (module_id) REFERENCES system_modules(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_group (group_id),
    INDEX idx_module (module_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

### 4. Permission Audit Log
```sql
CREATE TABLE permission_audit_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    group_id INT NOT NULL,
    module_id INT NOT NULL,
    permission_code VARCHAR(50) NOT NULL,
    action ENUM('granted', 'revoked') NOT NULL,
    previous_value BOOLEAN,
    new_value BOOLEAN,
    changed_by INT NOT NULL,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    user_agent VARCHAR(255),
    notes TEXT,
    
    FOREIGN KEY (group_id) REFERENCES user_groups(id),
    FOREIGN KEY (module_id) REFERENCES system_modules(id),
    FOREIGN KEY (changed_by) REFERENCES users(id),
    INDEX idx_group_module (group_id, module_id),
    INDEX idx_changed_at (changed_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 🏗️ System Architecture

### Module Registry
```php
// config/modules.php
return [
    'invoices' => [
        'name' => 'Invoices',
        'icon' => 'fa-file-invoice',
        'permissions' => [
            'view' => 'View Invoices',
            'create' => 'Create Invoices',
            'edit' => 'Edit Invoices',
            'delete' => 'Delete Invoices',
            'export' => 'Export Invoices',
            'bulk_actions' => 'Bulk Invoice Actions'
        ],
        'submodules' => [
            'retrocession' => [
                'name' => 'Retrocession Invoices',
                'permissions' => [
                    'generate' => 'Generate Retrocession',
                    'bulk_generate' => 'Bulk Generate'
                ]
            ]
        ]
    ],
    'clients' => [
        'name' => 'Clients',
        'icon' => 'fa-users',
        'permissions' => [
            'view' => 'View Clients',
            'create' => 'Create Clients',
            'edit' => 'Edit Clients',
            'delete' => 'Delete Clients',
            'export' => 'Export Clients'
        ]
    ],
    'products' => [
        'name' => 'Products & Services',
        'icon' => 'fa-box',
        'permissions' => [
            'view' => 'View Products',
            'create' => 'Create Products',
            'edit' => 'Edit Products',
            'delete' => 'Delete Products',
            'manage_stock' => 'Manage Stock'
        ]
    ],
    'reports' => [
        'name' => 'Reports',
        'icon' => 'fa-chart-bar',
        'permissions' => [
            'view_financial' => 'View Financial Reports',
            'view_sales' => 'View Sales Reports',
            'view_inventory' => 'View Inventory Reports',
            'export' => 'Export Reports'
        ]
    ],
    'config' => [
        'name' => 'Configuration',
        'icon' => 'fa-cog',
        'permissions' => [
            'view' => 'View Configuration',
            'manage_company' => 'Manage Company Settings',
            'manage_system' => 'Manage System Settings',
            'manage_templates' => 'Manage Templates'
        ]
    ],
    'users' => [
        'name' => 'User Management',
        'icon' => 'fa-user-cog',
        'permissions' => [
            'view' => 'View Users',
            'create' => 'Create Users',
            'edit' => 'Edit Users',
            'delete' => 'Delete Users',
            'manage_groups' => 'Manage User Groups',
            'manage_permissions' => 'Manage Permissions'
        ]
    ]
];
```

## 🎨 UI Design

### Permission Management Interface

```
┌─────────────────────────────────────────────────────────────────────┐
│                     Permission Management                            │
├─────────────────────────────────────────────────────────────────────┤
│                                                                      │
│  User Groups                     Permissions Matrix                  │
│  ┌─────────────┐               ┌──────────────────────────────────┐ │
│  │ ▼ Admin     │               │ Module      View Create Edit Del │ │
│  │ ▶ Manager   │               ├──────────────────────────────────┤ │
│  │ ▶ Staff     │               │ Invoices    ✓    ✓     ✓    ✓  │ │
│  │ ▶ Practit.. │               │ Clients     ✓    ✓     ✓    ✓  │ │
│  │ ▶ Coach     │               │ Products    ✓    ✓     ✓    ✓  │ │
│  │ + Add Group │               │ Reports     ✓    □     □    □  │ │
│  └─────────────┘               │ Config      ✓    ✓     ✓    □  │ │
│                                │ Users       ✓    ✓     ✓    ✓  │ │
│                                └──────────────────────────────────┘ │
│                                                                      │
│  [Copy Permissions] [Reset to Default] [Save Changes]               │
└─────────────────────────────────────────────────────────────────────┘
```

### Features:
1. **Group Selection**: Click on a group to view/edit its permissions
2. **Module Tree**: Expandable modules with submodules
3. **Quick Actions**: 
   - Select all/none for a module
   - Copy permissions from another group
   - Reset to default template
4. **Bulk Operations**: Apply same permissions to multiple groups
5. **Search**: Filter modules and permissions
6. **Audit Trail**: View history of permission changes

## 💻 Implementation Code

### 1. Permission Controller
```php
// app/controllers/PermissionController.php
class PermissionController extends Controller
{
    public function index()
    {
        $this->requirePermission('users.manage_permissions');
        
        $groups = UserGroup::all();
        $modules = $this->getModuleRegistry();
        $permissions = $this->getGroupPermissions();
        
        return $this->render('permissions/index', [
            'groups' => $groups,
            'modules' => $modules,
            'permissions' => $permissions
        ]);
    }
    
    public function update(Request $request)
    {
        $this->requirePermission('users.manage_permissions');
        
        $groupId = $request->input('group_id');
        $permissions = $request->input('permissions', []);
        
        DB::beginTransaction();
        try {
            // Clear existing permissions
            GroupPermission::where('group_id', $groupId)->delete();
            
            // Insert new permissions
            foreach ($permissions as $moduleCode => $perms) {
                $module = SystemModule::where('code', $moduleCode)->first();
                if (!$module) continue;
                
                foreach ($perms as $permCode => $granted) {
                    if ($granted) {
                        GroupPermission::create([
                            'group_id' => $groupId,
                            'module_id' => $module->id,
                            'permission_code' => $permCode,
                            'is_granted' => true,
                            'granted_by' => Auth::id()
                        ]);
                        
                        // Log the change
                        $this->logPermissionChange($groupId, $module->id, $permCode, 'granted');
                    }
                }
            }
            
            DB::commit();
            
            // Clear permission cache
            Cache::tags(['permissions', "group_{$groupId}"])->flush();
            
            return redirect()->back()->with('success', 'Permissions updated successfully');
            
        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Failed to update permissions');
        }
    }
}
```

### 2. Permission Checking Service
```php
// app/services/PermissionService.php
class PermissionService
{
    protected $cache;
    protected $user;
    
    public function __construct($user = null)
    {
        $this->cache = new CacheService();
        $this->user = $user ?: Auth::user();
    }
    
    public function hasPermission($permission)
    {
        if (!$this->user) {
            return false;
        }
        
        // Super admin bypass
        if ($this->user->is_super_admin) {
            return true;
        }
        
        // Check cached permissions
        $cacheKey = "user_{$this->user->id}_permissions";
        $permissions = $this->cache->remember($cacheKey, 3600, function() {
            return $this->loadUserPermissions();
        });
        
        return in_array($permission, $permissions);
    }
    
    public function hasAnyPermission($permissions)
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }
    
    public function hasAllPermissions($permissions)
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }
    
    protected function loadUserPermissions()
    {
        $permissions = [];
        
        // Get user's groups
        $groupIds = DB::table('user_group_members')
            ->where('user_id', $this->user->id)
            ->pluck('group_id');
        
        // Get permissions for all groups
        $groupPermissions = DB::table('group_permissions as gp')
            ->join('system_modules as sm', 'gp.module_id', '=', 'sm.id')
            ->whereIn('gp.group_id', $groupIds)
            ->where('gp.is_granted', true)
            ->where('sm.is_active', true)
            ->select('sm.code', 'gp.permission_code')
            ->get();
        
        foreach ($groupPermissions as $perm) {
            $permissions[] = $perm->code . '.' . $perm->permission_code;
        }
        
        return array_unique($permissions);
    }
}
```

### 3. Permission Middleware
```php
// app/middleware/PermissionMiddleware.php
class PermissionMiddleware
{
    public function handle($request, $next, $permission)
    {
        $permissionService = new PermissionService();
        
        if (!$permissionService->hasPermission($permission)) {
            if ($request->ajax()) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
            
            return redirect()->route('dashboard')
                ->with('error', 'You do not have permission to access this resource.');
        }
        
        return $next($request);
    }
}
```

### 4. Helper Functions
```php
// app/helpers/permission_helpers.php

function hasPermission($permission)
{
    static $service = null;
    if (!$service) {
        $service = new PermissionService();
    }
    return $service->hasPermission($permission);
}

function requirePermission($permission)
{
    if (!hasPermission($permission)) {
        abort(403, 'Unauthorized');
    }
}

function canAny($permissions)
{
    $service = new PermissionService();
    return $service->hasAnyPermission($permissions);
}

function canAll($permissions)
{
    $service = new PermissionService();
    return $service->hasAllPermissions($permissions);
}
```

## 🔧 Integration with Existing Code

### Update Controllers
```php
// Before
if ($user->group_name !== 'Admin' && $user->group_name !== 'Manager') {
    abort(403);
}

// After
$this->requirePermission('invoices.create');
```

### Update Views
```twig
{# Before #}
{% if user.group_name == 'Admin' %}
    <button>Delete Invoice</button>
{% endif %}

{# After #}
{% if hasPermission('invoices.delete') %}
    <button>Delete Invoice</button>
{% endif %}
```

## 📝 Default Permission Templates

### Admin Group
- All permissions granted

### Manager Group
- All modules: view, create, edit
- Restricted: delete (except own records)
- No access to: system settings, permission management

### Staff Group
- Invoices: view, create
- Clients: view, create, edit
- Products: view
- Reports: view basic reports

### Practitioner Group
- Invoices: view own, create retrocession
- Clients: view assigned
- Products: view

### Coach Group
- Invoices: view own, create course invoices
- Clients: view assigned
- Products: view services

## 🚀 Implementation Steps

1. **Phase 1: Database Setup**
   - Create migration files
   - Seed initial modules and permissions
   - Create default permission templates

2. **Phase 2: Backend Implementation**
   - Create PermissionService
   - Create PermissionController
   - Add middleware
   - Update authentication system

3. **Phase 3: UI Development**
   - Create permission management views
   - Add JavaScript for dynamic interaction
   - Implement audit trail viewer

4. **Phase 4: Integration**
   - Update existing controllers
   - Update existing views
   - Add permission checks to API endpoints

5. **Phase 5: Testing & Documentation**
   - Create unit tests
   - Create integration tests
   - Write user documentation
   - Create admin guide

## 🔒 Security Considerations

1. **Permission Caching**: Cache permissions per user with appropriate TTL
2. **Audit Trail**: Log all permission changes with full context
3. **Validation**: Validate all permission codes against registry
4. **Rate Limiting**: Limit permission update requests
5. **Two-Factor**: Require 2FA for permission management access

## 📊 Performance Optimization

1. **Eager Loading**: Load all user permissions on login
2. **Redis Caching**: Store permissions in Redis for fast lookup
3. **Database Indexes**: Optimize queries with proper indexes
4. **Bulk Operations**: Use bulk inserts for permission updates

## 🎯 Success Metrics

1. **Reduced Support Tickets**: Less confusion about access rights
2. **Faster Onboarding**: Easy to set up new user groups
3. **Improved Security**: Granular control reduces over-permissioning
4. **Audit Compliance**: Complete trail of permission changes

## Timeline

- Week 1-2: Database schema and backend services
- Week 3-4: UI development and integration
- Week 5: Testing and refinement
- Week 6: Documentation and deployment

This permission management system will provide Fit360 AdminDesk with enterprise-level access control while maintaining ease of use for administrators.