<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\EmailTemplate;
use App\Models\Setting;
use PDO;

class ReminderService {
    
    private $db;
    private $emailService;
    private $settings;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->emailService = new EmailService();
        $this->loadSettings();
    }
    
    /**
     * Load reminder settings from database
     */
    private function loadSettings() {
        $settingModel = new Setting();
        $reminderSettings = $settingModel->where('key', 'payment_reminders')->first();
        
        $this->settings = $reminderSettings ? json_decode($reminderSettings->value, true) : [
            'enabled' => false,
            'reminder_days' => [7, 14, 30],
            'max_reminders' => 3,
            'templates' => [
                1 => 'payment_reminder_friendly',
                2 => 'payment_reminder_firm', 
                3 => 'payment_reminder_urgent'
            ]
        ];
    }
    
    /**
     * Check for overdue invoices and send reminders
     */
    public function checkOverdueInvoices() {
        if (!$this->settings['enabled']) {
            return ['status' => 'disabled', 'message' => 'Payment reminders are disabled'];
        }
        
        $results = [
            'checked' => 0,
            'sent' => 0,
            'errors' => 0,
            'details' => []
        ];
        
        // Get all unpaid invoices
        $sql = "SELECT i.*, c.email, c.first_name, c.last_name, c.company_name,
                DATEDIFF(CURDATE(), i.invoice_date) as days_overdue,
                COUNT(rl.id) as reminder_count,
                MAX(rl.sent_date) as last_reminder_date
                FROM invoices i
                LEFT JOIN clients c ON i.client_id = c.id
                LEFT JOIN reminder_logs rl ON i.id = rl.invoice_id
                WHERE i.status = 'pending' 
                AND i.archived = 0
                AND DATEDIFF(CURDATE(), i.invoice_date) > 0
                GROUP BY i.id
                HAVING reminder_count < :max_reminders";
                
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['max_reminders' => $this->settings['max_reminders']]);
        
        while ($invoice = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $results['checked']++;
            
            // Check if it's time for next reminder
            $reminderNumber = $invoice['reminder_count'] + 1;
            if ($reminderNumber > count($this->settings['reminder_days'])) {
                continue;
            }
            
            $requiredDays = $this->settings['reminder_days'][$reminderNumber - 1];
            
            if ($invoice['days_overdue'] >= $requiredDays) {
                // Check if enough time has passed since last reminder
                if ($invoice['last_reminder_date']) {
                    $daysSinceLastReminder = (new \DateTime())->diff(new \DateTime($invoice['last_reminder_date']))->days;
                    if ($daysSinceLastReminder < 3) { // Wait at least 3 days between reminders
                        continue;
                    }
                }
                
                // Send reminder
                $sent = $this->sendReminder($invoice, $reminderNumber);
                
                if ($sent) {
                    $results['sent']++;
                    $results['details'][] = [
                        'invoice' => $invoice['invoice_number'],
                        'client' => $invoice['company_name'] ?: $invoice['first_name'] . ' ' . $invoice['last_name'],
                        'reminder' => $reminderNumber,
                        'days_overdue' => $invoice['days_overdue']
                    ];
                } else {
                    $results['errors']++;
                }
            }
        }
        
        return $results;
    }
    
    /**
     * Send payment reminder for an invoice
     */
    public function sendReminder($invoice, $reminderNumber) {
        try {
            // Get the appropriate template
            $templateKey = $this->settings['templates'][$reminderNumber] ?? 'payment_reminder_default';
            
            $templateModel = new EmailTemplate();
            $template = $templateModel->where('key', $templateKey)->where('active', 1)->first();
            
            if (!$template) {
                // Use default template if specific one not found
                $template = $templateModel->where('key', 'payment_reminder_default')->where('active', 1)->first();
            }
            
            if (!$template) {
                throw new \Exception("No payment reminder template found");
            }
            
            // Prepare email data
            $emailData = [
                'to' => $invoice['email'],
                'to_name' => $invoice['company_name'] ?: $invoice['first_name'] . ' ' . $invoice['last_name'],
                'subject' => $template->subject,
                'body' => $template->body,
                'template_id' => $template->id,
                'related_type' => 'invoice_reminder',
                'related_id' => $invoice['id']
            ];
            
            // Prepare variables for template
            $invoiceModel = new Invoice();
            $fullInvoice = $invoiceModel->find($invoice['id']);
            
            if ($fullInvoice) {
                $templateVars = $this->emailService->prepareTemplateVariables([
                    'invoice' => $fullInvoice,
                    'client' => $fullInvoice->client,
                    'reminder_number' => $reminderNumber,
                    'days_overdue' => $invoice['days_overdue'],
                    'original_due_date' => $invoice['due_date'] ?? $invoice['invoice_date']
                ]);
                
                // Add reminder-specific variables
                $templateVars['reminder'] = [
                    'number' => $reminderNumber,
                    'days_overdue' => $invoice['days_overdue'],
                    'is_first' => $reminderNumber == 1,
                    'is_final' => $reminderNumber == $this->settings['max_reminders']
                ];
                
                $emailData['variables'] = $templateVars;
            }
            
            // Send the email
            $sent = $this->emailService->send($emailData);
            
            if ($sent) {
                // Log the reminder
                $this->logReminder($invoice['id'], $reminderNumber);
            }
            
            return $sent;
            
        } catch (\Exception $e) {
            error_log("Error sending payment reminder: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log that a reminder was sent
     */
    private function logReminder($invoiceId, $reminderNumber) {
        $sql = "INSERT INTO reminder_logs (invoice_id, sent_date, reminder_number, created_at) 
                VALUES (:invoice_id, NOW(), :reminder_number, NOW())";
                
        $stmt = $this->db->prepare($sql);
        $stmt->execute([
            'invoice_id' => $invoiceId,
            'reminder_number' => $reminderNumber
        ]);
    }
    
    /**
     * Get reminder history for an invoice
     */
    public function getReminderHistory($invoiceId) {
        $sql = "SELECT * FROM reminder_logs 
                WHERE invoice_id = :invoice_id 
                ORDER BY sent_date DESC";
                
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['invoice_id' => $invoiceId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if client is excluded from reminders
     */
    public function isClientExcluded($clientId) {
        $sql = "SELECT excluded_from_reminders 
                FROM clients 
                WHERE id = :client_id";
                
        $stmt = $this->db->prepare($sql);
        $stmt->execute(['client_id' => $clientId]);
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result && $result['excluded_from_reminders'] == 1;
    }
    
    /**
     * Update reminder settings
     */
    public function updateSettings($settings) {
        $settingModel = new Setting();
        
        $existing = $settingModel->where('key', 'payment_reminders')->first();
        
        if ($existing) {
            $existing->value = json_encode($settings);
            $existing->save();
        } else {
            $settingModel->create([
                'key' => 'payment_reminders',
                'value' => json_encode($settings),
                'group' => 'email_automation'
            ]);
        }
        
        $this->settings = $settings;
    }
}