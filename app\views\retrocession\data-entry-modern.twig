{% extends "base-modern.twig" %}

{% block title %}{{ __('retrocession.data_entry') }} - {{ practitioner.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ base_url }}">{{ __('common.home') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ base_url }}/retrocession">{{ __('retrocession.title') }}</a></li>
                    <li class="breadcrumb-item active">{{ __('retrocession.data_entry') }}</li>
                </ol>
            </nav>
            <h1 class="h3 mb-0 text-gray-800 mt-2">
                {{ __('retrocession.data_entry') }} - {{ practitioner.name }}
                <small class="text-muted">{{ monthName }} {{ year }}</small>
            </h1>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/retrocession?month={{ month }}&year={{ year }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
        </div>
    </div>

    <!-- Alert if already exists -->
    {% if dataEntry and dataEntry.status == 'confirmed' %}
    <div class="alert alert-warning">
        <i class="bi bi-exclamation-triangle me-2"></i>
        {{ __('retrocession.data_already_confirmed') }}
    </div>
    {% endif %}

    <!-- Data Entry Form -->
    <form id="dataEntryForm" method="post" action="{{ base_url }}/retrocession/save-data-entry">
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" name="practitioner_id" value="{{ practitioner.id }}">
        <input type="hidden" name="month" value="{{ month }}">
        <input type="hidden" name="year" value="{{ year }}">
        
        <div class="row">
            <!-- Main Entry Form -->
            <div class="col-lg-8">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ __('retrocession.monthly_data') }}</h5>
                    </div>
                    <div class="card-body">
                        {% if suggestion and not dataEntry %}
                        <div class="alert alert-info">
                            <i class="bi bi-lightbulb me-2"></i>
                            {{ __('retrocession.suggestion_available') }}
                            <button type="button" class="btn btn-sm btn-primary ms-2" onclick="applySuggestion()">
                                {{ __('retrocession.apply_suggestion') }}
                            </button>
                        </div>
                        {% endif %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('retrocession.cns_amount') }} (€)</label>
                                    <input type="number" name="cns_amount" id="cns_amount" class="form-control" 
                                           step="0.01" value="{{ dataEntry.cns_amount|default(0) }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('retrocession.patient_amount') }} (€)</label>
                                    <input type="number" name="patient_amount" id="patient_amount" class="form-control" 
                                           step="0.01" value="{{ dataEntry.patient_amount|default(0) }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label class="form-label">{{ __('retrocession.total_amount') }} (€)</label>
                                    <input type="number" name="total_amount" id="total_amount" class="form-control" 
                                           step="0.01" value="{{ dataEntry.total_amount|default(0) }}" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">{{ __('common.notes') }}</label>
                            <textarea name="notes" class="form-control" rows="3">{{ dataEntry.notes|default('') }}</textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Retrocession Calculation Preview -->
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="mb-0">{{ __('retrocession.calculation_preview') }}</h5>
                    </div>
                    <div class="card-body">
                        <div id="calculationPreview">
                            <p class="text-muted">{{ __('retrocession.enter_amounts_to_preview') }}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Side Panel -->
            <div class="col-lg-4">
                <!-- Rate Information -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">{{ __('retrocession.current_rates') }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-6">{{ __('retrocession.cns_rate') }}:</dt>
                            <dd class="col-6">{{ rates.cns_percent }} %</dd>
                            
                            <dt class="col-6">{{ __('retrocession.patient_rate') }}:</dt>
                            <dd class="col-6">{{ rates.patient_percent }} %</dd>
                            
                            <dt class="col-6">{{ __('retrocession.secretariat_rate') }}:</dt>
                            <dd class="col-6">{{ rates.secretariat_percent }} %</dd>
                        </dl>
                    </div>
                </div>
                
                <!-- CNS Import Info -->
                {% if cnsImports|length > 0 %}
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">{{ __('retrocession.cns_imports') }}</h6>
                    </div>
                    <div class="card-body">
                        {% for import in cnsImports %}
                        <div class="mb-2">
                            <small class="text-muted">{{ import.import_date|date('d/m/Y') }}</small><br>
                            <strong>{{ import.total_amount|number_format(2, ',', '.') }}€</strong>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
                
                <!-- Historical Data -->
                {% if history|length > 0 %}
                <div class="card shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">{{ __('retrocession.historical_data') }}</h6>
                    </div>
                    <div class="card-body">
                        <small>
                            {% for item in history %}
                            <div class="mb-2">
                                <strong>{{ item.month_name }} {{ item.year }}</strong><br>
                                CNS: {{ item.cns_amount|number_format(2, ',', '.') }}€<br>
                                Patient: {{ item.patient_amount|number_format(2, ',', '.') }}€
                            </div>
                            {% endfor %}
                        </small>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ base_url }}/retrocession?month={{ month }}&year={{ year }}" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.cancel') }}
            </a>
            <div>
                <button type="submit" name="action" value="save" class="btn btn-primary">
                    <i class="bi bi-save me-2"></i>{{ __('common.save') }}
                </button>
                {% if not dataEntry or dataEntry.status == 'draft' %}
                <button type="submit" name="action" value="confirm" class="btn btn-success">
                    <i class="bi bi-check-circle me-2"></i>{{ __('retrocession.save_and_confirm') }}
                </button>
                {% endif %}
            </div>
        </div>
    </form>
</div>

<script>
// Suggestion data
{% if suggestion %}
const suggestion = {{ suggestion|json_encode|raw }};
{% else %}
const suggestion = null;
{% endif %}

// Apply suggestion
function applySuggestion() {
    if (suggestion) {
        document.getElementById('cns_amount').value = suggestion.cns_amount.toFixed(2);
        document.getElementById('patient_amount').value = suggestion.patient_amount.toFixed(2);
        calculateTotal();
        updatePreview();
    }
}

// Calculate total
function calculateTotal() {
    const cnsAmount = parseFloat(document.getElementById('cns_amount').value) || 0;
    const patientAmount = parseFloat(document.getElementById('patient_amount').value) || 0;
    const total = cnsAmount + patientAmount;
    document.getElementById('total_amount').value = total.toFixed(2);
}

// Update calculation preview
function updatePreview() {
    const cnsAmount = parseFloat(document.getElementById('cns_amount').value) || 0;
    const patientAmount = parseFloat(document.getElementById('patient_amount').value) || 0;
    const total = cnsAmount + patientAmount;
    
    if (total > 0) {
        const cnsRate = {{ rates.cns_percent }};
        const patientRate = {{ rates.patient_percent }};
        const secretariatRate = {{ rates.secretariat_percent }};
        
        const cnsRetro = cnsAmount * (cnsRate / 100);
        const patientRetro = patientAmount * (patientRate / 100);
        const secretariatAmount = total * (secretariatRate / 100);
        const totalRetro = cnsRetro + patientRetro + secretariatAmount;
        
        const html = `
            <table class="table table-sm">
                <tr>
                    <td>{{ __('retrocession.cns_retrocession') }}:</td>
                    <td class="text-end">€ ${cnsRetro.toFixed(2)} (${cnsRate}%)</td>
                </tr>
                <tr>
                    <td>{{ __('retrocession.patient_retrocession') }}:</td>
                    <td class="text-end">€ ${patientRetro.toFixed(2)} (${patientRate}%)</td>
                </tr>
                <tr>
                    <td>{{ __('retrocession.secretariat_fee') }}:</td>
                    <td class="text-end">€ ${secretariatAmount.toFixed(2)} (${secretariatRate}%)</td>
                </tr>
                <tr class="table-active">
                    <td><strong>{{ __('retrocession.total_retrocession') }}:</strong></td>
                    <td class="text-end"><strong>€ ${totalRetro.toFixed(2)}</strong></td>
                </tr>
            </table>
        `;
        
        document.getElementById('calculationPreview').innerHTML = html;
    } else {
        document.getElementById('calculationPreview').innerHTML = '<p class="text-muted">{{ __('retrocession.enter_amounts_to_preview') }}</p>';
    }
}

// Event listeners
document.getElementById('cns_amount').addEventListener('input', function() {
    calculateTotal();
    updatePreview();
});

document.getElementById('patient_amount').addEventListener('input', function() {
    calculateTotal();
    updatePreview();
});

// Initialize on load
calculateTotal();
updatePreview();

// Form submission
document.getElementById('dataEntryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = this;
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]:focus');
    const action = submitBtn ? submitBtn.value : 'save';
    formData.append('action', action);
    
    // Disable buttons
    form.querySelectorAll('button').forEach(btn => btn.disabled = true);
    
    fetch(form.action, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof toastr !== 'undefined') {
                toastr.success(data.message);
            }
            
            // Redirect after short delay
            setTimeout(() => {
                window.location.href = '{{ base_url }}/retrocession?month={{ month }}&year={{ year }}';
            }, 1000);
        } else {
            if (typeof toastr !== 'undefined') {
                toastr.error(data.message || '{{ __("common.error_occurred") }}');
            }
            form.querySelectorAll('button').forEach(btn => btn.disabled = false);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        toastr.error('{{ __("common.error_occurred") }}');
        form.querySelectorAll('button').forEach(btn => btn.disabled = false);
    });
});
</script>
{% endblock %}