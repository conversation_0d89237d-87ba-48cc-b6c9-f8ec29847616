<?php

namespace App\Middleware;

use Flight;
use App\Services\PermissionService;

class PermissionMiddleware
{
    /**
     * Check if user has required permission
     * 
     * @param string $permission Permission code required
     * @param string $redirectTo Where to redirect if permission denied (default: dashboard)
     * @param string $message Flash message to display
     * @param bool $force Force permission check without cache
     * @return callable
     */
    public static function require($permission, $redirectTo = '/dashboard', $message = null, $force = false)
    {
        return function() use ($permission, $redirectTo, $message, $force) {
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->hasPermission($permission, $force)) {
                $message = $message ?: "You don't have permission to access this page.";
                $_SESSION['flash']['error'] = $message;
                
                // Check if mobile and handle differently
                require_once APP_PATH . '/helpers/mobile_helpers.php';
                if (is_mobile() && Flight::request()->ajax === false) {
                    // Store permission info for error page
                    $_SESSION['permission_denied'] = [
                        'required' => $permission,
                        'message' => $message,
                        'timestamp' => time()
                    ];
                    
                    // Use mobile-friendly error page
                    Flight::render('errors/permission-denied-mobile', [
                        'message' => $message,
                        'required_permission' => $permission
                    ]);
                    exit;
                }
                
                Flight::redirect($redirectTo);
                exit;
            }
            
            return true;
        };
    }
    
    /**
     * Check if user has any of the required permissions
     * 
     * @param array $permissions Array of permission codes
     * @param string $redirectTo Where to redirect if permission denied
     * @param string $message Flash message to display
     * @return callable
     */
    public static function requireAny(array $permissions, $redirectTo = '/dashboard', $message = null)
    {
        return function() use ($permissions, $redirectTo, $message) {
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->hasAnyPermission($permissions)) {
                $message = $message ?: "You don't have permission to access this page.";
                $_SESSION['flash']['error'] = $message;
                Flight::redirect($redirectTo);
                exit;
            }
            
            return true;
        };
    }
    
    /**
     * Check if user has all of the required permissions
     * 
     * @param array $permissions Array of permission codes
     * @param string $redirectTo Where to redirect if permission denied
     * @param string $message Flash message to display
     * @return callable
     */
    public static function requireAll(array $permissions, $redirectTo = '/dashboard', $message = null)
    {
        return function() use ($permissions, $redirectTo, $message) {
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->hasAllPermissions($permissions)) {
                $message = $message ?: "You don't have all required permissions to access this page.";
                $_SESSION['flash']['error'] = $message;
                Flight::redirect($redirectTo);
                exit;
            }
            
            return true;
        };
    }
    
    /**
     * Check permission and return JSON error if not authorized (for API endpoints)
     * 
     * @param string $permission Permission code required
     * @return callable
     */
    public static function requireApi($permission)
    {
        return function() use ($permission) {
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->hasPermission($permission)) {
                Flight::json([
                    'success' => false,
                    'error' => 'Unauthorized',
                    'message' => "You don't have permission to perform this action.",
                    'required_permission' => $permission
                ], 403);
                exit;
            }
            
            return true;
        };
    }
    
    /**
     * Check if user has any of the required permissions (API version)
     * 
     * @param array $permissions Array of permission codes
     * @return callable
     */
    public static function requireAnyApi(array $permissions)
    {
        return function() use ($permissions) {
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->hasAnyPermission($permissions)) {
                Flight::json([
                    'success' => false,
                    'error' => 'Unauthorized',
                    'message' => "You don't have permission to perform this action.",
                    'required_permissions' => $permissions
                ], 403);
                exit;
            }
            
            return true;
        };
    }
    
    /**
     * Super admin only middleware
     * 
     * @param string $redirectTo Where to redirect if not super admin
     * @return callable
     */
    public static function superAdminOnly($redirectTo = '/dashboard')
    {
        return function() use ($redirectTo) {
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->isSuperAdmin()) {
                $_SESSION['flash']['error'] = "This action is restricted to super administrators only.";
                Flight::redirect($redirectTo);
                exit;
            }
            
            return true;
        };
    }
    
    /**
     * Module-based permission check
     * 
     * @param string $module Module code (e.g., 'users', 'invoices')
     * @param string $action Action (e.g., 'create', 'update', 'delete')
     * @param string $redirectTo Where to redirect if permission denied
     * @return callable
     */
    public static function requireModuleAction($module, $action, $redirectTo = '/dashboard')
    {
        $permission = $module . '.' . $action;
        return self::require($permission, $redirectTo);
    }
    
    /**
     * Dynamic permission check based on request parameter
     * Useful for routes where permission depends on the resource being accessed
     * 
     * @param callable $permissionResolver Function that returns the required permission based on request
     * @param string $redirectTo Where to redirect if permission denied
     * @return callable
     */
    public static function requireDynamic(callable $permissionResolver, $redirectTo = '/dashboard')
    {
        return function() use ($permissionResolver, $redirectTo) {
            $permission = $permissionResolver(Flight::request());
            
            if (!$permission) {
                return true; // No permission required
            }
            
            $permissionService = PermissionService::getInstance();
            
            if (!$permissionService->hasPermission($permission)) {
                $_SESSION['flash']['error'] = "You don't have permission to access this resource.";
                Flight::redirect($redirectTo);
                exit;
            }
            
            return true;
        };
    }
}