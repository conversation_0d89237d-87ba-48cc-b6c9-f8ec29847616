<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain; charset=utf-8');

try {
    // Get DB connection from Flight or create new one
    try {
        $pdo = Flight::db();
    } catch (Exception $e) {
        // If Flight is not initialized, create direct connection
        $pdo = new PDO('mysql:host=127.0.0.1;dbname=fitapp;charset=utf8mb4', 'root', 'test1234');
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    }
    
    echo "=== CAPITALIZING USERNAMES ===\n\n";
    
    // Get all users
    $stmt = $pdo->query("SELECT id, username, first_name, last_name FROM users ORDER BY id");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Current users:\n";
    echo str_pad("ID", 5) . str_pad("Current Username", 20) . str_pad("New Username", 20) . "Name\n";
    echo str_repeat("-", 70) . "\n";
    
    $updates = [];
    
    foreach ($users as $user) {
        // Capitalize first letter
        $newUsername = ucfirst(strtolower($user['username']));
        
        // Special case for names that should stay uppercase (like ID-based usernames)
        if (preg_match('/^[A-Z0-9_]+$/', $user['username'])) {
            // If it's all uppercase with numbers/underscores, leave it
            $newUsername = $user['username'];
        }
        
        echo str_pad($user['id'], 5) . 
             str_pad($user['username'], 20) . 
             str_pad($newUsername, 20) . 
             "{$user['first_name']} {$user['last_name']}\n";
        
        if ($user['username'] !== $newUsername) {
            $updates[] = [
                'id' => $user['id'],
                'old' => $user['username'],
                'new' => $newUsername
            ];
        }
    }
    
    if (count($updates) > 0) {
        echo "\n\nUpdating " . count($updates) . " usernames...\n\n";
        
        foreach ($updates as $update) {
            // Check if new username already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username = :username AND id != :id");
            $stmt->execute([
                ':username' => $update['new'],
                ':id' => $update['id']
            ]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] > 0) {
                echo "⚠️  Cannot update {$update['old']} to {$update['new']} - username already exists\n";
                continue;
            }
            
            // Update username
            $stmt = $pdo->prepare("UPDATE users SET username = :username WHERE id = :id");
            $stmt->execute([
                ':username' => $update['new'],
                ':id' => $update['id']
            ]);
            
            echo "✅ Updated: {$update['old']} → {$update['new']}\n";
        }
        
        echo "\n✅ All usernames have been updated!\n";
    } else {
        echo "\n\nℹ️  No usernames need to be updated - all are already properly capitalized.\n";
    }
    
} catch (PDOException $e) {
    echo "Database error: " . $e->getMessage() . "\n";
    exit(1);
}