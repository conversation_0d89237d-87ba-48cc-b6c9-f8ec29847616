<?php
return [
    // Page titles
    'title' => 'Produktkategorien',
    'create' => 'Kategorie erstellen',
    'edit' => 'Kategorie bearbeiten',
    'list' => 'Kategorienliste',
    
    // Buttons
    'create_button' => 'Neue Kategorie',
    'back_to_list' => '<PERSON>ur<PERSON> zu Kategorien',
    
    // Table headers
    'name' => 'Name',
    'parent' => 'Übergeordnete Kategorie',
    'products_count' => 'Produkte',
    'sort_order' => 'Sortierreihenfolge',
    'icon' => 'Symbol',
    'is_active' => 'Aktiv',
    'actions' => 'Aktionen',
    
    // Form fields
    'no_parent' => 'Keine übergeordnete (Hauptkategorie)',
    'icon_help' => 'Font Awesome Symbol-Klasse (z.B. fa-box)',
    'sort_order_help' => 'Niedrigere Zahlen erscheinen zuerst',
    
    // Messages
    'no_categories' => 'Keine Kategorien gefunden',
    'created_success' => 'Kategorie erfolgreich erstellt',
    'updated_success' => 'Kategorie erfolgreich aktualisiert',
    'deleted_success' => 'Kategorie erfolgreich gelöscht',
    'has_products' => 'Kategorie mit Produkten kann nicht gelöscht werden',
    'has_children' => 'Kategorie mit Unterkategorien kann nicht gelöscht werden',
    
    // Validation
    'name_required' => 'Kategoriename ist erforderlich',
];