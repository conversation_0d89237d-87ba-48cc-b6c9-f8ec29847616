/**
 * Email Automation Settings Manager
 * Handles email automation configuration UI
 */

class EmailAutomationSettings {
    constructor() {
        this.form = document.getElementById('emailAutomationForm');
        this.masterEnable = document.getElementById('masterEnable');
        this.remindersEnable = document.getElementById('remindersEnable');
        this.reminderSettings = document.getElementById('reminderSettings');
        this.remindersList = document.getElementById('remindersList');
        this.addReminderBtn = document.getElementById('addReminder');
        this.testTemplateSelect = document.getElementById('testTemplate');
        this.testEmailInput = document.getElementById('testEmail');
        this.sendTestBtn = document.getElementById('sendTestEmail');
        this.previewBtn = document.getElementById('previewTemplate');
        this.previewModal = $('#previewModal');
        
        this.reminderCount = this.remindersList ? this.remindersList.querySelectorAll('.reminder-row').length : 0;
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateUI();
    }
    
    bindEvents() {
        // Master enable/disable
        if (this.masterEnable) {
            this.masterEnable.addEventListener('change', () => this.updateUI());
        }
        
        // Payment reminders enable/disable
        if (this.remindersEnable) {
            this.remindersEnable.addEventListener('change', () => {
                this.reminderSettings.style.display = this.remindersEnable.checked ? 'block' : 'none';
            });
        }
        
        // Delay select changes
        document.querySelectorAll('.delay-select').forEach(select => {
            select.addEventListener('change', (e) => {
                const typeId = e.target.dataset.typeId;
                const customInput = document.querySelector(`.custom-delay-input[data-type-id="${typeId}"]`);
                if (customInput) {
                    customInput.style.display = e.target.value === 'custom' ? 'block' : 'none';
                }
            });
        });
        
        // Add reminder button
        if (this.addReminderBtn) {
            this.addReminderBtn.addEventListener('click', () => this.addReminder());
        }
        
        // Remove reminder buttons
        document.addEventListener('click', (e) => {
            if (e.target.closest('.remove-reminder')) {
                e.preventDefault();
                this.removeReminder(e.target.closest('.reminder-row'));
            }
        });
        
        // Test email button
        if (this.sendTestBtn) {
            this.sendTestBtn.addEventListener('click', () => this.sendTestEmail());
        }
        
        // Preview button
        if (this.previewBtn) {
            this.previewBtn.addEventListener('click', () => this.previewTemplate());
        }
        
        // Form submission
        if (this.form) {
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
        }
        
        // Invoice type enable toggles
        document.querySelectorAll('.invoice-type-enable').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const row = e.target.closest('tr');
                const inputs = row.querySelectorAll('select, input:not(.invoice-type-enable)');
                inputs.forEach(input => {
                    input.disabled = !e.target.checked;
                });
            });
        });
    }
    
    updateUI() {
        const isEnabled = this.masterEnable && this.masterEnable.checked;
        
        // Enable/disable all form elements based on master switch
        const formElements = this.form.querySelectorAll('input:not(#masterEnable), select, button:not([type="submit"])');
        formElements.forEach(el => {
            if (!el.closest('.card-primary')) { // Don't disable elements in master control card
                el.disabled = !isEnabled;
            }
        });
        
        // Update invoice type rows based on their individual enables
        if (isEnabled) {
            document.querySelectorAll('.invoice-type-enable').forEach(checkbox => {
                const row = checkbox.closest('tr');
                const inputs = row.querySelectorAll('select, input:not(.invoice-type-enable)');
                inputs.forEach(input => {
                    input.disabled = !checkbox.checked;
                });
            });
        }
    }
    
    addReminder() {
        const newRow = document.createElement('tr');
        newRow.className = 'reminder-row';
        newRow.innerHTML = `
            <td>
                <span class="badge badge-warning">
                    ${window.__('email.automation.reminder')} ${this.reminderCount + 1}
                </span>
            </td>
            <td>
                <input type="number" class="form-control form-control-sm" 
                       name="settings[payment_reminders][reminders][${this.reminderCount}][days]"
                       value="${(this.reminderCount + 1) * 7}" min="1" max="365">
            </td>
            <td>
                <select class="form-control form-control-sm" 
                        name="settings[payment_reminders][reminders][${this.reminderCount}][template_id]">
                    <option value="">${window.__('email.automation.select_template')}</option>
                    ${this.getTemplateOptions()}
                </select>
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-sm btn-danger remove-reminder">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        this.remindersList.appendChild(newRow);
        this.reminderCount++;
        this.updateReminderNumbers();
    }
    
    removeReminder(row) {
        if (confirm(window.__('messages.confirm_delete'))) {
            row.remove();
            this.updateReminderNumbers();
        }
    }
    
    updateReminderNumbers() {
        const rows = this.remindersList.querySelectorAll('.reminder-row');
        rows.forEach((row, index) => {
            // Update badge number
            const badge = row.querySelector('.badge');
            if (badge) {
                badge.textContent = `${window.__('email.automation.reminder')} ${index + 1}`;
            }
            
            // Update input names
            const daysInput = row.querySelector('input[type="number"]');
            const templateSelect = row.querySelector('select');
            
            if (daysInput) {
                daysInput.name = `settings[payment_reminders][reminders][${index}][days]`;
            }
            if (templateSelect) {
                templateSelect.name = `settings[payment_reminders][reminders][${index}][template_id]`;
            }
        });
        
        this.reminderCount = rows.length;
    }
    
    getTemplateOptions() {
        // Get template options from the test template select
        const options = [];
        const testSelect = this.testTemplateSelect;
        
        if (testSelect) {
            testSelect.querySelectorAll('optgroup').forEach(group => {
                const label = group.label;
                if (label.includes('payment_reminder') || label.includes('general')) {
                    options.push(`<optgroup label="${group.label}">`);
                    group.querySelectorAll('option').forEach(option => {
                        if (option.value) {
                            options.push(`<option value="${option.value}">${option.textContent}</option>`);
                        }
                    });
                    options.push('</optgroup>');
                }
            });
        }
        
        return options.join('');
    }
    
    async sendTestEmail() {
        const templateId = this.testTemplateSelect.value;
        const email = this.testEmailInput.value;
        
        if (!templateId) {
            this.showAlert('warning', window.__('email.automation.select_template_first'));
            return;
        }
        
        if (!email) {
            this.showAlert('warning', window.__('email.automation.enter_email_first'));
            return;
        }
        
        this.sendTestBtn.disabled = true;
        this.sendTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + window.__('common.sending');
        
        try {
            const response = await fetch('/admin/email-automation/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify({
                    template_id: templateId,
                    email: email
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.showAlert('success', data.message);
            } else {
                this.showAlert('danger', data.message || window.__('errors.operation_failed'));
            }
        } catch (error) {
            console.error('Error sending test email:', error);
            this.showAlert('danger', window.__('errors.network_error'));
        } finally {
            this.sendTestBtn.disabled = false;
            this.sendTestBtn.innerHTML = '<i class="fas fa-paper-plane"></i> ' + window.__('email.automation.send_test');
        }
    }
    
    async previewTemplate() {
        const templateId = this.testTemplateSelect.value;
        
        if (!templateId) {
            this.showAlert('warning', window.__('email.automation.select_template_first'));
            return;
        }
        
        this.previewModal.modal('show');
        const previewContent = document.getElementById('previewContent');
        previewContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i></div>';
        
        try {
            const response = await fetch(`/admin/email-automation/preview/${templateId}`, {
                headers: {
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
                }
            });
            
            const data = await response.json();
            
            if (data.success) {
                previewContent.innerHTML = `
                    <div class="preview-container">
                        <h6>${window.__('email.subject')}:</h6>
                        <p class="border p-2 bg-light">${data.preview.subject}</p>
                        
                        <h6>${window.__('email.body_html')}:</h6>
                        <div class="border p-3 bg-white">
                            ${data.preview.body_html}
                        </div>
                        
                        <h6 class="mt-3">${window.__('email.body_text')}:</h6>
                        <pre class="border p-2 bg-light">${data.preview.body_text}</pre>
                        
                        <h6 class="mt-3">${window.__('email.available_variables')}:</h6>
                        <div class="row">
                            ${Object.entries(data.preview.variables).map(([key, value]) => `
                                <div class="col-md-6">
                                    <code>{${key}}</code>: ${value}
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            } else {
                previewContent.innerHTML = `
                    <div class="alert alert-danger">
                        ${data.message || window.__('errors.operation_failed')}
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error previewing template:', error);
            previewContent.innerHTML = `
                <div class="alert alert-danger">
                    ${window.__('errors.network_error')}
                </div>
            `;
        }
    }
    
    async handleSubmit(e) {
        e.preventDefault();
        
        const submitBtn = this.form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + window.__('common.saving');
        
        try {
            const formData = new FormData(this.form);
            const data = this.formDataToObject(formData);
            
            const response = await fetch(this.form.action, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]')?.content
                },
                body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showAlert('success', result.message);
            } else {
                this.showAlert('danger', result.message || window.__('errors.save_failed'));
            }
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showAlert('danger', window.__('errors.network_error'));
        } finally {
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }
    
    formDataToObject(formData) {
        const obj = {};
        
        for (let [key, value] of formData.entries()) {
            // Parse nested keys like settings[invoice_types][1][enabled]
            const keys = key.split(/[\[\]]+/).filter(k => k);
            
            let current = obj;
            for (let i = 0; i < keys.length - 1; i++) {
                const k = keys[i];
                if (!current[k]) {
                    // Check if next key is numeric (array index)
                    current[k] = isNaN(keys[i + 1]) ? {} : [];
                }
                current = current[k];
            }
            
            const lastKey = keys[keys.length - 1];
            
            // Handle checkboxes
            if (value === '1' || value === 'on') {
                value = true;
            } else if (value === '0' || value === '') {
                // Skip empty values for non-checkbox fields
                if (formData.getAll(key).length === 1) {
                    continue;
                }
            }
            
            // Convert numeric strings to numbers
            if (!isNaN(value) && value !== '') {
                value = Number(value);
            }
            
            current[lastKey] = value;
        }
        
        return obj;
    }
    
    showAlert(type, message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        `;
        
        const cardBody = this.form.closest('.card-body');
        cardBody.insertBefore(alertDiv, cardBody.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            $(alertDiv).alert('close');
        }, 5000);
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', () => {
    window.emailAutomationSettings = new EmailAutomationSettings();
});