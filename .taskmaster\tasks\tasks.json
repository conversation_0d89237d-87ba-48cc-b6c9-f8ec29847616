{"master": {"tasks": [{"id": 1, "title": "Implement Bulk Invoice Generation for Coaches Location Fees", "description": "Develop a feature that allows administrators to generate monthly location/rental invoices for multiple coaches simultaneously, similar to the existing LOY bulk generation functionality.", "details": "This task involves creating a new bulk invoice generation system specifically for Coaches Location (LOC) invoices. The implementation should include:\n\n1. **User Interface Components**:\n   - Create a dedicated page or section in the admin interface for bulk LOC invoice generation\n   - Design a table/grid view showing all coaches with location fees configured\n   - Implement selection mechanisms (checkboxes) to choose which coaches to generate invoices for\n   - Add a \"Generate Invoices\" button to trigger the bulk generation process\n   - Design a progress indicator to show generation status\n\n2. **Backend Implementation**:\n   - Create a service to fetch all coaches with location fee configurations\n   - Implement logic to determine which coaches already have invoices generated for the current period\n   - Develop the core invoice generation algorithm that:\n     - Creates LOC type invoices\n     - Sets the correct period (previous month for services rendered)\n     - Applies the appropriate location fee amounts\n     - Handles any tax calculations if required\n   - Implement a tracking mechanism to prevent duplicate invoice generation\n   - Create a background job system to handle potentially long-running generation tasks\n\n3. **Data Model Updates**:\n   - Add fields to track when LOC invoices were last generated for each coach\n   - Create any necessary logging tables to maintain an audit trail of bulk generations\n\n4. **Progress Tracking**:\n   - Implement a real-time progress indicator using WebSockets or polling\n   - Show which coaches are being processed and completion status\n   - Provide error handling for failed invoice generations\n\n5. **Integration Points**:\n   - Ensure the bulk generation system integrates with the existing invoice data model\n   - Connect with any notification systems to alert coaches of new invoices\n   - Integrate with existing payment processing if applicable\n\nThe implementation should follow existing code patterns from the LOY bulk generation system while adapting to the specific requirements of location-based invoices.", "testStrategy": "1. **Unit Testing**:\n   - Write unit tests for the invoice generation service\n   - Test period calculation logic to ensure correct month handling\n   - Verify duplicate detection logic works correctly\n   - Test the coach selection and filtering functionality\n\n2. **Integration Testing**:\n   - Verify the bulk generation process correctly creates invoices in the database\n   - Test that the progress tracking system accurately reports generation status\n   - Ensure proper integration with existing invoice systems\n\n3. **UI Testing**:\n   - Verify all coaches with location fees appear in the selection grid\n   - Test the selection mechanism (individual selections and select all)\n   - Confirm the progress indicator updates properly during generation\n   - Test responsive behavior on different screen sizes\n\n4. **Validation Testing**:\n   - Create test coaches with various location fee configurations\n   - Generate invoices and verify correct fee amounts are applied\n   - Confirm proper period handling (previous month for services rendered)\n   - Attempt to generate duplicate invoices to verify prevention mechanisms\n\n5. **Performance Testing**:\n   - Test with a large number of coaches to ensure the system handles bulk operations efficiently\n   - Verify the background job system properly manages the workload\n\n6. **User Acceptance Testing**:\n   - Have administrative staff test the complete workflow\n   - Verify the generated invoices match expected formats and amounts\n   - Confirm the user interface is intuitive and provides clear feedback", "status": "in-progress", "dependencies": [], "priority": "high", "subtasks": [{"id": 1, "title": "Create UI components for bulk LOC invoice generation page", "description": "Develop the user interface components for the bulk location invoice generation feature, including the main page layout, coach selection table, and action buttons.", "dependencies": [], "details": "Create a new page in the admin interface specifically for LOC invoice generation. Implement a data table with columns for coach name, location details, fee amount, last invoice date, and selection checkbox. Add a header with title, description, and action buttons (Generate Invoices, Cancel). Include a date picker for selecting the invoice period. Follow existing UI patterns and styling from the LOY bulk generation interface.\n<info added on 2025-07-26T15:43:25.282Z>\nUpdate: LOC tab has been created in the bulk generation view. Investigation revealed that an LOC invoice type already exists but is currently used for course-based invoices. We need to make a decision on how to handle coach location/rental fees:\n\nOption 1: Create a new invoice type (e.g., RENT) specifically for coach location/rental fees\nOption 2: Continue using the existing LOC type but implement a mechanism to differentiate between course-based invoices and location fee invoices\n\nAdditionally, the expected database table `user_financial_locations` does not exist. We need to either:\n- Create this table to store coach location fee information\n- Identify and use an alternative existing data structure for storing this information\n\nThis decision impacts both the UI implementation and the backend data retrieval service.\n</info added on 2025-07-26T15:43:25.282Z>\n<info added on 2025-07-27T08:47:42.340Z>\nCode update in UnifiedInvoiceGenerator.php: Modified the getInvoiceNotes() method to display the current month instead of the next month for LOY invoices. This change ensures that invoice notes for LOY invoices correctly reference the current month period rather than calculating and showing the next month.\n</info added on 2025-07-27T08:47:42.340Z>", "status": "in-progress", "testStrategy": "Verify UI renders correctly across different screen sizes. Test that all interactive elements (checkboxes, buttons, date picker) function as expected. Ensure accessibility standards are met."}, {"id": 2, "title": "Implement coach data fetching service", "description": "Create a service to retrieve all coaches with location fee configurations and determine their invoice generation status for the selected period.", "dependencies": [], "details": "Develop a backend service that queries the database for all coaches who have location fee configurations. Include logic to determine which coaches already have LOC invoices generated for the selected period. The service should return coach data with fields for ID, name, location details, fee amount, last invoice generation date, and a flag indicating if an invoice already exists for the period. Implement pagination and sorting to handle potentially large datasets.", "status": "pending", "testStrategy": "Write unit tests to verify correct data retrieval and filtering. Test edge cases like coaches with multiple locations or missing fee configurations."}, {"id": 3, "title": "Create core invoice generation algorithm", "description": "Develop the central logic for generating location fee invoices with proper period calculation, fee application, and tax handling.", "dependencies": [], "details": "Implement the core invoice generation service that creates LOC type invoices. The service should calculate the correct invoice period (typically previous month for services rendered), apply the appropriate location fee amounts from coach configurations, handle any required tax calculations, and create properly formatted invoice records. Include logic to handle different fee structures if applicable (flat rate vs. percentage). Ensure generated invoices follow the existing invoice data model structure.", "status": "pending", "testStrategy": "Create comprehensive unit tests for period calculation, fee application, and tax handling. Test various fee configurations and edge cases like month transitions and leap years."}, {"id": 4, "title": "Implement bulk generation controller and background job system", "description": "Create the controller endpoint and background job infrastructure to handle potentially long-running bulk invoice generation tasks.", "dependencies": [], "details": "Develop an API endpoint that accepts the list of selected coach IDs and invoice period. Implement a background job system (using existing job queue infrastructure if available) to process invoice generation asynchronously. The controller should validate inputs, enqueue the background job, and return a job ID for tracking. The background job should process each coach sequentially, generating invoices using the core algorithm, and updating progress information. Include error handling and retry logic for failed generations.", "status": "pending", "testStrategy": "Test the controller with various input combinations. Verify background jobs execute correctly and handle errors appropriately. Test concurrency and performance with large batches of coaches."}, {"id": 5, "title": "Develop progress tracking and real-time updates", "description": "Implement a system to track generation progress and provide real-time updates to the user interface.", "dependencies": [], "details": "Create a progress tracking service that monitors the background job execution. Implement either WebSocket connections or a polling mechanism to provide real-time updates to the UI. The tracking system should maintain information about total coaches selected, number processed, current coach being processed, success count, error count, and detailed error messages. Store progress data in a temporary cache or database table that can be queried by the frontend.", "status": "pending", "testStrategy": "Test real-time update mechanisms under various network conditions. Verify progress calculations are accurate. Test recovery from connection interruptions."}, {"id": 6, "title": "Update data models for tracking and audit", "description": "Modify existing data models to track invoice generation history and create audit logging for bulk operations.", "dependencies": [], "details": "Add fields to the coach or invoice models to track when LOC invoices were last generated for each coach. Create a new audit log table to record bulk generation events with fields for operator ID, timestamp, selected period, number of invoices generated, and status. Implement database migrations to add these new fields and tables. Update the invoice generation service to record appropriate audit information during the generation process.", "status": "pending", "testStrategy": "Verify database migrations work correctly. Test that audit logs are properly created during invoice generation. Ensure tracking fields are correctly updated."}, {"id": 7, "title": "Integrate with notification and payment systems", "description": "Connect the bulk invoice generation system with existing notification mechanisms and payment processing if applicable.", "dependencies": [], "details": "Integrate the invoice generation process with the existing notification system to alert coaches when new location fee invoices are generated. If applicable, connect with payment processing systems to enable immediate payment options. Ensure notifications include appropriate invoice details and payment instructions. Implement configuration options to control notification behavior (immediate vs. batched notifications).", "status": "pending", "testStrategy": "Test that notifications are correctly triggered for new invoices. Verify notification content is accurate. If applicable, test payment system integration with test transactions."}, {"id": 8, "title": "Implement duplicate prevention and error handling", "description": "Develop mechanisms to prevent duplicate invoice generation and handle various error scenarios gracefully.", "dependencies": [], "details": "Implement robust duplicate detection logic that prevents generating multiple invoices for the same coach, location, and period. Create comprehensive error handling that categorizes errors (e.g., data validation errors, system errors) and provides meaningful feedback. Develop a recovery mechanism for failed generations that allows administrators to retry specific failed items without regenerating successful ones. Update the UI to display appropriate error messages and recovery options.", "status": "pending", "testStrategy": "Test duplicate prevention with various scenarios including concurrent generation attempts. Verify error handling for different error types (network issues, data problems, etc.). Test recovery mechanisms to ensure they correctly handle partial failures."}]}], "metadata": {"created": "2025-07-26T15:36:57.156Z", "updated": "2025-07-26T15:39:06.880Z", "description": "Tasks for master context"}}}