{% extends "base-modern.twig" %}

{% block title %}{{ __('categories.create') | default('Create Category') }}{% endblock %}

{% block breadcrumb %}
<ol class="breadcrumb mb-0">
    <li class="breadcrumb-item"><a href="{{ base_url }}/">{{ __('dashboard.title') | default('Dashboard') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products">{{ __('products.title') | default('Products') }}</a></li>
    <li class="breadcrumb-item"><a href="{{ base_url }}/products/categories">{{ __('categories.title') | default('Categories') }}</a></li>
    <li class="breadcrumb-item active">{{ __('common.create') | default('Create') }}</li>
</ol>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card shadow-sm">
            <form method="POST" action="{{ base_url }}/products/categories">
                <div class="card-header">
                    <h5 class="mb-0">{{ __('categories.create') | default('Create Category') }}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    {{ __('categories.name') | default('Category Name') }} <span class="text-danger">*</span>
                                </label>
                                <input type="text" class="form-control {% if errors.name is defined %}is-invalid{% endif %}" 
                                       id="name" name="name" value="{{ old.name | default('') }}" required>
                                {% if errors.name is defined %}
                                    <div class="invalid-feedback">{{ errors.name }}</div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sort_order" class="form-label">
                                    {{ __('categories.sort_order') | default('Sort Order') }}
                                </label>
                                <input type="number" class="form-control" id="sort_order" name="sort_order" 
                                       value="{{ old.sort_order | default('') }}" min="0">
                                <small class="text-muted">{{ __('categories.sort_order_help') | default('Leave empty for automatic ordering') }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            {{ __('categories.description') | default('Description') }}
                        </label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ old.description | default('') }}</textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="icon" class="form-label">
                                    {{ __('categories.icon') | default('Icon') }}
                                </label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="icon" name="icon" 
                                           value="{{ old.icon | default('') }}" placeholder="fas fa-box">
                                    <span class="input-group-text" id="icon-preview">
                                        <i class="{{ old.icon | default('fas fa-box') }}"></i>
                                    </span>
                                </div>
                                <small class="text-muted">
                                    {{ __('categories.icon_help') | default('FontAwesome icon class') }}
                                    <a href="https://fontawesome.com/icons" target="_blank">Browse icons</a>
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="color" class="form-label">
                                    {{ __('categories.color') | default('Color') }}
                                </label>
                                <input type="color" class="form-control form-control-color" id="color" 
                                       name="color" value="{{ old.color | default('#007bff') }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {% if old.is_active is not defined or old.is_active %}checked{% endif %}>
                            <label class="form-check-label" for="is_active">
                                {{ __('categories.is_active') | default('Active') }}
                            </label>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>{{ __('common.save') | default('Save') }}
                    </button>
                    <a href="{{ base_url }}/products/categories" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>{{ __('common.cancel') | default('Cancel') }}
                    </a>
                </div>
            </form>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow-sm">
            <div class="card-header">
                <h5 class="mb-0">{{ __('common.help') | default('Help') }}</h5>
            </div>
            <div class="card-body">
                <p>{{ __('categories.help_text') | default('Categories help organize your products and make them easier to find.') }}</p>
                <ul class="small">
                    <li>{{ __('categories.help_name') | default('Choose a clear, descriptive name') }}</li>
                    <li>{{ __('categories.help_icon') | default('Icons help users identify categories quickly') }}</li>
                    <li>{{ __('categories.help_order') | default('Categories are displayed in sort order') }}</li>
                    <li>{{ __('categories.help_active') | default('Inactive categories are hidden from users') }}</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
// Icon preview
document.getElementById('icon').addEventListener('input', function() {
    const preview = document.getElementById('icon-preview');
    preview.innerHTML = `<i class="${this.value || 'fas fa-box'}"></i>`;
});
</script>
{% endblock %}