<?php
require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

header('Content-Type: text/plain');

echo "=== TESTING TRANSLATION SYSTEM ===\n\n";

// Test basic translation
echo "Testing config.code translation:\n";
echo "Result: " . __('config.code') . "\n";
echo "Expected: Code\n\n";

echo "Testing config.payment_term_name_hint:\n";
echo "Result: " . __('config.payment_term_name_hint') . "\n";
echo "Expected: ex: Net 30, Paiement à réception\n\n";

echo "Testing config.payment_term_code_hint:\n";
echo "Result: " . __('config.payment_term_code_hint') . "\n";
echo "Expected: Identifiant unique (lettres, chiffres, tirets bas)\n\n";

// Check current language
echo "Current language: " . \App\Helpers\Language::getCurrentLanguage() . "\n\n";

// Check if translation file exists
$configFile = __DIR__ . '/../app/lang/fr/config.php';
echo "Config translation file exists: " . (file_exists($configFile) ? "YES" : "NO") . "\n";

// Load translations manually
\App\Helpers\Language::load('config', 'fr');
echo "\nManually loaded config translations:\n";
echo "config.code: " . \App\Helpers\Language::get('config.code') . "\n";

// Check raw translation array
$translations = include $configFile;
echo "\nRaw translation for 'code': " . ($translations['code'] ?? 'NOT FOUND') . "\n";