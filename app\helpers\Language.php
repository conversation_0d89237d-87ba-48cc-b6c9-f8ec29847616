<?php

namespace App\Helpers;

class Language
{
    private static $translations = [];
    private static $currentLanguage = 'fr';
    private static $fallbackLanguage = 'en';
    private static $loadedGroups = [];
    private static $missingTranslations = [];
    private static $cache = null;
    private static $cacheEnabled = true;
    private static $logMissing = true;
    private static $missingLogFile = null;
    
    /**
     * Initialize the language system with cache and logging
     */
    public static function initialize($options = [])
    {
        // Set cache options
        if (isset($options['cache_enabled'])) {
            self::$cacheEnabled = (bool) $options['cache_enabled'];
        }
        
        // Set logging options
        if (isset($options['log_missing'])) {
            self::$logMissing = (bool) $options['log_missing'];
        }
        
        // Set missing translations log file
        if (isset($options['missing_log_file'])) {
            self::$missingLogFile = $options['missing_log_file'];
        } else {
            self::$missingLogFile = __DIR__ . '/../../storage/logs/missing_translations.log';
        }
        
        // Initialize cache if enabled
        if (self::$cacheEnabled && class_exists('App\Services\CacheService')) {
            try {
                self::$cache = \Flight::cache();
            } catch (\Exception $e) {
                // Cache not available, continue without it
                self::$cacheEnabled = false;
            }
        }
    }
    
    /**
     * Set the current language
     */
    public static function setLanguage($language)
    {
        self::$currentLanguage = $language;
        self::$loadedGroups = []; // Clear loaded groups when language changes
        self::$translations = [];
    }
    
    /**
     * Get the current language
     */
    public static function getCurrentLanguage()
    {
        return self::$currentLanguage;
    }
    
    /**
     * Set the fallback language
     */
    public static function setFallbackLanguage($language)
    {
        self::$fallbackLanguage = $language;
    }
    
    /**
     * Load translations from a specific group
     */
    public static function load($group, $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        $cacheKey = 'lang.' . $language . '.' . $group;
        
        // Check if already loaded in memory
        if (in_array($cacheKey, self::$loadedGroups)) {
            return true;
        }
        
        // Try to load from cache first
        if (self::$cacheEnabled && self::$cache) {
            $cached = self::$cache->get($cacheKey);
            if ($cached !== false) {
                if (!isset(self::$translations[$language])) {
                    self::$translations[$language] = [];
                }
                self::$translations[$language][$group] = $cached;
                self::$loadedGroups[] = $cacheKey;
                return true;
            }
        }
        
        $file = __DIR__ . '/../lang/' . $language . '/' . $group . '.php';
        
        if (file_exists($file)) {
            $translations = include $file;
            
            if (is_array($translations)) {
                if (!isset(self::$translations[$language])) {
                    self::$translations[$language] = [];
                }
                
                self::$translations[$language][$group] = $translations;
                self::$loadedGroups[] = $cacheKey;
                
                // Store in cache
                if (self::$cacheEnabled && self::$cache) {
                    self::$cache->set($cacheKey, $translations, 3600); // Cache for 1 hour
                }
                
                return true;
            }
        }
        
        // Try to load fallback language
        if ($language !== self::$fallbackLanguage) {
            return self::load($group, self::$fallbackLanguage);
        }
        
        return false;
    }
    
    /**
     * Get a translation by key
     */
    public static function get($key, $params = [], $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        // Parse the key (e.g., "users.username" => group: users, item: username)
        $segments = explode('.', $key);
        
        if (count($segments) < 2) {
            // Log missing translation
            self::logMissingTranslation($key, $language);
            return self::formatMissingKey($key); // Invalid key format
        }
        
        $group = $segments[0];
        $item = implode('.', array_slice($segments, 1));
        
        // Load the group if not already loaded
        self::load($group, $language);
        
        // Get the translation
        $translation = null;
        $foundInLanguage = null;
        
        // First try to get it directly if the key contains dots (like month.1)
        if (isset(self::$translations[$language][$group][$item])) {
            $translation = self::$translations[$language][$group][$item];
            $foundInLanguage = $language;
        } else {
            // Try using the array traversal method
            $translationKey = $language . '.' . $group . '.' . $item;
            $translation = self::getFromArray($translationKey, self::$translations);
            if ($translation !== null && $translation !== $key) {
                $foundInLanguage = $language;
            }
        }
        
        // If not found, try fallback language
        if (($translation === null || $translation === $key) && $language !== self::$fallbackLanguage) {
            self::load($group, self::$fallbackLanguage);
            
            // Try direct access first
            if (isset(self::$translations[self::$fallbackLanguage][$group][$item])) {
                $translation = self::$translations[self::$fallbackLanguage][$group][$item];
                $foundInLanguage = self::$fallbackLanguage;
            } else {
                $fallbackKey = self::$fallbackLanguage . '.' . $group . '.' . $item;
                $fallbackTranslation = self::getFromArray($fallbackKey, self::$translations);
                if ($fallbackTranslation !== null && $fallbackTranslation !== $fallbackKey) {
                    $translation = $fallbackTranslation;
                    $foundInLanguage = self::$fallbackLanguage;
                }
            }
        }
        
        // If still not found, log it and return formatted key
        if ($translation === null || $translation === $key || is_array($translation)) {
            self::logMissingTranslation($key, $language);
            $translation = self::formatMissingKey($key);
        }
        
        // Replace parameters
        if (!empty($params)) {
            foreach ($params as $param => $value) {
                $translation = str_replace(':' . $param, $value, $translation);
            }
        }
        
        return $translation;
    }
    
    /**
     * Get value from array using dot notation
     * 
     * @param string $key The dot-notation key
     * @param array $array The array to search
     * @return mixed|null The value or null if not found
     */
    private static function getFromArray(string $key, array $array)
    {
        if (strpos($key, '.') === false) {
            return isset($array[$key]) ? $array[$key] : null;
        }
        
        foreach (explode('.', $key) as $segment) {
            if (!is_array($array) || !array_key_exists($segment, $array)) {
                return null;
            }
            $array = $array[$segment];
        }
        
        // Ensure we return a string, not an array
        if (is_array($array)) {
            // If it's an array, return the key as fallback
            return $key;
        }
        
        return $array;
    }
    
    /**
     * Check if a translation exists
     */
    public static function has($key, $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        $translation = self::get($key, [], $language);
        return $translation !== $key;
    }
    
    /**
     * Get all translations for a group
     */
    public static function getGroup($group, $language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        self::load($group, $language);
        
        return self::$translations[$language][$group] ?? [];
    }
    
    /**
     * Load all groups for the current language
     */
    public static function loadAll($language = null)
    {
        if ($language === null) {
            $language = self::$currentLanguage;
        }
        
        $langDir = __DIR__ . '/../lang/' . $language;
        
        if (is_dir($langDir)) {
            $files = glob($langDir . '/*.php');
            
            foreach ($files as $file) {
                $group = basename($file, '.php');
                self::load($group, $language);
            }
        }
    }
    
    /**
     * Get available languages
     */
    public static function getAvailableLanguages()
    {
        $languages = [];
        $langDir = __DIR__ . '/../lang';
        
        if (is_dir($langDir)) {
            $dirs = glob($langDir . '/*', GLOB_ONLYDIR);
            
            foreach ($dirs as $dir) {
                $languages[] = basename($dir);
            }
        }
        
        return $languages;
    }
    
    /**
     * Log missing translation
     */
    private static function logMissingTranslation($key, $language)
    {
        if (!self::$logMissing) {
            return;
        }
        
        // Track in memory
        if (!isset(self::$missingTranslations[$language])) {
            self::$missingTranslations[$language] = [];
        }
        
        if (!in_array($key, self::$missingTranslations[$language])) {
            self::$missingTranslations[$language][] = $key;
            
            // Log to file
            if (self::$missingLogFile) {
                $logDir = dirname(self::$missingLogFile);
                if (!is_dir($logDir)) {
                    mkdir($logDir, 0777, true);
                }
                
                $logEntry = sprintf(
                    "[%s] Missing translation - Language: %s, Key: %s, URL: %s\n",
                    date('Y-m-d H:i:s'),
                    $language,
                    $key,
                    $_SERVER['REQUEST_URI'] ?? 'CLI'
                );
                
                error_log($logEntry, 3, self::$missingLogFile);
            }
        }
    }
    
    /**
     * Format missing translation key for display
     */
    private static function formatMissingKey($key)
    {
        // Remove the group prefix if present
        $segments = explode('.', $key);
        $displayKey = end($segments);
        
        // Convert snake_case or kebab-case to Title Case
        $formatted = str_replace(['_', '-'], ' ', $displayKey);
        $formatted = ucwords($formatted);
        
        // Add brackets to indicate it's a missing translation
        return "[{$formatted}]";
    }
    
    /**
     * Get all missing translations
     */
    public static function getMissingTranslations($language = null)
    {
        if ($language === null) {
            return self::$missingTranslations;
        }
        
        return self::$missingTranslations[$language] ?? [];
    }
    
    /**
     * Clear translation cache
     */
    public static function clearCache()
    {
        self::$translations = [];
        self::$loadedGroups = [];
        
        // Clear cache storage if available
        if (self::$cacheEnabled && self::$cache) {
            // Clear all language cache entries
            foreach (self::getAvailableLanguages() as $language) {
                $pattern = 'lang.' . $language . '.*';
                // Note: This assumes the cache service supports pattern-based deletion
                // If not, you may need to track cache keys separately
                try {
                    self::$cache->deletePattern($pattern);
                } catch (\Exception $e) {
                    // Cache clear failed, continue
                }
            }
        }
    }
    
    /**
     * Export missing translations to array
     */
    public static function exportMissingTranslations()
    {
        $export = [];
        
        foreach (self::$missingTranslations as $language => $keys) {
            $export[$language] = [];
            foreach ($keys as $key) {
                $segments = explode('.', $key);
                if (count($segments) >= 2) {
                    $group = $segments[0];
                    $item = implode('.', array_slice($segments, 1));
                    
                    if (!isset($export[$language][$group])) {
                        $export[$language][$group] = [];
                    }
                    
                    $export[$language][$group][$item] = self::formatMissingKey($key);
                }
            }
        }
        
        return $export;
    }
}