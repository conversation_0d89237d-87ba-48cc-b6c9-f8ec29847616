<?php
/**
 * Check Invoice Details
 */

require_once __DIR__ . '/../vendor/autoload.php';
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();
$invoiceNumber = $_GET['number'] ?? 'FAC-2025-00064';

// Get invoice
$stmt = $db->prepare("SELECT * FROM invoices WHERE invoice_number = ?");
$stmt->execute([$invoiceNumber]);
$invoice = $stmt->fetch(PDO::FETCH_ASSOC);

// Get invoice items
$items = [];
if ($invoice) {
    $stmt = $db->prepare("SELECT * FROM invoice_items WHERE invoice_id = ?");
    $stmt->execute([$invoice['id']]);
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Check Invoice <?php echo htmlspecialchars($invoiceNumber); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container mt-4">
    <h1>Invoice Details: <?php echo htmlspecialchars($invoiceNumber); ?></h1>
    
    <?php if ($invoice): ?>
        <div class="card mt-3">
            <div class="card-header">
                <h3>Invoice Information</h3>
            </div>
            <div class="card-body">
                <table class="table">
                    <tr>
                        <th>ID:</th>
                        <td><?php echo $invoice['id']; ?></td>
                    </tr>
                    <tr>
                        <th>Status:</th>
                        <td><?php echo $invoice['status']; ?></td>
                    </tr>
                    <tr>
                        <th>Client ID:</th>
                        <td><?php echo $invoice['client_id']; ?></td>
                    </tr>
                    <tr>
                        <th>Subtotal:</th>
                        <td><?php echo $invoice['subtotal']; ?></td>
                    </tr>
                    <tr>
                        <th>VAT Amount:</th>
                        <td><?php echo $invoice['vat_amount']; ?></td>
                    </tr>
                    <tr>
                        <th>Total:</th>
                        <td><?php echo $invoice['total']; ?></td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h3>Invoice Items (<?php echo count($items); ?> items)</h3>
            </div>
            <div class="card-body">
                <?php if (count($items) > 0): ?>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Description</th>
                                <th>Quantity</th>
                                <th>Unit Price</th>
                                <th>VAT Rate ID</th>
                                <th>Line Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($items as $item): ?>
                                <tr>
                                    <td><?php echo $item['id']; ?></td>
                                    <td><?php echo htmlspecialchars($item['description']); ?></td>
                                    <td><?php echo $item['quantity']; ?></td>
                                    <td><?php echo $item['unit_price']; ?></td>
                                    <td><?php echo $item['vat_rate_id']; ?></td>
                                    <td><?php echo $item['line_total'] ?? 'N/A'; ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <strong>No items found!</strong> This invoice has no line items.
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="mt-3">
            <a href="/fit/public/invoices/<?php echo $invoice['id']; ?>/edit" class="btn btn-primary">
                Edit Invoice
            </a>
            <a href="/fit/public/invoices/<?php echo $invoice['id']; ?>" class="btn btn-secondary">
                View Invoice
            </a>
        </div>
        
    <?php else: ?>
        <div class="alert alert-danger">
            Invoice not found!
        </div>
    <?php endif; ?>
    
    <div class="mt-5">
        <h3>Quick Add Items</h3>
        <p>To add items to this invoice, you can edit it or use this quick form:</p>
        
        <?php if ($invoice): ?>
        <form method="POST" action="/fit/public/invoices/<?php echo $invoice['id']; ?>/add-item">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token'] ?? ''; ?>">
            <input type="hidden" name="_method" value="PUT">
            
            <div class="row">
                <div class="col-md-4">
                    <input type="text" name="description" class="form-control" placeholder="Description" required>
                </div>
                <div class="col-md-2">
                    <input type="number" name="quantity" class="form-control" placeholder="Qty" value="1" required>
                </div>
                <div class="col-md-2">
                    <input type="number" name="unit_price" class="form-control" placeholder="Price" step="0.01" required>
                </div>
                <div class="col-md-2">
                    <select name="vat_rate_id" class="form-control" required>
                        <option value="1">0%</option>
                        <option value="2">0% INT</option>
                        <option value="3" selected>17%</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-success">Add Item</button>
                </div>
            </div>
        </form>
        <?php endif; ?>
    </div>
</div>
</body>
</html>