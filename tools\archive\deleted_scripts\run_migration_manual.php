<?php
// Manual migration runner - minimal dependencies

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database configuration
$host = 'localhost';
$dbname = 'healthcenter_billing';
$username = 'root';
$password = '';

try {
    // Create PDO connection
    $db = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Running Migration: Add deleted invoice numbers pool</h2>";
    
    // Check if table already exists
    $stmt = $db->query("SHOW TABLES LIKE 'deleted_invoice_numbers'");
    if ($stmt->fetch()) {
        echo "! Table deleted_invoice_numbers already exists<br>";
    } else {
        // Create deleted_invoice_numbers table
        $sql = "CREATE TABLE deleted_invoice_numbers (
            id INT AUTO_INCREMENT PRIMARY KEY,
            document_type_id INT NOT NULL,
            invoice_type_id INT NULL,
            invoice_number VARCHAR(100) NOT NULL,
            year INT NULL,
            month INT NULL,
            sequence_number INT NOT NULL,
            deleted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            deleted_by INT NULL,
            reused_at TIMESTAMP NULL,
            reused_by INT NULL,
            UNIQUE KEY unique_number (invoice_number),
            INDEX idx_available (document_type_id, year, month, reused_at),
            INDEX idx_sequence (document_type_id, invoice_type_id, year, month, sequence_number),
            FOREIGN KEY (document_type_id) REFERENCES document_types(id) ON DELETE CASCADE,
            FOREIGN KEY (invoice_type_id) REFERENCES config_invoice_types(id) ON DELETE SET NULL,
            FOREIGN KEY (deleted_by) REFERENCES users(id) ON DELETE SET NULL,
            FOREIGN KEY (reused_by) REFERENCES users(id) ON DELETE SET NULL
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->exec($sql);
        echo "✓ Created deleted_invoice_numbers table<br>";
    }
    
    // Add configuration option
    try {
        $stmt = $db->prepare("
            INSERT INTO config (config_key, config_value, created_at, updated_at) 
            VALUES ('reuse_deleted_invoice_numbers', 'true', NOW(), NOW())
            ON DUPLICATE KEY UPDATE config_value = 'true', updated_at = NOW()
        ");
        $stmt->execute();
        echo "✓ Added/Updated configuration: reuse_deleted_invoice_numbers = true<br>";
    } catch (Exception $e) {
        echo "! Error adding config: " . $e->getMessage() . "<br>";
    }
    
    // Check if index exists on invoices table
    try {
        $stmt = $db->query("SHOW INDEX FROM invoices WHERE Key_name = 'idx_invoice_number'");
        if ($stmt->fetch()) {
            echo "! Index idx_invoice_number already exists<br>";
        } else {
            $db->exec("ALTER TABLE invoices ADD INDEX idx_invoice_number (invoice_number)");
            echo "✓ Added index on invoices.invoice_number<br>";
        }
    } catch (Exception $e) {
        echo "! Could not add index: " . $e->getMessage() . "<br>";
    }
    
    echo "<br><strong>Migration completed!</strong><br>";
    echo "<br>Now you can run the fix script:<br>";
    echo "<a href='fix_invoice_number_186.php' style='font-size: 16px;'>Click here to fix invoice number FAC-2025-0190 → FAC-LOY-2025-0186</a>";
    
} catch (PDOException $e) {
    echo "<strong>Database Error:</strong> " . $e->getMessage();
    echo "<br><br>Make sure:";
    echo "<ul>";
    echo "<li>MySQL/MariaDB is running</li>";
    echo "<li>Database 'healthcenter_billing' exists</li>";
    echo "<li>User 'root' has access</li>";
    echo "</ul>";
}
?>