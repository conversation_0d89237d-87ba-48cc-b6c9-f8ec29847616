# Repository Cleanup Guide

## Issue: Too Many Development Files in Source Control

The repository currently contains many development, debug, and temporary files that should not be tracked in version control.

## Solution: Updated .gitignore and Cleanup

### 1. Updated .gitignore File ✅
- Created comprehensive .gitignore for Flight PHP project
- Excludes development files, debug scripts, temporary files
- Excludes test files, backup files, and build artifacts

### 2. Files That Should Be Removed from Tracking

#### Development PHP Files:
- All `test-*.php` files in public directory
- All `debug-*.php` files
- All `fix-*.php` files  
- All `check-*.php` files
- All `apply-*.php` files
- All `run-*.php` files
- All `setup-*.php` files
- All `add-*.php` files

#### Development JavaScript Files:
- All `*-debug.js` files
- All `*-fix*.js` files
- All `client-form-*.js` files
- All `invoice-*-fix.js` files

#### Development Documentation:
- All `*-fixes-report.md` files
- All `*CLEANUP*.md` files
- All `*ERRORS*.md` files
- All `*INTEGRATION*.md` files
- All `*TRANSLATION*.md` files
- All `*USERNAME*.md` files
- All `*USER_*.md` files
- All `*SESSION_SAVE*.md` files

#### Development Database Files:
- All development migration files (050-070 series)
- All `check_*.sql` files
- All `optimize_*.sql` files

#### Development Models/Controllers:
- `InvoiceOptimized.php`
- `SalesInvoice.php`
- `SalesInvoiceLine.php`
- `SalesPayment.php`
- `OptimizedController.php`
- `PerformanceController.php`
- `PosController.php`
- `*_optimized.php` files

#### Development Views:
- All `*-backup.twig` files
- All `*-improved.twig` files
- All `*-optimized.twig` files
- `dashboard-fresh.twig`

#### Development Test Files:
- `CategoryCrudTest.php`
- `InvoiceCalculationTest.php`
- `MoneyHelperTest.php`
- `RetrocessionCalculationTest.php`
- All `add-*.php` test files
- All `check-*.php` test files
- All `setup-*.php` test files

### 3. Manual Cleanup Commands

To remove these files from Git tracking (but keep them locally):

```bash
# Remove development PHP files
git rm --cached public/test-*.php
git rm --cached public/debug-*.php
git rm --cached public/fix-*.php
git rm --cached public/check-*.php
git rm --cached public/apply-*.php
git rm --cached public/run-*.php
git rm --cached public/setup-*.php
git rm --cached public/add-*.php

# Remove development JS files
git rm --cached public/js/*-debug.js
git rm --cached public/js/*-fix*.js
git rm --cached public/js/client-form-*.js
git rm --cached public/js/invoice-*-fix.js
git rm --cached public/js/fix-*.js

# Remove development documentation
git rm --cached *-fixes-report.md
git rm --cached *CLEANUP*.md
git rm --cached *ERRORS*.md
git rm --cached *INTEGRATION*.md
git rm --cached *TRANSLATION*.md
git rm --cached *USERNAME*.md
git rm --cached *USER_*.md
git rm --cached *SESSION_SAVE*.md

# Remove development database files
git rm --cached database/migrations/050_*.sql
git rm --cached database/migrations/051_*.sql
git rm --cached database/migrations/052_*.sql
git rm --cached database/migrations/053_*.sql
git rm --cached database/migrations/054_*.sql
git rm --cached database/migrations/055_*.sql
git rm --cached database/migrations/056_*.sql
git rm --cached database/migrations/057_*.sql
git rm --cached database/migrations/060_*.sql
git rm --cached database/migrations/061_*.sql
git rm --cached database/migrations/062_*.sql
git rm --cached database/migrations/063_*.sql
git rm --cached database/migrations/066_*.sql
git rm --cached database/migrations/067_*.sql
git rm --cached database/migrations/068_*.sql
git rm --cached database/migrations/069_*.sql
git rm --cached database/migrations/070_*.sql
git rm --cached database/check_*.sql
git rm --cached database/optimize_*.sql

# Remove development models/controllers
git rm --cached app/Models/InvoiceOptimized.php
git rm --cached app/Models/SalesInvoice.php
git rm --cached app/Models/SalesInvoiceLine.php
git rm --cached app/Models/SalesPayment.php
git rm --cached app/Core/OptimizedController.php
git rm --cached app/controllers/PerformanceController.php
git rm --cached app/controllers/PosController.php

# Remove development test files
git rm --cached tests/CategoryCrudTest.php
git rm --cached tests/InvoiceCalculationTest.php
git rm --cached tests/MoneyHelperTest.php
git rm --cached tests/RetrocessionCalculationTest.php
git rm --cached tests/add-*.php
git rm --cached tests/check-*.php
git rm --cached tests/setup-*.php
```

### 4. After Cleanup

1. Commit the updated .gitignore
2. Commit the removal of tracked development files
3. Push changes to GitHub
4. Repository will be much cleaner with only production-ready files

### 5. Benefits of Cleanup

- **Cleaner Repository** - Only production-ready files tracked
- **Better Performance** - Faster clones and pulls
- **Professional Appearance** - Clean, organized codebase
- **Easier Navigation** - Less clutter in file listings
- **Better Security** - No debug or development files exposed

### 6. Files to Keep

- Core application files (models, controllers, views)
- Production configuration files
- Documentation (README, deployment guides)
- Test suite (Phase 3 and Phase 4 tests)
- Database migrations (production ones)
- Assets and public files (production)

This cleanup will result in a much more professional and maintainable repository.
