@echo off
REM Script to download and start Mailhog on Windows

echo Starting Mailhog for email testing...

REM Check if mailhog.exe exists
if exist "mailhog.exe" (
    echo Mailhog found. Starting...
    start mailhog.exe
    echo.
    echo Mailhog started!
    echo SMTP Server: localhost:1025
    echo Web Interface: http://localhost:8025
    echo.
    echo Press Ctrl+C in the Mailhog window to stop it.
) else (
    echo Mailhog not found. Please download it first:
    echo.
    echo 1. Go to: https://github.com/mailhog/MailHog/releases
    echo 2. Download MailHog_windows_amd64.exe
    echo 3. Rename it to mailhog.exe
    echo 4. Place it in this directory
    echo 5. Run this script again
    echo.
    echo Opening download page...
    start https://github.com/mailhog/MailHog/releases
)

pause