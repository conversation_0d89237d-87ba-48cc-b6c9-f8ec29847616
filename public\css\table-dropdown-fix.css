/**
 * CSS-only Table Dropdown Fix
 * Ensures dropdowns in tables are properly displayed
 */

/* Prevent table from clipping dropdowns */
.table-responsive {
    position: relative;
    min-height: 300px; /* Ensure minimum height for dropdowns */
    overflow-x: auto;
    overflow-y: visible !important; /* Allow vertical overflow for dropdowns */
}

/* Remove all overflow restrictions when dropdown is open */
.table-responsive:has(.dropdown-menu.show) {
    overflow: visible !important;
    z-index: 1050;
}

/* Ensure dropdown stays in correct position */
.table .dropdown {
    position: relative;
}

/* Basic dropdown menu positioning */
.table .dropdown-menu {
    position: absolute !important;
    z-index: 1055 !important;
    top: 100%;
    right: 0;
    left: auto;
    margin-top: 0.125rem;
    min-width: 160px;
    max-height: none !important;
    overflow: visible !important;
}

/* Ensure dropdown menu is visible */
.dropdown-menu.show {
    display: block !important;
}

/* Fix for edge positioning */
.table td:last-child .dropdown-menu,
.table th:last-child .dropdown-menu {
    right: 0 !important;
    left: auto !important;
}

/* Fix for mobile devices */
@media (max-width: 768px) {
    .table .dropdown-menu {
        position: fixed !important;
        max-width: 90vw;
        max-height: 80vh;
        overflow-y: auto;
    }
    
    /* Adjust position on mobile */
    .table .dropdown.show .dropdown-menu {
        right: 10px !important;
        left: auto !important;
    }
}

/* Ensure proper layering */
.dropdown-menu {
    background-color: var(--bs-dropdown-bg, #fff);
    border: var(--bs-dropdown-border-width, 1px) solid var(--bs-dropdown-border-color, rgba(0,0,0,.15));
    border-radius: var(--bs-dropdown-border-radius, 0.375rem);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Prevent transform animations that cause positioning issues */
.table .dropdown-menu.show {
    transform: none !important;
    transition: none !important;
}

/* Ensure parent containers don't clip dropdowns */
.card {
    overflow: visible !important;
}

.card-body {
    overflow: visible !important;
}

/* Fix for Bootstrap's dropdown item display */
.dropdown-item {
    display: block !important;
    width: 100%;
    padding: 0.25rem 1rem;
    clear: both;
    white-space: nowrap;
}

/* Ensure all dropdown menu items are visible */
.dropdown-menu {
    display: none;
}

.dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.dropdown-menu.show > * {
    display: block !important;
    visibility: visible !important;
}