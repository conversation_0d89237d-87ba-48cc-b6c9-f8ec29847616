<?php

require_once __DIR__ . '/../app/config/bootstrap.php';

use App\Core\Database;

try {
    $db = Database::getInstance()->getConnection();
    
    echo "Creating payment reminder tables...\n";
    
    // Create reminder_logs table
    $db->exec("
        CREATE TABLE IF NOT EXISTS reminder_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            invoice_id INT NOT NULL,
            reminder_number INT NOT NULL,
            sent_date DATETIME NOT NULL,
            response_status VARCHAR(50) DEFAULT NULL,
            response_date DATETIME DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
            INDEX idx_invoice_reminder (invoice_id, reminder_number),
            INDEX idx_sent_date (sent_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    echo "✓ Created reminder_logs table\n";
    
    // Create reminder_settings table
    $db->exec("
        CREATE TABLE IF NOT EXISTS reminder_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            reminder_level INT NOT NULL,
            days_overdue INT NOT NULL,
            template_id INT DEFAULT NULL,
            enabled TINYINT(1) DEFAULT 1,
            send_copy_to_admin TINYINT(1) DEFAULT 0,
            admin_email VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (template_id) REFERENCES email_templates(id) ON DELETE SET NULL,
            UNIQUE KEY unique_reminder_level (reminder_level)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    echo "✓ Created reminder_settings table\n";
    
    // Create reminder_exclusions table
    $db->exec("
        CREATE TABLE IF NOT EXISTS reminder_exclusions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            client_id INT NOT NULL,
            reason TEXT DEFAULT NULL,
            start_date DATE NOT NULL,
            end_date DATE DEFAULT NULL,
            active TINYINT(1) DEFAULT 1,
            created_by INT DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
            FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
            UNIQUE KEY unique_active_exclusion (client_id, active),
            INDEX idx_active_exclusions (active, end_date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ");
    echo "✓ Created reminder_exclusions table\n";
    
    // Insert default reminder settings
    $stmt = $db->prepare("
        INSERT IGNORE INTO reminder_settings (reminder_level, days_overdue, enabled)
        VALUES 
            (1, 7, 1),
            (2, 14, 1),
            (3, 30, 1)
    ");
    $stmt->execute();
    echo "✓ Inserted default reminder settings\n";
    
    // Add reminder tracking columns to invoices if they don't exist
    $columns = $db->query("SHOW COLUMNS FROM invoices")->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('last_reminder_date', $columns)) {
        $db->exec("ALTER TABLE invoices ADD COLUMN last_reminder_date DATETIME DEFAULT NULL");
        echo "✓ Added last_reminder_date column to invoices\n";
    }
    
    if (!in_array('reminder_count', $columns)) {
        $db->exec("ALTER TABLE invoices ADD COLUMN reminder_count INT DEFAULT 0");
        echo "✓ Added reminder_count column to invoices\n";
    }
    
    echo "\n✅ Payment reminder tables created successfully!\n";
    
} catch (Exception $e) {
    echo "\n❌ Error creating payment reminder tables: " . $e->getMessage() . "\n";
    exit(1);
}