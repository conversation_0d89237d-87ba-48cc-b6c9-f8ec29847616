{% extends "base-modern.twig" %}

{% block title %}{{ __('config.number_formats') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('config.number_formats') }}</h1>
        <div class="d-flex gap-2">
            <a href="{{ base_url }}/config" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>{{ __('common.back') }}
            </a>
            <button type="submit" form="numberFormatsForm" class="btn btn-primary">
                <i class="bi bi-check-circle me-2"></i>{{ __('common.save') }}
            </button>
        </div>
    </div>

    <!-- Description -->
    <div class="alert alert-info alert-dismissible fade show" role="alert">
        <i class="bi bi-info-circle me-2"></i>
        {{ __('config.number_formats_description') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>

    <form id="numberFormatsForm" method="POST" action="{{ base_url }}/config/number-formats" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <div class="row">
            <div class="col-lg-8">
                <!-- Client Number Format -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0"><i class="bi bi-building me-2"></i>{{ __('config.client_number_format') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="client_prefix" class="form-label">{{ __('config.prefix') }}</label>
                                <input type="text" class="form-control" id="client_prefix" name="client_prefix" 
                                       value="{{ formats.client_prefix|default('CLT-') }}" placeholder="CLT-">
                                <small class="text-muted">{{ __('config.prefix_hint') }}</small>
                            </div>
                            
                            <div class="col-md-6">
                                <label for="client_digits" class="form-label">{{ __('config.number_digits') }}</label>
                                <input type="number" class="form-control" id="client_digits" name="client_digits" 
                                       value="{{ formats.client_digits|default(6) }}" min="3" max="10">
                                <small class="text-muted">{{ __('config.digits_hint') }}</small>
                            </div>
                            
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="client_auto" name="client_auto" 
                                           value="1" {{ formats.client_auto == '1' ? 'checked' : '' }}>
                                    <label class="form-check-label" for="client_auto">
                                        {{ __('config.auto_generate_client_number') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>{{ __('config.example') }}:</strong> 
                                    <span id="clientExample">CLT-000001</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Patient Number Format -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0"><i class="bi bi-person me-2"></i>{{ __('config.patient_number_format') }}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="patient_prefix" class="form-label">{{ __('config.prefix') }}</label>
                                <input type="text" class="form-control" id="patient_prefix" name="patient_prefix" 
                                       value="{{ formats.patient_prefix|default('PAT-') }}" placeholder="PAT-">
                            </div>
                            
                            <div class="col-md-6">
                                <label for="patient_digits" class="form-label">{{ __('config.number_digits') }}</label>
                                <input type="number" class="form-control" id="patient_digits" name="patient_digits" 
                                       value="{{ formats.patient_digits|default(6) }}" min="3" max="10">
                            </div>
                            
                            <div class="col-12">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="patient_auto" name="patient_auto" 
                                           value="1" {{ formats.patient_auto ? 'checked' : '' }}>
                                    <label class="form-check-label" for="patient_auto">
                                        {{ __('config.auto_generate_patient_number') }}
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-12">
                                <div class="alert alert-info mb-0">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>{{ __('config.example') }}:</strong> 
                                    <span id="patientExample">PAT-000001</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="col-lg-4">
                <!-- Current Numbers -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-counter me-2"></i>{{ __('config.current_numbers') }}</h6>
                    </div>
                    <div class="card-body">
                        <dl class="row mb-0">
                            <dt class="col-7">{{ __('config.last_client_number') }}:</dt>
                            <dd class="col-5"><code>{{ current_numbers.last_client|default('N/A') }}</code></dd>
                            
                            <dt class="col-7">{{ __('config.last_patient_number') }}:</dt>
                            <dd class="col-5"><code>{{ current_numbers.last_patient|default('N/A') }}</code></dd>
                        </dl>
                    </div>
                </div>

                <!-- Date Format Variables -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0"><i class="bi bi-calendar-date me-2"></i>{{ __('config.date_variables') }}</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">{{ __('config.date_variables_description') }}</p>
                        <ul class="list-unstyled mb-0">
                            <li><code>{YYYY}</code> - {{ __('config.year_4_digits') }}</li>
                            <li><code>{YY}</code> - {{ __('config.year_2_digits') }}</li>
                            <li><code>{MM}</code> - {{ __('config.month_2_digits') }}</li>
                            <li><code>{M}</code> - {{ __('config.month_1_digit') }}</li>
                            <li><code>{DD}</code> - {{ __('config.day_2_digits') }}</li>
                            <li><code>{D}</code> - {{ __('config.day_1_digit') }}</li>
                        </ul>
                    </div>
                </div>

                <!-- Reset Counters -->
                <div class="card shadow-sm">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0"><i class="bi bi-arrow-counterclockwise me-2"></i>{{ __('config.reset_counters') }}</h6>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-3">{{ __('config.reset_counters_warning') }}</p>
                        <button type="button" class="btn btn-danger btn-sm w-100" onclick="resetCounters()">
                            <i class="bi bi-exclamation-triangle me-2"></i>{{ __('config.reset_all_counters') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>

<script>
// Update examples as user types
function updateExamples() {
    // Client example
    const cltPrefix = document.getElementById('client_prefix').value || '';
    const cltDigits = parseInt(document.getElementById('client_digits').value) || 6;
    const cltNumber = '1'.padStart(cltDigits, '0');
    document.getElementById('clientExample').textContent = cltPrefix + cltNumber;
    
    // Patient example
    const patPrefix = document.getElementById('patient_prefix').value || '';
    const patDigits = parseInt(document.getElementById('patient_digits').value) || 6;
    const patNumber = '1'.padStart(patDigits, '0');
    document.getElementById('patientExample').textContent = patPrefix + patNumber;
}

// Add event listeners
document.querySelectorAll('input[type="text"], input[type="number"]').forEach(input => {
    input.addEventListener('input', updateExamples);
});

// Reset counters
function resetCounters() {
    if (confirm('{{ __("config.reset_counters_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/config/number-formats/reset';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Form validation is handled in the scripts block below

// Initial example update
updateExamples();
</script>
{% endblock %}

{% block scripts %}
<script>
// Number formats form handler
document.addEventListener('DOMContentLoaded', function() {
    const numberFormatsForm = document.getElementById('numberFormatsForm');
    if (!numberFormatsForm) {
        console.error('Number formats form not found!');
        return;
    }
    
    // Override form submission
    numberFormatsForm.onsubmit = null;
    numberFormatsForm.addEventListener('submit', function(e) {
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        
        console.log('Form submission intercepted');
        
        // Validate form
        if (!this.checkValidity()) {
            this.classList.add('was-validated');
            return false;
        }
        
        const formData = new FormData(this);
        formData.append('_method', 'PUT'); // Add PUT method
        
        // Get all submit buttons
        const submitBtns = document.querySelectorAll('button[type="submit"][form="numberFormatsForm"], #numberFormatsForm button[type="submit"]');
        const originalTexts = [];
        
        // Disable all buttons and show loading
        submitBtns.forEach((btn, index) => {
            originalTexts[index] = btn.innerHTML;
            btn.disabled = true;
            btn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>{{ __("common.saving") }}...';
        });
        
        // Use XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('POST', this.action, true);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        
        xhr.onload = function() {
            try {
                const data = JSON.parse(xhr.responseText);
                console.log('Response received:', data);
                
                if (data.success) {
                    // Show success with toastr
                    if (typeof toastr !== 'undefined') {
                        toastr.success(data.message || '{{ __("config.number_format_saved_successfully") }}');
                    }
                    
                    // Change buttons to success state temporarily
                    submitBtns.forEach((btn) => {
                        btn.innerHTML = '<i class="bi bi-check-circle me-2"></i>{{ __("common.saved") }}';
                        btn.classList.remove('btn-primary');
                        btn.classList.add('btn-success');
                    });
                    
                    // Reset buttons after 2 seconds
                    setTimeout(() => {
                        submitBtns.forEach((btn, index) => {
                            btn.innerHTML = originalTexts[index];
                            btn.classList.remove('btn-success');
                            btn.classList.add('btn-primary');
                            btn.disabled = false;
                        });
                    }, 2000);
                } else {
                    // Show error with toastr
                    if (typeof toastr !== 'undefined') {
                        toastr.error(data.message || '{{ __("common.error_occurred") }}');
                    }
                    submitBtns.forEach((btn, index) => {
                        btn.disabled = false;
                        btn.innerHTML = originalTexts[index];
                    });
                }
            } catch (e) {
                console.error('Parse error:', e);
                if (typeof toastr !== 'undefined') {
                    toastr.error('{{ __("common.server_error") }}');
                }
                submitBtns.forEach((btn, index) => {
                    btn.disabled = false;
                    btn.innerHTML = originalTexts[index];
                });
            }
        };
        
        xhr.onerror = function() {
            console.error('XHR Error');
            if (typeof toastr !== 'undefined') {
                toastr.error('{{ __("common.connection_error") }}');
            }
            submitBtns.forEach((btn, index) => {
                btn.disabled = false;
                btn.innerHTML = originalTexts[index];
            });
        };
        
        // Send the request
        xhr.send(formData);
        
        return false;
    }, true); // Use capture phase
});
</script>
{% endblock %}