<?php
/**
 * Route Monitoring Dashboard - Quick Status Check
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Route Monitor - Fit360</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .actions {
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .routes {
            margin-top: 30px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .method {
            font-weight: bold;
            color: #007bff;
        }
        .path {
            font-family: monospace;
            font-size: 13px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Route Monitoring Dashboard</h1>
        
        <div class="info">
            <p><strong>Welcome to the Route Monitoring Dashboard!</strong></p>
            <p>This dashboard helps you monitor the health and status of critical routes in the Fit360 AdminDesk application.</p>
        </div>
        
        <div class="actions">
            <a href="test-routes.php" class="btn">Run Route Tests</a>
            <a href="../tests/logs/" class="btn">View Test Logs</a>
        </div>
        
        <div class="routes">
            <h2>Critical Routes to Monitor</h2>
            <table>
                <thead>
                    <tr>
                        <th>Method</th>
                        <th>Path</th>
                        <th>Description</th>
                        <th>Category</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/</td>
                        <td>Homepage/Dashboard</td>
                        <td>Dashboard</td>
                    </tr>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/config/email-templates</td>
                        <td>Email Templates List</td>
                        <td>Configuration</td>
                    </tr>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/config/email-templates/1/edit</td>
                        <td>Edit Email Template</td>
                        <td>Configuration</td>
                    </tr>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/invoices</td>
                        <td>Invoices List</td>
                        <td>Invoices</td>
                    </tr>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/clients</td>
                        <td>Clients List</td>
                        <td>Clients</td>
                    </tr>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/api/dashboard/stats</td>
                        <td>Dashboard Stats API</td>
                        <td>API</td>
                    </tr>
                    <tr>
                        <td class="method">GET</td>
                        <td class="path">/health</td>
                        <td>Health Check</td>
                        <td>API</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div style="margin-top: 40px; text-align: center; color: #666;">
            <p>Fit360 AdminDesk Route Monitor v1.0</p>
        </div>
    </div>
</body>
</html>
