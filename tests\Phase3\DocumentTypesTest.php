<?php

namespace Tests\Phase3;

use PHPUnit\Framework\TestCase;
use App\Models\DocumentType;
use App\Models\Invoice;
use Flight;

class DocumentTypesTest extends TestCase
{
    protected static $db;
    protected $testDocumentTypes = [];
    protected $testSequences = [];
    
    public static function setUpBeforeClass(): void
    {
        // Initialize database connection
        require_once __DIR__ . '/../bootstrap-test.php';
        self::$db = Flight::db();
        
        // Clean up test data
        self::$db->exec("DELETE FROM document_sequences WHERE document_type_id IN (SELECT id FROM document_types WHERE code LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM document_types WHERE code LIKE 'TEST-%'");
    }
    
    public function testCreateDocumentType()
    {
        $docType = DocumentType::create([
            'code' => 'TEST-INV',
            'name' => json_encode(['en' => 'Test Invoice', 'fr' => 'Facture Test']),
            'prefix' => 'TINV',
            'counter_type' => 'yearly',
            'is_negative' => 0,
            'requires_reference' => 0,
            'icon' => 'bi-file-text',
            'color' => '#28a745',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->assertNotNull($docType);
        $this->assertNotNull($docType->id);
        $this->assertEquals('TEST-INV', $docType->code);
        $this->assertEquals('TINV', $docType->prefix);
        $this->assertEquals('yearly', $docType->counter_type);
        
        $this->testDocumentTypes[] = $docType->id;
        
        return $docType;
    }
    
    public function testGenerateDocumentNumber()
    {
        // Create test document type
        $docType = DocumentType::create([
            'code' => 'TEST-NUM-GEN',
            'name' => json_encode(['en' => 'Test Number Gen', 'fr' => 'Test Génération Numéro']),
            'prefix' => 'TNUM',
            'counter_type' => 'yearly',
            'is_negative' => 0,
            'requires_reference' => 0,
            'icon' => 'bi-file-text',
            'color' => '#28a745',
            'is_active' => 1
        ]);
        
        $this->testDocumentTypes[] = $docType->id;
        
        $invoice = new Invoice();
        $year = date('Y');
        
        // Generate first number
        $number1 = $invoice->generateDocumentNumber($docType->id);
        $expectedNumber1 = "TNUM-{$year}-00001";
        $this->assertEquals($expectedNumber1, $number1);
        
        // Generate second number - should increment
        $number2 = $invoice->generateDocumentNumber($docType->id);
        $expectedNumber2 = "TNUM-{$year}-00002";
        $this->assertEquals($expectedNumber2, $number2);
        
        // Verify sequence was created/updated
        $stmt = self::$db->prepare("SELECT last_number FROM document_sequences WHERE document_type_id = ? AND year = ?");
        $stmt->execute([$docType->id, $year]);
        $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        $this->assertNotNull($sequence);
        $this->assertEquals(2, $sequence['last_number']);
    }
    
    public function testMonthlyCounterType()
    {
        $monthlyDocType = DocumentType::create([
            'code' => 'TEST-MONTHLY',
            'name' => json_encode(['en' => 'Monthly Invoice']),
            'prefix' => 'MON',
            'counter_type' => 'monthly',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testDocumentTypes[] = $monthlyDocType->id;
        
        $invoice = new Invoice();
        $year = date('Y');
        $month = date('m');
        
        // Generate number
        $number = $invoice->generateDocumentNumber($monthlyDocType->id);
        $expectedNumber = "MON-{$year}-{$month}-00001";
        $this->assertEquals($expectedNumber, $number);
        
        // Verify sequence includes month
        $stmt = self::$db->prepare("SELECT last_number FROM document_sequences WHERE document_type_id = ? AND year = ? AND month = ?");
        $stmt->execute([$monthlyDocType->id, $year, $month]);
        $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        $this->assertNotNull($sequence);
        $this->assertEquals(1, $sequence['last_number']);
    }
    
    public function testGlobalCounterType()
    {
        $globalDocType = DocumentType::create([
            'code' => 'TEST-GLOBAL',
            'name' => json_encode(['en' => 'Global Invoice']),
            'prefix' => 'GLB',
            'counter_type' => 'global',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testDocumentTypes[] = $globalDocType->id;
        
        $invoice = new Invoice();
        
        // Generate numbers
        $number1 = $invoice->generateDocumentNumber($globalDocType->id);
        $this->assertEquals('GLB-00001', $number1);
        
        $number2 = $invoice->generateDocumentNumber($globalDocType->id);
        $this->assertEquals('GLB-00002', $number2);
        
        // Verify sequence has no year/month
        $stmt = self::$db->prepare("SELECT last_number, year, month FROM document_sequences WHERE document_type_id = ?");
        $stmt->execute([$globalDocType->id]);
        $sequence = $stmt->fetch(\PDO::FETCH_ASSOC);
        
        $this->assertNotNull($sequence);
        $this->assertEquals(2, $sequence['last_number']);
        $this->assertNull($sequence['year']);
        $this->assertNull($sequence['month']);
    }
    
    public function testCreditNoteDocumentType()
    {
        $creditNoteType = DocumentType::create([
            'code' => 'TEST-CN',
            'name' => json_encode(['en' => 'Credit Note', 'fr' => 'Note de Crédit']),
            'prefix' => 'CN',
            'counter_type' => 'yearly',
            'is_negative' => 1,
            'requires_reference' => 1,
            'icon' => 'bi-file-minus',
            'color' => '#dc3545',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testDocumentTypes[] = $creditNoteType->id;
        
        // Test negative flag
        $this->assertTrue($creditNoteType->is_negative);
        $this->assertTrue($creditNoteType->requires_reference);
        
        // Generate credit note number
        $invoice = new Invoice();
        $year = date('Y');
        $number = $invoice->generateDocumentNumber($creditNoteType->id);
        $expectedNumber = "CN-{$year}-00001";
        $this->assertEquals($expectedNumber, $number);
    }
    
    public function testCustomNumberFormat()
    {
        $customDocType = DocumentType::create([
            'code' => 'TEST-CUSTOM',
            'name' => json_encode(['en' => 'Custom Format']),
            'prefix' => 'CUST',
            'counter_type' => 'yearly',
            'number_format' => '{PREFIX}/{YYYY}/{MM}/{NNNN}',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testDocumentTypes[] = $customDocType->id;
        
        $invoice = new Invoice();
        $year = date('Y');
        $month = date('m');
        
        // Generate number with custom format
        $number = $invoice->generateDocumentNumber($customDocType->id);
        $expectedNumber = "CUST/{$year}/{$month}/0001";
        $this->assertEquals($expectedNumber, $number);
    }
    
    public function testMultipleDocumentTypes()
    {
        $types = [
            ['code' => 'TEST-QUOTE', 'name' => 'Quote', 'prefix' => 'QT', 'icon' => 'bi-file-text'],
            ['code' => 'TEST-ORDER', 'name' => 'Order', 'prefix' => 'ORD', 'icon' => 'bi-cart'],
            ['code' => 'TEST-DELIVERY', 'name' => 'Delivery Note', 'prefix' => 'DN', 'icon' => 'bi-truck'],
            ['code' => 'TEST-RECEIPT', 'name' => 'Receipt', 'prefix' => 'RCP', 'icon' => 'bi-receipt']
        ];
        
        foreach ($types as $typeData) {
            $docType = DocumentType::create([
                'code' => $typeData['code'],
                'name' => json_encode(['en' => $typeData['name']]),
                'prefix' => $typeData['prefix'],
                'counter_type' => 'yearly',
                'icon' => $typeData['icon'],
                'is_active' => 1,
                'created_by' => 1
            ]);
            
            $this->testDocumentTypes[] = $docType->id;
        }
        
        // Test retrieving all active document types
        $activeTypes = DocumentType::where('is_active', '=', 1)
                                  ->where('code', 'LIKE', 'TEST-%')
                                  ->orderBy('name')
                                  ->get();
        
        $this->assertGreaterThanOrEqual(4, count($activeTypes));
    }
    
    public function testDocumentTypeStatus()
    {
        $docType = DocumentType::create([
            'code' => 'TEST-STATUS',
            'name' => json_encode(['en' => 'Status Test']),
            'prefix' => 'ST',
            'counter_type' => 'yearly',
            'is_active' => 1,
            'created_by' => 1
        ]);
        
        $this->testDocumentTypes[] = $docType->id;
        
        // Test active status
        $this->assertTrue($docType->is_active);
        
        // Deactivate
        $docType->is_active = 0;
        $docType->save();
        
        // Verify deactivated
        $deactivated = DocumentType::find($docType->id);
        $this->assertFalse($deactivated->is_active);
    }
    
    public static function tearDownAfterClass(): void
    {
        // Clean up test data
        self::$db->exec("DELETE FROM document_sequences WHERE document_type_id IN (SELECT id FROM document_types WHERE code LIKE 'TEST-%')");
        self::$db->exec("DELETE FROM document_types WHERE code LIKE 'TEST-%'");
    }
}