# How to Restore Fit360 to Document Root

Your application was previously installed directly in `C:\wamp64\www\` but is now in `C:\wamp64\www\fit\`.

## Option 1: Move Back to Document Root (Recommended)

1. **Stop WAMP services**

2. **Move all files from `C:\wamp64\www\fit\` to `C:\wamp64\www\`**
   - Move everything EXCEPT the `fit` folder itself
   - Your `public` folder should be at `C:\wamp64\www\public\`
   - Your `app` folder should be at `C:\wamp64\www\app\`
   - etc.

3. **Update .env file:**
   ```
   APP_URL=http://localhost
   APP_BASE_URL=
   ```

4. **Remove the redirect .htaccess I created:**
   - Delete `C:\wamp64\www\.htaccess`

5. **Restart WAMP**

6. **Access at:** http://localhost/

## Option 2: Update All Links (More Work)

If you want to keep it in the `/fit/` subdirectory:

1. All your links need to be updated from:
   - `http://localhost/clients` → `http://localhost/fit/public/clients`
   - `http://localhost/invoices` → `http://localhost/fit/public/invoices`
   - etc.

2. Update .env to ensure BASE_URL is set correctly

3. Clear all caches

## Quick Test

After moving files, test with:
- http://localhost/test-setup.php (if Option 1)
- http://localhost/fit/public/test-setup.php (if Option 2)