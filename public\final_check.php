<?php
/**
 * Final check for syntax errors
 */

// Clear all caches
if (function_exists('opcache_reset')) {
    opcache_reset();
}

$file = dirname(__DIR__) . '/app/views/invoices/create-modern.twig';
$content = file_get_contents($file);

echo "<pre>";
echo "=== Final Syntax Check ===\n\n";

// Check for undefined variables in template literals
$issues = [];

// Check for ${varName} patterns and verify they're defined
preg_match_all('/\$\{([a-zA-Z_][a-zA-Z0-9_]*)\}/', $content, $matches, PREG_OFFSET_CAPTURE);

if (!empty($matches[1])) {
    echo "Found " . count($matches[1]) . " template literal variables:\n\n";
    
    foreach ($matches[1] as $match) {
        $varName = $match[0];
        $position = $match[1];
        
        // Find line number
        $lineNumber = substr_count(substr($content, 0, $position), "\n") + 1;
        
        // Check if variable is defined nearby (within 200 lines before)
        $startCheck = max(0, $position - 10000);
        $contextBefore = substr($content, $startCheck, $position - $startCheck);
        
        $patterns = [
            "const $varName =",
            "let $varName =",
            "var $varName =",
            "$varName =",
            "function.*\($varName",
            "forEach.*$varName",
            "\($varName\)"
        ];
        
        $isDefined = false;
        foreach ($patterns as $pattern) {
            if (preg_match("/$pattern/", $contextBefore)) {
                $isDefined = true;
                break;
            }
        }
        
        if (!$isDefined) {
            $issues[] = "Line $lineNumber: Variable '$varName' may not be defined";
            echo "⚠️ Line $lineNumber: \${$varName} - POSSIBLY UNDEFINED\n";
            
            // Show context
            $lines = explode("\n", $content);
            if (isset($lines[$lineNumber - 1])) {
                echo "   Context: " . trim($lines[$lineNumber - 1]) . "\n";
            }
        } else {
            echo "✅ Line $lineNumber: \${$varName} - defined\n";
        }
    }
}

// Clear Twig cache
$cacheDir = dirname(__DIR__) . '/storage/cache/twig';
if (is_dir($cacheDir)) {
    system("rm -rf " . escapeshellarg($cacheDir) . "/*");
    echo "\n✅ Twig cache cleared\n";
}

if (empty($issues)) {
    echo "\n✅ No syntax issues found!\n";
} else {
    echo "\n⚠️ Potential issues found:\n";
    foreach ($issues as $issue) {
        echo "  - $issue\n";
    }
}

echo "\nCache bust marker: ";
if (preg_match('/{# CACHE_BUST_(\d+) #}/', $content, $match)) {
    echo $match[1];
} else {
    echo "Not found";
}

echo "\n\nTo force browser to reload:\n";
echo "1. Clear browser cache (Ctrl+Shift+Delete)\n";
echo "2. Or use incognito/private window\n";
echo "3. Visit: http://localhost/fit/public/invoices/create?_cb=" . time() . "\n";

echo "</pre>";
?>