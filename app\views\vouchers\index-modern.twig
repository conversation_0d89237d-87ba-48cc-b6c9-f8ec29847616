{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.vouchers') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.vouchers') }}</h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#useVoucherModal">
                <i class="bi bi-ticket-perforated me-2"></i>{{ __('vouchers.use_voucher') }}
            </button>
            <a href="{{ base_url }}/vouchers/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('vouchers.create_voucher') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('vouchers.total_vouchers') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_vouchers|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-ticket text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('vouchers.active_vouchers') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.active_vouchers|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('vouchers.total_value') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.total_value|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-euro text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-danger h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-danger text-uppercase mb-1">
                                {{ __('vouchers.expiring_soon') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.expiring_soon|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-history text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/vouchers" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">{{ __('common.status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="active" {{ filters.status == 'active' ? 'selected' : '' }}>{{ __('vouchers.active') }}</option>
                        <option value="used" {{ filters.status == 'used' ? 'selected' : '' }}>{{ __('vouchers.used') }}</option>
                        <option value="expired" {{ filters.status == 'expired' ? 'selected' : '' }}>{{ __('vouchers.expired') }}</option>
                        <option value="cancelled" {{ filters.status == 'cancelled' ? 'selected' : '' }}>{{ __('vouchers.cancelled') }}</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="type" class="form-label">{{ __('vouchers.type') }}</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="amount" {{ filters.type == 'amount' ? 'selected' : '' }}>{{ __('vouchers.fixed_amount') }}</option>
                        <option value="percentage" {{ filters.type == 'percentage' ? 'selected' : '' }}>{{ __('vouchers.percentage') }}</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="valid_from" class="form-label">{{ __('vouchers.valid_from') }}</label>
                    <input type="date" class="form-control" id="valid_from" name="valid_from" value="{{ filters.valid_from }}">
                </div>
                
                <div class="col-md-3">
                    <label for="search" class="form-label">{{ __('common.search') }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" value="{{ filters.search }}" 
                               placeholder="{{ __('vouchers.search_placeholder') }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Vouchers Table -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ __('vouchers.code') }}</th>
                            <th>{{ __('vouchers.description') }}</th>
                            <th>{{ __('vouchers.type') }}</th>
                            <th>{{ __('vouchers.value') }}</th>
                            <th>{{ __('vouchers.valid_from') }}</th>
                            <th>{{ __('vouchers.valid_until') }}</th>
                            <th>{{ __('vouchers.usage') }}</th>
                            <th>{{ __('common.status') }}</th>
                            <th>{{ __('common.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for voucher in vouchers %}
                        <tr>
                            <td>
                                <code class="fs-6">{{ voucher.code }}</code>
                            </td>
                            <td>{{ voucher.description|default('-') }}</td>
                            <td>
                                {% if voucher.type == 'amount' %}
                                    <span class="badge bg-primary">{{ __('vouchers.fixed_amount') }}</span>
                                {% else %}
                                    <span class="badge bg-info">{{ __('vouchers.percentage') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if voucher.type == 'amount' %}
                                    {{ currency }}{{ voucher.value|number_format(2, ',', ' ') }}
                                {% else %}
                                    {{ voucher.value }}%
                                {% endif %}
                            </td>
                            <td>{{ voucher.valid_from|date('d/m/Y') }}</td>
                            <td>
                                {{ voucher.valid_until|date('d/m/Y') }}
                                {% if voucher.is_expiring_soon %}
                                    <i class="bi bi-exclamation-triangle text-warning ms-1" title="{{ __('vouchers.expiring_soon') }}"></i>
                                {% endif %}
                            </td>
                            <td>
                                <div class="progress" style="height: 20px;">
                                    <div class="progress-bar" role="progressbar" 
                                         style="width: {{ voucher.usage_percentage }}%"
                                         aria-valuenow="{{ voucher.usage_percentage }}" 
                                         aria-valuemin="0" aria-valuemax="100">
                                        {{ voucher.times_used }}/{{ voucher.max_uses|default('∞') }}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if voucher.status == 'active' %}
                                    <span class="badge bg-success">{{ __('vouchers.active') }}</span>
                                {% elseif voucher.status == 'used' %}
                                    <span class="badge bg-secondary">{{ __('vouchers.used') }}</span>
                                {% elseif voucher.status == 'expired' %}
                                    <span class="badge bg-warning">{{ __('vouchers.expired') }}</span>
                                {% elseif voucher.status == 'cancelled' %}
                                    <span class="badge bg-danger">{{ __('vouchers.cancelled') }}</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm btn-light" type="button" data-bs-toggle="dropdown" 
                                            data-bs-boundary="viewport" data-bs-flip="true" aria-expanded="false">
                                        <i class="bi bi-three-dots-vertical"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="{{ base_url }}/vouchers/{{ voucher.id }}">
                                                <i class="bi bi-eye me-2"></i>{{ __('common.view') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="copyCode('{{ voucher.code }}')">
                                                <i class="bi bi-clipboard me-2"></i>{{ __('vouchers.copy_code') }}
                                            </a>
                                        </li>
                                        {% if voucher.status == 'active' %}
                                        <li>
                                            <a class="dropdown-item" href="{{ base_url }}/vouchers/{{ voucher.id }}/print" target="_blank">
                                                <i class="bi bi-printer me-2"></i>{{ __('common.print') }}
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="emailVoucher({{ voucher.id }})">
                                                <i class="bi bi-envelope me-2"></i>{{ __('common.email') }}
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="cancelVoucher({{ voucher.id }})">
                                                <i class="bi bi-x-circle me-2"></i>{{ __('vouchers.cancel') }}
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="9" class="text-center py-4 text-muted">
                                {{ __('vouchers.no_vouchers_found') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if vouchers|length > 0 %}
                <div class="d-flex justify-content-center mt-4">
                    {{ include('_partials/pagination.twig', {
                        'current_page': current_page,
                        'total_pages': total_pages,
                        'base_url': base_url ~ '/vouchers'
                    }) }}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Use Voucher Modal -->
<div class="modal fade" id="useVoucherModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/vouchers/use">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('vouchers.use_voucher') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="voucher_code" class="form-label">{{ __('vouchers.enter_code') }} *</label>
                        <input type="text" class="form-control" id="voucher_code" name="code" 
                               placeholder="XXXXXX" required style="text-transform: uppercase;">
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoice_id" class="form-label">{{ __('vouchers.apply_to_invoice') }}</label>
                        <select class="form-select" id="invoice_id" name="invoice_id">
                            <option value="">{{ __('vouchers.check_only') }}</option>
                            {% for invoice in pending_invoices %}
                                <option value="{{ invoice.id }}">
                                    {{ invoice.invoice_number }} - {{ currency }}{{ invoice.total_amount|number_format(2, ',', ' ') }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div id="voucherInfo" class="alert alert-info d-none">
                        <i class="bi bi-info-circle me-2"></i>
                        <span id="voucherMessage"></span>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('vouchers.validate') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function copyCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        // Show toast notification
        const toast = new bootstrap.Toast(document.createElement('div'));
        toast.show();
        alert('{{ __("vouchers.code_copied") }}');
    });
}

function emailVoucher(id) {
    const email = prompt('{{ __("vouchers.enter_email") }}:');
    if (email) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/vouchers/' + id + '/email';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        const emailInput = document.createElement('input');
        emailInput.type = 'hidden';
        emailInput.name = 'email';
        emailInput.value = email;
        form.appendChild(emailInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function cancelVoucher(id) {
    if (confirm('{{ __("vouchers.cancel_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/vouchers/' + id + '/cancel';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Check voucher code as user types
document.getElementById('voucher_code').addEventListener('input', function() {
    const code = this.value;
    if (code.length >= 6) {
        // Check voucher via AJAX
        fetch('{{ base_url }}/vouchers/check?code=' + code)
            .then(response => response.json())
            .then(data => {
                const info = document.getElementById('voucherInfo');
                const message = document.getElementById('voucherMessage');
                
                if (data.valid) {
                    info.classList.remove('d-none', 'alert-danger');
                    info.classList.add('alert-info');
                    message.textContent = data.message;
                } else {
                    info.classList.remove('d-none', 'alert-info');
                    info.classList.add('alert-danger');
                    message.textContent = data.message;
                }
            });
    }
});
</script>

<style>
/* Fix for dropdown positioning in tables */
.table-responsive .dropdown {
    position: static;
}

.table-responsive .dropdown-menu {
    position: absolute !important;
    transform: translate3d(0px, 0px, 0px) !important;
    will-change: transform !important;
}
</style>
{% endblock %}