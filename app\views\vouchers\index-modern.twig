{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.vouchers') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.vouchers') }}</h1>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#useVoucherModal">
                <i class="bi bi-ticket-perforated me-2"></i>{{ __('vouchers.use_voucher') }}
            </button>
            <a href="{{ base_url }}/vouchers/create" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>{{ __('vouchers.create_voucher') }}
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-primary h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                                {{ __('vouchers.total_vouchers') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.total_vouchers|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-ticket text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-success h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-success text-uppercase mb-1">
                                {{ __('vouchers.active_vouchers') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.active_vouchers|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-check-circle text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-warning h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-warning text-uppercase mb-1">
                                {{ __('vouchers.total_value') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">
                                {{ currency }}{{ statistics.total_value|number_format(2, ',', ' ') }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-currency-euro text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card border-start border-4 border-danger h-100">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="text-xs fw-bold text-danger text-uppercase mb-1">
                                {{ __('vouchers.expiring_soon') }}
                            </div>
                            <div class="h5 mb-0 fw-bold text-gray-800">{{ statistics.expiring_soon|default(0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-clock-history text-gray-300 fs-2"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow-sm mb-4">
        <div class="card-body">
            <form method="GET" action="{{ base_url }}/vouchers" class="row g-3">
                <div class="col-md-3">
                    <label for="status" class="form-label">{{ __('common.status') }}</label>
                    <select class="form-select" id="status" name="status">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="active" {{ filters.status == 'active' ? 'selected' : '' }}>{{ __('vouchers.active') }}</option>
                        <option value="used" {{ filters.status == 'used' ? 'selected' : '' }}>{{ __('vouchers.used') }}</option>
                        <option value="expired" {{ filters.status == 'expired' ? 'selected' : '' }}>{{ __('vouchers.expired') }}</option>
                        <option value="cancelled" {{ filters.status == 'cancelled' ? 'selected' : '' }}>{{ __('vouchers.cancelled') }}</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="type" class="form-label">{{ __('vouchers.type') }}</label>
                    <select class="form-select" id="type" name="type">
                        <option value="">{{ __('common.all') }}</option>
                        <option value="amount" {{ filters.type == 'amount' ? 'selected' : '' }}>{{ __('vouchers.fixed_amount') }}</option>
                        <option value="percentage" {{ filters.type == 'percentage' ? 'selected' : '' }}>{{ __('vouchers.percentage') }}</option>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="valid_from" class="form-label">{{ __('vouchers.valid_from') }}</label>
                    <input type="date" class="form-control" id="valid_from" name="valid_from" value="{{ filters.valid_from }}">
                </div>
                
                <div class="col-md-3">
                    <label for="search" class="form-label">{{ __('common.search') }}</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" value="{{ filters.search }}" 
                               placeholder="{{ __('vouchers.search_placeholder') }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="d-none justify-content-between align-items-center mb-3" id="bulk-actions">
        <div>
            <span class="text-muted">{{ __('common.selected') }}: <strong id="selected-count">0</strong></span>
        </div>
        <div class="d-flex gap-2">
            <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                <i class="bi bi-trash me-1"></i>{{ __('common.delete_selected') }}
            </button>
            <button type="button" class="btn btn-warning btn-sm" onclick="bulkCancel()">
                <i class="bi bi-x-circle me-1"></i>{{ __('vouchers.cancel_selected') }}
            </button>
        </div>
    </div>

    <!-- Vouchers Table -->
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            {% if visibleColumns.checkbox is not defined or visibleColumns.checkbox %}
                            <th class="text-center" style="width: 40px;">
                                <input type="checkbox" class="form-check-input" id="select-all" onchange="toggleSelectAll()">
                            </th>
                            {% endif %}
                            {% if visibleColumns.voucher_number is not defined or visibleColumns.voucher_number %}
                            <th>{{ __('vouchers.code') }}</th>
                            {% endif %}
                            {% if visibleColumns.beneficiary is not defined or visibleColumns.beneficiary %}
                            <th>{{ __('vouchers.beneficiary') }}</th>
                            {% endif %}
                            {% if visibleColumns.type is not defined or visibleColumns.type %}
                            <th>{{ __('vouchers.type') }}</th>
                            {% endif %}
                            {% if visibleColumns.amount is not defined or visibleColumns.amount %}
                            <th>{{ __('vouchers.amount') }}</th>
                            {% endif %}
                            {% if visibleColumns.remaining is not defined or visibleColumns.remaining %}
                            <th>{{ __('vouchers.remaining') }}</th>
                            {% endif %}
                            {% if visibleColumns.expiry_date is not defined or visibleColumns.expiry_date %}
                            <th>{{ __('vouchers.expiry_date') }}</th>
                            {% endif %}
                            {% if visibleColumns.status is not defined or visibleColumns.status %}
                            <th>{{ __('common.status') }}</th>
                            {% endif %}
                            {% if visibleColumns.created_by is not defined or visibleColumns.created_by %}
                            <th>{{ __('common.created_by') }}</th>
                            {% endif %}
                            {% if visibleColumns.actions is not defined or visibleColumns.actions %}
                            <th class="text-end">{{ __('common.actions') }}</th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for voucher in vouchers %}
                        <tr data-id="{{ voucher.id }}">
                            {% if visibleColumns.checkbox is not defined or visibleColumns.checkbox %}
                            <td class="text-center">
                                <input type="checkbox" class="form-check-input voucher-checkbox" value="{{ voucher.id }}" onchange="updateBulkActions()">
                            </td>
                            {% endif %}
                            {% if visibleColumns.voucher_number is not defined or visibleColumns.voucher_number %}
                            <td>
                                <code class="fs-6">{{ voucher.voucher_number }}</code>
                            </td>
                            {% endif %}
                            {% if visibleColumns.beneficiary is not defined or visibleColumns.beneficiary %}
                            <td>{{ voucher.beneficiary_name|default(voucher.client_name) }}</td>
                            {% endif %}
                            {% if visibleColumns.type is not defined or visibleColumns.type %}
                            <td>
                                {% if voucher.voucher_type == 'amount' %}
                                    <span class="badge bg-primary">{{ __('vouchers.type.amount') }}</span>
                                {% elseif voucher.voucher_type == 'service' %}
                                    <span class="badge bg-info">{{ __('vouchers.type.service') }}</span>
                                {% else %}
                                    <span class="badge bg-warning">{{ __('vouchers.type.percentage') }}</span>
                                {% endif %}
                            </td>
                            {% endif %}
                            {% if visibleColumns.amount is not defined or visibleColumns.amount %}
                            <td>
                                {% if voucher.voucher_type == 'amount' %}
                                    {{ currency|default('€') }}{{ voucher.amount|number_format(2, ',', ' ') }}
                                {% elseif voucher.voucher_type == 'percentage' %}
                                    {{ voucher.amount }} %
                                {% else %}
                                    {{ voucher.service_description|default('-') }}
                                {% endif %}
                            </td>
                            {% endif %}
                            {% if visibleColumns.remaining is not defined or visibleColumns.remaining %}
                            <td>
                                {% if voucher.voucher_type == 'amount' %}
                                    {{ currency|default('€') }}{{ voucher.remaining_amount|number_format(2, ',', ' ') }}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                            {% endif %}
                            {% if visibleColumns.expiry_date is not defined or visibleColumns.expiry_date %}
                            <td>
                                {{ voucher.expiry_date|date('d/m/Y') }}
                                {% if voucher.is_expiring_soon %}
                                    <i class="bi bi-exclamation-triangle text-warning ms-1" title="{{ __('vouchers.expiring_soon') }}"></i>
                                {% endif %}
                            </td>
                            {% endif %}
                            {% if visibleColumns.status is not defined or visibleColumns.status %}
                            <td>
                                {% if voucher.status == 'active' %}
                                    <span class="badge bg-success">{{ __('vouchers.status.active') }}</span>
                                {% elseif voucher.status == 'partially_used' %}
                                    <span class="badge bg-info">{{ __('vouchers.status.partially_used') }}</span>
                                {% elseif voucher.status == 'fully_used' %}
                                    <span class="badge bg-secondary">{{ __('vouchers.status.fully_used') }}</span>
                                {% elseif voucher.status == 'expired' %}
                                    <span class="badge bg-warning">{{ __('vouchers.status.expired') }}</span>
                                {% elseif voucher.status == 'cancelled' %}
                                    <span class="badge bg-danger">{{ __('vouchers.status.cancelled') }}</span>
                                {% endif %}
                            </td>
                            {% endif %}
                            {% if visibleColumns.created_by is not defined or visibleColumns.created_by %}
                            <td>{{ voucher.creator_name|default('-') }}</td>
                            {% endif %}
                            {% if visibleColumns.actions is not defined or visibleColumns.actions %}
                            <td class="text-end">
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="{{ base_url }}/vouchers/{{ voucher.id }}" 
                                       class="btn btn-outline-primary" 
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.view') }}">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-info" 
                                            onclick="copyCode('{{ voucher.voucher_number }}')"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('vouchers.copy_code') }}">
                                        <i class="bi bi-clipboard"></i>
                                    </button>
                                    {% if voucher.status == 'active' %}
                                    <a href="{{ base_url }}/vouchers/{{ voucher.id }}/print" 
                                       class="btn btn-outline-secondary" 
                                       target="_blank"
                                       data-bs-toggle="tooltip" 
                                       title="{{ __('common.print') }}">
                                        <i class="bi bi-printer"></i>
                                    </a>
                                    <button type="button" 
                                            class="btn btn-outline-primary" 
                                            onclick="emailVoucher({{ voucher.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('common.email') }}">
                                        <i class="bi bi-envelope"></i>
                                    </button>
                                    <button type="button" 
                                            class="btn btn-outline-danger" 
                                            onclick="cancelVoucher({{ voucher.id }})"
                                            data-bs-toggle="tooltip" 
                                            title="{{ __('vouchers.cancel') }}">
                                        <i class="bi bi-x-circle"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                            {% endif %}
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="10" class="text-center py-4 text-muted">
                                {{ __('vouchers.no_vouchers_found') }}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            {% if vouchers|length > 0 %}
                <div class="d-flex justify-content-center mt-4">
                    {{ include('_partials/pagination.twig', {
                        'current_page': current_page,
                        'total_pages': total_pages,
                        'base_url': base_url ~ '/vouchers'
                    }) }}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Use Voucher Modal -->
<div class="modal fade" id="useVoucherModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ base_url }}/vouchers/use">
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
                
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('vouchers.use_voucher') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="voucher_code" class="form-label">{{ __('vouchers.enter_code') }} *</label>
                        <input type="text" class="form-control" id="voucher_code" name="code" 
                               placeholder="XXXXXX" required style="text-transform: uppercase;">
                    </div>
                    
                    <div class="mb-3">
                        <label for="invoice_id" class="form-label">{{ __('vouchers.apply_to_invoice') }}</label>
                        <select class="form-select" id="invoice_id" name="invoice_id">
                            <option value="">{{ __('vouchers.check_only') }}</option>
                            {% for invoice in pending_invoices %}
                                <option value="{{ invoice.id }}">
                                    {{ invoice.invoice_number }} - {{ currency }}{{ invoice.total_amount|number_format(2, ',', ' ') }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div id="voucherInfo" class="alert alert-info d-none">
                        <i class="bi bi-info-circle me-2"></i>
                        <span id="voucherMessage"></span>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('common.cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('vouchers.validate') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Initialize Bootstrap tooltips
document.addEventListener('DOMContentLoaded', function() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

function copyCode(code) {
    navigator.clipboard.writeText(code).then(function() {
        // Show toast notification
        const toast = new bootstrap.Toast(document.createElement('div'));
        toast.show();
        alert('{{ __("vouchers.code_copied") }}');
    });
}

function emailVoucher(id) {
    const email = prompt('{{ __("vouchers.enter_email") }}:');
    if (email) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/vouchers/' + id + '/email';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        const emailInput = document.createElement('input');
        emailInput.type = 'hidden';
        emailInput.name = 'email';
        emailInput.value = email;
        form.appendChild(emailInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

function cancelVoucher(id) {
    if (confirm('{{ __("vouchers.cancel_confirm") }}')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '{{ base_url }}/vouchers/' + id + '/cancel';
        
        const csrf = document.createElement('input');
        csrf.type = 'hidden';
        csrf.name = 'csrf_token';
        csrf.value = '{{ csrf_token }}';
        form.appendChild(csrf);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Bulk actions functionality
function toggleSelectAll() {
    const selectAll = document.getElementById('select-all');
    const checkboxes = document.querySelectorAll('.voucher-checkbox');
    checkboxes.forEach(cb => {
        cb.checked = selectAll.checked;
    });
    updateBulkActions();
}

function updateBulkActions() {
    const checkboxes = document.querySelectorAll('.voucher-checkbox:checked');
    const count = checkboxes.length;
    const bulkActions = document.getElementById('bulk-actions');
    const selectedCount = document.getElementById('selected-count');
    
    if (count > 0) {
        bulkActions.classList.remove('d-none');
        bulkActions.classList.add('d-flex');
        selectedCount.textContent = count;
    } else {
        bulkActions.classList.remove('d-flex');
        bulkActions.classList.add('d-none');
    }
    
    // Update select all checkbox state
    const selectAll = document.getElementById('select-all');
    const totalCheckboxes = document.querySelectorAll('.voucher-checkbox');
    selectAll.checked = count === totalCheckboxes.length && count > 0;
    selectAll.indeterminate = count > 0 && count < totalCheckboxes.length;
}

function getSelectedIds() {
    const checkboxes = document.querySelectorAll('.voucher-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function bulkDelete() {
    const ids = getSelectedIds();
    if (ids.length === 0) return;
    
    if (confirm('{{ __("vouchers.bulk_delete_confirm") }}'.replace(':count', ids.length))) {
        // Implementation for bulk delete
        console.log('Bulk delete:', ids);
        // TODO: Implement bulk delete functionality
    }
}

function bulkCancel() {
    const ids = getSelectedIds();
    if (ids.length === 0) return;
    
    if (confirm('{{ __("vouchers.bulk_cancel_confirm") }}'.replace(':count', ids.length))) {
        // Implementation for bulk cancel
        console.log('Bulk cancel:', ids);
        // TODO: Implement bulk cancel functionality
    }
}

// Check voucher code as user types
document.getElementById('voucher_code').addEventListener('input', function() {
    const code = this.value;
    if (code.length >= 6) {
        // Check voucher via AJAX
        fetch('{{ base_url }}/vouchers/check?code=' + code)
            .then(response => response.json())
            .then(data => {
                const info = document.getElementById('voucherInfo');
                const message = document.getElementById('voucherMessage');
                
                if (data.valid) {
                    info.classList.remove('d-none', 'alert-danger');
                    info.classList.add('alert-info');
                    message.textContent = data.message;
                } else {
                    info.classList.remove('d-none', 'alert-info');
                    info.classList.add('alert-danger');
                    message.textContent = data.message;
                }
            });
    }
});
</script>

{% endblock %}