{% extends "base-modern.twig" %}

{% block title %}{{ __('translations.multilingual_editor') }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">{{ __('translations.multilingual_editor') }}</h3>
                <div class="card-tools">
                    <a href="{{ base_url }}/translations" class="btn btn-secondary btn-sm">
                        <i class="bi bi-arrow-left"></i> {{ __('common.back') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <!-- Search and Filter Section -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text">
                                <i class="bi bi-search"></i>
                            </span>
                            <input type="text" id="searchTranslations" class="form-control" 
                                   placeholder="{{ __('translations.search_translations') }}">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select id="filterGroup" class="form-select">
                            <option value="">{{ __('translations.all_groups') }}</option>
                            {% for group in groups %}
                                <option value="{{ group }}">{{ group }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select id="filterStatus" class="form-select">
                            <option value="">{{ __('translations.all_status') }}</option>
                            <option value="missing">{{ __('translations.missing_translations') }}</option>
                            <option value="complete">{{ __('translations.complete_translations') }}</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="button" class="btn btn-primary w-100" id="saveAllTranslations">
                            <i class="bi bi-save"></i> {{ __('common.save_all') }}
                        </button>
                    </div>
                </div>

                <!-- Language Tabs -->
                <ul class="nav nav-tabs" role="tablist">
                    {% for lang in displayLanguages %}
                        <li class="nav-item">
                            <a class="nav-link {% if loop.first %}active{% endif %}" 
                               data-bs-toggle="tab" href="#lang-{{ lang }}" role="tab">
                                <i class="bi bi-translate me-1"></i>
                                {{ lang|upper }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>

                <!-- Tab Content -->
                <div class="tab-content mt-3">
                    {% for lang in displayLanguages %}
                        <div class="tab-pane fade {% if loop.first %}show active{% endif %}" 
                             id="lang-{{ lang }}" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-hover" id="translationsTable-{{ lang }}">
                                    <thead>
                                        <tr>
                                            <th style="width: 150px;">{{ __('translations.group') }}</th>
                                            <th style="width: 250px;">{{ __('translations.key') }}</th>
                                            <th>{{ __('translations.value') }}</th>
                                            <th style="width: 100px;">{{ __('common.actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for keyIdentifier, data in groupedTranslations %}
                                            <tr data-group="{{ data.group }}" data-key="{{ data.key }}" 
                                                class="{% if not data.translations[lang] %}table-warning{% endif %}">
                                                <td>
                                                    <span class="badge bg-secondary">{{ data.group }}</span>
                                                </td>
                                                <td>
                                                    <code>{{ data.key }}</code>
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="text" 
                                                               class="form-control translation-input" 
                                                               data-lang="{{ lang }}" 
                                                               data-group="{{ data.group }}" 
                                                               data-key="{{ data.key }}"
                                                               value="{{ data.translations[lang]|default('') }}" 
                                                               placeholder="{{ __('translations.enter_translation') }}">
                                                        {% if data.translations[displayLanguages[0]] and lang != displayLanguages[0] %}
                                                            <button class="btn btn-outline-secondary btn-sm" 
                                                                    type="button" 
                                                                    data-bs-toggle="popover" 
                                                                    data-bs-content="{{ data.translations[displayLanguages[0]] }}"
                                                                    title="{{ displayLanguages[0]|upper }} Reference">
                                                                <i class="bi bi-info-circle"></i>
                                                            </button>
                                                        {% endif %}
                                                    </div>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-success save-translation" 
                                                            data-lang="{{ lang }}" 
                                                            data-group="{{ data.group }}" 
                                                            data-key="{{ data.key }}">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        {% else %}
                                            <tr>
                                                <td colspan="4" class="text-center text-muted py-4">
                                                    {{ __('translations.no_translations_found') }}
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if totalPages > 1 %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        <li class="page-item {% if currentPage == 1 %}disabled{% endif %}">
                            <a class="page-link" href="?page={{ currentPage - 1 }}&{{ queryString }}">{{ __('common.previous') }}</a>
                        </li>
                        {% for i in 1..totalPages %}
                            <li class="page-item {% if i == currentPage %}active{% endif %}">
                                <a class="page-link" href="?page={{ i }}&{{ queryString }}">{{ i }}</a>
                            </li>
                        {% endfor %}
                        <li class="page-item {% if currentPage == totalPages %}disabled{% endif %}">
                            <a class="page-link" href="?page={{ currentPage + 1 }}&{{ queryString }}">{{ __('common.next') }}</a>
                        </li>
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
$(document).ready(function() {
    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl)
    });

    // Search functionality
    $('#searchTranslations').on('keyup', function() {
        var searchText = $(this).val().toLowerCase();
        filterTranslations();
    });

    // Group filter
    $('#filterGroup').on('change', function() {
        filterTranslations();
    });

    // Status filter
    $('#filterStatus').on('change', function() {
        filterTranslations();
    });

    function filterTranslations() {
        var searchText = $('#searchTranslations').val().toLowerCase();
        var groupFilter = $('#filterGroup').val();
        var statusFilter = $('#filterStatus').val();
        
        $('.tab-pane.active tbody tr').each(function() {
            var group = $(this).data('group');
            var key = $(this).data('key');
            var value = $(this).find('.translation-input').val();
            var hasValue = value.trim() !== '';
            
            var matchesSearch = searchText === '' || 
                               group.toLowerCase().includes(searchText) || 
                               key.toLowerCase().includes(searchText) || 
                               value.toLowerCase().includes(searchText);
            
            var matchesGroup = groupFilter === '' || group === groupFilter;
            
            var matchesStatus = statusFilter === '' || 
                               (statusFilter === 'missing' && !hasValue) || 
                               (statusFilter === 'complete' && hasValue);
            
            if (matchesSearch && matchesGroup && matchesStatus) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    // Save individual translation
    $('.save-translation').on('click', function() {
        var btn = $(this);
        var lang = btn.data('lang');
        var group = btn.data('group');
        var key = btn.data('key');
        var input = btn.closest('tr').find('.translation-input');
        var value = input.val();
        
        saveTranslation(lang, group, key, value, btn);
    });

    // Save on Enter key
    $('.translation-input').on('keypress', function(e) {
        if (e.which === 13) {
            $(this).closest('tr').find('.save-translation').click();
        }
    });

    // Mark changed inputs
    $('.translation-input').on('input', function() {
        $(this).addClass('border-warning');
    });

    // Save all translations
    $('#saveAllTranslations').on('click', function() {
        var translations = [];
        var activeTab = $('.tab-pane.active');
        
        activeTab.find('.translation-input.border-warning').each(function() {
            var input = $(this);
            translations.push({
                lang: input.data('lang'),
                group: input.data('group'),
                key: input.data('key'),
                value: input.val()
            });
        });
        
        if (translations.length === 0) {
            toastr.info('{{ __("translations.no_changes") }}');
            return;
        }
        
        // Save all changed translations
        var savedCount = 0;
        translations.forEach(function(trans) {
            saveTranslation(trans.lang, trans.group, trans.key, trans.value, null, function() {
                savedCount++;
                if (savedCount === translations.length) {
                    toastr.success('{{ __("translations.all_saved") }}');
                }
            });
        });
    });

    function saveTranslation(lang, group, key, value, btn, callback) {
        if (btn) {
            btn.prop('disabled', true);
            btn.html('<i class="bi bi-hourglass-split"></i>');
        }
        
        $.ajax({
            url: '{{ base_url }}/translations/save',
            method: 'POST',
            data: {
                lang: lang,
                group: group,
                key: key,
                value: value,
                csrf_token: '{{ csrf_token }}'
            },
            success: function(response) {
                if (response.success) {
                    if (btn) {
                        btn.html('<i class="bi bi-check"></i>');
                        btn.removeClass('btn-success').addClass('btn-outline-success');
                    }
                    
                    // Remove warning class
                    $('input[data-lang="' + lang + '"][data-group="' + group + '"][data-key="' + key + '"]')
                        .removeClass('border-warning');
                    
                    // Update row class
                    if (value.trim() !== '') {
                        $('tr[data-group="' + group + '"][data-key="' + key + '"]').removeClass('table-warning');
                    }
                    
                    updateStats();
                    
                    if (callback) callback();
                } else {
                    toastr.error(response.message || '{{ __("common.error_occurred") }}');
                    if (btn) btn.html('<i class="bi bi-x"></i>');
                }
            },
            error: function() {
                toastr.error('{{ __("common.error_occurred") }}');
                if (btn) btn.html('<i class="bi bi-x"></i>');
            },
            complete: function() {
                if (btn) {
                    btn.prop('disabled', false);
                    setTimeout(function() {
                        btn.html('<i class="bi bi-check"></i>');
                    }, 1000);
                }
            }
        });
    }

    function updateStats() {
        // Update statistics after saving
        // This would typically make an AJAX call to get updated stats
    }
});
</script>
{% endblock %}