<?php

return [
    // Page titles and headers
    'management' => 'Permissions Management',
    'user_groups' => 'User Groups',
    'add_group' => 'Add New Group',
    'permissions_for' => 'Permissions for :group',
    
    // Search and modules
    'search_modules' => 'Search modules...',
    'module' => 'Module',
    'all_modules' => 'All Modules',
    
    // Permission types
    'view' => 'View',
    'create' => 'Create',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'special' => 'Special',
    
    // Actions
    'copy_from' => 'Copy permissions from',
    'reset_default' => 'Reset to Default',
    'save_changes' => 'Save Changes',
    'load_template' => 'Load Template',
    'select_template' => 'Select a template',
    
    // Templates
    'template_admin' => 'Administrator',
    'template_manager' => 'Manager',
    'template_staff' => 'Staff',
    'template_readonly' => 'Read Only',
    
    // Template descriptions
    'template_admin_desc' => 'Full system access with all permissions',
    'template_manager_desc' => 'Management access with most permissions except system settings',
    'template_staff_desc' => 'Standard staff access for daily operations',
    'template_readonly_desc' => 'View-only access across all modules',
    
    // Messages
    'saving' => 'Saving permissions...',
    'saved_successfully' => 'Permissions saved successfully',
    'save_failed' => 'Failed to save permissions',
    'permissions_updated' => 'Permissions have been updated',
    'permissions_copied' => 'Permissions copied from :source',
    'permissions_reset' => 'Permissions reset to default',
    'template_loaded' => 'Template :template loaded successfully',
    
    // Confirmations
    'confirm_reset' => 'Are you sure you want to reset permissions to default? This action cannot be undone.',
    'confirm_copy' => 'Are you sure you want to copy permissions from :source? Current permissions will be overwritten.',
    'confirm_template' => 'Are you sure you want to load the :template template? Current permissions will be overwritten.',
    
    // Selection
    'select_all' => 'Select All',
    'deselect_all' => 'Deselect All',
    'select_all_in_module' => 'Select all in this module',
    'deselect_all_in_module' => 'Deselect all in this module',
    
    // Audit log
    'audit_log' => 'Audit Log',
    'view_audit_log' => 'View Audit Log',
    'permission_changes' => 'Permission Changes',
    'changed_by' => 'Changed by',
    'changed_at' => 'Changed at',
    'old_permissions' => 'Old Permissions',
    'new_permissions' => 'New Permissions',
    'changes_made' => 'Changes Made',
    'no_audit_entries' => 'No audit log entries found',
    
    // Groups
    'group_name' => 'Group Name',
    'group_description' => 'Description',
    'members_count' => 'Members',
    'edit_group' => 'Edit Group',
    'delete_group' => 'Delete Group',
    'group_created' => 'Group created successfully',
    'group_updated' => 'Group updated successfully',
    'group_deleted' => 'Group deleted successfully',
    'confirm_delete_group' => 'Are you sure you want to delete this group? Users in this group will lose their permissions.',
    
    // Permissions details
    'permission_granted' => 'Permission granted',
    'permission_revoked' => 'Permission revoked',
    'inherited_from' => 'Inherited from :source',
    'custom_permission' => 'Custom permission',
    
    // Special permissions
    'export' => 'Export',
    'import' => 'Import',
    'bulk_actions' => 'Bulk Actions',
    'settings' => 'Settings',
    'reports' => 'Reports',
    'audit' => 'Audit',
    
    // Tooltips
    'permission_view_tooltip' => 'Allow viewing and listing records',
    'permission_create_tooltip' => 'Allow creating new records',
    'permission_edit_tooltip' => 'Allow editing existing records',
    'permission_delete_tooltip' => 'Allow deleting records',
    'permission_special_tooltip' => 'Special module-specific permissions',
    
    // Errors
    'error_loading_permissions' => 'Error loading permissions',
    'error_saving_permissions' => 'Error saving permissions',
    'error_invalid_group' => 'Invalid group selected',
    'error_permission_denied' => 'You do not have permission to perform this action',
    
    // Module names (common modules in fitness center)
    'modules' => [
        'dashboard' => 'Dashboard',
        'members' => 'Members',
        'subscriptions' => 'Subscriptions',
        'payments' => 'Payments',
        'classes' => 'Classes',
        'trainers' => 'Trainers',
        'equipment' => 'Equipment',
        'inventory' => 'Inventory',
        'reports' => 'Reports',
        'settings' => 'Settings',
        'users' => 'Users',
        'permissions' => 'Permissions',
        'audit' => 'Audit Log',
        'notifications' => 'Notifications',
        'calendar' => 'Calendar',
        'pos' => 'Point of Sale',
        'accounting' => 'Accounting',
        'marketing' => 'Marketing',
        'maintenance' => 'Maintenance',
        'access_control' => 'Access Control',
    ],
    
    // Bulk actions
    'bulk_update' => 'Bulk Update Permissions',
    'apply_to_selected' => 'Apply to Selected',
    'grant_all' => 'Grant All',
    'revoke_all' => 'Revoke All',
    
    // Filters
    'filter_by_module' => 'Filter by Module',
    'filter_by_permission' => 'Filter by Permission Type',
    'show_granted_only' => 'Show Granted Only',
    'show_all' => 'Show All',
    
    // Export/Import
    'export_permissions' => 'Export Permissions',
    'import_permissions' => 'Import Permissions',
    'download_template' => 'Download Template',
    'upload_file' => 'Upload File',
    
    // Help text
    'help_permissions' => 'Manage user group permissions across all system modules',
    'help_templates' => 'Use templates to quickly set up common permission configurations',
    'help_copy' => 'Copy permissions from another group as a starting point',
    'help_audit' => 'View all permission changes for compliance and security',
];