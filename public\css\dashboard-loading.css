/* ========================================
   DASHBOARD LOADING STATES & ERROR HANDLING
   Fit360 AdminDesk
   ======================================== */

/* ========================================
   1. SKELETON LOADING STATES
   ======================================== */

/* Base skeleton loader */
.skeleton-loader {
    position: relative;
    overflow: hidden;
    background-color: #f0f0f0;
}

.skeleton-loader::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transform: translateX(-100%);
    background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0,
        rgba(255, 255, 255, 0.2) 20%,
        rgba(255, 255, 255, 0.5) 60%,
        rgba(255, 255, 255, 0)
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    100% {
        transform: translateX(100%);
    }
}

/* Stat card skeleton */
.stat-card-skeleton {
    height: 120px;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.stat-card-skeleton .skeleton-title {
    width: 60%;
    height: 20px;
    margin: 1rem;
    background-color: #e0e0e0;
    border-radius: 4px;
}

.stat-card-skeleton .skeleton-value {
    width: 40%;
    height: 36px;
    margin: 1rem;
    background-color: #e0e0e0;
    border-radius: 4px;
}

/* Table skeleton */
.table-skeleton {
    width: 100%;
}

.table-skeleton .skeleton-row {
    display: flex;
    padding: 1rem;
    border-bottom: 1px solid #f0f0f0;
}

.table-skeleton .skeleton-cell {
    flex: 1;
    height: 20px;
    margin-right: 1rem;
    background-color: #e0e0e0;
    border-radius: 4px;
}

.table-skeleton .skeleton-cell:last-child {
    margin-right: 0;
}

/* Chart skeleton */
.chart-skeleton {
    height: 300px;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chart-skeleton .skeleton-bars {
    display: flex;
    align-items: flex-end;
    gap: 0.5rem;
    height: 200px;
}

.chart-skeleton .skeleton-bar {
    width: 30px;
    background-color: #e0e0e0;
    border-radius: 4px 4px 0 0;
}

.chart-skeleton .skeleton-bar:nth-child(1) { height: 40%; }
.chart-skeleton .skeleton-bar:nth-child(2) { height: 70%; }
.chart-skeleton .skeleton-bar:nth-child(3) { height: 55%; }
.chart-skeleton .skeleton-bar:nth-child(4) { height: 85%; }
.chart-skeleton .skeleton-bar:nth-child(5) { height: 65%; }

/* ========================================
   2. LOADING SPINNERS
   ======================================== */

/* Primary spinner */
.spinner-primary {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    border-top-color: #007bff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Small spinner for buttons */
.spinner-small {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 0.8s linear infinite;
    vertical-align: middle;
    margin-right: 0.5rem;
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
}

.loading-overlay.show {
    opacity: 1;
    visibility: visible;
}

.loading-overlay .spinner-container {
    text-align: center;
}

.loading-overlay .loading-text {
    margin-top: 1rem;
    color: #6c757d;
    font-size: 0.875rem;
}

/* ========================================
   3. ERROR STATES
   ======================================== */

/* Error container */
.error-state {
    padding: 3rem 1rem;
    text-align: center;
}

.error-state .error-icon {
    font-size: 4rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.error-state .error-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #343a40;
    margin-bottom: 0.5rem;
}

.error-state .error-message {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.error-state .error-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

/* Card error state */
.card-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
    padding: 1rem;
    border-radius: 0.5rem;
    margin: 1rem;
}

.card-error .error-icon {
    display: inline-block;
    margin-right: 0.5rem;
}

/* ========================================
   4. EMPTY STATES
   ======================================== */

/* Empty state container */
.empty-state {
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
}

.empty-state .empty-icon {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 1rem;
}

.empty-state .empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.empty-state .empty-message {
    margin-bottom: 1.5rem;
}

.empty-state .empty-action {
    display: inline-block;
}

/* ========================================
   5. PROGRESS INDICATORS
   ======================================== */

/* Progress bar */
.progress-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background-color: #f8f9fa;
    z-index: 1100;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.progress-indicator.show {
    opacity: 1;
}

.progress-indicator .progress-bar {
    height: 100%;
    background-color: #007bff;
    width: 0;
    transition: width 0.3s ease;
}

.progress-indicator.indeterminate .progress-bar {
    width: 30%;
    animation: indeterminate 1.5s infinite;
}

@keyframes indeterminate {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(400%);
    }
}

/* ========================================
   6. TOAST NOTIFICATIONS
   ======================================== */

/* Toast container */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1200;
}

/* Custom toast styles */
.toast {
    min-width: 300px;
    margin-bottom: 0.5rem;
}

.toast.toast-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.toast.toast-error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.toast.toast-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.toast.toast-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* ========================================
   7. LOADING BUTTON STATES
   ======================================== */

/* Loading button */
.btn-loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn-loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #fff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 0.8s linear infinite;
}

.btn-loading.btn-sm::after {
    width: 12px;
    height: 12px;
    margin-left: -6px;
    margin-top: -6px;
    border-width: 1px;
}

.btn-loading.btn-lg::after {
    width: 20px;
    height: 20px;
    margin-left: -10px;
    margin-top: -10px;
    border-width: 3px;
}

/* ========================================
   8. REFRESH ANIMATION
   ======================================== */

/* Refresh icon animation */
.icon-refresh {
    display: inline-block;
    transition: transform 0.3s ease;
}

.icon-refresh.spinning {
    animation: spin 1s linear infinite;
}

/* Pull to refresh */
.pull-to-refresh-box {
    position: fixed;
    top: -80px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: top 0.3s ease;
    z-index: 1100;
}

.pull-to-refresh-box.show {
    top: 20px;
}

.pull-to-refresh-box.refreshing {
    animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        transform: translateX(-50%) scale(1);
    }
    50% {
        transform: translateX(-50%) scale(1.1);
    }
    100% {
        transform: translateX(-50%) scale(1);
    }
}

/* ========================================
   9. MOBILE-SPECIFIC LOADING STATES
   ======================================== */

@media (max-width: 767px) {
    /* Smaller skeletons on mobile */
    .stat-card-skeleton {
        height: 100px;
    }
    
    /* Compact error states */
    .error-state {
        padding: 2rem 1rem;
    }
    
    .error-state .error-icon {
        font-size: 3rem;
    }
    
    /* Mobile toast position */
    .toast-container {
        top: auto;
        bottom: 5rem;
        right: 1rem;
        left: 1rem;
    }
    
    .toast {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* ========================================
   10. UTILITY CLASSES
   ======================================== */

/* Fade in animation */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse animation for attention */
.pulse {
    animation: pulse 2s ease-in-out infinite;
}

/* Disabled state */
.loading-disabled {
    opacity: 0.6;
    pointer-events: none;
    cursor: not-allowed;
}

/* Hidden but accessible */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}