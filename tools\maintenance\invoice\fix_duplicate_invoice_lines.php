<?php
require_once __DIR__ . '/../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
$dotenv->load();
require_once __DIR__ . '/../app/config/bootstrap.php';

$db = Flight::db();

echo '<style>
body { font-family: Arial, sans-serif; margin: 20px; }
.section { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
.warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; }
.error { background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; }
.success { background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; }
pre { background: #e9ecef; padding: 10px; overflow-x: auto; }
table { width: 100%; border-collapse: collapse; margin: 10px 0; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background: #007bff; color: white; }
tr:nth-child(even) { background: #f2f2f2; }
.duplicate { background: #ffcccc !important; }
</style>';

echo "<h1>Find and Fix Duplicate Invoice Lines</h1>";

// First, let's find all duplicate invoice lines
echo '<div class="section">';
echo '<h2>Step 1: Finding Duplicate Invoice Lines</h2>';

// Find duplicate lines based on invoice_id and all relevant fields
$stmt = $db->prepare("
    SELECT 
        il1.*,
        i.invoice_number
    FROM invoice_lines il1
    JOIN invoices i ON i.id = il1.invoice_id
    WHERE EXISTS (
        SELECT 1 
        FROM invoice_lines il2 
        WHERE il2.invoice_id = il1.invoice_id
        AND il2.description = il1.description
        AND il2.quantity = il1.quantity
        AND il2.unit_price = il1.unit_price
        AND il2.vat_rate = il1.vat_rate
        AND il2.id != il1.id
    )
    ORDER BY il1.invoice_id, il1.description, il1.id
");
$stmt->execute();
$duplicates = $stmt->fetchAll(PDO::FETCH_ASSOC);

if (empty($duplicates)) {
    echo '<div class="success">✓ No duplicate invoice lines found!</div>';
} else {
    echo '<div class="error">⚠️ Found ' . count($duplicates) . ' duplicate line entries!</div>';
    
    // Group by invoice
    $groupedDuplicates = [];
    foreach ($duplicates as $dup) {
        $groupedDuplicates[$dup['invoice_id']][] = $dup;
    }
    
    echo '<p>Found duplicates in ' . count($groupedDuplicates) . ' invoices.</p>';
    
    // Display duplicates
    foreach ($groupedDuplicates as $invoiceId => $lines) {
        $invoiceNumber = $lines[0]['invoice_number'];
        echo '<div class="warning">';
        echo '<h3>Invoice: ' . $invoiceNumber . ' (ID: ' . $invoiceId . ')</h3>';
        
        echo '<table>';
        echo '<tr><th>Line ID</th><th>Description</th><th>Qty</th><th>Unit Price</th><th>VAT</th><th>Sort Order</th><th>Action</th></tr>';
        
        // Group identical lines
        $identicalGroups = [];
        foreach ($lines as $line) {
            $key = $line['description'] . '|' . $line['quantity'] . '|' . $line['unit_price'] . '|' . $line['vat_rate'];
            $identicalGroups[$key][] = $line;
        }
        
        foreach ($identicalGroups as $group) {
            $isFirst = true;
            foreach ($group as $line) {
                echo '<tr class="' . (!$isFirst ? 'duplicate' : '') . '">';
                echo '<td>' . $line['id'] . '</td>';
                echo '<td>' . htmlspecialchars($line['description']) . '</td>';
                echo '<td>' . $line['quantity'] . '</td>';
                echo '<td>' . number_format($line['unit_price'], 2) . '€</td>';
                echo '<td>' . $line['vat_rate'] . '%</td>';
                echo '<td>' . ($line['sort_order'] ?? 'NULL') . '</td>';
                echo '<td>';
                if (!$isFirst) {
                    echo '<span style="color: red;">DUPLICATE - To be removed</span>';
                } else {
                    echo '<span style="color: green;">Keep</span>';
                }
                echo '</td>';
                echo '</tr>';
                $isFirst = false;
            }
        }
        
        echo '</table>';
        echo '</div>';
    }
    
    // Add fix button
    if (!isset($_GET['fix'])) {
        echo '<div class="section">';
        echo '<h2>Step 2: Fix Duplicates</h2>';
        echo '<p><strong>The fix will:</strong></p>';
        echo '<ul>';
        echo '<li>Keep the first occurrence of each duplicate line (lowest ID)</li>';
        echo '<li>Delete all duplicate entries</li>';
        echo '<li>Preserve sort order from the kept lines</li>';
        echo '</ul>';
        echo '<form method="get">';
        echo '<input type="hidden" name="fix" value="1">';
        echo '<button type="submit" style="background: #dc3545; color: white; padding: 10px 20px; border: none; cursor: pointer;">Fix Duplicates</button>';
        echo '</form>';
        echo '</div>';
    }
}
echo '</div>';

// Execute fix if requested
if (isset($_GET['fix']) && !empty($duplicates)) {
    echo '<div class="section">';
    echo '<h2>Fixing Duplicates...</h2>';
    
    try {
        $db->beginTransaction();
        
        // For each invoice with duplicates
        foreach ($groupedDuplicates as $invoiceId => $lines) {
            // Group identical lines
            $identicalGroups = [];
            foreach ($lines as $line) {
                $key = $line['description'] . '|' . $line['quantity'] . '|' . $line['unit_price'] . '|' . $line['vat_rate'];
                $identicalGroups[$key][] = $line;
            }
            
            // For each group of identical lines
            foreach ($identicalGroups as $group) {
                if (count($group) > 1) {
                    // Sort by ID to keep the lowest
                    usort($group, function($a, $b) {
                        return $a['id'] - $b['id'];
                    });
                    
                    // Keep the first, delete the rest
                    $keepId = $group[0]['id'];
                    for ($i = 1; $i < count($group); $i++) {
                        $deleteId = $group[$i]['id'];
                        $stmt = $db->prepare("DELETE FROM invoice_lines WHERE id = ?");
                        $stmt->execute([$deleteId]);
                        echo '<div class="success">Deleted duplicate line ID: ' . $deleteId . ' from invoice ' . $lines[0]['invoice_number'] . '</div>';
                    }
                }
            }
            
            // Recalculate invoice totals
            $stmt = $db->prepare("
                UPDATE invoices i
                SET 
                    subtotal = (
                        SELECT SUM(quantity * unit_price) 
                        FROM invoice_lines 
                        WHERE invoice_id = i.id
                    ),
                    vat_amount = (
                        SELECT SUM(quantity * unit_price * vat_rate / 100) 
                        FROM invoice_lines 
                        WHERE invoice_id = i.id
                    ),
                    total = (
                        SELECT SUM(quantity * unit_price * (1 + vat_rate / 100)) 
                        FROM invoice_lines 
                        WHERE invoice_id = i.id
                    )
                WHERE i.id = ?
            ");
            $stmt->execute([$invoiceId]);
            echo '<div class="success">Updated totals for invoice ' . $lines[0]['invoice_number'] . '</div>';
        }
        
        $db->commit();
        echo '<div class="success"><strong>✓ All duplicates have been fixed!</strong></div>';
        
    } catch (Exception $e) {
        $db->rollBack();
        echo '<div class="error">Error: ' . $e->getMessage() . '</div>';
    }
    
    echo '</div>';
}

// Summary
echo '<div class="section">';
echo '<h2>Summary</h2>';
echo '<p><a href="check_duplicate_invoice_lines.php">Check for remaining duplicates</a></p>';
echo '<p><a href="invoice-pdf.php?id=241">Test PDF generation</a></p>';
echo '</div>';