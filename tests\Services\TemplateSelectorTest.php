<?php

namespace Tests\Services;

use PHPUnit\Framework\TestCase;
use App\Services\TemplateSelector;
use App\Models\EmailTemplate;
use App\Models\Invoice;
use App\Models\Client;

class TemplateSelectorTest extends TestCase
{
    private $selector;
    
    protected function setUp(): void
    {
        parent::setUp();
        $this->selector = new TemplateSelector();
    }
    
    /**
     * Test basic template selection
     */
    public function testBasicTemplateSelection()
    {
        // Mock invoice data
        $invoice = new Invoice();
        $invoice->type = 'FAC';
        $invoice->total_amount = 100;
        $invoice->paid_amount = 0;
        $invoice->due_date = date('Y-m-d', strtotime('+30 days'));
        
        // Mock client data
        $client = new Client();
        $client->id = 1;
        $client->name = '<PERSON>';
        $client->language = 'fr';
        
        // Test invoice new template selection
        $context = [
            'invoice' => $invoice,
            'client' => $client,
            'language' => 'fr'
        ];
        
        $template = $this->selector->selectTemplate(TemplateSelector::TYPE_INVOICE_NEW, $context);
        
        $this->assertNotNull($template);
        $this->assertInstanceOf(EmailTemplate::class, $template);
    }
    
    /**
     * Test overdue invoice template selection
     */
    public function testOverdueInvoiceTemplateSelection()
    {
        // Mock overdue invoice
        $invoice = new Invoice();
        $invoice->type = 'FAC';
        $invoice->total_amount = 100;
        $invoice->paid_amount = 0;
        $invoice->due_date = date('Y-m-d', strtotime('-35 days')); // 35 days overdue
        
        $client = new Client();
        $client->language = 'fr';
        
        $context = [
            'invoice' => $invoice,
            'client' => $client
        ];
        
        $template = $this->selector->selectTemplate(TemplateSelector::TYPE_INVOICE_REMINDER, $context);
        $metadata = $this->selector->getSelectionMetadata();
        
        $this->assertNotNull($template);
        $this->assertEquals(35, $metadata['criteria']['days_overdue']);
        $this->assertTrue($metadata['criteria']['is_overdue']);
    }
    
    /**
     * Test retrocession invoice template selection
     */
    public function testRetrocessionTemplateSelection()
    {
        // Mock retrocession invoice
        $invoice = new Invoice();
        $invoice->type = 'FAC-RET30';
        $invoice->total_amount = 1000;
        
        // Mock practitioner client
        $client = new Client();
        $client->client_type = 'practitioner';
        $client->language = 'fr';
        
        $context = [
            'invoice' => $invoice,
            'client' => $client
        ];
        
        $template = $this->selector->selectTemplate(TemplateSelector::TYPE_INVOICE_NEW, $context);
        $metadata = $this->selector->getSelectionMetadata();
        
        $this->assertNotNull($template);
        $this->assertEquals('FAC-RET30', $metadata['criteria']['invoice_type']);
        $this->assertTrue($metadata['criteria']['is_practitioner']);
    }
    
    /**
     * Test template with inheritance
     */
    public function testTemplateInheritance()
    {
        // Assuming we have a template with parent
        $template = $this->selector->getTemplateWithInheritance('invoice_company_en');
        
        if ($template) {
            $variables = [
                'client_name' => 'ACME Corp',
                'invoice_number' => 'INV-2025-001',
                'amount' => '€1,000.00',
                'due_date' => '2025-02-28'
            ];
            
            $parsed = $this->selector->parseWithInheritance($template, $variables);
            
            $this->assertArrayHasKey('subject', $parsed);
            $this->assertArrayHasKey('body', $parsed);
            $this->assertStringContainsString('ACME Corp', $parsed['body']);
        }
    }
    
    /**
     * Test conditional sections parsing
     */
    public function testConditionalSections()
    {
        // Create a template with conditional sections
        $template = new EmailTemplate();
        $template->subject = 'Invoice {invoice_number}';
        $template->body = '
Dear {client_name},

{{if is_overdue}}
Your invoice is overdue. Please pay immediately.
{{/if}}

{{unless is_paid}}
Amount due: {amount}
{{/unless}}

{{if has_items}}
Items:
{{each items}}
- {description}: {price}
{{/each}}
{{/if}}

Thank you for your business.
        ';
        
        $variables = [
            'client_name' => 'John Doe',
            'invoice_number' => 'INV-001',
            'amount' => '€100.00',
            'is_overdue' => true,
            'is_paid' => false,
            'has_items' => true,
            'items' => [
                ['description' => 'Service A', 'price' => '€50.00'],
                ['description' => 'Service B', 'price' => '€50.00']
            ]
        ];
        
        $parsed = $this->selector->parseWithInheritance($template, $variables);
        
        $this->assertStringContainsString('Your invoice is overdue', $parsed['body']);
        $this->assertStringContainsString('Amount due: €100.00', $parsed['body']);
        $this->assertStringContainsString('Service A: €50.00', $parsed['body']);
        $this->assertStringContainsString('Service B: €50.00', $parsed['body']);
    }
    
    /**
     * Test language-specific template selection
     */
    public function testLanguageSpecificSelection()
    {
        $languages = ['fr', 'en', 'de'];
        
        foreach ($languages as $lang) {
            $client = new Client();
            $client->language = $lang;
            
            $context = [
                'client' => $client,
                'language' => $lang
            ];
            
            $template = $this->selector->selectTemplate(TemplateSelector::TYPE_WELCOME, $context);
            $metadata = $this->selector->getSelectionMetadata();
            
            $this->assertEquals($lang, $metadata['criteria']['language']);
        }
    }
    
    /**
     * Test custom conditions
     */
    public function testCustomConditions()
    {
        $context = [
            'client' => new Client(),
            'custom_conditions' => [
                'is_vip' => true,
                'subscription_type' => 'premium',
                'region' => 'europe'
            ]
        ];
        
        $template = $this->selector->selectTemplate(TemplateSelector::TYPE_INVOICE_NEW, $context);
        $metadata = $this->selector->getSelectionMetadata();
        
        $this->assertArrayHasKey('custom_conditions', $metadata['criteria']);
        $this->assertTrue($metadata['criteria']['custom_conditions']['is_vip']);
    }
}