<?php

/**
 * Get menu configuration with enabled status
 * @return array Menu items with their settings
 */
function getMenuConfig() {
    static $menuConfig = null;
    
    if ($menuConfig === null) {
        // Load base menu configuration
        $menuConfig = require __DIR__ . '/../config/menu.php';
        
        // Check if we have custom menu settings in database
        $db = Flight::db();
        if ($db) {
            try {
                $stmt = $db->prepare("SELECT menu_id, enabled FROM menu_settings WHERE company_id = ?");
                $companyId = $_SESSION['company_id'] ?? 1;
                $stmt->execute([$companyId]);
                $customSettings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                
                // Apply custom settings
                foreach ($menuConfig as &$item) {
                    if (isset($customSettings[$item['id']])) {
                        $item['enabled'] = (bool)$customSettings[$item['id']];
                    }
                    
                    // Apply to submenu items
                    if (isset($item['submenu'])) {
                        foreach ($item['submenu'] as &$subitem) {
                            if (isset($customSettings[$subitem['id']])) {
                                $subitem['enabled'] = (bool)$customSettings[$subitem['id']];
                            }
                        }
                    }
                }
            } catch (PDOException $e) {
                // Table might not exist yet, use default config
                error_log("Menu settings table not found: " . $e->getMessage());
            }
        }
    }
    
    return $menuConfig;
}

/**
 * Save menu configuration
 * @param array $settings Array of menu_id => enabled status
 * @return bool Success status
 */
function saveMenuConfig($settings) {
    $db = Flight::db();
    $companyId = $_SESSION['company_id'] ?? 1;
    
    try {
        // Create table if not exists
        $db->exec("CREATE TABLE IF NOT EXISTS menu_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            company_id INT NOT NULL,
            menu_id VARCHAR(50) NOT NULL,
            enabled TINYINT(1) DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_company_menu (company_id, menu_id)
        )");
        
        // Start transaction
        $db->beginTransaction();
        
        // Delete existing settings for this company
        $stmt = $db->prepare("DELETE FROM menu_settings WHERE company_id = ?");
        $stmt->execute([$companyId]);
        
        // Insert new settings
        $stmt = $db->prepare("INSERT INTO menu_settings (company_id, menu_id, enabled) VALUES (?, ?, ?)");
        foreach ($settings as $menuId => $enabled) {
            $stmt->execute([$companyId, $menuId, $enabled ? 1 : 0]);
        }
        
        $db->commit();
        return true;
        
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Error saving menu settings: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if user has permission for menu item
 * @param array $menuItem Menu item configuration
 * @return bool Whether user has permission
 */
function hasMenuPermission($menuItem) {
    // If no permission required, allow access
    if (!isset($menuItem['permission'])) {
        return true;
    }
    
    // Check if admin only
    if (isset($menuItem['admin_only']) && $menuItem['admin_only']) {
        return isset($_SESSION['user']['is_admin']) && $_SESSION['user']['is_admin'];
    }
    
    // For now, allow all authenticated users
    // TODO: Implement proper permission system
    return isset($_SESSION['user']);
}