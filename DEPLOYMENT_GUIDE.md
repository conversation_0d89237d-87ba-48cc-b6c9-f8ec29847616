# 🚀 Fit360 AdminDesk - Production Deployment Guide

## 📋 **System Requirements**

### **Server Requirements**
- **PHP:** 8.1+ (Tested with PHP 8.3.6)
- **Database:** MySQL 5.7+ or MariaDB 10.3+
- **Web Server:** Apache 2.4+ or Nginx 1.18+
- **Memory:** Minimum 512MB RAM (1GB+ recommended)
- **Storage:** 500MB+ free space

### **PHP Extensions Required**
- `mysqli` or `pdo_mysql`
- `mbstring`
- `json`
- `openssl`
- `curl`
- `gd` (for PDF generation)
- `zip`
- `xml`

---

## 🛠️ **Installation Steps**

### **1. Clone Repository**
```bash
git clone https://github.com/InfoALR/AdminDesk.git
cd AdminDesk
```

### **2. Install Dependencies**
```bash
composer install --no-dev --optimize-autoloader
```

### **3. Environment Configuration**
```bash
cp .env.example .env
```

Edit `.env` file with your production settings:
```env
APP_NAME="Fit360 AdminDesk"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com
APP_TIMEZONE=Europe/Luxembourg

DB_CONNECTION=mysql
DB_HOST=your_db_host
DB_PORT=3306
DB_DATABASE=fitapp_production
DB_USERNAME=your_db_user
DB_PASSWORD=your_secure_password

JWT_SECRET=your_32_character_secret_key
CSRF_PROTECTION=true
```

### **4. Database Setup**
```bash
# Create database
mysql -u root -p -e "CREATE DATABASE fitapp_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# Run migrations
php database/migrate.php
```

### **5. Web Server Configuration**

#### **Apache (.htaccess)**
```apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [QSA,L]
```

#### **Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/AdminDesk/public;
    index index.php;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
}
```

---

## 🔒 **Security Configuration**

### **File Permissions**
```bash
# Set proper permissions
chmod -R 755 /path/to/AdminDesk
chmod -R 777 storage/
chmod -R 777 cache/
chmod 600 .env
```

### **Security Headers (Apache)**
```apache
# Add to .htaccess
Header always set X-Content-Type-Options nosniff
Header always set X-Frame-Options DENY
Header always set X-XSS-Protection "1; mode=block"
Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
```

### **Database Security**
- Create dedicated database user with minimal privileges
- Use strong passwords
- Enable SSL connections if possible
- Regular database backups

---

## 📊 **Production Checklist**

### **✅ Pre-Deployment**
- [ ] Environment variables configured
- [ ] Database created and migrated
- [ ] File permissions set correctly
- [ ] SSL certificate installed
- [ ] Backup strategy implemented
- [ ] Monitoring configured

### **✅ Post-Deployment**
- [ ] Test all core functionality
- [ ] Verify PDF generation works
- [ ] Check email configuration
- [ ] Test invoice creation and printing
- [ ] Verify catalog and stock management
- [ ] Run test suite: `php tests/run-all-tests.php`

---

## 🧪 **Testing in Production**

### **Run Test Suite**
```bash
# Run all tests
php tests/run-all-tests.php

# Run specific phase tests
php public/test-runner.php?phase=4
```

### **Expected Results**
- **Phase 4 Tests:** 30/30 passing ✅
- **All core functionality:** Working ✅
- **PDF generation:** Functional ✅
- **Database operations:** Successful ✅

---

## 📈 **Performance Optimization**

### **PHP Configuration**
```ini
; php.ini optimizations
memory_limit = 256M
max_execution_time = 60
upload_max_filesize = 10M
post_max_size = 10M
opcache.enable = 1
opcache.memory_consumption = 128
```

### **Database Optimization**
- Enable query cache
- Optimize MySQL configuration
- Regular ANALYZE TABLE operations
- Monitor slow query log

---

## 🔧 **Maintenance**

### **Regular Tasks**
- **Daily:** Monitor error logs
- **Weekly:** Database backups
- **Monthly:** Security updates
- **Quarterly:** Performance review

### **Backup Strategy**
```bash
# Database backup
mysqldump -u user -p fitapp_production > backup_$(date +%Y%m%d).sql

# File backup
tar -czf files_backup_$(date +%Y%m%d).tar.gz /path/to/AdminDesk
```

---

## 🆘 **Troubleshooting**

### **Common Issues**

#### **Database Connection Failed**
- Check database credentials in `.env`
- Verify database server is running
- Test connection: `mysql -h host -u user -p database`

#### **PDF Generation Issues**
- Verify GD extension is installed
- Check file permissions on storage directory
- Ensure TCPDF library is properly installed

#### **Email Not Sending**
- Verify SMTP settings in `.env`
- Check firewall rules for SMTP ports
- Test with mail testing tools

### **Log Files**
- **Application logs:** `storage/logs/`
- **Web server logs:** `/var/log/apache2/` or `/var/log/nginx/`
- **PHP logs:** Check `php.ini` for log location

---

## 📞 **Support**

### **Documentation**
- `PROJECT_SUMMARY.md` - Complete feature overview
- `PHASE_4_COMPLETION.md` - Latest achievements
- `tasks/` directory - Detailed implementation guides

### **System Status**
- All Phase 4 features: ✅ Complete
- Test coverage: ✅ 30/30 passing
- Production ready: ✅ Verified

---

**🎉 Your Fit360 AdminDesk system is ready for production deployment!**
