{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.create_invoice') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.create_invoice') }}</h1>
        <a href="{{ base_url }}/invoices" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
        </a>
    </div>

    <!-- Action Buttons -->
    <div class="card shadow-sm mb-4">
        <div class="card-body py-2">
            <div class="d-flex gap-2 justify-content-end">
                <button type="submit" form="invoiceForm" name="action" value="save" class="btn btn-primary">
                    <i class="bi bi-save me-2"></i>{{ __('common.save_draft') }}
                </button>
                <button type="submit" form="invoiceForm" name="action" value="save_and_send" class="btn btn-success">
                    <i class="bi bi-send me-2"></i>{{ __('invoices.save_and_send') }}
                </button>
                <a href="{{ base_url }}/invoices" class="btn btn-secondary">
                    <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                </a>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ base_url }}/invoices" id="invoiceForm" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        
        <!-- Invoice Details -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>{{ __('invoices.invoice_details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="document_type_id" class="form-label">{{ __('invoices.document_type') }} *</label>
                        <select class="form-select" id="document_type_id" name="document_type_id" required>
                            <option value="">{{ __('common.select') }}</option>
                            {% for dtype in documentTypes %}
                                <option value="{{ dtype.id }}" 
                                        data-prefix="{{ dtype.prefix }}" 
                                        data-negative="{{ dtype.is_negative }}" 
                                        data-requires-ref="{{ dtype.requires_reference }}">
                                    <i class="{{ dtype.icon }} me-1" style="color: {{ dtype.color }}"></i>
                                    {{ dtype.name }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="invoice_type_id" class="form-label">{{ __('invoices.invoice_category') }}</label>
                        <select class="form-select" id="invoice_type_id" name="invoice_type_id">
                            <option value="">{{ __('common.select') }}</option>
                            {% for type in invoiceTypes %}
                                <option value="{{ type.id }}" data-prefix="{{ type.prefix }}">
                                    {{ type.display_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="invoice_number" class="form-label">{{ __('invoices.invoice_number') }} *</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="invoice_number" name="invoice_number" required readonly>
                            <button class="btn btn-outline-secondary" type="button" id="generateNumberBtn">
                                <i class="bi bi-arrow-clockwise"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="issue_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                        <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ 'now'|date('Y-m-d') }}" required>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="billable_type" class="form-label">{{ __('invoices.bill_to') }} *</label>
                        <select class="form-select" id="billable_type" name="billable_type" required>
                            <option value="">{{ __('common.select') }}</option>
                            <option value="client">{{ __('clients.client') }}</option>
                            <option value="user">{{ __('users.user') }} ({{ __('invoices.internal') }})</option>
                            <option value="patient">{{ __('patients.patient') }}</option>
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="billable_id" class="form-label">{{ __('common.select') }} <span id="billable_label">{{ __('clients.client') }}</span> *</label>
                        <div class="input-group">
                            <select class="form-select" id="billable_id" name="billable_id" required disabled>
                                <option value="">{{ __('common.select_type_first') }}</option>
                            </select>
                            <button class="btn btn-success" type="button" id="addNewBillableBtn" disabled>
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                        <small class="text-muted" id="billable_hint">{{ __('invoices.cant_find_client_add_new') }}</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>{{ __('invoices.invoice_items') }}</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-light me-2" id="searchProductBtn">
                        <i class="bi bi-search me-1"></i>{{ __('invoices.search_product')|default('Search Product') }}
                    </button>
                    <button type="button" class="btn btn-sm btn-light" id="addItemBtn">
                        <i class="bi bi-plus-circle me-1"></i>{{ __('invoices.add_item')|default('Add Item') }}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="itemsTable">
                        <thead>
                            <tr>
                                <th width="40%">{{ __('invoices.description') }}</th>
                                <th width="15%">{{ __('invoices.quantity') }}</th>
                                <th width="15%">{{ __('invoices.unit_price') }}</th>
                                <th width="15%">{{ __('invoices.vat_rate') }}</th>
                                <th width="15%">{{ __('invoices.total') }}</th>
                                <th width="50"></th>
                            </tr>
                        </thead>
                        <tbody id="itemsBody">
                            <!-- Items will be added dynamically -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="subtotal">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="vatAmount">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                            <tr id="cnsAmountRow" style="display: none;">
                                <td colspan="4" class="text-end">
                                    <strong>{{ __('invoices.cns_base_amount') | default('CNS/Patient Amount') }}:</strong>
                                </td>
                                <td colspan="2">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">{{ currency }}</span>
                                        <input type="number" class="form-control form-control-sm" id="cns_base_amount" 
                                               name="cns_base_amount" min="0" step="0.01" value="0.00">
                                    </div>
                                </td>
                            </tr>
                            <tr id="secretaryFeeRow" style="display: none;">
                                <td colspan="4" class="text-end">
                                    <strong>{{ __('invoices.secretary_fee') | default('Secretary Fee') }} (<span id="secretaryPercent">{{ secretary_percent|default(10) }}</span>%):</strong>
                                </td>
                                <td colspan="2">
                                    <strong id="secretaryFeeAmount">{{ currency }}0.00</strong>
                                    <input type="hidden" id="secretary_fee_amount" name="secretary_fee_amount" value="0.00">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.total') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="total" class="text-primary">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('invoices.additional_information') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-12">
                        <label for="notes" class="form-label">{{ __('invoices.notes') }}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                 placeholder="{{ __('invoices.notes_placeholder') }}"></textarea>
                    </div>
                    
                    <div class="col-md-12">
                        <label for="internal_notes" class="form-label">{{ __('invoices.internal_notes') }}</label>
                        <textarea class="form-control" id="internal_notes" name="internal_notes" rows="2" 
                                 placeholder="{{ __('invoices.internal_notes_placeholder') }}"></textarea>
                        <small class="text-muted">{{ __('invoices.internal_notes_hint') }}</small>
                    </div>
                    
                    <!-- Period fields for retrocession invoices -->
                    <div class="col-md-6" id="period-month-field" style="display: none;">
                        <label for="period_month" class="form-label">{{ __('invoices.period_month') }}</label>
                        <select class="form-select" id="period_month" name="period_month">
                            <option value="">{{ __('common.select') }}</option>
                            <option value="1">{{ __('months.january') }}</option>
                            <option value="2">{{ __('months.february') }}</option>
                            <option value="3">{{ __('months.march') }}</option>
                            <option value="4">{{ __('months.april') }}</option>
                            <option value="5">{{ __('months.may') }}</option>
                            <option value="6">{{ __('months.june') }}</option>
                            <option value="7">{{ __('months.july') }}</option>
                            <option value="8">{{ __('months.august') }}</option>
                            <option value="9">{{ __('months.september') }}</option>
                            <option value="10">{{ __('months.october') }}</option>
                            <option value="11">{{ __('months.november') }}</option>
                            <option value="12">{{ __('months.december') }}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-6" id="period-year-field" style="display: none;">
                        <label for="period_year" class="form-label">{{ __('invoices.period_year') }}</label>
                        <select class="form-select" id="period_year" name="period_year">
                            <option value="">{{ __('common.select') }}</option>
                            {% for year in range(2020, 'now'|date('Y')|number_format(0, '', '')|int + 2) %}
                                <option value="{{ year }}"{% if year == 'now'|date('Y') %} selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
                </div>

        <!-- Payment Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>{{ __('invoices.payment_terms') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="payment_term_id" class="form-label">{{ __('invoices.payment_terms') }}</label>
                        <select class="form-select" id="payment_term_id" name="payment_term_id">
                            <option value="">{{ __('common.select') }}</option>
                            {% for term in paymentTerms %}
                                <option value="{{ term.id }}" data-days="{{ term.days }}" 
                                        {% if term.id == defaultPaymentTermId %}selected{% endif %}>
                                    {{ term.display_name }}
                                    {% if term.display_description %}
                                        ({{ term.display_description }})
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">{{ __('config.payment_term_description_hint') }}</small>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="due_date" class="form-label">{{ __('invoices.due_date') }}</label>
                        <input type="date" class="form-control" id="due_date" name="due_date" value="{{ defaultDueDate }}">
                        <small class="text-muted">{{ __('invoices.leave_empty_immediate') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Item Template -->
<template id="itemTemplate">
    <tr class="invoice-item">
        <td>
            <input type="text" class="form-control form-control-sm item-description" name="items[INDEX][description]" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-quantity" name="items[INDEX][quantity]" value="1" min="1" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-price" name="items[INDEX][unit_price]" min="0" step="0.01" required>
        </td>
        <td>
            <select class="form-select form-select-sm item-vat" name="items[INDEX][vat_rate_id]" required>
                <!-- VAT options will be loaded dynamically -->
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm item-total" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger remove-item">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    </tr>
</template>

<script>
console.log('=== Invoice Create Script Started ===');

let itemIndex = 0;
const currency = '{{ currency }}';
let vatRates = [];
const secretaryPercent = {{ secretary_percent|default(10) }};

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Setting up invoice form...');
    
    // Get elements first
    const docTypeElement = document.getElementById('document_type_id');
    const genNumberBtn = document.getElementById('generateNumberBtn');
    const billableTypeElement = document.getElementById('billable_type');
    
    // Load VAT rates and columns, then add first item
    Promise.all([
        loadVatRates(),
        updateInvoiceItemColumns() // This now returns a Promise
    ]).then(() => {
        // Add first item after VAT rates and columns are loaded
        addItem();
    }).catch(error => {
        console.error('Error during initialization:', error);
        // Still try to add an item with default config
        addItem();
    });
    
    // Generate number button
    if (genNumberBtn) {
        genNumberBtn.addEventListener('click', generateInvoiceNumber);
    }
    
    // Generate number on page load if document type is selected
    if (docTypeElement && docTypeElement.value) {
        generateInvoiceNumber();
    }
    
    // Handle billable type change - CRITICAL EVENT
    if (billableTypeElement) {
        console.log('Attaching billable type change listener...');
        billableTypeElement.addEventListener('change', function(e) {
            console.log('*** BILLABLE TYPE CHANGED TO:', e.target.value, '***');
            loadBillableOptions();
        });
        console.log('Billable type change listener attached successfully');
    } else {
        console.error('ERROR: billable_type element not found!');
    }
    
    // Handle payment term change
    document.getElementById('payment_term_id').addEventListener('change', updateDueDate);
    
    // Handle issue date change
    document.getElementById('issue_date').addEventListener('change', updateDueDate);
    
    // Handle invoice type change to show/hide CNS fields
    document.getElementById('invoice_type_id').addEventListener('change', function() {
        handleInvoiceTypeChange();
        updateInvoiceItemColumns(); // Update columns when invoice type changes
    });
    
    // Handle document type change to update columns
    if (docTypeElement) {
        docTypeElement.addEventListener('change', function() {
            generateInvoiceNumber();
            updateInvoiceItemColumns(); // Update columns when document type changes
        });
        console.log('Document type change listener attached');
    }
    
    // Handle CNS amount input (with null check as element may not exist for all invoice types)
    const cnsAmountElement = document.getElementById('cns_base_amount');
    if (cnsAmountElement) {
        cnsAmountElement.addEventListener('input', calculateSecretaryFee);
    }
    
    // Add item button
    document.getElementById('addItemBtn').addEventListener('click', addItem);
    
    // Apply voucher - commented out as voucher section was removed
    // document.getElementById('applyVoucherBtn').addEventListener('click', applyVoucher);
    
    // Add new billable button
    document.getElementById('addNewBillableBtn').addEventListener('click', function() {
        const type = document.getElementById('billable_type').value;
        if (type === 'client') {
            window.open('{{ base_url }}/clients/create?quick=1', 'newClient', 'width=800,height=600');
        } else if (type === 'patient') {
            window.open('{{ base_url }}/patients/create?quick=1', 'newPatient', 'width=800,height=600');
        }
    });
    
    // Form validation with debugging
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            console.log('Form submission attempted');
            
            // Check if we have at least one item
            const items = document.querySelectorAll('.invoice-item');
            if (items.length === 0) {
                event.preventDefault();
                event.stopPropagation();
                alert('Please add at least one invoice item');
                return;
            }
            
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
                console.log('Form validation failed');
                
                // Show which fields are invalid
                const invalidFields = form.querySelectorAll(':invalid');
                invalidFields.forEach(field => {
                    console.log('Invalid field:', field.name || field.id);
                });
            } else {
                console.log('Form validation passed, submitting...');
            }
            form.classList.add('was-validated');
        });
    });
});

// Load VAT rates from server
async function loadVatRates() {
    try {
        const response = await fetch('{{ base_url }}/api/vat-rates');
        const data = await response.json();
        if (data.success) {
            vatRates = data.rates;
        } else {
            // Fallback VAT rates if API fails
            vatRates = [
                {id: 1, name: 'Standard', rate: 17, is_default: true},
                {id: 2, name: 'Reduced', rate: 8, is_default: false},
                {id: 3, name: 'Super Reduced', rate: 3, is_default: false},
                {id: 4, name: 'Parking', rate: 14, is_default: false},
                {id: 5, name: 'Exempt', rate: 0, is_default: false}
            ];
        }
    } catch (error) {
        console.error('Error loading VAT rates:', error);
        // Use fallback rates
        vatRates = [
            {id: 1, name: 'Standard', rate: 17, is_default: true},
            {id: 2, name: 'Reduced', rate: 8, is_default: false},
            {id: 3, name: 'Super Reduced', rate: 3, is_default: false},
            {id: 4, name: 'Parking', rate: 14, is_default: false},
            {id: 5, name: 'Exempt', rate: 0, is_default: false}
        ];
    }
}

// Original addItem function removed - now using addItemWithData through window.addItem

function removeItem(row) {
    if (document.querySelectorAll('.invoice-item').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('{{ __("invoices.at_least_one_item") }}');
    }
}

// Original calculateItemTotal function removed - now using updated version that handles discount and subtotal

function calculateTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    document.querySelectorAll('.invoice-item').forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
        const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
        const discount = parseFloat(row.querySelector('.item-discount')?.value) || 0;
        const vatSelect = row.querySelector('.item-vat');
        const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
        
        const itemSubtotal = quantity * price;
        const discountAmount = itemSubtotal * (discount / 100);
        const discountedSubtotal = itemSubtotal - discountAmount;
        const itemVat = discountedSubtotal * (vatRate / 100);
        
        subtotal += discountedSubtotal;
        vatAmount += itemVat;
    });
    
    // Include secretary fee if applicable
    const secretaryFee = parseFloat(document.getElementById('secretary_fee_amount').value) || 0;
    const total = subtotal + vatAmount + secretaryFee;
    
    document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
    document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
    document.getElementById('total').textContent = currency + total.toFixed(2);
}

function handleInvoiceTypeChange() {
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    
    // Show CNS fields for retrocession invoices
    const showCnsFields = typeName.includes('rétrocession') || typeName.includes('retrocession');
    
    document.getElementById('cnsAmountRow').style.display = showCnsFields ? '' : 'none';
    document.getElementById('secretaryFeeRow').style.display = showCnsFields ? '' : 'none';
    
    // Show period fields for retrocession invoices
    const periodMonthField = document.getElementById('period-month-field');
    const periodYearField = document.getElementById('period-year-field');
    
    if (periodMonthField && periodYearField) {
        periodMonthField.style.display = showCnsFields ? '' : 'none';
        periodYearField.style.display = showCnsFields ? '' : 'none';
        
        if (showCnsFields) {
            // Set current month and year as defaults
            const now = new Date();
            const currentMonth = now.getMonth() + 1; // JavaScript months are 0-indexed
            const currentYear = now.getFullYear();
            
            document.getElementById('period_month').value = currentMonth;
            document.getElementById('period_year').value = currentYear;
        }
    }
    
    if (!showCnsFields) {
        // Reset CNS values if not a retrocession invoice
        const cnsElement = document.getElementById('cns_base_amount');
        if (cnsElement) {
            cnsElement.value = '0.00';
        }
        document.getElementById('secretary_fee_amount').value = '0.00';
        document.getElementById('secretaryFeeAmount').textContent = currency + '0.00';
        
        // Reset period fields
        if (periodMonthField && periodYearField) {
            document.getElementById('period_month').value = '';
            document.getElementById('period_year').value = '';
        }
        
        calculateTotals();
    }
}

function calculateSecretaryFee() {
    const cnsAmountEl = document.getElementById('cns_base_amount');
    const cnsAmount = cnsAmountEl ? parseFloat(cnsAmountEl.value) || 0 : 0;
    const secretaryFee = (cnsAmount * secretaryPercent) / 100;
    
    document.getElementById('secretary_fee_amount').value = secretaryFee.toFixed(2);
    document.getElementById('secretaryFeeAmount').textContent = currency + secretaryFee.toFixed(2);
    
    calculateTotals();
}

function generateInvoiceNumber() {
    const docTypeId = document.getElementById('document_type_id').value;
    if (!docTypeId) {
        console.log('No document type selected');
        return;
    }
    
    // AJAX call to generate number based on document type
    fetch('{{ base_url }}/invoices/generate-number?document_type_id=' + docTypeId)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('invoice_number').value = data.number;
            } else {
                console.error('Failed to generate number:', data.message);
            }
        })
        .catch(error => {
            console.error('Error generating invoice number:', error);
        });
}

// Debug: Script execution checkpoint
console.log('Invoice create script - Loading client/user data...');

// Store clients and users data safely with error handling
let clientsData = [];
let usersData = [];

try {
    clientsData = {{ clients|default([])|json_encode|raw }};
    console.log('Clients data loaded successfully:', clientsData.length, 'clients');
    if (clientsData.length > 0) {
        console.log('First client:', clientsData[0]);
    }
} catch (error) {
    console.error('Error loading clients data:', error);
    console.log('Clients variable exists:', {{ clients is defined ? 'true' : 'false' }});
}

try {
    usersData = {{ users|default([])|json_encode|raw }};
    console.log('Users data loaded successfully:', usersData.length, 'users');
    if (usersData.length > 0) {
        console.log('First user:', usersData[0]);
    }
} catch (error) {
    console.error('Error loading users data:', error);
    console.log('Users variable exists:', {{ users is defined ? 'true' : 'false' }});
}

function loadBillableOptions() {
    const type = document.getElementById('billable_type').value;
    const select = document.getElementById('billable_id');
    const label = document.getElementById('billable_label');
    const hint = document.getElementById('billable_hint');
    const addBtn = document.getElementById('addNewBillableBtn');
    
    console.log('loadBillableOptions called with type:', type);
    
    if (!type) {
        select.disabled = true;
        addBtn.disabled = true;
        select.innerHTML = '<option value="">{{ __("common.select_type_first") }}</option>';
        return;
    }
    
    select.disabled = false;
    
    // Clear the select options
    select.innerHTML = '<option value="">{{ __("common.select") }}</option>';
    
    try {
        // Handle different types
        if (type === 'client') {
            console.log('Loading clients, count:', clientsData.length);
            label.textContent = '{{ __("clients.client") }}';
            hint.textContent = '{{ __("invoices.cant_find_client_add_new") }}';
            addBtn.disabled = false;
            
            // Add clients to dropdown using safe JSON data
            clientsData.forEach(client => {
                const option = new Option(`${client.name} (${client.client_number})`, `client_${client.id}`);
                select.add(option);
            });
            console.log('Clients loaded into dropdown, options count:', select.options.length);
            
        } else if (type === 'user') {
            label.textContent = '{{ __("users.user") }}';
            hint.textContent = '{{ __("invoices.internal_invoice_hint") }}';
            addBtn.disabled = true;
            
            // Add users to dropdown using safe JSON data
            usersData.forEach(user => {
                const option = new Option(`${user.name} (${user.username})`, `user_${user.id}`);
                select.add(option);
            });
            
        } else if (type === 'patient') {
            label.textContent = '{{ __("patients.patient") }}';
            hint.textContent = '{{ __("invoices.patient_invoice_hint") }}';
            addBtn.disabled = false;
            
            // Load patients via AJAX
            fetch('{{ base_url }}/invoices/search-billable?type=patient')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (Array.isArray(data)) {
                        data.forEach(item => {
                            const option = new Option(item.name, 'patient_' + item.id);
                            select.add(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading patients:', error);
                    alert('{{ __("common.error_loading_data") | default("Error loading data") }}');
                });
        }
    } catch (error) {
        console.error('Error in loadBillableOptions:', error);
        alert('{{ __("common.error_loading_data") | default("Error loading data") }}: ' + error.message);
    }
}

// applyVoucher function removed - voucher section no longer exists in the form

function updateDueDate() {
    const paymentTermSelect = document.getElementById('payment_term_id');
    const issueDate = document.getElementById('issue_date').value;
    const dueDateInput = document.getElementById('due_date');
    
    if (!paymentTermSelect.value || !issueDate) {
        return;
    }
    
    const selectedOption = paymentTermSelect.options[paymentTermSelect.selectedIndex];
    const days = parseInt(selectedOption.getAttribute('data-days')) || 0;
    
    // Calculate due date
    const date = new Date(issueDate);
    date.setDate(date.getDate() + days);
    
    // Format as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}`;
}

// Store current column configuration
let currentColumnConfig = [];

// Update invoice item columns based on document type and invoice type
function updateInvoiceItemColumns() {
    const documentTypeId = document.getElementById('document_type_id')?.value || '';
    const invoiceTypeId = document.getElementById('invoice_type_id')?.value || '';
    
    console.log('Updating invoice item columns for docType:', documentTypeId, 'invoiceType:', invoiceTypeId);
    
    // Build URL with parameters
    let url = '{{ base_url }}/api/column-config/invoice_items';
    const params = new URLSearchParams();
    if (documentTypeId) params.append('documentTypeId', documentTypeId);
    if (invoiceTypeId) params.append('invoiceTypeId', invoiceTypeId);
    if (params.toString()) url += '?' + params.toString();
    
    return fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentColumnConfig = data.columns;
                console.log('Column configuration loaded:', currentColumnConfig);
                rebuildInvoiceItemsTable();
            } else {
                console.error('Failed to load column config:', data.message);
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error loading column config:', error);
            // Use default configuration
            currentColumnConfig = [
                {id: 'description', name: '{{ __("common.description") }}', visible: true, required: true},
                {id: 'quantity', name: '{{ __("common.quantity") }}', visible: true},
                {id: 'unit_price', name: '{{ __("invoices.unit_price") }}', visible: true},
                {id: 'vat_rate', name: '{{ __("invoices.vat_rate") }}', visible: true},
                {id: 'total', name: '{{ __("common.total") }}', visible: true, required: true}
            ];
            rebuildInvoiceItemsTable();
        });
}

// Rebuild the invoice items table based on column configuration
function rebuildInvoiceItemsTable() {
    const thead = document.querySelector('#itemsTable thead tr');
    const tbody = document.getElementById('itemsBody');
    
    if (!thead || !currentColumnConfig.length) return;
    
    // Save existing items data
    const existingItems = [];
    document.querySelectorAll('.invoice-item').forEach(row => {
        const item = {
            description: row.querySelector('[name*="[description]"]')?.value || '',
            reference: row.querySelector('[name*="[reference]"]')?.value || '',
            quantity: row.querySelector('[name*="[quantity]"]')?.value || '1',
            unit: row.querySelector('[name*="[unit]"]')?.value || '',
            unit_price: row.querySelector('[name*="[unit_price]"]')?.value || '',
            discount: row.querySelector('[name*="[discount]"]')?.value || '0',
            vat_rate_id: row.querySelector('[name*="[vat_rate_id]"]')?.value || ''
        };
        existingItems.push(item);
    });
    
    // Clear and rebuild table headers
    thead.innerHTML = '';
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const th = document.createElement('th');
        th.textContent = col.name;
        
        // Set width based on column type
        switch(col.id) {
            case 'description':
                th.style.width = '40%';
                break;
            case 'reference':
                th.style.width = '15%';
                break;
            case 'quantity':
            case 'unit':
            case 'unit_price':
            case 'discount':
            case 'vat_rate':
            case 'subtotal':
                th.style.width = '10%';
                break;
            case 'total':
                th.style.width = '15%';
                break;
        }
        
        thead.appendChild(th);
    });
    
    // Add actions column
    const actionsHeader = document.createElement('th');
    actionsHeader.style.width = '50px';
    thead.appendChild(actionsHeader);
    
    // Clear tbody and re-add items with new structure
    tbody.innerHTML = '';
    itemIndex = 0;
    
    // Re-add existing items
    existingItems.forEach(itemData => {
        addItemWithData(itemData);
    });
    
    // If no items, add one empty item
    if (existingItems.length === 0) {
        addItem();
    }
}

// Modified addItem function to use column configuration
function addItemWithData(itemData = {}) {
    const tbody = document.getElementById('itemsBody');
    const row = document.createElement('tr');
    row.className = 'invoice-item';
    
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const td = document.createElement('td');
        
        switch(col.id) {
            case 'description':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-description" name="items[${itemIndex}][description]" value="${itemData.description || ''}" required>`;
                break;
                
            case 'reference':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-reference" name="items[${itemIndex}][reference]" value="${itemData.reference || ''}">`;
                break;
                
            case 'quantity':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-quantity" name="items[${itemIndex}][quantity]" value="${itemData.quantity || '1'}" min="0.01" step="0.01" required>`;
                break;
                
            case 'unit':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-unit" name="items[${itemIndex}][unit]" value="${itemData.unit || ''}">`;
                break;
                
            case 'unit_price':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${itemIndex}][unit_price]" value="${itemData.unit_price || ''}" min="0" step="0.01" required>`;
                break;
                
            case 'discount':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-discount" name="items[${itemIndex}][discount]" value="${itemData.discount || '0'}" min="0" max="100" step="0.01">`;
                break;
                
            case 'vat_rate':
                const vatSelect = document.createElement('select');
                vatSelect.className = 'form-select form-select-sm item-vat';
                vatSelect.name = `items[${itemIndex}][vat_rate_id]`;
                vatSelect.required = true;
                
                // Add VAT options
                vatRates.forEach(vat => {
                    const option = document.createElement('option');
                    option.value = vat.id;
                    option.setAttribute('data-rate', vat.rate);
                    option.textContent = `${vat.name} (${vat.rate}%)`;
                    if (itemData.vat_rate_id == vat.id || (!itemData.vat_rate_id && vat.is_default)) {
                        option.selected = true;
                    }
                    vatSelect.appendChild(option);
                });
                
                td.appendChild(vatSelect);
                break;
                
            case 'subtotal':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-subtotal" readonly>`;
                break;
                
            case 'total':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
                break;
        }
        
        row.appendChild(td);
    });
    
    // Add actions column
    const actionsTd = document.createElement('td');
    actionsTd.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item"><i class="bi bi-trash"></i></button>`;
    row.appendChild(actionsTd);
    
    // Add event listeners
    const quantityInput = row.querySelector('.item-quantity');
    const priceInput = row.querySelector('.item-price');
    const discountInput = row.querySelector('.item-discount');
    const vatSelect = row.querySelector('.item-vat');
    const removeBtn = row.querySelector('.remove-item');
    
    if (quantityInput) quantityInput.addEventListener('input', () => calculateItemTotal(row));
    if (priceInput) priceInput.addEventListener('input', () => calculateItemTotal(row));
    if (discountInput) discountInput.addEventListener('input', () => calculateItemTotal(row));
    if (vatSelect) vatSelect.addEventListener('change', () => calculateItemTotal(row));
    if (removeBtn) removeBtn.addEventListener('click', () => removeItem(row));
    
    tbody.appendChild(row);
    itemIndex++;
    
    // Calculate initial total for this row
    calculateItemTotal(row);
}

// Update the original addItem function to use the new one
window.addItem = function() {
    // Check if columns are loaded
    if (!currentColumnConfig || currentColumnConfig.length === 0) {
        // Use default columns if not loaded yet
        currentColumnConfig = [
            {id: 'description', name: '{{ __("common.description") }}', visible: true, required: true},
            {id: 'quantity', name: '{{ __("common.quantity") }}', visible: true},
            {id: 'unit_price', name: '{{ __("invoices.unit_price") }}', visible: true},
            {id: 'vat_rate', name: '{{ __("invoices.vat_rate") }}', visible: true},
            {id: 'total', name: '{{ __("common.total") }}', visible: true, required: true}
        ];
    }
    addItemWithData();
};

// Update calculateItemTotal to handle discount and subtotal columns
function calculateItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
    const discount = parseFloat(row.querySelector('.item-discount')?.value) || 0;
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
    
    const subtotal = quantity * price;
    const discountAmount = subtotal * (discount / 100);
    const discountedSubtotal = subtotal - discountAmount;
    const vat = discountedSubtotal * (vatRate / 100);
    const total = discountedSubtotal + vat;
    
    // Update subtotal field if visible
    const subtotalInput = row.querySelector('.item-subtotal');
    if (subtotalInput) {
        subtotalInput.value = currency + discountedSubtotal.toFixed(2);
    }
    
    // Update total field
    const totalInput = row.querySelector('.item-total');
    if (totalInput) {
        totalInput.value = currency + total.toFixed(2);
    }
    
    calculateTotals();
}

// Set base_url for JavaScript
window.base_url = '{{ base_url }}';

// Callback function to refresh billable options after creating new client/patient in popup
window.refreshBillableOptions = function() {
    loadBillableOptions();
};
</script>

<!-- Include fix scripts -->
<script src="{{ base_url }}/js/invoice-create-fix.js"></script>
<!-- Commented out as it overrides the main loadBillableOptions function -->
<!-- <script src="{{ base_url }}/js/invoice-billable-fix.js"></script> -->
<script src="{{ base_url }}/js/invoice-create-complete-fix.js"></script>
{% endblock %}