{# CACHE_BUST_{{ "now"|date("U") }} #}


{# Cache version: {{ "now"|date("YmdHis") }} #}
{% extends "base-modern.twig" %}

{% block title %}{{ __('invoices.create_invoice') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ __('invoices.create_invoice') }}</h1>
        <a href="{{ base_url }}/invoices" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>{{ __('common.back_to_list') }}
        </a>
    </div>

    <!-- Action Buttons -->
    <div class="card shadow-sm mb-4">
        <div class="card-body py-2">
            <div class="d-flex gap-2 justify-content-end">
                <button type="submit" form="invoiceForm" name="action" value="save" class="btn btn-primary">
                    <i class="bi bi-save me-2"></i>{{ __('common.save_draft') }}
                </button>
                <button type="submit" form="invoiceForm" name="action" value="save_and_send" class="btn btn-success">
                    <i class="bi bi-send me-2"></i>{{ __('invoices.save_and_send') }}
                </button>
                <a href="{{ base_url }}/invoices" class="btn btn-secondary">
                    <i class="bi bi-x-circle me-2"></i>{{ __('common.cancel') }}
                </a>
            </div>
        </div>
    </div>

    <form method="POST" action="{{ base_url }}/invoices" id="invoiceForm" class="needs-validation" novalidate>
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}">
        <input type="hidden" name="action" id="formAction" value="save">
        
        <!-- Invoice Details -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-file-text me-2"></i>{{ __('invoices.invoice_details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="document_type_id" class="form-label">{{ __('invoices.document_type') }} *</label>
                        <select class="form-select" id="document_type_id" name="document_type_id" required>
                            <option value="">{{ __('common.select') }}</option>
                            {% for dtype in documentTypes %}
                                <option value="{{ dtype.id }}" 
                                        data-prefix="{{ dtype.prefix }}" 
                                        data-negative="{{ dtype.is_negative }}" 
                                        data-requires-ref="{{ dtype.requires_reference }}"
                                        {% if dtype.code == 'invoice' %}selected{% endif %}>
                                    {{ dtype.name|default(dtype.code|upper) }}
                                </option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="invoice_type_id" class="form-label">{{ __('invoices.invoice_category') }}</label>
                        <select class="form-select" id="invoice_type_id" name="invoice_type_id">
                            <option value="">{{ __('common.select') }}</option>
                            {% for type in invoiceTypes %}
                                <option value="{{ type.id }}" data-prefix="{{ type.prefix }}"
                                        {% if (duplicateData.invoice_type_id is defined and duplicateData.invoice_type_id == type.id) or (duplicateData.invoice_type_id is not defined and defaultInvoiceTypeId == type.id) %}selected{% endif %}>
                                    {{ type.display_name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="template_id" class="form-label">{{ __('invoices.template') }}</label>
                        <select class="form-select" id="template_id" name="template_id">
                            <option value="">{{ __('invoices.no_template')|default('No template') }}</option>
                            {% for template in templates %}
                                <option value="{{ template.id }}">
                                    {{ template.name }}
                                </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">{{ __('invoices.template_hint')|default('Select a template to pre-fill invoice lines') }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="invoice_number" class="form-label">{{ __('invoices.invoice_number') }}</label>
                        <div class="input-group">
                            <input type="text" class="form-control" id="invoice_number" name="invoice_number" 
                                   value="{{ suggestedNumber }}" 
                                   placeholder="{{ __('invoices.enter_invoice_number') }}">
                        </div>
                        <small class="text-muted">{{ __('invoices.invoice_number_editable')|default('You can modify this number if needed') }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="issue_date" class="form-label">{{ __('invoices.issue_date') }} *</label>
                        <input type="date" class="form-control" id="issue_date" name="issue_date" value="{{ 'now'|date('Y-m-d') }}" required>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="subject" class="form-label">{{ __('invoices.subject') }}</label>
                        <input type="text" class="form-control" id="subject" name="subject" placeholder="{{ __('invoices.subject_placeholder')|default('Ex: Loyer + Charges') }}">
                        <small class="text-muted">{{ __('invoices.subject_hint')|default('Objet de la facture') }}</small>
                    </div>
                    
                    <div class="col-md-4">
                        <label for="period" class="form-label">{{ __('invoices.period') }}</label>
                        <input type="text" class="form-control" id="period" name="period" placeholder="{{ __('invoices.period_placeholder')|default('Ex: JUIN 2025') }}">
                        <small class="text-muted">{{ __('invoices.period_hint')|default('Période concernée') }}</small>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="billable_type" class="form-label">{{ __('invoices.bill_to') }} *</label>
                        <select class="form-select" id="billable_type" name="billable_type" required>
                            <option value="">{{ __('common.select') }}</option>
                            <option value="client">{{ __('clients.client') }}</option>
                            <option value="user" selected>{{ __('users.user') }} ({{ __('invoices.internal') }})</option>
                        </select>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="billable_id" class="form-label">{{ __('common.select') }} <span id="billable_label">{{ __('users.user') }}</span> *</label>
                        <div class="input-group">
                            <select class="form-select" id="billable_id" name="billable_id" required>
                                <option value="">{{ __('common.select') }}</option>
                            </select>
                            <button class="btn btn-success" type="button" id="addNewBillableBtn" disabled>
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback">
                            {{ __('validation.required') }}
                        </div>
                        <small class="text-muted" id="billable_hint">{{ __('invoices.internal_invoice_hint') }}</small>
                    </div>

                
                <!-- Exclude Patient Line (for retrocession invoices only) -->
                <div class="row g-3 mt-3" id="exclude_patient_line_container" style="display: none;">
                    <div class="col-md-12">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" id="exclude_patient_line" 
                                   name="exclude_patient_line" value="1">
                            <label class="form-check-label" for="exclude_patient_line">
                                {{ __("retrocession.exclude_patient_line")|default("Exclure la ligne patient") }}
                                <small class="text-muted d-block">{{ __("retrocession.exclude_patient_line_help")|default("Si coché, la facture ne contiendra pas la ligne RÉTROCESSION PATIENTS") }}</small>
                            </label>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- User/Client Information Display -->
        <div class="card shadow-sm mb-4" id="billableInfoCard" style="display: none; opacity: 0; animation: 0.5s ease-out 0.15s forwards fadeIn;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('invoices.billable_information') }}</h5>
            </div>
            <div class="card-body" id="billableInfoContent">
                <!-- Information will be populated via JavaScript -->
            </div>
        </div>

        <!-- Invoice Items -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>{{ __('invoices.invoice_items') }}</h5>
                <div>
                    <button type="button" class="btn btn-sm btn-light me-2" id="searchProductBtn">
                        <i class="bi bi-search me-1"></i>{{ __('invoices.search_product')|default('Search Product') }}
                    </button>
                    <button type="button" class="btn btn-sm btn-light" id="addItemBtn">
                        <i class="bi bi-plus-circle me-1"></i>{{ __('invoices.add_item')|default('Add Item') }}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table" id="itemsTable">
                        <thead>
                            <tr id="itemsTableHeader">
                                <th width="40%">{{ __('invoices.description') }}</th>
                                <th width="15%">{{ __('common.unit') }}</th>
                                <th width="15%">{{ __('invoices.unit_price') }}</th>
                                <th width="15%">{{ __('invoices.vat_rate') }}</th>
                                <th width="15%">{{ __('invoices.total') }}</th>
                                <th width="50"></th>
                            </tr>
                        </thead>
                        <tbody id="itemsBody">
                            <!-- Items will be added dynamically -->
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.subtotal') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="subtotal">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.vat_amount') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="vatAmount">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4" class="text-end"><strong>{{ __('invoices.total') }}:</strong></td>
                                <td colspan="2">
                                    <strong id="total" class="text-primary">{{ currency }}0.00</strong>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>{{ __('invoices.additional_information') }}</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-12">
                        <label for="notes" class="form-label">{{ __('invoices.notes') }}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3" 
                                 placeholder="{{ __('invoices.notes_placeholder') }}"></textarea>
                    </div>
                    
                    <div class="col-md-12">
                        <label for="internal_notes" class="form-label">{{ __('invoices.internal_notes') }}</label>
                        <textarea class="form-control" id="internal_notes" name="internal_notes" rows="2" 
                                 placeholder="{{ __('invoices.internal_notes_placeholder') }}"></textarea>
                        <small class="text-muted">{{ __('invoices.internal_notes_hint') }}</small>
                    </div>
                </div>
            </div>
                </div>

        <!-- Payment Information -->
        <div class="card shadow-sm mb-4">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="bi bi-calendar-check me-2"></i>{{ __('invoices.payment_terms') }}</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <label for="payment_term_id" class="form-label">{{ __('invoices.payment_terms') }}</label>
                        <select class="form-select" id="payment_term_id" name="payment_term_id">
                            <option value="">{{ __('common.select') }}</option>
                            {% for term in paymentTerms %}
                                <option value="{{ term.id }}" data-days="{{ term.days }}" 
                                        {% if term.id == defaultPaymentTermId %}selected{% endif %}>
                                    {{ term.display_name }}
                                    {% if term.display_description %}
                                        ({{ term.display_description }})
                                    {% endif %}
                                </option>
                            {% endfor %}
                        </select>
                        <small class="text-muted">{{ __('config.payment_term_description_hint') }}</small>
                    </div>
                    
                    <div class="col-md-6">
                        <label for="due_date" class="form-label">{{ __('invoices.due_date') }}</label>
                        <input type="date" class="form-control" id="due_date" name="due_date" value="">
                        <small class="text-muted">{{ __('invoices.leave_empty_immediate') }}</small>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Item Template -->
<template id="itemTemplate">
    <tr class="invoice-item">
        <td>
            <input type="hidden" name="items[INDEX][item_id]" class="item-id">
            <input type="text" class="form-control form-control-sm item-description" name="items[INDEX][description]" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-quantity" name="items[INDEX][quantity]" value="1" min="1" step="0.01" required>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm item-price" name="items[INDEX][unit_price]" min="0" step="0.01" required>
        </td>
        <td>
            <select class="form-select form-select-sm item-vat" name="items[INDEX][vat_rate_id]" required>
                <!-- VAT options will be loaded dynamically -->
            </select>
        </td>
        <td>
            <input type="text" class="form-control form-control-sm item-total" readonly>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger remove-item">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    </tr>
</template>

<script>
console.log('=== Invoice Create Script Started ===');

let itemIndex = 0;
const currency = '{{ currency }}';
let vatRates = [];
const secretaryPercent = {{ secretary_percent|default(10) }};

// Check invoice type from URL
const urlParams = new URLSearchParams(window.location.search);
const invoiceType = urlParams.get('type') || 'rental';
console.log('Invoice type from URL:', invoiceType);

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded - Setting up invoice form...');
    
    // Get elements first
    const docTypeElement = document.getElementById('document_type_id');
    const genNumberBtn = document.getElementById('generateNumberBtn');
    const billableTypeElement = document.getElementById('billable_type');
    
    // Check if this is a retrocession invoice FIRST
    const isRetrocession = invoiceType === 'retrocession_30' || invoiceType === 'retrocession_25';
    // Check if this is a location invoice
    const isLocation = invoiceType === 'location';
    
    // Load VAT rates and columns, then add first item
    Promise.all([
        loadVatRates(),
        isRetrocession ? Promise.resolve() : updateInvoiceItemColumns() // Skip column update for retrocession
    ]).then(() => {
        // Check if we have duplicate data
        {% if duplicateData %}
        console.log('Duplicate data found, populating form...');
        
        // Set form fields from duplicate data
        {% if duplicateData.document_type_id %}
        document.getElementById('document_type_id').value = '{{ duplicateData.document_type_id }}';
        {% endif %}
        
        {% if duplicateData.invoice_type_id %}
        document.getElementById('invoice_type_id').value = '{{ duplicateData.invoice_type_id }}';
        {% endif %}
        
        {% if duplicateData.subject %}
        document.getElementById('subject').value = '{{ duplicateData.subject|escape('js') }}';
        {% endif %}
        
        {% if duplicateData.period %}
        document.getElementById('period').value = '{{ duplicateData.period|escape('js') }}';
        {% endif %}
        
        {% if duplicateData.payment_term_id %}
        document.getElementById('payment_term_id').value = '{{ duplicateData.payment_term_id }}';
        {% endif %}
        
        // Set billable type and id
        {% if duplicateData.client_id %}
        document.getElementById('billable_type').value = 'client';
        loadBillableOptions().then(() => {
            document.getElementById('billable_id').value = 'client_{{ duplicateData.client_id }}';
        });
        {% elseif duplicateData.user_id %}
        document.getElementById('billable_type').value = 'user';
        loadBillableOptions().then(() => {
            document.getElementById('billable_id').value = 'user_{{ duplicateData.user_id }}';
        });
        {% endif %}
        
        // Check if this is a retrocession invoice to handle it specially
        {% if duplicateData.invoice_type_id %}
        const duplicateInvoiceTypeId = '{{ duplicateData.invoice_type_id }}';
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        let isRetrocessionDuplicate = false;
        
        // Check if this is a retrocession type
        if (invoiceTypeSelect) {
            for (let option of invoiceTypeSelect.options) {
                if (option.value === duplicateInvoiceTypeId && option.getAttribute('data-prefix') === 'RET') {
                    isRetrocessionDuplicate = true;
                    break;
                }
            }
        }
        
        if (isRetrocessionDuplicate) {
            console.log('Duplicating retrocession invoice - initializing complete retrocession structure');
            
            // Use the complete initialization function
            initializeRetrocessionInvoiceComplete();
            
            // After initialization, populate amounts from duplicate data
            setTimeout(() => {
                
                // If we have CNS and patient amounts from duplicate data, set them
                {% if duplicateData.cns_base_amount or duplicateData.items %}
                setTimeout(() => {
                    // Look for CNS and patient amounts in the duplicate data
                    {% for item in duplicateData.items %}
                    {% if item.description and 'CNS' in item.description %}
                    const cnsInput = document.getElementById('cns_amount');
                    if (cnsInput) {
                        // Calculate base amount from the 20% retrocession
                        const cnsRetrocession = {{ item.unit_price|default(0) }};
                        const cnsBase = cnsRetrocession / 0.20;
                        cnsInput.value = cnsBase.toFixed(2);
                    }
                    {% elseif item.description and 'PATIENTS' in item.description %}
                    const patientInput = document.getElementById('patient_amount');
                    if (patientInput) {
                        // Calculate base amount from the 20% retrocession
                        const patientRetrocession = {{ item.unit_price|default(0) }};
                        const patientBase = patientRetrocession / 0.20;
                        patientInput.value = patientBase.toFixed(2);
                    }
                    {% endif %}
                    {% endfor %}
                    
                    // Trigger calculation
                    calculateRetrocessionAmounts();
                }, 50);
                {% endif %}
            }, 200); // Wait for complete initialization to finish
        } else {
            // Not a retrocession invoice, add items normally
            {% if duplicateData.items %}
            {% for item in duplicateData.items %}
            window.addItemWithData({
                description: '{{ item.description|escape('js') }}',
                quantity: '{{ item.quantity }}',
                unit_price: '{{ item.unit_price }}',
                vat_rate: '{{ item.vat_rate }}',
                item_id: '{{ item.item_id|default('') }}',
                reference: '{{ item.reference|default('')|escape('js') }}',
                unit: '{{ item.unit|default('')|escape('js') }}'
            });
            {% endfor %}
            {% else %}
            // No duplicate items, add default empty item
            addItem();
            {% endif %}
        }
        {% else %}
        // No duplicate data, add items normally
        {% if duplicateData.items %}
        {% for item in duplicateData.items %}
        window.addItemWithData({
            description: '{{ item.description|escape('js') }}',
            quantity: '{{ item.quantity }}',
            unit_price: '{{ item.unit_price }}',
            vat_rate: '{{ item.vat_rate }}',
            item_id: '{{ item.item_id|default('') }}',
            reference: '{{ item.reference|default('')|escape('js') }}',
            unit: '{{ item.unit|default('')|escape('js') }}'
        });
        {% endfor %}
        {% else %}
        // No duplicate items, add default empty item
        addItem();
        {% endif %}
        {% endif %}
        
        {% else %}
        // Handle retrocession invoice initialization BEFORE adding items
        if (isRetrocession) {
            console.log('Initializing retrocession invoice from URL parameter...');
            initializeRetrocessionInvoiceComplete();
        } else if (isLocation) {
            console.log('Initializing location invoice from URL parameter...');
            initializeLocationInvoiceComplete();
        } else {
            // No duplicate data, add default empty item
            addItem();
        }
        // Load billable options for ALL invoice types
        if (billableTypeElement) {
            // Set default billable type if not set
            if (!billableTypeElement.value) {
                // Default to 'client' for regular invoices, 'user' for special types
                if (isLocation || isRetrocession) {
                    billableTypeElement.value = 'user';
                    console.log('📝 Set default billable type to user for special invoice');
                } else {
                    billableTypeElement.value = 'client';
                    console.log('📝 Set default billable type to client for regular invoice');
                }
            }
            
            console.log('🔍 Loading billable options on page load...');
            console.log('Billable type value:', billableTypeElement.value);
            console.log('Invoice type:', invoiceType);
            
            // Add appropriate delay based on invoice type
            let loadDelay = 0;
            if (isLocation) {
                console.log('🏠 Location invoice - adding delay for type setup');
                loadDelay = 200;
            } else if (isRetrocession) {
                console.log('🎯 Retrocession invoice - adding delay for initialization');
                loadDelay = 300; // Slightly longer for retrocession setup
            }
            
            // Load billable options after delay
            setTimeout(() => {
                console.log('📋 Loading billable options now...');
                loadBillableOptions();
            }, loadDelay);
        }
        {% endif %}
    }).catch(error => {
        console.error('Error during initialization:', error);
        // Still try to add an item with default config
        addItem();
    });
    
    // Invoice numbers are now generated on save - no manual generation needed
    
    // Handle billable type change - CRITICAL EVENT
    if (billableTypeElement) {
        console.log('Attaching billable type change listener...');
        billableTypeElement.addEventListener('change', function(e) {
            console.log('*** BILLABLE TYPE CHANGED TO:', e.target.value, '***');
            loadBillableOptions();
        });
        console.log('Billable type change listener attached successfully');
    } else {
        console.error('ERROR: billable_type element not found!');
    }
    
    // Handle billable selection change
    const billableSelectElement = document.getElementById('billable_id');
    if (billableSelectElement) {
        console.log('Attaching billable selection change listener...');
        billableSelectElement.addEventListener('change', function(e) {
            console.log('*** BILLABLE SELECTED:', e.target.value, '***');
            const billableId = e.target.value;
            
            // Display billable info
            displayBillableInfo(billableId);
            
            // For location invoices, update VAT status and load courses
            const invoiceTypeSelect = document.getElementById('invoice_type_id');
            const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
            const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
            const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
            
            if (isLocationInvoice && billableId && billableId.startsWith('user_')) {
                const userId = billableId.replace('user_', '');
                console.log('🏠 Coach selected for location invoice:', userId);
                
                // Update VAT status immediately
                updateSelectedUserVatStatus(billableId);
                
                // Check if there are existing items that need TTC conversion
                const existingItems = document.querySelectorAll('.invoice-item');
                if (existingItems.length > 0) {
                    console.log('Found existing items, applying TTC conversion...');
                    setTimeout(() => {
                        forceTTCConversion();
                        calculateTotals();
                    }, 100);
                }
                
                // Then load courses
                loadCoachCourses(userId);
            }
        });
    }
    
    // Handle payment term change
    document.getElementById('payment_term_id').addEventListener('change', updateDueDate);
    
    // Handle issue date change
    document.getElementById('issue_date').addEventListener('change', function() {
        updateDueDate();
        // Also update period for rental invoices
        updateRentalPeriod();
    });
    
    // Handle invoice type change to show/hide CNS fields
    document.getElementById('invoice_type_id').addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        const typeId = this.value;
        const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
        
        console.log('🔍 Invoice type change - Type ID:', typeId, 'Type Code:', typeCode, 'Text:', selectedOption?.textContent);
        
        // Define type mappings
        const typeMapping = {
            // Retrocession types
            'RET25': 'retrocession_25',
            'RET30': 'retrocession_30',
            'RET': 'retrocession_30',  // Default RET to 30%
            // Location types
            'LOY': 'location',
            'LOCS': 'location',
            'LOC': 'location',  // Add LOC mapping
            // By ID
            '1': 'loyer',
            '2': 'retrocession_30',
            '12': 'location',
            '15': 'retrocession_25'
        };
        
        // Determine the URL type parameter
        let urlType = null;
        
        // Check by code first
        if (typeCode && typeMapping[typeCode]) {
            urlType = typeMapping[typeCode];
        }
        // Then by ID
        else if (typeId && typeMapping[typeId]) {
            urlType = typeMapping[typeId];
        }
        // Finally by name
        else if (typeName.includes('rétrocession 25')) {
            urlType = 'retrocession_25';
        } else if (typeName.includes('rétrocession 30')) {
            urlType = 'retrocession_30';
        } else if (typeName.includes('location')) {
            urlType = 'location';
        } else if (typeName.includes('loyer')) {
            urlType = 'loyer';
        }
        
        // Get current URL type
        const currentUrl = new URL(window.location.href);
        const currentType = currentUrl.searchParams.get('type');
        
        console.log('URL type needed:', urlType, 'Current type:', currentType);
        
        // Navigate if we need a special type URL parameter
        // This includes: retrocession_25, retrocession_30, location
        const specialTypes = ['retrocession_25', 'retrocession_30', 'retrocession', 'location'];
        const needsSpecialUrl = urlType && specialTypes.includes(urlType);
        const hasSpecialUrl = currentType && specialTypes.includes(currentType);
        
        // Navigate if:
        // 1. We need a special URL and don't have it yet
        // 2. We need a different special URL than current
        // 3. We don't need a special URL but currently have one
        const needsNavigation = (needsSpecialUrl && urlType !== currentType) || 
                               (!needsSpecialUrl && hasSpecialUrl);
        
        if (needsNavigation) {
            console.log('🔄 Navigation needed - from:', currentType || 'none', 'to:', urlType || 'none');
            if (urlType) {
                currentUrl.searchParams.set('type', urlType);
            } else {
                currentUrl.searchParams.delete('type');
            }
            window.location.href = currentUrl.toString();
            return;
        }
        
        // Continue with existing logic
        handleInvoiceTypeChange();
        updateInvoiceNumber();
        
        // Always toggle exclude patient line visibility when changing types
        setTimeout(() => {
            toggleExcludePatientLine();
            console.log('Toggled exclude patient line visibility for type:', typeCode);
        }, 50);
        
        // Update columns if not retrocession
        if (!typeCode || !typeCode.startsWith('RET')) {
            updateInvoiceItemColumns();
        } else {
            setTimeout(() => {
                updateInvoiceTableHeaders('RET');
            }, 50);
        }
        
        updateTemplateOptions();
    });
    
    // Event listeners for retrocession inputs are now added in addRetrocessionItem function
    
    // Handle template selection
    const templateSelect = document.getElementById('template_id');
    if (templateSelect) {
        templateSelect.addEventListener('change', function() {
            const templateId = this.value;
            if (templateId) {
                applyTemplate(templateId);
            }
        });
    }
    
    // Add item button
    document.getElementById('addItemBtn').addEventListener('click', addItem);
    
    // Apply voucher - commented out as voucher section was removed
    // document.getElementById('applyVoucherBtn').addEventListener('click', applyVoucher);
    
    // Add new billable button
    document.getElementById('addNewBillableBtn').addEventListener('click', function() {
        const type = document.getElementById('billable_type').value;
        if (type === 'client') {
            window.open('{{ base_url }}/clients/create?quick=1', 'newClient', 'width=800,height=600');
        } else if (type === 'patient') {
            window.open('{{ base_url }}/patients/create?quick=1', 'newPatient', 'width=800,height=600');
        }
    });
    
    // Handle submit button clicks to set action value
    const submitButtons = document.querySelectorAll('button[type="submit"][form="invoiceForm"]');
    submitButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default form submission
            const actionValue = this.value || this.getAttribute('value');
            console.log('Submit button clicked, action:', actionValue);
            
            // Set the action value in the hidden field
            const actionField = document.getElementById('formAction');
            if (actionField) {
                actionField.value = actionValue;
                console.log('Set action field to:', actionValue);
            }
            
            // Submit the form
            const form = document.getElementById('invoiceForm');
            if (form) {
                console.log('🚀 Submitting form programmatically');
                // Trigger form submit event (which will run validation)
                if (form.requestSubmit) {
                    form.requestSubmit();
                } else {
                    // Fallback for browsers that don't support requestSubmit
                    const submitEvent = new Event('submit', {
                        bubbles: true,
                        cancelable: true
                    });
                    form.dispatchEvent(submitEvent);
                }
            } else {
                console.error('❌ Form not found!');
            }
        });
    });
    
    // Form validation with debugging
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            console.log('=== FORM SUBMISSION STARTED ===');
            console.log('Submission triggered by:', event.submitter?.textContent || 'Unknown');
            console.log('Form action:', form.action);
            console.log('Form method:', form.method);
            
            try {
                // Debug: Log all form data
                const formData = new FormData(form);
                console.log('Form data before validation:');
                for (let [key, value] of formData.entries()) {
                    console.log(`  ${key}: ${value}`);
                }
                
                // Check action field
                const actionField = formData.get('action');
                console.log('Action field value:', actionField);
                
                // Double-check the hidden action field
                const hiddenAction = document.getElementById('formAction');
                console.log('Hidden action field value:', hiddenAction ? hiddenAction.value : 'not found');
                
                // Check if we have at least one item
                const items = document.querySelectorAll('.invoice-item');
                console.log('Number of invoice items:', items.length);
                if (items.length === 0) {
                    event.preventDefault();
                    event.stopPropagation();
                    console.error('❌ No invoice items found');
                    alert('Please add at least one invoice item');
                    return;
                }
                
                // Check if this is a retrocession invoice
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
                const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                const isRetrocessionForm = typeCode && typeCode.startsWith('RET');
                console.log('Invoice type:', selectedOption?.textContent, 'Code:', typeCode, 'Is Retrocession:', isRetrocessionForm);
                
                // Log all required fields
                console.log('=== REQUIRED FIELDS CHECK ===');
                const requiredFields = form.querySelectorAll('[required]');
                console.log('Total required fields:', requiredFields.length);
                requiredFields.forEach(field => {
                    const fieldInfo = {
                        name: field.name || field.id,
                        type: field.type,
                        value: field.value,
                        disabled: field.disabled,
                        visible: field.offsetParent !== null,
                        validity: field.checkValidity()
                    };
                    console.log('Required field:', fieldInfo);
                });
                
                // For retrocession invoices, ensure VAT fields have values even if disabled
                if (isRetrocessionForm) {
                    console.log('=== RETROCESSION VAT FIELDS ===');
                    items.forEach((row, index) => {
                        const vatSelect = row.querySelector('.item-vat');
                        if (vatSelect) {
                            console.log(`Row ${index} VAT:`, {
                                name: vatSelect.name,
                                value: vatSelect.value,
                                disabled: vatSelect.disabled,
                                required: vatSelect.required,
                                selectedIndex: vatSelect.selectedIndex,
                                selectedText: vatSelect.options[vatSelect.selectedIndex]?.text
                            });
                            
                            // For disabled fields, we need to add a hidden input with the value
                            if (vatSelect.disabled) {
                                // Check if hidden input already exists
                                const hiddenInputName = vatSelect.name + '_hidden';
                                let hiddenInput = row.querySelector(`input[name="${hiddenInputName}"]`);
                                
                                if (!hiddenInput) {
                                    // Create hidden input with same name as the select
                                    hiddenInput = document.createElement('input');
                                    hiddenInput.type = 'hidden';
                                    hiddenInput.name = vatSelect.name; // Use same name so it gets submitted
                                    hiddenInput.value = vatSelect.value;
                                    vatSelect.parentNode.appendChild(hiddenInput);
                                    console.log(`✅ Added hidden VAT input for row ${index}: ${hiddenInput.value}`);
                                } else {
                                    // Update existing hidden input value
                                    hiddenInput.value = vatSelect.value;
                                    console.log(`✅ Updated hidden VAT value for row ${index}: ${hiddenInput.value}`);
                                }
                            }
                        }
                        
                        // Log all item fields
                        const itemFields = row.querySelectorAll('input, select, textarea');
                        console.log(`Row ${index} fields:`, Array.from(itemFields).map(f => ({
                            name: f.name,
                            value: f.value,
                            type: f.type,
                            required: f.required,
                            disabled: f.disabled
                        })));
                    });
                }
                
                // Check form validity
                console.log('=== FORM VALIDATION ===');
                const isValid = form.checkValidity();
                console.log('Form validity:', isValid);
                
                if (!isValid) {
                    event.preventDefault();
                    event.stopPropagation();
                    console.error('❌ Form validation failed');
                    
                    // Show which fields are invalid
                    const invalidFields = form.querySelectorAll(':invalid');
                    console.log('Invalid fields count:', invalidFields.length);
                    invalidFields.forEach((field, index) => {
                        console.error(`Invalid field ${index + 1}:`, {
                            name: field.name || field.id,
                            type: field.type,
                            required: field.required,
                            disabled: field.disabled,
                            value: field.value,
                            validationMessage: field.validationMessage,
                            parent: field.parentElement?.className
                        });
                        
                        // Special handling for retrocession VAT fields
                        if (isRetrocessionForm && field.name && field.name.includes('vat_rate_id') && field.disabled) {
                            console.log('🔧 Removing required from disabled VAT field:', field.name);
                            field.required = false;
                        }
                    });
                    
                    // Retry validation for retrocession after fixing VAT fields
                    if (isRetrocessionForm) {
                        const isValidAfterFix = form.checkValidity();
                        console.log('Form validity after VAT fix:', isValidAfterFix);
                        
                        if (!isValidAfterFix) {
                            console.log('❌ Still invalid after fixes');
                            const stillInvalid = form.querySelectorAll(':invalid');
                            console.log('Still invalid count:', stillInvalid.length);
                            stillInvalid.forEach(field => {
                                console.error('Still invalid:', field.name || field.id, 'Message:', field.validationMessage);
                            });
                        } else {
                            console.log('✅ Form valid after fixes, allowing submission');
                            // Remove preventDefault to allow submission
                            return;
                        }
                    }
                } else {
                    console.log('✅ Form validation passed, submitting...');
                    console.log('Final form data being submitted:');
                    const finalData = new FormData(form);
                    for (let [key, value] of finalData.entries()) {
                        if (key.includes('item') || key === 'action' || key === 'billable_id' || key.includes('vat_rate_id')) {
                            console.log(`  ${key}: ${value}`);
                        }
                    }
                    
                    // Allow form to submit naturally
                    console.log('Form will now submit to:', form.action);
                    console.log('Form method:', form.method);
                    
                    // Log final check before submission
                    console.log('=== FINAL SUBMISSION CHECK ===');
                    console.log('CSRF Token present:', !!formData.get('csrf_token'));
                    console.log('Action value:', formData.get('action'));
                    console.log('Items count:', document.querySelectorAll('.invoice-item').length);
                    
                    // Check if all VAT fields have values
                    const vatInputs = form.querySelectorAll('input[name*="vat_rate_id"]');
                    console.log('VAT inputs found:', vatInputs.length);
                    vatInputs.forEach((input, idx) => {
                        console.log(`VAT input ${idx}: name=${input.name}, value=${input.value}, type=${input.type}`);
                    });
                    
                    console.log('✅ Form submission proceeding...');
                }
                
            } catch (error) {
                console.error('❌ Error during form submission:', error);
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
            console.log('=== FORM SUBMISSION END ===');
        });
    });
    
    // Check and fix existing items after everything is loaded
    setTimeout(() => {
        console.log('=== Checking for existing items that need TTC fix ===');
        checkAndFixExistingItems();
        
        // Additional check for location invoices with existing items
        const billableSelect = document.getElementById('billable_id');
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
        const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
        const isLocationInvoice = typeName.includes('location');
        
        if (isLocationInvoice && billableSelect?.value?.startsWith('user_')) {
            console.log('Location invoice with user selected on load - applying TTC conversion');
            
            // Update user VAT status
            updateSelectedUserVatStatus(billableSelect.value);
            
            // Force TTC conversion after a short delay
            setTimeout(() => {
                forceTTCConversion();
                calculateTotals();
            }, 200);
        }
    }, 1500);
});

// Load VAT rates from server
async function loadVatRates() {
    try {
        const response = await fetch('{{ base_url }}/api/vat-rates');
        const data = await response.json();
        if (data.success) {
            vatRates = data.rates;
        } else {
            // Fallback VAT rates if API fails
            vatRates = [
                {id: 1, name: 'Standard', rate: 17, is_default: true},
                {id: 2, name: 'Reduced', rate: 8, is_default: false},
                {id: 3, name: 'Super Reduced', rate: 3, is_default: false},
                {id: 4, name: 'Parking', rate: 14, is_default: false},
                {id: 5, name: 'Exempt', rate: 0, is_default: false}
            ];
        }
    } catch (error) {
        console.error('Error loading VAT rates:', error);
        // Use fallback rates
        vatRates = [
            {id: 1, name: 'Standard', rate: 17, is_default: true},
            {id: 2, name: 'Reduced', rate: 8, is_default: false},
            {id: 3, name: 'Super Reduced', rate: 3, is_default: false},
            {id: 4, name: 'Parking', rate: 14, is_default: false},
            {id: 5, name: 'Exempt', rate: 0, is_default: false}
        ];
    }
}

// Original addItem function removed - now using addItemWithData through window.addItem

function removeItem(row) {
    if (document.querySelectorAll('.invoice-item').length > 1) {
        row.remove();
        calculateTotals();
    } else {
        alert('{{ __("invoices.at_least_one_item") }}');
    }
}

// Original calculateItemTotal function removed - now using updated version that handles discount and subtotal

function calculateTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    let expectedTTCTotal = 0;
    
    // Check if this is a location invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
    
    // Check if we should use TTC pricing for adjustments
    const shouldUseTTC = isLocationInvoice && selectedUserVatStatus.userId && 
                       (!selectedUserVatStatus.hasVatIntracommunautaire || selectedUserVatStatus.isLuxembourgVat);
    
    document.querySelectorAll('.invoice-item').forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
        const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
        const discount = parseFloat(row.querySelector('.item-discount')?.value) || 0;
        const vatSelect = row.querySelector('.item-vat');
        const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
        
        // Normal calculation - price is always NET/HT
        const itemSubtotal = quantity * price;
        const discountAmount = itemSubtotal * (discount / 100);
        const discountedSubtotal = itemSubtotal - discountAmount;
        const itemVat = discountedSubtotal * (vatRate / 100);
        
        subtotal += discountedSubtotal;
        vatAmount += itemVat;
        
        // Calculate expected TTC total if we have stored TTC prices
        if (shouldUseTTC) {
            const ttcPrice = parseFloat(row.getAttribute('data-expected-ttc-total') || 0);
            if (ttcPrice > 0) {
                expectedTTCTotal += ttcPrice * quantity;
            }
        }
    });
    
    // Include secretary fee if applicable (only for non-retrocession invoices)
    const secretaryFeeElement = document.getElementById('secretary_fee_amount');
    const secretaryFee = secretaryFeeElement ? parseFloat(secretaryFeeElement.value) || 0 : 0;
    
    // Check if user has VAT intracommunautaire for location invoices
    let total;
    if (isLocationInvoice && selectedUserVatStatus.userId && selectedUserVatStatus.hasVatIntracommunautaire && !selectedUserVatStatus.isLuxembourgVat) {
        // For location invoices WITH non-Luxembourg EU VAT, no VAT
        total = subtotal + secretaryFee;
        vatAmount = 0; // Override VAT to 0
        console.log('Location invoice with non-Luxembourg EU VAT - VAT set to 0');
    } else {
        // Normal calculation with VAT (includes Luxembourg VAT numbers)
        total = subtotal + vatAmount + secretaryFee;
        
        // Apply rounding adjustment if we have expected TTC total
        if (shouldUseTTC && expectedTTCTotal > 0) {
            const calculatedTotal = total;
            const difference = expectedTTCTotal - calculatedTotal;
            
            console.log('Rounding check - Expected:', expectedTTCTotal.toFixed(2), 'Calculated:', calculatedTotal.toFixed(2), 'Difference:', difference.toFixed(3));
            
            // If the difference is small (due to rounding), adjust the VAT amount
            if (Math.abs(difference) < 0.05 && Math.abs(difference) > 0.001) {
                vatAmount += difference;
                total = expectedTTCTotal;
                console.log('Applied rounding adjustment of', difference.toFixed(3), '- New VAT:', vatAmount.toFixed(2));
            }
        }
    }
    
    document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
    document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
    document.getElementById('total').textContent = currency + total.toFixed(2);
}

function handleInvoiceTypeChange() {
    console.log('🔔 handleInvoiceTypeChange called');
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const typeId = invoiceTypeSelect.value;
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    console.log('📌 Type details - ID:', typeId, 'Name:', typeName, 'Code:', typeCode);
    
    // Check if this is a retrocession invoice
    const isRetrocession = (typeCode && typeCode.startsWith('RET')) || typeName.includes('rétrocession') || typeName.includes('retrocession');
    
    // Check if this is a location invoice
    const isLocation = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location salle');
    
    console.log('🎯 Is Retrocession?', isRetrocession, '🏠 Is Location?', isLocation);
    
    if (isRetrocession) {
        console.log('✅ Processing RETROCESSION invoice type change');
        // Use the complete initialization function
        initializeRetrocessionInvoiceComplete();
    } else if (isLocation) {
        console.log('🏠 Processing LOCATION invoice type change');
        initializeLocationInvoice();
    } else {
        // Update table headers to default
        updateInvoiceTableHeaders('');
        
        // Clear table if switching from retrocession
        const itemsBody = document.getElementById('itemsBody');
        if (itemsBody && itemsBody.querySelector('[data-line-type]')) {
            itemsBody.innerHTML = '';
            itemIndex = 0;
            addItem(); // Add default item
        }
    }
    
    // Handle rental invoice type (Loyer)
    if (typeId === '1' || typeName.includes('loyer')) {
        // Auto-fill subject if empty
        const subjectField = document.getElementById('subject');
        if (subjectField && !subjectField.value) {
            subjectField.value = 'LOYER + CHARGES';
        }
        
        // Auto-fill period with current month if empty (for rental invoices)
        const periodField = document.getElementById('period');
        if (periodField && !periodField.value) {
            // French month names
            const frenchMonths = [
                'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL',
                'MAI', 'JUIN', 'JUILLET', 'AOÛT',
                'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
            ];
            
            // Get issue date - use current month for rental invoices
            const issueDateField = document.getElementById('issue_date');
            const issueDate = issueDateField ? new Date(issueDateField.value) : new Date();
            
            // For rental invoices: use current month
            const monthIndex = issueDate.getMonth();
            const year = issueDate.getFullYear();
            
            periodField.value = frenchMonths[monthIndex] + ' ' + year;
        }
        
        // Add default rental lines if table is empty
        const itemsBody = document.getElementById('itemsBody');
        if (itemsBody && itemsBody.children.length === 0) {
            // Update columns first, then add default items
            updateInvoiceItemColumns().then(() => {
                // Add "Loyer mensuel" line
                addItemWithData({
                    description: 'Loyer mensuel',
                    quantity: '1',
                    unit_price: '',
                    vat_rate_id: defaultVatRateId || ''
                });
                
                // Add "Charges Location" line
                addItemWithData({
                    description: 'Charges Location',
                    quantity: '1',
                    unit_price: '',
                    vat_rate_id: defaultVatRateId || ''
                });
            });
        }
    }
    
    // Update table headers for retrocession invoices
    updateInvoiceTableHeaders(typeCode);
}

function updateRentalPeriod() {
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const typeId = invoiceTypeSelect.value;
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    
    const periodField = document.getElementById('period');
    if (!periodField) return;
    
    // French month names
    const frenchMonths = [
        'JANVIER', 'FÉVRIER', 'MARS', 'AVRIL',
        'MAI', 'JUIN', 'JUILLET', 'AOÛT',
        'SEPTEMBRE', 'OCTOBRE', 'NOVEMBRE', 'DÉCEMBRE'
    ];
    
    // Get issue date
    const issueDateField = document.getElementById('issue_date');
    const issueDate = issueDateField ? new Date(issueDateField.value) : new Date();
    
    // Check if it's a rental invoice (loyer/LOY)
    if (typeId === '1' || typeName.includes('loyer')) {
        // For rental invoices: use current month
        const monthIndex = issueDate.getMonth();
        const year = issueDate.getFullYear();
        periodField.value = frenchMonths[monthIndex] + ' ' + year;
    } 
    // Check if it's a retrocession invoice
    else if (typeName.includes('retrocession') || typeName.includes('rétrocession')) {
        // For retrocession invoices: use previous month
        const previousMonth = new Date(issueDate);
        previousMonth.setMonth(previousMonth.getMonth() - 1);
        
        const monthIndex = previousMonth.getMonth();
        const year = previousMonth.getFullYear();
        periodField.value = frenchMonths[monthIndex] + ' ' + year;
    }
    // Check if it's a location invoice
    else if (typeName.includes('location')) {
        // For location invoices: use previous month (same as retrocession)
        const previousMonth = new Date(issueDate);
        previousMonth.setMonth(previousMonth.getMonth() - 1);
        
        const monthIndex = previousMonth.getMonth();
        const year = previousMonth.getFullYear();
        periodField.value = frenchMonths[monthIndex] + ' ' + year;
    }
}

// Complete retrocession invoice initialization
function initializeRetrocessionInvoiceComplete() {
    console.log('🚀 Complete retrocession initialization starting...');
    
    // Determine if this is 25% or 30% retrocession
    const urlParams = new URLSearchParams(window.location.search);
    const typeParam = urlParams.get('type');
    const isRet25 = typeParam === 'retrocession_25';
    
    // Set invoice type to retrocession in dropdown
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    if (invoiceTypeSelect) {
        // Find the correct retrocession option
        for (let option of invoiceTypeSelect.options) {
            const prefix = option.getAttribute('data-prefix');
            if (isRet25 && prefix === 'RET25') {
                invoiceTypeSelect.value = option.value;
                console.log('Set invoice type to Retrocession 25%');
                break;
            } else if (!isRet25 && (prefix === 'RET30' || prefix === 'RET')) {
                invoiceTypeSelect.value = option.value;
                console.log('Set invoice type to Retrocession 30%');
                break;
            }
        }
    }
    
    // Set default subject for retrocession
    const subjectField = document.getElementById('subject');
    if (subjectField && !subjectField.value) {
        subjectField.value = 'RÉTROCESSION';
        console.log('Set subject to RÉTROCESSION');
    }
    
    // Update period to previous month
    updateRentalPeriod();
    
    // Update invoice number to use RET prefix
    updateInvoiceNumber();
    console.log('Updated invoice number for retrocession');
    
    // Clear any existing items first
    const itemsBody = document.getElementById('itemsBody');
    if (itemsBody) {
        itemsBody.innerHTML = '';
        itemIndex = 0;
    }
    
    // Update table headers for retrocession format immediately
    updateInvoiceTableHeaders('RET');
    
    // Update tfoot colspan for retrocession (5 columns instead of 4)
    const tfootCells = document.querySelectorAll('#itemsTable tfoot td[colspan="4"]');
    tfootCells.forEach(cell => {
        cell.setAttribute('colspan', '3'); // Montant Base, OBJET, TOTAL columns
    });
    
    // Initialize retrocession invoice with pre-configured lines
    setTimeout(() => {
        initializeRetrocessionInvoice(isRet25);
        
        // Load billable options for retrocession (practitioners)
        console.log('🎯 Loading billable options for retrocession invoice...');
        loadBillableOptions();
    }, 100); // Small delay to ensure DOM is ready
}

// Initialize retrocession invoice with 3 pre-configured lines
function initializeRetrocessionInvoice(isRet25 = false) {
    const itemsBody = document.getElementById('itemsBody');
    if (!itemsBody) {
        console.error('itemsBody not found');
        return;
    }
    
    console.log('Initializing retrocession invoice...');
    
    // Clear existing items
    itemsBody.innerHTML = '';
    itemIndex = 0;
    
    // Check if patient line should be excluded
    const excludePatientCheckbox = document.getElementById('exclude_patient_line');
    const excludePatient = excludePatientCheckbox ? excludePatientCheckbox.checked : false;
    
    console.log('Exclude patient line:', excludePatient);
    
    // Create lines based on exclude setting
    const lines = [
        {
            hasBaseInput: true,
            baseInputId: 'cns_amount',
            placeholder: '{{ __("invoices.enter_cns_amount")|default("Montant CNS") }}',
            description: 'RÉTROCESSION CNS 20%',
            lineType: 'cns',
            vatRate: 0
        }
    ];
    
    // Only add patient line if not excluded
    if (!excludePatient) {
        lines.push({
            hasBaseInput: true,
            baseInputId: 'patient_amount',
            placeholder: '{{ __("invoices.enter_patient_amount")|default("Montant Patients") }}',
            description: 'RÉTROCESSION PATIENTS 20%',
            lineType: 'patient',
            vatRate: 0
        });
    }
    
    // Always add secretary line
    lines.push({
        showTotal: true,
        totalId: 'secretary_total',
        description: 'FRAIS SECRÉTARIAT ET MISE À DISPOSITION MATÉRIEL ' + (isRet25 ? '5%' : '10%'),
        lineType: 'secretary',
        vatRate: 17
    });
    
    console.log('Creating', lines.length, 'retrocession lines');
    
    // Adjust line numbering to ensure proper indexing
    let lineIndex = 0;
    
    // Add all lines
    lines.forEach((lineData) => {
        const row = document.createElement('tr');
        row.className = 'invoice-item';
        row.setAttribute('data-line-type', lineData.lineType);
        
        // Hidden quantity field
        const hiddenQty = document.createElement('input');
        hiddenQty.type = 'hidden';
        hiddenQty.name = `items[${lineIndex}][quantity]`;
        hiddenQty.value = '1';
        hiddenQty.className = 'item-quantity';
        row.appendChild(hiddenQty);
        
        // Column 1: Description (OBJET) - First column
        const col1 = document.createElement('td');
        const descInput = document.createElement('input');
        descInput.type = 'text';
        descInput.className = 'form-control form-control-sm item-description';
        descInput.name = `items[${lineIndex}][description]`;
        descInput.value = lineData.description;
        descInput.readOnly = true;
        descInput.required = true;
        
        const hiddenItemId = document.createElement('input');
        hiddenItemId.type = 'hidden';
        hiddenItemId.name = `items[${lineIndex}][item_id]`;
        hiddenItemId.className = 'item-id';
        hiddenItemId.value = '';
        
        col1.appendChild(hiddenItemId);
        col1.appendChild(descInput);
        row.appendChild(col1);
        
        // Column 2: Base amount input or total display (Montant Base) - Second column
        const col2 = document.createElement('td');
        if (lineData.hasBaseInput) {
            const baseInput = document.createElement('input');
            baseInput.type = 'number';
            baseInput.className = 'form-control form-control-sm retrocession-base-amount';
            baseInput.id = lineData.baseInputId;
            baseInput.placeholder = lineData.placeholder;
            baseInput.min = '0';
            baseInput.step = '0.01';
            baseInput.value = '';
            col2.appendChild(baseInput);
        } else if (lineData.showTotal) {
            const totalInput = document.createElement('input');
            totalInput.type = 'text';
            totalInput.className = 'form-control form-control-sm';
            totalInput.id = lineData.totalId;
            totalInput.value = '€0.00';
            totalInput.readOnly = true;
            totalInput.style.backgroundColor = '#f8f9fa';
            totalInput.style.fontWeight = '500';
            col2.appendChild(totalInput);
        }
        row.appendChild(col2);
        
        // Column 3: Total
        const col3 = document.createElement('td');
        const totalDisplay = document.createElement('input');
        totalDisplay.type = 'text';
        totalDisplay.className = 'form-control form-control-sm item-total';
        totalDisplay.readOnly = true;
        col3.appendChild(totalDisplay);
        row.appendChild(col3);
        
        // Column 4: HTVA
        const col4 = document.createElement('td');
        const priceInput = document.createElement('input');
        priceInput.type = 'number';
        priceInput.className = 'form-control form-control-sm item-price';
        priceInput.name = `items[${lineIndex}][unit_price]`;
        priceInput.value = '0.00';
        priceInput.min = '0';
        priceInput.step = '0.01';
        priceInput.readOnly = true;
        col4.appendChild(priceInput);
        row.appendChild(col4);
        
        // Column 5: VAT Rate
        const col5 = document.createElement('td');
        const vatSelect = document.createElement('select');
        vatSelect.className = 'form-select form-select-sm item-vat';
        vatSelect.name = `items[${lineIndex}][vat_rate_id]`;
        // Don't set required on disabled fields
        vatSelect.disabled = true;
        
        // Add VAT options and select the appropriate rate
        vatRates.forEach(vat => {
            const option = document.createElement('option');
            option.value = vat.id;
            option.setAttribute('data-rate', vat.rate);
            option.textContent = `${vat.rate}%`;
            if (parseFloat(vat.rate) === lineData.vatRate) {
                option.selected = true;
                // Set the value explicitly to ensure it's saved
                vatSelect.value = vat.id;
            }
            vatSelect.appendChild(option);
        });
        col5.appendChild(vatSelect);
        
        // Add hidden input for disabled select to ensure value is submitted
        const hiddenVat = document.createElement('input');
        hiddenVat.type = 'hidden';
        hiddenVat.name = vatSelect.name;
        hiddenVat.value = vatSelect.value;
        col5.appendChild(hiddenVat);
        
        // Update hidden input when select changes (even though it's disabled)
        vatSelect.addEventListener('change', function() {
            hiddenVat.value = this.value;
        });
        
        row.appendChild(col5);
        
        // Column 6: Actions
        const col6 = document.createElement('td');
        col6.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item" disabled><i class="bi bi-trash"></i></button>`;
        row.appendChild(col6);
        
        // Add to table
        itemsBody.appendChild(row);
        
        // Increment line index for next iteration
        lineIndex++;
    });
    
    // Set itemIndex to the number of lines added
    itemIndex = lineIndex;
    
    // Add event listeners to base amount inputs
    const cnsInput = document.getElementById('cns_amount');
    const patientInput = document.getElementById('patient_amount');
    
    if (cnsInput) {
        cnsInput.addEventListener('input', calculateRetrocessionAmounts);
    }
    if (patientInput) {
        patientInput.addEventListener('input', calculateRetrocessionAmounts);
    }
    
    // Calculate initial totals (all should be 0)
    calculateRetrocessionTotals();
    
    console.log('Retrocession initialization complete - added ' + lines.length + ' lines');
}

// Check and fix existing items on page load
function checkAndFixExistingItems() {
    console.log('Checking for existing items that need TTC fix...');
    
    // Check if it's a location invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = typeName.includes('location');
    
    if (!isLocationInvoice) {
        console.log('Not a location invoice, skipping TTC check');
        return;
    }
    
    // Check if a user is already selected
    const billableSelect = document.getElementById('billable_id');
    if (billableSelect && billableSelect.value && billableSelect.value.startsWith('user_')) {
        console.log('User already selected:', billableSelect.value);
        
        // Update VAT status
        updateSelectedUserVatStatus(billableSelect.value);
        
        // Check for existing items
        const existingItems = document.querySelectorAll('.invoice-item');
        if (existingItems.length > 0) {
            console.log(`Found ${existingItems.length} existing items`);
            
            // Apply TTC fix
            setTimeout(() => {
                forceTTCConversion();
                calculateTotals();
                console.log('TTC fix applied to existing items');
            }, 500);
        }
    }
}

// Calculate retrocession amounts based on inline inputs
function calculateRetrocessionAmounts() {
    const cnsInput = document.getElementById('cns_amount');
    const patientInput = document.getElementById('patient_amount');
    
    const cnsAmount = cnsInput ? parseFloat(cnsInput.value) || 0 : 0;
    const patientAmount = patientInput ? parseFloat(patientInput.value) || 0 : 0;
    
    // Calculate retrocession parts (20% each)
    const partCns = cnsAmount * 0.20;
    const partPatients = patientAmount * 0.20;
    
    // Calculate secretary fee (5% or 10% of total CNS + Patient)
    const totalCnsPatient = cnsAmount + patientAmount;
    // Determine secretary percentage based on invoice type
    const urlParams = new URLSearchParams(window.location.search);
    const typeParam = urlParams.get('type');
    const secretaryPercent = (typeParam === 'retrocession_25') ? 0.05 : 0.10;
    const secretaryFeeTvac = totalCnsPatient * secretaryPercent;
    
    // Calculate HTVA (remove 17% VAT)
    const secretaryFeeHtva = secretaryFeeTvac / 1.17;
    
    // Update the secretary total display
    const secretaryTotalInput = document.getElementById('secretary_total');
    if (secretaryTotalInput) {
        secretaryTotalInput.value = currency + totalCnsPatient.toFixed(2);
    }
    
    // Update the calculated amounts in each row
    const rows = document.querySelectorAll('.invoice-item');
    rows.forEach(row => {
        const lineType = row.getAttribute('data-line-type');
        const priceInput = row.querySelector('.item-price');
        
        if (lineType === 'cns' && priceInput) {
            priceInput.value = partCns.toFixed(2);
        } else if (lineType === 'patient' && priceInput) {
            priceInput.value = partPatients.toFixed(2);
        } else if (lineType === 'secretary' && priceInput) {
            priceInput.value = secretaryFeeHtva.toFixed(2);
        }
        
        // Recalculate row total
        calculateRetrocessionItemTotal(row);
    });
    
    // Use retrocession-specific totals calculation
    calculateRetrocessionTotals();
}

// Legacy function for compatibility
function calculateRetrocession() {
    calculateRetrocessionAmounts();
}

// Helper function to get 0% VAT rate ID
function getZeroVatRateId() {
    for (let vat of vatRates) {
        if (parseFloat(vat.rate) === 0) {
            return vat.id;
        }
    }
    return vatRates[0]?.id || '';
}

// Helper function to get 17% VAT rate ID  
function get17VatRateId() {
    for (let vat of vatRates) {
        if (parseFloat(vat.rate) === 17) {
            return vat.id;
        }
    }
    return vatRates[0]?.id || '';
}

// Backward compatibility for old function name
function calculateSecretaryFee() {
    calculateRetrocession();
}


// Invoice number is now generated on save - no need for this function

// Debug: Script execution checkpoint
console.log('Invoice create script - Loading client/user data...');

// Store clients and users data safely with error handling
let clientsData = [];
let usersData = [];

// Store selected user's VAT status for location invoices
let selectedUserVatStatus = {
    hasVatIntracommunautaire: false,
    isLuxembourgVat: false,
    userId: null
};

// Store expected TTC totals for location invoices to handle rounding
let expectedTTCTotals = [];

// Function to update selected user VAT status
function updateSelectedUserVatStatus(billableId) {
    if (!billableId || !billableId.startsWith('user_')) {
        selectedUserVatStatus = {
            hasVatIntracommunautaire: false,
            isLuxembourgVat: false,
            userId: null
        };
        return;
    }
    
    const userId = billableId.replace('user_', '');
    
    // Try to find user data
    let userData = null;
    if (coachesData && coachesData.length > 0) {
        userData = coachesData.find(u => u.id == userId);
    }
    if (!userData && practitionersData && practitionersData.length > 0) {
        userData = practitionersData.find(u => u.id == userId);
    }
    if (!userData && usersData && usersData.length > 0) {
        userData = usersData.find(u => u.id == userId);
    }
    
    if (userData) {
        const vatNumber = userData.vat_intercommunautaire ? userData.vat_intercommunautaire.trim() : '';
        selectedUserVatStatus.userId = userData.id;
        selectedUserVatStatus.hasVatIntracommunautaire = !!vatNumber;
        selectedUserVatStatus.isLuxembourgVat = vatNumber.toUpperCase().startsWith('LU');
        console.log('Updated VAT status:', selectedUserVatStatus);
        console.log('User:', userData.name, 'VAT:', vatNumber);
    } else {
        console.warn('User data not found for ID:', userId);
    }
}

try {
    clientsData = {{ clients|default([])|json_encode|raw }};
    console.log('Clients data loaded successfully:', clientsData.length, 'clients');
    if (clientsData.length > 0) {
        console.log('First client:', clientsData[0]);
    }
} catch (error) {
    console.error('Error loading clients data:', error);
    console.log('Clients variable exists:', {{ clients is defined ? 'true' : 'false' }});
}

try {
    usersData = {{ users|default([])|json_encode|raw }};
    console.log('Users data loaded successfully:', usersData.length, 'users');
    if (usersData.length > 0) {
        console.log('First user:', usersData[0]);
    }
} catch (error) {
    console.error('Error loading users data:', error);
    console.log('Users variable exists:', {{ users is defined ? 'true' : 'false' }});
}

// Store coaches data separately for location invoices
let coachesData = [];
try {
    coachesData = {{ coaches|default([])|json_encode|raw }};
    console.log('Coaches data loaded successfully:', coachesData.length, 'coaches');
    if (coachesData.length > 0) {
        console.log('First coach:', coachesData[0]);
    }
    
    // Additional debugging
    console.log('=== COACHES DATA DEBUG ===');
    console.log('Coaches variable is defined:', {{ coaches is defined ? 'true' : 'false' }});
    console.log('Coaches count from Twig:', {{ coaches|default([])|length }});
    console.log('Raw coaches data:', coachesData);
    console.log('========================');
} catch (error) {
    console.error('Error loading coaches data:', error);
    console.log('Coaches variable exists:', {{ coaches is defined ? 'true' : 'false' }});
}
// Store practitioners data separately for retrocession invoices
let practitionersData = [];
try {
    practitionersData = {{ practitioners|default([])|json_encode|raw }};
    console.log('Practitioners data loaded successfully:', practitionersData.length, 'practitioners');
    if (practitionersData.length > 0) {
        console.log('First practitioner:', practitionersData[0]);
    }
} catch (error) {
    console.error('Error loading practitioners data:', error);
    console.log('Practitioners variable exists:', {{ practitioners is defined ? 'true' : 'false' }});
}

function loadBillableOptions() {
    return new Promise((resolve, reject) => {
        const type = document.getElementById('billable_type').value;
        const select = document.getElementById('billable_id');
        const label = document.getElementById('billable_label');
        const hint = document.getElementById('billable_hint');
        const addBtn = document.getElementById('addNewBillableBtn');
        
        console.log('🔧 === loadBillableOptions START ===');
        console.log('Billable type:', type);
        console.log('Select element exists:', !!select);
        console.log('Current invoice type ID:', document.getElementById('invoice_type_id')?.value);
        console.log('Current invoice type text:', document.getElementById('invoice_type_id')?.options[document.getElementById('invoice_type_id')?.selectedIndex]?.textContent);
        console.log('URL:', window.location.search);
        
        if (!type) {
            select.disabled = true;
            addBtn.disabled = true;
            select.innerHTML = '<option value="">{{ __("common.select_type_first") }}</option>';
            resolve(); // Resolve even when no type
            return;
        }
        
        select.disabled = false;
        
        // Clear the select options
        select.innerHTML = '<option value="">{{ __("common.select") }}</option>';
        
        try {
            // Handle different types
            if (type === 'client') {
                console.log('Loading clients, count:', clientsData.length);
                label.textContent = '{{ __("clients.client") }}';
                hint.textContent = '{{ __("invoices.cant_find_client_add_new") }}';
                addBtn.disabled = false;
                
                // Add clients to dropdown using safe JSON data
                clientsData.forEach(client => {
                    const option = new Option(`${client.name} (${client.client_number})`, `client_${client.id}`);
                    select.add(option);
                });
                console.log('Clients loaded into dropdown, options count:', select.options.length);
                
            } else if (type === 'user') {
                label.textContent = '{{ __("users.user") }}';
                hint.textContent = '{{ __("invoices.internal_invoice_hint") }}';
                addBtn.disabled = true;
                
                // Check if this is a location invoice - if so, only show coaches
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                const selectedOption = invoiceTypeSelect ? invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex] : null;
                const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                const typeText = selectedOption ? selectedOption.textContent.toLowerCase() : '';
                
                // Debug invoice type detection
                console.log('=== INVOICE TYPE DETECTION ===');
                console.log('Invoice type select exists:', !!invoiceTypeSelect);
                console.log('Selected option:', selectedOption ? selectedOption.textContent : 'None');
                console.log('Type code:', typeCode);
                console.log('Type text:', typeText);
                console.log('Invoice type ID:', invoiceTypeSelect ? invoiceTypeSelect.value : 'N/A');
                console.log('URL includes type=location:', window.location.search.includes('type=location'));
                
                // Check both prefix and text to handle empty prefix case
                // Also check invoice type ID for Location (12)
                const invoiceTypeId = invoiceTypeSelect ? invoiceTypeSelect.value : '';
                const isLocationInvoice = (typeCode && (typeCode.startsWith('LOCS') || typeCode === 'LOY')) || 
                                        typeText.includes('location') ||
                                        typeText.includes('loyer') ||
                                        invoiceTypeId === '12' || // Location invoice type ID
                                        (window.location.search.includes('type=location'));
                
                console.log('Is location invoice:', isLocationInvoice);
                console.log('===========================');
                
                // Check for retrocession invoice (including by ID)
                const isRetrocessionInvoice = (typeCode && typeCode.startsWith('RET')) || 
                                             typeText.includes('rétrocession') || 
                                             typeText.includes('retrocession') ||
                                             invoiceTypeId === '2' || // RET30
                                             invoiceTypeId === '15' || // RET25
                                             (window.location.search.includes('type=retrocession_30')) ||
                                             (window.location.search.includes('type=retrocession_25'));
                
                console.log('Invoice type detection:', {
                    typeCode: typeCode,
                    typeText: typeText,
                    urlParams: window.location.search,
                    isLocationInvoice: isLocationInvoice,
                    isRetrocessionInvoice: isRetrocessionInvoice,
                    selectedOptionHtml: selectedOption ? selectedOption.outerHTML : 'null'
                });
                
                if (isLocationInvoice) {
                    console.log('✅ Location invoice detected - showing only coaches');
                    console.log('Invoice type ID:', invoiceTypeId);
                    console.log('Invoice type code:', typeCode);
                    console.log('Invoice type text:', typeText);
                    
                    label.textContent = {{ __("users.coach") | default("Coach") | json_encode | raw }};
                    hint.textContent = {{ __("invoices.select_coach_for_location") | default("Select the coach for location rental") | json_encode | raw }};
                    
                    // Add only coaches to dropdown
                    console.log('📊 Coaches data:', coachesData.length, 'coaches available');
                    console.log('📋 Coaches array:', JSON.stringify(coachesData, null, 2));
                    
                    // Debug: Check if coachesData is truly empty or has unexpected structure
                    console.log('=== DROPDOWN POPULATION DEBUG ===');
                    console.log('coachesData type:', typeof coachesData);
                    console.log('coachesData is array:', Array.isArray(coachesData));
                    console.log('coachesData contents:', coachesData);
                    
                    let coachCount = 0;
                    if (Array.isArray(coachesData) && coachesData.length > 0) {
                        coachesData.forEach(coach => {
                            console.log('➕ Adding coach ' + (coachCount + 1) + ':', coach);
                            const courseName = coach.course_name ? ' - ' + coach.course_name : '';
                            const option = new Option(coach.name + ' (' + coach.username + ')' + courseName, 'user_' + coach.id);
                            select.add(option);
                            coachCount++;
                            console.log('✅ Coach added to dropdown: ' + coach.name + ' (option value: user_' + coach.id + ')');
                        });
                    } else {
                        console.log('⚠️ coachesData is not a valid array or is empty');
                    }
                    
                    console.log(`📊 Total coaches added to dropdown: ${coachCount}`);
                    console.log(`📋 Select element now has ${select.options.length} options`);
                    
                    if (coachCount === 0) {
                        console.log('❌ No coaches found in coachesData array');
                        const option = new Option({{ __("invoices.no_coaches_found") | default("No coaches found") | json_encode | raw }}, '');
                        option.disabled = true;
                        select.add(option);
                    }
                } else if (isRetrocessionInvoice) {
                    console.log('Retrocession invoice detected - showing only practitioners');
                    label.textContent = {{ __("users.practitioner") | default("Practitioner") | json_encode | raw }};
                    hint.textContent = {{ __("invoices.select_practitioner_for_retrocession") | default("Select the practitioner for retrocession calculation") | json_encode | raw }};
                    
                    // Add only practitioners to dropdown
                    console.log('Practitioners data:', practitionersData.length, 'practitioners available');
                    practitionersData.forEach(practitioner => {
                        const courseName = practitioner.course_name ? ` - ${practitioner.course_name}` : '';
                        const option = new Option(`${practitioner.name} (${practitioner.username})${courseName}`, `user_${practitioner.id}`);
                        select.add(option);
                    });
                    
                    if (practitionersData.length === 0) {
                        console.log('No practitioners found in practitionersData array');
                        const option = new Option({{ __("invoices.no_practitioners_found") | default("No practitioners found") | json_encode | raw }}, '');
                        option.disabled = true;
                        select.add(option);
                    }
                } else {
                    // Add all users to dropdown for regular user invoices
                    usersData.forEach(user => {
                        const option = new Option(`${user.name} (${user.username})`, `user_${user.id}`);
                        select.add(option);
                    });
                }
            }
            
            // Add event listener to display billable info when selection changes
            select.removeEventListener('change', handleBillableChange); // Remove any existing listener
            select.addEventListener('change', handleBillableChange);
            
            // Resolve the promise after options are loaded
            resolve();
        } catch (error) {
            console.error('Error in loadBillableOptions:', error);
            alert('{{ __("common.error_loading_data") | default("Error loading data") }}: ' + error.message);
            reject(error);
        }
    });
}

// Handle billable selection change
function handleBillableChange(e) {
    const billableId = e.target.value;
    console.log('🔍 handleBillableChange called with:', billableId);
    
    displayBillableInfo(billableId);
    
    // For location invoices, also load coach courses
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect ? invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex] : null;
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeText = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    
    // Check if this is a location invoice using multiple conditions
    const invoiceTypeId = invoiceTypeSelect ? invoiceTypeSelect.value : '';
    const isLocationInvoice = (typeCode && (typeCode === 'LOCS' || typeCode === 'LOC' || typeCode === 'LOY')) || 
                             typeText.includes('location') ||
                             typeText.includes('loyer') ||
                             invoiceTypeId === '12' || // Location invoice type ID
                             (window.location.search.includes('type=location'));
    
    console.log('🔍 Location invoice check:', {
        typeCode: typeCode,
        typeText: typeText,
        isLocationInvoice: isLocationInvoice,
        billableId: billableId,
        selectedOptionHtml: selectedOption ? selectedOption.outerHTML : 'null'
    });
    
    if (isLocationInvoice && billableId && billableId.startsWith('user_')) {
        const userId = billableId.replace('user_', '');
        console.log('🏠 Coach selected for location invoice:', userId);
        loadCoachCourses(userId);
    } else {
        console.log('🔍 Not loading courses:', {
            isLocationInvoice: isLocationInvoice,
            hasBillableId: !!billableId,
            isUserBillable: billableId && billableId.startsWith('user_')
        });
    }
}

// applyVoucher function removed - voucher section no longer exists in the form

function updateDueDate() {
    const paymentTermSelect = document.getElementById('payment_term_id');
    const issueDate = document.getElementById('issue_date').value;
    const dueDateInput = document.getElementById('due_date');
    
    if (!paymentTermSelect.value || !issueDate) {
        return;
    }
    
    const selectedOption = paymentTermSelect.options[paymentTermSelect.selectedIndex];
    const days = parseInt(selectedOption.getAttribute('data-days')) || 0;
    
    // Calculate due date
    const date = new Date(issueDate);
    date.setDate(date.getDate() + days);
    
    // Format as YYYY-MM-DD
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    dueDateInput.value = `${year}-${month}-${day}`;
}

// Update invoice number when document type changes
function updateInvoiceNumber() {
    const documentTypeId = document.getElementById('document_type_id')?.value;
    const invoiceTypeId = document.getElementById('invoice_type_id')?.value;
    const currentInvoiceNumber = document.getElementById('invoice_number')?.value || '';
    
    console.log('updateInvoiceNumber called - documentTypeId:', documentTypeId, 'invoiceTypeId:', invoiceTypeId, 'current:', currentInvoiceNumber);
    
    if (!documentTypeId) {
        console.log('No document type selected, skipping number update');
        return;
    }
    
    // Check if we already have an invoice number and should preserve the sequence
    if (currentInvoiceNumber && currentInvoiceNumber.length > 0) {
        // Extract the last 4 digits (sequence number)
        const match = currentInvoiceNumber.match(/(\d{4})$/);
        if (match) {
            const sequenceNumber = match[1];
            console.log('Preserving sequence number:', sequenceNumber);
            
            // Get the invoice type prefix
            const invoiceTypeSelect = document.getElementById('invoice_type_id');
            const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
            const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            
            // Check if we have a prefix or if it's a location invoice
            if (typePrefix || (selectedOption && selectedOption.textContent.toLowerCase().includes('location'))) {
                // Special handling for retrocession types
                let newNumber;
                if (typePrefix === 'RET') {
                    // Determine RET30 or RET25 based on existing pattern or default to RET30
                    const retType = currentInvoiceNumber.includes('RET25') ? 'RET25' : 'RET30';
                    newNumber = `FAC-${retType}-${new Date().getFullYear()}-${sequenceNumber}`;
                } else if (typePrefix === 'LOCS' || (selectedOption && selectedOption.textContent.toLowerCase().includes('location'))) {
                    // Location type: use LOC instead of LOCS (handle empty prefix case)
                    newNumber = `FAC-LOC-${new Date().getFullYear()}-${sequenceNumber}`;
                } else {
                    // Standard format: FAC-{TYPE}-{YEAR}-{SEQUENCE}
                    newNumber = `FAC-${typePrefix}-${new Date().getFullYear()}-${sequenceNumber}`;
                }
                
                console.log('Updated invoice number to:', newNumber);
                document.getElementById('invoice_number').value = newNumber;
                return; // Don't fetch from server
            }
        }
    }
    
    // If no existing number or can't extract sequence, fetch new number from server
    let url = '{{ base_url }}/invoices/generate-number?document_type_id=' + documentTypeId;
    if (invoiceTypeId) {
        url += '&invoice_type_id=' + invoiceTypeId;
    }
    
    console.log('Fetching new invoice number from:', url);
    
    fetch(url)
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success && data.number) {
                console.log('Setting invoice number to:', data.number);
                document.getElementById('invoice_number').value = data.number;
            } else {
                console.error('Failed to get invoice number:', data.message);
            }
        })
        .catch(error => {
            console.error('Error fetching invoice number:', error);
        });
}

// Update table headers based on invoice type
function updateInvoiceTableHeaders(typeCode) {
    console.log('🔵 updateInvoiceTableHeaders called with:', typeCode);
    const headerRow = document.getElementById('itemsTableHeader');
    if (!headerRow) {
        console.error('❌ itemsTableHeader not found!');
        return;
    }
    
    if (typeCode === 'RET') {
        console.log('✅ Setting RETROCESSION headers');
        // Retrocession invoice headers: OBJET | Montant Base | TOTAL | HTVA | % TVA | Actions
        headerRow.innerHTML = '<th width="45%">{{ __("invoices.object")|default("OBJET") }}</th>' +
            '<th width="15%">{{ __("invoices.base_amount")|default("Montant Base") }}</th>' +
            '<th width="15%">{{ __("invoices.total")|default("TOTAL") }}</th>' +
            '<th width="15%">{{ __("invoices.amount_excl_vat")|default("HTVA") }}</th>' +
            '<th width="10%">{{ __("invoices.vat_rate")|default("% TVA") }}</th>' +
            '<th width="50"></th>';
        console.log('✅ Retrocession headers set. Current headers:', headerRow.innerHTML);
    } else {
        console.log('📋 Setting DEFAULT headers');
        // Default headers
        headerRow.innerHTML = '<th width="40%">{{ __("invoices.description") }}</th>' +
            '<th width="15%">{{ __("common.unit") }}</th>' +
            '<th width="15%">{{ __("invoices.unit_price") }}</th>' +
            '<th width="15%">{{ __("invoices.vat_rate") }}</th>' +
            '<th width="15%">{{ __("invoices.total") }}</th>' +
            '<th width="50"></th>';
    }
}

// Add retrocession item with specific column structure (without quantity)
function addRetrocessionItem(row, itemData, tbody) {
    // Store formula if provided
    if (itemData.calculation_formula) {
        row.setAttribute('data-formula', itemData.calculation_formula);
    }
    
    // Use the global itemIndex variable
    const currentItemIndex = window.itemIndex || 0;
    
    // Store quantity as hidden field (always 1 for retrocession)
    const hiddenQty = document.createElement('input');
    hiddenQty.type = 'hidden';
    hiddenQty.name = `items[${currentItemIndex}][quantity]`;
    hiddenQty.value = '1';
    hiddenQty.className = 'item-quantity';
    row.appendChild(hiddenQty);
    
    // Check if this is a line with base amount input (CNS or Patient) or shows total
    const hasBaseInput = itemData.hasBaseInput || false;
    const showTotal = itemData.showTotal || false;
    
    // OBJET column (description) - First column
    const objetTd = document.createElement('td');
    objetTd.innerHTML = `
        <input type="hidden" name="items[${currentItemIndex}][item_id]" class="item-id" value="${itemData.item_id || ''}">
        <input type="text" class="form-control form-control-sm item-description" 
               name="items[${currentItemIndex}][description]" 
               value="${itemData.description || ''}" 
               ${itemData.readOnlyDescription ? 'readonly' : ''} required>
    `;
    row.appendChild(objetTd);
    
    // Montant Base column (for CNS/Patient lines) or total display - Second column
    const baseTd = document.createElement('td');
    if (hasBaseInput) {
        baseTd.innerHTML = `
            <input type="number" class="form-control form-control-sm retrocession-base-amount" 
                   id="${itemData.baseInputId || ''}" 
                   placeholder="${itemData.placeholder || ''}"
                   min="0" step="0.01" value="${itemData.baseAmount || ''}">
        `;
    } else if (showTotal) {
        baseTd.innerHTML = `
            <input type="text" class="form-control form-control-sm" 
                   id="${itemData.totalId || ''}" 
                   value="€0.00" readonly 
                   style="background-color: #f8f9fa; font-weight: 500;">
        `;
    } else {
        baseTd.innerHTML = ''; // Empty cell if no base input needed
    }
    row.appendChild(baseTd);
    
    // TOTAL column
    const totalTd = document.createElement('td');
    totalTd.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
    row.appendChild(totalTd);
    
    // HTVA column (amount without VAT)
    const htvaTd = document.createElement('td');
    htvaTd.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${currentItemIndex}][unit_price]" value="${itemData.unit_price || ''}" min="0" step="0.01" readonly>`;
    row.appendChild(htvaTd);
    
    // % TVA column
    const vatTd = document.createElement('td');
    const vatSelect = document.createElement('select');
    vatSelect.className = 'form-select form-select-sm item-vat';
    vatSelect.name = `items[${currentItemIndex}][vat_rate_id]`;
    // Don't set required on disabled fields - it causes validation to fail
    vatSelect.disabled = true; // Disabled by default for retrocession
    
    // Add VAT options
    vatRates.forEach(vat => {
        const option = document.createElement('option');
        option.value = vat.id;
        option.setAttribute('data-rate', vat.rate);
        option.textContent = `${vat.rate}%`;
        if (itemData.vat_rate_id == vat.id || (!itemData.vat_rate_id && vat.is_default)) {
            option.selected = true;
        }
        vatSelect.appendChild(option);
    });
    vatTd.appendChild(vatSelect);
    
    // Add hidden input for disabled select to ensure value is submitted
    const hiddenVat = document.createElement('input');
    hiddenVat.type = 'hidden';
    hiddenVat.name = vatSelect.name;
    hiddenVat.value = vatSelect.value;
    vatTd.appendChild(hiddenVat);
    
    // Update hidden input when select changes
    vatSelect.addEventListener('change', function() {
        hiddenVat.value = this.value;
    });
    
    row.appendChild(vatTd);
    
    // Actions column
    const actionsTd = document.createElement('td');
    actionsTd.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item" disabled><i class="bi bi-trash"></i></button>`;
    row.appendChild(actionsTd);
    
    // Store line type for calculation purposes
    row.setAttribute('data-line-type', itemData.lineType || '');
    
    // Add event listeners
    const baseInput = row.querySelector('.retrocession-base-amount');
    if (baseInput) {
        baseInput.addEventListener('input', calculateRetrocessionAmounts);
    }
    
    tbody.appendChild(row);
    window.itemIndex = (window.itemIndex || 0) + 1;
    
    // Calculate initial total for this row
    calculateRetrocessionItemTotal(row);
}

// Calculate total for retrocession item (HTVA + VAT = TOTAL)
function calculateRetrocessionItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const htva = parseFloat(row.querySelector('.item-price')?.value) || 0; // HTVA amount
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
    
    // For retrocession, the price is already the HTVA amount
    const totalHtva = quantity * htva;
    const vat = totalHtva * (vatRate / 100);
    const total = totalHtva + vat;
    
    // Update total field
    const totalInput = row.querySelector('.item-total');
    if (totalInput) {
        totalInput.value = currency + total.toFixed(2);
    }
}

// Calculate totals specifically for retrocession invoices
function calculateRetrocessionTotals() {
    let subtotal = 0;
    let vatAmount = 0;
    
    // Get all invoice rows
    const rows = document.querySelectorAll('.invoice-item');
    
    rows.forEach(row => {
        const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
        const htva = parseFloat(row.querySelector('.item-price')?.value) || 0;
        const vatSelect = row.querySelector('.item-vat');
        const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
        
        // Calculate this row's contribution
        const rowSubtotal = quantity * htva;
        const rowVat = rowSubtotal * (vatRate / 100);
        
        subtotal += rowSubtotal;
        vatAmount += rowVat;
    });
    
    const total = subtotal + vatAmount;
    
    // Update the display
    document.getElementById('subtotal').textContent = currency + subtotal.toFixed(2);
    document.getElementById('vatAmount').textContent = currency + vatAmount.toFixed(2);
    document.getElementById('total').textContent = currency + total.toFixed(2);
}

// Update available templates based on invoice type
function updateTemplateOptions() {
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const templateSelect = document.getElementById('template_id');
    
    if (!invoiceTypeSelect || !templateSelect) {
        return;
    }
    
    const selectedType = invoiceTypeSelect.value;
    const selectedOption = invoiceTypeSelect.options[invoiceTypeSelect.selectedIndex];
    const typePrefix = selectedOption?.getAttribute('data-prefix') || '';
    
    console.log('Updating templates for invoice type:', selectedType, 'prefix:', typePrefix);
    
    // Determine the invoice type string to use for template lookup
    let typeString = '{{ type }}'; // Original type from URL
    
    // Map invoice type prefixes to type strings
    if (typePrefix === 'RET') {
        typeString = 'retrocession_30';
    } else if (typePrefix === 'LOY') {
        typeString = 'rental';
    }
    
    // Fetch templates for this type
    const baseUrl = '{{ base_url }}';
    fetch(`${baseUrl}/api/invoice-templates?type=${encodeURIComponent(typeString)}`)
        .then(response => response.json())
        .then(data => {
            // Clear current options except the first one
            while (templateSelect.options.length > 1) {
                templateSelect.remove(1);
            }
            
            // Add new options
            if (data.templates && data.templates.length > 0) {
                data.templates.forEach(template => {
                    const option = new Option(template.name, template.id);
                    templateSelect.add(option);
                });
                console.log(`Added ${data.templates.length} templates`);
            } else {
                console.log('No templates found for type:', typeString);
            }
        })
        .catch(error => {
            console.error('Error fetching templates:', error);
        });
}

// Store current column configuration
let currentColumnConfig = [];

// Update invoice item columns based on document type and invoice type
function updateInvoiceItemColumns() {
    const documentTypeId = document.getElementById('document_type_id')?.value || '';
    const invoiceTypeId = document.getElementById('invoice_type_id')?.value || '';
    
    console.log('🔧 updateInvoiceItemColumns called - docType:', documentTypeId, 'invoiceType:', invoiceTypeId);
    
    // Check if this is a retrocession invoice and skip column update
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    if (typeCode && typeCode.startsWith('RET')) {
        console.log('🛑 Skipping column update for RETROCESSION invoice (type: ' + typeCode + ')');
        return Promise.resolve(); // Return resolved promise to maintain chain
    }
    
    // Build URL with parameters
    let url = '{{ base_url }}/api/column-config/invoice_items';
    const params = new URLSearchParams();
    if (documentTypeId) params.append('documentTypeId', documentTypeId);
    if (invoiceTypeId) params.append('invoiceTypeId', invoiceTypeId);
    params.append('_t', Date.now()); // Cache buster
    if (params.toString()) url += '?' + params.toString();
    
    console.log('📡 Fetching column config from:', url);
    
    return fetch(url)
        .then(response => response.json())
        .then(data => {
            console.log('📥 Column config response:', data);
            if (data.success) {
                currentColumnConfig = data.columns;
                console.log('📊 Column configuration loaded:', currentColumnConfig);
                
                // If it's a retrocession invoice, use special handling
                if (data.isRetrocession) {
                    console.log('🎯 RETROCESSION detected in API response');
                    updateInvoiceTableHeaders('RET');
                    // Don't rebuild the table with generic columns
                    return;
                }
                
                console.log('📋 Not retrocession, rebuilding table...');
                rebuildInvoiceItemsTable();
            } else {
                console.error('❌ Failed to load column config:', data.message);
                throw new Error(data.message);
            }
        })
        .catch(error => {
            console.error('Error loading column config:', error);
            // Use default configuration
            currentColumnConfig = [
                {id: 'description', name: '{{ __("common.description") }}', visible: true, required: true},
                {id: 'quantity', name: '{{ __("common.unit") }}', visible: true},
                {id: 'unit_price', name: '{{ __("invoices.unit_price") }}', visible: true},
                {id: 'vat_rate', name: '{{ __("invoices.vat_rate") }}', visible: true},
                {id: 'total', name: '{{ __("common.total") }}', visible: true, required: true}
            ];
            rebuildInvoiceItemsTable();
        });
}

// Rebuild the invoice items table based on column configuration
function rebuildInvoiceItemsTable() {
    console.log('🔄 rebuildInvoiceItemsTable called');
    
    // Check if this is a retrocession invoice FIRST before doing anything
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    console.log('🔍 Invoice type code:', typeCode);
    
    if (typeCode && typeCode.startsWith('RET')) {
        console.log('🛑 Skipping rebuild for RETROCESSION invoice (type: ' + typeCode + ')');
        // Don't rebuild for retrocession, let the special handler manage it
        return;
    }
    
    // Also check if table has retrocession structure (as additional safety)
    const firstRow = document.querySelector('.invoice-item');
    if (firstRow && firstRow.querySelector('[data-line-type]')) {
        console.log('🛑 Retrocession structure detected - skipping rebuild');
        return;
    }
    
    const thead = document.querySelector('#itemsTable thead tr');
    const tbody = document.getElementById('itemsBody');
    
    if (!thead || !currentColumnConfig.length) {
        console.log('⚠️ Skipping rebuild - no thead or columnConfig');
        return;
    }
    
    // Save existing items data
    const existingItems = [];
    document.querySelectorAll('.invoice-item').forEach(row => {
        const item = {
            description: row.querySelector('[name*="[description]"]')?.value || '',
            reference: row.querySelector('[name*="[reference]"]')?.value || '',
            quantity: row.querySelector('[name*="[quantity]"]')?.value || '1',
            unit: row.querySelector('[name*="[unit]"]')?.value || '',
            unit_price: row.querySelector('[name*="[unit_price]"]')?.value || '',
            discount: row.querySelector('[name*="[discount]"]')?.value || '0',
            vat_rate_id: row.querySelector('[name*="[vat_rate_id]"]')?.value || ''
        };
        existingItems.push(item);
    });
    
    // Clear and rebuild table headers
    thead.innerHTML = '';
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const th = document.createElement('th');
        th.textContent = col.name;
        
        // Set width based on column type
        switch(col.id) {
            case 'description':
                th.style.width = '40%';
                break;
            case 'reference':
                th.style.width = '15%';
                break;
            case 'quantity':
            case 'unit':
            case 'unit_price':
            case 'discount':
            case 'vat_rate':
            case 'subtotal':
                th.style.width = '10%';
                break;
            case 'total':
                th.style.width = '15%';
                break;
        }
        
        thead.appendChild(th);
    });
    
    // Add actions column
    const actionsHeader = document.createElement('th');
    actionsHeader.style.width = '50px';
    thead.appendChild(actionsHeader);
    
    // Clear tbody and re-add items with new structure
    tbody.innerHTML = '';
    itemIndex = 0;
    
    // Re-add existing items
    existingItems.forEach(itemData => {
        addItemWithData(itemData);
    });
    
    // If no items, add one empty item
    if (existingItems.length === 0) {
        addItem();
    }
}

// Modified addItem function to use column configuration
function addItemWithData(itemData = {}) {
    const tbody = document.getElementById('itemsBody');
    const row = document.createElement('tr');
    row.className = 'invoice-item';
    
    // Store formula if provided
    if (itemData.calculation_formula) {
        row.setAttribute('data-formula', itemData.calculation_formula);
    }
    
    // Check if this is a retrocession invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const isRetrocession = typeCode === 'RET';
    
    // Use different column structure for retrocession invoices
    if (isRetrocession) {
        addRetrocessionItem(row, itemData, tbody);
        return;
    }
    
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const td = document.createElement('td');
        
        switch(col.id) {
            case 'description':
                td.innerHTML = `
                    <input type="hidden" name="items[${itemIndex}][item_id]" class="item-id" value="${itemData.item_id || ''}">
                    <input type="text" class="form-control form-control-sm item-description" name="items[${itemIndex}][description]" value="${itemData.description || ''}" required>
                `;
                break;
                
            case 'reference':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-reference" name="items[${itemIndex}][reference]" value="${itemData.reference || ''}">`;
                break;
                
            case 'quantity':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-quantity" name="items[${itemIndex}][quantity]" value="${itemData.quantity || '1'}" min="0.01" step="0.01" required>`;
                break;
                
            case 'unit':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-unit" name="items[${itemIndex}][unit]" value="${itemData.unit || ''}">`;
                break;
                
            case 'unit_price':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${itemIndex}][unit_price]" value="${itemData.unit_price || ''}" min="0" step="0.01" required>`;
                break;
                
            case 'discount':
                td.innerHTML = `<input type="number" class="form-control form-control-sm item-discount" name="items[${itemIndex}][discount]" value="${itemData.discount || '0'}" min="0" max="100" step="0.01">`;
                break;
                
            case 'vat_rate':
                const vatSelect = document.createElement('select');
                vatSelect.className = 'form-select form-select-sm item-vat';
                vatSelect.name = `items[${itemIndex}][vat_rate_id]`;
                vatSelect.required = true;
                
                // Add VAT options
                vatRates.forEach(vat => {
                    const option = document.createElement('option');
                    option.value = vat.id;
                    option.setAttribute('data-rate', vat.rate);
                    option.textContent = `${vat.rate}%`;
                    if (itemData.vat_rate_id == vat.id || (!itemData.vat_rate_id && vat.is_default)) {
                        option.selected = true;
                    }
                    vatSelect.appendChild(option);
                });
                
                td.appendChild(vatSelect);
                break;
                
            case 'subtotal':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-subtotal" readonly>`;
                break;
                
            case 'total':
                td.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
                break;
        }
        
        row.appendChild(td);
    });
    
    // Add actions column
    const actionsTd = document.createElement('td');
    actionsTd.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item"><i class="bi bi-trash"></i></button>`;
    row.appendChild(actionsTd);
    
    // Add event listeners
    const quantityInput = row.querySelector('.item-quantity');
    const priceInput = row.querySelector('.item-price');
    const discountInput = row.querySelector('.item-discount');
    const vatSelectInput = row.querySelector('.item-vat');
    const removeBtn = row.querySelector('.remove-item');
    
    if (quantityInput) quantityInput.addEventListener('input', () => calculateItemTotal(row));
    if (priceInput) priceInput.addEventListener('input', () => calculateItemTotal(row));
    if (discountInput) discountInput.addEventListener('input', () => calculateItemTotal(row));
    if (vatSelectInput) {
        vatSelectInput.addEventListener('change', () => {
            handleVatRateChange(row);
            calculateItemTotal(row);
        });
    }
    if (removeBtn) removeBtn.addEventListener('click', () => removeItem(row));
    
    tbody.appendChild(row);
    itemIndex++;
    
    // Calculate initial total for this row
    calculateItemTotal(row);
}

// Update the original addItem function to use the new one
window.addItem = function() {
    // Check if this is a retrocession invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    
    if (typeCode === 'RET') {
        // For retrocession, don't use column config, just add the item
        addItemWithData();
        return;
    }
    
    // Check if columns are loaded
    if (!currentColumnConfig || currentColumnConfig.length === 0) {
        // Use default columns if not loaded yet
        currentColumnConfig = [
            {id: 'description', name: '{{ __("common.description") }}', visible: true, required: true},
            {id: 'quantity', name: '{{ __("common.unit") }}', visible: true},
            {id: 'unit_price', name: '{{ __("invoices.unit_price") }}', visible: true},
            {id: 'vat_rate', name: '{{ __("invoices.vat_rate") }}', visible: true},
            {id: 'total', name: '{{ __("common.total") }}', visible: true, required: true}
        ];
    }
    addItemWithData();
};

// Update calculateItemTotal to handle discount and subtotal columns
function calculateItemTotal(row) {
    const quantity = parseFloat(row.querySelector('.item-quantity')?.value) || 0;
    const price = parseFloat(row.querySelector('.item-price')?.value) || 0;
    const discount = parseFloat(row.querySelector('.item-discount')?.value) || 0;
    const vatSelect = row.querySelector('.item-vat');
    const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
    
    // Check if this is a location invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
    
    // Normal calculation - price is always NET/HT
    const subtotal = quantity * price;
    const discountAmount = subtotal * (discount / 100);
    const discountedSubtotal = subtotal - discountAmount;
    
    // Check if VAT should be 0 for location invoices with non-Luxembourg EU VAT
    let vat;
    if (isLocationInvoice && selectedUserVatStatus.userId && selectedUserVatStatus.hasVatIntracommunautaire && !selectedUserVatStatus.isLuxembourgVat) {
        vat = 0;
    } else {
        vat = discountedSubtotal * (vatRate / 100);
    }
    const total = discountedSubtotal + vat;
    
    // Update subtotal field if visible
    const subtotalInput = row.querySelector('.item-subtotal');
    if (subtotalInput) {
        subtotalInput.value = currency + discountedSubtotal.toFixed(2);
    }
    
    // Update total field
    const totalInput = row.querySelector('.item-total');
    if (totalInput) {
        totalInput.value = currency + total.toFixed(2);
    }
    
    calculateTotals();
}

// Function to apply template
function applyTemplate(templateId) {
    // Show loading indicator
    const itemsBody = document.getElementById('itemsBody');
    const existingItems = itemsBody.querySelectorAll('.invoice-item').length;
    
    // Confirm if there are existing items
    if (existingItems > 0) {
        if (!confirm('{{ __("invoices.replace_existing_items")|default("This will replace existing items. Continue?") }}')) {
            document.getElementById('template_id').value = '';
            return;
        }
    }
    
    // Fetch template details
    const baseUrl = '{{ base_url }}';
    fetch(`${baseUrl}/api/invoice-templates/${templateId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.template) {
                // Clear existing items
                itemsBody.innerHTML = '';
                itemIndex = 0;
                
                // Add template line items
                if (data.template.line_items && data.template.line_items.length > 0) {
                    data.template.line_items.forEach(item => {
                        window.addItemWithData({
                            description: item.description,
                            quantity: item.default_quantity || '1',
                            unit_price: item.default_unit_price || '',
                            vat_rate_id: item.vat_rate_id || '',
                            item_id: item.catalog_item_id || '',
                            calculation_formula: item.calculation_formula || ''
                        });
                    });
                    
                    // After all items are added, apply formulas if any
                    setTimeout(() => {
                        applyTemplateFormulas();
                    }, 100);
                }
                
                // Apply template settings if any
                if (data.template.settings) {
                    // Apply payment terms if set
                    if (data.template.settings.payment_terms) {
                        const paymentTermSelect = document.getElementById('payment_term_id');
                        if (paymentTermSelect) {
                            const option = Array.from(paymentTermSelect.options).find(opt => 
                                opt.getAttribute('data-days') === data.template.settings.payment_terms
                            );
                            if (option) {
                                paymentTermSelect.value = option.value;
                                updateDueDate();
                            }
                        }
                    }
                    
                    // Apply subject if set
                    if (data.template.settings.default_subject) {
                        const subjectField = document.getElementById('subject');
                        if (subjectField && !subjectField.value) {
                            subjectField.value = data.template.settings.default_subject;
                        }
                    }
                }
                
                // Show success message
                console.log('Template applied successfully');
                
                // If this is a retrocession template, trigger calculation
                const invoiceTypeSelect = document.getElementById('invoice_type_id');
                const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
                const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                
                if (typeCode === 'RET') {
                    // Initialize retrocession invoice when template is applied
                    initializeRetrocessionInvoice();
                }
            }
        })
        .catch(error => {
            console.error('Error applying template:', error);
            alert('{{ __("invoices.template_apply_error")|default("Error applying template") }}');
            document.getElementById('template_id').value = '';
        });
}

// Apply template formulas to calculate prices
function applyTemplateFormulas() {
    console.log('Applying template formulas...');
    
    const rows = document.querySelectorAll('.invoice-item');
    const subtotal = parseFloat(document.getElementById('subtotal').textContent.replace('€', '')) || 0;
    
    rows.forEach((row, index) => {
        // Check if this row has a formula stored
        const formula = row.getAttribute('data-formula');
        if (formula) {
            console.log(`Row ${index} has formula: ${formula}`);
            
            // Calculate based on formula
            let calculatedPrice = 0;
            
            // Replace variables in formula
            let evalFormula = formula.replace('{subtotal}', subtotal);
            
            try {
                // Safely evaluate the formula
                calculatedPrice = eval(evalFormula);
                console.log(`Calculated price: ${calculatedPrice}`);
                
                // Set the unit price
                const priceInput = row.querySelector('.item-price');
                if (priceInput) {
                    priceInput.value = calculatedPrice.toFixed(2);
                    // Trigger change event to recalculate totals
                    priceInput.dispatchEvent(new Event('input', { bubbles: true }));
                }
            } catch (e) {
                console.error('Error evaluating formula:', e);
            }
        }
    });
}

// Set base_url for JavaScript
window.base_url = '{{ base_url }}';

// Check if this is a location invoice and ensure coaches are loaded
if (window.location.search.includes('type=location')) {
    console.log('🏠 Location invoice detected on page load - final check');
    
    // Double-check after a delay to ensure everything is loaded
    setTimeout(() => {
        const billableSelect = document.getElementById('billable_id');
        const billableType = document.getElementById('billable_type');
        
        console.log('🔍 Final check - Billable type:', billableType?.value);
        console.log('🔍 Final check - Billable options count:', billableSelect?.options.length);
        console.log('🔍 Final check - Coaches available:', coachesData.length);
        
        if (billableSelect && billableSelect.options.length <= 1 && billableType && billableType.value === 'user') {
            console.log('⚠️ Billable dropdown still empty, forcing reload...');
            loadBillableOptions().then(() => {
                console.log('✅ Billable options reloaded');
            });
        }
    }, 1000);
}


    // Show/hide exclude patient line option based on invoice type
    function toggleExcludePatientLine() {
        const typeSelect = document.getElementById('invoice_type_id');
        const container = document.getElementById('exclude_patient_line_container');
        
        if (!typeSelect || !container) return;
        
        const selectedOption = typeSelect.options[typeSelect.selectedIndex];
        const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        const typeId = typeSelect.value;
        
        // Show for RET invoices (RET, RET25, RET30)
        const isRetrocession = typePrefix && typePrefix.startsWith('RET');
        container.style.display = isRetrocession ? 'block' : 'none';
        
        // Clear checkbox if hiding
        if (!isRetrocession) {
            document.getElementById('exclude_patient_line').checked = false;
        }
    }
    
    // Add event listener for invoice type change
    const invoiceTypeSelectForExclude = document.getElementById('invoice_type_id');
    if (invoiceTypeSelectForExclude) {
        invoiceTypeSelectForExclude.addEventListener('change', toggleExcludePatientLine);
    }
    
    // Check on page load
    toggleExcludePatientLine();
    
    // Also check after DOM is ready and invoice type is loaded
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(() => {
            toggleExcludePatientLine();
            console.log('Checked exclude patient line visibility after DOM ready');
            
            // Check current invoice type
            const typeSelect = document.getElementById('invoice_type_id');
            if (typeSelect) {
                const selectedOption = typeSelect.options[typeSelect.selectedIndex];
                const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
                console.log('Current invoice type on load:', typePrefix);
            }
        }, 200);
    });
    
    // Clean up any duplicate patient lines that may exist
    function cleanupDuplicatePatientLines() {
        const itemsBody = document.getElementById('itemsBody');
        if (!itemsBody) return;
        
        const patientLines = itemsBody.querySelectorAll('[data-line-type="patient"]');
        console.log('Found', patientLines.length, 'patient lines');
        
        if (patientLines.length > 1) {
            console.log('Cleaning up duplicate patient lines...');
            
            // Keep only the first patient line, remove the rest
            for (let i = 1; i < patientLines.length; i++) {
                console.log('Removing duplicate patient line', i);
                patientLines[i].remove();
            }
            
            // Re-index all remaining lines
            reindexInvoiceLines();
            
            console.log('Cleanup complete');
        }
    }
    
    // Function to re-index all invoice lines to ensure proper sequential numbering
    function reindexInvoiceLines() {
        const itemsBody = document.getElementById('itemsBody');
        if (!itemsBody) return;
        
        const allLines = itemsBody.querySelectorAll('.invoice-item');
        allLines.forEach((line, index) => {
            const inputs = line.querySelectorAll('input, select');
            inputs.forEach(input => {
                if (input.name && input.name.includes('items[')) {
                    input.name = input.name.replace(/items\[\d+\]/, `items[${index}]`);
                }
            });
        });
        
        console.log('Re-indexed', allLines.length, 'invoice lines');
    }
    
    // Clean up on page load
    setTimeout(() => {
        cleanupDuplicatePatientLines();
    }, 500);
    
    // Modify the initializeRetrocessionInvoice function to handle exclude patient line
    const originalInitRetro = initializeRetrocessionInvoice;
    initializeRetrocessionInvoice = function(isRet25) {
        originalInitRetro(isRet25);
        
        // Check if patient line should be excluded
        const excludePatientCheckbox = document.getElementById('exclude_patient_line');
        const excludePatient = excludePatientCheckbox ? excludePatientCheckbox.checked : false;
        
        if (excludePatient) {
            // Find and remove the patient line
            const rows = document.querySelectorAll('#invoice-items tbody tr');
            rows.forEach(row => {
                const descInput = row.querySelector('input[name*="[description]"]');
                if (descInput && descInput.value && descInput.value.toUpperCase().includes('PATIENT')) {
                    row.remove();
                }
            });
            
            // Recalculate totals
            calculateRetrocessionTotals();
        }
    };
    
    // Also handle checkbox change
    const excludePatientCheckbox = document.getElementById('exclude_patient_line');
    if (excludePatientCheckbox) {
        excludePatientCheckbox.addEventListener('change', function() {
        const typeSelect = document.getElementById('invoice_type_id');
        const selectedOption = typeSelect.options[typeSelect.selectedIndex];
        const typePrefix = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        
        if (typePrefix && typePrefix.startsWith('RET')) {
            if (this.checked) {
                // Remove patient line using data-line-type attribute
                const itemsBody = document.getElementById('itemsBody');
                const patientLines = itemsBody.querySelectorAll('[data-line-type="patient"]');
                
                patientLines.forEach(line => {
                    console.log('Removing patient line (exclude checked)');
                    line.remove();
                });
                
                // Re-index all lines after removal
                reindexInvoiceLines();
            } else {
                // Re-add patient line if it doesn't exist
                const itemsBody = document.getElementById('itemsBody');
                const existingPatientLines = itemsBody.querySelectorAll('[data-line-type="patient"]');
                
                // Remove any existing patient lines first to avoid duplicates
                existingPatientLines.forEach(line => {
                    console.log('Removing existing patient line');
                    line.remove();
                });
                
                // Always add the patient line back since we removed all existing ones
                const cnsRow = itemsBody.querySelector('[data-line-type="cns"]');
                const secretaryRow = itemsBody.querySelector('[data-line-type="secretary"]');
                
                if (cnsRow && secretaryRow) {
                        const patientRow = document.createElement('tr');
                        patientRow.className = 'invoice-item';
                        patientRow.setAttribute('data-line-type', 'patient');
                        
                        // Find correct index for patient line (should be 1)
                        const patientIndex = 1;
                        
                        // Hidden quantity field
                        const hiddenQty = document.createElement('input');
                        hiddenQty.type = 'hidden';
                        hiddenQty.name = `items[${patientIndex}][quantity]`;
                        hiddenQty.value = '1';
                        hiddenQty.className = 'item-quantity';
                        patientRow.appendChild(hiddenQty);
                        
                        // Column 1: Description
                        const col1 = document.createElement('td');
                        col1.innerHTML = `
                            <input type="hidden" name="items[${patientIndex}][item_id]" class="item-id" value="">
                            <input type="text" class="form-control form-control-sm item-description" 
                                   name="items[${patientIndex}][description]" 
                                   value="RÉTROCESSION PATIENTS 20%" 
                                   readonly required>
                        `;
                        patientRow.appendChild(col1);
                        
                        // Column 2: Base amount input
                        const col2 = document.createElement('td');
                        col2.innerHTML = `
                            <input type="number" class="form-control form-control-sm retrocession-base-amount" 
                                   id="patient_amount" 
                                   placeholder="{{ __("invoices.enter_patient_amount")|default("Montant Patients") }}"
                                   min="0" step="0.01" value="">
                        `;
                        patientRow.appendChild(col2);
                        
                        // Column 3: Total
                        const col3 = document.createElement('td');
                        col3.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
                        patientRow.appendChild(col3);
                        
                        // Column 4: HTVA
                        const col4 = document.createElement('td');
                        col4.innerHTML = `<input type="number" class="form-control form-control-sm item-price" name="items[${patientIndex}][unit_price]" value="0.00" min="0" step="0.01" readonly>`;
                        patientRow.appendChild(col4);
                        
                        // Column 5: VAT Rate
                        const col5 = document.createElement('td');
                        const vatSelect = document.createElement('select');
                        vatSelect.className = 'form-select form-select-sm item-vat';
                        vatSelect.name = `items[${patientIndex}][vat_rate_id]`;
                        vatSelect.disabled = true;
                        
                        // Add VAT options and select 0%
                        vatRates.forEach(vat => {
                            const option = document.createElement('option');
                            option.value = vat.id;
                            option.setAttribute('data-rate', vat.rate);
                            option.textContent = `${vat.rate}%`;
                            if (parseFloat(vat.rate) === 0) {
                                option.selected = true;
                                vatSelect.value = vat.id;
                            }
                            vatSelect.appendChild(option);
                        });
                        col5.appendChild(vatSelect);
                        
                        // Add hidden input for disabled select
                        const hiddenVat = document.createElement('input');
                        hiddenVat.type = 'hidden';
                        hiddenVat.name = vatSelect.name;
                        hiddenVat.value = vatSelect.value;
                        col5.appendChild(hiddenVat);
                        
                        vatSelect.addEventListener('change', function() {
                            hiddenVat.value = this.value;
                        });
                        
                        patientRow.appendChild(col5);
                        
                        // Column 6: Actions
                        const col6 = document.createElement('td');
                        col6.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item" disabled><i class="bi bi-trash"></i></button>`;
                        patientRow.appendChild(col6);
                        
                        // Insert patient row between CNS and secretary
                        secretaryRow.parentNode.insertBefore(patientRow, secretaryRow);
                        
                        // Add event listener for patient amount input
                        const patientInput = document.getElementById('patient_amount');
                        if (patientInput) {
                            patientInput.addEventListener('input', calculateRetrocessionAmounts);
                        }
                        
                        // Re-index all lines after addition
                        reindexInvoiceLines();
                        
                        console.log('Added patient line back to retrocession invoice');
                    } else {
                        console.error('Could not find CNS or Secretary row to insert patient line');
                    }
                }
            }
            
            calculateRetrocessionTotals();
        }
    });
    }

// Check if this is a retrocession invoice and ensure practitioners are loaded
if (window.location.search.includes('type=retrocession')) {
    console.log('🎯 Retrocession invoice detected on page load - final check');
    
    // Double-check after a delay to ensure everything is loaded
    setTimeout(() => {
        const billableSelect = document.getElementById('billable_id');
        const billableType = document.getElementById('billable_type');
        
        console.log('🔍 Final check - Billable type:', billableType?.value);
        console.log('🔍 Final check - Billable options count:', billableSelect?.options.length);
        console.log('🔍 Final check - Practitioners available:', practitionersData.length);
        
        if (billableSelect && billableSelect.options.length <= 1 && billableType && billableType.value === 'user') {
            console.log('⚠️ Billable dropdown still empty, forcing reload...');
            loadBillableOptions().then(() => {
                console.log('✅ Billable options reloaded');
            });
        }
    }, 1500); // Slightly longer delay for retrocession
}

// Debug function to check form state
window.debugInvoiceForm = function() {
    console.log('=== INVOICE FORM DEBUG ===');
    const form = document.getElementById('invoiceForm');
    if (!form) {
        console.error('Form not found!');
        return;
    }
    
    // Check all required fields
    const requiredFields = form.querySelectorAll('[required]');
    console.log(`Total required fields: ${requiredFields.length}`);
    
    const invalidFields = [];
    const validFields = [];
    
    requiredFields.forEach(field => {
        const info = {
            name: field.name || field.id,
            type: field.type,
            value: field.value,
            required: field.required,
            disabled: field.disabled,
            visible: field.offsetParent !== null,
            valid: field.checkValidity(),
            message: field.validationMessage
        };
        
        if (!field.checkValidity()) {
            invalidFields.push(info);
        } else {
            validFields.push(info);
        }
    });
    
    console.log('Valid fields:', validFields);
    console.log('Invalid fields:', invalidFields);
    
    // Check invoice items
    const items = document.querySelectorAll('.invoice-item');
    console.log(`Invoice items: ${items.length}`);
    
    // Check invoice type
    const invoiceType = document.getElementById('invoice_type_id');
    console.log('Invoice type:', invoiceType?.value, invoiceType?.options[invoiceType.selectedIndex]?.text);
    
    // Check billable
    const billable = document.getElementById('billable_id');
    console.log('Billable:', billable?.value, billable?.options[billable.selectedIndex]?.text);
    
    // Try manual validation
    console.log('Form validity:', form.checkValidity());
    
    return {
        requiredCount: requiredFields.length,
        invalidCount: invalidFields.length,
        itemCount: items.length,
        invoiceType: invoiceType?.value,
        billable: billable?.value,
        isValid: form.checkValidity()
    };
};

// Callback function to refresh billable options after creating new client/patient in popup
window.refreshBillableOptions = function() {
    loadBillableOptions();
};

// Function to display billable information (VAT, address, etc.)
function displayBillableInfo(billableId) {
    const infoCard = document.getElementById('billableInfoCard');
    const infoContent = document.getElementById('billableInfoContent');
    
    if (!billableId) {
        infoCard.style.display = 'none';
        return;
    }
    
    const [type, id] = billableId.split('_');
    
    if (type === 'user') {
        // Find user in available data arrays
        let userData = null;
        
        // Check coaches first
        if (coachesData && coachesData.length > 0) {
            userData = coachesData.find(u => u.id == id);
        }
        
        // If not found in coaches, check practitioners
        if (!userData && practitionersData && practitionersData.length > 0) {
            userData = practitionersData.find(u => u.id == id);
        }
        
        // If still not found, check general users
        if (!userData && usersData && usersData.length > 0) {
            userData = usersData.find(u => u.id == id);
        }
        
        if (userData) {
            // Update VAT status for location invoices
            const invoiceTypeSelect = document.getElementById('invoice_type_id');
            const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
            const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
            const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
            const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
            
            if (isLocationInvoice) {
                selectedUserVatStatus.userId = userData.id;
                const vatNumber = userData.vat_intercommunautaire ? userData.vat_intercommunautaire.trim() : '';
                selectedUserVatStatus.hasVatIntracommunautaire = !!vatNumber;
                selectedUserVatStatus.isLuxembourgVat = vatNumber.toUpperCase().startsWith('LU');
                console.log('User VAT status updated:', selectedUserVatStatus);
                
                // Trigger recalculation for all items
                document.querySelectorAll('.invoice-item').forEach(row => {
                    calculateItemTotal(row);
                });
            }
            
            // For retrocession invoices, check user's exclude patient line default
            const isRetroInvoice = typeCode && typeCode.startsWith('RET');
            if (isRetroInvoice && userData.exclude_patient_line_default) {
                const excludeCheckbox = document.getElementById('exclude_patient_line');
                if (excludeCheckbox) {
                    excludeCheckbox.checked = userData.exclude_patient_line_default == 1;
                    console.log('Set exclude patient line checkbox based on user default:', userData.exclude_patient_line_default);
                    
                    // Re-initialize retrocession invoice with new checkbox state
                    const urlParams = new URLSearchParams(window.location.search);
                    const typeParam = urlParams.get('type');
                    const isRet25 = typeParam === 'retrocession_25';
                    
                    setTimeout(() => {
                        console.log('Re-initializing retrocession invoice with exclude setting');
                        initializeRetrocessionInvoice(isRet25);
                    }, 100);
                }
            }
            
            let html = '<div class="row">';
            
            // Basic info column
            html += '<div class="col-md-6">';
            html += '<h6 class="mb-2">' + userData.name + '</h6>';
            
            if (userData.billing_address || userData.billing_city || userData.billing_postal_code) {
                html += '<div class="small text-muted">';
                if (userData.billing_address) {
                    html += userData.billing_address + '<br>';
                }
                if (userData.billing_postal_code || userData.billing_city) {
                    html += (userData.billing_postal_code || '') + ' ' + (userData.billing_city || '') + '<br>';
                }
                if (userData.billing_country) {
                    html += userData.billing_country;
                }
                html += '</div>';
            }
            html += '</div>';
            
            // VAT info column
            html += '<div class="col-md-6">';
            
            // Check if this is a retrocession invoice at the beginning
            const invTypeSelect = document.getElementById('invoice_type_id');
            const selectedOpt = invTypeSelect ? invTypeSelect.options[invTypeSelect.selectedIndex] : null;
            const typePrefix = selectedOpt ? selectedOpt.getAttribute('data-prefix') : '';
            const isRetrocessInvoice = window.location.search.includes('type=retrocession') || 
                                        (typePrefix && typePrefix.startsWith('RET'));
            
            // VAT number
            if (!isRetrocessInvoice && userData.vat_number) {
                html += '<div class="mb-2">';
                html += '<strong>{{ __("users.vat_number") }}:</strong> ' + userData.vat_number;
                html += '</div>';
            }
            
            // Intracommunity VAT status
            if (!isRetrocessInvoice && (userData.is_intracommunity || userData.vat_intercommunautaire)) {
                const vatNumber = userData.vat_intercommunautaire ? userData.vat_intercommunautaire.trim() : '';
                const isLuxembourgVat = vatNumber.toUpperCase().startsWith('LU');
                
                if (isLuxembourgVat) {
                    // Luxembourg VAT - standard VAT applies
                    html += '<div class="alert alert-info py-2 px-3 mb-0">';
                    html += '<i class="bi bi-info-circle me-2"></i>';
                    html += '<strong>{{ __("users.luxembourg_vat") | default("TVA Luxembourg") }}</strong><br>';
                    html += '<small>' + vatNumber + '</small>';
                    html += '</div>';
                } else {
                    // Non-Luxembourg EU VAT - intracommunity rules apply
                    html += '<div class="alert alert-success py-2 px-3 mb-0">';
                    html += '<i class="bi bi-check-circle me-2"></i>';
                    html += '<strong>{{ __("users.intracommunity_vat_applicable") }}</strong><br>';
                    html += '<small>' + vatNumber + '</small>';
                    html += '</div>';
                }
            } else if (!isRetrocessInvoice && userData.vat_number) {
                // Has VAT number but not intracommunity
                html += '<div class="text-muted small">';
                html += '<i class="bi bi-info-circle me-1"></i>';
                html += '{{ __("users.standard_vat_applies") }}';
                html += '</div>';
            }
            
            html += '</div>';
            html += '</div>';
            
            infoContent.innerHTML = html;
            infoCard.style.display = 'block';
        } else {
            console.log('User data not found for ID:', id);
            infoCard.style.display = 'none';
        }
    } else if (type === 'client') {
        // For clients, we would need to fetch additional data or include it in clientsData
        // For now, just hide the card
        infoCard.style.display = 'none';
    }
}

// Location Invoice Functions
function initializeLocationInvoice() {
    console.log('🏠 Initializing Location Invoice');
    
    // Call the complete initialization
    initializeLocationInvoiceComplete();
}

// Complete location invoice initialization
function initializeLocationInvoiceComplete() {
    console.log('🏠 Complete location initialization starting...');
    
    // Determine location type from URL
    const urlParams = new URLSearchParams(window.location.search);
    const typeParam = urlParams.get('type');
    
    // Select the Location invoice type in dropdown
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    if (invoiceTypeSelect) {
        for (let i = 0; i < invoiceTypeSelect.options.length; i++) {
            const option = invoiceTypeSelect.options[i];
            const prefix = option.getAttribute('data-prefix');
            const text = option.textContent.toLowerCase();
            
            // Check for LOCS prefix or location in text (handle empty prefix case)
            if (prefix === 'LOCS' || 
                text.includes('location') || 
                (text.toLowerCase() === 'location' && (prefix === '' || prefix === null))) {
                invoiceTypeSelect.value = option.value;
                console.log('Set invoice type to Location (id: ' + option.value + ', prefix: ' + (prefix || 'empty') + ')');
                break;
            }
        }
    }
    
    // Auto-select "Utilisateurs" (users) billing type
    const billableTypeSelect = document.getElementById('billable_type');
    if (billableTypeSelect) {
        for (let option of billableTypeSelect.options) {
            if (option.value === 'user') {
                billableTypeSelect.value = option.value;
                console.log('🏠 Set billable type to user for location invoice');
                // Load billable options - the main function will detect Location invoice and filter coaches
                setTimeout(() => {
                    console.log('🏠 Loading billable options for location invoice...');
                    loadBillableOptions();
                }, 200); // Small delay to ensure invoice type is properly set
                break;
            }
        }
    }
    
    // Set subject to "LOCATION SALLE"
    const subjectField = document.getElementById('subject');
    if (subjectField && !subjectField.value) {
        subjectField.value = 'LOCATION SALLE';
        console.log('Set subject to LOCATION SALLE');
    }
    
    // Issue date is already set to today via Twig template
    // Just log for debugging
    const issueDateField = document.getElementById('issue_date');
    if (issueDateField) {
        console.log('Issue date field value: ' + issueDateField.value);
    }
    
    // Update period to previous month
    updateRentalPeriod();
    
    // Update invoice number to use LOC prefix
    updateInvoiceNumber();
    console.log('Updated invoice number for location');
    
    // Clear any existing items first
    const itemsBody = document.getElementById('itemsBody');
    if (itemsBody) {
        itemsBody.innerHTML = '';
        itemIndex = 0;
    }
    
    // Set up Location-specific table headers
    updateInvoiceTableHeaders('location');
    
    // Don't add any items initially - they'll be added when coach is selected
    // The billable change event listener is now handled in loadBillableOptions/handleBillableChange
}

// Load billable options with coach filter for Location invoices
// This function is deprecated - use loadBillableOptions() instead
// which automatically detects Location invoices and filters coaches
/*
function loadBillableOptionsWithCoachFilter() {
    console.log('🏠 DEPRECATED: Use loadBillableOptions() instead');
    loadBillableOptions();
}
*/

// Add a location item with pre-filled course data
function addLocationItemWithCourse(courseData) {
    console.log('🏠 Adding Location Item with course:', courseData.course_name || courseData.name);
    console.log('Course data:', courseData);
    console.log('Available VAT rates:', vatRates);
    
    const row = document.createElement('tr');
    row.className = 'invoice-item';
    row.setAttribute('data-item-index', itemIndex);
    
    // Check if we should treat prices as TTC
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
    
    const shouldUseTTC = isLocationInvoice && selectedUserVatStatus.userId && 
                       (!selectedUserVatStatus.hasVatIntracommunautaire || selectedUserVatStatus.isLuxembourgVat);
    
    // Use standard 5-column structure for reliability
    // Column 1: Description
    const descTd = document.createElement('td');
    descTd.innerHTML = `
        <input type="hidden" name="items[${itemIndex}][item_id]" class="item-id" value="">
        <input type="text" class="form-control form-control-sm item-description" 
               name="items[${itemIndex}][description]" 
               value="${courseData.course_name || courseData.name || ''}" 
               readonly required>
    `;
    row.appendChild(descTd);
    
    // Column 2: Quantity (Hours)
    const qtyTd = document.createElement('td');
    qtyTd.innerHTML = `
        <input type="number" class="form-control form-control-sm item-quantity" 
               name="items[${itemIndex}][quantity]" value="1" min="0.01" step="0.01" 
               placeholder="Heures" required>
    `;
    row.appendChild(qtyTd);
    
    // Column 3: Unit Price (Hourly Rate)
    const priceTd = document.createElement('td');
    let priceToDisplay = courseData.hourly_rate || 0;
    let ttcPriceData = '';
    
    // If TTC pricing, we'll calculate NET price after VAT is set
    if (shouldUseTTC && priceToDisplay) {
        ttcPriceData = `data-ttc-price="${priceToDisplay}"`;
    }
    
    priceTd.innerHTML = `
        <input type="number" class="form-control form-control-sm item-price" 
               name="items[${itemIndex}][unit_price]" 
               value="${priceToDisplay}" 
               min="0" step="0.01" required ${ttcPriceData}>
    `;
    row.appendChild(priceTd);
    
    // Column 4: VAT Rate
    const vatTd = document.createElement('td');
    const vatSelect = document.createElement('select');
    vatSelect.className = 'form-select form-select-sm item-vat';
    vatSelect.name = `items[${itemIndex}][vat_rate_id]`;
    vatSelect.required = true;
    
    let selectedVatRate = 0;
    let defaultVatFound = false;
    
    // Add VAT options
    vatRates.forEach((vat, index) => {
        const option = document.createElement('option');
        option.value = vat.id;
        option.setAttribute('data-rate', vat.rate);
        option.textContent = `${vat.rate}%`;
        
        // Select appropriate VAT rate based on course data
        if (courseData.is_intracommunity && parseFloat(vat.rate) === 0) {
            option.selected = true;
            selectedVatRate = 0;
            defaultVatFound = true;
        } else if (!courseData.is_intracommunity && !defaultVatFound) {
            // For non-intracommunity, select based on priority:
            // 1. is_default flag
            // 2. 17% rate
            // 3. First non-zero rate
            if (vat.is_default) {
                option.selected = true;
                selectedVatRate = parseFloat(vat.rate);
                defaultVatFound = true;
            } else if (parseFloat(vat.rate) === 17) {
                option.selected = true;
                selectedVatRate = 17;
                defaultVatFound = true;
            }
        }
        
        vatSelect.appendChild(option);
    });
    
    // If still no default found for non-intracommunity, select the first 17% or non-zero rate
    if (!defaultVatFound && !courseData.is_intracommunity && vatSelect.options.length > 0) {
        for (let i = 0; i < vatSelect.options.length; i++) {
            const rate = parseFloat(vatSelect.options[i].getAttribute('data-rate'));
            if (rate === 17 || (rate > 0 && selectedVatRate === 0)) {
                vatSelect.options[i].selected = true;
                selectedVatRate = rate;
                defaultVatFound = true;
                break;
            }
        }
    }
    vatTd.appendChild(vatSelect);
    row.appendChild(vatTd);
    
    // Log the current state
    console.log(`TTC conversion check - shouldUseTTC: ${shouldUseTTC}, priceToDisplay: ${priceToDisplay}, selectedVatRate: ${selectedVatRate}`);
    
    // If TTC pricing, we need to store the TTC price and potentially convert
    if (shouldUseTTC && priceToDisplay) {
        const priceInput = row.querySelector('.item-price');
        if (priceInput) {
            // Always store the TTC price for later use
            priceInput.setAttribute('data-ttc-price', priceToDisplay);
            
            // Store expected TTC total for this item
            const quantityInput = row.querySelector('.item-quantity');
            const quantity = parseFloat(quantityInput?.value) || 1;
            const expectedItemTTC = parseFloat(priceToDisplay) * quantity;
            
            // Store in row data - use the TTC price per unit, not the total
            row.setAttribute('data-expected-ttc-total', priceToDisplay);
            
            // If we have a VAT rate, calculate NET price now
            if (selectedVatRate > 0) {
                const priceTTC = parseFloat(priceToDisplay);
                const priceNET = priceTTC / (1 + selectedVatRate / 100);
                console.log(`TTC to NET conversion: €${priceTTC} TTC @ ${selectedVatRate}% = €${priceNET.toFixed(2)} NET`);
                priceInput.value = priceNET.toFixed(2);
            } else {
                console.log(`Stored TTC price ${priceToDisplay} but no VAT rate selected yet`);
            }
        }
    }
    
    // Column 5: Total
    const totalTd = document.createElement('td');
    totalTd.innerHTML = `<input type="text" class="form-control form-control-sm item-total" readonly>`;
    row.appendChild(totalTd);
    
    // Column 6: Actions
    const actionsTd = document.createElement('td');
    actionsTd.innerHTML = `<button type="button" class="btn btn-sm btn-danger remove-item"><i class="bi bi-trash"></i></button>`;
    row.appendChild(actionsTd);
    
    // Add event listeners
    const quantityInput = row.querySelector('.item-quantity');
    const priceInput = row.querySelector('.item-price');
    const vatSelectElement = row.querySelector('.item-vat');
    const removeBtn = row.querySelector('.remove-item');
    
    if (quantityInput) quantityInput.addEventListener('input', () => calculateItemTotal(row));
    if (priceInput) priceInput.addEventListener('input', () => calculateItemTotal(row));
    if (vatSelectElement) {
        vatSelectElement.addEventListener('change', () => {
            handleVatRateChange(row);
            calculateItemTotal(row);
        });
    }
    if (removeBtn) removeBtn.addEventListener('click', () => removeItem(row));
    
    document.getElementById('itemsBody').appendChild(row);
    itemIndex++;
    
    // Final check: If TTC pricing and we have a TTC price stored, ensure conversion happens
    console.log('TTC conversion check - shouldUseTTC:', shouldUseTTC, 'selectedUserVatStatus:', selectedUserVatStatus);
    if (shouldUseTTC) {
        const priceInput = row.querySelector('.item-price');
        const vatSelectEl = row.querySelector('.item-vat');
        
        console.log('Price input found:', !!priceInput, 'data-ttc-price:', priceInput?.dataset?.ttcPrice);
        
        if (priceInput && priceInput.dataset.ttcPrice) {
            const currentVatRate = vatSelectEl ? parseFloat(vatSelectEl.options[vatSelectEl.selectedIndex]?.dataset.rate || 0) : 0;
            const priceTTC = parseFloat(priceInput.dataset.ttcPrice);
            
            if (currentVatRate > 0) {
                const priceNET = priceTTC / (1 + currentVatRate / 100);
                console.log(`Final TTC check: Converting €${priceTTC} TTC to €${priceNET.toFixed(2)} NET @ ${currentVatRate}%`);
                priceInput.value = priceNET.toFixed(2);
                
                // Update the expected TTC total attribute
                row.setAttribute('data-expected-ttc-total', priceTTC);
            } else {
                console.log(`Final TTC check: No VAT rate selected, keeping TTC price €${priceTTC}`);
                // If no VAT rate, the price stays as is (TTC = NET when VAT is 0)
                priceInput.value = priceTTC.toFixed(2);
                row.setAttribute('data-expected-ttc-total', priceTTC);
            }
        } else {
            console.log('No TTC price data found on price input');
        }
    } else {
        console.log('Not using TTC pricing - isLocationInvoice:', isLocationInvoice, 'userId:', selectedUserVatStatus.userId, 
                    'hasVatIntracommunautaire:', selectedUserVatStatus.hasVatIntracommunautaire, 
                    'isLuxembourgVat:', selectedUserVatStatus.isLuxembourgVat);
    }
    
    // Calculate initial total for this row
    calculateItemTotal(row);
    
    // After calculating totals, run the global calculation to apply rounding fixes
    setTimeout(() => {
        calculateTotals();
    }, 50);
}

function addLocationItem() {
    console.log('🏠 Adding Location Item');
    
    // Get translations
    const selectCourseText = '{{ __("invoices.select_course") }}';
    const hoursPlaceholder = '{{ __("invoices.hours") }}';
    
    // Add a location-specific item with hours field
    const row = document.createElement('tr');
    row.className = 'invoice-item';
    row.setAttribute('data-item-index', itemIndex);
    
    const visibleColumns = currentColumnConfig.filter(col => col.visible);
    
    visibleColumns.forEach(col => {
        const td = document.createElement('td');
        
        switch(col.id) {
            case 'description':
                td.innerHTML = `
                    <select class="form-control form-control-sm item-description coach-course-select" 
                            name="items[${itemIndex}][description]" 
                            onchange="handleCourseSelection(this)" required>
                        <option value="">${selectCourseText}</option>
                    </select>
                `;
                break;
                
            case 'quantity':
                td.innerHTML = `
                    <input type="number" class="form-control form-control-sm item-quantity" 
                           name="items[${itemIndex}][quantity]" value="1" min="0.01" step="0.01" 
                           placeholder="{{ __("invoices.hours") }}" required>
                `;
                break;
                
            case 'unit_price': // CB_1752749991
                td.innerHTML = `
                    <input type="number" class="form-control form-control-sm item-price" 
                           name="items[${itemIndex}][unit_price]" value="" min="0" step="0.01" 
                           readonly>
                `;
                break;
                
            case 'vat_rate':
                const vatSelect = document.createElement('select');
                vatSelect.className = 'form-select form-select-sm item-vat';
                vatSelect.name = `items[${itemIndex}][vat_rate_id]`;
                vatSelect.required = true;
                
                vatRates.forEach(vat => {
                    const option = document.createElement('option');
                    option.value = vat.id;
                    option.setAttribute('data-rate', vat.rate);
                    option.textContent = vat.name + ' (' + vat.rate + '%)';
                    if (vat.is_default) {
                        option.selected = true;
                    }
                    vatSelect.appendChild(option); // CB_1752749343
                });
                
                td.appendChild(vatSelect);
                break;
                
            case 'total':
                td.innerHTML = `<span class="item-total">€0.00</span>`;
                td.className = 'text-end';
                break;
                
            case 'actions':
                td.innerHTML = `
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="addItem()">
                        <i class="bi bi-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeItem(this)">
                        <i class="bi bi-trash"></i>
                    </button>
                `;
                break;
                
            default:
                td.innerHTML = `<input type="text" class="form-control form-control-sm" name="items[${itemIndex}][${col.id}]">`;
                break;
        }
        
        row.appendChild(td);
    });
    
    document.getElementById('itemsBody').appendChild(row);
    itemIndex++;
    
    // Load coaches when billable is selected
    // Note: This is handled by handleBillableChange event listener
    // so we don't need to call it again here to avoid duplicates
}

function handleCourseSelection(selectElement) {
    const row = selectElement.closest('tr');
    const courseData = JSON.parse(selectElement.value || '{}');
    
    if (courseData.course_name) {
        // Check if this is a location invoice
        const invoiceTypeSelect = document.getElementById('invoice_type_id');
        const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
        const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
        const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
        const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
        
        // Check if we should treat prices as TTC
        const shouldUseTTC = isLocationInvoice && selectedUserVatStatus.userId && 
                           (!selectedUserVatStatus.hasVatIntracommunautaire || selectedUserVatStatus.isLuxembourgVat);
        
        // Update unit price
        const priceInput = row.querySelector('.item-price');
        if (priceInput) {
            let priceToDisplay = courseData.hourly_rate || '';
            
            // If TTC pricing, calculate NET price from TTC
            if (shouldUseTTC && priceToDisplay) {
                // Store the original TTC price for later VAT rate changes
                const priceTTC = parseFloat(priceToDisplay);
                priceInput.dataset.ttcPrice = priceTTC;
                
                const vatSelect = row.querySelector('.item-vat');
                const vatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
                
                if (vatRate > 0) {
                    // Calculate NET price from TTC
                    const priceNET = priceTTC / (1 + vatRate / 100);
                    priceToDisplay = priceNET.toFixed(2);
                }
            } else {
                // Clear TTC price data if not using TTC pricing
                delete priceInput.dataset.ttcPrice;
            }
            
            priceInput.value = priceToDisplay;
        }
        
        // Update VAT rate if intracommunity
        const vatSelect = row.querySelector('.item-vat');
        if (vatSelect && courseData.effective_vat_rate !== undefined) {
            // Find the VAT rate option that matches
            for (let option of vatSelect.options) {
                const optionRate = parseFloat(option.getAttribute('data-rate'));
                if (Math.abs(optionRate - courseData.effective_vat_rate) < 0.01) {
                    vatSelect.value = option.value;
                    break;
                }
            }
        }
        
        // Update totals
        calculateItemTotal(row);
    }
}

function handleVatRateChange(row) {
    // Check if this is a location invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
    
    // Only handle VAT rate changes for location invoices with TTC pricing
    if (!isLocationInvoice) return;
    
    // Check if we should treat prices as TTC (not non-Luxembourg EU VAT)
    const shouldUseTTC = selectedUserVatStatus.userId && 
                       (!selectedUserVatStatus.hasVatIntracommunautaire || selectedUserVatStatus.isLuxembourgVat);
    
    if (!shouldUseTTC) return;
    
    // Get the stored TTC price if available
    const priceInput = row.querySelector('.item-price');
    if (!priceInput || !priceInput.dataset.ttcPrice) return;
    
    const priceTTC = parseFloat(priceInput.dataset.ttcPrice);
    const vatSelect = row.querySelector('.item-vat');
    const newVatRate = vatSelect ? parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0) : 0;
    
    if (newVatRate > 0) {
        // Calculate NET price from TTC with new VAT rate
        const priceNET = priceTTC / (1 + newVatRate / 100);
        priceInput.value = priceNET.toFixed(2);
    } else {
        // If VAT is 0, NET price equals TTC price
        priceInput.value = priceTTC.toFixed(2);
    }
}

function loadCoachCourses(coachId) {
    if (!coachId) return;
    
    console.log('🏠 Loading courses for coach:', coachId);
    
    // Extract numeric ID if needed
    const userId = coachId.toString().replace('user_', '');
    
    const baseUrl = '{{ base_url }}';
    fetch(`${baseUrl}/users/${userId}/courses`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                if (data.courses && data.courses.length > 0) {
                    // Add all courses as rows
                    addAllCoachCoursesAsRows(data.courses);
                } else {
                    console.warn('No courses found for coach:', coachId);
                    alert('{{ __("invoices.no_courses_found_for_coach") | default("No courses found for this coach") }}');
                }
            } else {
                console.error('API error:', data.message || 'Unknown error');
                alert('{{ __("invoices.error_loading_courses") | default("Error loading courses") }}: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error loading courses:', error);
            alert('{{ __("invoices.error_loading_courses") | default("Error loading courses") }}: ' + error.message);
        });
}

// New function to add all coach courses as invoice rows
function addAllCoachCoursesAsRows(courses) {
    console.log('🏠 Adding all coach courses as invoice rows:', courses.length);
    
    if (!courses || courses.length === 0) {
        console.log('No courses found for this coach');
        return;
    }
    
    // Ask user if they want to clear existing items
    const itemsBody = document.getElementById('itemsBody');
    const existingItems = itemsBody.querySelectorAll('.invoice-item');
    
    if (existingItems.length > 0) {
        if (confirm('{{ __("invoices.replace_existing_items_with_courses") | default("Replace existing items with coach courses?") }}')) {
            // Clear existing items
            itemsBody.innerHTML = '';
            itemIndex = 0;
        }
    } else {
        // Clear the table anyway if empty
        itemsBody.innerHTML = '';
        itemIndex = 0;
    }
    
    // Add each course as a row
    courses.forEach(course => {
        addLocationItemWithCourse(course);
    });
    
    // Fallback: Ensure TTC conversion for all items
    ensureTTCConversionForAllItems();
    
    // Calculate totals after adding all items
    calculateTotals();
}

// Manual function to fix TTC prices for existing items (can be called from console)
window.fixTTCPrices = function(forceUpdate = false) {
    console.log('Manually fixing TTC prices...');
    
    // First, ensure the user VAT status is set if we have a selected user
    const billableSelect = document.getElementById('billable_id');
    if (billableSelect && billableSelect.value && billableSelect.value.startsWith('user_')) {
        const userId = billableSelect.value.replace('user_', '');
        
        // Try to find user data
        let userData = null;
        if (coachesData && coachesData.length > 0) {
            userData = coachesData.find(u => u.id == userId);
        }
        if (!userData && practitionersData && practitionersData.length > 0) {
            userData = practitionersData.find(u => u.id == userId);
        }
        if (!userData && usersData && usersData.length > 0) {
            userData = usersData.find(u => u.id == userId);
        }
        
        if (userData) {
            console.log('Found user data:', userData.name, userData.vat_intercommunautaire);
            const vatNumber = userData.vat_intercommunautaire ? userData.vat_intercommunautaire.trim() : '';
            selectedUserVatStatus.userId = userData.id;
            selectedUserVatStatus.hasVatIntracommunautaire = !!vatNumber;
            selectedUserVatStatus.isLuxembourgVat = vatNumber.toUpperCase().startsWith('LU');
            console.log('Updated VAT status:', selectedUserVatStatus);
        }
    }
    
    // Force TTC conversion with current prices as TTC
    if (forceUpdate) {
        forceTTCConversion();
    } else {
        ensureTTCConversionForAllItems();
    }
    
    calculateTotals();
    console.log('TTC price fix completed');
};

// Force TTC conversion treating current prices as TTC
function forceTTCConversion() {
    console.log('Forcing TTC conversion...');
    
    // Check if this is a location invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
    
    if (!isLocationInvoice) {
        console.log('Not a location invoice, skipping TTC conversion');
        return;
    }
    
    // Check if we should use TTC pricing
    const shouldUseTTC = selectedUserVatStatus.userId && 
                       (!selectedUserVatStatus.hasVatIntracommunautaire || selectedUserVatStatus.isLuxembourgVat);
    
    if (!shouldUseTTC) {
        console.log('Should not use TTC pricing for this user');
        return;
    }
    
    console.log('Applying forced TTC conversion...');
    
    // Process each row
    const rows = document.querySelectorAll('.invoice-item');
    rows.forEach((row, index) => {
        const priceInput = row.querySelector('.item-price');
        const vatSelect = row.querySelector('.item-vat');
        
        if (priceInput && vatSelect) {
            const currentPrice = parseFloat(priceInput.value) || 0;
            if (currentPrice > 0) {
                // Store current price as TTC
                priceInput.setAttribute('data-ttc-price', currentPrice);
                row.setAttribute('data-expected-ttc-total', currentPrice);
                
                // Get VAT rate
                const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0);
                
                if (vatRate > 0) {
                    // Calculate NET from TTC
                    const priceNET = currentPrice / (1 + vatRate / 100);
                    priceInput.value = priceNET.toFixed(2);
                    console.log(`Row ${index}: Converted €${currentPrice} TTC to €${priceNET.toFixed(2)} NET @ ${vatRate}%`);
                    
                    // Trigger input event to update calculations
                    const event = new Event('input', { bubbles: true });
                    priceInput.dispatchEvent(event);
                }
            }
        }
    });
}

// Fallback function to ensure TTC conversion happens for all location invoice items
function ensureTTCConversionForAllItems() {
    console.log('Running TTC conversion fallback check...');
    
    // Check if this is a location invoice
    const invoiceTypeSelect = document.getElementById('invoice_type_id');
    const selectedOption = invoiceTypeSelect?.options[invoiceTypeSelect.selectedIndex];
    const typeCode = selectedOption ? selectedOption.getAttribute('data-prefix') : '';
    const typeName = selectedOption ? selectedOption.textContent.toLowerCase() : '';
    const isLocationInvoice = (typeCode && typeCode.startsWith('LOCS')) || typeName.includes('location');
    
    if (!isLocationInvoice) return;
    
    // Check if we should use TTC pricing
    const shouldUseTTC = selectedUserVatStatus.userId && 
                       (!selectedUserVatStatus.hasVatIntracommunautaire || selectedUserVatStatus.isLuxembourgVat);
    
    if (!shouldUseTTC) return;
    
    // Process each row
    const rows = document.querySelectorAll('.invoice-item');
    rows.forEach((row, index) => {
        const priceInput = row.querySelector('.item-price');
        const vatSelect = row.querySelector('.item-vat');
        
        if (priceInput && vatSelect) {
            // First, ensure a VAT rate is selected
            if (vatSelect.selectedIndex === -1 || vatSelect.value === '') {
                // No VAT rate selected, try to select 17%
                for (let i = 0; i < vatSelect.options.length; i++) {
                    const rate = parseFloat(vatSelect.options[i].getAttribute('data-rate'));
                    if (rate === 17) {
                        vatSelect.selectedIndex = i;
                        console.log(`Row ${index}: Selected 17% VAT rate`);
                        break;
                    }
                }
            }
            
            // Check if we need to apply TTC conversion
            const currentPrice = parseFloat(priceInput.value) || 0;
            const ttcPrice = priceInput.dataset.ttcPrice ? parseFloat(priceInput.dataset.ttcPrice) : currentPrice;
            const vatRate = parseFloat(vatSelect.options[vatSelect.selectedIndex]?.dataset.rate || 0);
            
            // If no TTC price stored but we should use TTC, store current price as TTC
            if (!priceInput.dataset.ttcPrice && currentPrice > 0) {
                priceInput.dataset.ttcPrice = currentPrice;
                console.log(`Row ${index}: Storing current price €${currentPrice} as TTC`);
            }
            
            // Apply TTC to NET conversion if needed
            if (vatRate > 0 && ttcPrice > 0) {
                const expectedNetPrice = ttcPrice / (1 + vatRate / 100);
                const currentNetPrice = parseFloat(priceInput.value);
                
                // Check if conversion is needed (allow small rounding differences)
                if (Math.abs(currentNetPrice - ttcPrice) < 0.01) {
                    // Price is still TTC, needs conversion
                    priceInput.value = expectedNetPrice.toFixed(2);
                    console.log(`Row ${index}: Applied TTC conversion - €${ttcPrice} TTC to €${expectedNetPrice.toFixed(2)} NET @ ${vatRate}%`);
                    
                    // Trigger change event to update calculations
                    const event = new Event('input', { bubbles: true });
                    priceInput.dispatchEvent(event);
                }
            }
        }
    });
}

function updateCourseOptions(courses) {
    const courseSelects = document.querySelectorAll('.coach-course-select');
    const selectCourseText = '{{ __("invoices.select_course") }}';
    
    courseSelects.forEach(select => {
        // Clear existing options except the first one
        select.innerHTML = `<option value="">${selectCourseText}</option>`;
        
        courses.forEach(course => {
            const option = document.createElement('option');
            option.value = JSON.stringify(course);
            option.textContent = `${course.course_name} - €${course.hourly_rate}/h`;
            if (course.is_intracommunity) {
                option.textContent += ' ({{ __("users.intracommunity") }})';
            }
            select.appendChild(option);
        });
    });
}

</script>

<!-- Product Search Modal -->
<div class="modal fade" id="productSearchModal" tabindex="-1" aria-labelledby="productSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="productSearchModalLabel">{{ __('invoices.search_product')|default('Search Product') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="productSearchInput" 
                           placeholder="{{ __('invoices.search_placeholder')|default('Search by name, code or description...') }}">
                </div>
                <div id="productSearchResults" class="list-group" style="max-height: 400px; overflow-y: auto;">
                    <!-- Search results will be displayed here -->
                </div>
                <div id="productSearchLoading" class="text-center py-3 d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <div id="productSearchEmpty" class="alert alert-info d-none">
                    {{ __('invoices.no_products_found')|default('No products found') }}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Product Search Functionality
document.addEventListener('DOMContentLoaded', function() {
    const searchBtn = document.getElementById('searchProductBtn');
    const modal = new bootstrap.Modal(document.getElementById('productSearchModal'));
    const searchInput = document.getElementById('productSearchInput');
    const resultsContainer = document.getElementById('productSearchResults');
    const loadingDiv = document.getElementById('productSearchLoading');
    const emptyDiv = document.getElementById('productSearchEmpty');
    
    let searchTimeout;
    
    // Open modal on button click
    searchBtn.addEventListener('click', function() {
        modal.show();
        searchInput.focus();
    });
    
    // Search on input
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();
        
        if (query.length < 2) {
            resultsContainer.innerHTML = '';
            emptyDiv.classList.add('d-none');
            return;
        }
        
        searchTimeout = setTimeout(() => searchProducts(query), 300);
    });
    
    // Search products function
    function searchProducts(query) {
        loadingDiv.classList.remove('d-none');
        emptyDiv.classList.add('d-none');
        resultsContainer.innerHTML = '';
        
        const baseUrl = '{{ base_url }}';
        fetch(`${baseUrl}/api/products/search?term=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                loadingDiv.classList.add('d-none');
                
                if (!data.success || !data.items || data.items.length === 0) {
                    emptyDiv.classList.remove('d-none');
                    return;
                }
                
                data.items.forEach(product => {
                    const item = document.createElement('a');
                    item.href = '#';
                    item.className = 'list-group-item list-group-item-action';
                    item.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">${product.name}</h6>
                                <small class="text-muted">${product.code || ''}</small>
                            </div>
                            <div class="text-end">
                                <div class="fw-bold">${window.currency || '€'}${parseFloat(product.unit_price).toFixed(2)}</div>
                                <small class="text-muted">{{ __('invoices.vat') }}: ${product.vat_rate}%</small>
                            </div>
                        </div>
                    `;
                    
                    item.addEventListener('click', function(e) {
                        e.preventDefault();
                        addProductToInvoice(product);
                        modal.hide();
                    });
                    
                    resultsContainer.appendChild(item);
                });
            })
            .catch(error => {
                console.error('Error searching products:', error);
                loadingDiv.classList.add('d-none');
                emptyDiv.classList.remove('d-none');
            });
    }
    
    // Add product to invoice
    function addProductToInvoice(product) {
        // Find VAT rate ID from the rate value
        const vatSelects = document.querySelectorAll('.item-vat');
        let vatRateId = '';
        
        if (vatSelects.length > 0) {
            const firstVatSelect = vatSelects[0];
            for (let option of firstVatSelect.options) {
                if (parseFloat(option.dataset.rate) === parseFloat(product.vat_rate)) {
                    vatRateId = option.value;
                    break;
                }
            }
        }
        
        // Add item with product data
        window.addItemWithData({
            description: product.name,
            quantity: '1',
            unit_price: product.unit_price,
            vat_rate_id: vatRateId,
            item_id: product.id,
            code: product.code
        });
    }
});
</script>

<!-- Include fix scripts -->
<!-- Invoice number generation moved to server-side on save
<script src="{{ base_url }}/js/invoice-create-fix.js"></script> -->
<!-- Commented out as it overrides the main loadBillableOptions function -->
<!-- <script src="{{ base_url }}/js/invoice-billable-fix.js"></script> -->
<script src="{{ base_url }}/js/invoice-create-complete-fix.js"></script>
{% endblock %}