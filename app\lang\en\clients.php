<?php

return array (
  'add_contact' => 'Add Contact',
  'add_new' => 'Add New Client',
  'active_clients' => 'Active Clients',
  'add_client' => 'Add Client',
  'new_client_added' => 'New client added',
  'additional_information' => 'Additional Information',
  'address' => 'Address',
  'address_line1' => 'Address Line 1',
  'address_line2' => 'Address Line 2',
  'basic_information' => 'Basic Information',
  'billing_information' => 'Billing Information',
  'birth_date' => 'Birth Date',
  'bulk_delete_result' => '%d client(s) deleted',
  'bulk_delete_skipped' => '%d client(s) skipped (have invoices)',
  'cannot_delete_has_invoices' => 'Cannot delete this client because it has associated invoices',
  'city' => 'City',
  'client_details' => 'Client Details',
  'client_type_flags' => 'Client Type',
  'client_information' => 'Client Information',
  'client_number' => 'Client Number',
  'client_type' => 'Client Type',
  'companies' => 'Companies',
  'company' => 'Company',
  'company_information' => 'Company Information',
  'company_name' => 'Company Name',
  'company_name_required' => 'Company name is required',
  'company_registration' => 'Registration Number',
  'contact_email' => 'Contact Email',
  'contact_information' => 'Contact Information',
  'contact_name' => 'Contact Name',
  'contact_person' => 'Contact Person',
  'contact_phone' => 'Contact Phone',
  'contact_title' => 'Contact Title',
  'contacts' => 'Contacts',
  'country' => 'Country',
  'create_invoice' => 'Create Invoice',
  'created_at' => 'Created At',
  'created_by' => 'Created By',
  'created_successfully' => 'Client created successfully',
  'credit_limit' => 'Credit Limit',
  'current_balance' => 'Current Balance',
  'deleted_successfully' => 'Client deleted successfully',
  'discount_percentage' => 'Discount (%)',
  'documents' => 'Documents',
  'documents_count' => 'Documents',
  'edit_client' => 'Edit Client',
  'email' => 'Email',
  'fax' => 'Fax',
  'female' => 'Female',
  'financial_information' => 'Financial Information',
  'first_name' => 'First Name',
  'first_name_required' => 'First name is required',
  'gender' => 'Gender',
  'individual' => 'Individual',
  'individuals' => 'Individuals',
  'invalid_website' => 'Invalid website URL',
  'invoices' => 'Invoices',
  'is_active' => 'Active',
  'is_billing_contact' => 'Is Billing Contact',
  'is_practitioner' => 'Practitioner',
  'is_staff_member' => 'Staff Member',
  'is_primary' => 'Is Primary',
  'last_name' => 'Last Name',
  'last_name_required' => 'Last name is required',
  'male' => 'Male',
  'mobile' => 'Mobile',
  'new_this_month' => 'New This Month',
  'no_documents' => 'No Documents',
  'notes' => 'Notes',
  'other' => 'Other',
  'outstanding' => 'Outstanding',
  'outstanding_amount' => 'Outstanding Amount',
  'payment_terms' => 'Payment Terms',
  'payment_terms_days' => 'days',
  'personal_information' => 'Personal Information',
  'phone' => 'Phone',
  'postal_code' => 'Postal Code',
  'search_placeholder' => 'Search by name, email, client number...',
  'tags' => 'Tags',
  'tags_hint' => 'Tags Hint',
  'tax_exempt' => 'Tax Exempt',
  'title' => 'Clients',
  'total_active' => 'Active Clients',
  'total_invoices' => 'Total Invoices',
  'total_revenue' => 'Total Revenue',
  'unpaid_invoices' => 'Unpaid Invoices',
  'updated_at' => 'Updated At',
  'updated_by' => 'Updated By',
  'updated_successfully' => 'Client updated successfully',
  'upload_document' => 'Upload Document',
  'vat_number' => 'VAT Number',
  'view_documents' => 'View Documents',
  'view_invoices' => 'View Invoices',
  'website' => 'Website',
  'add_new_client' => 'Add New Client',
  'name_required' => 'First name and last name are required',
  'default_vat_rate' => 'Default VAT Rate',
  'vat_rate' => 'VAT Rate',
  'client' => 'Client',
  'type' => 'Type',
  'contact' => 'Contact',
  'location' => 'Location',
  'client_activated' => 'Client activated successfully',
  'client_deactivated' => 'Client deactivated successfully',
  'add_client' => 'Add Client',
);
