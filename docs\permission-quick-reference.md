# Permission System Quick Reference

## Check Permissions in Controllers

```php
use App\Services\PermissionService;

$permissionService = PermissionService::getInstance();

// Single permission
if (!$permissionService->hasPermission('users.create')) {
    // Redirect or show error
}

// Any of multiple permissions
if (!$permissionService->hasAnyPermission(['users.create', 'users.update'])) {
    // User needs at least one
}

// All permissions required
if (!$permissionService->hasAllPermissions(['reports.view', 'reports.export'])) {
    // User needs all permissions
}

// Super admin check
if (!$permissionService->isSuperAdmin()) {
    // Only administrators group
}
```

## Route Protection with Middleware

```php
use App\Middleware\PermissionMiddleware;

// In routes file
Flight::route('GET /users', 
    PermissionMiddleware::require('users.view'),
    function() { /* handler */ }
);

// API routes (returns JSON error)
Flight::route('POST /api/users',
    PermissionMiddleware::requireApi('users.create'),
    function() { /* handler */ }
);

// Multiple permissions
Flight::route('GET /admin/settings',
    PermissionMiddleware::requireAny(['config.view', 'config.manage']),
    function() { /* handler */ }
);
```

## Permission Codes Format

```
module.action

Examples:
- users.view
- users.create
- users.update
- users.delete
- invoices.send
- config.manage
```

## In Blade/Twig Templates

```twig
{# Check permission in template #}
{% if hasPermission('users.create') %}
    <button>Create User</button>
{% endif %}

{# Multiple permissions #}
{% if hasAnyPermission(['users.create', 'users.update']) %}
    <div class="user-controls">...</div>
{% endif %}
```

## Common Permission Checks

```php
// User management
'users.view'              // View user list
'users.create'            // Create users
'users.update'            // Edit users
'users.delete'            // Delete users
'user_groups.manage'      // Manage groups

// Configuration
'config.view'             // View settings
'config.manage'           // Change settings

// Invoicing
'invoices.view'           // View invoices
'invoices.create'         // Create invoices
'invoices.send'           // Send invoices
'payments.create'         // Record payments

// Reports
'reports.financial'       // Financial reports
'reports.sales'           // Sales reports
'reports.export'          // Export data
```

## Error Handling

```php
// Web routes - Redirect
if (!$permissionService->hasPermission('users.view')) {
    $_SESSION['flash']['error'] = 'Access denied';
    return redirect('/dashboard');
}

// API routes - JSON response
if (!$permissionService->hasPermission('api.users.create')) {
    return Flight::json([
        'success' => false,
        'error' => 'Unauthorized',
        'message' => 'Insufficient permissions'
    ], 403);
}
```

## Adding New Permissions

1. Add to `database/seeders/PermissionSeeder.php`
2. Run the seeder
3. Add permission checks to routes/controllers
4. Assign to appropriate groups

## Testing Permissions

```php
// Get current user's permissions
$permissions = $permissionService->getUserPermissions();

// Check if permission exists
$exists = $permissionService->permissionExists('custom.permission');

// Get user's groups
$groups = $permissionService->getUserGroups();
```