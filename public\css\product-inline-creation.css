/**
 * Product Inline Creation Styles
 * Mobile-first responsive design for inline product creation
 */

/* Product creation row styling */
.product-creation-row {
    background-color: #f8f9fa;
}

.product-creation-row td {
    padding: 0 !important;
}

/* Creation form container */
.product-creation-form {
    width: 100%;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card styling */
.product-creation-form .card {
    margin: 0;
    border: 2px solid var(--bs-primary);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.product-creation-form .card-header {
    background-color: var(--bs-primary) !important;
    border-bottom: none;
}

.product-creation-form .card-body {
    padding: 1.5rem;
}

/* Create new product option in dropdown */
.product-search-create-new {
    border-top: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

.product-search-create-new:hover {
    background-color: #e9ecef;
}

.product-search-create-new .product-search-item-name {
    color: var(--bs-primary);
    font-weight: 500;
}

/* Form controls */
.product-creation-form .form-control,
.product-creation-form .form-select {
    font-size: 0.95rem;
}

.product-creation-form .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

/* Generate code button */
#generate-code-btn {
    padding: 0.375rem 0.75rem;
}

#generate-code-btn:hover {
    background-color: var(--bs-primary);
    color: white;
}

/* Toast notification */
.toast {
    min-width: 300px;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
    .product-creation-form .card-body {
        padding: 1rem;
    }
    
    .product-creation-form .row {
        margin: 0;
    }
    
    .product-creation-form .col-md-3,
    .product-creation-form .col-md-6 {
        padding: 0 0.5rem;
        margin-bottom: 1rem;
    }
    
    .product-creation-form .form-label {
        font-size: 0.875rem;
    }
    
    .product-creation-form .form-control,
    .product-creation-form .form-select {
        font-size: 0.875rem;
        height: 44px; /* Touch-friendly height */
    }
    
    .product-creation-form .btn {
        min-height: 44px;
        font-size: 0.875rem;
    }
    
    /* Stack buttons on mobile */
    .product-creation-form .d-flex {
        flex-direction: column;
    }
    
    .product-creation-form .btn-cancel-creation {
        order: 2;
        margin-top: 0.5rem;
    }
    
    .product-creation-form .btn-save-product {
        order: 1;
        width: 100%;
    }
    
    /* Adjust input group on mobile */
    .product-creation-form .input-group {
        flex-wrap: nowrap;
    }
    
    #generate-code-btn {
        padding: 0.375rem 0.5rem;
    }
    
    /* Toast on mobile */
    .toast {
        margin: 1rem;
        min-width: calc(100vw - 2rem);
    }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 991px) {
    .product-creation-form .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    
    .product-creation-form .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Loading state */
.product-creation-form .btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* Error states */
.product-creation-form .is-invalid {
    border-color: #dc3545;
    background-image: none;
}

.product-creation-form .invalid-feedback {
    display: block;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success animation */
@keyframes successPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

.product-creation-success {
    animation: successPulse 1s;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .product-creation-row {
        background-color: #212529;
    }
    
    .product-creation-form .card {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .product-creation-form .card-header {
        background-color: #495057 !important;
    }
    
    .product-search-create-new {
        background-color: #343a40;
        border-color: #495057;
    }
    
    .product-search-create-new:hover {
        background-color: #495057;
    }
    
    .product-creation-form .form-control,
    .product-creation-form .form-select {
        background-color: #495057;
        border-color: #6c757d;
        color: #f8f9fa;
    }
    
    .product-creation-form .form-label {
        color: #adb5bd;
    }
}

/* Accessibility improvements */
.product-creation-form .btn:focus,
.product-creation-form .form-control:focus,
.product-creation-form .form-select:focus {
    outline: 2px solid var(--bs-primary);
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .product-creation-form .card {
        border-width: 3px;
    }
    
    .product-creation-form .btn {
        border-width: 2px;
    }
}