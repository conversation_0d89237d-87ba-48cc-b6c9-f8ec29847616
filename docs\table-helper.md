# Table Helper Documentation (v2)

The Table Helper v2 is an enhanced reusable component system that provides advanced table functionality across the FIT application, including:
- Live search with highlighting
- Filter persistence using localStorage
- Import/Export functionality
- Bulk actions
- **Column sorting (NEW)**
- **Drag-and-drop column reordering (NEW)**
- **State persistence for sorting and column order (NEW)**
- Responsive design
- Dark mode support

## Features

### 1. Live Search
- Real-time filtering as you type
- Highlights matching text
- Configurable search columns
- Results counter
- Clear button
- Escape key to clear
- Works with reordered columns

### 2. Filter Persistence
- Saves filters to localStorage
- Persists even after logout
- Auto-applies saved filters on page load
- Syncs with URL parameters

### 3. Import/Export
- Multiple export formats (CSV, Excel, PDF)
- Maintains current filters in exports
- Exports data in current sort order
- Respects column order in exports
- Configurable import URL

### 4. Bulk Actions
- Select all/individual rows
- Dynamic action buttons
- Confirmation dialogs
- CSRF protection
- Custom action handlers

### 5. Column Sorting (NEW)
- Click column headers to sort
- Ascending/descending toggle
- Visual indicators for sort direction
- Smart sorting (numbers, dates, strings)
- Configurable sortable columns
- Default sort option

### 6. Column Reordering (NEW)
- Drag and drop column headers
- Visual feedback during drag
- Persists column order in localStorage
- Reorder applies to both header and data
- Configurable reorderable columns
- Action columns stay fixed

## Usage

### Basic Implementation

1. Include the table helper JavaScript (v2):
```html
<script src="{{ base_url }}/js/table-helper-v2.js"></script>
```

2. Import the Twig macros (v2):
```twig
{% import '_macros/table-helper-v2.twig' as tableHelper %}
```

3. Use the table wrapper:
```twig
{{ tableHelper.tableWithFilters({
    tableId: 'myTable',
    formAction: base_url ~ '/my-page',
    storageKey: 'my_filters',
    tableContent: tableContent,
    // ... other options
}) }}
```

### Complete Example with Sorting and Reordering

```twig
{% extends "base-modern.twig" %}
{% import '_macros/table-helper-v2.twig' as tableHelper %}

{% block content %}
    {# Define table content #}
    {% set tableContent %}
        {{ tableHelper.tableHeader([
            { label: 'Name', sortable: true },
            { label: 'Email', sortable: true },
            { label: 'Status', sortable: true },
            { label: 'Actions', width: 100, sortable: false, reorderable: false, isAction: true }
        ], true) }}
        <tbody>
            {% for item in items %}
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input row-checkbox" value="{{ item.id }}">
                </td>
                <td>{{ item.name }}</td>
                <td>{{ item.email }}</td>
                <td>
                    <span class="badge bg-{{ item.status == 'active' ? 'success' : 'secondary' }}">
                        {{ item.status }}
                    </span>
                </td>
                <td>
                    <!-- Action buttons -->
                </td>
            </tr>
            {% endfor %}
        </tbody>
    {% endset %}

    {# Render table with filters #}
    {{ tableHelper.tableWithFilters({
        tableId: 'itemsTable',
        formAction: base_url ~ '/items',
        storageKey: 'items_filters',
        tableContent: tableContent,
        searchColumns: [1, 2], // Search in name and email columns
        searchPlaceholder: 'Search items...',
        
        // Filter configuration
        filters: [
            {
                id: 'status',
                name: 'status',
                label: 'Status',
                type: 'select',
                width: 3,
                value: filters.status|default(''),
                options: {
                    'active': 'Active',
                    'inactive': 'Inactive'
                }
            },
            {
                id: 'date_from',
                name: 'date_from',
                label: 'From Date',
                type: 'date',
                width: 3,
                value: filters.date_from|default('')
            }
        ],
        
        // Sorting and Reordering (NEW)
        sortable: true,
        reorderable: true,
        defaultSort: { column: 1, direction: 'asc' },
        
        // Import/Export
        showImport: true,
        importUrl: base_url ~ '/items/import',
        showExport: true,
        exportUrl: base_url ~ '/items/export',
        exportFormats: ['csv', 'excel', 'pdf'],
        
        // Bulk Actions
        showBulkActions: true,
        bulkActions: [
            {
                action: 'activate',
                label: 'Activate Selected',
                icon: 'bi bi-check-circle',
                url: base_url ~ '/items/bulk-activate'
            },
            {
                action: 'delete',
                label: 'Delete Selected',
                icon: 'bi bi-trash',
                class: 'text-danger',
                url: base_url ~ '/items/bulk-delete',
                confirm: true,
                confirmMessage: 'Are you sure you want to delete the selected items?'
            }
        ],
        
        // Pagination
        pagination: {
            current_page: current_page,
            total_pages: total_pages,
            base_url: base_url ~ '/items'
        }
    }) }}
{% endblock %}
```

## Configuration Options

### Main Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `tableId` | string | 'data-table' | HTML ID of the table element |
| `formAction` | string | '' | Form submission URL |
| `storageKey` | string | 'table_filters' | localStorage key for filter persistence |
| `tableContent` | string | required | HTML content of the table |
| `searchColumns` | array | [] | Column indices to search (0-based) |
| `searchPlaceholder` | string | 'Search...' | Search input placeholder |
| `resetUrl` | string | '?reset_filters=1' | URL for reset button |
| `sortable` | boolean | true | Enable column sorting |
| `reorderable` | boolean | true | Enable column drag-and-drop reordering |
| `defaultSort` | object | null | Default sort configuration `{ column: 0, direction: 'asc' }` |
| `showColumnToggle` | boolean | false | Show column visibility toggle (future feature) |

### Filter Options

Each filter in the `filters` array can have:

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `id` | string | required | HTML ID of the filter element |
| `name` | string | id value | Form field name |
| `label` | string | required | Filter label |
| `type` | string | 'text' | Filter type: 'select', 'date', 'text' |
| `width` | integer | 3 | Bootstrap column width (1-12) |
| `value` | mixed | '' | Current filter value |
| `options` | object | {} | Options for select type |
| `placeholder` | string | '' | Placeholder text |
| `autoSubmit` | boolean | true | Auto-submit form on change |

### Import/Export Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `showImport` | boolean | true | Show import button |
| `importUrl` | string | '' | Import page URL |
| `showExport` | boolean | true | Show export dropdown |
| `exportUrl` | string | '' | Export endpoint URL |
| `exportFormats` | array | ['csv', 'excel', 'pdf'] | Available export formats |

### Bulk Action Options

Each action in the `bulkActions` array can have:

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `action` | string | required | Action identifier |
| `label` | string | required | Button label |
| `icon` | string | '' | Bootstrap icon class |
| `class` | string | '' | Additional CSS classes |
| `url` | string | '' | Action endpoint URL |
| `confirm` | boolean | false | Show confirmation dialog |
| `confirmMessage` | string | 'Are you sure?' | Confirmation message |
| `divider` | boolean | false | Add divider before this action |

## JavaScript API

### Initialization

```javascript
const tableHelper = initTableHelper({
    tableId: 'myTable',
    searchInputId: 'search',
    searchColumns: [1, 2],
    storageKey: 'my_filters',
    // ... other options
});
```

### Methods

```javascript
// Clear all filters
tableHelper.clearFilters();

// Save current filters
tableHelper.saveFilters();

// Get selected row IDs
const selectedIds = tableHelper.getSelectedIds();

// Execute bulk action
tableHelper.executeBulkAction('delete');

// Export data
tableHelper.exportData('csv');

// Sort table programmatically
tableHelper.sortTable(1, 'asc'); // Sort by column 1 ascending

// Reorder columns programmatically
tableHelper.reorderColumns(2, 0); // Move column 2 to position 0

// Save current table state
tableHelper.saveTableState();

// Clear all filters and reset table
tableHelper.clearFilters();
```

## Controller Implementation

### Basic Setup

```php
public function index()
{
    // Get filters from query string
    $filters = [
        'status' => Flight::request()->query->status,
        'search' => Flight::request()->query->search
    ];
    
    // Apply filters to your query
    $items = $this->model->getFiltered($filters);
    
    // Render view
    $this->render('items/index', [
        'items' => $items,
        'filters' => $filters,
        'current_page' => $page,
        'total_pages' => $totalPages
    ]);
}
```

### Bulk Actions

```php
public function bulkDelete()
{
    $ids = Flight::request()->data->ids ?? [];
    
    if (empty($ids)) {
        $this->flash('error', 'No items selected');
        $this->redirect('/items');
    }
    
    // Process bulk delete
    foreach ($ids as $id) {
        $this->model->delete($id);
    }
    
    $this->flash('success', count($ids) . ' items deleted');
    $this->redirect('/items');
}
```

### Export with Column Order and Sorting

```php
public function export()
{
    $format = Flight::request()->query->format ?? 'csv';
    $filters = [
        'status' => Flight::request()->query->status,
        'search' => Flight::request()->query->search
    ];
    
    // Get column order and sort from query
    $columnOrder = json_decode(Flight::request()->query->column_order ?? '[]', true);
    $sortColumn = Flight::request()->query->sort_column;
    $sortDirection = Flight::request()->query->sort_direction ?? 'asc';
    
    // Get filtered data
    $items = $this->model->getFiltered($filters);
    
    // Apply sorting if specified
    if ($sortColumn !== null) {
        $items = $this->sortData($items, $sortColumn, $sortDirection);
    }
    
    // Reorder columns if specified
    if (!empty($columnOrder)) {
        $items = $this->reorderColumns($items, $columnOrder);
    }
    
    switch ($format) {
        case 'csv':
            $this->exportCsv($items);
            break;
        case 'excel':
            $this->exportExcel($items);
            break;
        case 'pdf':
            $this->exportPdf($items);
            break;
    }
}
```

## Styling

The table helper includes custom CSS (`/css/table-helper.css`) and uses Bootstrap 5 classes:
- Responsive table wrapper
- Shadow effects on cards
- Hover states on rows
- Proper spacing and alignment
- Dark mode support
- Sort indicators with icons
- Drag and drop visual feedback
- Column reordering animations

## Browser Support

- Chrome/Edge (latest)
- Firefox (latest)
- Safari (latest)
- Supports localStorage API
- Requires JavaScript enabled

## Tips

1. **Search Performance**: For large tables, limit searchable columns using `searchColumns`
2. **Filter Persistence**: Use unique `storageKey` values for different tables
3. **Bulk Actions**: Always include CSRF protection
4. **Export**: Consider pagination for large datasets
5. **Accessibility**: Table includes proper ARIA labels and keyboard navigation
6. **Sorting**: Add `data-sort` attributes to cells with custom sort values (e.g., dates)
7. **Column Reordering**: Mark action columns with `isAction: true` to prevent reordering
8. **Performance**: For tables with 1000+ rows, consider server-side sorting
9. **Mobile**: Column reordering is automatically disabled on mobile devices
10. **State Management**: Clear localStorage periodically to prevent stale configurations