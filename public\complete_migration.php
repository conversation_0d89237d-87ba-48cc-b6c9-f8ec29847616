<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment
if (file_exists(dirname(__DIR__) . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
    $dotenv->load();
}

// Database connection
$host = getenv('DB_HOST') ?: '127.0.0.1';
$dbname = getenv('DB_DATABASE') ?: 'fitapp';
$username = getenv('DB_USERNAME') ?: 'root';
$password = getenv('DB_PASSWORD') ?: 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Completing Email Automation Migration</h2>";
    echo "<pre>";
    echo "Connected to database successfully\n\n";
    
    // Check if config_settings table has group column
    $checkColumn = "SELECT COUNT(*) as count 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = '$dbname' 
                    AND TABLE_NAME = 'config_settings' 
                    AND COLUMN_NAME = 'group'";
    
    $result = $pdo->query($checkColumn)->fetch();
    
    if ($result['count'] == 0) {
        // Add group column to existing table
        $sql = "ALTER TABLE config_settings 
                ADD COLUMN `group` VARCHAR(50) AFTER `value`";
        $pdo->exec($sql);
        echo "✓ Added group column to config_settings table\n";
        
        // Also add description if missing
        $checkDesc = "SELECT COUNT(*) as count 
                      FROM INFORMATION_SCHEMA.COLUMNS 
                      WHERE TABLE_SCHEMA = '$dbname' 
                      AND TABLE_NAME = 'config_settings' 
                      AND COLUMN_NAME = 'description'";
        
        $resultDesc = $pdo->query($checkDesc)->fetch();
        
        if ($resultDesc['count'] == 0) {
            $sql = "ALTER TABLE config_settings 
                    ADD COLUMN description TEXT AFTER `group`";
            $pdo->exec($sql);
            echo "✓ Added description column to config_settings table\n";
        }
        
        // Add indexes
        try {
            $pdo->exec("CREATE INDEX idx_group ON config_settings (`group`)");
            echo "✓ Added group index\n";
        } catch (PDOException $e) {
            echo "✓ Group index already exists\n";
        }
    } else {
        echo "✓ Group column already exists in config_settings table\n";
    }
    
    // Now insert the default settings
    $defaultSettings = [
        [
            'key' => 'email_automation_enabled',
            'value' => '0',
            'group' => 'email_automation',
            'description' => 'Master switch for email automation'
        ],
        [
            'key' => 'payment_reminders',
            'value' => json_encode([
                'enabled' => false,
                'reminder_days' => [7, 14, 30],
                'max_reminders' => 3,
                'templates' => [
                    1 => 'payment_reminder_friendly',
                    2 => 'payment_reminder_firm',
                    3 => 'payment_reminder_urgent'
                ]
            ]),
            'group' => 'email_automation',
            'description' => 'Payment reminder configuration'
        ],
        [
            'key' => 'invoice_auto_send',
            'value' => json_encode([
                'FAC' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_standard'],
                'FAC-RET30' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_retrocession'],
                'FAC-RET25' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_retrocession'],
                'FAC-LOC' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_rental'],
                'FAC-COURS' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_course'],
                'FAC-DIV' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_standard']
            ]),
            'group' => 'email_automation',
            'description' => 'Auto-send settings per invoice type'
        ]
    ];
    
    foreach ($defaultSettings as $setting) {
        // First check if key exists
        $checkKey = $pdo->prepare("SELECT id FROM config_settings WHERE `key` = :key");
        $checkKey->execute(['key' => $setting['key']]);
        $existing = $checkKey->fetch();
        
        if ($existing) {
            // Update existing
            $sql = "UPDATE config_settings 
                    SET `value` = :value, 
                        `group` = :group, 
                        description = :description 
                    WHERE `key` = :key";
        } else {
            // Insert new
            $sql = "INSERT INTO config_settings (`key`, `value`, `group`, description) 
                    VALUES (:key, :value, :group, :description)";
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($setting);
        echo "✓ Configured setting: " . $setting['key'] . "\n";
    }
    
    // Verify all tables exist
    echo "\nVerifying all required tables:\n";
    
    $tables = ['reminder_logs', 'config_settings', 'export_logs'];
    foreach ($tables as $table) {
        $sql = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = '$dbname' AND TABLE_NAME = '$table'";
        $result = $pdo->query($sql)->fetch();
        
        if ($result['count'] > 0) {
            echo "✓ Table '$table' exists\n";
        } else {
            echo "✗ Table '$table' is missing\n";
        }
    }
    
    echo "\n<strong>✅ Migration completed successfully!</strong>\n";
    echo "</pre>";
    
    echo "<h3>All Set! 🎉</h3>";
    echo "<p>The email automation system is now fully configured and ready to use.</p>";
    
    echo "<h3>What You Can Do Now:</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='/fit/admin/email-automation' class='btn btn-primary' style='margin-right: 10px;'>Configure Email Automation</a>";
    echo "<a href='/fit/public/test-email-variables.php' class='btn btn-success' style='margin-right: 10px;'>Test Email Variables</a>";
    echo "<a href='/fit/invoices' class='btn btn-info'>Try Excel Export</a>";
    echo "</div>";
    
    echo "<h4>Quick Start Guide:</h4>";
    echo "<ol>";
    echo "<li><strong>Email Automation:</strong> Go to Configuration → Email Automation to enable automatic invoice sending</li>";
    echo "<li><strong>Payment Reminders:</strong> Set up reminder schedules (7, 14, 30 days) and enable reminders</li>";
    echo "<li><strong>Excel Export:</strong> Visit any list page (Invoices, Users, Products) and use the Export button</li>";
    echo "<li><strong>Test Emails:</strong> Use the test feature in Email Automation settings before enabling</li>";
    echo "</ol>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    echo "<pre>Debug info:\n";
    echo "SQL Error Code: " . $e->getCode() . "\n";
    echo "</pre>";
}