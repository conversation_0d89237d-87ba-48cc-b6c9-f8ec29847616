<?php
require_once dirname(__DIR__) . '/vendor/autoload.php';

// Load environment
if (file_exists(dirname(__DIR__) . '/.env')) {
    $dotenv = Dotenv\Dotenv::createImmutable(dirname(__DIR__));
    $dotenv->load();
}

// Database connection
$host = getenv('DB_HOST') ?: '127.0.0.1';
$dbname = getenv('DB_DATABASE') ?: 'fitapp';
$username = getenv('DB_USERNAME') ?: 'root';
$password = getenv('DB_PASSWORD') ?: 'test1234';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Migration Fix: Email Automation Tables</h2>";
    echo "<pre>";
    echo "Connected to database successfully\n\n";
    
    // First, check the invoices table id column type
    $sql = "SHOW COLUMNS FROM invoices WHERE Field = 'id'";
    $result = $pdo->query($sql)->fetch(PDO::FETCH_ASSOC);
    
    echo "Invoices.id column type: " . $result['Type'] . "\n";
    
    // Extract the data type to match it
    $idType = $result['Type']; // This will be something like "int(11)" or "bigint(20) unsigned"
    
    // Drop the table if it exists with wrong structure
    $pdo->exec("DROP TABLE IF EXISTS reminder_logs");
    echo "✓ Dropped existing reminder_logs table (if any)\n";
    
    // Create reminder_logs table with matching column type
    $sql = "CREATE TABLE reminder_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        invoice_id $idType NOT NULL,
        sent_date DATETIME NOT NULL,
        reminder_number INT NOT NULL DEFAULT 1,
        template_used VARCHAR(100),
        email_sent_to VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_invoice_id (invoice_id),
        INDEX idx_sent_date (sent_date),
        INDEX idx_reminder_number (reminder_number),
        
        FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created reminder_logs table with matching invoice_id type: $idType\n";
    
    // Add excluded_from_reminders column to clients table if it doesn't exist
    $checkColumn = "SELECT COUNT(*) as count 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_SCHEMA = '$dbname' 
                    AND TABLE_NAME = 'clients' 
                    AND COLUMN_NAME = 'excluded_from_reminders'";
    
    $result = $pdo->query($checkColumn)->fetch();
    
    if ($result['count'] == 0) {
        $sql = "ALTER TABLE clients 
                ADD COLUMN excluded_from_reminders TINYINT(1) DEFAULT 0 
                COMMENT 'If 1, client will not receive payment reminders'";
        $pdo->exec($sql);
        echo "✓ Added excluded_from_reminders column to clients table\n";
    } else {
        echo "✓ Column excluded_from_reminders already exists in clients table\n";
    }
    
    // Create or update config_settings table
    $sql = "CREATE TABLE IF NOT EXISTS config_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        `key` VARCHAR(100) NOT NULL UNIQUE,
        `value` TEXT,
        `group` VARCHAR(50),
        description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_key (`key`),
        INDEX idx_group (`group`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created/verified config_settings table\n";
    
    // Insert default email automation settings
    $defaultSettings = [
        [
            'key' => 'email_automation_enabled',
            'value' => '0',
            'group' => 'email_automation',
            'description' => 'Master switch for email automation'
        ],
        [
            'key' => 'payment_reminders',
            'value' => json_encode([
                'enabled' => false,
                'reminder_days' => [7, 14, 30],
                'max_reminders' => 3,
                'templates' => [
                    1 => 'payment_reminder_friendly',
                    2 => 'payment_reminder_firm',
                    3 => 'payment_reminder_urgent'
                ]
            ]),
            'group' => 'email_automation',
            'description' => 'Payment reminder configuration'
        ],
        [
            'key' => 'invoice_auto_send',
            'value' => json_encode([
                'FAC' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_standard'],
                'FAC-RET30' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_retrocession'],
                'FAC-RET25' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_retrocession'],
                'FAC-LOC' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_rental'],
                'FAC-COURS' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_course'],
                'FAC-DIV' => ['enabled' => false, 'delay' => 0, 'template' => 'invoice_standard']
            ]),
            'group' => 'email_automation',
            'description' => 'Auto-send settings per invoice type'
        ]
    ];
    
    foreach ($defaultSettings as $setting) {
        $sql = "INSERT INTO config_settings (`key`, `value`, `group`, description) 
                VALUES (:key, :value, :group, :description)
                ON DUPLICATE KEY UPDATE 
                `group` = VALUES(`group`),
                description = VALUES(description)";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($setting);
    }
    
    echo "✓ Inserted/updated default settings\n";
    
    // Check if export_logs table exists, if not create it
    $sql = "CREATE TABLE IF NOT EXISTS export_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT,
        export_type VARCHAR(20) NOT NULL,
        module VARCHAR(50) NOT NULL,
        filters TEXT,
        row_count INT,
        file_size INT,
        ip_address VARCHAR(45),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        
        INDEX idx_user_id (user_id),
        INDEX idx_export_type (export_type),
        INDEX idx_module (module),
        INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✓ Created/verified export_logs table\n";
    
    echo "\n<strong>Migration completed successfully!</strong>\n";
    echo "</pre>";
    
    echo "<h3>✅ All Tables Created Successfully!</h3>";
    echo "<p>The migration has been completed with the correct column types.</p>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Go to <a href='/fit/admin/email-automation'>Configuration → Email Automation</a> to configure settings</li>";
    echo "<li>Enable automatic email sending for invoice types as needed</li>";
    echo "<li>Configure payment reminder schedules</li>";
    echo "<li>Test the email functionality</li>";
    echo "</ol>";
    
    echo "<p><a href='/fit/admin/email-automation' class='btn btn-primary'>Go to Email Automation Settings</a></p>";
    echo "<p><a href='/fit/admin' class='btn btn-secondary'>Back to Admin Panel</a></p>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
    echo "<pre>Debug info:\n";
    echo "Host: $host\n";
    echo "Database: $dbname\n";
    echo "Username: $username\n";
    echo "</pre>";
}