<?php

namespace App\Services;

use Flight;
use PDO;
use App\Models\User;

class PermissionService
{
    private static $instance = null;
    private $db;
    private $cache;
    private $cacheKeyPrefix = 'permissions:';
    private $cacheTTL = 3600; // 1 hour
    
    /**
     * Get singleton instance
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct()
    {
        $this->db = Flight::db();
        $this->initializeCache();
    }
    
    /**
     * Initialize cache service
     */
    private function initializeCache()
    {
        // Try to use Redis if available
        if (class_exists('Redis') && extension_loaded('redis')) {
            try {
                $redis = new \Redis();
                if ($redis->connect('127.0.0.1', 6379)) {
                    $this->cache = $redis;
                    return;
                }
            } catch (\Exception $e) {
                // Fall back to file cache
            }
        }
        
        // Fall back to simple file cache
        $this->cache = new FileCache();
    }
    
    /**
     * Check if current user has a specific permission
     * 
     * @param string $permission Permission code (e.g., 'users.create', 'invoices.delete')
     * @param bool $force Force permission check without cache (for critical operations)
     * @return bool
     */
    public function hasPermission($permission, $force = false)
    {
        $userId = $_SESSION['user_id'] ?? null;
        if (!$userId) {
            return false;
        }
        
        // Super admin bypass (user ID 1)
        if ($userId == 1) {
            return true;
        }
        
        // Check cache first (unless forced)
        if (!$force) {
            $cacheKey = $this->cacheKeyPrefix . $userId . ':' . $permission;
            $cached = $this->getFromCache($cacheKey);
            if ($cached !== null) {
                return $cached;
            }
        }
        
        // Check database - OLD schema compatibility
        // In OLD schema, permissions are in permissions table with code format: category.action
        $stmt = $this->db->prepare("
            SELECT COUNT(*) as has_permission
            FROM permissions p
            JOIN group_permissions gp ON p.id = gp.permission_id
            JOIN user_group_members ugm ON gp.group_id = ugm.group_id
            WHERE ugm.user_id = ? 
                AND p.code = ?
                AND gp.is_granted = 1
        ");
        
        $stmt->execute([$userId, $permission]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $hasPermission = $result['has_permission'] > 0;
        
        // Cache the result (unless forced)
        if (!$force) {
            $this->setInCache($cacheKey, $hasPermission);
        }
        
        // Log force permission checks for security auditing
        if ($force) {
            $this->logPermissionCheck($userId, $permission, $hasPermission, true);
        }
        
        return $hasPermission;
    }
    
    /**
     * Log permission check for auditing
     * 
     * @param int $userId User ID
     * @param string $permission Permission checked
     * @param bool $granted Whether permission was granted
     * @param bool $forced Whether this was a forced check
     */
    private function logPermissionCheck($userId, $permission, $granted, $forced = false)
    {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO permission_audit_log 
                (user_id, permission_code, granted, forced_check, checked_at, ip_address, user_agent)
                VALUES (?, ?, ?, ?, NOW(), ?, ?)
            ");
            
            $stmt->execute([
                $userId,
                $permission,
                $granted ? 1 : 0,
                $forced ? 1 : 0,
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);
        } catch (\Exception $e) {
            // Silently fail - don't break permission checks if logging fails
            error_log("Permission audit log failed: " . $e->getMessage());
        }
    }
    
    /**
     * Check if current user has any of the given permissions
     * 
     * @param array $permissions Array of permission codes
     * @return bool
     */
    public function hasAnyPermission(array $permissions)
    {
        foreach ($permissions as $permission) {
            if ($this->hasPermission($permission)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if current user has all the given permissions
     * 
     * @param array $permissions Array of permission codes
     * @return bool
     */
    public function hasAllPermissions(array $permissions)
    {
        foreach ($permissions as $permission) {
            if (!$this->hasPermission($permission)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get all permissions for the current user
     * 
     * @return array
     */
    public function getUserPermissions()
    {
        $userId = $_SESSION['user_id'] ?? null;
        if (!$userId) {
            return [];
        }
        
        // Super admin gets all permissions
        if ($userId == 1) {
            return $this->getAllPermissions();
        }
        
        // Check cache
        $cacheKey = $this->cacheKeyPrefix . $userId . ':all';
        $cached = $this->getFromCache($cacheKey);
        if ($cached !== null) {
            return $cached;
        }
        
        // Get from database - OLD schema compatibility
        $stmt = $this->db->prepare("
            SELECT DISTINCT
                p.category as module_code,
                p.category as module_name,
                p.code as permission_code,
                p.name as permission_name,
                p.description
            FROM permissions p
            JOIN group_permissions gp ON p.id = gp.permission_id
            JOIN user_group_members ugm ON gp.group_id = ugm.group_id
            WHERE ugm.user_id = ?
            ORDER BY p.category, p.code
        ");
        
        $stmt->execute([$userId]);
        $permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Cache the result
        $this->setInCache($cacheKey, $permissions);
        
        return $permissions;
    }
    
    /**
     * Get all available permissions in the system
     * 
     * @return array
     */
    private function getAllPermissions()
    {
        // OLD schema compatibility - get all permissions from permissions table
        $stmt = $this->db->prepare("
            SELECT DISTINCT
                category as module_code,
                category as module_name,
                code as permission_code,
                name as permission_name,
                description
            FROM permissions
            ORDER BY category, code
        ");
        
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Clear permission cache for a specific user or all users
     * 
     * @param int|null $userId User ID or null for all users
     */
    public function clearPermissionCache($userId = null)
    {
        if ($userId) {
            // Clear specific user's cache
            $pattern = $this->cacheKeyPrefix . $userId . ':*';
        } else {
            // Clear all permission cache
            $pattern = $this->cacheKeyPrefix . '*';
        }
        
        if ($this->cache instanceof \Redis) {
            $keys = $this->cache->keys($pattern);
            if (!empty($keys)) {
                $this->cache->del(...$keys);
            }
        } else {
            // File cache clear
            $this->cache->clearPattern($pattern);
        }
    }
    
    /**
     * Check if user is super admin
     * 
     * @param int|null $userId User ID or null for current user
     * @return bool
     */
    public function isSuperAdmin($userId = null)
    {
        if ($userId === null) {
            $userId = $_SESSION['user_id'] ?? null;
        }
        
        return $userId == 1;
    }
    
    /**
     * Get from cache
     */
    private function getFromCache($key)
    {
        try {
            if ($this->cache instanceof \Redis) {
                $value = $this->cache->get($key);
                return $value !== false ? unserialize($value) : null;
            } else {
                return $this->cache->get($key);
            }
        } catch (\Exception $e) {
            return null;
        }
    }
    
    /**
     * Set in cache
     */
    private function setInCache($key, $value)
    {
        try {
            if ($this->cache instanceof \Redis) {
                $this->cache->setex($key, $this->cacheTTL, serialize($value));
            } else {
                $this->cache->set($key, $value, $this->cacheTTL);
            }
        } catch (\Exception $e) {
            // Cache write failed, continue without caching
        }
    }
}

/**
 * Simple file-based cache implementation
 */
class FileCache
{
    private $cacheDir;
    
    public function __construct()
    {
        $this->cacheDir = __DIR__ . '/../../cache/permissions/';
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0777, true);
        }
    }
    
    public function get($key)
    {
        $file = $this->cacheDir . md5($key) . '.cache';
        if (file_exists($file)) {
            $data = unserialize(file_get_contents($file));
            if ($data['expires'] > time()) {
                return $data['value'];
            }
            unlink($file);
        }
        return null;
    }
    
    public function set($key, $value, $ttl)
    {
        $file = $this->cacheDir . md5($key) . '.cache';
        $data = [
            'value' => $value,
            'expires' => time() + $ttl
        ];
        file_put_contents($file, serialize($data));
    }
    
    public function clearPattern($pattern)
    {
        // Simple pattern matching for file cache
        $files = glob($this->cacheDir . '*.cache');
        foreach ($files as $file) {
            unlink($file);
        }
    }
}